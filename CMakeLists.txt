cmake_minimum_required(VERSION 3.5)
project(vis_g3_software)

set(CMAKE_CXX_STANDARD 11)

# 设置库路径
set(SDK_ROOT /work/rv1126BP/host/arm-buildroot-linux-gnueabihf/sysroot)
link_directories(
    ${SDK_ROOT}/usr/lib
    ${SDK_ROOT}/usr/local/lib
    ${SDK_ROOT}/lib
)

# 设置动态链接（取消静态链接，因为有些库只有动态版本）
set(BUILD_SHARED_LIBS ON)

# 判断是否需要交叉编译
if (EXISTS /work/rv1126BP/host/bin/arm-buildroot-linux-gnueabihf-g++)
#if (EXISTS /work/G3/rv1126_rv1109_linux_sdk2.2.4_yolo6/buildroot/output/rockchip_rv1126_rv1109/host/bin/arm-linux-gnueabihf-g++)

    # 设置目标主机为linux
    set(CMAKE_SYSTEM_NAME Linux)
    # 设置架构为arm
    set(CMAKE_SYSTEM_PROCESSOR arm)

    # 设置编译选项
    #set(GNU_FLAGS "-mfpu=vfp -fPIC -Wall -Wno-psabi -rdynamic -g -O0 -ggdb")
    # 如果需要把二进制文件变得更小，则取消下一行的注释，并把上一行注释掉，需要注意的是，瘦身之后，很多信息会被去掉，意味着用gdb调试会有更多的消息得不到
    set(GNU_FLAGS "-mfpu=vfp -fPIC -Wall -Wno-psabi -rdynamic -s")
    set(CMAKE_CXX_FLAGS "${GNU_FLAGS} ")
    set(CMAKE_C_FLAGS "${GNU_FLAGS}  ")
    set(CMAKE_CXX_STANDARD 17)
    set(CMAKE_CXX_STANDARD_REQUIRED ON)
    # 设置交叉编译工具路径
    # 版本需要固定
    set(tools /work/rv1126BP/host/bin/)
    set(CMAKE_C_COMPILER ${tools}arm-buildroot-linux-gnueabihf-gcc)
    set(CMAKE_CXX_COMPILER ${tools}arm-buildroot-linux-gnueabihf-g++)
    set(CMAKE_SYSROOT /work/rv1126BP/host/arm-buildroot-linux-gnueabihf/sysroot)

#    set(tools /work/G3/rv1126_rv1109_linux_sdk2.2.4_yolo6/buildroot/output/rockchip_rv1126_rv1109/host/bin/)
#    set(CMAKE_C_COMPILER ${tools}arm-linux-gnueabihf-gcc)
#    set(CMAKE_CXX_COMPILER ${tools}arm-linux-gnueabihf-g++)
#    set(CMAKE_SYSROOT /work/G3/rv1126_rv1109_linux_sdk2.2.4_yolo6/buildroot/output/rockchip_rv1126_rv1109/host/arm-buildroot-linux-gnueabihf/sysroot)
    #设置在系统中查找工具
    set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
    #只查找root path的库
    set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
    #只查找root path的头文件
    set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
    #只查找root path的依赖包
    set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)
endif ()


# 添加源码
aux_source_directory(. VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(CommunicationUnit VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(CommunicationUnit/PCConfigure VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(CommunicationUnit/PCConfigure/protocol VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(CommunicationUnit/G3AndMCUUart VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(CommunicationUnit/G3AndMCUUart/protocol VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(CommunicationUnit/RS485_G3 VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(CommunicationUnit/MODBUS VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(CommunicationUnit/MODBUS/protocol VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(DetectUnit VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(MainUnit VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(MediaUnit VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(MediaUnit/CameraOpt VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(MediaUnit/H264Encodec VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(MediaUnit/Mp4Record VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(MediaUnit/JPEGRecord VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit/ConfigureOpt VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit/RealViewOpt VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit/RS485Opt VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit/RS232Opt VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit/SpeakerOpt VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit/GPIOOpt VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit/Uart_mcu VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit/Sensor VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(PeripheralUnit/ADCVoltage VIS_G3_CODER_SOURCE_FILE)

aux_source_directory(UIUnit VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(UIUnit/vo VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(UpgradeUnit VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(utils VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(utils/xudownload VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(utils/bzip2 VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(utils/logFromXWF VIS_G3_CODER_SOURCE_FILE)
aux_source_directory(utils/xulog VIS_G3_CODER_SOURCE_FILE)


include_directories(CommunicationUnit)
include_directories(CommunicationUnit/PCConfigure)
include_directories(CommunicationUnit/PCConfigure/protocol)
include_directories(CommunicationUnit/RS485_G3)
include_directories(CommunicationUnit/G3AndMCUUart)
include_directories(CommunicationUnit/G3AndMCUUart/protocol)
include_directories(CommunicationUnit/MODBUS)
include_directories(CommunicationUnit/MODBUS/protocol)
include_directories(DetectUnit)
include_directories(MainUnit)
include_directories(MediaUnit)
include_directories(utils/easymedia)
include_directories(utils/rkmedia)
include_directories(MediaUnit/CameraOpt)
include_directories(MediaUnit/H264Encodec)
include_directories(MediaUnit/Mp4Record)
include_directories(MediaUnit/JPEGRecord)
include_directories(PeripheralUnit)
include_directories(PeripheralUnit/ConfigureOpt)
include_directories(PeripheralUnit/RealViewOpt)
include_directories(PeripheralUnit/RS485Opt)
include_directories(PeripheralUnit/RS232Opt)
include_directories(PeripheralUnit/SpeakerOpt)
include_directories(PeripheralUnit/GPIOOpt)
include_directories(PeripheralUnit/Uart_mcu)
include_directories(PeripheralUnit/Sensor)
include_directories(PeripheralUnit/ADCVoltage)
include_directories(UIUnit)
include_directories(UIUnit/vo)
include_directories(UpgradeUnit)
include_directories(utils)
include_directories(utils/xudownload)
include_directories(utils/bzip2)
include_directories(utils/logFromXWF)
include_directories(utils/xulog)
include_directories(.)


#####################################################################侧边算法相关#####################################################################
include_directories(utils/algorithmSrc/src_bsd)  #lyc
#include_directories(DetectUnit/src/CamCap)  #lyc
include_directories(utils/algorithmSrc/src_bsd/maskCodec)  #lyc
include_directories(utils/algorithmSrc/src_bsd/ini)  #lyc
include_directories(utils/algorithmSrc/src_bsd/rknnYolo)  #lyc
include_directories(utils/algorithmSrc/src_bsd/3rdparty/librknn_api/include)  #lyc
include_directories(utils/algorithmSrc/src_bsd/3rdparty/rga/include)  #lyc
include_directories(utils/algorithmSrc/src_bsd/3rdparty/drm/include)  #lyc
include_directories(utils/algorithmSrc/src_bsd/3rdparty/drm/include/libdrm)  #lyc
include_directories(utils/algorithmSrc/src_bsd/yolo)  #lyc
include_directories(utils/algorithmSrc/src_bsd/stb)  #lyc

# XS508 已屏蔽
#include_directories(utils/algorithmSrc/src_bsd/3rdparty/XS508)
#include_directories(utils/algorithmSrc/src_bsd/3rdparty/XS508/include/i2c)
#include_directories(utils/algorithmSrc/src_bsd/3rdparty/XS508/include/linux)
#include_directories(utils/algorithmSrc/src_bsd/3rdparty/XS508/lib_rv1126)
aux_source_directory(utils/algorithmSrc/src_bsd VIS_G3_CODER_SOURCE_FILE)  #lyc
aux_source_directory(utils/algorithmSrc/src_bsd/maskCodec VIS_G3_CODER_SOURCE_FILE)  #lyc
aux_source_directory(utils/algorithmSrc/src_bsd/rknnYolo VIS_G3_CODER_SOURCE_FILE)  #lyc
aux_source_directory(utils/algorithmSrc/src_bsd/ini VIS_G3_CODER_SOURCE_FILE)  #lyc
#aux_source_directory(utils/algorithmSrc/src_bsd/3rdparty/XS508 VIS_G3_CODER_SOURCE_FILE)  #lyc 已屏蔽
#aux_source_directory(utils/algorithmSrc/src_bsd/3rdparty/XS508/lib_rv1126 VIS_G3_CODER_SOURCE_FILE)  #lyc 已屏蔽
aux_source_directory(utils/algorithmSrc/src_bsd/stb VIS_G3_CODER_SOURCE_FILE)  #lyc
aux_source_directory(utils/algorithmSrc/src_bsd/yolo VIS_G3_CODER_SOURCE_FILE)  #lyc

#####################################################################侧边算法相关#####################################################################



#####################################################################DSM（天津地铁）算法相关#####################################################################
include_directories(utils/algorithmSrc/src_dsm_mt)
include_directories(utils/algorithmSrc/src_dsm_mt/3rdparty/librknn_api/include)
include_directories(utils/algorithmSrc/src_dsm_mt/3rdparty/rga/include)
include_directories(utils/algorithmSrc/src_dsm_mt/3rdparty/drm/include)
include_directories(utils/algorithmSrc/src_dsm_mt/3rdparty/drm/include/libdrm)
# XS508 已屏蔽
#include_directories(utils/algorithmSrc/src_dsm_mt/3rdparty/XS508)
#include_directories(utils/algorithmSrc/src_dsm_mt/3rdparty/XS508/include/i2c)
#include_directories(utils/algorithmSrc/src_dsm_mt/3rdparty/XS508/include/linux)
#include_directories(utils/algorithmSrc/src_dsm_mt/3rdparty/XS508/lib_rv1126)
aux_source_directory(utils/algorithmSrc/src_dsm_mt VIS_G3_CODER_SOURCE_FILE)
#aux_source_directory(utils/algorithmSrc/src_dsm_mt/3rdparty/XS508 VIS_G3_CODER_SOURCE_FILE)  # 已屏蔽
#aux_source_directory(utils/algorithmSrc/src_dsm_mt/3rdparty/XS508/lib_rv1126 VIS_G3_CODER_SOURCE_FILE)  # 已屏蔽
#####################################################################DSM算法相关#####################################################################



#####################################################################手势算法相关#####################################################################
include_directories(utils/algorithmSrc/src_gesture)  #lyc
aux_source_directory(utils/algorithmSrc/src_gesture VIS_G3_CODER_SOURCE_FILE)  #lyc
#####################################################################手势算法相关#####################################################################



#####################################################################adas算法相关#####################################################################
include_directories(utils/algorithmSrc/src_adas_160_area)  #lyc
aux_source_directory(utils/algorithmSrc/src_adas_160_area VIS_G3_CODER_SOURCE_FILE)  #lyc
#####################################################################adas算法相关#####################################################################



######################################################################SBST算法相关#####################################################################
include_directories(utils/algorithmSrc/src_sbst)  #lyc
aux_source_directory(utils/algorithmSrc/src_sbst VIS_G3_CODER_SOURCE_FILE)  #lyc
######################################################################adas算法相关#####################################################################



#####################################################################DSM（新加坡MUV）算法相关#####################################################################
include_directories(utils/algorithmSrc/src_dsm_muv)
include_directories(utils/algorithmSrc/src_dsm_muv/3rdparty/librknn_api/include)
include_directories(utils/algorithmSrc/src_dsm_muv/3rdparty/rga/include)
include_directories(utils/algorithmSrc/src_dsm_muv/3rdparty/drm/include)
include_directories(utils/algorithmSrc/src_dsm_muv/3rdparty/drm/include/libdrm)
# XS508 已屏蔽
#include_directories(utils/algorithmSrc/src_dsm_muv/3rdparty/XS508)
#include_directories(utils/algorithmSrc/src_dsm_muv/3rdparty/XS508/include/i2c)
#include_directories(utils/algorithmSrc/src_dsm_muv/3rdparty/XS508/include/linux)
#include_directories(utils/algorithmSrc/src_dsm_muv/3rdparty/XS508/lib_rv1126)
aux_source_directory(utils/algorithmSrc/src_dsm_muv VIS_G3_CODER_SOURCE_FILE)
#aux_source_directory(utils/algorithmSrc/src_dsm_muv/3rdparty/XS508 VIS_G3_CODER_SOURCE_FILE)  # 已屏蔽
#aux_source_directory(utils/algorithmSrc/src_dsm_muv/3rdparty/XS508/lib_rv1126 VIS_G3_CODER_SOURCE_FILE)  # 已屏蔽
#####################################################################DSM算法相关#####################################################################




#####################################################################新的算法选择相关#####################################################################
include_directories(DetectUnit/alarm_decision_list)
aux_source_directory(DetectUnit/alarm_decision_list VIS_G3_CODER_SOURCE_FILE)

include_directories(DetectUnit/detecter_list)
aux_source_directory(DetectUnit/detecter_list VIS_G3_CODER_SOURCE_FILE)
#####################################################################新的算法选择相关#####################################################################

#####################################################################NDS通信的MQTT相关#####################################################################
include_directories(utils/paho_mqtt)
aux_source_directory(utils/paho_mqtt VIS_G3_CODER_SOURCE_FILE)

include_directories(PeripheralUnit/ndsOpt)
aux_source_directory(PeripheralUnit/ndsOpt VIS_G3_CODER_SOURCE_FILE)

include_directories(PeripheralUnit/RTMPPush)
aux_source_directory(PeripheralUnit/RTMPPush VIS_G3_CODER_SOURCE_FILE)

include_directories(CommunicationUnit/G3NDSMQTT)
aux_source_directory(CommunicationUnit/G3NDSMQTT VIS_G3_CODER_SOURCE_FILE)

include_directories(utils/json)
aux_source_directory(utils/json VIS_G3_CODER_SOURCE_FILE)

include_directories(utils/rtmp)
aux_source_directory(utils/rtmp VIS_G3_CODER_SOURCE_FILE)
#####################################################################NDS通信的MQTT相关#####################################################################



#####################################################################DSM（标准版本）算法相关#####################################################################
include_directories(utils/algorithmSrc/src_dsm_std)

aux_source_directory(utils/algorithmSrc/src_dsm_std VIS_G3_CODER_SOURCE_FILE)
#####################################################################DSM算法相关#####################################################################

#####################################################################DSM（识别安全带）算法相关#####################################################################
include_directories(utils/algorithmSrc/src_dsm_fb)

aux_source_directory(utils/algorithmSrc/src_dsm_fb VIS_G3_CODER_SOURCE_FILE)
#####################################################################DSM算法相关#####################################################################


















# 取消静态链接选项，改用动态链接
#set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static")

# 生成执行文件
add_executable(vis_g3_software ${VIS_G3_CODER_SOURCE_FILE})

# XS508 相关库已屏蔽
#target_link_libraries(vis_g3_software
#    -Wl,--start-group
#    ${CMAKE_SOURCE_DIR}/utils/algorithmSrc/src_bsd/3rdparty/XS508/lib_rv1126/lib_WPZN508G_R1.rv1126.o
#    -Wl,--end-group
#    -static)
target_link_libraries(vis_g3_software easymedia)
target_link_libraries(vis_g3_software pthread)
target_link_libraries(vis_g3_software rkaiq)
target_link_libraries(vis_g3_software third_media)
target_link_libraries(vis_g3_software rt)
target_link_libraries(vis_g3_software liveMedia)
target_link_libraries(vis_g3_software groupsock)
target_link_libraries(vis_g3_software BasicUsageEnvironment)
target_link_libraries(vis_g3_software UsageEnvironment)
target_link_libraries(vis_g3_software PocoFoundation)
target_link_libraries(vis_g3_software PocoCrypto)
target_link_libraries(vis_g3_software ssl)
target_link_libraries(vis_g3_software crypto)
target_link_libraries(vis_g3_software curl)

target_link_libraries(vis_g3_software mp4v2)
target_link_libraries(vis_g3_software rga)
target_link_libraries(vis_g3_software pthread usb-1.0)
target_link_libraries(vis_g3_software yaml-cpp)
target_link_libraries(vis_g3_software iio)
target_link_libraries(vis_g3_software rknnrt)  #lyc
target_link_libraries(vis_g3_software opencv_core)  #lyc
target_link_libraries(vis_g3_software opencv_highgui)  #lyc
target_link_libraries(vis_g3_software opencv_imgproc)  #lyc
target_link_libraries(vis_g3_software opencv_flann)  #lyc
target_link_libraries(vis_g3_software opencv_imgcodecs)  #lyc
target_link_libraries(vis_g3_software opencv_video)  #lyc
target_link_libraries(vis_g3_software opencv_videoio)  #lyc
target_link_libraries(vis_g3_software dl)  #lyc
target_link_libraries(vis_g3_software usb-1.0)  #lyc
target_link_libraries(vis_g3_software boost_filesystem)
target_link_libraries(vis_g3_software exiv2)
target_link_libraries(vis_g3_software pcap)


#target_link_libraries(vis_g3_software boost_thread)
#target_link_libraries(vis_g3_software boost_system)
#target_link_libraries(vis_g3_software boost_date_time)





