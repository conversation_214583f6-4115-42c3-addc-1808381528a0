# Makefile for image conversion example
# 用于编译图像转换示例程序

CXX = g++
CXXFLAGS = -std=c++11 -Wall -O2
INCLUDES = -I. -Iutils -IMediaUnit -IMainUnit -Iutils/algorithmSrc/src_bsd/3rdparty/rga/include
LIBS = -lpthread -lrga

# 目标文件
TARGET = image_conversion_example
SOURCE = image_conversion_example.cpp

# 依赖的对象文件
OBJS = utils/XuFile.o utils/XuRGAUtils.o

# 默认目标
all: $(TARGET)

# 编译主程序
$(TARGET): $(SOURCE) $(OBJS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^ $(LIBS)

# 编译XuFile.o
utils/XuFile.o: utils/XuFile.cpp utils/XuFile.h
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 编译XuRGAUtils.o
utils/XuRGAUtils.o: utils/XuRGAUtils.cpp utils/XuRGAUtils.h
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	rm -f $(TARGET) $(OBJS)

# 安装（复制到目标设备）
install: $(TARGET)
	@echo "将程序复制到目标设备..."
	@echo "请手动将 $(TARGET) 复制到目标设备并运行"

# 运行程序
run: $(TARGET)
	./$(TARGET)

.PHONY: all clean install run
