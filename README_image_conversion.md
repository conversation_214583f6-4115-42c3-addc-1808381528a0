# 图像转换功能说明

## 概述

本项目完善了 `XuRGAUtils::imageTransformation` 函数的使用，实现了从 YUV420SP 格式到 RGBA8888 格式的图像转换，并将转换后的图像保存到 `/userdata/` 目录。

## 功能特性

1. **图像格式转换**：支持多种图像格式之间的转换
   - YUV420SP (NV21) → RGBA8888
   - YUV420SP (NV21) → RGB888
   - 支持图像缩放

2. **自动保存**：转换后的图像自动保存到 `/userdata/` 目录
   - 文件名包含尺寸和时间戳
   - 根据格式自动选择文件扩展名

3. **内存管理**：自动处理缓冲区分配和释放

## 文件说明

### 主要文件

- `image_conversion_example.cpp` - 独立的图像转换示例程序
- `main.cpp` - 修改后的主程序，集成了图像转换功能
- `Makefile.image_conversion` - 用于编译示例程序的Makefile

### 核心函数

#### `performImageTransformation()`
```cpp
int performImageTransformation(int srcWidth, int srcHeight, int srcFormat, 
                              int dstWidth, int dstHeight, int dstFormat)
```
- 执行完整的图像转换流程
- 自动计算缓冲区大小
- 创建测试图案
- 保存转换结果

#### `saveConvertedImage()`
```cpp
bool saveConvertedImage(uint8_t* imageData, int dataSize, int width, int height, const std::string& format)
```
- 保存图像数据到文件
- 自动创建目录
- 生成带时间戳的文件名

## 使用方法

### 1. 编译示例程序

```bash
# 使用专用的Makefile编译
make -f Makefile.image_conversion

# 或者手动编译
g++ -std=c++11 -I. -Iutils -o image_conversion_example image_conversion_example.cpp utils/XuFile.o utils/XuRGAUtils.o -lrga -lpthread
```

### 2. 运行示例程序

```bash
./image_conversion_example
```

### 3. 在现有代码中使用

```cpp
#include "utils/XuRGAUtils.h"
#include "utils/XuFile.h"

// 创建RGA工具实例
XuRGAUtils rgaUtils;

// 分配缓冲区
uint8_t* srcBuffer = new uint8_t[1280 * 720 * 3/2];  // YUV420SP
uint8_t* dstBuffer = new uint8_t[1280 * 720 * 4];    // RGBA8888

// 执行转换
int result = rgaUtils.imageTransformation(
    1280, 720, XuRGAUtils::IMG_TYPE_NV21, srcBuffer,    // 源图像
    1280, 720, XuRGAUtils::IMG_TYPE_RGBA8888, dstBuffer // 目标图像
);

if (result > 0) {
    // 保存转换后的图像
    saveConvertedImage(dstBuffer, result, 1280, 720, "rgba");
}

// 释放内存
delete[] srcBuffer;
delete[] dstBuffer;
```

## 支持的图像格式

### 输入格式
- `XuRGAUtils::IMG_TYPE_NV21` - YUV420SP (NV21)
- `XuRGAUtils::IMG_TYPE_NV12` - YUV420SP (NV12)
- `XuRGAUtils::IMG_TYPE_RGB888` - RGB888
- `XuRGAUtils::IMG_TYPE_BGR888` - BGR888
- `XuRGAUtils::IMG_TYPE_RGBA8888` - RGBA8888
- `XuRGAUtils::IMG_TYPE_BGRA8888` - BGRA8888

### 输出格式
- 同输入格式，支持格式间转换

## 输出文件

转换后的图像文件保存在 `/userdata/` 目录，文件名格式：
```
converted_[宽度]x[高度]_[时间戳].[扩展名]
```

例如：
- `converted_1280x720_1640995200000.rgba`
- `converted_640x480_1640995201000.rgb`

## 示例输出

```
图像转换示例程序
功能：使用XuRGAUtils进行图像格式转换并保存到/userdata/目录

示例1: YUV420SP -> RGBA8888
=== 开始图像转换 ===
源图像: 1280x720, 格式: 18
目标图像: 1280x720, 格式: 0
已创建YUV测试图案
正在执行RGA图像转换...
图像转换成功！输出数据大小: 3686400 bytes (预期: 3686400 bytes)
图像转换并保存成功: /userdata/converted_1280x720_1640995200000.rgba, 尺寸: 1280x720, 大小: 3686400 bytes
图像已成功保存到 /userdata/ 目录
=== 图像转换完成 ===
```

## 注意事项

1. **权限**：确保程序有写入 `/userdata/` 目录的权限
2. **内存**：大尺寸图像转换需要足够的内存空间
3. **RGA硬件**：需要Rockchip RGA硬件支持
4. **格式支持**：不同的RGA版本可能支持的格式有所不同

## 故障排除

### 常见错误

1. **转换失败（返回值 < 0）**
   - 检查RGA驱动是否正常加载
   - 确认图像格式是否支持
   - 验证缓冲区大小是否正确

2. **保存失败**
   - 检查 `/userdata/` 目录权限
   - 确认磁盘空间是否充足
   - 验证文件路径是否有效

3. **内存分配失败**
   - 检查系统可用内存
   - 考虑使用较小的图像尺寸

## 扩展功能

可以进一步扩展的功能：
- 支持更多图像格式
- 添加图像旋转和翻转
- 实现批量转换
- 添加图像质量验证
- 支持不同的保存路径
