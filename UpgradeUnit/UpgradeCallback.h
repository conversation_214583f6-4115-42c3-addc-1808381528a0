//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/1.
//

#ifndef VIS_G3_SOFTWARE_UPGRADECALLBACK_H
#define VIS_G3_SOFTWARE_UPGRADECALLBACK_H


class UpgradeCallback {
public:
    /**
     * 获取到一个升级结果
     *
     * @param fileType ： 升级文件的类型
     * @param result ： 结果
     * @param ip ： 请求端的IP
     * @param port ： 请求端的端口
     */
    virtual void onGetUpgradeResult(const int fileType, const int result, const char *ip, const int port);

private:

};


#endif //VIS_G3_SOFTWARE_UPGRADECALLBACK_H
