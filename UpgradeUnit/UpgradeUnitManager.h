//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/28.
//

#ifndef VIS_G3_SOFTWARE_UPGRADEUNITMANAGER_H
#define VIS_G3_SOFTWARE_UPGRADEUNITMANAGER_H

#include <Poco/Runnable.h>
#include <netinet/in.h>
#include "UpgradeCallback.h"
#include "CodeUtils.h"
#include "XuString.h"
#include "XuFile.h"
#include <Poco/DigestEngine.h>
#include <Poco/DigestStream.h>
#include <Poco/MD5Engine.h>
#include <Poco/StreamCopier.h>
#include <fstream>
#include <string>


class UpgradeUnitManager : public Poco::Runnable {
public:

    /* 升级的文件的下载方式-----Socket */
    const static int UPGRADE_FILE_DOWNLAOD_TYPE_SOCKET = 0;
    /* 升级的文件的下载方式-----HTTP */
    const static int UPGRADE_FILE_DOWNLAOD_TYPE_HTTP = 1;

    /* 升级的文件的加密类型-----不加密 */
    const static int UPGRADE_FILE_ENCRYPTION_TYPE_NORMAL = 0;
    /* 升级的文件的加密类型-----不加密 */
    const static int UPGRADE_FILE_ENCRYPTION_TYPE_INTERVAL_INVERT = 1;

    /* TCP端口号 */
    const static int UPGRADE_FILE_UPLOAD_TCP_PORT = 6667;


    /* 升级文件头部的长度 */
    const int UPHRADE_FILE_HEAD_LEN = 30;

    /* 为解密 但是去掉了文件头的文件路径 */
    const char *UPHRADE_FILE_PATH_NOT_DECRYP = "/userdata/media/notDecrypFile";
    /* 从客户端接收过来的原始文件 */
    const char *UPHRADE_FILE_PATH_SRC = "/userdata/media/uploadfile";

    const char *UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD = "/userdata/media/g3DevicesSoftwardTemp";



    const char *UPGRADE_FILE_PATH_G3DEVICESSOFTWARD = "/userdata/media/g3DevicesSoftward";
    const char *UPHRADE_FILE_PATH_UPGRADE_FILE_G3MCU = "/tmp/g3MCU";
    const char *UPHRADE_FILE_PATH_UPGRADE_FILE_G4MCU = "/tmp/g4MCU";
    const char *UPHRADE_FILE_PATH_UPGRADE_FILE_GPSUNIT = "/tmp/gpsUnit";
    const char *UPHRADE_FILE_PATH_UPGRADE_FILE_L3 = "/tmp/l3";
    const char *UPHRADE_FILE_PATH_UPGRADE_FILE_BUZZER = "/tmp/buzzer";
    const char *UPHRADE_FILE_PATH_UPGRADE_FILE_L4 = "/tmp/l4";
    const char *UPHRADE_FILE_PATH_UPGRADE_FILE_GPSBOX = "/tmp/gpsbox";

    /*文件类型：G3设备软件 */
    const static int UPGRADE_FILE_TYPE_G3DEVICE_SOFTWARD = 0;
    /*文件类型：G3的MCU软件 */
    const static int UPGRADE_FILE_TYPE_G3MCU_SOFTWARD = 1;
    /*文件类型：G4的MCU软件 */
    const static int UPGRADE_FILE_TYPE_G4MCU_SOFTWARD = 2;
    /*文件类型：GPS盒子的软件 */
    const static int UPGRADE_FILE_TYPE_GPSUNIT_SOFTWARD = 3;
    /*文件类型：L3的软件 */
    const static int UPGRADE_FILE_TYPE_L3_SOFTWARD = 4;
    /*文件类型：提示器的软件 */
    const static int UPGRADE_FILE_TYPE_BUZZER_SOFTWARD = 5;
    /*文件类型：C3(3568)设备软件 */
    const static int UPGRADE_FILE_TYPE_C3_SOFTWARD = 6;
    /*文件类型：L4软件 */
    const static int UPGRADE_FILE_TYPE_L4_SOFTWARD = 7;
    /*文件类型：报警声音更新包 */
    const static int UPGRADE_FILE_TYPE_ALARM_SOUND_FILE_PACK = 8;


    const static int UPGRADE_RESULT_FAILED = -1;
    const static int UPGRADE_RESULT_SUCCESS = 0;
    const static int RECV_BUF_MAX_SIZE = 1024 * 1024 * 1;

    int initBySocket(int fileType, int fileLen, uint8_t *md5, int tcpPort, const char *ip, const int port, UpgradeCallback &upgradeCallback);

    int initByHttp(int fileType, std::string fileUrl, UpgradeCallback &upgradeCallback);

    int readData(const int fd, const char *ip, const int ipLen, const int port);

    int checkFileData();

    void run() override;

    bool isUpgrading() const;

    void setUpgrading(bool upgrading);

    int getMD5OfFile(const char *filePath, uint8_t *md5Buf);

    int getMD5OfBytes(uint8_t *bytes, int bytesLen, uint8_t *md5Buf);

    int getNotDecrypFile(const char *srcFilePath, const char *desFilePath);

    int doUpgrade(const std::string filepath, const int fileType, const int fileVersion = 0);

private:



    UpgradeCallback *callback;
    int curFileDownloadType = UPGRADE_FILE_DOWNLAOD_TYPE_SOCKET;


    bool upgrading = false;
    bool tcpOpen = false;
    int curFileType = -1;
    std::string curFileUrl;
    int curFileLen = -1;
    char clientIp[20] = {0x00};
    int clientPort = -1;
    uint8_t fileHead[30] = {0x00};
    uint8_t fileMD5[16] = {0x00};
    int curTcpPort;
    int listenfd;
    struct sockaddr_in servaddr;
    uint8_t *recvBuf = nullptr;
    int readedLen;

    std::string alarmSoundFileMaskHead = "*VispectAlarmSoundUpgradePack*";

    enum ALARM_SOUND_TYPE{

        ALARM_SOUND_TYPE_EFFECTS = 0,
        ALARM_SOUND_TYPE_VOICE = 1,
        ALARM_SOUND_TYPE_CINFIG = 2,
    };


    void downloadBySocket();

    void downloadByHttp();

    bool upgradeAlarmSound(const std::string filepath);


    /**
     * 清理掉临时文件
     */
    void clearTempFile();


};


#endif //VIS_G3_SOFTWARE_UPGRADEUNITMANAGER_H
