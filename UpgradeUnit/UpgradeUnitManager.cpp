//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/28.
//

#include <cstring>
#include <unistd.h>
#include <arpa/inet.h>
#include <sstream>
#include "UpgradeUnitManager.h"
#include "XuShell.h"
#include "XuLog.h"
#include "G3_Configuration.h"
#include "G3DetectionDefind.h"
#include "XuMemGeter.h"
#include "XuHttpUtil.h"

static const char *TAG = "UpgradeUnitManager";

int UpgradeUnitManager::initBySocket(int fileType, int fileLen, uint8_t *md5, int tcpPort, const char *ip, const int port,
                             UpgradeCallback &upgradeCallback) {
    int ret = -1;
    if (!upgrading) {
        curFileDownloadType = UPGRADE_FILE_DOWNLAOD_TYPE_SOCKET;
        curFileType = fileType;
        curTcpPort = tcpPort;
        curFileLen = fileLen;
        (void) memcpy(fileMD5, md5, sizeof(fileMD5));
        callback = &upgradeCallback;
        (void) memcpy(clientIp, ip, strlen(ip));
        clientPort = port;
        ret = 0;
    }
    return ret;
}

void UpgradeUnitManager::run() {
    std::string pthreadName = "UpgradeUnit_";
    pthreadName.append(std::to_string(curFileType));
    pthread_setname_np(pthread_self(), pthreadName.c_str());

    upgrading = true;
    switch (curFileDownloadType) {
        case UPGRADE_FILE_DOWNLAOD_TYPE_SOCKET:{
            downloadBySocket();
        }
            break;

        case UPGRADE_FILE_DOWNLAOD_TYPE_HTTP:{
            downloadByHttp();
        }
            break;

    }
    upgrading = false;

    pthread_setname_np(pthread_self(), "Finish");
}

int UpgradeUnitManager::readData(const int fd, const char *clientIp, const int ipLen, const int clientPort) {
    int revcLen = 0;
    recvBuf = static_cast<uint8_t *>(malloc(RECV_BUF_MAX_SIZE));
    if(recvBuf != nullptr){
        memset(recvBuf, 0x00, RECV_BUF_MAX_SIZE);
        readedLen = 0;
        FILE *uploadFile = fopen(UPHRADE_FILE_PATH_SRC, "w");
        if (nullptr != uploadFile) {
            fd_set readfds;//保存读文件描述符集合
            while (1) {
                //把readfds清空
                FD_ZERO(&readfds);
                //把要监听的sockfd添加到readfds中
                FD_SET(fd, &readfds);
                timeval tv;
                tv.tv_sec = 60;
                tv.tv_usec = 0;
                //用select类监听sockfd 阻塞状态
                int ret = select(fd + 1, &readfds, nullptr, nullptr, &tv);
                if (ret > 0 && FD_ISSET(fd, &readfds)) {

                    /* revcLen为接收到的长度   如果连接已中止，返回0。如果发生错误，返回-1，应用程序可通过perror()获取相应错误信息 */
                    revcLen = recv(fd, recvBuf, RECV_BUF_MAX_SIZE, MSG_DONTWAIT);
                    if (revcLen > 0) {
                        readedLen += revcLen;
                        printf("from upgrade socket %s get %d bytes! \n", clientIp, revcLen);
                        /* 读到数据了  那么直接写到文件里去 */
                        fwrite(recvBuf, 1, revcLen, uploadFile);
                        fflush(uploadFile);
                        /* 读到传输过来的长度就停止  开始进行校验 */
                        if (readedLen >= curFileLen) {
                            /* 如果重启了，升级会从头开始来，所以不用管这个临时文件，也就不用fsync拖慢性能 */
                            /* 关闭文件 */
                            fclose(uploadFile);
                            printf("recv file data done! readedLen=%d  \n", readedLen);
                            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "recv file data done! readedLen="<< readedLen << XU_LOG_END;
                            /* 如果是MRV220的升级软件，那么需要解密，如果不是那么都不需要解密 */
                            if(curFileType == UPGRADE_FILE_TYPE_G3DEVICE_SOFTWARD){
                                /* 取出文件头跟未解密的文件 */
                                int writeRet = getNotDecrypFile(UPHRADE_FILE_PATH_SRC, UPHRADE_FILE_PATH_NOT_DECRYP);
                                /* 看看是不是写成功了 */
                                if (writeRet > 0) {
                                    /* 写成功了 校验一下  如果成功了则解密完写入文件 */
                                    if (checkFileData() == 0) {
                                        /* 校验成功了，那么删除下上次升级可能存在文件 */
                                        XuFile::getInstance().deleteFile(UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD);
                                        /* 校验成功了则进行解密 */
                                        if (XuFile::getInstance().doDecryptForUpgrade(UPHRADE_FILE_PATH_NOT_DECRYP,
                                                                                      UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD,
                                                                                      fileHead) == 0) {
                                            /* 解密成功了，那么需要校验一下MD5值是否正确了 */
                                            uint8_t mFileMD5[16] = {0x00};
                                            getMD5OfFile(UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD, mFileMD5);
//                                    printf("fileMD5=%s   mFileMD5=%s   \n",
//                                           XuString::getInstance().byteArrayToString(fileHead + 5, 16).c_str(),
//                                           XuString::getInstance().byteArrayToString(mFileMD5, 16).c_str());
                                            if (XuString::getInstance().cmpTwoByteArray(mFileMD5, sizeof(mFileMD5),
                                                                                        fileHead + 5, 16)) {
                                                /* 校验成功了，把升级文件移动到正式路径下 */
                                                std::string cpcmd = "cp ";
                                                cpcmd.append(UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD);
                                                cpcmd.append(" ");
                                                cpcmd.append(UPGRADE_FILE_PATH_G3DEVICESSOFTWARD);
                                                cpcmd.append(" && sync");
                                                system(cpcmd.c_str());
                                                /* 移动完成，该干什么就干什么了 */
                                                upgrading = false;
                                                close(fd);
                                                close(listenfd);
                                                /* 获取更新文件的版本号 */
                                                int fileVersion = CodeUtils::getInstance().BbToUint32(fileHead+22);
                                                /* 执行对应的升级操作 */
                                                doUpgrade(UPGRADE_FILE_PATH_G3DEVICESSOFTWARD, curFileType,fileVersion);
                                                /* 清理掉那些临时文件 */
                                                clearTempFile();
                                            } else {
                                                upgrading = false;
                                                close(fd);
                                                close(listenfd);
                                                callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp,
                                                                             clientPort);
                                                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "after decryp file, check md5 failed!  fileMD5="<< XuString::getInstance().byteArrayToString(fileHead + 5, 16).c_str() << "   mFileMD5=" << XuString::getInstance().byteArrayToString(mFileMD5, 16).c_str() << XU_LOG_END;
                                                /* 校验失败需要删除掉临时文件 */
                                                    std::string cmd = "rm -rf ";
                                                    cmd.append(UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD);
                                                    cmd.append(" && sync");
                                                    system(cmd.c_str());
                                                /* 清理掉那些临时文件 */
                                                clearTempFile();
                                            }
                                        } else {
                                            upgrading = false;
                                            close(fd);
                                            close(listenfd);
                                            callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp,
                                                                         clientPort);
                                            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "decryp file failed! " << XU_LOG_END;
                                            /* 清理掉那些临时文件 */
                                            clearTempFile();
                                        }
                                        break;

                                    } else {
                                        upgrading = false;
                                        close(fd);
                                        close(listenfd);
                                        callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
                                        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "checkFileData() != 0 " << XU_LOG_END;
                                        /* 清理掉那些临时文件 */
                                        clearTempFile();
                                    }

                                } else {
                                    upgrading = false;
                                    close(fd);
                                    close(listenfd);
                                    callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
                                    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "write not head file failed! " << XU_LOG_END;
                                    /* 清理掉那些临时文件 */
                                    clearTempFile();
                                }
                            }else{
                                /* 不是MRV220的升级软件，那么就直接拿去用吧 */
                                upgrading = false;
                                close(fd);
                                close(listenfd);
                                /* 执行对应的升级操作 */
                                doUpgrade(UPHRADE_FILE_PATH_SRC, curFileType);
                            }
                            break;
                        }
                    } else {
                        /* 如果返回0  那么说明连接被中断了  这个客户端就可以关闭了 */
                        upgrading = false;
                        close(fd);
                        close(listenfd);
                        callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
                        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "upgrade client " << clientIp << " is closed!   readedLen=" << readedLen << " curFileLen=" << curFileLen << XU_LOG_END;
                        /* 清理掉那些临时文件 */
                        clearTempFile();
                        break;
                    }
                } else {
                    upgrading = false;
                    close(fd);
                    close(listenfd);
                    callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
                    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "upgrade client " << clientIp << " is not data > 60s!   readedLen=" << readedLen << " curFileLen=" << curFileLen << XU_LOG_END;
                    /* 清理掉那些临时文件 */
                    clearTempFile();

                    break;
                }


            }
        } else {
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << UPHRADE_FILE_PATH_SRC << "  open failed!" << XU_LOG_END;
            upgrading = false;
            close(listenfd);
            callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
            /* 清理掉那些临时文件 */
            clearTempFile();
        }
        /* 关掉 */
        close(fd);
        /* 释放 */
        free(recvBuf);
    }else{
        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << UPHRADE_FILE_PATH_SRC << "  malloc failed!" << XU_LOG_END;
        upgrading = false;
        close(listenfd);
        callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
    }

    return 0;
}

int UpgradeUnitManager::checkFileData() {
    int ret = -1;
    /*  先校验长度对不对 */
    if (readedLen == curFileLen) {
        /* 先拿出来第一个字节  比对文件类型是否正确 */
        if (curFileType == fileHead[0]) {
            /* 比对下未加密的文件长度对不对 */
            if (static_cast<uint32_t>(curFileLen) == ((CodeUtils::getInstance().BbToUint32(fileHead + 1)) + 30)) {
                /* 比对下加密后的文件MD5值是否正确 */
                uint8_t mFileMD5[16] = {0x00};
                getMD5OfFile(UPHRADE_FILE_PATH_SRC, mFileMD5);
                if (XuString::getInstance().cmpTwoByteArray(mFileMD5, sizeof(mFileMD5), fileMD5, sizeof(fileMD5))) {
                    ret = 0;
                    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "MD5 check SUCCESS! mFileMD5=" << XuString::getInstance().byteArrayToString(mFileMD5, 16).c_str() << "  fileMD5=" << XuString::getInstance().byteArrayToString(fileMD5, 16).c_str() << XU_LOG_END;
                } else {
                    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "befor decryp check MD5 fialed  mFileMD5=" << XuString::getInstance().byteArrayToString(mFileMD5, 16).c_str() << "  fileMD5=" << XuString::getInstance().byteArrayToString(fileMD5, 16).c_str() << XU_LOG_END;
                }
            } else {
                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "curFileLen(" << curFileLen << ") == (CodeUtils::getInstance().BbToUint32(recvBuf + 1)(" << CodeUtils::getInstance().BbToUint32(fileHead + 1) << ") " << XU_LOG_END;
            }
        } else {
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "curFileType(" << curFileType << ") != fileHead[0](" << fileHead[0] << ") " << XU_LOG_END;
        }
    } else {
        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "data len error!  readedLen=" << readedLen << "    curFileLen=" << curFileLen << XU_LOG_END;
    }
    return ret;
}

bool UpgradeUnitManager::isUpgrading() const {
    return upgrading;
}

void UpgradeUnitManager::setUpgrading(bool upgrading) {
    UpgradeUnitManager::upgrading = upgrading;
}

int UpgradeUnitManager::getMD5OfFile(const char *filePath, uint8_t *md5Buf) {
    Poco::MD5Engine md5_engine;
    Poco::DigestOutputStream output_stream(md5_engine);
    std::ifstream file_stream(filePath);
    Poco::StreamCopier::copyStream(file_stream, output_stream);
    output_stream.close();
    auto md5_val = md5_engine.digest();
    (void) memcpy(md5Buf, md5_val.data(), md5_val.size());
    return md5_val.size();
}

int UpgradeUnitManager::getMD5OfBytes(uint8_t *bytes, int bytesLen, uint8_t *md5Buf) {
    Poco::MD5Engine md5_engine;
    Poco::DigestOutputStream output_stream(md5_engine);
    std::string bytesTemp = "";
    bytesTemp.append(reinterpret_cast<const char *>(bytes), bytesLen);
    std::istringstream istr(bytesTemp);
    Poco::StreamCopier::copyStream(istr, output_stream);
    output_stream.close();
    auto md5_val = md5_engine.digest();
    printf("getMD5OfBytes  md5_val.size()=%d \n", md5_val.size());
    (void) memcpy(md5Buf, md5_val.data(), md5_val.size());
    return md5_val.size();
}

int UpgradeUnitManager::getNotDecrypFile(const char *srcFilePath, const char *desFilePath) {
    int ret = 0;
    FILE *srfFile = fopen(srcFilePath, "rb");
    FILE *desFile = fopen(desFilePath, "wb");

    if ((nullptr != srfFile) && (nullptr != desFile)) {
        /* 先把30个字节的文件头拿出来 */
        fread(fileHead, 30, 1, srfFile);
        printf("ret=%d   fileHead:", ret);
        for (int i = 0; i < 30; i++) {
            printf(" %02x", fileHead[i]);
        }
        printf("\n");

        /* 把去掉文件头的数据拿出来 */
        int readFileLen = 0;
        while ((readFileLen = fread(recvBuf, 1, RECV_BUF_MAX_SIZE, srfFile)) > 0) {
            fwrite(recvBuf, readFileLen, 1, desFile);
            fflush(desFile);
            ret = ret + readFileLen;
        }
        fclose(srfFile);
        fflush(desFile);
        /* 如果重启了，升级会从头开始来，所以不用管这个临时文件，也就不用fsync拖慢性能 */
        fclose(desFile);

    } else {
        ret = -1;
    }


    return ret;
}

int UpgradeUnitManager::doUpgrade(const std::string filepath, const int fileType, const int fileVersion) {
    /* 不同的文件进行不同的操作 */
    switch (fileType) {
        case UPGRADE_FILE_TYPE_G3DEVICE_SOFTWARD: {
//            /* 删除掉旧的模型，防止模型堆积过多(这里是临时使用一下，后续不能这么玩) */
//            system("rm /userdata/model/* && rm vis_g3_software && rm sync");

            printf("fileVersion=%d \n",fileVersion);
            bool changeCameraList = true;
            /* 这里需要判断一下版本号 */
            if(fileVersion < 214){
                /* 如果版本号低于214，那么说明是降级到旧的两个镜头版本，这个时候需要从配置表删除多余的镜头，不然徐工的客户端会设置失败。(这里会自动重启) */
                changeCameraList = G3_Configuration::getInstance().changeCameraListToTwo();
            }else{
                /* 如果版本号不低于214，直接不用管 */
                ; // not to do
            }
            /* 如果是G3的设备软件的话,先通知外面 */
            callback->onGetUpgradeResult(curFileType, changeCameraList ? UPGRADE_RESULT_SUCCESS : UPGRADE_RESULT_FAILED, clientIp, clientPort);
            /* 重启一下自己 */
            XuShell::getInstance().killallG3sofeward("upgrade softward");


        }
            break;

        case UPGRADE_FILE_TYPE_G3MCU_SOFTWARD: {
            /* 如果是G3的MCU软件的话 只需要移动一下文件就好了 */
            XuFile::getInstance().moveFille(filepath.c_str(), UPHRADE_FILE_PATH_UPGRADE_FILE_G3MCU, 0);
            callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_SUCCESS, clientIp, clientPort);
        }
            break;

        case UPGRADE_FILE_TYPE_GPSUNIT_SOFTWARD: {
            /* 如果是G3的GPS盒子软件的话 只需要移动一下文件就好了 */
            XuFile::getInstance().moveFille(filepath.c_str(), UPHRADE_FILE_PATH_UPGRADE_FILE_GPSBOX, 0);
            callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_SUCCESS, clientIp, clientPort);
        }
            break;
        case UPGRADE_FILE_TYPE_L3_SOFTWARD: {
            /* 如果是G3的L3盒子软件的话 只需要移动一下文件就好了 */
            XuFile::getInstance().moveFille(filepath.c_str(), UPHRADE_FILE_PATH_UPGRADE_FILE_L3, 0);
            callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_SUCCESS, clientIp, clientPort);
        }
            break;
        case UPGRADE_FILE_TYPE_L4_SOFTWARD: {
            /* 如果是G3的L4盒子软件的话 只需要移动一下文件就好了 */
            XuFile::getInstance().moveFille(filepath.c_str(), UPHRADE_FILE_PATH_UPGRADE_FILE_L4, 0);
            callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_SUCCESS, clientIp, clientPort);
        }
            break;

        case UPGRADE_FILE_TYPE_ALARM_SOUND_FILE_PACK: {
            /* 如果是报警声音的更新文件，那么就直接释放到对应的目录下 */
            bool upret = upgradeAlarmSound(filepath);
            callback->onGetUpgradeResult(curFileType, upret ? UPGRADE_RESULT_SUCCESS : UPGRADE_RESULT_FAILED, clientIp, clientPort);
        }
            break;
    }


    return 0;
}

void UpgradeUnitManager::clearTempFile() {
    /* 解密失败需要删除掉临时文件 */
    std::string cmd = "rm -r ";
    cmd.append(UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD);
    system(cmd.c_str());

    std::string cmd2 = "rm -rf ";
    cmd2.append(UPHRADE_FILE_PATH_NOT_DECRYP);
    cmd2.append(" && sync");
    XuShell::getInstance().runShellWithTimeout(cmd2.c_str());
    std::string cmd3 = "rm -rf ";
    cmd3.append(" && sync");
    cmd3.append(UPHRADE_FILE_PATH_SRC);
    XuShell::getInstance().runShellWithTimeout(cmd3.c_str());

}

bool UpgradeUnitManager::upgradeAlarmSound(const std::string filepath) {
    bool ret = false;
    /* 获取下文件长度 */
    int fileLen = XuFile::getInstance().getFileLength(filepath.c_str());
    /* 有长度才需要接下去 */
    if(fileLen > 30){
        /* 读出文件内容 */
        VIS::XuMemGeter<uint8_t> fileData(fileLen);

        XuFile::getInstance().readFile(filepath.c_str(),fileData.getPtr(),fileLen);
        /* 校验一下CRC是不是一致，一致了才需要接下去 */
        uint16_t mCrc16 = CodeUtils::getInstance().generateCrc16(fileData.getPtr(),fileLen-2);
        uint16_t fCrc16 = CodeUtils::getInstance().BbToUint16(fileData.getPtr()+(fileLen-2));
        if(mCrc16 == fCrc16){
            /* 循环拿出数据 */
            int index = 0;
            std::string filPath = "/userdata/media/";
            while (index < fileLen){
                /* 先比较30个标识头,如果是标志头，就解析后面，如果不是，就移后一位 */
                if(XuString::getInstance().cmpTwoByteArray(fileData.getPtr()+index, 30, reinterpret_cast<const uint8_t *>(alarmSoundFileMaskHead.data()), 30)){
                    /* 偏移一下指针 */
                    index += 30;
                    /* 获取下声音类型，确定下存放文件的目录 */
                    int soundType = fileData.getPtr()[index];
                    index ++;
                    /* 获取下语言类型，确定下存放文件的目录 */
                    int languageType = fileData.getPtr()[index];
                    index ++;
                    switch (soundType) {
                        case ALARM_SOUND_TYPE_EFFECTS:{
                            /* 是音效，音效文件放在 */
                            filPath.clear();
                            filPath.append("/userdata/alarmSoundFile/soundEffect/");
                        }
                            break;
                        case ALARM_SOUND_TYPE_VOICE:{
                            /* 是语言，那么需要判断下语言类型 */
                            switch (languageType) {
                                case SPEAKER_SOUND_LANGUAGE_CHINESE: {
                                    /* 报中文语音 */
                                    filPath.clear();
                                    filPath.append("/userdata/alarmSoundFile/voice_zh/");
                                }
                                    break;
                                case SPEAKER_SOUND_LANGUAGE_ENGLISH: {
                                    /* 报英文语音 */
                                    filPath.clear();
                                    filPath.append("/userdata/alarmSoundFile/voice_en/");
                                }
                                    break;
                            }
                        }
                            break;

                        case ALARM_SOUND_TYPE_CINFIG:{
                            filPath.clear();
                            filPath.append(ALARM_SOUND_FILE_CONFIG_PATH);
                        }
                    }
                    /* 获取报警ID */
                    int alarmId = CodeUtils::getInstance().BbToUint16(fileData.getPtr()+index);
                    index += 2;
                    /* 如果不是配置文件，那么就需要判断一下报警类型 */
                    if(soundType != ALARM_SOUND_TYPE_CINFIG){
                        /* 根据报警ID决定文件名 */
                        /* 收到事件了 先播放声音 */
                        switch (alarmId) {
                            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:{
                                filPath.append("pdw.pcm");
                            }
                                break;
                            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1: {
                                filPath.append("pdw.pcm");
                            }
                                break;
                            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:{
                                filPath.append("pdw_2.pcm");
                            }
                                break;
                            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2: {
                                filPath.append("pdw_2.pcm");
                            }
                                break;

                            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:{
                                filPath.append("pdw.pcm");
                            }
                                break;
                            case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1: {
                                filPath.append("pdw.pcm");
                            }
                                break;
                            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:{
                                filPath.append("pdw_2.pcm");
                            }
                                break;
                            case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2: {
                                filPath.append("pdw_2.pcm");
                            }
                                break;


                            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1: {
                                filPath.append("pdw.pcm");
                            }
                                break;

                            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2: {
                                filPath.append("pdw_2.pcm");
                            }
                                break;

                            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
                                filPath.append("pdw.pcm");
                            }
                                break;

                            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
                                filPath.append("pdw_2.pcm");
                            }
                                break;


                            case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1:{
                                filPath.append("fcw.pcm");
                            }
                                break;
                            case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1:{
                                filPath.append("fcw.pcm");
                            }
                                break;
                            case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1:{
                                filPath.append("fcw.pcm");
                            }
                                break;
                            case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1:{
                                filPath.append("fcw.pcm");
                            }
                                break;
                            case EVENT_BSD_FORWARD_VEHICLE_LEVEL1:{
                                filPath.append("fcw.pcm");
                            }
                                break;
                            case EVENT_BSD_BACKWARD_VEHICLE_LEVEL1:{
                                filPath.append("fcw.pcm");
                            }
                                break;
                            case EVENT_SBST_FORWARD_FCW_LEVEL1:{
                                filPath.append("fcw.pcm");
                            }
                                break;

                            case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2:{
                                filPath.append("fcw_2.pcm");
                            }
                                break;
                            case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2:{
                                filPath.append("fcw_2.pcm");
                            }
                                break;
                            case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2:{
                                filPath.append("fcw_2.pcm");
                            }
                                break;
                            case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2:{
                                filPath.append("fcw_2.pcm");
                            }
                                break;
                            case EVENT_BSD_FORWARD_VEHICLE_LEVEL2:{
                                filPath.append("fcw_2.pcm");
                            }
                                break;
                            case EVENT_BSD_BACKWARD_VEHICLE_LEVEL2:{
                                filPath.append("fcw_2.pcm");
                            }
                                break;
                            case EVENT_SBST_FORWARD_FCW_LEVEL2:{
                                filPath.append("fcw_2.pcm");
                            }
                                break;

                            case EVENT_BSD_R151_AREA1_PEDESTRIAN:{
                                filPath.append("r151_pedestrian_zone1.pcm");
                            }
                                break;

                            case EVENT_BSD_R151_AREA2_PEDESTRIAN:{
                                filPath.append("r151_pedestrian_zone2.pcm");
                            }
                                break;

                            case EVENT_BSD_R151_AREA3_PEDESTRIAN:{
                                filPath.append("r151_pedestrian_zone3.pcm");
                            }
                                break;

                            case EVENT_BSD_R151_AREA4_PEDESTRIAN:{
                                filPath.append("r151_pedestrian_zone4.pcm");
                            }
                                break;


                            case EVENT_BSD_R151_AREA1_VEHICLE:{
                                filPath.append("r151_vehicle_zone1.pcm");
                            }
                                break;

                            case EVENT_BSD_R151_AREA2_VEHICLE:{
                                //TODO R151的车辆二级不报声音
                                filPath.append("r151_vehicle_zone2.pcm");
                            }
                                break;

                            case EVENT_BSD_R151_CAMERA_COVER:{
                                //TODO 镜头遮挡的报警需要确定报警声音用什么样的
                                filPath.append("camcoverd.pcm");
                            }
                                break;

                            case EVENT_BSD_R158_AREA1_PEDESTRIAN:{
                                filPath.append("r158_pedestrian_zone1.pcm");
                            }
                                break;
                            case EVENT_BSD_R158_AREA2_PEDESTRIAN:{
                                filPath.append("r158_pedestrian_zone2.pcm");
                            }
                                break;
                            case EVENT_BSD_R158_AREA3_PEDESTRIAN:{
                                filPath.append("r158_pedestrian_zone3.pcm");

                            }
                                break;
                            case EVENT_BSD_R158_AREA1_VEHICLE:{
                                filPath.append("r158_vehicle_zone1.pcm");
                            }
                                break;
                            case EVENT_BSD_R158_AREA2_VEHICLE:{
                                filPath.append("r158_vehicle_zone2.pcm");
                            }
                                break;
                            case EVENT_BSD_R158_CAMERA_COVER:{
                                //TODO 镜头遮挡的报警需要确定报警声音用什么样的
                                filPath.append("camcoverd.pcm");
                            }
                                break;
                            case EVENT_BSD_R159_AREA1_PEDESTRIAN:{
                                filPath.append("r159_pedestrian_zone1.pcm");
                            }
                                break;
                            case EVENT_BSD_R159_AREA2_PEDESTRIAN:{
                                filPath.append("r159_pedestrian_zone2.pcm");
                            }
                                break;

                            case EVENT_BSD_R159_AREA3_PEDESTRIAN:{
                                filPath.append("r159_pedestrian_zone3.pcm");
                            }
                                break;
                            case EVENT_BSD_R159_AREA1_VEHICLE:{
                                filPath.append("r159_vehicle_zone1.pcm");
                            }
                                break;
                            case EVENT_BSD_R159_AREA2_VEHICLE:{
                                filPath.append("r159_vehicle_zone2.pcm");
                            }
                                break;
                            case EVENT_BSD_R159_CAMERA_COVER:{
                                //TODO 镜头遮挡的报警需要确定报警声音用什么样的
                                filPath.append("camcoverd.pcm");
                            }
                                break;

                            default:{
                                /* 目前不支持修改的文件就直接丢到media里面 */
                                filPath.clear();
                                filPath.append("/userdata/media/unkonwsoundfile");
                            }
                                break;
                        }
                    }
                        /* 获取一下该文件的大小 */
                        uint32_t soundFileLen = CodeUtils::getInstance().BbToUint32(fileData.getPtr()+index);
                        index += 4;
                        /* 写到对应的文件 */
                        if(soundFileLen > 0){
                            int wret = XuFile::getInstance().writeFile(filPath.c_str(),fileData.getPtr()+index,soundFileLen);

                            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"UpgradeUnitManager") << "changge alarm sound file " << filPath.c_str() << " success！" << XU_LOG_END;
                            if(wret <= 0){
                                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"UpgradeUnitManager") << "alarm sound file " << filPath.c_str() << "writeFile data len <= 0!  wret=" << wret << XU_LOG_END;
                            }
                        }else{
                            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"UpgradeUnitManager") << "alarm sound file " << filPath.c_str() << " data len <= 0!" << XU_LOG_END;
                        }

                        index += soundFileLen;


                }else{
                    index ++;
                }
            }
            /* 无论解析得怎么样，都返回成功，因为外部的结构校验已经过了 */
            ret = true;
        }else{
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"UpgradeUnitManager") << "alarm sound upgrade file " << filepath.c_str() << " crc error" << "  mcrc=" << mCrc16 << "file's crc=" << fCrc16 << XU_LOG_END;
        }
    }else{
        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"UpgradeUnitManager") << "alarm sound upgrade file " << filepath.c_str() << " len is <= 30!" << XU_LOG_END;
    }
    return ret;
}

int UpgradeUnitManager::initByHttp(int fileType, std::string fileUrl, UpgradeCallback &upgradeCallback) {
    int ret = -1;
    if (!upgrading) {
        curFileDownloadType = UPGRADE_FILE_DOWNLAOD_TYPE_HTTP;
        curFileType = fileType;
        curFileUrl = fileUrl;
        callback = &upgradeCallback;
        ret = 0;
    }
    return ret;
}

void UpgradeUnitManager::downloadBySocket() {
    int ret = -1;
    while (ret != 0) {
        if ((listenfd = socket(AF_INET, SOCK_STREAM, 0)) == -1) {
            printf("==================create upgrade socket error: %s(errno: %d)==========================\n",
                   strerror(errno), errno);
            ret = -1;
        } else {
            memset(&servaddr, 0, sizeof(servaddr));
            servaddr.sin_family = AF_INET;
            servaddr.sin_addr.s_addr = htonl(INADDR_ANY);
            servaddr.sin_port = htons(curTcpPort);
            /* 设置一下端口复用 */
            int opt = 1;
            setsockopt(listenfd, SOL_SOCKET, SO_REUSEADDR, (const void *) &opt, sizeof(opt));
            /* 绑定端口 */
            if (bind(listenfd, (struct sockaddr *) &servaddr, sizeof(servaddr)) == -1) {
                printf("=========================bind upgrade socket error: %s(errno: %d)========================\n",
                       strerror(errno), errno);
                ret = -1;
            } else {
                if (listen(listenfd, 10) == -1) {
                    printf("=========================listen upgrade socket error: %s(errno: %d)=========================\n",
                           strerror(errno), errno);
                    ret = -1;
                } else {
                    ret = 0;
                    tcpOpen = true;
                }
            }
        }
        sleep(1);
    }

    fd_set readfds;//保存读文件描述符集合

    while (listenfd != -1) {
        printf(" while (listenfd != -1)\n");

        //把readfds清空
        FD_ZERO(&readfds);
        //把要监听的sockfd添加到readfds中
        FD_SET(listenfd, &readfds);
        timeval tv;
        tv.tv_sec = 60;
        tv.tv_usec = 0;
        //用select类监听sockfd 阻塞状态
        int ret = select(listenfd + 1, &readfds, nullptr, nullptr, &tv);
        int connfd = -1;
        if (ret > 0 && FD_ISSET(listenfd, &readfds)) {
            if ((connfd = accept(listenfd, (struct sockaddr *) nullptr, 0)) == -1) {
                printf("accept socket error: %s(errno: %d)   \n", strerror(errno), errno);
                usleep(1000 * 1000);
                continue;
            }
            struct sockaddr_in sa;
            int len;
            len = sizeof(sa);
            if (!getpeername(connfd, (struct sockaddr *) &sa, reinterpret_cast<socklen_t *>(&len))) {
//            char *clientIp = inet_ntoa(sa.sin_addr);
//            int clientPort = ntohs(sa.sin_port);
                printf("upgrade client login. ip: %s, port :%d \n", clientIp, clientPort);
                readData(connfd, clientIp, strlen(clientIp), clientPort);
                break;


            } else {
                close(connfd);
                close(listenfd);
                upgrading = false;
                callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
                printf("upgrade client get ip failed! \n");
                break;
            }
        } else {
            //超时了  直接return掉把
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "accept timeout!  wait time >= 60 " << XU_LOG_END;
            callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
            close(listenfd);
            listenfd = -1;
            return;
        }


    }
}

void UpgradeUnitManager::downloadByHttp() {
    int ret = XuHttpUtil::getInstance().downlaodFile(curFileUrl,UPHRADE_FILE_PATH_SRC);
    if(ret > 0){
        /* 如果是MRV220的升级软件，那么需要解密，如果不是那么都不需要解密 */
        if(curFileType == UPGRADE_FILE_TYPE_G3DEVICE_SOFTWARD){
            /* 取出文件头跟未解密的文件 */
            recvBuf = static_cast<uint8_t *>(malloc(RECV_BUF_MAX_SIZE));
            int writeRet = getNotDecrypFile(UPHRADE_FILE_PATH_SRC, UPHRADE_FILE_PATH_NOT_DECRYP);
            /* 看看是不是写成功了 */
            if (writeRet > 0) {
                /* 写成功了 校验一下  如果成功了则解密完写入文件 */
                readedLen = ret;
                curFileLen = ret;

                getMD5OfFile(UPHRADE_FILE_PATH_SRC, fileMD5);
                if (checkFileData() == 0) {
                    /* 校验成功了，那么删除下上次升级可能存在文件 */
                    XuFile::getInstance().deleteFile(UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD);
                    /* 校验成功了则进行解密 */
                    if (XuFile::getInstance().doDecryptForUpgrade(UPHRADE_FILE_PATH_NOT_DECRYP,
                                                                  UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD,
                                                                  fileHead) == 0) {
                        /* 解密成功了，那么需要校验一下MD5值是否正确了 */
                        uint8_t mFileMD5[16] = {0x00};
                        getMD5OfFile(UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD, mFileMD5);
                                    printf("fileMD5=%s   mFileMD5=%s   \n",
                                           XuString::getInstance().byteArrayToString(fileHead + 5, 16).c_str(),
                                           XuString::getInstance().byteArrayToString(mFileMD5, 16).c_str());
                        if (XuString::getInstance().cmpTwoByteArray(mFileMD5, sizeof(mFileMD5),
                                                                    fileHead + 5, 16)) {
                            /* 校验成功了，把升级文件移动到正式路径下 */
                            std::string cpcmd = "cp ";
                            cpcmd.append(UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD);
                            cpcmd.append(" ");
                            cpcmd.append(UPGRADE_FILE_PATH_G3DEVICESSOFTWARD);
                            cpcmd.append(" && sync");
                            system(cpcmd.c_str());
                            /* 移动完成，该干什么就干什么了 */
                            upgrading = false;
                            /* 获取更新文件的版本号 */
                            int fileVersion = CodeUtils::getInstance().BbToUint32(fileHead+22);
                            /* 执行对应的升级操作 */
                            doUpgrade(UPGRADE_FILE_PATH_G3DEVICESSOFTWARD, curFileType,fileVersion);
                            /* 清理掉那些临时文件 */
                            clearTempFile();
                        } else {
                            upgrading = false;
                            callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp,
                                                         clientPort);
                            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "after decryp file, check md5 failed!  fileMD5="<< XuString::getInstance().byteArrayToString(fileHead + 5, 16).c_str() << "   mFileMD5=" << XuString::getInstance().byteArrayToString(mFileMD5, 16).c_str() << XU_LOG_END;
                            /* 校验失败需要删除掉临时文件 */
                            std::string cmd = "rm -rf ";
                            cmd.append(UPHRADE_FILE_PATH_UPGRADE_FILE_TYPE_G3DEVICESSOFTWARD);
                            cmd.append(" && sync");
                            system(cmd.c_str());
                            /* 清理掉那些临时文件 */
                            clearTempFile();
                        }
                    } else {
                        upgrading = false;
                        callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp,
                                                     clientPort);
                        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "decryp file failed! " << XU_LOG_END;
                        /* 清理掉那些临时文件 */
                        clearTempFile();
                    }
                } else {
                    upgrading = false;
                    callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
                    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "checkFileData() != 0 " << XU_LOG_END;
                    /* 清理掉那些临时文件 */
                    clearTempFile();
                }

            } else {
                upgrading = false;
                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << "write not head file failed! " << XU_LOG_END;
                callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
                /* 清理掉那些临时文件 */
                clearTempFile();
            }
        }else{
            /* 不是MRV220的升级软件，那么就直接拿去用吧 */
            upgrading = false;
            /* 执行对应的升级操作 */
            doUpgrade(UPHRADE_FILE_PATH_SRC, curFileType);
        }


    }else{
        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, TAG) << curFileUrl << "  download failed!" << XU_LOG_END;
        upgrading = false;
        callback->onGetUpgradeResult(curFileType, UPGRADE_RESULT_FAILED, clientIp, clientPort);
    }

}

