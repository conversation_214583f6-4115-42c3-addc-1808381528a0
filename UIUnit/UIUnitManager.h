//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/22.
//

#ifndef VIS_G3_SOFTWARE_UIUNITMANAGER_H
#define VIS_G3_SOFTWARE_UIUNITMANAGER_H

#include <Poco/ThreadPool.h>
#include <cstring>
#include "tdShareDefine.h"
#include "DispalayShower.h"

class UIUnitManager {
public:
    UIUnitManager();

    ~UIUnitManager();

    void start();

    /**
     * 设置YUV数据到缓冲区
     *
     * @param cameraYuvData ： YUV数据封装好的对象
     *
     * */
    void setYUVDataToDispaly(CameraYUVData &cameraYuvData);

    /**
     * 设置从识别模块拿到的识别信息(BSD)
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

    /**
     * 设置从识别模块拿到的识别信息(Adas)
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);


    /**
     * 设置从识别模块拿到的报警事件
     *
     * @param cameraId : 相机ID
     * @param detectType : 识别算法类型
     * @param eventCode ： 事件码
     *
     * */
    void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

    /**
     * 获取当前的运行状态
     *
     * @return 0：一切正常   其他：有故障
     * */
    int getWorkStaus();

    /**
    * 设置车速
    *
    * @param speed : 速度  （单位：KM\H）
    * @param baseOf ： 来源    （0：CAN  1：UART  2：GPS  3:以太网口）
    *
    * @return 无
    * */
    void setSpeed(const float speed, const int baseOf);

    /**
     * 设置转向信号
     *
     * @param trunL ： 是否左转
     * @param trunR ： 是否右转
     */
    void setTurnSignal(const bool trunL,const bool trunR);

    /**
     * 设置倒车信号
     *
     * @param reverse ： 倒车信号
     */
    void setReverseSignal(const bool reverse);

    /**
     * 设置从识别模块拿到的识别信息(镜头状态)
     *
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectInfo_CameraStatus(G3DetectionResult &detectionResult);

private:
    Poco::ThreadPool threadPool;
    DispalayShower dispalayShower;


};


#endif //VIS_G3_SOFTWARE_UIUNITMANAGER_H
