//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/22.
//


#include "UIUnitManager.h"


UIUnitManager::UIUnitManager() : threadPool(1, 1) {

}

UIUnitManager::~UIUnitManager() {

}

void UIUnitManager::start() {
    dispalayShower.init();
    /* 如果是作为G4的一部分工作，就不需要启动 */
    if (G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_G4_UNIT &&
        G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_G4MT_UNIT &&
        G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_G4GJ_G4_UNIT &&
        threadPool.available() > 0) {
        threadPool.start(dispalayShower);
    }
}

void UIUnitManager::setYUVDataToDispaly(CameraYUVData &cameraYuvData) {
    dispalayShower.setYUVDataToDispaly(cameraYuvData);
}


int UIUnitManager::getWorkStaus() {
    /* 先检查CAN */
    int ret = true;
    /* 看看是不是能输出到USB上去进行显示 */
    ret &= dispalayShower.isCanShow();
    return (ret ? 0 : -1);
}

void UIUnitManager::setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
    dispalayShower.setDetectionInfo_BSD(objectInfo, detectionResult);
}

void UIUnitManager::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
    dispalayShower.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
}

void UIUnitManager::setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
    dispalayShower.setDetectionInfo_Adas(objectInfo, detectionResult);
}

void UIUnitManager::setSpeed(const float speed, const int baseOf) {
    dispalayShower.setSpeed(speed,baseOf);

}

void UIUnitManager::setTurnSignal(const bool trunL, const bool trunR) {
    dispalayShower.setTurnSignal(trunL,trunR);
}

void UIUnitManager::setReverseSignal(const bool reverse) {
    dispalayShower.setReverseSignal(reverse);
}

void UIUnitManager::setDetectInfo_CameraStatus(G3DetectionResult &detectionResult) {

}
