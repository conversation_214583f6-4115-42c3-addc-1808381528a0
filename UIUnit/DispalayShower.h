//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/22.
//

#ifndef VIS_G3_SOFTWARE_DISPALAYSHOWER_H
#define VIS_G3_SOFTWARE_DISPALAYSHOWER_H

#include <Poco/Runnable.h>
#include "tdShareDefine.h"
#include "vo/Usb2CbsVo.h"
#include "XuRGAUtils.h"
#include "CameraYUVData.h"
#include "XuYUVDataOpt.h"
#include <mutex>
#include "XuString.h"
#include "G3_Configuration.h"
#include "G3DetectionDefind.h"
#include "dasShareDefine.h"
#include "CameraImagRunnable.h"


class DispalayShower : public Poco::Runnable {
public:


    void init();

    void run() override;



    /**
     * 设置YUV数据到缓冲区
     *
     * @param cameraYuvData ： YUV数据封装好的对象
     *
     * */
    void setYUVDataToDispaly(CameraYUVData &cameraYuvData);

    /**
     * 从缓冲区获取一帧YUV数据
     *
     * @param buf ： 用来存放YUV数据的指针
     * @param cameraId : 想要获取的相机的ID
     * @param width : 图像的宽
     * @param height : 图像的高
     *
     * @return 结果     0：成功  其他：失败
     * */
    int getYUVData(uint8_t *buf, const int cameraId, int &width, int &height);


    /**
     * 设置从识别模块拿到的识别信息
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

    /**
     * 设置从识别模块拿到的识别信息(Adas)
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);


    /**
     * 设置从识别模块拿到的报警事件
     *
     * @param cameraId : 相机ID
     * @param detectType : 识别算法类型
     * @param eventCode ： 事件码
     *
     * */
    void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

    /**
     * 不显示镜头图像，就显示一张画面
     *
     * @param reason : 不显示镜头画面的原因
     *
     * @return
     */
    void notShowCamera(int reason);

    bool isCanShow() const;



    /**
    * 设置车速
    *
    * @param speed : 速度  （单位：KM\H）
    * @param baseOf ： 来源    （0：CAN  1：UART  2：GPS  3:以太网口）
    *
    * @return 无
    * */
    void setSpeed(const float speed, const int baseOf);


    /**
     * 设置转向信号
     *
     * @param trunL ： 是否左转
     * @param trunR ： 是否右转
     */
    void setTurnSignal(const bool turnL,const bool turnR);

    /**
     * 设置倒车信号
     *
     * @param reverse ： 倒车信号
     */
    void setReverseSignal(const bool reverse);

    /**
     * 显示R158的的屏幕输出内容
     * */
    void r158Display();

    /**
     * 显示R158认证需要的屏幕画面
     *
     * @param cameraId ： 对应的镜头ID
     * @param needDrawZone : 是否需要话报警区域
     *
     */
    void showR158(const int cameraId, const bool needDrawZone = true);

    /**
     * 把VISPoint的vector转成YUVDataOpt_Point数组
     *
     * @param visPointList ： VISPoint的vector
     * @param yuvDataOpt_PointList ： YUVDataOpt_Point数组
     * @param len ： YUVDataOpt_Point数组长度
     * @param needMirror : 是否需要镜像
     *
     * @return 0：成功  其他：失败
     */
    int visPointListToYUVDataOpt_PointList(const std::vector<VISPoint> &visPointList,YUVDataOpt_Point *yuvDataOpt_PointList,const int len,const bool needMirror);

    /**
     * 获取一些icon和屏幕显示用的图像的数据
     *
     * @param resolutionRatio : 分辨率的字符串（由于有些图片又不同分辨率的文件，所以这里需要传入分辨率用以组装完整的文件名）
     */
    void getIconAndDisplayImgData(const std::string &resolutionRatio);

private:

    /* 根据报警事件来显示画面的依据信息 */
    struct AlarmEventInfoFomrDispaly{
        /* 产生报警的ID */
        CAMERAIDLIST alarmCameraId = CAMERA_ID_UNKNOW;
        /* 产生报警的时间 */
        uint64_t alarmTime = 0;
    };

     /* 屏幕输出的分辨率 */
     int DISPLAY_W = 1280;
     int DISPLAY_H = 720;



     /* R158跟R159的行人报警ICON的分辨率 */
    static const int R158ICON_PEDESTRIAN_W = 176;
    static const int R158ICON_PEDESTRIAN_H = 250;
    /* R158跟R159的车辆报警ICON的分辨率 */
    static const int R158ICON_VEHICLE_W = 176;
    static const int R158ICON_VEHICLE_H = 250;
    /* R158跟R159的镜头朝向ICON的分辨率 */
    static const int R158ICON_ORIENTATION_W = 81;
    static const int R158ICON_ORIENTATION_H = 38;
    /* R151的镜头朝向ICON的分辨率 */
    static const int R151ICON_ORIENTATION_W = 102;
    static const int R151ICON_ORIENTATION_H = 38;


    /* 不显示画面的原因---显示logo */
    static const int NOT_SHOW_CAMERA_FOR_LOGO = 0;
    /* 不显示画面的原因---前向超速 */
    static const int NOT_SHOW_CAMERA_FOR_OVER_SPEED_FRONT = 1;
    /* 不显示画面的原因---前向镜头遮挡 */
    static const int NOT_SHOW_CAMERA_FOR_CAMERA_COVERED_FRONT = 2;
    /* 不显示画面的原因---后向超速 */
    static const int NOT_SHOW_CAMERA_FOR_OVER_SPEED_REAR = 3;
    /* 不显示画面的原因---后向镜头遮挡 */
    static const int NOT_SHOW_CAMERA_FOR_CAMERA_COVERED_REAR = 4;
    /* 不显示画面的原因---侧边向后镜头超速 */
    static const int NOT_SHOW_CAMERA_FOR_OVER_SPEED_SIDEBACKWARD = 5;
    /* 不显示画面的原因---侧边向后镜头遮挡 */
    static const int NOT_SHOW_CAMERA_FOR_CAMERA_COVERED_SIDEBACKWARD = 6;
    /* 启用根据报警信息切换显示模式时，每个报警信息持续显示的时间 单位ms */
    static const int MAX_ALARM_CAMERA_SHOW_TIME_FOR_DISPLAY = 2000;



    /* R151、R158、R159叠加ICON的时候是否需要缩放和偏移 */
    bool r158IconNeedNeedScale = true;

    /* 是否需要停止显示的线程 */
    bool isNeedStopDispalayShower = true;
    /* 相机图像 */
    CameraYUVData *yuvCache_camera1 = nullptr;
    CameraYUVData *yuvCache_camera2 = nullptr;
    CameraYUVData *yuvCache_camera3 = nullptr;
    CameraYUVData *yuvCache_camera4 = nullptr;
    /* 复制出来用来显示的相机YUV画面 */
    uint8_t *showYUVData_camera1;
    uint8_t *showYUVData_camera2;
    uint8_t *showYUVData_camera3;
    uint8_t *showYUVData_camera4;
    uint8_t *showFourPalaceGridYUVData = nullptr;



    /* RGA的工具类 */
    XuRGAUtils rgaUtils;
    /* 在相机的YUV数据上绘画的工具类 */
    XuYUVDataOpt yuvDataOpt;
    /* 存放时间字符串的内存地址 */
    char timeStr[25];

    /* 用来作为显示数据的内存指针 */
    uint8_t *leftImag;
    uint8_t *rightImag;
    uint8_t *showData;


    /* 操作识别出来的互斥锁 */
    std::mutex objectInfolock;

    /* 镜头的图像识别出来的BSD识别信息 */
    std::vector<CameraImagRunnable::BSDDetectionInfoAll> curDetectionResult_list;




    /* 可以显示 */
    bool canShow = false;

    /* logo的YUV数据 */
    uint8_t *loggYUVdata;
    int loggYUVdataLen = 0;
    /* 前向镜头超速的YUV数据 */
    uint8_t *overspeedFrontYUVdata;
    int overspeedFrontYUVdataLen = 0;




    /* 后向镜头超速的YUV数据 */
    uint8_t *overspeedRearYUVdata;
    int overspeedRearYUVdataLen = 0;

    /* 侧边向后镜头超速的YUV数据 */
    uint8_t *overspeedSidebackwardYUVdata;
    int overspeedSidebackwardYUVdataLen = 0;






    /* R159的前向标志ICON的RGBA数据 */
    uint8_t *r159_frontRGBAdata;
    int r159_frontRGBAdataLen = 0;
    /* R158的后向标志ICON的RGBA数据 */
    uint8_t *r158_rearRGBAdata;
    int r158_rearRGBAdataLen = 0;
    /* R151的侧边向后标志ICON的RGBA数据 */
    uint8_t *r151_sidebackwardRGBAdata;
    int r151_sidebackwardRGBAdataLen = 0;

    /* R158的绿色人ICON的RGBA数据 */
    uint8_t *r158_pedestrian_green_RGBAdata;
    int r158_pedestrian_green_RGBAdataLen = 0;

    /* R158的黄色人ICON的RGBA数据 */
    uint8_t *r158_pedestrian_yellow_RGBAdata;
    int r158_pedestrian_yellow_RGBAdataLen = 0;

    /* R158的红色人ICON的RGBA数据 */
    uint8_t *r158_pedestrian_red_RGBAdata;
    int r158_pedestrian_red_RGBAdataLen = 0;



    /* R158的绿色车ICON的RGBA数据 */
    uint8_t *r158_vehicle_green_RGBAdata;
    int r158_vehicle_green_RGBAdataLen = 0;

    /* R158的黄色车ICON的RGBA数据 */
    uint8_t *r158_vehicle_yellow_RGBAdata;
    int r158_vehicle_yellow_RGBAdataLen = 0;

    /* R158的红色车ICON的RGBA数据 */
    uint8_t *r158_vehicle_red_RGBAdata;
    int r158_vehicle_red_RGBAdataLen = 0;





    /* 当前的车速 */
    float curSpeed;


    /* 当前是否左转 */
    bool curTurnL = false;
    /* 当前是否右转 */
    bool curTurnR = false;
    /* 当前是否有倒车信号 */
    bool curReverse = false;



    /* R151的四个行人报警区域 */
    YUVDataOpt_Point r151PedestrianAlarmArea1[6];
    YUVDataOpt_Point r151PedestrianAlarmArea2[6];
    YUVDataOpt_Point r151PedestrianAlarmArea3[6];
    YUVDataOpt_Point r151PedestrianAlarmArea4[6];
    /* r151的两个车辆报警区域 */
    YUVDataOpt_Point r151VehicleAlarmArea1[6];
    YUVDataOpt_Point r151VehicleAlarmArea2[6];


    /* R158的三个行人报警区域 */
    YUVDataOpt_Point r158PedestrianAlarmArea1[6];
    YUVDataOpt_Point r158PedestrianAlarmArea2[6];
    YUVDataOpt_Point r158PedestrianAlarmArea3[6];
    /* R158的两个车辆报警区域 */
    YUVDataOpt_Point r158VehicleAlarmArea1[6];
    YUVDataOpt_Point r158VehicleAlarmArea2[6];


    /* R159的三个行人报警区域 */
    YUVDataOpt_Point r159PedestrianAlarmArea1[6];
    YUVDataOpt_Point r159PedestrianAlarmArea2[6];
    YUVDataOpt_Point r159PedestrianAlarmArea3[6];
    /* R159的两个车辆报警区域 */
    YUVDataOpt_Point r159VehicleAlarmArea1[6];
    YUVDataOpt_Point r159VehicleAlarmArea2[6];

    /* USB输出模块的SDK实体类 */
    Usb2CbsVo vo;

    /* 用opencv画框所用到的缓存 */
    cv::Mat rgbaImg;
    cv::Mat yuvImg;


    const int MAX_ALARAM_CANCEL_DELAY_TIME = 1500;

    /* R158最后一次行人报警状态的时间 */
    uint64_t lastR158PedestrianAlarmTime = 0;
    /* R158最后一次行人报警状态 */
    R158_BSDDetectionInfo lastR158PedestrianAlarm;
    /* R158最后一次行人报警状态的时间 */
    uint64_t lastR158VehicleAlarmTime = 0;
    /* R158最后一次车辆报警状态 */
    R158_BSDDetectionInfo lastR158VehicleAlarm;

    /* R159最后一次行人报警状态的时间 */
    uint64_t lastR159PedestrianAlarmTime = 0;
    /* R159最后一次行人报警状态 */
    R159_BSDDetectionInfo lastR159PedestrianAlarm;
    /* R159最后一次行人报警状态的时间 */
    uint64_t lastR159VehicleAlarmTime = 0;
    /* R159最后一次车辆报警状态 */
    R159_BSDDetectionInfo lastR159VehicleAlarm;
    /* 是否向USB输出过图像 */
    bool isUSBOutput = false;
    /* 用来给显示用的报警事件信息 */
    AlarmEventInfoFomrDispaly lastAlarmEventInfoFomrDispaly;






    /**
     * 在YUV上画BSD的识别信息
     *
     * @param yuvdata : YUV数据
     * @param len : YUV数据的长度
     * @param imagWidth ：图像的宽
     * @param imagHeight ：图像的高
     * @param curobjectInfo ：识别信息
     * @param needMirror ：是否需要镜像
     */
    void drawBSDInfo(uint8_t *yuvdata, const int len, const int imagWidth, const int imagHeight,td::objectInfo_t &curobjectInfo,const bool needMirror);

    /**
     * 在MAT(RGBA)上画BSD的识别信息
     *
     * @param srcimg : 源图像
     * @param imagWidth ：图像的宽
     * @param imagHeight ：图像的高
     * @param curobjectInfo ：识别信息
     * @param needMirror ：是否需要镜像
     */
    void drawBSDInfoByMat(cv::Mat srcimg,const int imagWidth, const int imagHeight,td::objectInfo_t &curobjectInfo,const bool needMirror);

    /**
     * 把YUV（NV21）数据输出到USB显示
     *
     * @param yuvbuf : YUV数据
     * @param width ： 图像的宽
     * @param height ： 图像的高
     */
    void transferNV21YToUSB(uint8_t *const yuvbuf, const int width, const int height);

    /**
     * 把RGBA数据输出到USB显示
     *
     * @param rgbbuf : RGBA数据
     * @param width ： 图像的宽
     * @param height ： 图像的高
     */
    void transferRGBAToUSB(uint8_t *const rgbabuf, const int width, const int height);

    /**
     * 在RGBA上画报警区域
     *
     * @param srcimg ： RGA图像
     * @param alarmAreaPoints ： 报警区域的点
     * @param color ： 区域线的颜色
     */
    void drawAlarmAreaByRGBA(cv::Mat srcimg, YUVDataOpt_Point *alarmAreaPoints, cv::Scalar color, bool closed = true);

    /**
     * 分屏同时显示两个镜头的画面
     */
    void showTwoCamera();

    /**
     * 全屏显示某个镜头的画面
     *
     * @param cameraId ： 需要全屏显示的镜头的ID
     */
    void showOneCamera(int cameraId);

    /**
     * 显示R151的的屏幕输出内容
     * */
    void r151Display();

    /**
     * 显示R151认证需要的屏幕画面
     *
     * @param cameraId ： 对应的镜头ID
     *
     */
    void showR151(const int cameraId);


    /**
     * 显示R159的的屏幕输出内容
     * */
    void r159Display();

    /**
     * 显示R159认证需要的屏幕画面
     *
     * @param cameraId ： 对应的镜头ID
     *
     */
    void showR159(const int cameraId);

    /**
     * 同时运行R158和R159的镜头时屏幕显示的内容
     * */
    void r158Andr159Display();

    /**
     * 根据车辆信号切换屏幕显示画面时屏幕显示的内容
     */
    void switchForSignalDisplay();

    /**
     * 根据报警信号切换屏幕显示画面时屏幕显示的内容
     */
    void switchForAlarmEventDisplay();

    /**
     * 分屏同时显示四个镜头的画面
     */
    void showFourCamera();

};


#endif //VIS_G3_SOFTWARE_DISPALAYSHOWER_H
