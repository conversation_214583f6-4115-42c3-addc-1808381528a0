//
// Created by z<PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/22.
//

#include <cstring>
#include <XuTimeUtil.h>
#include <cstdio>
#include "YUVChanger.h"

int YUVChanger::init(const int inputImagType, const int inputWidth, const int inputHeight, const int outputImagType,
                     const int outWidth, const int outHeight, const int RGAChn) {
    int ret = -1;
    RK_MPI_SYS_Init();
    curRGAChn = RGAChn;
    RGA_ATTR_S stRgaAttr;
    memset(&stRgaAttr, 0, sizeof(stRgaAttr));
    stRgaAttr.bEnBufPool = RK_TRUE;
    stRgaAttr.u16BufPoolCnt = 2;
    stRgaAttr.u16Rotaion = 0;
    stRgaAttr.stImgIn.u32X = 0;
    stRgaAttr.stImgIn.u32Y = 0;
    stRgaAttr.stImgIn.imgType = (IMAGE_TYPE_E) inputImagType;
    stRgaAttr.stImgIn.u32Width = inputWidth;
    stRgaAttr.stImgIn.u32Height = inputHeight;
    stRgaAttr.stImgIn.u32HorStride = inputWidth;
    stRgaAttr.stImgIn.u32VirStride = inputHeight;
    stRgaAttr.stImgOut.u32X = 0;
    stRgaAttr.stImgOut.u32Y = 0;
    stRgaAttr.stImgOut.imgType = (IMAGE_TYPE_E) outputImagType;
    stRgaAttr.stImgOut.u32Width = outWidth;
    stRgaAttr.stImgOut.u32Height = outHeight;
    stRgaAttr.stImgOut.u32HorStride = outWidth;
    stRgaAttr.stImgOut.u32VirStride = outHeight;
    ret = RK_MPI_RGA_CreateChn(curRGAChn, &stRgaAttr);

    if (ret == 0) {
        curStImageInfo = {stRgaAttr.stImgIn.u32Width, stRgaAttr.stImgIn.u32Height, stRgaAttr.stImgIn.u32Width,
                          stRgaAttr.stImgIn.u32Height, stRgaAttr.stImgIn.imgType};
        isNeedStopYUVChanger = false;

    } else {
    }
    return ret;
}

int YUVChanger::setInputYUVData(const uint8_t *buf, const int len) {
    int ret = -1;
    MEDIA_BUFFER mb = RK_MPI_MB_CreateImageBuffer(&curStImageInfo, RK_FALSE, MB_FLAG_NOCACHED);
    memcpy(RK_MPI_MB_GetPtr(mb), buf, len);
    RK_MPI_MB_SetSize(mb, len);
    ret = RK_MPI_SYS_SendMediaBuffer(RK_ID_RGA, curRGAChn, mb);
    RK_MPI_MB_ReleaseBuffer(mb);
    return ret;
}

int YUVChanger::getOutputData(uint8_t *buf) {
    int ret = -1;
    MEDIA_BUFFER mb = nullptr;
    mb = RK_MPI_SYS_GetMediaBuffer(RK_ID_RGA, curRGAChn, 10);
    if (!mb) {
//        printf("RGA  RK_MPI_SYS_GetMediaBuffer get nullptr buffer!\n");
        ret = -1;
    } else {
//        printf("========================================Get packet:ptr:%p, fd:%d, size:%zu, mode:%d, channel:%d, ""timestamp:%lld\n",RK_MPI_MB_GetPtr(mb), RK_MPI_MB_GetFD(mb), RK_MPI_MB_GetSize(mb),RK_MPI_MB_GetModeID(mb), RK_MPI_MB_GetChannelID(mb),RK_MPI_MB_GetTimestamp(mb));
        memcpy(buf, RK_MPI_MB_GetPtr(mb), RK_MPI_MB_GetSize(mb));
        ret = RK_MPI_MB_GetSize(mb);
        /* 只要拿到的不为空  就都要释放 */
        RK_MPI_MB_ReleaseBuffer(mb);
    }
    return ret;
}


