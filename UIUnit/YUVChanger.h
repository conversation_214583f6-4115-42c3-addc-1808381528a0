//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/22.
//

#ifndef VIS_G3_SOFTWARE_YUVCHANGER_H
#define VIS_G3_SOFTWARE_YUVCHANGER_H


#include "rkmedia_api.h"

class YUVChanger {
public:
    const static int YUV_TYPE_NV12 = IMAGE_TYPE_NV12;
    const static int YUV_TYPE_UYVY422 = IMAGE_TYPE_UYVY422;
    const static int YUV_TYPE_RGB888 = IMAGE_TYPE_RGB888;
    const static int YUV_TYPE_BGR888 = IMAGE_TYPE_BGR888;


    int init(const int inputImagType, const int inputWidth, const int inputHeight, const int outputImagType,
             const int outWidth, const int outHeight, const int RGAChn);

    int setInputYUVData(const uint8_t *buf, const int len);

    int getOutputData(uint8_t *buf);

    int stop();


private:
    uint8_t *yuvDataCache;
    /* 当前使用的RGA通道号 */
    int curRGAChn = 0;
    /* 喂给RGA的数据的信息 */
    MB_IMAGE_INFO_S curStImageInfo;
    /*  */
    bool isNeedStopYUVChanger = true;


};


#endif //VIS_G3_SOFTWARE_YUVCHANGER_H
