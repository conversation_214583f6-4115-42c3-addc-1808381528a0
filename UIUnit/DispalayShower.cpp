//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/22.
//

#include <unistd.h>
#include <opencv2/imgproc.hpp>
#include "DispalayShower.h"
#include "rga/RockchipRga.h"
#include "XuShell.h"
#include "XuLog.h"
#include "XuFile.h"



/* 是否完成了V6屏幕输出的初始化 */
bool isV6DisplayInited = false;
/**
 * 如果是V6，那么在进行过一次USB输出之后，需要拉一下reset管脚并写I2C才能正常输出
 */
void *initV6Display(void *args) {
    isV6DisplayInited = true;
    /* 先给运行权限 */
    XuShell::getInstance().runShellWithTimeout("chmod 777 /userdata/v6display.sh");
    /* 再调用设置 */
    std::string result = XuShell::getInstance().runShellWithTimeout("bash /userdata/v6display.sh");
    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,"initV6Display") << "v6display result=" << result << XU_LOG_END;
    /* 判断一下是否初始化成功，不成功就重复再写 */
    if(strstr(result.c_str(), "initV6Display finish!") == nullptr){
        isV6DisplayInited = false;
    }
    /* 没东西返回，直接返回空指针 */
    return nullptr;
}



void DispalayShower::init() {
    /* 用于读取某些有多分辨率的图片的分辨率字符串 */
    std::string resolutionRatio = "";
    /* 根据配置表的信号输出方式选择分辨率 */
    switch (G3_Configuration::getInstance().getG3DisplayOutputSignalType()) {
        case DISPLAY_SIGNAL_TYPE_CVBS_PAL:{
            DISPLAY_W = 720;
            DISPLAY_H = 576;
            resolutionRatio.append("720x576");
            r158IconNeedNeedScale = true;
        }
            break;
        case DISPLAY_SIGNAL_TYPE_AHD:{
            DISPLAY_W = 1280;
            DISPLAY_H = 720;
            resolutionRatio.append("1280x720");
            r158IconNeedNeedScale = false;
        }
            break;
        case DISPLAY_SIGNAL_TYPE_CVBS_NTSC:{
            DISPLAY_W = 720;
            DISPLAY_H = 480;
            resolutionRatio.append("720x480");
            r158IconNeedNeedScale = true;
        }

        default:{
            DISPLAY_W = 720;
            DISPLAY_H = 576;
            resolutionRatio.append("720x576");
            r158IconNeedNeedScale = true;
        }
            break;
    }
    /* 开辟后面需要使用的内存空间 */
    leftImag = static_cast<uint8_t *>(malloc(DISPLAY_W * DISPLAY_H * 4));
    rightImag = static_cast<uint8_t *>(malloc(DISPLAY_W * DISPLAY_H * 4));
    showData = static_cast<uint8_t *>(malloc(DISPLAY_W * DISPLAY_H * 2));
    showYUVData_camera1 = static_cast<uint8_t *>(malloc(1280*720*3/2));
    showYUVData_camera2 = static_cast<uint8_t *>(malloc(1280*720*3/2));
    showYUVData_camera3 = static_cast<uint8_t *>(malloc(1280*720*3/2));
    showYUVData_camera4 = static_cast<uint8_t *>(malloc(1280*720*3/2));
    yuvImg = cv::Mat(720 * 3 / 2, 1280, CV_8UC1, (unsigned char *) showYUVData_camera1);

    /* 获取一下一些屏幕显示用的ICON和图片  主要是R158和R159认证要求的功能 */
    getIconAndDisplayImgData(resolutionRatio);



    isNeedStopDispalayShower = false;

}

void DispalayShower::run() {




    /* 设置一下线程名 */
    std::string pthreadName = "DispalayShower";
    pthread_setname_np(pthread_self(), pthreadName.c_str());
    /* 初始化一下VO，这里可以不用判断返回值，因为循环里面输出失败会尝试重新初始化的 */
    vo.voInit(DISPLAY_W,DISPLAY_H,G3_Configuration::getInstance().getG3DisplayOutputSignalType());

    /* 计算每输出一帧的间隔时间 */
    int showCmaeraYUVInterval = 1000 / G3_Configuration::getInstance().getCameraFrameRate();
    /* 用以显示的循环 */
    while (!isNeedStopDispalayShower) {

        /* 如果是V6需要特殊配置一下 */
        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_V6 && isUSBOutput && !isV6DisplayInited){
            pthread_t threads;
            pthread_create(&threads,NULL, initV6Display,NULL );
        }

        /* 先默认使用配置表的显示模式 */
        int displayMode = G3_Configuration::getInstance().getG3DisplayMode();
        /* 根据配置选择显示方式 */
        switch (displayMode) {
            case G3_DISPALY_MODE_ALL_SLICING_2: {
                /* 单纯分屏显示两个镜头的画面 */
                showTwoCamera();
            }
                break;
            case G3_DISPALY_MODE_CAMERA1_FULL: {
                /* 单纯全屏显示镜头1的画面 */
                showOneCamera(CAMERA_ID_1);
            }
                break;
            case G3_DISPALY_MODE_CAMERA2_FULL: {
                /* 单纯全屏显示镜头2的画面 */
                showOneCamera(CAMERA_ID_2);
            }
                break;
            case G3_DISPALY_MODE_NOT_SHOW: {
                /* 不显示镜头画面，就显示一片黑色 */
                notShowCamera(NOT_SHOW_CAMERA_FOR_LOGO);
            }
                break;

            case G3_DISPALY_MODE_R151: {
                /* 单独显示R151的显示内容 */
                r151Display();
            }
                break;
            case G3_DISPALY_MODE_R158: {
                /* 单独显示R158的显示内容 */
                r158Display();
            }
                break;
            case G3_DISPALY_MODE_R159: {
                /* 单独显示R159的显示内容 */
                r159Display();
            }
                break;
            case G3_DISPALY_MODE_R158_AND_R159: {
                /* 显示R158认证和R159认证同时运行的显示内容 */
                r158Andr159Display();
            }
                break;
            case G3_DISPALY_MODE_SWITCH_FOR_SIGNAL: {
                /* 根据车辆信号切换屏幕显示画面时屏幕显示的内容 */
                switchForSignalDisplay();
            }
                break;
            case G3_DISPALY_MODE_ALARM_EVENT_SIGNAL: {
                /* 根据报警信号切换屏幕显示画面时屏幕显示的内容 */
                switchForAlarmEventDisplay();
            }
                break;

            case G3_DISPALY_MODE_ALL_SLICING_4: {
                showFourCamera();
            }
                break;
            case G3_DISPALY_MODE_CAMERA3_FULL: {
                showOneCamera(CAMERA_ID_3);
            }
                break;
            case G3_DISPALY_MODE_CAMERA4_FULL: {
                showOneCamera(CAMERA_ID_4);
            }
                break;

            default: {
                /* 其他情况直接分屏 */
                showTwoCamera();
            }
                break;
        }

        /* 休眠40ms  强制降低屏幕输出的帧率 */
        usleep(1000 * showCmaeraYUVInterval);
    }
    pthread_setname_np(pthread_self(), "Finish");
}

void DispalayShower::setYUVDataToDispaly(CameraYUVData &cameraYuvData) {
    switch (cameraYuvData.getCameraId()) {
        case CAMERA_ID_1: {
            yuvCache_camera1 = &cameraYuvData;
        }
            break;
        case CAMERA_ID_2: {
            yuvCache_camera2 = &cameraYuvData;
        }
            break;
        case CAMERA_ID_3: {
            yuvCache_camera3 = &cameraYuvData;
        }
            break;
        case CAMERA_ID_4: {
            yuvCache_camera4 = &cameraYuvData;
        }
            break;
    }
}

int DispalayShower::getYUVData(uint8_t *buf, const int cameraId, int &width, int &height) {
    int ret = -1;

    switch (cameraId) {
        case CAMERA_ID_1: {
            if(yuvCache_camera1 != nullptr){
                ret = yuvCache_camera1->getYUVData(buf,yuvCache_camera1->getDataLen());
                width = yuvCache_camera1->getWidth();
                height = yuvCache_camera1->getHeight();
            }

        }
            break;
        case CAMERA_ID_2: {
            if(yuvCache_camera2 != nullptr) {
                ret = yuvCache_camera2->getYUVData(buf,yuvCache_camera2->getDataLen());
                width = yuvCache_camera2->getWidth();
                height = yuvCache_camera2->getHeight();
            }
        }
            break;
        case CAMERA_ID_3: {
            if(yuvCache_camera3 != nullptr) {
                ret = yuvCache_camera3->getYUVData(buf,yuvCache_camera3->getDataLen());
                width = yuvCache_camera3->getWidth();
                height = yuvCache_camera3->getHeight();
            }
        }
            break;
        case CAMERA_ID_4: {
            if(yuvCache_camera4 != nullptr) {
                ret = yuvCache_camera4->getYUVData(buf,yuvCache_camera4->getDataLen());
                width = yuvCache_camera4->getWidth();
                height = yuvCache_camera4->getHeight();
            }
        }
            break;
    }

    return ret;
}


bool DispalayShower::isCanShow() const {
    return canShow;
}

void DispalayShower::showTwoCamera() {
    int ret = -1;
    int width = 1280;
    int height = 720;
    ret = getYUVData(showYUVData_camera1, CAMERA_ID_1,width,height);
    if (ret > 0) {
//            /* 在这里对图像进行绘画 */
//            drawYUV(cameraYuvData_camera1);

        ret = getYUVData(showYUVData_camera2, CAMERA_ID_2,width,height);
        if (ret > 0) {
            /* 先把左边这张转成RGB888的格式  并缩小到720 * 576  */

            ret = rgaUtils.imageTransformation(yuvCache_camera1->getWidth(), yuvCache_camera1->getHeight(),
                                               XuRGAUtils::IMG_TYPE_NV21, showYUVData_camera1,
                                               DISPLAY_W, DISPLAY_H, XuRGAUtils::IMG_TYPE_RGBA8888, leftImag);
            if (ret > 0) {
                /* 再把右边这张转成RGB888的格式  并缩小到720 * 576  */
                ret = rgaUtils.imageTransformation(yuvCache_camera2->getWidth(),
                                                   yuvCache_camera2->getHeight(), XuRGAUtils::IMG_TYPE_NV21,
                                                   showYUVData_camera2, DISPLAY_W, DISPLAY_H,
                                                   XuRGAUtils::IMG_TYPE_RGBA8888, rightImag);
                if (ret > 0) {
                    /* 把两张图像合并一下 */
                    ret = rgaUtils.synthesisImg_LR(DISPLAY_W, DISPLAY_H, leftImag, DISPLAY_W, DISPLAY_H, rightImag,XuRGAUtils::IMG_TYPE_RGBA8888);
                    if (ret > 0) {
                        /* 输出到屏幕 */
                        transferRGBAToUSB(rightImag,DISPLAY_W,DISPLAY_H);
                    } else {
                        //printf("DispalayShower synthesis Imag fialed!  ret=%d \n",ret);
                    }

                } else {
//                        printf("right image transformation failed!   ret = %d  \n",ret);
                }
            } else {
//                    printf("left image transformation failed!   ret = %d  \n",ret);
            }
        } else {
            //printf("DispalayShower get camera2 YUVData fialed!  ret=%d \n",ret);

        }

    } else {
        //printf("DispalayShower get camera1 YUVData fialed!  ret=%d \n",ret);

    };


}

void DispalayShower::notShowCamera(int reason) {
    int ret = -1;

    switch (reason) {
        case NOT_SHOW_CAMERA_FOR_LOGO: {
            /* 显示log */
            ret = vo.voSetFmt(loggYUVdata);
            isUSBOutput = true;
        }
            break;
        case NOT_SHOW_CAMERA_FOR_OVER_SPEED_FRONT: {
            /* 显示前向的超速 */
            ret = vo.voSetFmt(overspeedFrontYUVdata);
            isUSBOutput = true;
        }
            break;
//        case NOT_SHOW_CAMERA_FOR_CAMERA_COVERED_FRONT: {
//            /* 显示前向的镜头遮挡 */
//            ret = vo.voSetFmt(camcoverFrontYUVdata);
//        }
            break;
        case NOT_SHOW_CAMERA_FOR_OVER_SPEED_REAR: {
            /* 显示后向的超速 */
            ret = vo.voSetFmt(overspeedRearYUVdata);
            isUSBOutput = true;
        }
            break;
//        case NOT_SHOW_CAMERA_FOR_CAMERA_COVERED_REAR: {
//            /* 显示后向的镜头遮挡 */
//            ret = vo.voSetFmt(camcoverRearYUVdata);
//        }
            break;
        case NOT_SHOW_CAMERA_FOR_OVER_SPEED_SIDEBACKWARD: {
            /* 显示侧边向后的超速 */
            ret = vo.voSetFmt(overspeedSidebackwardYUVdata);
            isUSBOutput = true;
        }
            break;
//        case NOT_SHOW_CAMERA_FOR_CAMERA_COVERED_SIDEBACKWARD: {
//            /* 显示侧边向后的镜头遮挡 */
//            ret = vo.voSetFmt(camcoverSidebackwardYUVdata);
//        }
            break;
        default: {
            /* 默认显示log */
            ret = vo.voSetFmt(loggYUVdata);
            isUSBOutput = true;
        }
            break;
    }

    /* 输出到显示屏失败的话就重启下输出芯片 */
    if (ret < 0) {
        canShow = false;
        vo.voFree();
        sleep(2);
        vo.voInit(DISPLAY_W,DISPLAY_H,G3_Configuration::getInstance().getG3DisplayOutputSignalType());
    } else {
        canShow = true;
    }

}


void DispalayShower::showOneCamera(int cameraId) {
    int ret = -1;
    int width = 1280;
    int height = 720;
    ret = getYUVData(showYUVData_camera1, cameraId,width,height);
    if (ret > 0) {
        /* 直接输出出去 */
        transferNV21YToUSB(showYUVData_camera1,width,height);

    } else {
//            printf("DispalayShower get camera1 YUVData fialed!  ret=%d \n",ret);

    };

}


void DispalayShower::setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
    objectInfolock.lock();
    if (!curDetectionResult_list.empty()) {
        /* 先看看是否在vector里面了 */
        bool isInVectot = false;
        for (int i = 0; i < static_cast<int>(curDetectionResult_list.size()); i++) {
            if (curDetectionResult_list[i].detectionResult.alarmDecisionType == detectionResult.alarmDecisionType &&
                curDetectionResult_list[i].detectionResult.curCameraType.cameraId ==
                detectionResult.curCameraType.cameraId) {
                isInVectot = true;
                curDetectionResult_list[i].curobjectInfo.objects.clear();
                copy(objectInfo.objects.begin(), objectInfo.objects.end(),
                     inserter(curDetectionResult_list[i].curobjectInfo.objects,
                              curDetectionResult_list[i].curobjectInfo.objects.begin()));
                /* 由于结构体里面没有裸指针，故直接用=就好了 */
                curDetectionResult_list[i].detectionResult = detectionResult;
            }
        }
        /* 如果没在vector里面就加一个 */
        if (!isInVectot) {
            CameraImagRunnable::BSDDetectionInfoAll temp;

            copy(temp.curobjectInfo.objects.begin(), temp.curobjectInfo.objects.end(),
                 inserter(objectInfo.objects, objectInfo.objects.begin()));
            /* 由于结构体里面没有裸指针，故直接用=就好了 */
            temp.detectionResult = detectionResult;
            curDetectionResult_list.push_back(temp);
        }
    } else {
        CameraImagRunnable::BSDDetectionInfoAll temp;

        copy(temp.curobjectInfo.objects.begin(), temp.curobjectInfo.objects.end(),
             inserter(objectInfo.objects, objectInfo.objects.begin()));
        /* 由于结构体里面没有裸指针，故直接用=就好了 */
        temp.detectionResult = detectionResult;
        curDetectionResult_list.push_back(temp);
    }
    objectInfolock.unlock();
}

void DispalayShower::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
    /* 先看看上一个报警是否还未持续两秒 */
    int lastAlarmInterval = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - lastAlarmEventInfoFomrDispaly.alarmTime;
    if(lastAlarmInterval >= MAX_ALARM_CAMERA_SHOW_TIME_FOR_DISPLAY){
        /* 持续时间超过两秒了，那么就可以显示这个报警了 */
        lastAlarmEventInfoFomrDispaly.alarmCameraId = static_cast<CAMERAIDLIST>(curCameraType.cameraId);
        lastAlarmEventInfoFomrDispaly.alarmTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
    }else{
        /* 持续时间未达到两秒，那么就忽略这个报警 */
        ; // not to do

    }
}

void DispalayShower::setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
//    objectInfolock.lock();
//    memcpy(&curDetectionResult, &detectionResult, sizeof(curDetectionResult));
//    objectInfolock.unlock();
}

void DispalayShower::r151Display() {


//    /* 先判断一下车速，车速到了某个阈值之后就不显示画面了 */
//    if ((int) curSpeed >= G3_Configuration::getInstance().getR151PedestrianDetectStopSpeed()) {
//        notShowCamera(NOT_SHOW_CAMERA_FOR_LOGO);
//    } else {
    /* 判断哪个镜头是151的，且朝向要是朝后的 */
    int cameraId = -1;
    std::vector<CameraType> cameraTypeList = G3_Configuration::getInstance().getCameraTypeList();
    for (std::size_t i = 0; i < cameraTypeList.size(); i++) {
        if (cameraTypeList[i].adType.pedestrian_r151 || cameraTypeList[i].adType.vehicle_r151) {
            if (cameraTypeList[i].cameraOrientation == CAMERA_ORIENTATION_BACKWARD) {
                cameraId = cameraTypeList[i].cameraId;
                break;
            }

        }
    }
    /* 有找到镜头则显示画面，没有就显示logo */
    if (cameraId != -1) {
        showR151(cameraId);

    } else {
        notShowCamera(NOT_SHOW_CAMERA_FOR_LOGO);
    }


//    }


}

void DispalayShower::setSpeed(const float speed, const int baseOf) {
    if (speed >= 0) {
        curSpeed = speed;
    }

}

void DispalayShower::setTurnSignal(const bool turnL, const bool turnR) {
    curTurnL = turnL;
    curTurnR = turnR;
}

void DispalayShower::setReverseSignal(const bool reverse) {
    curReverse = reverse;
}

void DispalayShower::r158Display() {
    /* 先判断一下车速，车速到了某个阈值之后就不显示画面了 */
    if ((int) curSpeed > G3_Configuration::getInstance().getR158DetectStopSpeed()) {
        notShowCamera(NOT_SHOW_CAMERA_FOR_OVER_SPEED_REAR);
    } else {
        /* 判断哪个镜头是158的 */
        int cameraId = -1;
        std::vector<CameraType> cameraTypeList = G3_Configuration::getInstance().getCameraTypeList();
        for (std::size_t i = 0; i < cameraTypeList.size(); i++) {
            if (cameraTypeList[i].adType.pedestrian_r158 || cameraTypeList[i].adType.vehicle_r158) {
                cameraId = cameraTypeList[i].cameraId;
                break;
            }
        }

        /* 有找到镜头则显示画面，没有就显示logo */
        if (cameraId != -1) {
            /* 如果有了倒车信号，那么需要显示158的镜头，不打就显示logo */
            if (curReverse) {
                /* 显示R158需要的镜头画面 */
                showR158(cameraId);

            } else {
                notShowCamera(NOT_SHOW_CAMERA_FOR_LOGO);
            }

        } else {
            notShowCamera(NOT_SHOW_CAMERA_FOR_LOGO);
        }


    }

}

void DispalayShower::showR158(const int cameraId, const bool needDrawZone) {
    /* 先获取整理R158的结果 */
    G3DetectionResult detectionResult;
    td::objectInfo_t curobjectInfo;
    objectInfolock.lock();
    if (!curDetectionResult_list.empty()) {
        for (int i = 0; i < static_cast<int>(curDetectionResult_list.size()); i++) {
            /* 看看是不是这个相机的R158的报警 */
            if ((curDetectionResult_list[i].detectionResult.curCameraType.cameraId == cameraId) &&
                (curDetectionResult_list[i].detectionResult.alarmDecisionType == ALARM_DECISION_TYPE_PEDESTRIAN_R158 ||
                 curDetectionResult_list[i].detectionResult.alarmDecisionType == ALARM_DECISION_TYPE_VEHICLE_R158)) {
                /* 整理报警信息和识别信息 */
                detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea1 |= curDetectionResult_list[i].detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea1;
                detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea2 |= curDetectionResult_list[i].detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea2;
                detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea3 |= curDetectionResult_list[i].detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea3;
                detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea1 |= curDetectionResult_list[i].detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea1;
                detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea2 |= curDetectionResult_list[i].detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea2;
                detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea3 |= curDetectionResult_list[i].detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea3;
                detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea1 |= curDetectionResult_list[i].detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea1;
                detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea2 |= curDetectionResult_list[i].detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea2;
                for (std::size_t j = 0; j < curDetectionResult_list[i].curobjectInfo.objects.size(); j++) {
                    curobjectInfo.objects.push_back(curDetectionResult_list[i].curobjectInfo.objects[j]);
                }

            }

        }
    }
    objectInfolock.unlock();


    /* 如果当前是行人报警状态记录一下最后一次有报警状态的事件，用来做延迟取消 */
    if( detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea1 ||  detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea2 ||  detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea3){
        lastR158PedestrianAlarmTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();

        lastR158PedestrianAlarm.hasPedestrianInAlarmArea1 = detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea1;
        lastR158PedestrianAlarm.hasPedestrianInAlarmArea2 = detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea2;
        lastR158PedestrianAlarm.hasPedestrianInAlarmArea3 = detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea3;
        lastR158PedestrianAlarm.hasCameraCoverInAlarmArea1 = detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea1;
        lastR158PedestrianAlarm.hasCameraCoverInAlarmArea2 = detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea2;
        lastR158PedestrianAlarm.hasCameraCoverInAlarmArea3 = detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea3;
    }else{
        /* 如果当前不是报警状态，那么就判断下是否达到了指定间隔，达到了指定间隔才允许变成没办法的状态,没达到就沿用旧的 */
        int delayTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - lastR158PedestrianAlarmTime;
        if(delayTime < MAX_ALARAM_CANCEL_DELAY_TIME){
            detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea1 = lastR158PedestrianAlarm.hasPedestrianInAlarmArea1;
            detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea2 = lastR158PedestrianAlarm.hasPedestrianInAlarmArea2;
            detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea3 = lastR158PedestrianAlarm.hasPedestrianInAlarmArea3;
            detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea1 = lastR158PedestrianAlarm.hasCameraCoverInAlarmArea1;
            detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea2 = lastR158PedestrianAlarm.hasCameraCoverInAlarmArea2;
            detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea3 = lastR158PedestrianAlarm.hasCameraCoverInAlarmArea3;
        }

    }


    /* 如果当前是车辆报警状态记录一下最后一次有报警状态的事件，用来做延迟取消 */
    if(detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea1 || detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea2){
        lastR158VehicleAlarmTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
        lastR158VehicleAlarm.hasVehicleInAlarmArea1 = detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea1;
        lastR158VehicleAlarm.hasVehicleInAlarmArea2 = detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea2;
    }else{
        /* 如果当前不是报警状态，那么就判断下是否达到了指定间隔，达到了指定间隔才允许变成没办法的状态,没达到就沿用旧的 */
        int delayTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - lastR158VehicleAlarmTime;

        if(delayTime < MAX_ALARAM_CANCEL_DELAY_TIME){
            detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea1 = lastR158VehicleAlarm.hasVehicleInAlarmArea1;
            detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea2 = lastR158VehicleAlarm.hasVehicleInAlarmArea2;
        }

    }

    /* 开始画ICON和报警区域 */
    int ret = -1;
    /* 先获取YUV数据的指针 */
    int width = 1280;
    int height = 720;
    ret = getYUVData(showYUVData_camera1, cameraId,width,height);


    if (ret > 0) {

//        /* 先看看是不是有遮挡报警，没有才需要画区域和icon，有就直接显示遮挡的画面 */
//        if(detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea1 || detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea2 || detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea3){
//            notShowCamera(NOT_SHOW_CAMERA_FOR_CAMERA_COVERED_REAR);
//        }else{

        cvtColor(yuvImg, rgbaImg, cv::COLOR_YUV2RGBA_NV12);
        /* 需要画报警区域的时候才画 */
        if (needDrawZone) {
            /* 获取所有的行人报警区域 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getR158PedestrianAlramArea3(),
                                               r158PedestrianAlarmArea3, 6,
                                               true);
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getR158PedestrianAlramArea2(),
                                               r158PedestrianAlarmArea2, 6,
                                               true);
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getR158PedestrianAlramArea1(),
                                               r158PedestrianAlarmArea1, 6,
                                               true);

            drawAlarmAreaByRGBA(rgbaImg,r158PedestrianAlarmArea3,cv::Scalar(0, 255, 0, 255),false);
            drawAlarmAreaByRGBA(rgbaImg,r158PedestrianAlarmArea2,cv::Scalar(255, 255, 0, 255),false);
            drawAlarmAreaByRGBA(rgbaImg,r158PedestrianAlarmArea1,cv::Scalar(255, 0, 0, 255),false);



            /* 开了识别开关就需要画识别框 */
            if (G3_Configuration::getInstance().getR158DispalyDetectInfoAdd() == 1) {
                /* 画一下识别框 */
                drawBSDInfoByMat(rgbaImg,width, height, curobjectInfo, true);
            }


        }


        /* 把镜头方向叠加到左上 */
        ret = rgaUtils.synthesisImg_R158(R158ICON_ORIENTATION_W, R158ICON_ORIENTATION_H, r158_rearRGBAdata, width, height, rgbaImg.data,
                                         XuRGAUtils::R158ICON_POSITION_LEFTTOP);

        /* 叠加行人的ICON */
        int pedestrianIconType = -1;
        if (detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea1) {
            pedestrianIconType = 0;
        } else if (detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea2) {
            pedestrianIconType = 1;
        } else if (detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea3) {
            pedestrianIconType = 2;
        }
        switch (pedestrianIconType) {
            case 0: {

                ret = rgaUtils.synthesisImg_R158(R158ICON_PEDESTRIAN_W, R158ICON_PEDESTRIAN_H,
                                                 r158_pedestrian_red_RGBAdata, width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_LEFTTOP);
            }
                break;
            case 1: {

                ret = rgaUtils.synthesisImg_R158(R158ICON_PEDESTRIAN_W, R158ICON_PEDESTRIAN_H,
                                                 r158_pedestrian_yellow_RGBAdata, width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_LEFTTOP);
            }
                break;

            case 2: {

                ret = rgaUtils.synthesisImg_R158(R158ICON_PEDESTRIAN_W, R158ICON_PEDESTRIAN_H,
                                                 r158_pedestrian_green_RGBAdata, width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_LEFTTOP);
            }
                break;


        }

        /* 叠加车辆的ICON */
        int vehicleIconType = -1;
        if (detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea1) {
            vehicleIconType = 0;
        } else if (detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea2) {
            vehicleIconType = 1;
        }

        switch (vehicleIconType) {
            case 0: {
                ret = rgaUtils.synthesisImg_R158(R158ICON_VEHICLE_W, R158ICON_VEHICLE_H, r158_vehicle_red_RGBAdata,
                                                 width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_RIGHTTOP);
            }
                break;
            case 1: {
                ret = rgaUtils.synthesisImg_R158(R158ICON_VEHICLE_W, R158ICON_VEHICLE_H, r158_vehicle_yellow_RGBAdata,
                                                 width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_RIGHTTOP);
            }
                break;
        }

        /* 输出到屏幕 */
        transferRGBAToUSB(rgbaImg.data,width,height);





    }
}

int DispalayShower::visPointListToYUVDataOpt_PointList(const std::vector<VISPoint> &visPointList,
                                                       YUVDataOpt_Point *yuvDataOpt_PointList, const int len,
                                                       const bool needMirror) {
    int ret = -1;
    if (len > 0 && !visPointList.empty()) {
        if (visPointList.size() >= static_cast<std::size_t>(len)) {
            for (int i = 0; i < len; i++) {
                /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
                if (needMirror) {
                    yuvDataOpt_PointList[i].x = (visPointList[i].x >= 640) ? (640 - (visPointList[i].x - 640)) : (640 +
                                                                                                                  (640 -
                                                                                                                   visPointList[i].x));
                } else {
                    yuvDataOpt_PointList[i].x = visPointList[i].x;
                }

                yuvDataOpt_PointList[i].y = visPointList[i].y;
            }
            ret = 0;
        }
    }
    return ret;
}

void DispalayShower::getIconAndDisplayImgData(const std::string &resolutionRatio) {
    /* 用来组装文件名的string变量 */
    std::string fileName = "";
    /* 读取一下log的图像 */
    fileName.clear();
    fileName.append("screenImg/blackImg_"+resolutionRatio+".uyvy");
    loggYUVdataLen = DISPLAY_H * DISPLAY_W * 2;
    loggYUVdata = static_cast<uint8_t *>(malloc(loggYUVdataLen));
    if(XuFile::getInstance().readFile(fileName.c_str(),loggYUVdata,loggYUVdataLen) != loggYUVdataLen){
        printf("read logo.uyvy failed! \n");
    }

    /* 读取一下前向超速的图像 */
    fileName.clear();
    fileName.append("screenImg/OverSpeedFront_"+resolutionRatio+".uyvy");
    overspeedFrontYUVdataLen = DISPLAY_H * DISPLAY_W * 2;
    overspeedFrontYUVdata = static_cast<uint8_t *>(malloc(overspeedFrontYUVdataLen));
    if(XuFile::getInstance().readFile(fileName.c_str(),overspeedFrontYUVdata,overspeedFrontYUVdataLen) != overspeedFrontYUVdataLen){
        printf("read OverSpeedFront.uyvy failed! \n");
    }

    /* 读取一下后向超速的图像 */
    fileName.clear();
    fileName.append("screenImg/OverSpeedRear_"+resolutionRatio+".uyvy");
    overspeedRearYUVdataLen = DISPLAY_H * DISPLAY_W * 2;
    overspeedRearYUVdata = static_cast<uint8_t *>(malloc(overspeedRearYUVdataLen));
    if(XuFile::getInstance().readFile(fileName.c_str(),overspeedRearYUVdata,overspeedRearYUVdataLen) != overspeedRearYUVdataLen){
        printf("read OverSpeedRear.uyvy failed! \n");
    }

    /* 读取一下侧边向后超速的图像 */
    fileName.clear();
    fileName.append("screenImg/OverSpeedSidebackward_"+resolutionRatio+".uyvy");
    overspeedSidebackwardYUVdataLen = DISPLAY_H * DISPLAY_W * 2;
    overspeedSidebackwardYUVdata = static_cast<uint8_t *>(malloc(overspeedSidebackwardYUVdataLen));
    if(XuFile::getInstance().readFile(fileName.c_str(),overspeedSidebackwardYUVdata,overspeedSidebackwardYUVdataLen) != overspeedSidebackwardYUVdataLen){
        printf("read OverSpeedSidebackward.uyvy failed! \n");
    }

    /* 读取一下R159的front的icon */
    r159_frontRGBAdataLen = R158ICON_ORIENTATION_W * R158ICON_ORIENTATION_H * 4;
    r159_frontRGBAdata = static_cast<uint8_t *>(malloc(r159_frontRGBAdataLen));
    if(XuFile::getInstance().readFile("screenImg/R158_front.rgba",r159_frontRGBAdata,r159_frontRGBAdataLen) != r159_frontRGBAdataLen){
        printf("read R159_front.rgba failed! \n");
    }

    /* 读取一下R158的rear的icon */
    r158_rearRGBAdataLen = R158ICON_ORIENTATION_W * R158ICON_ORIENTATION_H * 4;
    r158_rearRGBAdata = static_cast<uint8_t *>(malloc(r158_rearRGBAdataLen));
    if(XuFile::getInstance().readFile("screenImg/R158_rear.rgba",r158_rearRGBAdata,r158_rearRGBAdataLen) != r158_rearRGBAdataLen){
        printf("read R158_rear.rgba failed! \n");
    }

    /* 读取一下R151的sidebackward的icon */
    r151_sidebackwardRGBAdataLen = R151ICON_ORIENTATION_W * R151ICON_ORIENTATION_H * 4;
    r151_sidebackwardRGBAdata = static_cast<uint8_t *>(malloc(r151_sidebackwardRGBAdataLen));
    if(XuFile::getInstance().readFile("screenImg/R151_sidebackward.rgba",r151_sidebackwardRGBAdata,r151_sidebackwardRGBAdataLen) != r151_sidebackwardRGBAdataLen){
        printf("read R158_rear.rgba failed! \n");
    }

    /* 读取一下R158的绿色人的icon */
    r158_pedestrian_green_RGBAdataLen = R158ICON_PEDESTRIAN_H * R158ICON_PEDESTRIAN_W * 4;
    r158_pedestrian_green_RGBAdata = static_cast<uint8_t *>(malloc(r158_pedestrian_green_RGBAdataLen));
    if(XuFile::getInstance().readFile("screenImg/R158_pedestrian_green.rgba",r158_pedestrian_green_RGBAdata,r158_pedestrian_green_RGBAdataLen) != r158_pedestrian_green_RGBAdataLen){
        printf("read R158_pedestrian_green.rgba failed! \n");
    }

    /* 读取一下R158的黄色人的icon */
    r158_pedestrian_yellow_RGBAdataLen = R158ICON_PEDESTRIAN_H * R158ICON_PEDESTRIAN_W * 4;
    r158_pedestrian_yellow_RGBAdata = static_cast<uint8_t *>(malloc(r158_pedestrian_yellow_RGBAdataLen));
    if(XuFile::getInstance().readFile("screenImg/R158_pedestrian_yellow.rgba",r158_pedestrian_yellow_RGBAdata,r158_pedestrian_yellow_RGBAdataLen) != r158_pedestrian_yellow_RGBAdataLen){
        printf("read R158_pedestrian_yellow.rgba failed! \n");
    }

    /* 读取一下R158的红色人的icon */
    r158_pedestrian_red_RGBAdataLen = R158ICON_PEDESTRIAN_H * R158ICON_PEDESTRIAN_W * 4;
    r158_pedestrian_red_RGBAdata = static_cast<uint8_t *>(malloc(r158_pedestrian_red_RGBAdataLen));
    if(XuFile::getInstance().readFile("screenImg/R158_pedestrian_red.rgba",r158_pedestrian_red_RGBAdata,r158_pedestrian_red_RGBAdataLen) != r158_pedestrian_red_RGBAdataLen){
        printf("read R158_pedestrian_red.rgba failed! \n");
    }

    /* 读取一下R158的绿色车的icon */
    r158_vehicle_green_RGBAdataLen = DISPLAY_H * DISPLAY_W * 2;
    r158_vehicle_green_RGBAdata = static_cast<uint8_t *>(malloc(r158_vehicle_green_RGBAdataLen));
    if(XuFile::getInstance().readFile("screenImg/R158_vehicle_green.rgba",r158_vehicle_green_RGBAdata,r158_vehicle_green_RGBAdataLen) != r158_vehicle_green_RGBAdataLen){
        printf("read R158_vehicle_green.rgba failed! \n");
    }

    /* 读取一下R158的黄色车的icon */
    r158_vehicle_yellow_RGBAdataLen = R158ICON_VEHICLE_H * R158ICON_VEHICLE_W * 4;
    r158_vehicle_yellow_RGBAdata = static_cast<uint8_t *>(malloc(r158_vehicle_yellow_RGBAdataLen));
    if(XuFile::getInstance().readFile("screenImg/R158_vehicle_yellow.rgba",r158_vehicle_yellow_RGBAdata,r158_vehicle_yellow_RGBAdataLen) != r158_vehicle_yellow_RGBAdataLen){
        printf("read R158_vehicle_yellow.rgba failed! \n");
    }

    /* 读取一下R158的红色车的icon */
    r158_vehicle_red_RGBAdataLen = R158ICON_VEHICLE_H * R158ICON_VEHICLE_W * 4;
    r158_vehicle_red_RGBAdata = static_cast<uint8_t *>(malloc(r158_vehicle_red_RGBAdataLen));
    if(XuFile::getInstance().readFile("screenImg/R158_vehicle_red.rgba",r158_vehicle_red_RGBAdata,r158_vehicle_red_RGBAdataLen) != r158_vehicle_red_RGBAdataLen){
        printf("read R158_vehicle_red.rgba failed! \n");
    }
}

void DispalayShower::showR151(const int cameraId) {
    /* 先获取整理R151的结果 */
    G3DetectionResult detectionResult;
    td::objectInfo_t curobjectInfo;
    bool hasCameraCover = false;
    objectInfolock.lock();
    if (!curDetectionResult_list.empty()) {
        for (int i = 0; i < static_cast<int>(curDetectionResult_list.size()); i++) {
            if ((curDetectionResult_list[i].detectionResult.curCameraType.cameraId == cameraId)) {
                hasCameraCover |= detectionResult.r151BsdDetectionInfo.hasCameraCoverInAlarmArea1;
                hasCameraCover |= detectionResult.r151BsdDetectionInfo.hasCameraCoverInAlarmArea2;
                hasCameraCover |= detectionResult.r151BsdDetectionInfo.hasCameraCoverInAlarmArea3;
                hasCameraCover |= detectionResult.r151BsdDetectionInfo.hasCameraCoverInAlarmArea4;
                for (std::size_t j = 0; j < curDetectionResult_list[i].curobjectInfo.objects.size(); j++) {
                    curobjectInfo.objects.push_back(curDetectionResult_list[i].curobjectInfo.objects[j]);
                }

            }
        }
    }
    objectInfolock.unlock();

    /* 开始画ICON和报警区域 */
    int ret = -1;
    int width = 1280;
    int height = 720;
    ret = getYUVData(showYUVData_camera1, cameraId,width,height);
    if (ret > 0) {

//        /* 先看看是不是有遮挡报警，没有才需要画区域和icon，有就直接显示遮挡的画面 */
//        if(hasCameraCover){
//            notShowCamera(NOT_SHOW_CAMERA_FOR_CAMERA_COVERED_SIDEBACKWARD);
//        }else{

        /* 先转成RGBA */
        cvtColor(yuvImg, rgbaImg, cv::COLOR_YUV2RGBA_NV12);
        /* 如果开了叠加，那么就画一下报警区域和识别框 */
        if (G3_Configuration::getInstance().getR151DispalyDetectInfoAdd() == 1) {
            /* 先看看是不是需要镜像 */
            bool needMirror = false;
            std::vector<CameraType> cameraTypeList = G3_Configuration::getInstance().getCameraTypeList();
            for (std::size_t i = 0; i < cameraTypeList.size(); i++) {
                if (cameraTypeList[i].cameraId == cameraId ||
                    cameraTypeList[i].cameraOrientation == CAMERA_ORIENTATION_BACKWARD) {
                    needMirror = true;
                }
            }
            /* 获取五个报警区域 */
            switch (cameraId) {
                case CAMERA_ID_1: {
                    /* 先获取行人报警区域1 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera1R151PedestrianAlramArea1(),
                            r151PedestrianAlarmArea1, 6, needMirror);
                    /* 再获取行人报警区域2 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera1R151PedestrianAlramArea2(),
                            r151PedestrianAlarmArea2, 6, needMirror);
                    /* 先获取行人报警区域3 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera1R151PedestrianAlramArea3(),
                            r151PedestrianAlarmArea3, 6, needMirror);
                    /* 再获取行人报警区域4 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera1R151PedestrianAlramArea4(),
                            r151PedestrianAlarmArea4, 6, needMirror);
                    /* 再获取车辆报警区域1 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera1R151VehicleAlramArea1(), r151VehicleAlarmArea1, 6,
                            needMirror);
                    /* 再获取车辆报警区域2 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera1R151VehicleAlramArea2(), r151VehicleAlarmArea2, 6,
                            needMirror);

                }
                    break;
                case CAMERA_ID_2: {
                    /* 先获取行人报警区域1 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera2R151PedestrianAlramArea1(),
                            r151PedestrianAlarmArea1, 6, needMirror);
                    /* 再获取行人报警区域2 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera2R151PedestrianAlramArea2(),
                            r151PedestrianAlarmArea2, 6, needMirror);
                    /* 先获取行人报警区域3 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera2R151PedestrianAlramArea3(),
                            r151PedestrianAlarmArea3, 6, needMirror);
                    /* 再获取行人报警区域4 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera2R151PedestrianAlramArea4(),
                            r151PedestrianAlarmArea4, 6, needMirror);
                    /* 再获取车辆报警区域1 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera2R151VehicleAlramArea1(), r151VehicleAlarmArea1, 6,
                            needMirror);
                    /* 再获取车辆报警区域2 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera2R151VehicleAlramArea2(), r151VehicleAlarmArea2, 6,
                            needMirror);
                }
                    break;
                case CAMERA_ID_3: {
                    /* 先获取行人报警区域1 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera3R151PedestrianAlramArea1(),
                            r151PedestrianAlarmArea1, 6, needMirror);
                    /* 再获取行人报警区域2 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera3R151PedestrianAlramArea2(),
                            r151PedestrianAlarmArea2, 6, needMirror);
                    /* 先获取行人报警区域3 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera3R151PedestrianAlramArea3(),
                            r151PedestrianAlarmArea3, 6, needMirror);
                    /* 再获取行人报警区域4 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera3R151PedestrianAlramArea4(),
                            r151PedestrianAlarmArea4, 6, needMirror);
                    /* 再获取车辆报警区域1 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera3R151VehicleAlramArea1(), r151VehicleAlarmArea1, 6,
                            needMirror);
                    /* 再获取车辆报警区域2 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera3R151VehicleAlramArea2(), r151VehicleAlarmArea2, 6,
                            needMirror);

                }
                    break;
                case CAMERA_ID_4: {
                    /* 先获取行人报警区域1 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera4R151PedestrianAlramArea1(),
                            r151PedestrianAlarmArea1, 6, needMirror);
                    /* 再获取行人报警区域2 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera4R151PedestrianAlramArea2(),
                            r151PedestrianAlarmArea2, 6, needMirror);
                    /* 先获取行人报警区域3 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera4R151PedestrianAlramArea3(),
                            r151PedestrianAlarmArea3, 6, needMirror);
                    /* 再获取行人报警区域4 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera4R151PedestrianAlramArea4(),
                            r151PedestrianAlarmArea4, 6, needMirror);
                    /* 再获取车辆报警区域1 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera4R151VehicleAlramArea1(), r151VehicleAlarmArea1, 6,
                            needMirror);
                    /* 再获取车辆报警区域2 */
                    visPointListToYUVDataOpt_PointList(
                            G3_Configuration::getInstance().getCamera4R151VehicleAlramArea2(), r151VehicleAlarmArea2, 6,
                            needMirror);
                }
                    break;
            }
            drawAlarmAreaByRGBA(rgbaImg,r151PedestrianAlarmArea1,cv::Scalar(255,0,0,255));
            drawAlarmAreaByRGBA(rgbaImg,r151PedestrianAlarmArea2,cv::Scalar(255,255,0,255));
            drawAlarmAreaByRGBA(rgbaImg,r151PedestrianAlarmArea3,cv::Scalar(0,0,255,255));
            drawAlarmAreaByRGBA(rgbaImg,r151PedestrianAlarmArea4,cv::Scalar(0,255,0,255));

            /* 画一下识别框 */
            drawBSDInfoByMat(rgbaImg, width, height, curobjectInfo, needMirror);



        }



        /* 把ICON叠加到中上 */
        ret = rgaUtils.synthesisImg_R158(R151ICON_ORIENTATION_W, R151ICON_ORIENTATION_H, r151_sidebackwardRGBAdata, width, height, rgbaImg.data,
                                         XuRGAUtils::R158ICON_POSITION_CENTRETOP);

        /* 输出到屏幕 */
        transferRGBAToUSB(rgbaImg.data,width,height);


    }
}

void DispalayShower::r159Display() {
    /* 先判断一下车速，车速到了某个阈值之后就不显示画面了 */
    if ((int) curSpeed >= G3_Configuration::getInstance().getR159DetectStopSpeed()) {
        notShowCamera(NOT_SHOW_CAMERA_FOR_OVER_SPEED_FRONT);
    } else {
        /* 判断哪个镜头是159的 */
        int cameraId = -1;
        std::vector<CameraType> cameraTypeList = G3_Configuration::getInstance().getCameraTypeList();
        for (std::size_t i = 0; i < cameraTypeList.size(); i++) {
            if (cameraTypeList[i].adType.pedestrian_r159 || cameraTypeList[i].adType.vehicle_r159) {
                cameraId = cameraTypeList[i].cameraId;
                break;
            }
        }

        /* 有找到镜头则显示画面，没有就显示logo */
        if (cameraId != -1) {
            /* 如果有了倒车信号就显示LOGO，没有倒车信号就正常显示画面 */
            if (!curReverse) {
                /* 显示R159需要的镜头画面 */
                showR159(cameraId);

            } else {
                notShowCamera(NOT_SHOW_CAMERA_FOR_LOGO);
            }

        } else {
            notShowCamera(NOT_SHOW_CAMERA_FOR_LOGO);
        }
    }


}

void DispalayShower::showR159(const int cameraId) {
    /* 先获取整理R158的结果 */
    G3DetectionResult detectionResult;
    td::objectInfo_t curobjectInfo;
    objectInfolock.lock();
    if (!curDetectionResult_list.empty()) {
        for (int i = 0; i < static_cast<int>(curDetectionResult_list.size()); i++) {
            /* 找下是不是这镜头的R159的信息 */
            if ((curDetectionResult_list[i].detectionResult.curCameraType.cameraId == cameraId) &&
                (curDetectionResult_list[i].detectionResult.alarmDecisionType == ALARM_DECISION_TYPE_PEDESTRIAN_R159 ||
                 curDetectionResult_list[i].detectionResult.alarmDecisionType == ALARM_DECISION_TYPE_VEHICLE_R159)) {

                /* 整理一下报警信息和识别信息 */
                detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea1 |= curDetectionResult_list[i].detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea1;
                detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea2 |= curDetectionResult_list[i].detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea2;
                detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea3 |= curDetectionResult_list[i].detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea3;
                detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea1 |= curDetectionResult_list[i].detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea1;
                detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea2 |= curDetectionResult_list[i].detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea2;
                detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea3 |= curDetectionResult_list[i].detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea3;
                detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea1 |= curDetectionResult_list[i].detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea1;
                detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea2 |= curDetectionResult_list[i].detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea2;
                for (std::size_t j = 0; j < curDetectionResult_list[i].curobjectInfo.objects.size(); j++) {
                    curobjectInfo.objects.push_back(curDetectionResult_list[i].curobjectInfo.objects[j]);
                }



            }
        }
    }
    objectInfolock.unlock();


    /* 如果当前是行人报警状态记录一下最后一次有报警状态的事件，用来做延迟取消 */
    if( detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea1 ||  detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea2 ||  detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea3){
        lastR159PedestrianAlarmTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();

        lastR159PedestrianAlarm.hasPedestrianInAlarmArea1 = detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea1;
        lastR159PedestrianAlarm.hasPedestrianInAlarmArea2 = detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea2;
        lastR159PedestrianAlarm.hasPedestrianInAlarmArea3 = detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea3;
        lastR159PedestrianAlarm.hasCameraCoverInAlarmArea1 = detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea1;
        lastR159PedestrianAlarm.hasCameraCoverInAlarmArea2 = detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea2;
        lastR159PedestrianAlarm.hasCameraCoverInAlarmArea3 = detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea3;
    }else{
        /* 如果当前不是报警状态，那么就判断下是否达到了指定间隔，达到了指定间隔才允许变成没办法的状态,没达到就沿用旧的 */
        int delayTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - lastR159PedestrianAlarmTime;
        if(delayTime < MAX_ALARAM_CANCEL_DELAY_TIME){
            detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea1 = lastR159PedestrianAlarm.hasPedestrianInAlarmArea1;
            detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea2 = lastR159PedestrianAlarm.hasPedestrianInAlarmArea2;
            detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea3 = lastR159PedestrianAlarm.hasPedestrianInAlarmArea3;
            detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea1 = lastR159PedestrianAlarm.hasCameraCoverInAlarmArea1;
            detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea2 = lastR159PedestrianAlarm.hasCameraCoverInAlarmArea2;
            detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea3 = lastR159PedestrianAlarm.hasCameraCoverInAlarmArea3;
        }

    }


    /* 如果当前是车辆报警状态记录一下最后一次有报警状态的事件，用来做延迟取消 */
    if(detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea1 || detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea2){
        lastR159VehicleAlarmTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
        lastR159VehicleAlarm.hasVehicleInAlarmArea1 = detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea1;
        lastR159VehicleAlarm.hasVehicleInAlarmArea2 = detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea2;
    }else{
        /* 如果当前不是报警状态，那么就判断下是否达到了指定间隔，达到了指定间隔才允许变成没办法的状态,没达到就沿用旧的 */
        int delayTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - lastR159VehicleAlarmTime;

        if(delayTime < MAX_ALARAM_CANCEL_DELAY_TIME){
            detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea1 = lastR159VehicleAlarm.hasVehicleInAlarmArea1;
            detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea2 = lastR159VehicleAlarm.hasVehicleInAlarmArea2;
        }

    }


    /* 开始画ICON和报警区域 */
    int ret = -1;
    int width = 1280;
    int height = 720;
    ret = getYUVData(showYUVData_camera1, cameraId,width,height);
    if (ret > 0) {

//        /* 先看看是不是有遮挡报警，没有才需要画区域和icon，有就直接显示遮挡的画面 */
//        if(detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea1 || detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea2 || detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea3){
//            notShowCamera(NOT_SHOW_CAMERA_FOR_CAMERA_COVERED_FRONT);
//        }else{


        /* 先转成RGBA */
        cvtColor(yuvImg, rgbaImg, cv::COLOR_YUV2RGBA_NV12);

        /* 获取各个报警区域 */
        visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getR159PedestrianAlramArea3(),
                                           r159PedestrianAlarmArea3, 6,
                                           false);
        visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getR159PedestrianAlramArea2(),
                                           r159PedestrianAlarmArea2, 6,
                                           false);
        visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getR159PedestrianAlramArea1(),
                                           r159PedestrianAlarmArea1, 6,
                                           false);
        /* 画行人报警区域3 由于两个相机都是固定的720P，所以这里直接用相机1的宽高，不太考虑兼容性了 */
        drawAlarmAreaByRGBA(rgbaImg,r159PedestrianAlarmArea3,cv::Scalar(0,255,0,255),false);

        /* 画行人报警区域2 */
        drawAlarmAreaByRGBA(rgbaImg,r159PedestrianAlarmArea2,cv::Scalar(255,255,0,255),false);

        /* 画行人报警区域1 */
        drawAlarmAreaByRGBA(rgbaImg,r159PedestrianAlarmArea1,cv::Scalar(255,0,0,255),false);

        /* 开了识别开关就需要画识别框 */
        if (G3_Configuration::getInstance().getR159DispalyDetectInfoAdd() == 1) {
            /* 画一下识别框 */
            drawBSDInfoByMat(rgbaImg,width, height, curobjectInfo,false);
        }


        /* 叠加各种ICON */

        /* 把ICON叠加到中上 */
        ret = rgaUtils.synthesisImg_R158(R158ICON_ORIENTATION_W, R158ICON_ORIENTATION_H, r159_frontRGBAdata, width, height, rgbaImg.data,
                                         XuRGAUtils::R158ICON_POSITION_CENTRETOP);


        /* 叠加行人的ICON */
        int pedestrianIconType = -1;
        if (detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea1) {
            pedestrianIconType = 0;
        } else if (detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea2) {
            pedestrianIconType = 1;
        } else if (detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea3) {
            pedestrianIconType = 2;
        }
        switch (pedestrianIconType) {
            case 0: {

                ret = rgaUtils.synthesisImg_R158(R158ICON_PEDESTRIAN_W, R158ICON_PEDESTRIAN_H,
                                                 r158_pedestrian_red_RGBAdata, width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_LEFTTOP);
            }
                break;
            case 1: {

                ret = rgaUtils.synthesisImg_R158(R158ICON_PEDESTRIAN_W, R158ICON_PEDESTRIAN_H,
                                                 r158_pedestrian_yellow_RGBAdata, width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_LEFTTOP);
            }
                break;

            case 2: {

                ret = rgaUtils.synthesisImg_R158(R158ICON_PEDESTRIAN_W, R158ICON_PEDESTRIAN_H,
                                                 r158_pedestrian_green_RGBAdata, width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_LEFTTOP);
            }
                break;


        }

        /* 叠加车辆的ICON */
        int vehicleIconType = -1;
        if (detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea1) {
            vehicleIconType = 0;
        } else if (detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea2) {
            vehicleIconType = 1;
        }

        switch (vehicleIconType) {
            case 0: {
                ret = rgaUtils.synthesisImg_R158(R158ICON_VEHICLE_W, R158ICON_VEHICLE_H, r158_vehicle_red_RGBAdata,
                                                 width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_RIGHTTOP);
            }
                break;
            case 1: {
                ret = rgaUtils.synthesisImg_R158(R158ICON_VEHICLE_W, R158ICON_VEHICLE_H, r158_vehicle_yellow_RGBAdata,
                                                 width, height, rgbaImg.data,
                                                 XuRGAUtils::R158ICON_POSITION_RIGHTTOP);
            }
                break;
        }

        /* 输出到屏幕 */
        transferRGBAToUSB(rightImag,width,height);


    }
}

void DispalayShower::r158Andr159Display() {
    /* 判断哪个镜头是158的 */
    int cameraId = -1;
    std::vector<CameraType> cameraTypeList = G3_Configuration::getInstance().getCameraTypeList();
    for (std::size_t i = 0; i < cameraTypeList.size(); i++) {
        if (cameraTypeList[i].adType.pedestrian_r158 || cameraTypeList[i].adType.vehicle_r158) {
            cameraId = cameraTypeList[i].cameraId;
            break;
        }
    }

    printf("r158Andr159Display  curReverse=%d  \n",curReverse);
    /* 有找到镜头则显示画面，没有就显示logo */
    if (cameraId != -1) {
        /* 看看是不是打了倒车 */
        if (curReverse) {
            r158Display();
        } else {
            showR158(cameraId, false);
        }
    } else {
        notShowCamera(NOT_SHOW_CAMERA_FOR_LOGO);
    }

}

void DispalayShower::drawBSDInfo(uint8_t *yuvdata, const int len, const int imagWidth, const int imagHeight,
                                 td::objectInfo_t &curobjectInfo, const bool needMirror) {

    try {
        YUVDataOpt_Point objectList[2];
        if (!curobjectInfo.objects.empty()) {
            for (int j = 0; j < static_cast<int>(curobjectInfo.objects.size()); j++) {
                /* 整理一下框 免得超出屏幕导致崩溃 */
                if (curobjectInfo.objects[j].mLeft < 0 || curobjectInfo.objects[j].mRight > imagWidth ||
                    curobjectInfo.objects[j].mTop < 0 || curobjectInfo.objects[j].mBottom > imagHeight) {
                    if (curobjectInfo.objects[j].mLeft < 0) {
                        curobjectInfo.objects[j].mLeft = 0;
                    }
                    if (curobjectInfo.objects[j].mRight > imagWidth) {
                        curobjectInfo.objects[j].mLeft = imagWidth;
                    }
                    if (curobjectInfo.objects[j].mTop < 0) {
                        curobjectInfo.objects[j].mTop = 0;
                    }
                    if (curobjectInfo.objects[j].mBottom > imagHeight) {
                        curobjectInfo.objects[j].mBottom = imagHeight;
                    }
                }


                /* 把框画到图像上  不同的类型用不同的颜色 */
                int color = XuYUVDataOpt::COLOR_GREEN;
                if (strcmp(curobjectInfo.objects[j].label, "person") == 0 ||
                    strcmp(curobjectInfo.objects[j].label, "cyclist") == 0 ||
                    strcmp(curobjectInfo.objects[j].label, "motorcyclist") == 0) {
                    /* 是行人、单车、摩托车则用深绿色框 */
                    color = XuYUVDataOpt::COLOR_DARK_GREEN;
                } else if (strcmp(curobjectInfo.objects[j].label, "rider") == 0) {
                    /* 骑自行车、摩托的人则用紫色框（现已废弃） */
                    color = XuYUVDataOpt::COLOR_PURPLE;
                } else if (strcmp(curobjectInfo.objects[j].label, "car") == 0) {
                    /* 小轿车则用绿色框 */
                    color = XuYUVDataOpt::COLOR_GREEN;
                } else if (strcmp(curobjectInfo.objects[j].label, "bus") == 0) {
                    /* 公交车则用黄色框 */
                    color = XuYUVDataOpt::COLOR_YELLOW;
                } else if (strcmp(curobjectInfo.objects[j].label, "truck") == 0) {
                    /* 货车则用蓝色框 */
                    color = XuYUVDataOpt::COLOR_BLUE;
                } else if (strstr(curobjectInfo.objects[j].label, "A1-")) {
                    /* 一级报警区域内的则用红色框 */
                    color = XuYUVDataOpt::COLOR_RED;
                } else if (strstr(curobjectInfo.objects[j].label, "A2-")) {
                    /* 二级报警区域内的则用红色框 */
                    color = XuYUVDataOpt::COLOR_RED;
                }
                /* 把对角线的坐标填进去 */
                objectList[0].x = curobjectInfo.objects[j].mLeft;
                objectList[0].y = curobjectInfo.objects[j].mTop;
                objectList[1].x = curobjectInfo.objects[j].mRight;
                objectList[1].y = curobjectInfo.objects[j].mBottom;
                /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
                if (needMirror) {
                    objectList[0].x = (curobjectInfo.objects[j].mLeft >= 640) ? (640 -
                                                                                 (curobjectInfo.objects[j].mLeft - 640))
                                                                              : (640 + (640 -
                                                                                        curobjectInfo.objects[j].mLeft));
                    objectList[1].x = (curobjectInfo.objects[j].mRight >= 640) ? (640 -
                                                                                  (curobjectInfo.objects[j].mRight -
                                                                                   640)) : (640 + (640 -
                                                                                                   curobjectInfo.objects[j].mRight));
                }

                /* 开始画 */
                yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth,
                                      imagHeight, yuvdata,
                                      color,
                                      XuYUVDataOpt::TYPE_RECTANGLE);

//                /* 顺便把分数也给画上去 */
//                std::string scoreStr = std::to_string(curobjectInfo.objects[j].mScore);
//                yuvDataOpt.drawToNV21(objectList[0].x, objectList[0].y + 50, -1, -1, -1, -1, -1, -1,
//                                      scoreStr.substr(0, scoreStr.find(".") + 3).c_str(), imagWidth,
//                                      imagHeight, yuvdata,
//                                      color,
//                                      XuYUVDataOpt::TYPE_ONLY_TEXT);
            }

        }
    } catch (...) {

    }


}

void DispalayShower::transferNV21YToUSB(uint8_t *const yuvbuf, const int width, const int height) {
    /* 转格式跟分辨率 */
    int ret = rgaUtils.imageTransformation(width, height,
                                       XuRGAUtils::IMG_TYPE_NV21, yuvbuf,
                                       DISPLAY_W, DISPLAY_H, XuRGAUtils::IMG_TYPE_UYVY_422, showData);
    if (ret > 0) {
        ret = vo.voSetFmt(showData);
        isUSBOutput = true;
        /* 输出到显示屏失败的话就重启下输出芯片 */
        if (ret < 0) {
            canShow = false;
            vo.voFree();
            sleep(2);
            vo.voInit(DISPLAY_W,DISPLAY_H,G3_Configuration::getInstance().getG3DisplayOutputSignalType());
        } else {
            canShow = true;
        }
    }
}

void DispalayShower::transferRGBAToUSB(uint8_t *const rgbabuf, const int width, const int height) {
    /* 最后再把图像转成UYVY的格式 */
    int ret = rgaUtils.imageTransformation(width, height, XuRGAUtils::IMG_TYPE_RGBA8888,
                                           rgbabuf, DISPLAY_W, DISPLAY_H,
                                       XuRGAUtils::IMG_TYPE_UYVY_422, showData);
    if (ret > 0) {
        ret = vo.voSetFmt(showData);
        isUSBOutput = true;
        /* 输出到显示屏失败的话就重启下输出芯片 */
        if (ret < 0) {
            canShow = false;
            vo.voFree();
            sleep(2);
            vo.voInit(DISPLAY_W,DISPLAY_H,G3_Configuration::getInstance().getG3DisplayOutputSignalType());
        } else {
            canShow = true;
        }
    }
}

void DispalayShower::drawBSDInfoByMat(cv::Mat srcimg, const int imagWidth, const int imagHeight,
                                      td::objectInfo_t &curobjectInfo, const bool needMirror) {
    try {
        YUVDataOpt_Point objectList[2];
        if (!curobjectInfo.objects.empty()) {
            for (int j = 0; j < static_cast<int>(curobjectInfo.objects.size()); j++) {
                /* 整理一下框 免得超出屏幕导致崩溃 */
                if (curobjectInfo.objects[j].mLeft < 0 || curobjectInfo.objects[j].mRight > imagWidth ||
                    curobjectInfo.objects[j].mTop < 0 || curobjectInfo.objects[j].mBottom > imagHeight) {
                    if (curobjectInfo.objects[j].mLeft < 0) {
                        curobjectInfo.objects[j].mLeft = 0;
                    }
                    if (curobjectInfo.objects[j].mRight > imagWidth) {
                        curobjectInfo.objects[j].mLeft = imagWidth;
                    }
                    if (curobjectInfo.objects[j].mTop < 0) {
                        curobjectInfo.objects[j].mTop = 0;
                    }
                    if (curobjectInfo.objects[j].mBottom > imagHeight) {
                        curobjectInfo.objects[j].mBottom = imagHeight;
                    }
                }


                /* 把框画到图像上  不同的类型用不同的颜色 */
                cv::Scalar color(255,255,255,255);
                if (strcmp(curobjectInfo.objects[j].label, "person") == 0 ||
                    strcmp(curobjectInfo.objects[j].label, "cyclist") == 0 ||
                    strcmp(curobjectInfo.objects[j].label, "motorcyclist") == 0) {
                    /* 是行人、单车、摩托车则用深绿色框 */
                    color = cv::Scalar(0,102,0,255);
                } else if (strcmp(curobjectInfo.objects[j].label, "rider") == 0) {
                    /* 骑自行车、摩托的人则用紫色框（现已废弃） */
                    color = cv::Scalar(102,0,204,255);
                } else if (strcmp(curobjectInfo.objects[j].label, "car") == 0) {
                    /* 小轿车则用绿色框 */
                    color = cv::Scalar(0,255,0,255);
                } else if (strcmp(curobjectInfo.objects[j].label, "bus") == 0) {
                    /* 公交车则用黄色框 */
                    color = cv::Scalar(255,255,0,255);
                } else if (strcmp(curobjectInfo.objects[j].label, "truck") == 0) {
                    /* 货车则用蓝色框 */
                    color = cv::Scalar(0,0,255,255);
                } else if (strstr(curobjectInfo.objects[j].label, "A1-")) {
                    /* 一级报警区域内的则用红色框 */
                    color = cv::Scalar(255,0,0,255);
                } else if (strstr(curobjectInfo.objects[j].label, "A2-")) {
                    /* 二级报警区域内的则用红色框 */
                    color = cv::Scalar(255,0,0,255);
                }
                /* 把对角线的坐标填进去 */
                objectList[0].x = curobjectInfo.objects[j].mLeft;
                objectList[0].y = curobjectInfo.objects[j].mTop;
                objectList[1].x = curobjectInfo.objects[j].mRight;
                objectList[1].y = curobjectInfo.objects[j].mBottom;
                /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
                if (needMirror) {
                    objectList[0].x = (curobjectInfo.objects[j].mLeft >= 640) ? (640 -
                                                                                 (curobjectInfo.objects[j].mLeft - 640))
                                                                              : (640 + (640 -
                                                                                        curobjectInfo.objects[j].mLeft));
                    objectList[1].x = (curobjectInfo.objects[j].mRight >= 640) ? (640 -
                                                                                  (curobjectInfo.objects[j].mRight -
                                                                                   640)) : (640 + (640 -
                                                                                                   curobjectInfo.objects[j].mRight));
                }

                cv::rectangle(srcimg, cv::Point(objectList[0].x, objectList[0].y), cv::Point(objectList[1].x, objectList[1].y), color, 2);

//                cv::rectangle(srcimg, cv::Point(objectList[0].x, objectList[0].y), cv::Point(objectList[1].x, objectList[1].y), color);

            }

        }
    } catch (...) {

    }

}

void DispalayShower::drawAlarmAreaByRGBA(cv::Mat srcimg, YUVDataOpt_Point *alarmAreaPoints, cv::Scalar color, bool closed) {
    std::vector<cv::Point> polygonVertices;
    polygonVertices.push_back(cv::Point(alarmAreaPoints[0].x, alarmAreaPoints[0].y));
    polygonVertices.push_back(cv::Point(alarmAreaPoints[1].x, alarmAreaPoints[1].y));
    polygonVertices.push_back(cv::Point(alarmAreaPoints[2].x, alarmAreaPoints[2].y));
    polygonVertices.push_back(cv::Point(alarmAreaPoints[3].x, alarmAreaPoints[3].y));
    polygonVertices.push_back(cv::Point(alarmAreaPoints[4].x, alarmAreaPoints[4].y));
    polygonVertices.push_back(cv::Point(alarmAreaPoints[5].x, alarmAreaPoints[5].y));
    cv::polylines(srcimg, std::vector<std::vector<cv::Point>>{polygonVertices}, closed ? 1 : 0, color, 10);
}

void DispalayShower::switchForSignalDisplay() {
    /* 先获取下默认情况下应该显示什么 */
    int displayType = G3_Configuration::getInstance().getDisplaySwitchForSignalDefaultDisplayType();
    /* 检查下规则列表，看看最终应该显示什么 */
    int curPriority = 0;
    std::vector<DisplaySwitchForSignalRule> displaySwitchForSignalRuleList = G3_Configuration::getInstance().getDisplaySwitchForSignalRuleList();
    for(std::size_t i = 0; i < displaySwitchForSignalRuleList.size(); i ++){
//        printf("i=%d   signalType=%d  priority=%d   dispalyType=%d \n",i,displaySwitchForSignalRuleList[i].signalType,displaySwitchForSignalRuleList[i].priority,displaySwitchForSignalRuleList[i].dispalyType);
        /* 先看看优先级是不是大于现在正在使用的 */
        if(displaySwitchForSignalRuleList[i].priority > curPriority){
            /* 优先级比现在的高，那么判断信号是否On */
            bool isOn = false;
            switch (displaySwitchForSignalRuleList[i].signalType) {
                case DISPALY_SWITCH_SIGNALE_TYPE_TURN_LEFT:{
                    /* 检查是不是打了左转 */
                    isOn = curTurnL;
                }
                    break;
                case DISPALY_SWITCH_SIGNALE_TYPE_TURN_RIGHT:{
                    /* 检查是不是打了右转 */
                    isOn = curTurnR;
                }
                    break;
                case DISPALY_SWITCH_SIGNALE_TYPE_REVERSE:{
                    /* 检查是不是打了倒车 */
                    isOn = curReverse;
                }
                    break;
            }
            /* 优先级高了，还ON了，那么就需要切换成指定的显示方式 */
            if(isOn){
                displayType = displaySwitchForSignalRuleList[i].dispalyType;
                curPriority = displaySwitchForSignalRuleList[i].priority;
            }

        }else{
            /* 优先级没有现在在使用的大，那么就不管 */
            ; //not to do
        }
    }

    /* 遍历完规则了，现在需要决定最终的显示画面 */
    switch (displayType) {
        case DISPALY_SWITCH_DISPLAY_TYPE_ALL_SLICING:{
            /* 分屏显示,这里需要根据工作模式的不同确定分左右分还是四宫格 */
            switch (G3_Configuration::getInstance().getG3DeviceWorkingMode()) {
                case G3WORKMODE_G4MINI:{
                    /* G4-mini显示四宫格 */
                    showFourCamera();
                }
                    break;
                case G3WORKMODE_V6:{
                    /* V1只显示镜头1（V6选了这个就跟没选一样） */
                    showOneCamera(CAMERA_ID_1);
                }
                    break;
                default:{
                    /* 其他的全部左右分屏 */
                    showFourCamera();
                }
                    break;
            }
        }
            break;
        case DISPALY_SWITCH_DISPLAY_TYPE_FULL_CAMERA1:{
            /* 全屏显示镜头1 */
            showOneCamera(CAMERA_ID_1);
        }
            break;
        case DISPALY_SWITCH_DISPLAY_TYPE_FULL_CAMERA2:{
            /* 全屏显示镜头2 */
            showOneCamera(CAMERA_ID_2);
        }
            break;
        case DISPALY_SWITCH_DISPLAY_TYPE_FULL_CAMERA3:{
            /* 全屏显示镜头3 */
            showOneCamera(CAMERA_ID_3);
        }
            break;
        case DISPALY_SWITCH_DISPLAY_TYPE_FULL_CAMERA4:{
            /* 全屏显示镜头4 */
            showOneCamera(CAMERA_ID_4);
        }
            break;


    }


}

void DispalayShower::switchForAlarmEventDisplay() {
    G3_DISPALY_MODE curEventDispalyMode;
    /* 看看上一次的报警时间是否操作了两秒 */
    int lastAlarmInterval =
            XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - lastAlarmEventInfoFomrDispaly.alarmTime;
    if (lastAlarmInterval >= MAX_ALARM_CAMERA_SHOW_TIME_FOR_DISPLAY) {
        /* 持续时间超过两秒了，继续显示分屏,这里需要根据工作模式的不同确定分左右分还是四宫格 */
        switch (G3_Configuration::getInstance().getG3DeviceWorkingMode()) {
            case G3WORKMODE_G4MINI: {
                /* G4-mini显示四宫格 */
                curEventDispalyMode = G3_DISPALY_MODE_ALL_SLICING_4;
            }
                break;
            case G3WORKMODE_V6: {
                /* V1只显示镜头1（V6选了这个就跟没选一样） */
                curEventDispalyMode = G3_DISPALY_MODE_CAMERA1_FULL;
            }
                break;
            default: {
                /* 其他的全部左右分屏 */
                curEventDispalyMode = G3_DISPALY_MODE_ALL_SLICING_2;
            }
                break;
        }



    }else{
        /* 持续时间未达到两秒，那么就全屏显示这个镜头对应的画面 */
        switch (lastAlarmEventInfoFomrDispaly.alarmCameraId) {
            case CAMERA_ID_1:{
                curEventDispalyMode = G3_DISPALY_MODE_CAMERA1_FULL;
            }
                break;
            case CAMERA_ID_2:{
                curEventDispalyMode = G3_DISPALY_MODE_CAMERA2_FULL;
            }
                break;
            case CAMERA_ID_3:{
                curEventDispalyMode = G3_DISPALY_MODE_CAMERA3_FULL;
            }
                break;
            case CAMERA_ID_4:{
                curEventDispalyMode = G3_DISPALY_MODE_CAMERA4_FULL;
            }
                break;
        }
    }
    /* 根据显示模式直接显示对应画面 */
    switch (curEventDispalyMode) {
        case G3_DISPALY_MODE_ALL_SLICING_2: {
            /* 单纯分屏显示两个镜头的画面 */
            showTwoCamera();
        }
            break;
        case G3_DISPALY_MODE_CAMERA1_FULL: {
            /* 单纯全屏显示镜头1的画面 */
            showOneCamera(CAMERA_ID_1);
        }
            break;
        case G3_DISPALY_MODE_CAMERA2_FULL: {
            /* 单纯全屏显示镜头2的画面 */
            showOneCamera(CAMERA_ID_2);
        }
            break;
        case G3_DISPALY_MODE_ALL_SLICING_4: {
            /* 单纯分屏显示四个镜头的画面 */
            showFourCamera();
        }
            break;
        case G3_DISPALY_MODE_CAMERA3_FULL: {
            /* 单纯全屏显示镜头3的画面 */
            showOneCamera(CAMERA_ID_3);
        }
            break;
        case G3_DISPALY_MODE_CAMERA4_FULL: {
            /* 单纯全屏显示镜头4的画面 */
            showOneCamera(CAMERA_ID_4);
        }
            break;
        default:{
            /* 其他情况都只分屏显示两个镜头的画面 */
            showTwoCamera();
        }
            break;
    }



}

void DispalayShower::showFourCamera() {
    int ret = -1;
    int width = 1280;
    int height = 720;
    /* 先拿到全部的四张图片 */
    bool readImgRet = true;
    ret = getYUVData(showYUVData_camera1, CAMERA_ID_1,width,height);
    readImgRet &= (ret > 0);
    ret = getYUVData(showYUVData_camera2, CAMERA_ID_2,width,height);
    readImgRet &= (ret > 0);
    ret = getYUVData(showYUVData_camera3, CAMERA_ID_3,width,height);
    readImgRet &= (ret > 0);
    ret = getYUVData(showYUVData_camera4, CAMERA_ID_4,width,height);
    readImgRet &= (ret > 0);
    /* 读成功了才进行合成 */
    if (readImgRet) {
        if(showFourPalaceGridYUVData == nullptr){
            showFourPalaceGridYUVData = static_cast<uint8_t *>(malloc(width * height * 3));
        }
        ret = rgaUtils.synthesisImg_FourPalaceGrid(width, height, showYUVData_camera1, width, height, showYUVData_camera2,width, height, showYUVData_camera3, width, height, showYUVData_camera4, width, height, showFourPalaceGridYUVData,XuRGAUtils::IMG_TYPE_NV12);
        transferNV21YToUSB(showFourPalaceGridYUVData,width,height);
//        transferRGBAToUSB(rightImag,DISPLAY_W,DISPLAY_H);


////            /* 在这里对图像进行绘画 */
////            drawYUV(cameraYuvData_camera1);
//
//        ret = getYUVData(showYUVData_camera2, CAMERA_ID_2,width,height);
//        if (ret > 0) {
//            /* 先把左边这张转成RGB888的格式  并缩小到720 * 576  */
//
//            ret = rgaUtils.imageTransformation(yuvCache_camera1->getWidth(), yuvCache_camera1->getHeight(),
//                                               XuRGAUtils::IMG_TYPE_NV21, showYUVData_camera1,
//                                               DISPLAY_W, DISPLAY_H, XuRGAUtils::IMG_TYPE_RGBA8888, leftImag);
//            if (ret > 0) {
//                /* 再把右边这张转成RGB888的格式  并缩小到720 * 576  */
//                ret = rgaUtils.imageTransformation(yuvCache_camera2->getWidth(),
//                                                   yuvCache_camera2->getHeight(), XuRGAUtils::IMG_TYPE_NV21,
//                                                   showYUVData_camera2, DISPLAY_W, DISPLAY_H,
//                                                   XuRGAUtils::IMG_TYPE_RGBA8888, rightImag);
//                if (ret > 0) {
//                    /* 把两张图像合并一下 */
//                    ret = rgaUtils.synthesisImg_LR(DISPLAY_W, DISPLAY_H, leftImag, DISPLAY_W, DISPLAY_H, rightImag);
//                    if (ret > 0) {
//                        /* 输出到屏幕 */
//                        transferRGBAToUSB(rightImag,DISPLAY_W,DISPLAY_H);
//                    } else {
//                        //printf("DispalayShower synthesis Imag fialed!  ret=%d \n",ret);
//                    }
//
//                } else {
////                        printf("right image transformation failed!   ret = %d  \n",ret);
//                }
//            } else {
////                    printf("left image transformation failed!   ret = %d  \n",ret);
//            }
//        } else {
//            //printf("DispalayShower get camera2 YUVData fialed!  ret=%d \n",ret);
//
//        }

    } else {
        //printf("DispalayShower get camera1 YUVData fialed!  ret=%d \n",ret);

    };

}


