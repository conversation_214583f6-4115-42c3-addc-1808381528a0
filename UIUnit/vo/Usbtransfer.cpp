#include "Usbtransfer.h"


#define LOGI printf

int Usbtransfer::releaseInterface(long handle, int interface) {
    // release everything
    int ret;
    libusb_device_handle *dev_handle = (libusb_device_handle *) handle;

    if (dev_handle == nullptr) {
        LOGI("dev_handle is nullptr ptr\n");
        return -1;
    }
    ret = libusb_release_interface(dev_handle, 0);
    libusb_attach_kernel_driver(dev_handle, 0);
    LOGI("release interface ret = %d", ret);
    return ret;
}

int Usbtransfer::claimInterface(long handle, int indexInterface) {
    LOGI("claimInterface");
    int ret = 0;
    libusb_device_handle *dev_handle = (libusb_device_handle *) handle;

    if (dev_handle == nullptr) {
        LOGI("dev_handle is nullptr ptr\n");
        return -1;
    }

    // libusb_free_device_list(devs,1);
    if (libusb_kernel_driver_active(dev_handle, indexInterface) == 1) {
        LOGI("kernel driver active\n");
        if (libusb_detach_kernel_driver(dev_handle, indexInterface) == 0)
            LOGI("kernel driver detached\n");
    }
    ret = libusb_claim_interface(dev_handle, indexInterface);
    if (ret < 0) {
        LOGI("cannot claim interface\n");
        return -1;
    }
    LOGI("claimed interface ret = %d\n", ret);

    pthread_mutex_init(&control_transfer_lock, nullptr);

    return ret;
}

int Usbtransfer::usbBulkTransfer(long handle, int endpoint, unsigned char *buffer, int length, int timeout) {
    // LOGI("usbBulkTransfer");
    int ret = 0;
    int size = 0;
    libusb_device_handle *dev_handle = (libusb_device_handle *) handle;
    if (dev_handle == nullptr) {
        LOGI("******************************VO  dev_handle == nullptr**************************\n");
        ret = -1;
    } else {
        ret = libusb_bulk_transfer(dev_handle, endpoint, buffer, length, &size, timeout);
        // LOGI("usbBulkTransfer ret = %d, size = %d\n", ret, size);
    }


    return 0;
}

int Usbtransfer::usbControlTransfer(long handle, Usbtransfer::TRANSFER_INFO *p_transfer_info) {
    // LOGI("usbControlTransfer");
    pthread_mutex_lock(&control_transfer_lock);
    int ret = 0;
    libusb_device_handle *dev_handle = (libusb_device_handle *) handle;

    if (dev_handle == nullptr) {
        LOGI("usbControlTransfer dev_handle ptr is nullptr\n");
        ret = -1;
    } else {
        ret = libusb_control_transfer(dev_handle, p_transfer_info->requestType,
                                      p_transfer_info->request, p_transfer_info->value,
                                      p_transfer_info->index, p_transfer_info->buffer,
                                      p_transfer_info->length, p_transfer_info->timeout);

        if (ret < 0) {
            LOGI("transfer data error ret = %d\n", ret);
        } else {
            // LOGI("transfer data success ret = %d\n", ret);
        }
    }


    pthread_mutex_unlock(&control_transfer_lock);
    return ret;
}

unsigned char Usbtransfer::xdata_read(long handle, int addr) {
    unsigned char ctrlData[8] = {0};
    ctrlData[0] = HIDCMD_READ_XDATA; // 0xb5;
    ctrlData[1] = (unsigned char) ((addr >> 8) & 0x00ff);
    ctrlData[2] = (unsigned char) (addr & 0x00ff);
    ctrlData[3] = 0x00;
    ctrlData[4] = 0x00;
    ctrlData[5] = 0x00;
    ctrlData[6] = 0x00;
    ctrlData[7] = 0x00;
    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;
    int value = usbControlTransfer(handle, &t_transfer_info);

    t_transfer_info.requestType = 0xa1;
    t_transfer_info.request = 0x01;
    value = usbControlTransfer(handle, &t_transfer_info);
    LOGI("nativeControlTransfer read value:  ", value);
    return ctrlData[3];
}

int Usbtransfer::xdata_write(long handle, int addr, unsigned char xdata_value) {
    int ret = 0;
    int status = 0;
    unsigned char ctrlData[8] = {0};
    ctrlData[0] = (unsigned char) HIDCMD_WRITE_XDATA; // 0xb6;
    ctrlData[1] = (unsigned char) ((addr >> 8) & 0x00ff);
    ctrlData[2] = (unsigned char) (addr & 0x00ff);
    ctrlData[3] = xdata_value;
    if (addr == 0xF202 || addr == 0xF203) {
        ctrlData[4] = 0x01;
    } else
        ctrlData[4] = 0x00;

    ctrlData[5] = 0x00;
    ctrlData[6] = 0x00;
    ctrlData[7] = 0x00;

    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;

    ret = usbControlTransfer(handle, &t_transfer_info);
}

void Usbtransfer::frame_trigger(long handle, unsigned char fid, unsigned char delay) {
    int ret = 0;
    unsigned char ctrlData[8] = {0};
    ctrlData[0] = HIDCMD_SPECIAL; // 0xa6;
    ctrlData[1] = HIDCMD_SPECIAL_FRAMETRIGGER;
    ctrlData[2] = fid;
    ctrlData[3] = delay;
    // usb.usbhid_write(ctrlData, 8);

    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;

    ret = usbControlTransfer(handle, &t_transfer_info);
}

void Usbtransfer::frame_trigger_switch(long handle, unsigned char frame_index) {
    int ret = 0;
    unsigned char ctrlData[8] = {0};
    ctrlData[0] = 0x12;
    ctrlData[1] = 0xF2;
    ctrlData[2] = 0x02;
    ctrlData[3] = 0x00;
    ctrlData[4] = frame_index;
    ctrlData[5] = 0x00;
    ctrlData[6] = 0x00;
    ctrlData[7] = 0x00;

    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;
    // LOGI("frame_index = %d\n", frame_index);
    ret = usbControlTransfer(handle, &t_transfer_info);
}

void Usbtransfer::set_start_trans(long handle, Usbtransfer::byte start_flag) {
    int ret = 0;
    byte ctrlData[8] = {0};

    ctrlData[0] = HIDCMD_SPECIAL;
    ctrlData[1] = HIDCMD_SPECIAL_STARTTRANS;
    ctrlData[2] = start_flag;

    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;

    ret = usbControlTransfer(handle, &t_transfer_info);
}

void Usbtransfer::set_transfer_mode_frame(long handle) {
    int ret = 0;
    byte ctrlData[8] = {0};
    ctrlData[0] = HIDCMD_SPECIAL;
    ctrlData[1] = HIDCMD_SPECIAL_TRANSMODE;
    byte transfer_mode = 0;
    ctrlData[2] = transfer_mode;

    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;

    ret = usbControlTransfer(handle, &t_transfer_info);
}

void Usbtransfer::set_transfer_mode_manual_block(long handle) {

}

void Usbtransfer::set_transfer_mode_membypass(long handle) {

}

void Usbtransfer::set_transfer_mode_membypass_frame(long handle) {

}

void Usbtransfer::set_video_in(long handle, int width, int height, Usbtransfer::byte color, Usbtransfer::byte colSel) {
    int ret = 0;
    byte ctrlData[8] = {0};

    ctrlData[0] = HIDCMD_SPECIAL;
    ctrlData[1] = HIDCMD_SPECIAL_VIDEO_IN;
    ctrlData[2] = (byte) (width >> 8);
    ctrlData[3] = (byte) width;
    ctrlData[4] = (byte) (height >> 8);
    ctrlData[5] = (byte) height;
    ctrlData[6] = color;
    ctrlData[7] = colSel;

    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;

    ret = usbControlTransfer(handle, &t_transfer_info);
}

void Usbtransfer::set_video_out(long handle, Usbtransfer::byte timing_index, Usbtransfer::byte color, int width,
                                int height) {
    int ret = 0;
    byte ctrlData[8] = {0};

    ctrlData[0] = HIDCMD_SPECIAL;
    ctrlData[1] = HIDCMD_SPECIAL_VIDEO_OUT;
    ctrlData[2] = timing_index;
    ctrlData[3] = color;
    ctrlData[4] = (byte) (width >> 8);
    ctrlData[5] = (byte) width;
    ctrlData[6] = (byte) (height >> 8);
    ctrlData[7] = (byte) height;

    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;

    ret = usbControlTransfer(handle, &t_transfer_info);
}

void Usbtransfer::set_video_on(long handle, Usbtransfer::byte video_on_enable) {
    int ret = 0;
    byte ctrlData[8] = {0};
    ctrlData[0] = HIDCMD_SPECIAL;
    ctrlData[1] = HIDCMD_SPECIAL_VIDEOON;
    ctrlData[2] = video_on_enable;

    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;

    ret = usbControlTransfer(handle, &t_transfer_info);
}

void Usbtransfer::set_power_on(long handle, Usbtransfer::byte power_on_enable) {
    int ret = 0;
    byte ctrlData[8] = {0};
    ctrlData[0] = HIDCMD_SPECIAL;
    ctrlData[1] = HIDCMD_SPECIAL_POWERON;
    ctrlData[2] = power_on_enable;
    if (power_on_enable == 1) {
        ctrlData[3] = 2;
    } else {
        ctrlData[3] = 0;
    }

    TRANSFER_INFO t_transfer_info = {0};
    t_transfer_info.indexInterface = 0;
    t_transfer_info.requestType = (int) 0x21;
    t_transfer_info.request = (int) 0x09;
    t_transfer_info.value = (int) 0x0300;
    t_transfer_info.index = (int) 0;
    t_transfer_info.buffer = ctrlData;
    t_transfer_info.length = 8;
    t_transfer_info.timeout = 200;

    ret = usbControlTransfer(handle, &t_transfer_info);
}

int Usbtransfer::usbControlTransferStandard(long handle, Usbtransfer::TRANSFER_INFO transfer_info) {
    LOGI("usbControlTransfer");
    int ret = 0;
    libusb_device_handle *dev_handle = (libusb_device_handle *) handle;

    ret = libusb_control_transfer(dev_handle, transfer_info.requestType, transfer_info.request,
                                  transfer_info.value, transfer_info.index, transfer_info.buffer,
                                  transfer_info.length, 1000);
    // ret = libusb_control_transfer(dev_handle, 0xa1, 0x01, 0x0300, 0x0004, dataRead, 40, 1000);
    if (ret < 0) {
        LOGI("transfer stard data error ret = %d\n", ret);
    } else {
        LOGI("transfer stard data success ret = %d\n", ret);
    }

    return 0;
}
