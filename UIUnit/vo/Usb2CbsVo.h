//
// Created by xwf on 2021/12/13.
//

#ifndef VISPECT_VO_VO_H
#define VISPECT_VO_VO_H

#include <libusb-1.0/libusb.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include "ms2160.h"
#include "Usbtransfer.h"
#include "G3_Configuration.h"

class Usb2CbsVo {
public:

    int voInit(const int imgWidth,const int imgHeight,const int outputSignal);

    int voFree();

    int voSetFmt(unsigned char *yuvBuffer);

    void start_transaction();

    void print_devs(libusb_device **devs);

    void colorspaceTransfer(int width, int height, unsigned char *src, unsigned char *dst);

    bool wait_hid_done(long handle, int addr);


private:
    libusb_device_handle *handle = nullptr;
    int mVideoDisplayVIC = VFMT_CEA_17_720x576P_50HZ;


    int mVideoInColorSpace = YUV422;

    int mVieonInMEMColorSpace = YUV422;

    int mVideoDisplayColorSpace = YUV422;

    int mTransferMode = FRAME;

    struct RESOLUTION mVideoInResolution = {0};
    libusb_device **devs = nullptr;

    Usbtransfer usbtransfer;

    int inputWidth = 720;
    int inputHeight = 570;
};


#endif // VISPECT_VO_VO_H
