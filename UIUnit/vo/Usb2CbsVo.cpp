/*
 * libusb example program to list devices on the bus
 * Copyright © 2007 <PERSON> <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */


#include "Usb2CbsVo.h"
#include "XuLog.h"
#include "CodeUtils.h"
#include "XuString.h"


#define HID_DONE_POWER_ON   0XC454
#define HID_DONE_VIDEO_IN   0XC555
#define HID_DONE_TRANS_MODE 0XC558
#define HID_DONE_VIDEO_ON   0XC556
#define HID_DONE_VIDEO_OUT  0XC557




int Usb2CbsVo::voInit(const int imgWidth,const int imgHeight,const int outputSignal) {
    int r;
    ssize_t cnt;
    /* 根据不同的输出信号类型选择不同的时序 */
    switch (outputSignal) {
        case DISPLAY_SIGNAL_TYPE_CVBS_PAL:{
            mVideoDisplayVIC = VFMT_CEA_17_720x576P_50HZ;
            mVideoInResolution.framerate = 50;
        }
            break;
        case DISPLAY_SIGNAL_TYPE_CVBS_NTSC:{
            mVideoDisplayVIC = VFMT_CEA_02_720x480P_60HZ;
            mVideoInResolution.framerate = 60;
        }
            break;
        case DISPLAY_SIGNAL_TYPE_AHD:{
            mVideoDisplayVIC = VFMT_VESA_158_1280X720;
            mVideoInResolution.framerate = 25;
        }
            break;
        default:{
            mVideoDisplayVIC = VFMT_CEA_17_720x576P_50HZ;
            mVideoInResolution.framerate = 50;
        }
            break;
    }

    inputWidth = imgWidth;
    inputHeight = imgHeight;

    r = libusb_init(nullptr);
    if (r < 0)
        return r;

    cnt = libusb_get_device_list(nullptr, &devs);
    if (cnt < 0)
        return (int) cnt;

    print_devs(devs);

    // unsigned char *pictureBuffer = (unsigned char *)malloc(720 * 576 * 3);

    // unsigned char *yuvBuffer = (unsigned char *)malloc(720 * 576 * 2);

    mVideoInResolution.width = inputWidth;
    mVideoInResolution.height = inputHeight;



    usbtransfer.claimInterface((long) handle, 0);

    usbtransfer.set_start_trans((long) handle, 0x00);

    usbtransfer.set_power_on((long) handle, (byte) 0x01);
    if(wait_hid_done((long) handle,HID_DONE_POWER_ON)){
        start_transaction();
        usbtransfer.set_video_on((long) handle, (byte) 0x01);
        wait_hid_done((long) handle,HID_DONE_VIDEO_ON);
        return 0;
    }





    return 1;
}

int Usb2CbsVo::voFree() {
    usbtransfer.releaseInterface((long) handle, 0);
    libusb_free_device_list(devs, 1);

    libusb_exit(nullptr);

    return 0;
}

int Usb2CbsVo::voSetFmt(unsigned char *yuvBuffer) {
    static int frame_id = 0;
    usbtransfer.usbBulkTransfer((long) handle, 4, yuvBuffer, inputWidth * inputHeight * 2, 300);
    usbtransfer.usbBulkTransfer((long)handle,4,yuvBuffer,0,300);
    frame_id = frame_id == 0x00 ? 0x01 : 0x00;
    // m_winLibusb->frame_trigger(frame_id, 0);
    usbtransfer.frame_trigger_switch((long) handle, frame_id);
    int ret = usbtransfer.xdata_write((long) handle, 0xF202, (unsigned char) 0x01);
    return ret;
}

void Usb2CbsVo::start_transaction() {
    byte color = 0;
    if (mTransferMode == FRAME) {
        usbtransfer.set_transfer_mode_frame((long) handle);
        wait_hid_done((long) handle,HID_DONE_TRANS_MODE);
        color = (byte) (((byte) mVieonInMEMColorSpace << 4) | (byte) mVideoInColorSpace);
    }

    int width = (mVideoInResolution.width + 3) & ~3;
    usbtransfer.set_video_in((long) handle, width, mVideoInResolution.height, color, 0x00);
    wait_hid_done((long) handle,HID_DONE_VIDEO_IN);
    usbtransfer.set_video_out((long) handle, mVideoDisplayVIC, mVideoDisplayColorSpace, mVideoInResolution.width,
                              mVideoInResolution.height);
    wait_hid_done((long) handle,HID_DONE_VIDEO_OUT);
    // 1.向下位机发送开始传输HID指令
    usbtransfer.set_start_trans((long) handle, 0x01);
}

void Usb2CbsVo::print_devs(libusb_device **devs) {
    libusb_device *dev;
    int i = 0, j = 0;
    uint8_t path[8];

    while ((dev = devs[i++]) != nullptr) {
        struct libusb_device_descriptor desc;
        int r = libusb_get_device_descriptor(dev, &desc);
        if (r < 0) {
            fprintf(stderr, "failed to get device descriptor");
            return;
        }


        if ((desc.idVendor == 0x534D && desc.idProduct == 0x6021)
            || (desc.idVendor == 0x345F && desc.idProduct == 0x9132)) {
            handle = 0;
            libusb_open(dev, &handle);
        }

        r = libusb_get_port_numbers(dev, path, sizeof(path));
        if (r > 0) {
            printf(" path: %d", path[0]);
            for (j = 1; j < r; j++)
                printf(".%d", path[j]);
        }
        printf("\n");
    }
}

void Usb2CbsVo::colorspaceTransfer(int width, int height, unsigned char *src, unsigned char *dst) {

    bool ifgetU = true;
    unsigned char *p1 = (unsigned char *) src;
    unsigned char *p0 = (unsigned char *) dst;
    int temp = 0;

    for (int i = 0; i < height; i++) {
        for (int j = 0; j < width; j++) {
            if (ifgetU) {
                temp = (int) (-0.148 * (*(p1 + 2)) - 0.291 * (*(p1 + 1)) + 0.439 * (*p1) + 128); // U
                *p0 = (unsigned char) ((temp < 0) ? 0 : ((temp > 255) ? 255 : temp));
                ifgetU = false;
            } else {
                temp = (int) (0.439 * (*(p1 + 2)) - 0.368 * (*(p1 + 1)) - 0.071 * (*p1) + 128); // V
                *p0 = (unsigned char) ((temp < 0) ? 0 : ((temp > 255) ? 255 : temp));
                ifgetU = true;
            }

            temp = (int) (0.257 * (*(p1 + 2)) + 0.504 * (*(p1 + 1)) + 0.098 * (*p1) + 16); // Y
            *(p0 + 1) = (unsigned char) ((temp < 0) ? 0 : ((temp > 255) ? 255 : temp));

            p1 += 3;
            p0 += 2;
        }
    }
}


bool Usb2CbsVo::wait_hid_done(long handle, int addr) {
    int count = 0;
    bool ret = false;
    while (1)
    {
        if (usbtransfer.xdata_read(handle, addr) != 0) {
            usleep(10000);
            count++;
            if(count >= 10){
                printf("addr=%d  failed! \n",addr);
                //vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_FATAL,"Usb2CbsVo") << "addr=" << addr << "   failed!" << XU_LOG_END;
                break;
            }
        } else {
            printf("addr=%d  done! \n",addr);
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_FATAL,"Usb2CbsVo") << "addr=" << addr << "   done!" << XU_LOG_END;
            ret = true;
            break;
        }
    }

    return ret;
}
