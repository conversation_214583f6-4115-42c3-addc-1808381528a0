//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/21.
//

#include <cstdio>
#include <sys/syslog.h>
#include <boost/format.hpp>
#include "MainUnitManagr.h"
#include "G3RS485AlarmBroadcast.h"
#include "G3RS485BoradcastInfo_BSD.h"
#include "G3RS485BoradcastInfo_SystemInfo.h"
#include "G3RS485MessageEncoder.h"
#include "G3_Configuration.h"
#include "XuTimeUtil.h"
#include "XuFile.h"
#include "XuString.h"
#include "Log.h"
#include "XuLog.h"


namespace vis {
    int MainUnitManagr::start() {
        /* 先读下配置表 如果配置表正确，才允许接着往下走 */
        if (G3_Configuration::getInstance().loadFromFile() == 0) {

            setenv("TZ", G3_Configuration::getInstance().getLocalTimeZone().c_str(), 1);

            /* 判断是否挂载了TF卡 */
            if (XuFile::getInstance().hasMount("/mnt/sdcard")) {
                /* 挂载了TF，那么就把标志设置为true */
                hasTFcard = true;
            } else {
                /* 没挂载TF卡，那么就判断下是不是V6模式或者G4MINI模式 */
                if((G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_V6) || (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4MINI)){
                    /* 没挂载TF卡 但是处于V6模式或者G4MINI模式,那么就算有文件存储器，但是需要改下存储器路径 */
                    G3_Configuration::getInstance().setCurFileStorageRoot(EMMC_ROOT_PATH);
                    hasTFcard = true;
                }else{
                    /* 没挂载TF卡又不是V6模式或者G4MINI模式,那么就算没有文件存储器 */
                    hasTFcard = false;
                }
            }
            G3_Configuration::getInstance().setHasTfCard(hasTFcard);

            /* 获取下功能锁的状态 */
            getFunctionLockInfo();
            /* 获取一下镜头输入类型的过滤器 */
            getCameraInputTypeFilterInfo();
            /* 获取一下镜头降效信息 */
            getCameraReduceEffectInfo();

            communicationUnitManager.setInterface(*this);
            communicationUnitManager.start();

            peripheralUnitManager.setInterface(*this);
            peripheralUnitManager.start();


            /* XS508 has been removed, skip initialization check */
            detectUnitManager.init(*this);
            detectUnitManager.start();


            uiUnitManager.start();


            /* 如果需要加密多媒体文件，那么获取一下加密使用的密钥的信息 */
            getMultimediaFilesEncryptKey();


            mediaUnitManagr.setInterface(*this);
            mediaUnitManagr.start();

            unitStatusListener.init(*this, 1000 * 5, peripheralUnitManager, mediaUnitManagr, detectUnitManager,
                                    communicationUnitManager, uiUnitManager);
            if (threadPool.available() > 0) {
                threadPool.start(unitStatusListener);
            }

            /* 保存下启动时间 */
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG, "MainUnitManager") << "start softward!"
                                                                                          << XU_LOG_END;





            /* 读一下CPU的序列号 */
            uint8_t *buf = static_cast<uint8_t *>(malloc(1024 * 10));
            memset(buf, 0x00, 1024 * 10);
            int strLen = XuFile::getInstance().readFile("/proc/cpuinfo", buf, 1024 * 10);
            if (strLen > 0) {
                std::string cpuinfoStr;
                cpuinfoStr.append(reinterpret_cast<const char *>(buf));
                if (XuString::getInstance().split(cpuinfoStr, "Serial").size() == 2) {
                    if (XuString::getInstance().split(XuString::getInstance().split(cpuinfoStr, "Serial")[1],
                                                      ":").size() ==
                        2) {
                        /* 把CPU序列好写进去 */
                        memcpy(cpuSerialStr,
                               XuString::getInstance().split(XuString::getInstance().split(cpuinfoStr, "Serial")[1],
                                                             ":")[1].c_str(),
                               XuString::getInstance().split(XuString::getInstance().split(cpuinfoStr, "Serial")[1],
                                                             ":")[1].size());
                        mediaUnitManagr.setCPUSerial(cpuSerialStr, sizeof(cpuSerialStr));
                        std::string cpuSerialNumberStr;
                        cpuSerialNumberStr.append(cpuSerialStr + 1, strlen(cpuSerialStr) - 2);
                        G3_Configuration::getInstance().setCpuSerialNumber(cpuSerialNumberStr);
                    }
                }
            }
            free(buf);


            /* 设置G3软件版本号 */
            mediaUnitManagr.setG3SoftwardVersion(getenv("G3_SOFTWARD_VERSION_NAME"),
                                                 strlen(getenv("G3_SOFTWARD_VERSION_NAME")));


            if (XuFile::getInstance().fileExists("/mnt/sdcard/visptm.txt") || XuFile::getInstance().fileExists("/tmp/visptm.txt")) {
                isVISPtm = true;
                G3_Configuration::getInstance().setVisPtmModel(true);
            } else {
                isVISPtm = false;
                G3_Configuration::getInstance().setVisPtmModel(false);
            }



            /* 有没有存储空间 */
            if (hasTFcard) {
                /* 有TF或者是V6模式下，那么就获取下视频文件 */
                getDvrMp4FileList();
                getWarMp4FileList();
            }


            /* 获取下UUID */
            uint8_t readBufUuid[100] = {0x00};
            int readLen = XuFile::getInstance().readFile(G3_Configuration::getInstance().UUID_FILE_PATH,readBufUuid, sizeof(readBufUuid));
            if (readLen > 0) {
                std::string deviceUUID = "";
                deviceUUID.append(reinterpret_cast<const char *>(readBufUuid), readLen);
                G3_Configuration::getInstance().setDeviceUuid(deviceUUID);
                /* 获取下这台设备的调试模式密码 */
                uint8_t bcduuid[6] = {0x00};
                int bcdLen = XuString::getInstance().string2Bcd(
                        G3_Configuration::getInstance().getDeviceUuid().substr(4, 7), bcduuid);

                memcpy(myPwd, bcduuid, bcdLen);
                myPwd[6] = 0x69;
                myPwd[7] = 0x69;
            } else {
                /* 使用默认的调试密码 */
                myPwd[0] = 0x18;
                myPwd[1] = 0x28;
                myPwd[2] = 0x38;
                myPwd[3] = 0x48;
                myPwd[4] = 0x58;
                myPwd[5] = 0x68;
                myPwd[6] = 0x78;
                myPwd[7] = 0x88;
            }


            /* 先设置一下默认车速 */
            detectUnitManager.setSpeed(G3_Configuration::getInstance().getDefaultSpeed(), -1);
            mediaUnitManagr.setSpeed(G3_Configuration::getInstance().getDefaultSpeed(), -1);
            uiUnitManager.setSpeed(G3_Configuration::getInstance().getDefaultSpeed(), -1);
            communicationUnitManager.setSpeed(G3_Configuration::getInstance().getDefaultSpeed(), -1);

            while (true) {
                usleep(40 * 1000 * 1000);
                /* 在这里恢复出厂设置的APP */
                XuShell::getInstance().stopResetApp();
            }

        } else {
            printf("load config file failed! \n");
            // 一秒闪一次LED灯，表示是配置表问题
            while (true) {
                /* 闪一下那个LED灯 */
                XuShell::getInstance().runShellWithTimeout(
                        "echo 0 > /sys/devices/platform/leds/leds/system_led/brightness");
                usleep(200 * 1000);
                XuShell::getInstance().runShellWithTimeout(
                        "echo 1 > /sys/devices/platform/leds/leds/system_led/brightness");
                sleep(1);
            }
        }


        return 0;
    }

    int MainUnitManagr::onCameraYUVGet(CameraYUVData &cameraData) {
//    printf("onCameraYUVGet be call \n");
        /* 在非高温的情况下才给算法图片 */
        if (!isHighTemperature) {
            detectUnitManager.setYUVDataToDetect(cameraData);
        }
//        /* 存一下YUV图片 */
//        if(cameraData.getCameraId() == CAMERA_ID_1 && hasTFcard){
//            std::string fileNale = "/mnt/sdcard/";
//            fileNale.append(std::to_string(XuTimeUtil::getInstance().currentTimeMillis()));
//            fileNale.append(".yuv");
//            FILE *fd = fopen(fileNale.c_str(), "wb");
//            /* 如果文件打开成功才进行读写 */
//            if (fd) {
//                int ret = 0;
//                int writeLen = 0;
//                while (1) {
//                    writeLen = fwrite(cameraData.getCurYuvData() + writeLen, 1, cameraData.getDataLen() - writeLen, fd);
//                    if (writeLen > 0) {
//                        ret += writeLen;
//                        /* 看看写完了吗  写完了才可以退出 */
//                        if (ret >= cameraData.getDataLen()) {
//                            break;
//                        }
//                    }
//                }
//                fflush(fd);
//                fclose(fd);
//            }
//
//        }

        return 0;
    }

    int MainUnitManagr::onCameraH264Get(const CameraH264Data &cameraH264Data) {
//    printf("onCameraH264Get be call isStartRealViewMode_camera1=%d   isStartRealViewMode_camera2=%d  cameraH264Data.cameraId=%d   \n",isStartRealViewMode_camera1,isStartRealViewMode_camera2,cameraH264Data.getCameraId());
        switch (cameraH264Data.getCameraId()) {
            case CAMERA_ID_1: {
                /* 看看是不是要传输UDP实景数据 */
                if (isStartUDPRealViewMode_camera1) {
                    peripheralUnitManager.sendH264DataToUDP(cameraH264Data.getCameraId(),
                                                            cameraH264Data.getCurH264Data(),
                                                            cameraH264Data.getDataLen());
                }
                /* 看看是不是要传输TCP实景数据 */
                if(isStartTCPRealViewMode_camera1){
                    peripheralUnitManager.sendH264DataToTCP(cameraH264Data.getCameraId(),
                                                            cameraH264Data.getCurH264Data(),
                                                            cameraH264Data.getDataLen());
                }
            }
                break;
            case CAMERA_ID_2: {
                /* 看看是不是要传输UDP实景数据 */
                if (isStartUDPRealViewMode_camera2) {
                    peripheralUnitManager.sendH264DataToUDP(cameraH264Data.getCameraId(),
                                                            cameraH264Data.getCurH264Data(),
                                                            cameraH264Data.getDataLen());
                }
                /* 看看是不是要传输TCP实景数据 */
                if(isStartTCPRealViewMode_camera2){
                    peripheralUnitManager.sendH264DataToTCP(cameraH264Data.getCameraId(),
                                                            cameraH264Data.getCurH264Data(),
                                                            cameraH264Data.getDataLen());
                }
            }
                break;
            case CAMERA_ID_3: {
                /* 看看是不是要传输UDP实景数据 */
                if (isStartUDPRealViewMode_camera3) {
                    peripheralUnitManager.sendH264DataToUDP(cameraH264Data.getCameraId(),
                                                            cameraH264Data.getCurH264Data(),
                                                            cameraH264Data.getDataLen());
                }
                /* 看看是不是要传输TCP实景数据 */
                if(isStartTCPRealViewMode_camera3){
                    peripheralUnitManager.sendH264DataToTCP(cameraH264Data.getCameraId(),
                                                            cameraH264Data.getCurH264Data(),
                                                            cameraH264Data.getDataLen());
                }
            }
                break;
            case CAMERA_ID_4: {
                /* 看看是不是要传输UDP实景数据 */
                if (isStartUDPRealViewMode_camera4) {
                    peripheralUnitManager.sendH264DataToUDP(cameraH264Data.getCameraId(),
                                                            cameraH264Data.getCurH264Data(),
                                                            cameraH264Data.getDataLen());
                }
                /* 看看是不是要传输TCP实景数据 */
                if(isStartTCPRealViewMode_camera4){
                    peripheralUnitManager.sendH264DataToTCP(cameraH264Data.getCameraId(),
                                                            cameraH264Data.getCurH264Data(),
                                                            cameraH264Data.getDataLen());
                }
            }
                break;

        }
        /* 尝试推到RTMP 这里就不做工作模式判断了 */
        peripheralUnitManager.sendH264DataToRTMP(cameraH264Data.getCameraId(),
                                                 cameraH264Data.getCurH264Data(),
                                                 cameraH264Data.getDataLen());

        return 0;
    }

    int MainUnitManagr::onMp4FileGet(const Mp4FileInfo &mp4FileInfo) {
//        printf("===========================MainUnitManagr::onMp4FileGet   path:%s  \n",mp4FileInfo);
        /* 有新的MP4文件产生，那么更新一下文件列表 */
        if (hasTFcard) {
            getDvrMp4FileList();
            getWarMp4FileList();
            /* 如果不是DVR，就告诉通信模块 */
            if (mp4FileInfo.getFileType() != EVENT_UNKNOW) {
                communicationUnitManager.setNewMp4File(mp4FileInfo);
            }
        }
        return 0;
    }

    MainUnitManagr::MainUnitManagr() : threadPool(1, 2), mp4FileDownthreadPool(1, 3), logFileDownthreadPool(1, 1) {

    }

    MainUnitManagr::~MainUnitManagr() {

    }

    void MainUnitManagr::onGetDataFromSocket(const char *ip, const int port, const uint8_t *buf, int len) {
        communicationUnitManager.setSocketDataToConfigure(ip, port, buf, len);
//        /* 打印一下看看 */
//        std::string logContent = "onGetDataFromSocket:";
//        logContent.append("ip=");
//        logContent.append(ip);
//        logContent.append("  port="+std::to_string(port));
//        printf("buflen=%d \n",len);
//        logContent.append("  content=");
//        logContent.append(XuString::getInstance().byteArrayToString(const_cast<uint8_t *>(buf), len).c_str());
//        XuLog::getInstance().debug("MainUnitManager",logContent);
    }


    void MainUnitManagr::onGetDataFromRS232(const uint8_t *buf, int len) {
        communicationUnitManager.setRS232BusData(buf, len);
    }

    void MainUnitManagr::onGetDataFromRS485(const uint8_t *buf, int len) {
        communicationUnitManager.setRS485BusData(buf, len);
    }

    void MainUnitManagr::onGetDataFromCANBus(const can_frame &canFrame) {
        communicationUnitManager.setCANBusData(canFrame);
    }

    void MainUnitManagr::onGPIOValueChange(const int portNum, const char *value, const int valueLen) {

//        printf("************************portNum=%d  value=%s   valueLen=%d    \n",portNum,value,valueLen);

        /* 更新一下 */
        if (portNum == IO_IN_3_NUM) {
            io_level_110 = (std::atoi(value) == 1 ? 0 : 1);
            mediaUnitManagr.setIoValue110(io_level_110);
        } else if (portNum == IO_IN_2_NUM) {
            io_level_111 = (std::atoi(value) == 1 ? 0 : 1);
            mediaUnitManagr.setIoValue111(io_level_111);
        } else if (portNum == IO_IN_UNKNOW_NUM) {
            io_level_113 = (std::atoi(value) == 1 ? 0 : 1);
            mediaUnitManagr.setIoValue113(io_level_113);
        } else if (portNum == IO_IN_1_NUM) {
            io_level_115 = (std::atoi(value) == 1 ? 0 : 1);
            mediaUnitManagr.setIoValue115(io_level_115);
        }

        /* 如果没有值  那么就设置  如果有则判断是否需要设置IP */
        if (io_level_110 < 0 || io_level_111 < 0 || io_level_113 < 0 || io_level_115 < 0) {
            ; //not to do
        } else {
            if (io_level_110 >= 0 && io_level_111 >= 0 && !isSetNetworkDone) {
                switch (G3_Configuration::getInstance().getG3DeviceWorkingMode()) {
                    case G3WORKMODE_G4_UNIT: {
                        /* 根据槽位设置IP  槽位号   UDP端口 */
                        if (io_level_110 == 1 && io_level_111 == 1) {
                            setStaticIPAddress(G3_IP_SLOT_POSITION_1);


                            G3_Configuration::getInstance().setSlotPosition(1);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA1_PORT_SLOT_POSITION_1);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA2_PORT_SLOT_POSITION_1);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA3_PORT_SLOT_POSITION_1);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA4_PORT_SLOT_POSITION_1);
                            peripheralUnitManager.startUPDBroadcast(G3_UDP_CAMERA1_PORT_SLOT_POSITION_1,G3_UDP_CAMERA2_PORT_SLOT_POSITION_1,G3_UDP_CAMERA3_PORT_SLOT_POSITION_1,G3_UDP_CAMERA4_PORT_SLOT_POSITION_1);


                            printf("***************setIP to 189!\n");


                        } else if (io_level_110 == 0 && io_level_111 == 0) {
                            setStaticIPAddress(G3_IP_SLOT_POSITION_2);

                            G3_Configuration::getInstance().setSlotPosition(2);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA1_PORT_SLOT_POSITION_2);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA2_PORT_SLOT_POSITION_2);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA3_PORT_SLOT_POSITION_2);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA4_PORT_SLOT_POSITION_2);
                            peripheralUnitManager.startUPDBroadcast(G3_UDP_CAMERA1_PORT_SLOT_POSITION_2,G3_UDP_CAMERA2_PORT_SLOT_POSITION_2,G3_UDP_CAMERA3_PORT_SLOT_POSITION_2,G3_UDP_CAMERA4_PORT_SLOT_POSITION_2);
                            printf("***************setIP to 188!\n");

                        }
                        isSetNetworkDone = true;

                        printf("***************other IP set!  io_level_110=%d   io_level_111=%d \n", io_level_110,
                               io_level_111);

                    }
                        break;

                    case G3WORKMODE_G3S: {
                        setStaticIPAddress(G3_Configuration::getInstance().getMrv220IpAdress().c_str());

                        isSetNetworkDone = true;
                        peripheralUnitManager.startUPDBroadcast(G3_Configuration::getInstance().getCam1RealviewPort(),G3_Configuration::getInstance().getCam2RealviewPort(),G3_Configuration::getInstance().getCam3RealviewPort(),G3_Configuration::getInstance().getCam4RealviewPort());
                    }
                        break;
                    case G3WORKMODE_G4MT_UNIT: {
                        /* IP地址使用配置表的 */
                        setStaticIPAddress(G3_Configuration::getInstance().getMrv220IpAdress().c_str());
                        /* 根据槽位设置IP  槽位号   UDP端口 */
                        if (io_level_110 == 1 && io_level_111 == 1) {
                            G3_Configuration::getInstance().setSlotPosition(1);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA1_PORT_SLOT_POSITION_1);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA2_PORT_SLOT_POSITION_1);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA3_PORT_SLOT_POSITION_1);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA4_PORT_SLOT_POSITION_1);
                        } else if (io_level_110 == 0 && io_level_111 == 0) {
                            G3_Configuration::getInstance().setSlotPosition(2);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA1_PORT_SLOT_POSITION_2);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA2_PORT_SLOT_POSITION_2);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA3_PORT_SLOT_POSITION_2);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA4_PORT_SLOT_POSITION_2);
                        }
                        isSetNetworkDone = true;

                    }
                        break;

                    case G3WORKMODE_G4GJ_UNIT_G3: {
                        setStaticIPAddress(G3_Configuration::getInstance().getMrv220IpAdress().c_str());
                        isSetNetworkDone = true;
                        peripheralUnitManager.startUPDBroadcast(G3_Configuration::getInstance().getCam1RealviewPort(),G3_Configuration::getInstance().getCam2RealviewPort(),G3_Configuration::getInstance().getCam3RealviewPort(),G3_Configuration::getInstance().getCam4RealviewPort());
                    }
                        break;

                    case G3WORKMODE_G4GJ_G4_UNIT: {
                        setStaticIPAddress(G3_Configuration::getInstance().getMrv220IpAdress().c_str());
                        isSetNetworkDone = true;
                        peripheralUnitManager.startUPDBroadcast(G3_Configuration::getInstance().getCam1RealviewPort(),G3_Configuration::getInstance().getCam2RealviewPort(),G3_Configuration::getInstance().getCam3RealviewPort(),G3_Configuration::getInstance().getCam4RealviewPort());
                    }
                        break;

                    case G3WORKMODE_V6: {
                        setStaticIPAddress(G3_Configuration::getInstance().getMrv220IpAdress().c_str());
                        isSetNetworkDone = true;
                        peripheralUnitManager.startUPDBroadcast(G3_Configuration::getInstance().getCam1RealviewPort(),
                                                                G3_Configuration::getInstance().getCam2RealviewPort(),
                                                                G3_Configuration::getInstance().getCam3RealviewPort(),
                                                                G3_Configuration::getInstance().getCam4RealviewPort());
                    }
                        break;

                    case G3WORKMODE_G4MINI: {
                        setStaticIPAddress(G3_Configuration::getInstance().getMrv220IpAdress().c_str());
                        isSetNetworkDone = true;
                        peripheralUnitManager.startUPDBroadcast(G3_Configuration::getInstance().getCam1RealviewPort(),G3_Configuration::getInstance().getCam2RealviewPort(),G3_Configuration::getInstance().getCam3RealviewPort(),G3_Configuration::getInstance().getCam4RealviewPort());
                    }
                        break;

                    default: {
                        /* 根据槽位设置IP  槽位号   UDP端口 */
                        if (io_level_110 == 1 && io_level_111 == 1) {
                            setStaticIPAddress(G3_IP_SLOT_POSITION_1);

                            G3_Configuration::getInstance().setSlotPosition(1);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA1_PORT_SLOT_POSITION_1);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA2_PORT_SLOT_POSITION_1);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA3_PORT_SLOT_POSITION_1);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA4_PORT_SLOT_POSITION_1);
                            peripheralUnitManager.startUPDBroadcast(G3_UDP_CAMERA1_PORT_SLOT_POSITION_1, G3_UDP_CAMERA2_PORT_SLOT_POSITION_1, G3_UDP_CAMERA3_PORT_SLOT_POSITION_1, G3_UDP_CAMERA4_PORT_SLOT_POSITION_1);


                            printf("***************setIP to 189!\n");


                        } else if (io_level_110 == 0 && io_level_111 == 0) {
                            setStaticIPAddress(G3_IP_SLOT_POSITION_2);

                            G3_Configuration::getInstance().setSlotPosition(2);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA1_PORT_SLOT_POSITION_2);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA2_PORT_SLOT_POSITION_2);
                            G3_Configuration::getInstance().setCam1RealviewPort(G3_UDP_CAMERA3_PORT_SLOT_POSITION_2);
                            G3_Configuration::getInstance().setCam2RealviewPort(G3_UDP_CAMERA4_PORT_SLOT_POSITION_2);
                            peripheralUnitManager.startUPDBroadcast(G3_UDP_CAMERA1_PORT_SLOT_POSITION_2, G3_UDP_CAMERA2_PORT_SLOT_POSITION_2, G3_UDP_CAMERA3_PORT_SLOT_POSITION_2, G3_UDP_CAMERA4_PORT_SLOT_POSITION_2);
                            printf("***************setIP to 188!\n");

                        }
                        isSetNetworkDone = true;

                        printf("***************other IP set!  io_level_110=%d   io_level_111=%d \n", io_level_110,
                               io_level_111);
                    }
                        break;

                }


            }
        }

        communicationUnitManager.setIOStatus(portNum, value, valueLen);
//        peripheralUnitManager.getGpioManager()



        /* 如果是作为独立G3的工作模式，那么从这里取一下工程机械操作杆状态和镜头报警开关状态 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G3S ||
            G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4GJ_UNIT_G3 ||
            G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4MINI) {
            /* 先看看是不是有工程机械操作杆的状态 */
            bool machineryActionBarsOn = false;
            if (G3_Configuration::getInstance().getGpioIn1Type() == IO_TYPE_IN_MACHINERY_ACTION_BARS) {
                machineryActionBarsOn = machineryActionBarsOn | io_level_115;
            }
            if (G3_Configuration::getInstance().getGpioIn2Type() == IO_TYPE_IN_MACHINERY_ACTION_BARS) {
                machineryActionBarsOn = machineryActionBarsOn | io_level_111;
            }
            if (G3_Configuration::getInstance().getGpioIn3Type() == IO_TYPE_IN_MACHINERY_ACTION_BARS) {
                machineryActionBarsOn = machineryActionBarsOn | io_level_110;
            }
            G3_Configuration::getInstance().setMachineryActionBarsStatus((machineryActionBarsOn ? 1 : 0));

            /* 如果需要依据报警开关的话，就尝试更新一下开关状态 */
            if (G3_Configuration::getInstance().getCameraAlarmNeedCameraAlarmSwitch() == 1) {
                checkCameraAlarmSwitchStatusFromGPIO();
            }

            /* 检查下IO信号 */
            checkSignalFromGPIO();

        }


    }

    void MainUnitManagr::onGetEngineSpeedFromCANJ1939(const float engineSpeed) {
    }

    void MainUnitManagr::onGetSpeedFromCANJ1939(const float speed) {

    }

    void MainUnitManagr::onGetSetG3Config(SetG3Config &setG3Config, const char *ip, const int port) {
    }

    void MainUnitManagr::onGetQequestG3Config(QequestG3Config &qequestG3Config, const char *ip, const int port) {
    }

    void MainUnitManagr::onNeedSendDataToRS485(const uint8_t *buf, const int len) {
        peripheralUnitManager.sendDataToRS485(buf, len);
    }

    void MainUnitManagr::onGetPeripheralVersion(const uint8_t peripheralId, const char *softwarVersion,
                                                const char *protocoVersion) {
//        printf("****************MainUnitManagr::onGetPeripheralVersion   peripheralId:%d  softwarVersion:%s   protocoVersion:%s \n",peripheralId,softwarVersion,protocoVersion);
        /* 把L2的版本号存起来 */
        if (peripheralId == G3RS485DataPackage::RS485_DEVICE_ID_L2) {
            char l2SoftwareVersion[5] = {0x00};
            memcpy(l2SoftwareVersion, softwarVersion, 4);
            setenv("G3_L2_VERSION_NAME", l2SoftwareVersion, 1);
        }
        /* 把L3的版本号存起来 */
        if (peripheralId == G3RS485DataPackage::RS485_DEVICE_ID_L3) {
            char l3SoftwareVersion[5] = {0x00};
            memcpy(l3SoftwareVersion, softwarVersion, 4);
            setenv("G3_L3_VERSION_NAME", l3SoftwareVersion, 1);
        }
        /* 把L4的版本号存起来 */
        if (peripheralId == G3RS485DataPackage::RS485_DEVICE_ID_L4) {
            char l4SoftwareVersion[5] = {0x00};
            memcpy(l4SoftwareVersion, softwarVersion, 4);
            setenv("G3_L4_VERSION_NAME", l4SoftwareVersion, 1);
            /* 把协议版本号整理成int存起来 */
            char l4ProtocoVersion[5] = {0x00};
            memcpy(l4ProtocoVersion, protocoVersion, 4);
            float ver = std::atof(l4ProtocoVersion);
            G3_Configuration::getInstance().setL4ProtocoVersion(ver);
            printf("********+++++++++++++*******l4ProtocoVersion=%s  ver=%f \n",l4ProtocoVersion,ver);

        }

        /* 把GPS盒子的版本号存起来 */
        if (peripheralId == G3RS485DataPackage::RS485_DEVICE_ID_GPS) {
            char gpsBoxSoftwareVersion[5] = {0x00};
            memcpy(gpsBoxSoftwareVersion, softwarVersion, 4);
            setenv("G3_GPSBOX_VERSION_NAME", gpsBoxSoftwareVersion, 1);
        }
    }

    void MainUnitManagr::onHighTemperatureOff(int temperature) {
        isHighTemperature = false;
        detectUnitManager.setIsHighTemperature(isHighTemperature);
    }

    void MainUnitManagr::onHighTemperatureOn(int temperature) {
        isHighTemperature = true;
        detectUnitManager.setIsHighTemperature(isHighTemperature);
    }

    void MainUnitManagr::onGetGPSInfoFromRS485(G3RS485GPSInfo &gpsInfo) {
//    printf("%s \n  %s \n", gpsInfo.toString().c_str(), G3_Configuration::getInstance().getLocalTimeZone().c_str());

        GPSInfo curGPSInfo = G3_Configuration::getInstance().getCurGpsInfo();
        curGPSInfo.gpsSpeed = (float) gpsInfo.getGpsSpeed() / (float) 10;
        curGPSInfo.status = gpsInfo.getStatus();
        curGPSInfo.direction = gpsInfo.getDirection();
        curGPSInfo.altitude = gpsInfo.getAltitude();
        curGPSInfo.latitude = gpsInfo.getLatitude();
        curGPSInfo.longitude = gpsInfo.getLongitude();
        memcpy(curGPSInfo.bcdTime, gpsInfo.getGpsTimeDate(), sizeof(curGPSInfo.bcdTime));
        G3_Configuration::getInstance().setCurGpsInfo(curGPSInfo);

//        if (gpsInfo.getStatus() == 1) {
//            detectUnitManager.setSpeed((float) gpsInfo.getGpsSpeed() / (float) 10, SPEEDFROM_GPS);
//            mediaUnitManagr.setSpeed((float) gpsInfo.getGpsSpeed() / (float) 10, SPEEDFROM_GPS);
//            uiUnitManager.setSpeed((float) gpsInfo.getGpsSpeed() / (float) 10, SPEEDFROM_GPS);
//            communicationUnitManager.setSpeed((float) gpsInfo.getGpsSpeed() / (float) 10, SPEEDFROM_GPS);
//            std::string bcdStr = XuString::getInstance().bcd2String(gpsInfo.getGpsTimeDate(), 6);
//            bcdStr.insert(0, "20");
//            XuTimeUtil::getInstance().setGPSTimeToSystemTime(std::atoi(bcdStr.substr(0, 4).c_str()),
//                                                                                 std::atoi(bcdStr.substr(4, 2).c_str()),
//                                                                                 std::atoi(bcdStr.substr(6, 2).c_str()),
//                                                                                 std::atoi(bcdStr.substr(8, 2).c_str()),
//                                                                                 std::atoi(
//                                                                                         bcdStr.substr(10, 2).c_str()),
//                                                                                 std::atoi(
//                                                                                         bcdStr.substr(12, 2).c_str()),
//                                                                                 G3_Configuration::getInstance().getLocalTimeZone());
//        }


    }

    void MainUnitManagr::onNeedSendDataToSocket(const char *ip, const int port, const uint8_t *buf, int len) {
        peripheralUnitManager.sendDataToSokcet(ip, port, buf, len);
    }

    void MainUnitManagr::onGetStartUDPRealview(StartUDPRealview &startUDPRealview, const char *ip, const int port) {
        switch (startUDPRealview.getCameraId()) {
            case CAMERA_ID_1: {
                isStartUDPRealViewMode_camera1 = startUDPRealview.getOpt();
            }
                break;
            case CAMERA_ID_2: {
                isStartUDPRealViewMode_camera2 = startUDPRealview.getOpt();
            }
                break;
            case CAMERA_ID_3: {
                isStartUDPRealViewMode_camera3 = startUDPRealview.getOpt();
            }
                break;
            case CAMERA_ID_4: {
                isStartUDPRealViewMode_camera4 = startUDPRealview.getOpt();
            }
                break;
        }
    }


    void
    MainUnitManagr::onGetRealtimeVehicleStatus(RealTimeVehicleStatus &vehicleStatus, const char *ip, const int port) {

        /* 转换成算法模块需要的车况 */
        Vehicle_RealtimeStatus vehicleRealtimeStatus = {};
        vehicleRealtimeStatus.batteryVoltage = vehicleStatus.getBatteryVoltage();
        vehicleRealtimeStatus.engineSpeed = vehicleStatus.getEngineSpeed();
        vehicleRealtimeStatus.speed = vehicleStatus.getSpeed();
        vehicleRealtimeStatus.coolantTemperature = vehicleStatus.getCoolantTemperature();
        vehicleRealtimeStatus.instantaneousFuelConsumption = vehicleStatus.getInstantaneousFuelConsumption();
        vehicleRealtimeStatus.residualOil = vehicleStatus.getResidualOil();
        vehicleRealtimeStatus.acc = vehicleStatus.getCarStatus().acc;
        vehicleRealtimeStatus.acc = vehicleStatus.getCarStatus().acc;
        vehicleRealtimeStatus.turnL = vehicleStatus.getCarStatus().turnL;
        vehicleRealtimeStatus.turnR = vehicleStatus.getCarStatus().turnR;
        vehicleRealtimeStatus.bigLight = vehicleStatus.getCarStatus().bigLight;
        vehicleRealtimeStatus.wiper = vehicleStatus.getCarStatus().wiper;
        vehicleRealtimeStatus.brake = vehicleStatus.getCarStatus().brake;
        vehicleRealtimeStatus.frontDoor = vehicleStatus.getCarStatus().frontDoor;
        vehicleRealtimeStatus.backDoor = vehicleStatus.getCarStatus().backDoor;
        vehicleRealtimeStatus.reverse = vehicleStatus.getCarStatus().reverse;
        vehicleRealtimeStatus.MT_Camera_switch = vehicleStatus.getG4MtCameraSwitch();
        vehicleRealtimeStatus.safebeltStatus = vehicleStatus.getSeatbeltStatus();
        vehicleRealtimeStatus.SecurityModeStatus = vehicleStatus.getSecuritySwitchStatus() == 0x01;
        vehicleRealtimeStatus.c3Switch_c31 = vehicleStatus.getC3SwitchStatus().c3_1;
        vehicleRealtimeStatus.c3Switch_c32 = vehicleStatus.getC3SwitchStatus().c3_2;
        vehicleRealtimeStatus.c3Switch_c33 = vehicleStatus.getC3SwitchStatus().c3_3;
        vehicleRealtimeStatus.c3Switch_c34 = vehicleStatus.getC3SwitchStatus().c3_4;

        /* 先检查下车速 */
        int speedFrom = SPEEDFROM_ETH;
        if (vehicleRealtimeStatus.speed < 0) {
            /* uSocket过来的是车速是无效的，那么检查下GPS是否有车速  GPS车速要在有效范围内才使用 */
            if (G3_Configuration::getInstance().getCurGpsInfo().status == 1 &&
                (G3_Configuration::getInstance().getCurGpsInfo().gpsSpeed >= 0 &&
                 G3_Configuration::getInstance().getCurGpsInfo().gpsSpeed <= 2500)) {
                vehicleRealtimeStatus.speed = G3_Configuration::getInstance().getCurGpsInfo().gpsSpeed;
                speedFrom = SPEEDFROM_GPS;
            } else {
                /* 没有GPS，那么就使用默认的车速 */
                vehicleRealtimeStatus.speed = G3_Configuration::getInstance().getDefaultSpeed();
                speedFrom = SPEEDFROM_DEFAULT;
            }

        }
        /* 检查下转向灯的状态 */
        vehicleRealtimeStatus.turnL |= G3_Configuration::getInstance().getVehicleStatusFromIo().turnL;
        vehicleRealtimeStatus.turnR |= G3_Configuration::getInstance().getVehicleStatusFromIo().turnR;
        /* 检查下开门信号 */
        vehicleRealtimeStatus.backDoor |= G3_Configuration::getInstance().getVehicleStatusFromIo().door;
        vehicleRealtimeStatus.frontDoor |= G3_Configuration::getInstance().getVehicleStatusFromIo().door;
        /* 检查下倒车信号 */
        vehicleRealtimeStatus.reverse |= G3_Configuration::getInstance().getVehicleStatusFromIo().reverse;
        /* 检查下消警信号 */
        vehicleRealtimeStatus.safepassSign |= G3_Configuration::getInstance().getVehicleStatusFromIo().safepass;
        /* 检查下SOS信号 */
        vehicleRealtimeStatus.sosSign |= G3_Configuration::getInstance().getVehicleStatusFromIo().sos;

        /* 把车辆状态设置过去 */
        detectUnitManager.setVehicleRealtimeStatus(vehicleRealtimeStatus);
        communicationUnitManager.setVehicleRealtimeStatus(vehicleRealtimeStatus);

        /* 再设置车速和状态什么的 */
        mediaUnitManagr.setSpeed(vehicleRealtimeStatus.speed, speedFrom);
        uiUnitManager.setSpeed(vehicleRealtimeStatus.speed, speedFrom);
        communicationUnitManager.setSpeed(vehicleRealtimeStatus.speed, speedFrom);
        uiUnitManager.setTurnSignal(vehicleRealtimeStatus.turnL, vehicleRealtimeStatus.turnR);
        uiUnitManager.setReverseSignal(vehicleRealtimeStatus.reverse);
        communicationUnitManager.setTurnSignal(vehicleRealtimeStatus.turnL, vehicleRealtimeStatus.turnR);



        /* 如果是G4-S和G4-MT工作模式读取下工程机械操作杆状态和镜头报警开关来用 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4_UNIT ||
            G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4MT_UNIT) {
            G3_Configuration::getInstance().setMachineryActionBarsStatus(vehicleStatus.getMachineryActionBars());
            G3_Configuration::getInstance().setCameraAlarmSwitchStatus(vehicleStatus.getCameraAlarmSwitchStatus());
        }


    }

    void MainUnitManagr::onGetDataFromUART_MCU(const uint8_t *buf, int len) {
        //printf("onGetDataFromUART_MCU \n");
        communicationUnitManager.setMCUUARTData(buf, len);
    }

    void MainUnitManagr::onNeedSendDataToMCUUART(const uint8_t *buf, const int len) {
        peripheralUnitManager.sendDataToUART_MCU(buf, len);
    }

    void MainUnitManagr::onNeedPlaySound(const char *soundFilePath, bool isForce) {
        peripheralUnitManager.playSound(soundFilePath, isForce);
    }

    void MainUnitManagr::onNeedPlaySound_JKFZ(const char *soundFilePath, bool isForce) {
        peripheralUnitManager.playSound_JKFZ(soundFilePath, isForce);
    }

    int MainUnitManagr::onShowYUVGet(CameraYUVData &cameraData) {
        uiUnitManager.setYUVDataToDispaly(cameraData);
        return 0;
    }

    void MainUnitManagr::onGetCPUTemperature(float temperature) {
        if (isVISPtm) {
            mediaUnitManagr.setIsptm(true);
            /* 读一下UART状态 */
            mediaUnitManagr.setStatusUart(peripheralUnitManager.getUartMcuManager().isUartOpen());
            /* 读一下RS485状态 */
            mediaUnitManagr.setStatusRs485(peripheralUnitManager.getRs485OptManager().isRs485Open());
            /* 设置一下CPU温度 */
            mediaUnitManagr.setTemperature_CPU(temperature);
            /* 设置TF卡状态 */
            mediaUnitManagr.setStatusTf(hasTFcard);
            /* 设置TCP状态 */
            mediaUnitManagr.setTcpClient(peripheralUnitManager.getConfigureServer().isTcpOpen());
            /* 设置UDP状态 */
            mediaUnitManagr.setUdpBroadcast(isStartUDPRealViewMode_camera1 || isStartUDPRealViewMode_camera2 || isStartUDPRealViewMode_camera3 || isStartUDPRealViewMode_camera4);
            /* 设置下传感器状态 */
            mediaUnitManagr.setStatusSensor(
                    fabs(gSensor_x) >= 1e-6 && fabs(gSensor_y) >= 1e-6 && fabs(gSensor_z) >= 1e-6);
            /* 设置下ADC电压 */
            mediaUnitManagr.setVoltageStr(adcVoltage);
            /* 设置一下光敏电阻读数 */
            mediaUnitManagr.setPhotoresistorValue(photoresistorVoltage);
            /* 设置一下厂测模式 */
            peripheralUnitManager.setIsPtm(true);
            /* 改变一下IR灯 */
            onGetIRLampSwichContr(!curIRLampSwichStatus);


            if (tesplaySum % 30 == 0) {
                /* 播放一下测试音频 */
                peripheralUnitManager.playSound_JKFZ("se_visptm.pcm", true);
                tesplaySum = 0;
            }
            tesplaySum++;
        } else {
            mediaUnitManagr.setIsptm(false);
            /* 设置一下厂测模式 */
            peripheralUnitManager.setIsPtm(false);
        }


    }

    void MainUnitManagr::onGetPeripheralVersionFromMCUUART(G3AndMCUUartRespondPeripheralVersion &peripheralVersion) {
        /* 把MCU的版本号存起来 */
        if (peripheralVersion.getPeripheralId() == G3AndMCUUartPacket::PERIPHERAL_ID_MCU) {
            std::string fullVersion = "";
            /* 先填软件版本 */
            char mcuSoftwareVersion[5] = {0x00};
            memcpy(mcuSoftwareVersion, peripheralVersion.getSoftwareVersion(),
                   peripheralVersion.getSoftwareVersionLen());
            fullVersion.append(mcuSoftwareVersion);
            fullVersion.append("_");
            /* 再填协议版本号 */
            char mcuProtocolversion[5] = {0x00};
            memcpy(mcuProtocolversion, peripheralVersion.getProtocolVersion(),
                   peripheralVersion.getProtocolVersionLen());
            fullVersion.append(mcuProtocolversion);
            fullVersion.append("_");
            /* 再填应用分支编号 */
            char mcuBranchNumber[5] = {0x00};
            memcpy(mcuBranchNumber, peripheralVersion.getBranchNumber(),
                   peripheralVersion.getBranchNumberLen());
            fullVersion.append(mcuBranchNumber);


            setenv("G3_MCU_VERSION_NAME", fullVersion.c_str(), 1);
            /* 设置MCU版本号 */
            mediaUnitManagr.setMcuversion(const_cast<char *>(peripheralVersion.getSoftwareVersion()), peripheralVersion.getSoftwareVersionLen());
        }


    }

    void MainUnitManagr::onGetGSensorData(float value_x, float value_y, float value_z) {
        gSensor_x = value_x;
        gSensor_y = value_y;
        gSensor_z = value_z;
        communicationUnitManager.setGSensorData(value_x,value_y,value_z);
        //printf("onGetGSensorData:   value_x:%f  value_y:%f   value_z:%f  \n",value_x,value_y,value_z);
    }

    void MainUnitManagr::onGetADCVoltage(float voltage) {

        /* 看看是不是V6模式 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_V6) {
            /* 是V6模式，那么这个电压就是光敏电阻的电压，设置过去 */
            //NOTE 光敏电阻的读数就是通过ADC电压过来的,所以跟ADC电压的接口放一起了
            unitStatusListener.setPhotoresistorVoltage(voltage);
            photoresistorVoltage = voltage;
        } else {
            /* 不是V6模式，那么这个电压就是单纯的ADC电压，设置过去 */
            unitStatusListener.setADCVoltage(voltage);
            adcVoltage = voltage;
        }

    }


    void
    MainUnitManagr::onGetStartG3DeviceUpgrade(StartG3DeviceUpgrade &startG3DeviceUpgrade, const char *ip,
                                              const int port) {

        if (!upgradeUnitManager.isUpgrading()) {
            int ret = -1;
            ret = upgradeUnitManager.initBySocket(startG3DeviceUpgrade.getFileType(), startG3DeviceUpgrade.getFileLen(),
                                          const_cast<uint8_t *>(startG3DeviceUpgrade.getFileMd5()),
                                          UpgradeUnitManager::UPGRADE_FILE_UPLOAD_TCP_PORT, ip, port, *this);
            if (ret == 0) {
                if (threadPool.available() > 0) {
                    threadPool.start(upgradeUnitManager);
                    communicationUnitManager.setStartUpgardeResult(startG3DeviceUpgrade,
                                                                   UpgradeUnitManager::UPGRADE_FILE_UPLOAD_TCP_PORT,
                                                                   UpgradeUnitManager::UPGRADE_RESULT_SUCCESS, ip,
                                                                   port);
                } else {
                    communicationUnitManager.setStartUpgardeResult(startG3DeviceUpgrade, -1,
                                                                   UpgradeUnitManager::UPGRADE_RESULT_FAILED, ip, port);
                }
            } else {
                communicationUnitManager.setStartUpgardeResult(startG3DeviceUpgrade, -1,
                                                               UpgradeUnitManager::UPGRADE_RESULT_FAILED, ip, port);

            }

        } else {
            communicationUnitManager.setStartUpgardeResult(startG3DeviceUpgrade, -1,
                                                           UpgradeUnitManager::UPGRADE_RESULT_FAILED, ip, port);
        }
    }

    void MainUnitManagr::onGetUpgradeResult(const int fileType, const int result, const char *ip, const int port) {
        printf("MainUnitManagr::onGetUpgradeResult  fileType=%d  result=%d \n", fileType, result);
        /* 无论如何，返回个结果去给客户端 */


        /* 根据文件类型判断下是否需要什么其他操作 */
        /* 不同的文件进行不同的操作 */
        switch (fileType) {
            case UpgradeUnitManager::UPGRADE_FILE_TYPE_G3DEVICE_SOFTWARD: {
                /* 如果是G3的设备软件的话  主控模块不需要做什么操作 */
                communicationUnitManager.setUpgardeResult(PCConfigureDataPacket::DEVICEID_G30, result, ip, port);
            }
                break;

            case UpgradeUnitManager::UPGRADE_FILE_TYPE_G3MCU_SOFTWARD: {
                /* 如果是G3的MCU软件的话 直接开启强制更新 */
                std::string mcuFilePath = "";
                mcuFilePath.append(upgradeUnitManager.UPHRADE_FILE_PATH_UPGRADE_FILE_G3MCU);
                communicationUnitManager.setUpgradeMCU(mcuFilePath);
            }
                break;
            case UpgradeUnitManager::UPGRADE_FILE_TYPE_GPSUNIT_SOFTWARD: {
                /* 如果是G3的GPS盒子软件的话 直接开启强制更新 */
                std::string gpsBoxUpgradeFilePath = "";
                gpsBoxUpgradeFilePath.append(upgradeUnitManager.UPHRADE_FILE_PATH_UPGRADE_FILE_GPSBOX);
                communicationUnitManager.setUpgradeRS485(G3RS485DataPackage::RS485_DEVICE_ID_GPS,
                                                         gpsBoxUpgradeFilePath);
            }
                break;
            case UpgradeUnitManager::UPGRADE_FILE_TYPE_L3_SOFTWARD: {
                /* 如果是G3的L3软件的话 直接开启强制更新 */
                std::string l3UpgradeFilePath = "";
                l3UpgradeFilePath.append(upgradeUnitManager.UPHRADE_FILE_PATH_UPGRADE_FILE_L3);
                communicationUnitManager.setUpgradeRS485(G3RS485DataPackage::RS485_DEVICE_ID_L3, l3UpgradeFilePath);
            }
                break;
            case UpgradeUnitManager::UPGRADE_FILE_TYPE_L4_SOFTWARD: {
                /* 如果是G3的L4软件的话 直接开启强制更新 */
                std::string l4UpgradeFilePath = "";
                l4UpgradeFilePath.append(upgradeUnitManager.UPHRADE_FILE_PATH_UPGRADE_FILE_L4);
                communicationUnitManager.setUpgradeRS485(G3RS485DataPackage::RS485_DEVICE_ID_L4, l4UpgradeFilePath);
            }
                break;

            case UpgradeUnitManager::UPGRADE_FILE_TYPE_ALARM_SOUND_FILE_PACK: {
                /* 如果是G3的报警声音的话  主控模块不需要做什么操作 */
                communicationUnitManager.setUpgardeResult(PCConfigureDataPacket::DEVICEID_G30, result, ip, port);
            }
                break;

        }
    }

    void MainUnitManagr::onGetObjectInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) {
        /* 看看是不是需要锁掉报警输出 */
        if (!G3_Configuration::getInstance().getLockFunction().detectOut) {
            communicationUnitManager.setDetectionInfo_BSD(objectInfo, detectionInfo);
        }

        mediaUnitManagr.setDetectionInfo_BSD(objectInfo, detectionInfo);
        uiUnitManager.setDetectionInfo_BSD(objectInfo, detectionInfo);

    }

    void MainUnitManagr::onGetDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
        /* 能不能报警需要很多的判断，一个个判断完才能决定需不需要发送报警出去 */
        bool canSenEvent = true;

        /* 如果收到sos信号，那么就不发送报警事件了,没有sos信号，就正常发送报警事件 */
        if (G3_Configuration::getInstance().getVehicleStatusFromIo().sos) {
            canSenEvent = false;
        }

//        printf("MainUnitManagr:: get   vehicleStatusFromIo.sos=%d   canSenEvent=%d \n",G3_Configuration::getInstance().getVehicleStatusFromIo().sos,canSenEvent);
        /* 看看这一个镜头是否被bypass掉了 */
        if (canSenEvent && G3_Configuration::getInstance().getVehicleStatusFromIo().safepass) {
            switch (curCameraType.cameraId) {
                case CAMERA_ID_1: {
                    canSenEvent = !G3_Configuration::getInstance().getBsdCameraBypassInfo().camera1;
                }
                    break;
                case CAMERA_ID_2: {
                    canSenEvent = !G3_Configuration::getInstance().getBsdCameraBypassInfo().camera2;
                }
                    break;
                case CAMERA_ID_3: {
                    canSenEvent = !G3_Configuration::getInstance().getBsdCameraBypassInfo().camera3;
                }
                    break;
                case CAMERA_ID_4: {
                    canSenEvent = !G3_Configuration::getInstance().getBsdCameraBypassInfo().camera4;
                }
                    break;
            }

        }

        /* 看看是不是需要锁掉报警输出 如果只用于G4  那么只不发给协议通信模块就行，目前先都不发 */
        if (canSenEvent && G3_Configuration::getInstance().getLockFunction().alarmOut) {
            canSenEvent = false;
        }

        /* 可以发送报警的时候才需要发 */
        if (canSenEvent) {
            communicationUnitManager.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            /* 只有在不作为G4的一部分工作才需要给到外设跟录制还有UI显示 */
            if (G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_G4_UNIT &&
                G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_G4GJ_G4_UNIT) {
                peripheralUnitManager.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
                mediaUnitManagr.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
                uiUnitManager.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            }
        }


    }

    int MainUnitManagr::onGetSetG3SystemTime(SetG3SystemTime &setG3SystemTime, const char *ip, const int port) {
        /* 设置一下时间 */
        std::string bcdStr = XuString::getInstance().bcd2String(setG3SystemTime.getBcdTime(), 6);
        bcdStr.insert(0, "20");
//        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,"CAMERATest") << "start setTimeToSystemTime!" <<  XU_LOG_END;
        bool setTimeResult = XuTimeUtil::getInstance().setTimeToSystemTime(std::atoi(bcdStr.substr(0, 4).c_str()),
                                                                           std::atoi(bcdStr.substr(4, 2).c_str()),
                                                                           std::atoi(bcdStr.substr(6, 2).c_str()),
                                                                           std::atoi(bcdStr.substr(8, 2).c_str()),
                                                                           std::atoi(bcdStr.substr(10, 2).c_str()),
                                                                           std::atoi(bcdStr.substr(12, 2).c_str()),
                                                                           setG3SystemTime.getTimeZoneStr().c_str(),
                                                                           G3_Configuration::getInstance().getLocalTimeZone());
//        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,"CAMERATest") << "setTimeToSystemTime done! ret=" << setTimeResult <<  XU_LOG_END;
        return setTimeResult ? 0 : -1;
    }

    void MainUnitManagr::onGetDSMInfo(G3DetectionResult &detectionInfo) {

        mediaUnitManagr.setDSMInfo(detectionInfo);
        if (!G3_Configuration::getInstance().getLockFunction().detectOut) {
            communicationUnitManager.setDSMInfo(detectionInfo);
        }
    }

    void MainUnitManagr::onGetObjectInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) {
        mediaUnitManagr.setDetectionInfo_GES(objectInfo, detectionInfo);
        if (!G3_Configuration::getInstance().getLockFunction().detectOut) {
            communicationUnitManager.setDetectionInfo_GES(objectInfo, detectionInfo);
        }
    }

    void MainUnitManagr::getDvrMp4FileList() {

        /* 整理好成一份列表 */
        std::vector<PCConfigureDataPacket::MP4FileInfo> dvrFileList;

        /* 遍历所有镜头 */
        std::vector<CameraType> cameraList = G3_Configuration::getInstance().getCameraTypeList();
        for(std::size_t i = 0; i < cameraList.size(); i ++){
            /* 获取文件存放路径 */
            std::string dvrFilePath = G3_Configuration::getInstance().getCurFileStorageRoot();
            switch (cameraList[i].cameraId) {
                case CAMERA_ID_1:{
                    dvrFilePath.append(DVR_FILE_PATH_OF_CAMERA1);
                }
                    break;
                case CAMERA_ID_2:{
                    dvrFilePath.append(DVR_FILE_PATH_OF_CAMERA2);
                }
                    break;
                case CAMERA_ID_3:{
                    dvrFilePath.append(DVR_FILE_PATH_OF_CAMERA3);
                }
                    break;
                case CAMERA_ID_4:{
                    dvrFilePath.append(DVR_FILE_PATH_OF_CAMERA4);
                }
                    break;
            }
            /* 拿出所有的mp4文件 */
            std::vector<std::string> dvrListTemp = XuFile::getInstance().getDirAllFile(dvrFilePath.c_str(), ".mp4");
            /* 遍历MP4文件，封装一下 */
            if(!dvrListTemp.empty()){
                for (std::size_t j = 0; j < dvrListTemp.size(); j++) {
                    std::string fileName = dvrListTemp[j];
                    std::string fullPath = dvrFilePath;
                    if (XuString::getInstance().split(fileName, "_").size() >= 4) {
                        PCConfigureDataPacket::MP4FileInfo tempMp4FileInfo;
                        /* 设置一下文件类型 */
                        tempMp4FileInfo.fileType = PCConfigureDataPacket::MP4FILETYPE_DVR;
                        /* 设置识别类型 */
                        memset(&tempMp4FileInfo.detectContent, 0xFF, 1);
                        /* 设置文件状态 */
                        fullPath.append(fileName);
                        bool isGood = XuFile::getInstance().checkMp4File(fullPath.c_str());
                        bool isLocked = (
                                std::strcmp(XuString::getInstance().split(fileName, "_")[3].c_str(), "1") == 0);
                        if (isGood) {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_UNLOCK;
                            }

                        } else {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_UNLOCK;
                            }
                        }
                        /* 设置相机ID */
                        tempMp4FileInfo.cameraId = cameraList[i].cameraId;
                        /* 设置录制时间（BCD格式UTC时间） */
                        XuString::getInstance().stringToByteArray(XuString::getInstance().split(fileName, "_")[0],
                                                                  tempMp4FileInfo.bcdTime,
                                                                  sizeof(tempMp4FileInfo.bcdTime));
                        /* 设置文件名长度 */
                        tempMp4FileInfo.fileNameLen = fileName.size();
                        /* 设置文件名 */
                        memcpy(tempMp4FileInfo.fileName, fileName.data(), fileName.size());
                        /* 塞到列表里 */
                        dvrFileList.push_back(tempMp4FileInfo);
                    }
                }
            }
//        printf("dvrFilePath_camera1=%s   dvrList_camera1.size=%d   dvrFilePath_camera2=%s   dvrList_camera2.size=%d  dvrFileList.size=%d  \n",
//               dvrFilePath_camera1.c_str(), dvrList_camera1.size(), dvrFilePath_camera2.c_str(), dvrList_camera2.size(),
//               dvrFileList.size());
        }
        /* 把DVR设置给通信模块 */
        communicationUnitManager.setNewDVRFileList(dvrFileList);
    }

    void MainUnitManagr::getWarMp4FileList() {
        /* 整理好成一份列表 */
        std::vector<PCConfigureDataPacket::MP4FileInfo> warFileList;
        /* 遍历所有镜头 */
        std::vector<CameraType> cameraList = G3_Configuration::getInstance().getCameraTypeList();
        for(std::size_t i = 0; i < cameraList.size(); i ++) {
            /* 获取文件存放路径 */
            std::string cameraWarFilePath = G3_Configuration::getInstance().getCurFileStorageRoot();
            /* 根据相机ID确定次级目录 */
            switch (cameraList[i].cameraId) {
                case CAMERA_ID_1: {
                    cameraWarFilePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                }
                    break;
                case CAMERA_ID_2: {
                    cameraWarFilePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                }
                    break;
                case CAMERA_ID_3: {
                    cameraWarFilePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                }
                    break;
                case CAMERA_ID_4: {
                    cameraWarFilePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                }
                    break;
            }

            /* 获取下BSD_左边的报警视频 */
            std::string bsdLeftAlarmVideoPath = cameraWarFilePath;
            bsdLeftAlarmVideoPath.append(WAR_VIDEO_FILE_PATH_OF_BSD_LEFT);
            std::vector<std::string> bsdLeftWarList = XuFile::getInstance().getDirAllFile(bsdLeftAlarmVideoPath.c_str(), ".mp4");
            if (!bsdLeftWarList.empty()) {
                for (std::size_t j = 0; j < bsdLeftWarList.size(); j++) {
                    std::string fullPath = bsdLeftAlarmVideoPath;
                    std::string fileName = bsdLeftWarList[j];
                    if (XuString::getInstance().split(fileName, "_").size() >= 4) {
                        PCConfigureDataPacket::MP4FileInfo tempMp4FileInfo;
                        /* 设置一下文件类型 */
                        tempMp4FileInfo.fileType = PCConfigureDataPacket::MP4FILETYPE_BSD_LEFT;
                        /* 设置识别类型 */
                        memset(&tempMp4FileInfo.detectContent, 0xFF, 1);
                        /* 设置文件状态 */
                        fullPath.append(fileName);
                        bool isGood = XuFile::getInstance().checkMp4File(fullPath.c_str());
                        bool isLocked = (
                                std::strcmp(XuString::getInstance().split(fileName, "_")[3].c_str(),
                                            "1") == 0);
                        if (isGood) {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_UNLOCK;
                            }

                        } else {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_UNLOCK;
                            }
                        }
                        /* 设置相机ID */
                        tempMp4FileInfo.cameraId = cameraList[i].cameraId;
                        /* 设置录制时间（BCD格式UTC时间） */
                        XuString::getInstance().stringToByteArray(
                                XuString::getInstance().split(fileName, "_")[0], tempMp4FileInfo.bcdTime,
                                sizeof(tempMp4FileInfo.bcdTime));
                        /* 设置文件名长度 */
                        tempMp4FileInfo.fileNameLen = fileName.size();
                        /* 设置文件名 */
                        memcpy(tempMp4FileInfo.fileName, fileName.data(),fileName.size());
                        /* 塞到列表里 */
                        warFileList.push_back(tempMp4FileInfo);
                    }
                }
            }

            /* 获取下BSD_右边的报警视频 */
            std::string bsdRightAlarmVideoPath = cameraWarFilePath;
            bsdRightAlarmVideoPath.append(WAR_VIDEO_FILE_PATH_OF_BSD_RIGHT);
            std::vector<std::string> bsdRightWarList = XuFile::getInstance().getDirAllFile(bsdRightAlarmVideoPath.c_str(), ".mp4");
            if (!bsdRightWarList.empty()) {
                for (std::size_t j = 0; j < bsdRightWarList.size(); j++) {
                    std::string fullPath = bsdRightAlarmVideoPath;
                    std::string fileName = bsdRightWarList[j];
                    if (XuString::getInstance().split(fileName, "_").size() >= 4) {
                        PCConfigureDataPacket::MP4FileInfo tempMp4FileInfo;
                        /* 设置一下文件类型 */
                        tempMp4FileInfo.fileType = PCConfigureDataPacket::MP4FILETYPE_BSD_RIGHT;
                        /* 设置识别类型 */
                        memset(&tempMp4FileInfo.detectContent, 0xFF, 1);
                        /* 设置文件状态 */
                        fullPath.append(fileName);
                        bool isGood = XuFile::getInstance().checkMp4File(fullPath.c_str());
                        bool isLocked = (
                                std::strcmp(XuString::getInstance().split(fileName, "_")[3].c_str(),
                                            "1") == 0);
                        if (isGood) {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_UNLOCK;
                            }

                        } else {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_UNLOCK;
                            }
                        }
                        /* 设置相机ID */
                        tempMp4FileInfo.cameraId = cameraList[i].cameraId;
                        /* 设置录制时间（BCD格式UTC时间） */
                        XuString::getInstance().stringToByteArray(
                                XuString::getInstance().split(fileName, "_")[0], tempMp4FileInfo.bcdTime,
                                sizeof(tempMp4FileInfo.bcdTime));
                        /* 设置文件名长度 */
                        tempMp4FileInfo.fileNameLen = fileName.size();
                        /* 设置文件名 */
                        memcpy(tempMp4FileInfo.fileName, fileName.data(),fileName.size());
                        /* 塞到列表里 */
                        warFileList.push_back(tempMp4FileInfo);
                    }
                }
            }

            /* 获取下BSD_车前的报警视频 */
            std::string bsdForwardAlarmVideoPath = cameraWarFilePath;
            bsdForwardAlarmVideoPath.append(WAR_VIDEO_FILE_PATH_OF_BSD_FORWARD);
            std::vector<std::string> bsdForwardWarList = XuFile::getInstance().getDirAllFile(bsdForwardAlarmVideoPath.c_str(), ".mp4");
            if (!bsdForwardWarList.empty()) {
                for (std::size_t j = 0; j < bsdForwardWarList.size(); j++) {
                    std::string fullPath = bsdForwardAlarmVideoPath;
                    std::string fileName = bsdForwardWarList[j];
                    if (XuString::getInstance().split(fileName, "_").size() >= 4) {
                        PCConfigureDataPacket::MP4FileInfo tempMp4FileInfo;
                        /* 设置一下文件类型 */
                        tempMp4FileInfo.fileType = PCConfigureDataPacket::MP4FILETYPE_BSD_FORWARD;
                        /* 设置识别类型 */
                        memset(&tempMp4FileInfo.detectContent, 0xFF, 1);
                        /* 设置文件状态 */
                        fullPath.append(fileName);
                        bool isGood = XuFile::getInstance().checkMp4File(fullPath.c_str());
                        bool isLocked = (
                                std::strcmp(XuString::getInstance().split(fileName, "_")[3].c_str(),
                                            "1") == 0);
                        if (isGood) {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_UNLOCK;
                            }

                        } else {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_UNLOCK;
                            }
                        }
                        /* 设置相机ID */
                        tempMp4FileInfo.cameraId = cameraList[i].cameraId;
                        /* 设置录制时间（BCD格式UTC时间） */
                        XuString::getInstance().stringToByteArray(
                                XuString::getInstance().split(fileName, "_")[0], tempMp4FileInfo.bcdTime,
                                sizeof(tempMp4FileInfo.bcdTime));
                        /* 设置文件名长度 */
                        tempMp4FileInfo.fileNameLen = fileName.size();
                        /* 设置文件名 */
                        memcpy(tempMp4FileInfo.fileName, fileName.data(),fileName.size());
                        /* 塞到列表里 */
                        warFileList.push_back(tempMp4FileInfo);
                    }
                }
            }

            /* 获取下BSD_车尾的报警视频 */
            std::string bsdBackwardAlarmVideoPath = cameraWarFilePath;
            bsdBackwardAlarmVideoPath.append(WAR_VIDEO_FILE_PATH_OF_BSD_BACKWARD);
            std::vector<std::string> bsdBackwardWarList = XuFile::getInstance().getDirAllFile(bsdBackwardAlarmVideoPath.c_str(), ".mp4");
            if (!bsdBackwardWarList.empty()) {
                for (std::size_t j = 0; j < bsdBackwardWarList.size(); j++) {
                    std::string fullPath = bsdBackwardAlarmVideoPath;
                    std::string fileName = bsdBackwardWarList[j];
                    if (XuString::getInstance().split(fileName, "_").size() >= 4) {
                        PCConfigureDataPacket::MP4FileInfo tempMp4FileInfo;
                        /* 设置一下文件类型 */
                        tempMp4FileInfo.fileType = PCConfigureDataPacket::MP4FILETYPE_BSD_BACKWARD;
                        /* 设置识别类型 */
                        memset(&tempMp4FileInfo.detectContent, 0xFF, 1);
                        /* 设置文件状态 */
                        fullPath.append(fileName);
                        bool isGood = XuFile::getInstance().checkMp4File(fullPath.c_str());
                        bool isLocked = (
                                std::strcmp(XuString::getInstance().split(fileName, "_")[3].c_str(),
                                            "1") == 0);
                        if (isGood) {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_UNLOCK;
                            }

                        } else {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_UNLOCK;
                            }
                        }
                        /* 设置相机ID */
                        tempMp4FileInfo.cameraId = cameraList[i].cameraId;
                        /* 设置录制时间（BCD格式UTC时间） */
                        XuString::getInstance().stringToByteArray(
                                XuString::getInstance().split(fileName, "_")[0], tempMp4FileInfo.bcdTime,
                                sizeof(tempMp4FileInfo.bcdTime));
                        /* 设置文件名长度 */
                        tempMp4FileInfo.fileNameLen = fileName.size();
                        /* 设置文件名 */
                        memcpy(tempMp4FileInfo.fileName, fileName.data(),fileName.size());
                        /* 塞到列表里 */
                        warFileList.push_back(tempMp4FileInfo);
                    }
                }
            }

            /* 获取下R151的报警视频 */
            std::string r151AlarmVideoPath = cameraWarFilePath;
            r151AlarmVideoPath.append(WAR_VIDEO_FILE_PATH_OF_R151);
            std::vector<std::string> r151WarList = XuFile::getInstance().getDirAllFile(r151AlarmVideoPath.c_str(), ".mp4");
            if (!r151WarList.empty()) {
                for (std::size_t j = 0; j < r151WarList.size(); j++) {
                    std::string fullPath = r151AlarmVideoPath;
                    std::string fileName = r151WarList[j];
                    if (XuString::getInstance().split(fileName, "_").size() >= 4) {
                        PCConfigureDataPacket::MP4FileInfo tempMp4FileInfo;
                        /* 设置一下文件类型 */
                        tempMp4FileInfo.fileType = PCConfigureDataPacket::MP4FILETYPE_R151;
                        /* 设置识别类型 */
                        memset(&tempMp4FileInfo.detectContent, 0xFF, 1);
                        /* 设置文件状态 */
                        fullPath.append(fileName);
                        bool isGood = XuFile::getInstance().checkMp4File(fullPath.c_str());
                        bool isLocked = (
                                std::strcmp(XuString::getInstance().split(fileName, "_")[3].c_str(),
                                            "1") == 0);
                        if (isGood) {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_UNLOCK;
                            }

                        } else {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_UNLOCK;
                            }
                        }
                        /* 设置相机ID */
                        tempMp4FileInfo.cameraId = cameraList[i].cameraId;
                        /* 设置录制时间（BCD格式UTC时间） */
                        XuString::getInstance().stringToByteArray(
                                XuString::getInstance().split(fileName, "_")[0], tempMp4FileInfo.bcdTime,
                                sizeof(tempMp4FileInfo.bcdTime));
                        /* 设置文件名长度 */
                        tempMp4FileInfo.fileNameLen = fileName.size();
                        /* 设置文件名 */
                        memcpy(tempMp4FileInfo.fileName, fileName.data(),fileName.size());
                        /* 塞到列表里 */
                        warFileList.push_back(tempMp4FileInfo);
                    }
                }
            }

            /* 获取下R158的报警视频 */
            std::string r158AlarmVideoPath = cameraWarFilePath;
            r158AlarmVideoPath.append(WAR_VIDEO_FILE_PATH_OF_R158);
            std::vector<std::string> r158WarList = XuFile::getInstance().getDirAllFile(r158AlarmVideoPath.c_str(), ".mp4");
            if (!r158WarList.empty()) {
                for (std::size_t j = 0; j < r158WarList.size(); j++) {
                    std::string fullPath = r158AlarmVideoPath;
                    std::string fileName = r158WarList[j];
                    if (XuString::getInstance().split(fileName, "_").size() >= 4) {
                        PCConfigureDataPacket::MP4FileInfo tempMp4FileInfo;
                        /* 设置一下文件类型 */
                        tempMp4FileInfo.fileType = PCConfigureDataPacket::MP4FILETYPE_R158;
                        /* 设置识别类型 */
                        memset(&tempMp4FileInfo.detectContent, 0xFF, 1);
                        /* 设置文件状态 */
                        fullPath.append(fileName);
                        bool isGood = XuFile::getInstance().checkMp4File(fullPath.c_str());
                        bool isLocked = (
                                std::strcmp(XuString::getInstance().split(fileName, "_")[3].c_str(),
                                            "1") == 0);
                        if (isGood) {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_UNLOCK;
                            }

                        } else {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_UNLOCK;
                            }
                        }
                        /* 设置相机ID */
                        tempMp4FileInfo.cameraId = cameraList[i].cameraId;
                        /* 设置录制时间（BCD格式UTC时间） */
                        XuString::getInstance().stringToByteArray(
                                XuString::getInstance().split(fileName, "_")[0], tempMp4FileInfo.bcdTime,
                                sizeof(tempMp4FileInfo.bcdTime));
                        /* 设置文件名长度 */
                        tempMp4FileInfo.fileNameLen = fileName.size();
                        /* 设置文件名 */
                        memcpy(tempMp4FileInfo.fileName, fileName.data(),fileName.size());
                        /* 塞到列表里 */
                        warFileList.push_back(tempMp4FileInfo);
                    }
                }
            }


            /* 获取下R159的报警视频 */
            std::string r159AlarmVideoPath = cameraWarFilePath;
            r159AlarmVideoPath.append(WAR_VIDEO_FILE_PATH_OF_R159);
            std::vector<std::string> r159WarList = XuFile::getInstance().getDirAllFile(r159AlarmVideoPath.c_str(), ".mp4");
            if (!r159WarList.empty()) {
                for (std::size_t j = 0; j < r159WarList.size(); j++) {
                    std::string fullPath = r159AlarmVideoPath;
                    std::string fileName = r159WarList[j];
                    if (XuString::getInstance().split(fileName, "_").size() >= 4) {
                        PCConfigureDataPacket::MP4FileInfo tempMp4FileInfo;
                        /* 设置一下文件类型 */
                        tempMp4FileInfo.fileType = PCConfigureDataPacket::MP4FILETYPE_R159;
                        /* 设置识别类型 */
                        memset(&tempMp4FileInfo.detectContent, 0xFF, 1);
                        /* 设置文件状态 */
                        fullPath.append(fileName);
                        bool isGood = XuFile::getInstance().checkMp4File(fullPath.c_str());
                        bool isLocked = (
                                std::strcmp(XuString::getInstance().split(fileName, "_")[3].c_str(),
                                            "1") == 0);
                        if (isGood) {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_UNLOCK;
                            }

                        } else {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_UNLOCK;
                            }
                        }
                        /* 设置相机ID */
                        tempMp4FileInfo.cameraId = cameraList[i].cameraId;
                        /* 设置录制时间（BCD格式UTC时间） */
                        XuString::getInstance().stringToByteArray(
                                XuString::getInstance().split(fileName, "_")[0], tempMp4FileInfo.bcdTime,
                                sizeof(tempMp4FileInfo.bcdTime));
                        /* 设置文件名长度 */
                        tempMp4FileInfo.fileNameLen = fileName.size();
                        /* 设置文件名 */
                        memcpy(tempMp4FileInfo.fileName, fileName.data(),fileName.size());
                        /* 塞到列表里 */
                        warFileList.push_back(tempMp4FileInfo);
                    }
                }
            }

            /* 获取下未定义算法类型的报警视频 */
            std::string unknowAlarmVideoPath = cameraWarFilePath;
            unknowAlarmVideoPath.append(WAR_VIDEO_FILE_PATH_OF_UNKNOW_MP4);
            std::vector<std::string> unknowWarList = XuFile::getInstance().getDirAllFile(unknowAlarmVideoPath.c_str(), ".mp4");
            if (!unknowWarList.empty()) {
                for (std::size_t j = 0; j < unknowWarList.size(); j++) {
                    std::string fullPath = unknowAlarmVideoPath;
                    std::string fileName = unknowWarList[j];
                    if (XuString::getInstance().split(fileName, "_").size() >= 4) {
                        PCConfigureDataPacket::MP4FileInfo tempMp4FileInfo;
                        /* 设置一下文件类型 */
                        tempMp4FileInfo.fileType = PCConfigureDataPacket::MP4FILETYPE_UNKNOW;
                        /* 设置识别类型 */
                        memset(&tempMp4FileInfo.detectContent, 0xFF, 1);
                        /* 设置文件状态 */
                        fullPath.append(fileName);
                        bool isGood = XuFile::getInstance().checkMp4File(fullPath.c_str());
                        bool isLocked = (
                                std::strcmp(XuString::getInstance().split(fileName, "_")[3].c_str(),
                                            "1") == 0);
                        if (isGood) {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_NORMAL_UNLOCK;
                            }

                        } else {
                            if (isLocked) {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_LOCK;
                            } else {
                                tempMp4FileInfo.fileStatus = PCConfigureDataPacket::MP4FILESTATUS_DAMAGED_UNLOCK;
                            }
                        }
                        /* 设置相机ID */
                        tempMp4FileInfo.cameraId = cameraList[i].cameraId;
                        /* 设置录制时间（BCD格式UTC时间） */
                        XuString::getInstance().stringToByteArray(
                                XuString::getInstance().split(fileName, "_")[0], tempMp4FileInfo.bcdTime,
                                sizeof(tempMp4FileInfo.bcdTime));
                        /* 设置文件名长度 */
                        tempMp4FileInfo.fileNameLen = fileName.size();
                        /* 设置文件名 */
                        memcpy(tempMp4FileInfo.fileName, fileName.data(),fileName.size());
                        /* 塞到列表里 */
                        warFileList.push_back(tempMp4FileInfo);
                    }
                }
            }
        }

        /* 把报警视频列表设置给通信模块 */
        communicationUnitManager.setNewWarFileList(warFileList);
    }

    std::vector<PCConfigureDataPacket::MP4FileInfo>
    MainUnitManagr::onGetG3Mp4FileOpt(G3Mp4FileOpt &g3Mp4FileOpt, const char *ip, const int port) {


        std::vector<PCConfigureDataPacket::MP4FileInfo> ret;
        ret.resize(g3Mp4FileOpt.getFileListSize());

        try {
            std::vector<PCConfigureDataPacket::MP4FileInfo> failedList;
            /* 遍历一下 */
            if (g3Mp4FileOpt.getFileListSize() > 0) {
                for (int i = 0; i < g3Mp4FileOpt.getFileListSize(); ++i) {
                    std::string filePath = G3_Configuration::getInstance().getCurFileStorageRoot();
                    /* 先根据文件类型和相机ID确定目录 */
                    switch (g3Mp4FileOpt.getFileList()[i].fileType) {
                        case PCConfigureDataPacket::MP4FILETYPE_DVR: {
                            /* 根据不同的相机确定不同的目录 */
                            switch (g3Mp4FileOpt.getFileList()[i].cameraId) {
                                case CAMERA_ID_1: {
                                    filePath.append(DVR_FILE_PATH_OF_CAMERA1);
                                }
                                    break;
                                case CAMERA_ID_2: {
                                    filePath.append(DVR_FILE_PATH_OF_CAMERA2);
                                }
                                    break;
                                case CAMERA_ID_3: {
                                    filePath.append(DVR_FILE_PATH_OF_CAMERA3);
                                }
                                    break;
                                case CAMERA_ID_4: {
                                    filePath.append(DVR_FILE_PATH_OF_CAMERA4);
                                }
                                    break;
                            }

                        }
                            break;
                        case PCConfigureDataPacket::MP4FILETYPE_BSD_LEFT: {
                            /* 根据不同的镜头确定不同的次级目录 */
                            switch (g3Mp4FileOpt.getFileList()[i].cameraId) {
                                case CAMERA_ID_1: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                                }
                                    break;
                                case CAMERA_ID_2: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                                }
                                    break;
                                case CAMERA_ID_3: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                                }
                                    break;
                                case CAMERA_ID_4: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                                }
                                    break;
                            }
                            /* 补完最后的目录 */
                            filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_LEFT);
                        }
                            break;
                        case PCConfigureDataPacket::MP4FILETYPE_BSD_RIGHT: {
                            /* 根据不同的镜头确定不同的次级目录 */
                            switch (g3Mp4FileOpt.getFileList()[i].cameraId) {
                                case CAMERA_ID_1: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                                }
                                    break;
                                case CAMERA_ID_2: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                                }
                                    break;
                                case CAMERA_ID_3: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                                }
                                    break;
                                case CAMERA_ID_4: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                                }
                                    break;
                            }
                            /* 补完最后的目录 */
                            filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_RIGHT);
                        }
                            break;
                        case PCConfigureDataPacket::MP4FILETYPE_BSD_FORWARD: {
                            /* 根据不同的镜头确定不同的次级目录 */
                            switch (g3Mp4FileOpt.getFileList()[i].cameraId) {
                                case CAMERA_ID_1: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                                }
                                    break;
                                case CAMERA_ID_2: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                                }
                                    break;
                                case CAMERA_ID_3: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                                }
                                    break;
                                case CAMERA_ID_4: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                                }
                                    break;
                            }
                            /* 补完最后的目录 */
                            filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_FORWARD);
                        }
                            break;
                        case PCConfigureDataPacket::MP4FILETYPE_BSD_BACKWARD: {
                            /* 根据不同的镜头确定不同的次级目录 */
                            switch (g3Mp4FileOpt.getFileList()[i].cameraId) {
                                case CAMERA_ID_1: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                                }
                                    break;
                                case CAMERA_ID_2: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                                }
                                    break;
                                case CAMERA_ID_3: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                                }
                                    break;
                                case CAMERA_ID_4: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                                }
                                    break;
                            }
                            /* 补完最后的目录 */
                            filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_BACKWARD);
                        }
                            break;
                        case PCConfigureDataPacket::MP4FILETYPE_R151: {
                            /* 根据不同的镜头确定不同的次级目录 */
                            switch (g3Mp4FileOpt.getFileList()[i].cameraId) {
                                case CAMERA_ID_1: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                                }
                                    break;
                                case CAMERA_ID_2: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                                }
                                    break;
                                case CAMERA_ID_3: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                                }
                                    break;
                                case CAMERA_ID_4: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                                }
                                    break;
                            }
                            /* 补完最后的目录 */
                            filePath.append(WAR_VIDEO_FILE_PATH_OF_R151);
                        }
                            break;
                        case PCConfigureDataPacket::MP4FILETYPE_R158: {
                            /* 根据不同的镜头确定不同的次级目录 */
                            switch (g3Mp4FileOpt.getFileList()[i].cameraId) {
                                case CAMERA_ID_1: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                                }
                                    break;
                                case CAMERA_ID_2: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                                }
                                    break;
                                case CAMERA_ID_3: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                                }
                                    break;
                                case CAMERA_ID_4: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                                }
                                    break;
                            }
                            /* 补完最后的目录 */
                            filePath.append(WAR_VIDEO_FILE_PATH_OF_R158);
                        }
                            break;
                        case PCConfigureDataPacket::MP4FILETYPE_R159: {
                            /* 根据不同的镜头确定不同的次级目录 */
                            switch (g3Mp4FileOpt.getFileList()[i].cameraId) {
                                case CAMERA_ID_1: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                                }
                                    break;
                                case CAMERA_ID_2: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                                }
                                    break;
                                case CAMERA_ID_3: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                                }
                                    break;
                                case CAMERA_ID_4: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                                }
                                    break;
                            }
                            /* 补完最后的目录 */
                            filePath.append(WAR_VIDEO_FILE_PATH_OF_R159);
                        }
                            break;
                        case PCConfigureDataPacket::MP4FILETYPE_UNKNOW: {
                            /* 根据不同的镜头确定不同的次级目录 */
                            switch (g3Mp4FileOpt.getFileList()[i].cameraId) {
                                case CAMERA_ID_1: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                                }
                                    break;
                                case CAMERA_ID_2: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                                }
                                    break;
                                case CAMERA_ID_3: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                                }
                                    break;
                                case CAMERA_ID_4: {
                                    filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                                }
                                    break;
                            }
                            /* 补完最后的目录 */
                            filePath.append(WAR_VIDEO_FILE_PATH_OF_UNKNOW_MP4);
                        }
                            break;


                    }
                    /* 补齐文件名 */
                    filePath.append(reinterpret_cast<const char *>(g3Mp4FileOpt.getFileList()[i].fileName),
                                    g3Mp4FileOpt.getFileList()[i].fileNameLen);
                    /* 根据操作类型进行操作 */
                    switch (g3Mp4FileOpt.getOptType()) {
                        case PCConfigureDataPacket::MP4FILELISTOPTTYPE_DELETE: {
                            if (remove(filePath.c_str()) == 0) {
                                printf("*******remove %s success! \n", filePath.c_str());

                            } else {
                                printf("*******remove %s failed!  error=%s \n", filePath.c_str(), strerror(errno));
                                failedList.push_back(g3Mp4FileOpt.getFileList()[i]);
                            }

                        }
                            break;
                        case PCConfigureDataPacket::MP4FILELISTOPTTYPE_LOCK: {
                            printf("************  PCConfigureDataPacket::MP4FILELISTOPTTYPE_LOCK   filePath=%s \n",
                                   filePath.c_str());
                        }
                            break;
                        case PCConfigureDataPacket::MP4FILELISTOPTTYPE_UNLOCK: {
                            printf("************  PCConfigureDataPacket::MP4FILELISTOPTTYPE_UNLOCK   filePath=%s \n",
                                   filePath.c_str());
                        }
                            break;
                    }
                }
                ret = failedList;
                /*sync一下*/
                sync();
                /* 重新获取一下文件列表 */
                getDvrMp4FileList();
                getWarMp4FileList();

            }
        } catch (...) {
            printf("has some error %s \n", strerror(errno));
        }


        return ret;
    }

    StartG3FileDownloadResult
    MainUnitManagr::onGetStartG3FileDownload(StartG3FileDownload &startG3FileDownload, const char *ip, const int port) {
        printf("onGetStartG3FileDownload=%s  \n", startG3FileDownload.getFileInfo().fileName);

        StartG3FileDownloadResult result;
        /* 先看看是不是有空闲的下载工具 */
        int downloadIndex = -1;
        for (int i = 0; i < 3; i++) {
            if (!mp4FileDownloadUtils[i].isTcpOpen()) {
                downloadIndex = i;
                printf("mp4FileDownloadUtils downloadIndex=%d  \n", downloadIndex);
                break;
            } else { ; // not to do
            }
        }

        /* 如果没有空闲的  那么就直接失败  如果有空闲的话  就使用空闲的 */
        if (downloadIndex >= 0) {
            /* 先找下文件的全路径 */
            std::string filePath = G3_Configuration::getInstance().getCurFileStorageRoot();
            /* 先根据文件类型和相机ID确定目录 */
            switch (startG3FileDownload.getFileInfo().fileType) {
                case PCConfigureDataPacket::MP4FILETYPE_DVR: {
                    /* 根据不同的相机确定不同的目录 */
                    switch (startG3FileDownload.getFileInfo().cameraId) {
                        case CAMERA_ID_1: {
                            filePath.append(DVR_FILE_PATH_OF_CAMERA1);
                        }
                            break;
                        case CAMERA_ID_2: {
                            filePath.append(DVR_FILE_PATH_OF_CAMERA2);
                        }
                            break;
                        case CAMERA_ID_3: {
                            filePath.append(DVR_FILE_PATH_OF_CAMERA3);
                        }
                            break;
                        case CAMERA_ID_4: {
                            filePath.append(DVR_FILE_PATH_OF_CAMERA4);
                        }
                            break;
                    }

                }
                    break;
                case PCConfigureDataPacket::MP4FILETYPE_BSD_LEFT: {
                    /* 根据不同的镜头确定不同的次级目录 */
                    switch (startG3FileDownload.getFileInfo().cameraId) {
                        case CAMERA_ID_1: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                        }
                            break;
                        case CAMERA_ID_2: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                        }
                            break;
                        case CAMERA_ID_3: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                        }
                            break;
                        case CAMERA_ID_4: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                        }
                            break;
                    }
                    /* 补完最后的目录 */
                    filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_LEFT);
                }
                    break;
                case PCConfigureDataPacket::MP4FILETYPE_BSD_RIGHT: {
                    /* 根据不同的镜头确定不同的次级目录 */
                    switch (startG3FileDownload.getFileInfo().cameraId) {
                        case CAMERA_ID_1: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                        }
                            break;
                        case CAMERA_ID_2: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                        }
                            break;
                        case CAMERA_ID_3: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                        }
                            break;
                        case CAMERA_ID_4: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                        }
                            break;
                    }
                    /* 补完最后的目录 */
                    filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_RIGHT);
                }
                    break;
                case PCConfigureDataPacket::MP4FILETYPE_BSD_FORWARD: {
                    /* 根据不同的镜头确定不同的次级目录 */
                    switch (startG3FileDownload.getFileInfo().cameraId) {
                        case CAMERA_ID_1: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                        }
                            break;
                        case CAMERA_ID_2: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                        }
                            break;
                        case CAMERA_ID_3: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                        }
                            break;
                        case CAMERA_ID_4: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                        }
                            break;
                    }
                    /* 补完最后的目录 */
                    filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_FORWARD);
                }
                    break;
                case PCConfigureDataPacket::MP4FILETYPE_BSD_BACKWARD: {
                    /* 根据不同的镜头确定不同的次级目录 */
                    switch (startG3FileDownload.getFileInfo().cameraId) {
                        case CAMERA_ID_1: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                        }
                            break;
                        case CAMERA_ID_2: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                        }
                            break;
                        case CAMERA_ID_3: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                        }
                            break;
                        case CAMERA_ID_4: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                        }
                            break;
                    }
                    /* 补完最后的目录 */
                    filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_BACKWARD);
                }
                    break;
                case PCConfigureDataPacket::MP4FILETYPE_R151: {
                    /* 根据不同的镜头确定不同的次级目录 */
                    switch (startG3FileDownload.getFileInfo().cameraId) {
                        case CAMERA_ID_1: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                        }
                            break;
                        case CAMERA_ID_2: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                        }
                            break;
                        case CAMERA_ID_3: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                        }
                            break;
                        case CAMERA_ID_4: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                        }
                            break;
                    }
                    /* 补完最后的目录 */
                    filePath.append(WAR_VIDEO_FILE_PATH_OF_R151);
                }
                    break;
                case PCConfigureDataPacket::MP4FILETYPE_R158: {
                    /* 根据不同的镜头确定不同的次级目录 */
                    switch (startG3FileDownload.getFileInfo().cameraId) {
                        case CAMERA_ID_1: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                        }
                            break;
                        case CAMERA_ID_2: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                        }
                            break;
                        case CAMERA_ID_3: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                        }
                            break;
                        case CAMERA_ID_4: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                        }
                            break;
                    }
                    /* 补完最后的目录 */
                    filePath.append(WAR_VIDEO_FILE_PATH_OF_R158);
                }
                    break;
                case PCConfigureDataPacket::MP4FILETYPE_R159: {
                    /* 根据不同的镜头确定不同的次级目录 */
                    switch (startG3FileDownload.getFileInfo().cameraId) {
                        case CAMERA_ID_1: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                        }
                            break;
                        case CAMERA_ID_2: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                        }
                            break;
                        case CAMERA_ID_3: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                        }
                            break;
                        case CAMERA_ID_4: {
                            filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                        }
                            break;
                    }
                    /* 补完最后的目录 */
                    filePath.append(WAR_VIDEO_FILE_PATH_OF_R159);
                }
                    break;

            }
            /* 补齐文件名 */
            filePath.append(reinterpret_cast<const char *>(startG3FileDownload.getFileInfo().fileName),
                            startG3FileDownload.getFileInfo().fileNameLen);

            printf("filePath=%s  \n", filePath.c_str());

            /* 初始化下载工具  如果初始化失败  则返回失败 */
            if (mp4FileDownloadUtils[downloadIndex].initDownloadTool(mp4FileDownTcpPortList[downloadIndex], filePath,
                                                                     *this) == 0) {
                mp4FileDownthreadPool.start(mp4FileDownloadUtils[downloadIndex]);
                /* 封装一下结果 */
                result.setResult(PCConfigureDataPacket::RESULT_SUCCESS);
                result.setTcpPort(mp4FileDownloadUtils[downloadIndex].getCurTcpPort());
                result.setFileLen(mp4FileDownloadUtils[downloadIndex].getFileLen());
                result.setFileMD5(mp4FileDownloadUtils[downloadIndex].getFileMd5());
            } else {
                /* 封装一下结果 */
                result.setResult(PCConfigureDataPacket::RESULT_FAILED);
                result.setTcpPort(0xFFFF);
                result.setFileLen(0);
            }

        } else {
            /* 封装一下结果 */
            result.setResult(PCConfigureDataPacket::RESULT_FAILED);
            result.setTcpPort(0xFFFF);
            result.setFileLen(0);
        }
        return result;
    }

    void MainUnitManagr::onGetDownloadResult(std::string filePath, const int result, const char *ip, const int port) {
        printf("onGetDownloadResult   filePath=%s   result=%d  \n", filePath.c_str(), result);
    }

    /* 是否打开调试模式的计数 */
    int startDebugSum = 0;
    /* 上一次发送打开调试的时间 */
    uint64_t lastOpenDebugModeTime = 0;

    void MainUnitManagr::onGetStartG3DebugMode(StartG3DebugMode &startG3DebugMode, const char *ip, const int port) {
        printf("MainUnitManagr::onGetStartG3DebugMode  \n");

        if (XuString::getInstance().cmpTwoByteArray(startG3DebugMode.getPwd(), 8, myPwd, 8)) {
            switch (startG3DebugMode.getOptType()) {
                case StartG3DebugMode::G3_DEBUG_MODE_OPT_OPEN: {
                    if ((XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec() - lastOpenDebugModeTime) <= 2) {
                        if (startDebugSum >= 10) {
                            printf("********************open adb************************\n");
                            XuShell::getInstance().runShellWithTimeout(
                                    "echo \"usb_adb_en\" > /tmp/.usb_config && /etc/init.d/S50usbdevice restart", 2000);
                            XuShell::getInstance().runShellWithTimeout("touch /tmp/debug.g3 && sync");
                            startDebugSum = 0;
                        } else {
                            startDebugSum++;
                        }

                    } else {
                        startDebugSum = 0;
                    }

                    lastOpenDebugModeTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
                }
                    break;
                case StartG3DebugMode::G3_DEBUG_MODE_OPT_CLOSE: {
                    XuShell::getInstance().runShellWithTimeout(
                            "echo \" \" > /tmp/.usb_config && /etc/init.d/S50usbdevice restart", 2000);
                    XuShell::getInstance().runShellWithTimeout("rm /tmp/debug.g3 && sync");
                }
                    break;
            }
        } else {
//            printf("pwd=%s   myPwd=%s  \n",
//                   XuString::getInstance().byteArrayToString(const_cast<uint8_t *>(startG3DebugMode.getPwd()),
//                                                             8).c_str(),
//                   XuString::getInstance().byteArrayToString(const_cast<uint8_t *>(myPwd), 8).c_str());
        }


    }

    void MainUnitManagr::getFunctionLockInfo() {
        uint8_t readBuf[4096] = {0x00};
        int readLen = XuFile::getInstance().readFile(FUNCTIONLOCKFILEPATH, readBuf, sizeof(readBuf));
        if (readLen > 0) {
            std::string lockstr = G3_Configuration::getInstance().getLockstr();
            lockstr.clear();
            lockstr.append(reinterpret_cast<const char *>(readBuf));
            G3_Configuration::getInstance().setLockstr(lockstr);

            std::string reallLockStr = XuString::getInstance().AES_128_ECB_AND_BASE64_Decrypt(
                    (uint8_t *) AES_128_ECB_KEY_FUNCTIONLOCK, lockstr);
            if (!reallLockStr.empty()) {
                std::cout << "********************解密后锁的状态:" << reallLockStr << std::endl;

                std::vector<std::string> params = XuString::getInstance().split(reallLockStr, "-");
                if (!params.empty()) {
                    /* 先获取下是否被锁 */
                    int islock = atoi(params[1].c_str());
                    /* 获取下剩余次数 */
                    int remainingUsage = atoi(params[2].c_str());
                    /* 获取被锁功能 */
                    int lockFunction = atoi(params[3].c_str());
                    std::cout << "islock:" << islock << "    remainingUsage:" << remainingUsage << "    lockFunction:"
                              << lockFunction << std::endl;

                    Locked_Function lockedFunction = {false, false, 0};
                    G3_Configuration::getInstance().setLockFunction(lockedFunction);

                    if (islock == 1) {
                        if (remainingUsage < 1) {
                            Locked_Function lockedFunction = {false, false, 0};
                            if ((lockFunction & 0x01) == 0x01) {
                                lockedFunction.alarmOut = true;
                            } else {
                                lockedFunction.alarmOut = false;
                            }
                            if ((lockFunction & 0x02) == 0x02) {
                                lockedFunction.detectOut = true;
                            } else {
                                lockedFunction.detectOut = false;
                            }
                            G3_Configuration::getInstance().setLockFunction(lockedFunction);
                        } else {
                            remainingUsage--;
                            std::string newRealStrlock;
                            newRealStrlock.append(params[0] + "-");
                            newRealStrlock.append(params[1] + "-");
                            newRealStrlock.append(std::to_string(remainingUsage) + "-");
                            newRealStrlock.append(params[3]);
                            std::string newstrlock = XuString::getInstance().AES_ECB_128_AND_BASE64_Encrypt(
                                    (uint8_t *) AES_128_ECB_KEY_FUNCTIONLOCK,
                                    newRealStrlock);
                            printf("newRealStrlock=%s     newstrlock=%s \n", newRealStrlock.c_str(),
                                   newstrlock.c_str());
                            if (!newstrlock.empty()) {
                                XuFile::getInstance().writeFile("FunctionLock", (uint8_t *) newstrlock.c_str(),
                                                                newstrlock.size());
                            }


                        };
                    }
                }
            } else {
                printf("FunctionLock content error! \n");
            }
        } else {
            printf("read FunctionLock failed ! \n");
        }

    }

    void MainUnitManagr::onGetSetFunctionLock(SetFunctionLock &setFunctionLock, const char *ip, const int port) {
        getFunctionLockInfo();

    }

    void MainUnitManagr::onGetObjectInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) {
        mediaUnitManagr.setDetectionInfo_Adas(objectInfo, detectionInfo);
        if (!G3_Configuration::getInstance().getLockFunction().detectOut) {
            communicationUnitManager.setDetectionInfo_Adas(objectInfo, detectionInfo);
            uiUnitManager.setDetectionInfo_Adas(objectInfo, detectionInfo);
        }

    }

    void MainUnitManagr::onGetRestartMRV220(RestartG3 &restartG3, const char *ip, const int port) {
        /* 收到重启MRV220的指令 不同的操作类型对应不停的操作 */
        switch (restartG3.getOpt()) {
            case RestartG3::RESTART_OPT_ONLY_SOFTWARD: {
                /* 只杀掉软件 */
                XuShell::getInstance().killallG3sofeward("Socket msg");
            }
                break;
            case RestartG3::RESTART_OPT_REBOOT_MRV220: {
                /* 重启整块MRV220 */
                XuShell::getInstance().rebootG3sofeward("Socket msg");
            }
                break;

            default: {
                printf("get a restart mrv220 msg,but opt is undefined! opt = %d  \n", restartG3.getOpt());
            }
                break;
        }

    }

    int MainUnitManagr::onGetStartUDPRealviewSeparate(StartUDPRealview_Separate &startUdpRealview, const char *ip,
                                                      const int port) {
        printf("onGetStartUDPRealviewSeparate cameraId=%d    opt=%d port=%d \n", startUdpRealview.getCameraId(),
               startUdpRealview.getOpt(), startUdpRealview.getUdpPort());
        int ret = -1;
        switch (startUdpRealview.getOpt()) {
            case StartUDPRealview_Separate::OPT_CLOSE: {
                ret = peripheralUnitManager.closeRealviewUDPSocketSeparate(startUdpRealview.getCameraId(),
                                                                           startUdpRealview.getUdpPort(),
                                                                           startUdpRealview.getAddressStr());
            }
                break;
            case StartUDPRealview_Separate::OPT_OPEN: {
                ret = peripheralUnitManager.addRealviewUDPSocketSeparate(startUdpRealview.getCameraId(),
                                                                         startUdpRealview.getUdpPort(),
                                                                         startUdpRealview.getAddressStr(),
                                                                         startUdpRealview.getUdpPort());
            }
                break;
        }
        return ret;
    }

    void MainUnitManagr::onGetUnitWorkingAbnormally(const UNIT_NUM unitNum) {
//        XuLog::getInstance().log(XuLog::LOG_LEVEL_FATAL,typeid(*this).name()) << "onGetUnitWorkingAbnormally! unitId = " << unitNum << XU_LOG_END;
//        switch (unitNum) {
//            case UNIT_NUM_PERIPHERALUNIT: {
//                /* 外设模块工作异常了，重启下软件 */
//                XuShell::getInstance().killallG3sofeward("peripheralunit error");
//            }
//                break;
//            case UNIT_NUM_MEDIAUNIT: {
//                /* 多媒体模块工作异常了，重启下软件 */
//                XuShell::getInstance().killallG3sofeward("Mediaunit error");
//            }
//                break;
//            case UNIT_NUM_DETECTUNIT: {
//                /* 算法模块工作异常了，重启下软件 */
//                XuShell::getInstance().killallG3sofeward("Detectunit error");
//            }
//                break;
//            default:{
//               ; // not to do
//            }
//                break;
//
//        }


    }

    void MainUnitManagr::checkCameraAlarmSwitchStatusFromGPIO() {
        /* 是不是有定义了这个开关 */
        bool hasCameraAlarmSwitch1 = false;
        bool hasCameraAlarmSwitch2 = false;
        bool hasCameraAlarmSwitch3 = false;
        /* 这个开关的值 */
        bool cameraAlarmSwitch1 = false;
        bool cameraAlarmSwitch2 = false;
        bool cameraAlarmSwitch3 = false;

        /* 先检查开关1的 */
        if (G3_Configuration::getInstance().getGpioIn1Type() == IO_TYPE_IN_CAMERA_ALARM_SWITCH_1) {
            hasCameraAlarmSwitch1 = true;
            cameraAlarmSwitch1 = cameraAlarmSwitch1 | io_level_115;
        }
        if (G3_Configuration::getInstance().getGpioIn2Type() == IO_TYPE_IN_CAMERA_ALARM_SWITCH_1) {
            hasCameraAlarmSwitch1 = true;
            cameraAlarmSwitch1 = cameraAlarmSwitch1 | io_level_111;
        }
        if (G3_Configuration::getInstance().getGpioIn3Type() == IO_TYPE_IN_CAMERA_ALARM_SWITCH_1) {
            hasCameraAlarmSwitch1 = true;
            cameraAlarmSwitch1 = cameraAlarmSwitch1 | io_level_110;
        }

        /* 再检查开关2的 */
        if (G3_Configuration::getInstance().getGpioIn1Type() == IO_TYPE_IN_CAMERA_ALARM_SWITCH_2) {
            hasCameraAlarmSwitch2 = true;
            cameraAlarmSwitch2 = cameraAlarmSwitch2 | io_level_115;
        }
        if (G3_Configuration::getInstance().getGpioIn2Type() == IO_TYPE_IN_CAMERA_ALARM_SWITCH_2) {
            hasCameraAlarmSwitch2 = true;
            cameraAlarmSwitch2 = cameraAlarmSwitch2 | io_level_111;
        }
        if (G3_Configuration::getInstance().getGpioIn3Type() == IO_TYPE_IN_CAMERA_ALARM_SWITCH_2) {
            hasCameraAlarmSwitch2 = true;
            cameraAlarmSwitch2 = cameraAlarmSwitch2 | io_level_110;
        }

        /* 再检查开关3的 */
        if (G3_Configuration::getInstance().getGpioIn1Type() == IO_TYPE_IN_CAMERA_ALARM_SWITCH_3) {
            hasCameraAlarmSwitch3 = true;
            cameraAlarmSwitch3 = cameraAlarmSwitch3 | io_level_115;
        }
        if (G3_Configuration::getInstance().getGpioIn2Type() == IO_TYPE_IN_CAMERA_ALARM_SWITCH_3) {
            hasCameraAlarmSwitch3 = true;
            cameraAlarmSwitch3 = cameraAlarmSwitch3 | io_level_111;
        }
        if (G3_Configuration::getInstance().getGpioIn3Type() == IO_TYPE_IN_CAMERA_ALARM_SWITCH_3) {
            hasCameraAlarmSwitch3 = true;
            cameraAlarmSwitch3 = cameraAlarmSwitch3 | io_level_110;
        }

        /* 整理一下结果 */
        Camera_Alarm_Switch_Status cameraAlarmSwitchStatusTemp = G3_Configuration::getInstance().getCameraAlarmSwitchStatus();
        if (hasCameraAlarmSwitch1) {
            cameraAlarmSwitchStatusTemp.cameraAlarmSwitch1 = cameraAlarmSwitch1;
        }
        if (hasCameraAlarmSwitch2) {
            cameraAlarmSwitchStatusTemp.cameraAlarmSwitch2 = cameraAlarmSwitch2;
        }
        if (hasCameraAlarmSwitch3) {
            cameraAlarmSwitchStatusTemp.cameraAlarmSwitch3 = cameraAlarmSwitch3;
        }
        G3_Configuration::getInstance().setCameraAlarmSwitchStatus(cameraAlarmSwitchStatusTemp);
    }


    void MainUnitManagr::checkSignalFromGPIO() {
        bool reverse = false;
        bool turnL = false;
        bool turnR = false;
        bool rollover = false;
        bool door = false;
        /* 由于RS232的按钮改成了单纯一个信号输入，当成一个IO的input用，故sos跟safepass也在IO这里一起整理了 */
        bool safepass = safepassOn_RS232button;
        bool sos = sosOn_RS232button;



        /* 先判断IO输入口1的 */
        switch (G3_Configuration::getInstance().getGpioIn1Type()) {
            case IO_TYPE_IN_REVERSE: {
                reverse = (io_level_115 == 1);
            }
                break;
            case IO_TYPE_IN_TURN_LEFT: {
                turnL = (io_level_115 == 1);
            }
                break;
            case IO_TYPE_IN_TURN_RIGHT: {
                turnR = (io_level_115 == 1);
            }
                break;
            case IO_TYPE_IN_TURN_SPEED: {
                /* 车速线目前还没有处理方式 */

            }
                break;
            case IO_TYPE_IN_TURN_ROLLOVER: {
                rollover = (io_level_115 == 1);
            }
                break;
            case IO_TYPE_IN_DOOR: {
                door = (io_level_115 == 1);
            }
                break;
            case IO_TYPE_IN_SAFEPASS_SIGN: {
                safepass |= (io_level_115 == 1);
            }
                break;
            case IO_TYPE_IN_SOS_SIGN: {
                sos |= (io_level_115 == 1);
            }
                break;
        }


        /* 先判断IO输入口2的 */
        switch (G3_Configuration::getInstance().getGpioIn2Type()) {
            case IO_TYPE_IN_REVERSE: {
                reverse = (io_level_111 == 1);
            }
                break;
            case IO_TYPE_IN_TURN_LEFT: {
                turnL = (io_level_111 == 1);
            }
                break;
            case IO_TYPE_IN_TURN_RIGHT: {
                turnR = (io_level_111 == 1);
            }
                break;
            case IO_TYPE_IN_TURN_SPEED: {
                /* 车速线目前还没有处理方式 */

            }
                break;
            case IO_TYPE_IN_TURN_ROLLOVER: {
                rollover = (io_level_111 == 1);
            }
                break;
            case IO_TYPE_IN_DOOR: {
                door = (io_level_111 == 1);
            }
                break;
            case IO_TYPE_IN_SAFEPASS_SIGN: {
                safepass |= (io_level_111 == 1);
            }
                break;
            case IO_TYPE_IN_SOS_SIGN: {
                sos |= (io_level_111 == 1);
            }
                break;
        }


        /* 先判断IO输入口3的 */
        switch (G3_Configuration::getInstance().getGpioIn3Type()) {
            case IO_TYPE_IN_REVERSE: {
                reverse = (io_level_110 == 1);
            }
                break;
            case IO_TYPE_IN_TURN_LEFT: {
                turnL = (io_level_110 == 1);
            }
                break;
            case IO_TYPE_IN_TURN_RIGHT: {
                turnR = (io_level_110 == 1);
            }
                break;
            case IO_TYPE_IN_TURN_SPEED: {
                /* 车速线目前还没有处理方式 */

            }
                break;
            case IO_TYPE_IN_TURN_ROLLOVER: {
                rollover = (io_level_110 == 1);
            }
                break;
            case IO_TYPE_IN_DOOR: {
                door = (io_level_110 == 1);
            }
                break;
            case IO_TYPE_IN_SAFEPASS_SIGN: {
                safepass |= (io_level_110 == 1);
            }
                break;
            case IO_TYPE_IN_SOS_SIGN: {
                sos |= (io_level_110 == 1);
            }
                break;
        }


//        detectUnitManager.setTurnSignal(turnL,turnR);
//        detectUnitManager.setReverseSignal(reverse);
//        detectUnitManager.setDoorSignal(door);
//
//        uiUnitManager.setTurnSignal(turnL,turnR);
//        uiUnitManager.setReverseSignal(reverse);
//
//        communicationUnitManager.setTurnSignal(turnL,turnR);




        VehicleStatusFromIO vehicleStatusFromIo = G3_Configuration::getInstance().getVehicleStatusFromIo();
        vehicleStatusFromIo.reverse = reverse;
        vehicleStatusFromIo.turnL = turnL;
        vehicleStatusFromIo.turnR = turnR;
        vehicleStatusFromIo.rollover = rollover;
        vehicleStatusFromIo.door = door;
        vehicleStatusFromIo.safepass = safepass;
        vehicleStatusFromIo.sos = sos;
        G3_Configuration::getInstance().setVehicleStatusFromIo(vehicleStatusFromIo);

    }

    void MainUnitManagr::onGetRealtimeVehicleStatusFromMCUUART(G3AndMCUUartVehicleRealTimeStatus &vehicleStatus) {

        /* 只有作为G3S工作模式或者G4-NIMI或者厂测模式下才采用MCU给过来的车辆信息 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G3S ||
            G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4GJ_UNIT_G3 ||
                G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4MINI ||
                isVISPtm) {

            /* 转换成算法模块需要的车况 */
            Vehicle_RealtimeStatus vehicleRealtimeStatus = {};
            vehicleRealtimeStatus.batteryVoltage = vehicleStatus.getBatteryVoltage();
            vehicleRealtimeStatus.engineSpeed = vehicleStatus.getEngineSpeed();
            vehicleRealtimeStatus.speed = vehicleStatus.getSpeed();
            vehicleRealtimeStatus.coolantTemperature = vehicleStatus.getCoolantTemperature();
            vehicleRealtimeStatus.instantaneousFuelConsumption = vehicleStatus.getInstantaneousFuelConsumption();
            vehicleRealtimeStatus.residualOil = vehicleStatus.getResidualOil();
            vehicleRealtimeStatus.acc = vehicleStatus.getCarStatus().acc;
            vehicleRealtimeStatus.turnL = vehicleStatus.getCarStatus().turnL;
            vehicleRealtimeStatus.turnR = vehicleStatus.getCarStatus().turnR;
            vehicleRealtimeStatus.bigLight = vehicleStatus.getCarStatus().bigLight;
            vehicleRealtimeStatus.wiper = vehicleStatus.getCarStatus().wiper;
            vehicleRealtimeStatus.brake = vehicleStatus.getCarStatus().brake;
            vehicleRealtimeStatus.frontDoor = vehicleStatus.getCarStatus().frontDoor;
            vehicleRealtimeStatus.backDoor = vehicleStatus.getCarStatus().backDoor;
            vehicleRealtimeStatus.reverse = vehicleStatus.getCarStatus().reverse;

            /* 先检查下车速 */
            int speedFrom = SPEEDFROM_UART;
            if (vehicleRealtimeStatus.speed < 0) {
                /* uart过来的是车速是无效的，那么检查下GPS是否有车速   GPS车速要在有效范围内才使用 */
                if (G3_Configuration::getInstance().getCurGpsInfo().status == 1 &&
                    (G3_Configuration::getInstance().getCurGpsInfo().gpsSpeed >= 0 &&
                     G3_Configuration::getInstance().getCurGpsInfo().gpsSpeed <= 2500)) {
                    vehicleRealtimeStatus.speed = G3_Configuration::getInstance().getCurGpsInfo().gpsSpeed;
                    speedFrom = SPEEDFROM_GPS;
                } else {
                    /* 没有GPS，那么就使用默认的车速 */
                    vehicleRealtimeStatus.speed = G3_Configuration::getInstance().getDefaultSpeed();
                    speedFrom = SPEEDFROM_DEFAULT;
                }

            }
            /* 检查下转向灯的状态 */
            vehicleRealtimeStatus.turnL |= G3_Configuration::getInstance().getVehicleStatusFromIo().turnL;
            vehicleRealtimeStatus.turnR |= G3_Configuration::getInstance().getVehicleStatusFromIo().turnR;
            /* 检查下开门信号 */
            vehicleRealtimeStatus.backDoor |= G3_Configuration::getInstance().getVehicleStatusFromIo().door;
            vehicleRealtimeStatus.frontDoor |= G3_Configuration::getInstance().getVehicleStatusFromIo().door;
            /* 检查下倒车信号 */
            vehicleRealtimeStatus.reverse |= G3_Configuration::getInstance().getVehicleStatusFromIo().reverse;
            /* 检查下消警信号 */
            vehicleRealtimeStatus.safepassSign |= G3_Configuration::getInstance().getVehicleStatusFromIo().safepass;
            /* 检查下SOS信号 */
            vehicleRealtimeStatus.sosSign |= G3_Configuration::getInstance().getVehicleStatusFromIo().sos;

            /* 把车辆状态设置过去 */
            detectUnitManager.setVehicleRealtimeStatus(vehicleRealtimeStatus);
            communicationUnitManager.setVehicleRealtimeStatus(vehicleRealtimeStatus);

            /* 再设置车速和状态什么的 */
            mediaUnitManagr.setSpeed(vehicleRealtimeStatus.speed, speedFrom);
            uiUnitManager.setSpeed(vehicleRealtimeStatus.speed, speedFrom);
            communicationUnitManager.setSpeed(vehicleRealtimeStatus.speed, speedFrom);
            uiUnitManager.setTurnSignal(vehicleRealtimeStatus.turnL, vehicleRealtimeStatus.turnR);
            uiUnitManager.setReverseSignal(vehicleRealtimeStatus.reverse);
            communicationUnitManager.setTurnSignal(vehicleRealtimeStatus.turnL, vehicleRealtimeStatus.turnR);
        }

    }

    void MainUnitManagr::onGetProductionTestResultFromMCUUART(G3AndMCUUartPTWrokResult &ptWrokResult) {
        bool canWorkStatus = false;
        if (!ptWrokResult.getCanTestResultList().empty()) {
            for (std::size_t i = 0; i < ptWrokResult.getCanTestResultList().size(); i++) {
                canWorkStatus |= (ptWrokResult.getCanTestResultList()[i].getResult() == 0);
            }
        }
        mediaUnitManagr.setCANWorkStatus(canWorkStatus);
    }

    void MainUnitManagr::onGetDataFromMQTT(const int mqttType, const char *topicName, const int topicNameLen,
                                           const char *payload, const int payloadLen) {
//        printf("onGetDataFromMQTT   mqttType=%d   topicName=%s  payload=%s  \n",mqttType,topicName,payload);

        communicationUnitManager.setMQTTMsg(mqttType, topicName, topicNameLen, payload, payloadLen);


    }

    void MainUnitManagr::onGetMQTTConnectStatusChange(const bool isConnected) {
        communicationUnitManager.setMQTTConnectStatusChange(isConnected);
    }

    int MainUnitManagr::onNeedSendDataToNDSMqtt(std::string &strData) {
        return peripheralUnitManager.sendDataToNDSMqtt(strData);
    }

    int MainUnitManagr::onGetRTMPPushOpt(const std::string url, const int cameraId, const int opt) {
        return peripheralUnitManager.rtmpControl(url, cameraId, opt);
    }

    int MainUnitManagr::setDispalyParamsToRS485(const int displayMode, const int iconMode) {
        return communicationUnitManager.setDispalyParams(displayMode, iconMode);
    }

    void MainUnitManagr::onGetUpgradeResultFromRS485(G3RS485UpgradeResult &upgradeResult) {


        int deviceId = -1;
        /* 里面上因为用内容里面的外设ID的，但是L3写的有BUG，故使用消息结构定义的外设地址来进行判断 */
        switch (upgradeResult.getRs485DeviceId()) {
            case G3RS485DataPackage::RS485_DEVICE_ID_L3: {
                deviceId = PCConfigureDataPacket::DEVICEID_L3;
            }
                break;
            case G3RS485DataPackage::RS485_DEVICE_ID_L4: {
                deviceId = PCConfigureDataPacket::DEVICEID_L4;
            }
                break;
            case G3RS485DataPackage::RS485_DEVICE_ID_GPS: {
                deviceId = PCConfigureDataPacket::DEVICEID_GPSBOX;
            }
                break;

        }
        printf("deviceId=%d  485Id=%d \n",deviceId,upgradeResult.getPeripheralId());

        /* 直接发给所有人，让他们更新版本号 */
        communicationUnitManager.setUpgardeResult(deviceId, upgradeResult.getUpgradeResult(), "ALL", -1);
    }

    void MainUnitManagr::onGetSetDispalyParamsResultFromRS485(G3RS485SetDispalyParamsResult &setDispalyParamsResult) {
        communicationUnitManager.setDispalyParamsResultToSocket(PCConfigureDataPacket::DEVICEID_L4,
                                                                setDispalyParamsResult.getResult(), "ALL", -1);
    }

    int MainUnitManagr::onGetSetPeripheralDisplayParams(SetPeripheralDisplayParams displayParams, const char *ip,
                                                        const int port) {
        return communicationUnitManager.setDispalyParams(displayParams.getDisplayMode(), displayParams.getIconMode());
    }

    void
    MainUnitManagr::onGetGetG3AndG4DeviceVersion(GetG3AndG4DevicesVersion &getG3AndG4DevicesVersion, const char *ip,
                                                 const int port) {

        switch (getG3AndG4DevicesVersion.getDevicesId()) {
            case GetG3AndG4DevicesVersion::DEVICESID_G3_1:
            case GetG3AndG4DevicesVersion::DEVICESID_G3_2: {
                /* 先不处理 */
            }
                break;

            case GetG3AndG4DevicesVersion::DEVICESID_MCU: {
                /* 先不处理 */
            }
                break;

            case GetG3AndG4DevicesVersion::DEVICESID_L3: {
                /* 获取一下L3的最新版本号 */
                communicationUnitManager.getRS485PeripheralVersion(G3RS485DataPackage::RS485_DEVICE_ID_L3);
            }
                break;

            case GetG3AndG4DevicesVersion::DEVICESID_L4: {
                /* 获取一下L4的最新版本号 */
                communicationUnitManager.getRS485PeripheralVersion(G3RS485DataPackage::RS485_DEVICE_ID_L4);
            }
                break;
            case GetG3AndG4DevicesVersion::DEVICESID_GPSBOX: {
                /* 获取一下GPS盒子的最新版本号 */
                communicationUnitManager.getRS485PeripheralVersion(G3RS485DataPackage::RS485_DEVICE_ID_GPS);
            }
                break;
        }
    }

    void MainUnitManagr::onGetStopButtonStatusFromMODBUS(MODBUS_StopButtonStatus &stopButtonStatus) {
        printf("MainUnitManagr::onGetStopButtonStatusFromMODBUS %d  %d \n", stopButtonStatus.isMainButtonOn(),
               stopButtonStatus.isExtensionButtonOn());




        /* 先看看主按钮的定义 */
        switch (G3_Configuration::getInstance().getR232ModbusButtonMainType()) {
            case RS232_MODBUS_BUTTON_TYPE_SAFEPASS: {
                safepassOn_RS232button = stopButtonStatus.isMainButtonOn();

            }
                break;

            case RS232_MODBUS_BUTTON_TYPE_SOS: {
                sosOn_RS232button = stopButtonStatus.isMainButtonOn();
            }
                break;
        }

        /* 先看看主扩展按钮的定义 */
        switch (G3_Configuration::getInstance().getR232ModbusButtonExtensionType()) {
            case RS232_MODBUS_BUTTON_TYPE_SAFEPASS: {
                safepassOn_RS232button = stopButtonStatus.isExtensionButtonOn();

            }
                break;

            case RS232_MODBUS_BUTTON_TYPE_SOS: {
                sosOn_RS232button = stopButtonStatus.isExtensionButtonOn();
            }
                break;
        }

    }

    void MainUnitManagr::onNeedSendDataToRS232(const uint8_t *buf, const int len) {
        peripheralUnitManager.sendDataToRS232(buf, len);
    }

    void MainUnitManagr::onGetPeripheralUpgradeResultFromMCUUART(
            G3AndMCUUartPeripheralUpgradeResult &peripheralUpgradeResult) {
        /* 通过UART拿到的升级外设的结果  现在只有MCU是通过UART的 */

        /* 返回真实的结果给客户端 */
        communicationUnitManager.setUpgardeResult(PCConfigureDataPacket::DEVICEID_MCU,
                                                  peripheralUpgradeResult.getUpgradeResult(), "ALL", -1);

    }

    StartMRV220LogFileDownloadResult
    MainUnitManagr::onGetStartMRV220LogFileDownload(StartMRV220LogFileDownload &startMrv220LogFileDownload,
                                                    const char *ip, const int port) {
        printf("onGetStartMRV220LogFileDownload=%s  \n", startMrv220LogFileDownload.getFileInfo().fileName);

        StartMRV220LogFileDownloadResult result;
        /* 先看看是不是有空闲的下载工具 */
        int downloadIndex = -1;
        for (int i = 0; i < 3; i++) {
            if (!logFileDownloadUtils[i].isTcpOpen()) {
                downloadIndex = i;
                printf("logFileDownloadUtils downloadIndex=%d  \n", downloadIndex);
                break;
            } else { ; // not to do
            }
        }

        /* 如果没有空闲的  那么就直接失败  如果有空闲的话  就使用空闲的 */
        if (downloadIndex >= 0) {
            /* 先找下文件的全路径 */
            std::string filePath = XuLog::getInstance().getLogFileRootPath();
            /* 补齐文件名 */
            filePath.append(reinterpret_cast<const char *>(startMrv220LogFileDownload.getFileInfo().fileName),
                            startMrv220LogFileDownload.getFileInfo().fileNameLen);

            printf("log filePath=%s  %d  \n", filePath.c_str(), startMrv220LogFileDownload.getFileInfo().fileNameLen);

            /* 初始化下载工具  如果初始化失败  则返回失败 */
            if (logFileDownloadUtils[downloadIndex].initDownloadTool(logFileDownTcpPortList[downloadIndex], filePath,
                                                                     *this) == 0) {
                logFileDownthreadPool.start(logFileDownloadUtils[downloadIndex]);
                /* 封装一下结果 */
                result.setResult(PCConfigureDataPacket::RESULT_SUCCESS);
                result.setTcpPort(logFileDownloadUtils[downloadIndex].getCurTcpPort());
                result.setFileLen(logFileDownloadUtils[downloadIndex].getFileLen());
                result.setFileMD5(logFileDownloadUtils[downloadIndex].getFileMd5());
            } else {
                /* 封装一下结果 */
                result.setResult(PCConfigureDataPacket::RESULT_FAILED);
                result.setTcpPort(0xFFFF);
                result.setFileLen(0);
            }

        } else {
            /* 封装一下结果 */
            result.setResult(PCConfigureDataPacket::RESULT_FAILED);
            result.setTcpPort(0xFFFF);
            result.setFileLen(0);
        }
        return result;
    }

    int
    MainUnitManagr::onGetSetMultimediaFileEncryptionKey(SetMultimediaFileEncryptionKey &setMultimediaFileEncryptionKey,
                                                        const char *ip, const int port) {
        int ret = -1;

        /* 先检查一下是不是50个字节 */
        if (setMultimediaFileEncryptionKey.getKeyLen() == 50) {
            /* 先拿出校验码 */
            uint16_t crcCheckCode = CodeUtils::getInstance().BbToUint16(
                    const_cast<uint8_t *>(setMultimediaFileEncryptionKey.getKeyContent() + 10));
            /* 再拿出正确顺序的内容 */
            uint8_t keyAndIv[48] = {0X00};
            int index = 0;
            /* 先把最后面8位倒序放到最前面同时取反 */
            for (int i = 49; i >= 42; i--) {
                keyAndIv[index] = ~(setMultimediaFileEncryptionKey.getKeyContent()[i]);
                index++;
            }
            /* 再把0-7这10位放进去，这前面16位就是IV了 */
            memcpy(keyAndIv + index, setMultimediaFileEncryptionKey.getKeyContent(), 10);
            index += 10;
            /* 最后取出12~39同时取反 */
            for (int i = 12; i <= 41; i++) {
                keyAndIv[index] = ~(setMultimediaFileEncryptionKey.getKeyContent()[i]);
                index++;
            }
            /* 计算一下校验值，看看是不是一致 */
            uint16_t mCrcCheckCode = CodeUtils::getInstance().generateCrc16(keyAndIv, sizeof(keyAndIv));
            if (crcCheckCode == mCrcCheckCode) {
                printf("check keyAndIv success!  %s  \n",
                       XuString::getInstance().byteArrayToString(keyAndIv, sizeof(keyAndIv)).c_str());

                /* 校验值正确，直接把密文写到文件里 */
                XuFile::getInstance().writeFile(MULTIMEDIAFILEENCRYPTIONKEYPATH,
                                                const_cast<uint8_t *>(setMultimediaFileEncryptionKey.getKeyContent()),
                                                setMultimediaFileEncryptionKey.getKeyLen());

                /* 更新一下现在的IV */
                G3_Configuration::getInstance().setMultimediaFilesEncryptIv(keyAndIv, 16);
                /* 更新一下现在的Key */
                G3_Configuration::getInstance().setMultimediaFilesEncryptKey(keyAndIv + 16, 32);


                ret = 0;

            } else {
                /* 校验值错误，不可以使用 */
                printf("check keyAndIv failed!  %s  \n",
                       XuString::getInstance().byteArrayToString(keyAndIv, sizeof(keyAndIv)).c_str());
            }


        }


        return ret;
    }

    /**
     * 对被加密过的多媒体文件加密所使用的密钥信息进行解密
     */
    void MainUnitManagr::getMultimediaFilesEncryptKey() {
        uint8_t fileData[50] = {0x00};
        /* 打开文件读出密文 */
        int readLen = XuFile::getInstance().readFile(MULTIMEDIAFILEENCRYPTIONKEYPATH, fileData, sizeof(fileData));
        /* 先检查一下是不是50个字节 */
        if (readLen == 50) {
            /* 先拿出校验码 */
            uint16_t crcCheckCode = CodeUtils::getInstance().BbToUint16(fileData + 10);
            /* 再拿出正确顺序的内容 */
            uint8_t keyAndIv[48] = {0X00};
            int index = 0;
            /* 先把最后面8位倒序放到最前面同时取反 */
            for (int i = 49; i >= 42; i--) {
                keyAndIv[index] = ~(fileData[i]);
                index++;
            }
            /* 再把0-7这10位放进去，这前面16位就是IV了 */
            memcpy(keyAndIv + index, fileData, 10);
            index += 10;
            /* 最后取出12~39同时取反 */
            for (int i = 12; i <= 41; i++) {
                keyAndIv[index] = ~(fileData[i]);
                index++;
            }
            /* 计算一下校验值，看看是不是一致 */
            uint16_t mCrcCheckCode = CodeUtils::getInstance().generateCrc16(keyAndIv, sizeof(keyAndIv));
            if (crcCheckCode == mCrcCheckCode) {
                printf("get check keyAndIv success!  %s  \n",
                       XuString::getInstance().byteArrayToString(keyAndIv, sizeof(keyAndIv)).c_str());
                /* 更新一下现在的IV */
                G3_Configuration::getInstance().setMultimediaFilesEncryptIv(keyAndIv, 16);
                /* 更新一下现在的Key */
                G3_Configuration::getInstance().setMultimediaFilesEncryptKey(keyAndIv + 16, 32);
            } else {
                /* 校验值错误，不可以使用 */
                printf("get check keyAndIv failed!  %s  \n",
                       XuString::getInstance().byteArrayToString(keyAndIv, sizeof(keyAndIv)).c_str());
            }
        }
    }

    void MainUnitManagr::onGetDetectInof_CameraStatus(G3DetectionResult &detectionInfo) {
        /* 看看是不是需要锁掉报警输出 */
        if (!G3_Configuration::getInstance().getLockFunction().detectOut) {
            communicationUnitManager.setDetectInfo_CameraStatus(detectionInfo);
        }

        mediaUnitManagr.setDetectInfo_CameraStatus(detectionInfo);
        uiUnitManager.setDetectInfo_CameraStatus(detectionInfo);
    }

    void MainUnitManagr::getCameraInputTypeFilterInfo() {
        uint8_t readBuf[1] = {0x00};
        int readLen = XuFile::getInstance().readFile(CAMERAINPUTTYPEFILTERINFOFILEPATH, readBuf, sizeof(readBuf));
        if (readLen > 0) {
            printf("getCameraInputTypeFilterInfo=%d  \n", readBuf[0]);
            /* 镜头输入过滤器 */
            Camera_Input_Type_Filter cameraInputTypeFilter = {};
            /* 第一个字节为过滤器的内容，可以直接cpy给结构体 */
            (void) memcpy(&cameraInputTypeFilter, readBuf, 1);
            /* 直接把结构体设置给全局 */
            G3_Configuration::getInstance().setCameraInputTypeFilter(cameraInputTypeFilter);
        } else {
            printf("read camera input type filter failed ! \n");
        }
    }

    int
    MainUnitManagr::onGetSetCameraInputTypeFilterConfig(SetCameraInputTypeFilterConfig &setCameraInputTypeFilterConfig,
                                                        const char *ip, const int port) {
        /* 直接设置 */
        bool setRet = setCameraInputTypeFilterInfo(setCameraInputTypeFilterConfig.getCameraInputTypeFilterConfig());
        /* 刷新一下 */
        getCameraInputTypeFilterInfo();
        return setRet ? 0x00 : 0x01;
    }

    bool MainUnitManagr::setCameraInputTypeFilterInfo(const uint8_t filterInfo) {
        bool ret = false;
        int writeRet = XuFile::getInstance().writeFile(CAMERAINPUTTYPEFILTERINFOFILEPATH,
                                                       const_cast<uint8_t *>(&filterInfo), 1);
        if (writeRet > 0) {
            ret = true;
        } else {
            printf("MainUnitManagr::setCameraInputTypeFilterInfo  can not open %s \n",
                   CAMERAINPUTTYPEFILTERINFOFILEPATH);
        }
        return ret;
    }

    void MainUnitManagr::onGetIRLampSwichContr(const bool lampSwich) {
        curIRLampSwichStatus = lampSwich;
        peripheralUnitManager.swichCameraIRLamp(curIRLampSwichStatus);
        mediaUnitManagr.setIRLampSwichStatus(curIRLampSwichStatus);
    }

    void MainUnitManagr::onGetChangeRS485Baudrate(const int baudrate) {
        peripheralUnitManager.setRS485Baudrate(baudrate);
    }

    void MainUnitManagr::onGetDetectInfo_BarCode(G3DetectionResult &detectionInfo,
                                                 std::vector<BarCodeInfo> &barCodeInfoList) {
        communicationUnitManager.setDetectInfo_BarCode(detectionInfo, barCodeInfoList);
    }

    int MainUnitManagr::onGetSetCameraReduceEffectInfoFromSocket(SetCameraReduceEffectInfo &setCameraReduceEffectInfo,
                                                                 const char *ip, const int port) {
        /* 直接设置 */
        bool setRet = saveCameraReduceEffectInfo(setCameraReduceEffectInfo.getCameraReduceEffectSwitch(),setCameraReduceEffectInfo.getImgZoomRate(),setCameraReduceEffectInfo.getReserve());
        /* 刷新一下 */
        getCameraReduceEffectInfo();
        return setRet ? 0x00 : 0x01;
    }

    bool MainUnitManagr::saveCameraReduceEffectInfo(const CameraReduceEffectInfo &reduceEffectSwitch,
                                                    const float imgzoomRate, const uint8_t *otherData) {
        bool ret = false;
        /* 检查下缩放率是否大于1 */
        if(imgzoomRate > 1){
            /* 缩放比大于1，不合法，直接失败 */
            ; //not to do

        }else{
            uint8_t fileData[10];
            memset(fileData,0x00, sizeof(fileData));
            /* 整理开关 */
            (void) memcpy(fileData,&reduceEffectSwitch,1);
            /* 整理缩放比 */
            fileData[1] = imgzoomRate * 100;
            /* 整理预留字节 */
            (void) memcpy(fileData+2,&reduceEffectSwitch,8);

            int writeRet = XuFile::getInstance().writeFile(CAMERAREDUCEERRECTINFOFILEPATH,
                                                           fileData, sizeof(fileData));
            if (writeRet > 0) {
                ret = true;
            } else {
                printf("MainUnitManagr::saveCameraReduceEffectInfo  can not open %s \n",
                       CAMERAREDUCEERRECTINFOFILEPATH);
            }
        }



        return ret;
    }

    void MainUnitManagr::getCameraReduceEffectInfo() {
        uint8_t readBuf[10] = {0x00};
        int readLen = XuFile::getInstance().readFile(CAMERAREDUCEERRECTINFOFILEPATH, readBuf, sizeof(readBuf));
        if (readLen >= sizeof(readBuf)) {
            /* 降效开关 */
            CameraReduceEffectInfo cameraReduceEffectInfo = {false, false, false, false, 0};
            (void) memcpy(&cameraReduceEffectInfo, readBuf, 1);
            G3_Configuration::getInstance().setCurCameraReduceEffectInfo(cameraReduceEffectInfo);
            /* 缩放比例 */
            G3_Configuration::getInstance().setReduceEffectImgZoomRate(readBuf[1] / 100.0f);

        } else {
            printf("read camera reduce effect failed ! \n");
        }
    }

    bool MainUnitManagr::setStaticIPAddress(const char *ipaddr) {
        /* 设置静态IP */
        std::string ipstr = "";
        ipstr.append("ifconfig eth0 ");
        ipstr.append(ipaddr);
        ipstr.append(" netmask *************");
        XuShell::getInstance().runShellWithTimeout(ipstr.c_str());
        /* 再设置一下路由，才能访问外网。（因为ifconfig之后只会剩下一个局域网的路由） */
        std::string setRoute = "route add default gw ";
        std::vector<std::string> ipFragments = XuString::getInstance().split(ipaddr,".");
        if(ipFragments.size() == 4){
            setRoute.append(ipFragments[0]);
            setRoute.append(".");
            setRoute.append(ipFragments[1]);
            setRoute.append(".");
            setRoute.append(ipFragments[2]);
            setRoute.append(".1 dev eth0");
            XuShell::getInstance().runShellWithTimeout(setRoute.c_str());
        }else{
            XuLog::getInstance().log(XuLog::LOG_LEVEL_ERROR, "SETSTATICIPADDR") << "get gw failed! ip=" << ipaddr << XU_LOG_END;
        }
        // 设置DNS服务器地址
        std::string ndsCmd = "echo -e 'nameserver ";
        ndsCmd.append("***************");
        ndsCmd.append("\nnameserver ");
        ndsCmd.append("*********");
        ndsCmd.append("' > /etc/resolv.conf && sync");
        // 运行命令
        XuShell::getInstance().runShellWithTimeout(ndsCmd.c_str());


        return false;
    }

    int
    MainUnitManagr::onGetSetProductionTestingSwitchFromSocket(SetProductionTestingSwitch &setProductionTestingSwitch,
                                                              const char *ip, const int port) {
        int ret = -1;
        /* 根据开关状态决定怎么做 */
        switch (setProductionTestingSwitch.getProductionTestingSwitch()) {
            case SetProductionTestingSwitch::SWITCH_STATUS_OFF:{
                XuShell::getInstance().runShellWithTimeout("rm /tmp/visptm.txt && sync");
                ret = 0;
            }
                break;
            case SetProductionTestingSwitch::SWITCH_STATUS_ON:{
                XuShell::getInstance().runShellWithTimeout("touch /tmp/visptm.txt && sync");
                ret = 0;
            }
                break;
            default:{
                ret = -1;
            }
                break;
        }

        /* 准备重启 */
        XuShell::getInstance().killallG3sofeward("change production testing mode");

        return ret;
    }

    int MainUnitManagr::onGetStartDeviceUpgradeFromNDS(const int fileType, const std::string &fileUrl) {
        int ret = -1;
        if (!upgradeUnitManager.isUpgrading()) {
            ret = upgradeUnitManager.initByHttp(fileType, fileUrl, *this);
            if (ret == 0) {
                if (threadPool.available() > 0) {
                    threadPool.start(upgradeUnitManager);
                } else {
                    ret = -1;
                }
            } else {
                ret = -1;
            }
        } else {
            ret = -1;
        }
        return ret;
    }

    int MainUnitManagr::onJpgFileGet(const JPGFileInfo &jpgFileInfo) {
        if (hasTFcard) {
            /* 如果不是DVR，就告诉通信模块 */
            if (jpgFileInfo.getFileType() != EVENT_UNKNOW) {
                communicationUnitManager.setNewJPGFile(jpgFileInfo);
            }
        }
        return 0;
    }

    int MainUnitManagr::onGetTCPRealviewOptFromSocket(TCPRealviewOpt &tcpRealviewOpt, const char *ip, const int port) {
       int ret = 0;
        switch (tcpRealviewOpt.getCameraId()) {
            case CAMERA_ID_1: {
                isStartTCPRealViewMode_camera1 = tcpRealviewOpt.getOpt();
            }
                break;
            case CAMERA_ID_2: {
                isStartTCPRealViewMode_camera2 = tcpRealviewOpt.getOpt();
            }
                break;
            case CAMERA_ID_3: {
                isStartTCPRealViewMode_camera3 = tcpRealviewOpt.getOpt();
            }
                break;
            case CAMERA_ID_4: {
                isStartTCPRealViewMode_camera4 = tcpRealviewOpt.getOpt();
            }
                break;
            default:{
                ret = -1;
            }
                break;
        }
        return ret;
    }
} // namespace vis
