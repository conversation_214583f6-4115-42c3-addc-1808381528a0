//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/24.
//


#include "UnitStatusListenerCallback.h"


void UnitStatusListenerCallback::onHighTemperatureOn(int temperature) {

}

void UnitStatusListenerCallback::onHighTemperatureOff(int temperature) {
}

void UnitStatusListenerCallback::onGetCPUTemperature(float temperature) {
}

void UnitStatusListenerCallback::onGetUnitWorkingAbnormally(const UNIT_NUM unitNum) {

}

void UnitStatusListenerCallback::onGetIRLampSwichContr(const bool lampSwich) {

}

