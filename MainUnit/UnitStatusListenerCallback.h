//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/24.
//

#ifndef VIS_G3_SOFTWARE_UNITSTATUSLISTENERCALLBACK_H
#define VIS_G3_SOFTWARE_UNITSTATUSLISTENERCALLBACK_H

#include <cstdio>
#include "G3_Configuration.h"

class UnitStatusListenerCallback {

public:
    /**
     * 已经进入高温状态
     *
     * @param temperature ： 当前温度
     *
     * */
    virtual void onHighTemperatureOn(int temperature);

    /**
     * 已经解除高温状态
     *
     * @param temperature ： 当前温度
     *
     * */
    virtual void onHighTemperatureOff(int temperature);

    /**
     * 获取到当前的CPU温度
     *
     * @param temperature ： 当前温度
     *
     * */
    virtual void onGetCPUTemperature(float temperature);

    /**
     * 获取到有功能模块工作异常
     *
     * @param unitNum ： 功能模块代码
     */
    virtual void onGetUnitWorkingAbnormally(const UNIT_NUM unitNum);


    /**
     * 获取到镜头的红外灯的开关状态控制指令
     *
     * @param unitNum ： 红外灯的开关状态  ture:开  false：关
     */
    virtual void onGetIRLampSwichContr(const bool lampSwich);

private:

};


#endif //VIS_G3_SOFTWARE_UNITSTATUSLISTENERCALLBACK_H
