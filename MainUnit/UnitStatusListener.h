//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/22.
//

#ifndef VIS_G3_SOFTWARE_UNITSTATUSLISTENER_H
#define VIS_G3_SOFTWARE_UNITSTATUSLISTENER_H

#include <Poco/Runnable.h>
#include "MediaUnit/MediaUnitManagr.h"
#include "PeripheralUnit/PeripheralUnitManager.h"
#include "DetectUnit/DetectUnitManager.h"
#include "CommunicationUnit/CommunicationUnitManager.h"
#include "UIUnit/UIUnitManager.h"
#include "utils/XuShell.h"
#include "UnitStatusListenerCallback.h"
#include "UpgradeFromTFCard.h"
#include "Ping.h"

namespace vis {


    class UnitStatusListener : public Poco::Runnable {
    public:


        int init(UnitStatusListenerCallback &unitStatusListenerCallback, uint64_t interval,
                 PeripheralUnitManager &peripheralUnitManager, MediaUnitManagr &mediaUnitManagr,
                 DetectUnitManager &detectUnitManager, CommunicationUnitManager &communicationUnitManager,
                 UIUnitManager &uiUnitManager);

        void run() override;

        float getCpuTemperature() const;

        /**
         * 设置ADC电压
         *
         * @param voltage : ADC电压
         */
        void setADCVoltage(float voltage);

        /**
         * 设置光敏电阻电压
         *
         * @param voltage : 光敏电阻电压
         */
        void setPhotoresistorVoltage(float voltage);

    private:

        const char *CONFIG_FILE_PATH = "/mnt/sdcard/ht.txt";

        int startHightTemperatureValue = 115000;
        int stopHightTemperatureValue = 105000;

        UnitStatusListenerCallback *callback;

        uint64_t checnkInterval;

        PeripheralUnitManager *peripheralUnit;

        MediaUnitManagr *mediaUnit;

        DetectUnitManager *detectUnit;

        CommunicationUnitManager *communicationUnit;

        UIUnitManager *uiUnit;

        const char *G3_TEMPERATURE_FILE_PATH = "/userdata/media/G3_Temperature.log";

        bool needStopUnitStatusListener = true;

        bool hasHightTemperature = false;

        float cpuTemperature = -1;

        /* 场景是否过暗 */
        bool isDim = false;

        /* adc电压 */
        float adcVoltage = 0;
        /* 光敏电阻电压 */
        float photoresistorVoltage = 0;

        /* 通过TF卡升级软件的工具 */
        UpgradeFromTFCard upgradeFromTfCard;



        /* safepass状态的log的存放路径 */
        const char *SAFEPASS_LOG_FILE_PATH = "/userdata/media/vispectLog/safepassLog";
        /* sos状态的log的存放路径 */
        const char *SOS_LOG_FILE_PATH = "/userdata/media/vispectLog/sosLog";
        /* safepass的log文件和sos的log问题的最大大小 */
        const int MAX_SAFEPASSLOG_OR_SOSLOG_FILE_LEN = 1024*1024*50;
        /* 上次的safepass状态 */
        bool lastSafepassOn = false;
        /* 上次的sos状态 */
        bool lastSOSOn = false;
        /* 检查的次数 */
        int checkSum = 0;


        /**
         * 检查一下safepass状态和sos状态
         */
        void checkSafepassAndSOS();

        /**/
        bool hasInternet();


    };
} // namespace vis

#endif //VIS_G3_SOFTWARE_UNITSTATUSLISTENER_H
