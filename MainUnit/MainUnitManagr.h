//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/21.
//

#ifndef G3SOFTWARE_MAINUNITMANAGR_H
#define G3SOFTWARE_MAINUNITMANAGR_H

#include "MediaUnit/MultimediaDataCallback.h"
#include <Poco/ThreadPool.h>
#include "PeripheralUnit/PeripheraDataCallback.h"
#include "MediaUnit/MediaUnitManagr.h"
#include "PeripheralUnit/PeripheralUnitManager.h"
#include "DetectUnit/DetectUnitManager.h"
#include "CommunicationUnit/CommunicationUnitManager.h"
#include "UIUnit/UIUnitManager.h"
#include "UnitStatusListener.h"
#include "UpgradeUnit/UpgradeUnitManager.h"
#include "UpgradeUnit/UpgradeCallback.h"
#include "UnitStatusListenerCallback.h"

#include "XuFileDownloadUtils.h"

#include "RTMPPusher.h"



namespace vis {
    class MainUnitManagr
            : public MultimediaDataCallback,
              public PeripheraDataCallback,
              public DetectDataCallback,
              public CommunicationDataCallback,
              public UnitStatusListenerCallback,
              public UpgradeCallback,
              public XuFileDownloadCallback {
    public:
        MainUnitManagr();

        ~MainUnitManagr();



        /**
         * 获取所有DVR文件
         */
        void getDvrMp4FileList();
        /**
         * 获取所有报警视频文件
         */
        void getWarMp4FileList();
        /**
         * 获取功能锁信息
         */
        void getFunctionLockInfo();
        /**
         * 获取镜头输入类型过滤器信息
         */
        void getCameraInputTypeFilterInfo();

        /**
         * 启动函数
         *
         * @return 0：成功  其他：失败
         */
        int start();

        /**
         * 获取镜头报警开关的状态
         */
        void checkCameraAlarmSwitchStatusFromGPIO();

        /**
         * 检查IO口所代表的各种信号
         */
        void checkSignalFromGPIO();

        /**
         * 配置RS485外设显示参数
         *
         * @param displayMode ：显示模板
         * @param iconMode ：显示图标子集编号
         * @return 结果  0：成功  其他：失败
         */
        int setDispalyParamsToRS485(const int displayMode,const int iconMode);
        /**
         * 获取多媒体文件加密用的密钥信息
         */
        void getMultimediaFilesEncryptKey();

        /**
         * 设置镜头输入类型过滤器信息
         */
        bool setCameraInputTypeFilterInfo(const uint8_t filterInfo);


        /**
         * 保存镜头降效信息
         *
         * @param reduceEffectSwitch ： 镜头降效开关
         * @param imgzoomRate ： 有效画面的缩放比例
         * @param otherData ： 预留的其他信息
         *
         * @return 是否成功
         */
        bool saveCameraReduceEffectInfo(const CameraReduceEffectInfo &reduceEffectSwitch, const float imgzoomRate, const uint8_t *otherData);

         /**
          * 获取镜头降效信息
          */
        void getCameraReduceEffectInfo();

        int onCameraYUVGet(CameraYUVData &cameraData) override;

        int onCameraH264Get(const CameraH264Data &cameraH264Data) override;

        int onMp4FileGet(const Mp4FileInfo &mp4FileInfo) override;

        void onGetDataFromSocket(const char *ip, const int port, const uint8_t *buf, int len) override;

        void onGetDataFromRS232(const uint8_t *buf, int len) override;

        void onGetDataFromRS485(const uint8_t *buf, int len) override;

        void onGetDataFromCANBus(const can_frame &canFrame) override;

        void onGPIOValueChange(const int portNum, const char *value, const int valueLen) override;

        void onGetEngineSpeedFromCANJ1939(const float engineSpeed) override;

        void onGetSpeedFromCANJ1939(const float speed) override;

        void onGetSetG3Config(SetG3Config &setG3Config, const char *ip, const int port) override;

        void onGetQequestG3Config(QequestG3Config &qequestG3Config, const char *ip, const int port) override;

        void onNeedSendDataToRS485(const uint8_t *buf, const int len) override;

        void onGetPeripheralVersion(const uint8_t peripheral, const char *softwarVersion,
                                    const char *protocoVersion) override;

        void onNeedSendDataToSocket(const char *ip, const int port, const uint8_t *buf, int len) override;

        void onGetStartUDPRealview(StartUDPRealview &startUDPRealview, const char *ip, const int port) override;

        std::vector<PCConfigureDataPacket::MP4FileInfo>
        onGetG3Mp4FileOpt(G3Mp4FileOpt &g3Mp4FileOpt, const char *ip, const int port) override;

        StartG3FileDownloadResult
        onGetStartG3FileDownload(StartG3FileDownload &startG3FileDownload, const char *ip, const int port) override;

        void onHighTemperatureOff(int temperature) override;

        void onHighTemperatureOn(int temperature) override;

        void onGetCPUTemperature(float temperature) override;

        void onGetGPSInfoFromRS485(G3RS485GPSInfo &gpsInfo) override;

        void onGetRealtimeVehicleStatus(RealTimeVehicleStatus &vehicleStatus, const char *ip, const int port) override;

        void onGetDataFromUART_MCU(const uint8_t *buf, int len) override;

        void onNeedSendDataToMCUUART(const uint8_t *buf, const int len) override;

        void onNeedPlaySound(const char *soundFilePath, bool isForce) override;

        void onNeedPlaySound_JKFZ(const char *soundFilePath, bool isForce) override;

        int onShowYUVGet(CameraYUVData &cameraData) override;

        void onGetPeripheralVersionFromMCUUART(G3AndMCUUartRespondPeripheralVersion &peripheralVersion) override;

        void onGetGSensorData(float value_x, float value_y, float value_z) override;

        void onGetADCVoltage(float voltage) override;

        void onGetUpgradeResult(const int fileType, const int result, const char *ip, const int port) override;

        void
        onGetStartG3DeviceUpgrade(StartG3DeviceUpgrade &startG3DeviceUpgrade, const char *ip, const int port) override;

        void onGetDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) override;

        void onGetObjectInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) override;

        int onGetSetG3SystemTime(SetG3SystemTime &setG3SystemTime, const char *ip, const int port) override;

        void onGetDSMInfo(G3DetectionResult &detectionInfo) override;

        void onGetObjectInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) override;

        void onGetDownloadResult(std::string filePath, const int result, const char *ip, const int port) override;

        void onGetStartG3DebugMode(StartG3DebugMode &startG3DebugMode, const char *ip, const int port) override;

        void onGetSetFunctionLock(SetFunctionLock &setFunctionLock, const char *ip, const int port) override;

        void onGetObjectInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) override;

        void onGetRestartMRV220(RestartG3 &restartG3, const char *ip, const int port) override;

        int onGetStartUDPRealviewSeparate(StartUDPRealview_Separate &startUdpRealview, const char *ip, const int port) override;

        void onGetUnitWorkingAbnormally(const UNIT_NUM unitNum) override;

        void onGetRealtimeVehicleStatusFromMCUUART(G3AndMCUUartVehicleRealTimeStatus &vehicleStatus) override;

        void onGetProductionTestResultFromMCUUART(G3AndMCUUartPTWrokResult &ptWrokResult) override;

        void onGetDataFromMQTT(const int mqttType, const char *topicName, const int topicNameLen, const char *payload, const int payloadLen) override;

        void onGetMQTTConnectStatusChange(const bool isConnected) override;

        int onNeedSendDataToNDSMqtt(std::string &strData) override;

        int onGetRTMPPushOpt(const std::string url, const int cameraId, const int opt) override;

        void onGetUpgradeResultFromRS485(G3RS485UpgradeResult &upgradeResult) override;

        void onGetSetDispalyParamsResultFromRS485(G3RS485SetDispalyParamsResult &setDispalyParamsResult) override;

        int onGetSetPeripheralDisplayParams(SetPeripheralDisplayParams displayParams, const char *ip, const int port) override;

        void onGetGetG3AndG4DeviceVersion(GetG3AndG4DevicesVersion &getG3AndG4DevicesVersion, const char *ip, const int port) override;

        void onGetStopButtonStatusFromMODBUS(vis::MODBUS_StopButtonStatus &stopButtonStatus) override;

        void onNeedSendDataToRS232(const uint8_t *buf, const int len) override;

        void onGetPeripheralUpgradeResultFromMCUUART(G3AndMCUUartPeripheralUpgradeResult &peripheralUpgradeResult) override;

        StartMRV220LogFileDownloadResult onGetStartMRV220LogFileDownload(StartMRV220LogFileDownload &startMrv220LogFileDownload, const char *ip, const int port) override;

        int onGetSetMultimediaFileEncryptionKey(SetMultimediaFileEncryptionKey &setMultimediaFileEncryptionKey, const char *ip, const int port) override;

        void onGetDetectInof_CameraStatus(G3DetectionResult &detectionInfo) override;

        int onGetSetCameraInputTypeFilterConfig(SetCameraInputTypeFilterConfig &setCameraInputTypeFilterConfig, const char *ip, const int port) override;

        void onGetIRLampSwichContr(const bool lampSwich) override;

        void onGetChangeRS485Baudrate(const int baudrate) override;

        void onGetDetectInfo_BarCode(G3DetectionResult &detectionInfo, std::vector<BarCodeInfo> &barCodeInfoList) override;

        int onGetSetCameraReduceEffectInfoFromSocket(SetCameraReduceEffectInfo &setCameraReduceEffectInfo, const char *ip, const int port) override;

        int onGetSetProductionTestingSwitchFromSocket(SetProductionTestingSwitch &setProductionTestingSwitch, const char *ip, const int port) override;

        int onGetStartDeviceUpgradeFromNDS(const int fileType, const std::string &fileUrl) override;

        int onJpgFileGet(const JPGFileInfo &jpgFileInfo) override;

        int onGetTCPRealviewOptFromSocket(TCPRealviewOpt &tcpRealviewOpt, const char *ip, const int port) override;

    private:
        /* 用来跑检测跟软件更新的线程池 */
        Poco::ThreadPool threadPool;
        /* 用来跑MP4文件下载的线程池 */
        Poco::ThreadPool mp4FileDownthreadPool;
        /* 用来跑LOG文件下载的线程池 */
        Poco::ThreadPool logFileDownthreadPool;
        /* 外设模块的实例 */
        PeripheralUnitManager peripheralUnitManager;
        /* 编码模块的实例 */
        MediaUnitManagr mediaUnitManagr;
        /* 算法识别识别模块的实例 */
        DetectUnitManager detectUnitManager;
        /* 通信模块的实例 */
        CommunicationUnitManager communicationUnitManager;
        /* UI模块的实例 */
        UIUnitManager uiUnitManager;
        /* 设备状态监听工具的实例 */
        UnitStatusListener unitStatusListener;
        /* 软件更新工具的实例 */
        UpgradeUnitManager upgradeUnitManager;
        /* 是否在产测模式 */
        bool isVISPtm = false;
        /* 是否插入了TF卡且可用 */
        bool hasTFcard = false;
        /* 产测模式下测试语音的播放计数 */
        int tesplaySum = 0;


        /* 是不是高温了 */
        bool isHighTemperature = false;

        /* 是不是要通过UDP传输镜头1实景画面 */
        bool isStartUDPRealViewMode_camera1 = false;
        /* 是不是要通过UDP传输镜头2实景画面 */
        bool isStartUDPRealViewMode_camera2 = false;
        /* 是不是要通过UDP传输镜头3实景画面 */
        bool isStartUDPRealViewMode_camera3 = false;
        /* 是不是要通过UDP传输镜头4实景画面 */
        bool isStartUDPRealViewMode_camera4 = false;

        /* 是不是要通过TCP传输镜头1实景画面 */
        bool isStartTCPRealViewMode_camera1 = false;
        /* 是不是要通过TCP传输镜头2实景画面 */
        bool isStartTCPRealViewMode_camera2 = false;
        /* 是不是要通过TCP传输镜头3实景画面 */
        bool isStartTCPRealViewMode_camera3 = false;
        /* 是不是要通过TCP传输镜头4实景画面 */
        bool isStartTCPRealViewMode_camera4 = false;


        /* IO 110跟IO 111 的电平  用以区分槽位 从而制定IP跟端口   两个都为0就是槽位1  两个都为1就是槽位2 */
        int io_level_110 = -1;      //  (GPIN3)
        int io_level_111 = -1;      //(GPIN2)
        int io_level_113 = -1;
        int io_level_115 = -1;     //(GPIN1)
        /* 加速度传感器的值 */
        float gSensor_x;
        float gSensor_y;
        float gSensor_z;
        /* adc电压 */
        float adcVoltage = 0;
        /* 光敏电阻读数 */
        float photoresistorVoltage = 0;

        /* 是否已经配置要了IP跟端口 */
        bool isSetNetworkDone = false;


        /* 下载MP4文件用的客户端 */
        XuFileDownloadUtils mp4FileDownloadUtils[3];
        /* MP4文件下载的端口列表（只有三个端口意味着只能同时下载三个） */
        int mp4FileDownTcpPortList[3] = {6668, 6669, 7000};
        /* 打开调式模式的密码 */
        uint8_t myPwd[8] = {0x00};

        /* 下载Log文件用的客户端 */
        XuFileDownloadUtils logFileDownloadUtils[1];
        /* MP4文件下载的端口列表（只有三个端口意味着只能同时下载三个） */
        int logFileDownTcpPortList[1] = {6667};

        /* RS232的safepass按钮是否被按下 */
        bool safepassOn_RS232button = false;
        /* RS232的sos按钮是否被按下 */
        bool sosOn_RS232button = false;

        /* 当前IR灯开关的状态 */
        bool curIRLampSwichStatus = false;

        /* CPU序列号 */
        char cpuSerialStr[25] = {0x00};





        bool setStaticIPAddress(const char* ipaddr);





    };

} // namespace vis
#endif //G3SOFTWARE_MAINUNITMANAGR_H
