//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/22.
//

#include "UnitStatusListener.h"
#include <boost/algorithm/string/split.hpp>
#include "G3_Configuration.h"
#include "XuLog.h"
#include <curl/curl.h>

namespace vis {
    void UnitStatusListener::run() {
        /* 设置线程名 */
        pthread_setname_np(pthread_self(), "StatusListener");


        /* 在这里等5秒钟再启动检测，因为可能有一些模块还没启动完成 */
        sleep(5);

        /* CPU温度的读取路径 */
        std::string checkTemperatureCmd = "cat /sys/class/thermal/thermal_zone0/temp";
        char timeStr[25];
        while (!needStopUnitStatusListener) {
            /* 获取下温度 */
            std::string result = XuShell::getInstance().runShellWithTimeout(checkTemperatureCmd,2000);

            /* 先判断是否高温了 */
            int temperature = std::stoi(result);
            cpuTemperature = temperature / 1000;
            callback->onGetCPUTemperature(cpuTemperature);
            if (hasHightTemperature) {
                if (temperature <= stopHightTemperatureValue) {
                    hasHightTemperature = false;
                    callback->onHighTemperatureOff(temperature);
                    XuLog::getInstance().log(XuLog::LOG_LEVEL_WARNING,typeid(*this).name()) << "close hight temperature!  temperature=" << temperature << XU_LOG_END;
                }
            } else {
                if (temperature >= startHightTemperatureValue) {
                    hasHightTemperature = true;
                    callback->onHighTemperatureOn(temperature);
                    XuLog::getInstance().log(XuLog::LOG_LEVEL_WARNING,typeid(*this).name()) << "now is hight temperature!  temperature=" << temperature << XU_LOG_END;
                }
            }
            G3_Configuration::getInstance().setHasHightTemperature(hasHightTemperature);


            /* 如果保存温度的文件存在  则保存下到文件里 */
            if (XuFile::getInstance().fileExists(G3_TEMPERATURE_FILE_PATH)) {
                /* 添加时间等其他信息 */
                XuString::getInstance().getStrFormTime(timeStr, 25, "%Y-%m-%d %H:%M:%S", false);
                result.insert(0, "             temperature:");
                result.insert(0, timeStr);
                result.append("\n");
                XuFile::getInstance().saveStrToFile(G3_TEMPERATURE_FILE_PATH, result, false);
            }
//            /* 检查下电压 */
//            if ((fabs(adcVoltage + 0.1 - 10) > 0.00001 && (adcVoltage + 0.1) > 10) && fabs(adcVoltage + 0.1 - 15) > 0.00001 && (adcVoltage + 0.1) < 15){
//                /* 电压在10-15之间，算正常 */
//                ; //not to do
//            }else  if ((fabs(adcVoltage + 0.1 - 22) > 0.00001 && (adcVoltage + 0.1) > 22) && fabs(adcVoltage + 0.1 - 26) > 0.00001 && (adcVoltage + 0.1) < 26){
//                /* 电压在22-26之间，也算正常 */
//                ; //not to do
//            }else{
//                /* 其他情况都算电压异常 */
//                XuLog::getInstance().log(XuLog::LOG_LEVEL_WARNING,typeid(*this).name()) << "adcVoltage error!  adcVoltage=" << adcVoltage << XU_LOG_END;
//            }


            /* 先检查下media */
            int mediaWorkStatus = mediaUnit->getWorkStaus();
            if(mediaWorkStatus != 0){
                callback->onGetUnitWorkingAbnormally(UNIT_NUM_MEDIAUNIT);
            }
            /* 检查下CommunicationUnit */
            int communicationStatus = communicationUnit->getWorkStaus();
            if(communicationStatus != 0){
                callback->onGetUnitWorkingAbnormally(UNIT_NUM_COMMUNICATIONUNIT);
            }
            /* 检查下DetectUnit */
            int detectStatus = detectUnit->getWorkStaus();
            if(detectStatus != 0){
                callback->onGetUnitWorkingAbnormally(UNIT_NUM_DETECTUNIT);
            }
            /* 检查下PeripheralUnit */
            int peripheralStatus = peripheralUnit->getWorkStaus();
            if(peripheralStatus != 0){
                callback->onGetUnitWorkingAbnormally(UNIT_NUM_PERIPHERALUNIT);
            }
            /* 检查下显示模块的 */
            int uiShowlStatus = uiUnit->getWorkStaus();
            if(uiShowlStatus != 0){
                callback->onGetUnitWorkingAbnormally(UNIT_NUM_UIUNIT);
            }

            /* 如果处于G4模式下，检查一下LINK状态  没LINK上就reboot  这是个临时措施，所有放在这里实现  */
            if (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4_UNIT) {
                std::string linkResult = XuShell::getInstance().runShellWithTimeout("ethtool eth0",2000);
                /* 把link状态记录下来 */
//                XuLog::getInstance().log(XuLog::LOG_LEVEL_DEBUG,typeid(*this).name()) << linkResult << XU_LOG_END;
//            printf("linkResult=%s   \n",linkResult.c_str());
                if (linkResult.find("Link detected: yes", 0) != std::string::npos) {
                    printf("eth0 is linked now!   \n");
                } else {
                    printf("eth0 is not link now!   \n");
                    for (int i = 0; i < 25; i++) {
                        /* 闪一下那个LED灯 */
                        XuShell::getInstance().runShellWithTimeout("echo 0 > /sys/devices/platform/leds/leds/system_led/brightness",200);
                        usleep(100 * 1000);
                        XuShell::getInstance().runShellWithTimeout("echo 1 > /sys/devices/platform/leds/leds/system_led/brightness",200);
                        usleep(100 * 1000);
                    }


                    XuShell::getInstance().runShellWithTimeout("sync");
                    XuShell::getInstance().runShellWithTimeout("reboot");
                }
            }

//
//            /* 如果是正常link，那么ping一下177,然后记录下来 */
//            bool pingRet = pingUtils.ping("192.168.8.177",1000);
//            XuLog::getInstance().log(XuLog::LOG_LEVEL_DEBUG,typeid(*this).name()) << (pingRet ? "ping 192.168.8.177 success!" : "ping 192.168.8.177 failed!") << XU_LOG_END;
//            /* ping一下另一块MRV220,然后记录下来 */
//            pingRet = pingUtils.ping(
//                    const_cast<char *>((G3_Configuration::getInstance().getSlotPosition() == 1) ? "192.168.8.188"
//                                                                                                : "192.168.8.189"), 1000);
//
//            XuLog::getInstance().log(XuLog::LOG_LEVEL_DEBUG,typeid(*this).name()) << (pingRet ? "ping other MRV220 success!" : "ping other MRV220 failed!") << XU_LOG_END;

//            /* 读取一下内存信息 */
//            std::string meminfoRet = XuShell::getInstance().runShellWithTimeout("cat /proc/meminfo",2000);
//            XuLog::getInstance().log(XuLog::LOG_LEVEL_DEBUG,typeid(*this).name()) << meminfoRet << XU_LOG_END;

            /* 闪一下那个LED灯 */
            XuShell::getInstance().runShellWithTimeout("echo 0 > /sys/devices/platform/leds/leds/system_led/brightness",100);
            sleep(1);
            XuShell::getInstance().runShellWithTimeout("echo 1 > /sys/devices/platform/leds/leds/system_led/brightness",100);

            /* 检查下TF是否有更新的 */
            upgradeFromTfCard.startUpgrade();

            /* 检查一下safepass和sos状态 */
            checkSafepassAndSOS();


            /* 判断一下是不是V6模式 */
            if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_V6){
                XuLog::getInstance().log(XuLog::LOG_LEVEL_DEBUG,typeid(*this).name()) << "photoresistorVoltage=" << photoresistorVoltage << XU_LOG_END;
                /* 先判断一下是不是处于光线过暗的场景 */
                if(isDim){
                    /* 之前处于过暗的场景，那么判断下现在光线是否已经亮起来了 */
                    if(photoresistorVoltage < G3_Configuration::getInstance().getStopDimLightModeValue()){
                        /* 超过阈值，说明光线没那么暗了，关闭红外灯 */
                        callback->onGetIRLampSwichContr(false);
                        /* 设置目前没有处于光线过暗的状态 */
                        isDim = false;
                    }
                }else{
                    /* 之前没处于过暗的场景，那么判断下现在光线过暗了 */
                    if(photoresistorVoltage > G3_Configuration::getInstance().getStartDimLighModetValue()){
                        /* 超过阈值，说明场景光线过暗了，开启红外灯 */
                        callback->onGetIRLampSwichContr(true);
                        /* 设置目前处于光线过暗的状态 */
                        isDim = true;
                    }
                }
            }
            /* 每隔10个周期检查一下是不是连接着互联网（直接ping百度） */
            if((checkSum % 10) == 0){
                bool internetCon = hasInternet();
                G3_Configuration::getInstance().setInternetConnected(internetCon);
                checkSum = 0;
                printf("internetConnected=%d  \n",internetCon);
            }
            checkSum ++;
            usleep(checnkInterval * 1000);
        }
        pthread_setname_np(pthread_self(), "Finish");
    }

    int UnitStatusListener::init(UnitStatusListenerCallback &unitStatusListenerCallback, uint64_t interval,
                                 PeripheralUnitManager &peripheralUnitManager,
                                 MediaUnitManagr &mediaUnitManagr, DetectUnitManager &detectUnitManager,
                                 CommunicationUnitManager &communicationUnitManager, UIUnitManager &uiUnitManager) {
        checnkInterval = interval;

        peripheralUnit = &peripheralUnitManager;
        mediaUnit = &mediaUnitManagr;
        detectUnit = &detectUnitManager;
        communicationUnit = &communicationUnitManager;
        uiUnit = &uiUnitManager;

        needStopUnitStatusListener = false;

        callback = &unitStatusListenerCallback;

        /* 从TF卡里面读出关闭算法的温度和开启算法的温度 */
        int configFd = open(CONFIG_FILE_PATH, O_RDWR);
        if (configFd) {
            uint8_t readData[500];
            int rl = read(configFd, readData, sizeof(readData));
            if (rl > 0) {
                std::string config = "";
                config.append(reinterpret_cast<const char *>(readData));
//                printf("===============================ht config = %s  =================================\n",config.c_str());
                std::vector<std::string> strlist = XuString::getInstance().split(config, ";");
                if (strlist.size() == 3) {
                    startHightTemperatureValue = atoi(strlist[0].c_str());
                    stopHightTemperatureValue = atoi(strlist[1].c_str());
                }

            } else {
//                printf("===============================can not read ht config  =================================\n");
            }

        } else {
//            printf("===============================did not find ht config %s  =================================\n",CONFIG_FILE_PATH);
        }


        upgradeFromTfCard.init();

        return 0;
    }

    float UnitStatusListener::getCpuTemperature() const {
        return cpuTemperature;
    }

    void UnitStatusListener::setADCVoltage(float voltage) {
        adcVoltage = voltage;
    }

    void UnitStatusListener::checkSafepassAndSOS() {
        /* 先获取下最新的safepass状态 */
        bool safepassOn = G3_Configuration::getInstance().getVehicleStatusFromIo().safepass;
        /* 比对下最新的结果是不是跟上次的结果不同，不同的话就代表发送状态变化，需要记录下来 */
        if(lastSafepassOn != safepassOn){

            /* 获取一下时间 */
            char strTemp[100] = {0x00};
            XuString::getInstance().getStrFormTime(strTemp, sizeof(strTemp), "%Y-%m-%d %H:%M:%S", false);

            std::string safepassLogContent = "";
            /* 添加时间 */
            safepassLogContent.append(strTemp);
            /* 添加提示 */
            safepassLogContent.append("Safepass status changed!   lastStatus=");
            safepassLogContent.append(std::to_string(lastSafepassOn));
            safepassLogContent.append("Safepass status changed!   curStatus=");
            safepassLogContent.append(std::to_string(safepassOn));
            /* 判断一下长度有没有超过阈值，如果超过了，就删除重新创建 */
            if(XuFile::getInstance().getFileLength(SAFEPASS_LOG_FILE_PATH) > MAX_SAFEPASSLOG_OR_SOSLOG_FILE_LEN){
                std::string oldLogFileName = "";
                oldLogFileName.append(SAFEPASS_LOG_FILE_PATH);
                oldLogFileName.append("_old");
                rename(SAFEPASS_LOG_FILE_PATH,oldLogFileName.c_str());
            }
            XuFile::getInstance().writeFile(SAFEPASS_LOG_FILE_PATH, (uint8_t *) safepassLogContent.c_str(), safepassLogContent.size());

        }
        lastSafepassOn = safepassOn;


        /* 获取下最新的sos状态 */
        bool sosOn = G3_Configuration::getInstance().getVehicleStatusFromIo().sos;
        /* 比对下最新的结果是不是跟上次的结果不同，不同的话就代表发送状态变化，需要记录下来 */
        if(lastSOSOn != sosOn){
            /* 获取一下时间 */
            char strTemp[100] = {0x00};
            XuString::getInstance().getStrFormTime(strTemp, sizeof(strTemp), "%Y-%m-%d %H:%M:%S", false);
            std::string sosLogContent = "";
            /* 添加时间 */
            sosLogContent.append(strTemp);
            /* 添加提示 */
            sosLogContent.append("SOS status changed!   lastStatus=");
            sosLogContent.append(std::to_string(lastSOSOn));
            sosLogContent.append("SOS status changed!   curStatus=");
            sosLogContent.append(std::to_string(sosOn));
            /* 判断一下长度有没有超过阈值，如果超过了，就删除重新创建 */
            if(XuFile::getInstance().getFileLength(SOS_LOG_FILE_PATH) > MAX_SAFEPASSLOG_OR_SOSLOG_FILE_LEN){
                std::string oldLogFileName = "";
                oldLogFileName.append(SOS_LOG_FILE_PATH);
                oldLogFileName.append("_old");
                rename(SOS_LOG_FILE_PATH,oldLogFileName.c_str());
            }
            XuFile::getInstance().writeFile(SOS_LOG_FILE_PATH, (uint8_t *) sosLogContent.c_str(), sosLogContent.size());
        }
        lastSOSOn = sosOn;


    }

    void UnitStatusListener::setPhotoresistorVoltage(float voltage) {
        photoresistorVoltage = voltage;
    }

    bool UnitStatusListener::hasInternet() {
        CURL *curl = curl_easy_init();
        curl_easy_setopt(curl, CURLOPT_URL, "https://www.baidu.com");
        //设置不校验服务器证书
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, false);
        CURLcode re = curl_easy_perform(curl);
        bool ok =  (re == CURLE_OK);
        curl_easy_cleanup(curl);
        return ok;
    }
} // namespace vis