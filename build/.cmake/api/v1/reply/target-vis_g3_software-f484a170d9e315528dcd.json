{"artifacts": [{"path": "vis_g3_software"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 284, "parent": 0}, {"command": 1, "file": 0, "line": 8, "parent": 0}, {"command": 2, "file": 0, "line": 292, "parent": 0}, {"command": 2, "file": 0, "line": 293, "parent": 0}, {"command": 2, "file": 0, "line": 294, "parent": 0}, {"command": 2, "file": 0, "line": 295, "parent": 0}, {"command": 2, "file": 0, "line": 296, "parent": 0}, {"command": 2, "file": 0, "line": 297, "parent": 0}, {"command": 2, "file": 0, "line": 298, "parent": 0}, {"command": 2, "file": 0, "line": 299, "parent": 0}, {"command": 2, "file": 0, "line": 300, "parent": 0}, {"command": 2, "file": 0, "line": 301, "parent": 0}, {"command": 2, "file": 0, "line": 302, "parent": 0}, {"command": 2, "file": 0, "line": 303, "parent": 0}, {"command": 2, "file": 0, "line": 304, "parent": 0}, {"command": 2, "file": 0, "line": 305, "parent": 0}, {"command": 2, "file": 0, "line": 307, "parent": 0}, {"command": 2, "file": 0, "line": 308, "parent": 0}, {"command": 2, "file": 0, "line": 309, "parent": 0}, {"command": 2, "file": 0, "line": 310, "parent": 0}, {"command": 2, "file": 0, "line": 311, "parent": 0}, {"command": 2, "file": 0, "line": 312, "parent": 0}, {"command": 2, "file": 0, "line": 313, "parent": 0}, {"command": 2, "file": 0, "line": 314, "parent": 0}, {"command": 2, "file": 0, "line": 315, "parent": 0}, {"command": 2, "file": 0, "line": 316, "parent": 0}, {"command": 2, "file": 0, "line": 317, "parent": 0}, {"command": 2, "file": 0, "line": 318, "parent": 0}, {"command": 2, "file": 0, "line": 319, "parent": 0}, {"command": 2, "file": 0, "line": 320, "parent": 0}, {"command": 2, "file": 0, "line": 322, "parent": 0}, {"command": 2, "file": 0, "line": 323, "parent": 0}, {"command": 2, "file": 0, "line": 324, "parent": 0}, {"command": 3, "file": 0, "line": 94, "parent": 0}, {"command": 3, "file": 0, "line": 95, "parent": 0}, {"command": 3, "file": 0, "line": 96, "parent": 0}, {"command": 3, "file": 0, "line": 97, "parent": 0}, {"command": 3, "file": 0, "line": 98, "parent": 0}, {"command": 3, "file": 0, "line": 99, "parent": 0}, {"command": 3, "file": 0, "line": 100, "parent": 0}, {"command": 3, "file": 0, "line": 101, "parent": 0}, {"command": 3, "file": 0, "line": 102, "parent": 0}, {"command": 3, "file": 0, "line": 103, "parent": 0}, {"command": 3, "file": 0, "line": 104, "parent": 0}, {"command": 3, "file": 0, "line": 105, "parent": 0}, {"command": 3, "file": 0, "line": 106, "parent": 0}, {"command": 3, "file": 0, "line": 107, "parent": 0}, {"command": 3, "file": 0, "line": 108, "parent": 0}, {"command": 3, "file": 0, "line": 109, "parent": 0}, {"command": 3, "file": 0, "line": 110, "parent": 0}, {"command": 3, "file": 0, "line": 111, "parent": 0}, {"command": 3, "file": 0, "line": 112, "parent": 0}, {"command": 3, "file": 0, "line": 113, "parent": 0}, {"command": 3, "file": 0, "line": 114, "parent": 0}, {"command": 3, "file": 0, "line": 115, "parent": 0}, {"command": 3, "file": 0, "line": 116, "parent": 0}, {"command": 3, "file": 0, "line": 117, "parent": 0}, {"command": 3, "file": 0, "line": 118, "parent": 0}, {"command": 3, "file": 0, "line": 119, "parent": 0}, {"command": 3, "file": 0, "line": 120, "parent": 0}, {"command": 3, "file": 0, "line": 121, "parent": 0}, {"command": 3, "file": 0, "line": 122, "parent": 0}, {"command": 3, "file": 0, "line": 123, "parent": 0}, {"command": 3, "file": 0, "line": 124, "parent": 0}, {"command": 3, "file": 0, "line": 125, "parent": 0}, {"command": 3, "file": 0, "line": 126, "parent": 0}, {"command": 3, "file": 0, "line": 127, "parent": 0}, {"command": 3, "file": 0, "line": 128, "parent": 0}, {"command": 3, "file": 0, "line": 129, "parent": 0}, {"command": 3, "file": 0, "line": 133, "parent": 0}, {"command": 3, "file": 0, "line": 135, "parent": 0}, {"command": 3, "file": 0, "line": 136, "parent": 0}, {"command": 3, "file": 0, "line": 137, "parent": 0}, {"command": 3, "file": 0, "line": 138, "parent": 0}, {"command": 3, "file": 0, "line": 139, "parent": 0}, {"command": 3, "file": 0, "line": 140, "parent": 0}, {"command": 3, "file": 0, "line": 141, "parent": 0}, {"command": 3, "file": 0, "line": 142, "parent": 0}, {"command": 3, "file": 0, "line": 143, "parent": 0}, {"command": 3, "file": 0, "line": 164, "parent": 0}, {"command": 3, "file": 0, "line": 165, "parent": 0}, {"command": 3, "file": 0, "line": 166, "parent": 0}, {"command": 3, "file": 0, "line": 167, "parent": 0}, {"command": 3, "file": 0, "line": 168, "parent": 0}, {"command": 3, "file": 0, "line": 182, "parent": 0}, {"command": 3, "file": 0, "line": 189, "parent": 0}, {"command": 3, "file": 0, "line": 196, "parent": 0}, {"command": 3, "file": 0, "line": 203, "parent": 0}, {"command": 3, "file": 0, "line": 204, "parent": 0}, {"command": 3, "file": 0, "line": 205, "parent": 0}, {"command": 3, "file": 0, "line": 206, "parent": 0}, {"command": 3, "file": 0, "line": 207, "parent": 0}, {"command": 3, "file": 0, "line": 222, "parent": 0}, {"command": 3, "file": 0, "line": 225, "parent": 0}, {"command": 3, "file": 0, "line": 230, "parent": 0}, {"command": 3, "file": 0, "line": 233, "parent": 0}, {"command": 3, "file": 0, "line": 236, "parent": 0}, {"command": 3, "file": 0, "line": 239, "parent": 0}, {"command": 3, "file": 0, "line": 242, "parent": 0}, {"command": 3, "file": 0, "line": 245, "parent": 0}, {"command": 3, "file": 0, "line": 252, "parent": 0}, {"command": 3, "file": 0, "line": 258, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-mfpu=vfp -fPIC -Wall -Wno-psabi -rdynamic -s  -std=gnu++17"}], "includes": [{"backtrace": 34, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit"}, {"backtrace": 35, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/PCConfigure"}, {"backtrace": 36, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/PCConfigure/protocol"}, {"backtrace": 37, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/RS485_G3"}, {"backtrace": 38, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/G3AndMCUUart"}, {"backtrace": 39, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/G3AndMCUUart/protocol"}, {"backtrace": 40, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/MODBUS"}, {"backtrace": 41, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/MODBUS/protocol"}, {"backtrace": 42, "path": "/work/rv1126pb/work/zqxoldCode/DetectUnit"}, {"backtrace": 43, "path": "/work/rv1126pb/work/zqxoldCode/MainUnit"}, {"backtrace": 44, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit"}, {"backtrace": 45, "path": "/work/rv1126pb/work/zqxoldCode/utils/easymedia"}, {"backtrace": 46, "path": "/work/rv1126pb/work/zqxoldCode/utils/rkmedia"}, {"backtrace": 47, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit/CameraOpt"}, {"backtrace": 48, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit/H264Encodec"}, {"backtrace": 49, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit/Mp4Record"}, {"backtrace": 50, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit/JPEGRecord"}, {"backtrace": 51, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit"}, {"backtrace": 52, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/ConfigureOpt"}, {"backtrace": 53, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/RealViewOpt"}, {"backtrace": 54, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/RS485Opt"}, {"backtrace": 55, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/RS232Opt"}, {"backtrace": 56, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/SpeakerOpt"}, {"backtrace": 57, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/GPIOOpt"}, {"backtrace": 58, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/Uart_mcu"}, {"backtrace": 59, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/Sensor"}, {"backtrace": 60, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/ADCVoltage"}, {"backtrace": 61, "path": "/work/rv1126pb/work/zqxoldCode/UIUnit"}, {"backtrace": 62, "path": "/work/rv1126pb/work/zqxoldCode/UIUnit/vo"}, {"backtrace": 63, "path": "/work/rv1126pb/work/zqxoldCode/UpgradeUnit"}, {"backtrace": 64, "path": "/work/rv1126pb/work/zqxoldCode/utils"}, {"backtrace": 65, "path": "/work/rv1126pb/work/zqxoldCode/utils/xudownload"}, {"backtrace": 66, "path": "/work/rv1126pb/work/zqxoldCode/utils/bzip2"}, {"backtrace": 67, "path": "/work/rv1126pb/work/zqxoldCode/utils/logFromXWF"}, {"backtrace": 68, "path": "/work/rv1126pb/work/zqxoldCode/utils/xulog"}, {"backtrace": 69, "path": "/work/rv1126pb/work/zqxoldCode/."}, {"backtrace": 70, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd"}, {"backtrace": 71, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/maskCodec"}, {"backtrace": 72, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/ini"}, {"backtrace": 73, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/rknnYolo"}, {"backtrace": 74, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/3rdparty/librknn_api/include"}, {"backtrace": 75, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/3rdparty/rga/include"}, {"backtrace": 76, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/3rdparty/drm/include"}, {"backtrace": 77, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/3rdparty/drm/include/libdrm"}, {"backtrace": 78, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/yolo"}, {"backtrace": 79, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/stb"}, {"backtrace": 80, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt"}, {"backtrace": 81, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt/3rdparty/librknn_api/include"}, {"backtrace": 82, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt/3rdparty/rga/include"}, {"backtrace": 83, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt/3rdparty/drm/include"}, {"backtrace": 84, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt/3rdparty/drm/include/libdrm"}, {"backtrace": 85, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_gesture"}, {"backtrace": 86, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_adas_160_area"}, {"backtrace": 87, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_sbst"}, {"backtrace": 88, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv"}, {"backtrace": 89, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv/3rdparty/librknn_api/include"}, {"backtrace": 90, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv/3rdparty/rga/include"}, {"backtrace": 91, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv/3rdparty/drm/include"}, {"backtrace": 92, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv/3rdparty/drm/include/libdrm"}, {"backtrace": 93, "path": "/work/rv1126pb/work/zqxoldCode/DetectUnit/alarm_decision_list"}, {"backtrace": 94, "path": "/work/rv1126pb/work/zqxoldCode/DetectUnit/detecter_list"}, {"backtrace": 95, "path": "/work/rv1126pb/work/zqxoldCode/utils/paho_mqtt"}, {"backtrace": 96, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/ndsOpt"}, {"backtrace": 97, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/RTMPPush"}, {"backtrace": 98, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/G3NDSMQTT"}, {"backtrace": 99, "path": "/work/rv1126pb/work/zqxoldCode/utils/json"}, {"backtrace": 100, "path": "/work/rv1126pb/work/zqxoldCode/utils/rtmp"}, {"backtrace": 101, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_std"}, {"backtrace": 102, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_fb"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406], "sysroot": {"path": "/work/rv1126BP/host/arm-buildroot-linux-gnueabihf/sysroot"}}, {"compileCommandFragments": [{"fragment": "-mfpu=vfp -fPIC -Wall -Wno-psabi -rdynamic -s  "}], "includes": [{"backtrace": 34, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit"}, {"backtrace": 35, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/PCConfigure"}, {"backtrace": 36, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/PCConfigure/protocol"}, {"backtrace": 37, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/RS485_G3"}, {"backtrace": 38, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/G3AndMCUUart"}, {"backtrace": 39, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/G3AndMCUUart/protocol"}, {"backtrace": 40, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/MODBUS"}, {"backtrace": 41, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/MODBUS/protocol"}, {"backtrace": 42, "path": "/work/rv1126pb/work/zqxoldCode/DetectUnit"}, {"backtrace": 43, "path": "/work/rv1126pb/work/zqxoldCode/MainUnit"}, {"backtrace": 44, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit"}, {"backtrace": 45, "path": "/work/rv1126pb/work/zqxoldCode/utils/easymedia"}, {"backtrace": 46, "path": "/work/rv1126pb/work/zqxoldCode/utils/rkmedia"}, {"backtrace": 47, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit/CameraOpt"}, {"backtrace": 48, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit/H264Encodec"}, {"backtrace": 49, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit/Mp4Record"}, {"backtrace": 50, "path": "/work/rv1126pb/work/zqxoldCode/MediaUnit/JPEGRecord"}, {"backtrace": 51, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit"}, {"backtrace": 52, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/ConfigureOpt"}, {"backtrace": 53, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/RealViewOpt"}, {"backtrace": 54, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/RS485Opt"}, {"backtrace": 55, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/RS232Opt"}, {"backtrace": 56, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/SpeakerOpt"}, {"backtrace": 57, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/GPIOOpt"}, {"backtrace": 58, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/Uart_mcu"}, {"backtrace": 59, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/Sensor"}, {"backtrace": 60, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/ADCVoltage"}, {"backtrace": 61, "path": "/work/rv1126pb/work/zqxoldCode/UIUnit"}, {"backtrace": 62, "path": "/work/rv1126pb/work/zqxoldCode/UIUnit/vo"}, {"backtrace": 63, "path": "/work/rv1126pb/work/zqxoldCode/UpgradeUnit"}, {"backtrace": 64, "path": "/work/rv1126pb/work/zqxoldCode/utils"}, {"backtrace": 65, "path": "/work/rv1126pb/work/zqxoldCode/utils/xudownload"}, {"backtrace": 66, "path": "/work/rv1126pb/work/zqxoldCode/utils/bzip2"}, {"backtrace": 67, "path": "/work/rv1126pb/work/zqxoldCode/utils/logFromXWF"}, {"backtrace": 68, "path": "/work/rv1126pb/work/zqxoldCode/utils/xulog"}, {"backtrace": 69, "path": "/work/rv1126pb/work/zqxoldCode/."}, {"backtrace": 70, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd"}, {"backtrace": 71, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/maskCodec"}, {"backtrace": 72, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/ini"}, {"backtrace": 73, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/rknnYolo"}, {"backtrace": 74, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/3rdparty/librknn_api/include"}, {"backtrace": 75, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/3rdparty/rga/include"}, {"backtrace": 76, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/3rdparty/drm/include"}, {"backtrace": 77, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/3rdparty/drm/include/libdrm"}, {"backtrace": 78, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/yolo"}, {"backtrace": 79, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_bsd/stb"}, {"backtrace": 80, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt"}, {"backtrace": 81, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt/3rdparty/librknn_api/include"}, {"backtrace": 82, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt/3rdparty/rga/include"}, {"backtrace": 83, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt/3rdparty/drm/include"}, {"backtrace": 84, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_mt/3rdparty/drm/include/libdrm"}, {"backtrace": 85, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_gesture"}, {"backtrace": 86, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_adas_160_area"}, {"backtrace": 87, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_sbst"}, {"backtrace": 88, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv"}, {"backtrace": 89, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv/3rdparty/librknn_api/include"}, {"backtrace": 90, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv/3rdparty/rga/include"}, {"backtrace": 91, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv/3rdparty/drm/include"}, {"backtrace": 92, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_muv/3rdparty/drm/include/libdrm"}, {"backtrace": 93, "path": "/work/rv1126pb/work/zqxoldCode/DetectUnit/alarm_decision_list"}, {"backtrace": 94, "path": "/work/rv1126pb/work/zqxoldCode/DetectUnit/detecter_list"}, {"backtrace": 95, "path": "/work/rv1126pb/work/zqxoldCode/utils/paho_mqtt"}, {"backtrace": 96, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/ndsOpt"}, {"backtrace": 97, "path": "/work/rv1126pb/work/zqxoldCode/PeripheralUnit/RTMPPush"}, {"backtrace": 98, "path": "/work/rv1126pb/work/zqxoldCode/CommunicationUnit/G3NDSMQTT"}, {"backtrace": 99, "path": "/work/rv1126pb/work/zqxoldCode/utils/json"}, {"backtrace": 100, "path": "/work/rv1126pb/work/zqxoldCode/utils/rtmp"}, {"backtrace": 101, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_std"}, {"backtrace": 102, "path": "/work/rv1126pb/work/zqxoldCode/utils/algorithmSrc/src_dsm_fb"}], "language": "C", "sourceIndexes": [345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356], "sysroot": {"path": "/work/rv1126BP/host/arm-buildroot-linux-gnueabihf/sysroot"}}], "id": "vis_g3_software::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-mfpu=vfp -fPIC -Wall -Wno-psabi -rdynamic -s", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "-L/work/rv1126BP/host/arm-buildroot-linux-gnueabihf/sysroot/usr/lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-L/work/rv1126BP/host/arm-buildroot-linux-gnueabihf/sysroot/usr/local/lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-L/work/rv1126BP/host/arm-buildroot-linux-gnueabihf/sysroot/lib", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/usr/lib:/usr/local/lib:/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "-leasymedia", "role": "libraries"}, {"backtrace": 4, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 5, "fragment": "-l<PERSON><PERSON>", "role": "libraries"}, {"backtrace": 6, "fragment": "-lthird_media", "role": "libraries"}, {"backtrace": 7, "fragment": "-lrt", "role": "libraries"}, {"backtrace": 8, "fragment": "-lliveMedia", "role": "libraries"}, {"backtrace": 9, "fragment": "-lgroupsock", "role": "libraries"}, {"backtrace": 10, "fragment": "-lBasicUsageEnvironment", "role": "libraries"}, {"backtrace": 11, "fragment": "-lUsageEnvironment", "role": "libraries"}, {"backtrace": 12, "fragment": "-lPocoFoundation", "role": "libraries"}, {"backtrace": 13, "fragment": "-lPocoCrypto", "role": "libraries"}, {"backtrace": 14, "fragment": "-lssl", "role": "libraries"}, {"backtrace": 15, "fragment": "-lcrypto", "role": "libraries"}, {"backtrace": 16, "fragment": "-lcurl", "role": "libraries"}, {"backtrace": 17, "fragment": "-lmp4v2", "role": "libraries"}, {"backtrace": 18, "fragment": "-lrga", "role": "libraries"}, {"backtrace": 4, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 19, "fragment": "-lusb-1.0", "role": "libraries"}, {"backtrace": 20, "fragment": "-lyaml-cpp", "role": "libraries"}, {"backtrace": 21, "fragment": "-liio", "role": "libraries"}, {"backtrace": 22, "fragment": "-lrknnrt", "role": "libraries"}, {"backtrace": 23, "fragment": "-lopencv_core", "role": "libraries"}, {"backtrace": 24, "fragment": "-lopencv_highgui", "role": "libraries"}, {"backtrace": 25, "fragment": "-lopencv_imgproc", "role": "libraries"}, {"backtrace": 26, "fragment": "-lopencv_flann", "role": "libraries"}, {"backtrace": 27, "fragment": "-lopencv_imgcodecs", "role": "libraries"}, {"backtrace": 28, "fragment": "-lopencv_video", "role": "libraries"}, {"backtrace": 29, "fragment": "-lopencv_videoio", "role": "libraries"}, {"backtrace": 30, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 19, "fragment": "-lusb-1.0", "role": "libraries"}, {"backtrace": 31, "fragment": "-lboost_filesystem", "role": "libraries"}, {"backtrace": 32, "fragment": "-lexiv2", "role": "libraries"}, {"backtrace": 33, "fragment": "-lpcap", "role": "libraries"}, {"backtrace": 5, "fragment": "-l<PERSON><PERSON>", "role": "libraries"}, {"backtrace": 6, "fragment": "-lthird_media", "role": "libraries"}, {"backtrace": 7, "fragment": "-lrt", "role": "libraries"}, {"backtrace": 8, "fragment": "-lliveMedia", "role": "libraries"}, {"backtrace": 9, "fragment": "-lgroupsock", "role": "libraries"}, {"backtrace": 10, "fragment": "-lBasicUsageEnvironment", "role": "libraries"}, {"backtrace": 11, "fragment": "-lUsageEnvironment", "role": "libraries"}, {"backtrace": 12, "fragment": "-lPocoFoundation", "role": "libraries"}, {"backtrace": 13, "fragment": "-lPocoCrypto", "role": "libraries"}, {"backtrace": 14, "fragment": "-lssl", "role": "libraries"}, {"backtrace": 15, "fragment": "-lcrypto", "role": "libraries"}, {"backtrace": 16, "fragment": "-lcurl", "role": "libraries"}, {"backtrace": 17, "fragment": "-lmp4v2", "role": "libraries"}, {"backtrace": 18, "fragment": "-lrga", "role": "libraries"}, {"backtrace": 19, "fragment": "-lusb-1.0", "role": "libraries"}, {"backtrace": 20, "fragment": "-lyaml-cpp", "role": "libraries"}, {"backtrace": 21, "fragment": "-liio", "role": "libraries"}, {"backtrace": 22, "fragment": "-lrknnrt", "role": "libraries"}, {"backtrace": 23, "fragment": "-lopencv_core", "role": "libraries"}, {"backtrace": 24, "fragment": "-lopencv_highgui", "role": "libraries"}, {"backtrace": 25, "fragment": "-lopencv_imgproc", "role": "libraries"}, {"backtrace": 26, "fragment": "-lopencv_flann", "role": "libraries"}, {"backtrace": 27, "fragment": "-lopencv_imgcodecs", "role": "libraries"}, {"backtrace": 28, "fragment": "-lopencv_video", "role": "libraries"}, {"backtrace": 29, "fragment": "-lopencv_videoio", "role": "libraries"}, {"backtrace": 30, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 19, "fragment": "-lusb-1.0", "role": "libraries"}, {"backtrace": 20, "fragment": "-lyaml-cpp", "role": "libraries"}, {"backtrace": 21, "fragment": "-liio", "role": "libraries"}, {"backtrace": 22, "fragment": "-lrknnrt", "role": "libraries"}, {"backtrace": 23, "fragment": "-lopencv_core", "role": "libraries"}, {"backtrace": 24, "fragment": "-lopencv_highgui", "role": "libraries"}, {"backtrace": 25, "fragment": "-lopencv_imgproc", "role": "libraries"}, {"backtrace": 26, "fragment": "-lopencv_flann", "role": "libraries"}, {"backtrace": 27, "fragment": "-lopencv_imgcodecs", "role": "libraries"}, {"backtrace": 28, "fragment": "-lopencv_video", "role": "libraries"}, {"backtrace": 29, "fragment": "-lopencv_videoio", "role": "libraries"}, {"backtrace": 30, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 31, "fragment": "-lboost_filesystem", "role": "libraries"}, {"backtrace": 32, "fragment": "-lexiv2", "role": "libraries"}, {"backtrace": 33, "fragment": "-lpcap", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "/work/rv1126BP/host/arm-buildroot-linux-gnueabihf/sysroot"}}, "name": "vis_g3_software", "nameOnDisk": "vis_g3_software", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "image_conversion_example.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "minimal_image_conversion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "simple_image_conversion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/CommunicationDataCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/CommunicationUnitManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/G3SocketComManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/ConfigureMessageDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/ConfigureMessageEncoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionAlarmEvent.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionAlarmEvent_ADASEvent.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionAlarmEvent_BSDEvent.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionAlarmEvent_DSMEvent.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionAlarmEvent_DataItem.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionAlarmEvent_GeneralInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionAlarmEvent_R151Event.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionAlarmEvent_R158Event.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionAlarmEvent_R159Event.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_ADAS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_AlgDetectTime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_BarCodeInfos.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_Control.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_DSM.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_DataItem.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_GPIOIOStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_GSensorData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_Gesture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_MaskBuf.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_ObjectInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_R151Info.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_R158Info.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_R159Info.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_SeatBelt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_SecurityModeInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_SignalAndStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_SystemError.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/DetectionInfo_SystemInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/G3DeviceUpgradeResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/G3Mp4FileOpt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/G3Mp4FileOptResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetAlarmSoundFileConf.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetCPUSerialNum.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetCameraInputTypeFilterConfig.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetCameraReduceEffectInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetFunctionLockInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetG3AndG4DevicesVersion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetG3Mp4FileList.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetG3Mp4FileListResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetG3UTCTime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetG3UUID.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetMRV220LogFileList.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetMRV220LogFileListResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/GetMultimediaFileEncryptionKey.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/Heartbeat.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/PCConfigureDataPacket.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/QequestG3Config.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RealTimeVehicleStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondAlarmSoundFileConf.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondG3AndG4DevicesVersion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondG3Config.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondGetCPUSerialNumResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondGetCameraInputTypeFilterConfigResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondGetCameraReduceEffectInfoResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondGetFunctionLockInfoResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondGetG3UTCTimeResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondGetUUIDResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondMultimediaFileEncryptionKey.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RespondSetUUIDResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/RestartG3.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetCameraInputTypeFilterConfig.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetCameraReduceEffectInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetFunctionLock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetG3Config.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetG3SystemTime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetG3UUID.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetMultimediaFileEncryptionKey.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetPeripheralDisplayParams.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetPeripheralDisplayParamsResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/SetProductionTestingSwitch.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartG3DebugMode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartG3DeviceUpgrade.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartG3DeviceUpgradeResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartG3FileDownload.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartG3FileDownloadResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartMRV220LogFileDownload.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartMRV220LogFileDownloadResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartUDPRealview.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartUDPRealviewResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartUDPRealviewResult_Separate.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/StartUDPRealview_Separate.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/TCPRealviewOpt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/TCPRealviewOptResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/PCConfigure/protocol/UniversalAnswer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/G3AndMCUUartManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartAlarmInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartAlarmInfo_ADAS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartAlarmInfo_BSD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartAlarmInfo_DSM.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartCANDataTransmissionSwitch.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartDetectionInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartDetectionInfo_ADAS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartDetectionInfo_BSD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartDetectionInfo_IOStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartDetectionInfo_IndoorLocation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartDetectionInfo_LampControl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartDetectionInfo_SYSStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartEncoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartGetPeripheralUpgradeData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartGetPeripheralVersion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartOriginalCANData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartPTWrokResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartPTWrokResult_CANTestResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartPacket.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartPeripheralUpgradeResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartRespondPeripheralVersion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartSendPeripheralUpgradeData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartStartPTWrok.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartStartPTWrok_CANTest.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartStartPeripheralUpgrade.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartUniversalAnswer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3AndMCUUart/protocol/G3AndMCUUartVehicleRealTimeStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485AlarmBroadcast.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485AlarmEvent.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485AlarmEvent_ADASEVent.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485AlarmEvent_BSDEVent.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485AlarmEvent_DataItem.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_ADAS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_ADASAlarm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_BSD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_BSDAlarm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_CameraStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_DSMAlarm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_R151Info.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_R159Info.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_SystemInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485BoradcastInfo_VehicleRunningStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485DataPackage.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485GPSInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485GetGPSInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485GetL4UpgradeFileData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485GetPeripheralVersion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485GetUpgradeFileData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485Manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485MessageDeocder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485MessageEncoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485RespondL4UpgradeFileData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485RespondPeripheralVersion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485RespondUpgradeFileData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485ResponeUpgradeL4Result.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485SetDispalyParams.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485SetDispalyParamsResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485StartUpgrade.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485StartUpgradeL4.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485StartUpgradeL4Result.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485UpgradeL4Result.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/RS485_G3/G3RS485UpgradeResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/G3MODEBUSManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/QControl01DecisionMaker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/protocol/MODBUSMsgDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/protocol/MODBUSMsgEncoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/protocol/MODBUS_GetQContrPedalStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/protocol/MODBUS_GetStopButton.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/protocol/MODBUS_QContrPedalStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/protocol/MODBUS_ReqBase_ADU.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/protocol/MODBUS_RespBase_ADU.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/protocol/MODBUS_SetQContrPedalOutput.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/MODBUS/protocol/MODBUS_StopButtonStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/DetectDataCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/DetectUnitManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MainUnit/MainUnitManagr.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MainUnit/UnitStatusListener.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MainUnit/UnitStatusListenerCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/MediaUnitManagr.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/MultimediaDataCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/CameraOpt/CamCap.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/CameraOpt/CameraImagRunnable.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/CameraOpt/DEKOMAVTPCameraCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/CameraOpt/DEKOMAVTPCameraImageGet.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/CameraOpt/DEKOMAVTPCameraOpt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/CameraOpt/XuCameraUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/H264Encodec/CameraH264Data.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/H264Encodec/H264EncoderRunnable.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/H264Encodec/XuEncoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/Mp4Record/MP4RecordRunnable.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/Mp4Record/Mp4FileInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/Mp4Record/MultimediaFilesEncrypt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/Mp4Record/WarMp4Recorder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/Mp4Record/XuH264ToMp4.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/JPEGRecord/JPEGEncoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MediaUnit/JPEGRecord/JPGFileInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/PeripheraDataCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/PeripheralUnitManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/ConfigureOpt/ConfigureServer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/ConfigureOpt/SocketDataCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/ConfigureOpt/SocketIORunnable.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/RealViewOpt/H264TCPSender.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/RealViewOpt/H264TCPServerSocket.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/RealViewOpt/H264UDPSocket.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/RealViewOpt/H264UDPSocketSeparate.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/RS485Opt/RS485OptManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/RS232Opt/RS232OptManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/SpeakerOpt/SpeakerManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/GPIOOpt/G3GPIOManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/GPIOOpt/G3GPIOManager_V6.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/GPIOOpt/GPIOListener.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/Uart_mcu/Uart_MCU_Manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/Sensor/SensorManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/ADCVoltage/ADCVoltageManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "UIUnit/DispalayShower.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "UIUnit/UIUnitManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "UIUnit/YUVChanger.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "UIUnit/vo/Usb2CbsVo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "UIUnit/vo/Usbtransfer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "UpgradeUnit/UpgradeCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "UpgradeUnit/UpgradeFromTFCard.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "UpgradeUnit/UpgradeUnitManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/CameraYUVData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/CodeUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/ErrorTable.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/G3_Configuration.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/MP4ToYUV.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/Ping.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/Point2D.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/SHA256Utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuCANOpt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuCalculationTool.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuFile.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuHttpUtil.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuLog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuMemGeter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuRGAUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuShell.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuString.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuTimeUtil.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/XuYUVDataOpt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/ascii1632.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/ntp_client.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/term.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/xudownload/XuFileDownloadCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/xudownload/XuFileDownloadUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/logFromXWF/Log.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/logFromXWF/LogChannel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/xulog/XuLogChanneCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/xulog/XuLogStream.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/AlarmDecision_BSD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/Detecter_BSD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/tdDetect.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/tdFunction.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/tdRGBDetect.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/tdYOLOP.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/tdYOLOV5.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/tdYOLOV5DS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/tdYOLOV6.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/maskCodec/maskCodec.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/ini/ini.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/yolo/resize_function.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/yolo/rknn_demo_utils.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_bsd/yolo/yolo.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_mt/DSMGlobalVariable.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_mt/cut_faceroi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_mt/dsmPostprocess.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_mt/face_angle.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_mt/get_alarm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_mt/init_params.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_mt/putcnText.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_mt/resnet18.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_mt/yolov5s.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_gesture/Detecter_Gesture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_gesture/gtDetect.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_gesture/gtFunction.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_gesture/gtYOLOV5.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/AlarmDecision_ADAS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/Detecter_Adas.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/ThreadDo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/comput_distance.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/dasDetect.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/dasFunction.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/dasLane.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/dasObjAlarm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/dasParam.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/dasTracker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/dasYOLOV5DS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/dasYOLOV5Seg.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/tsrClassify.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/tsrGetNum.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_adas_160_area/tsrPostProcess.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_sbst/AlarmDecision_SBST_Backward.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_sbst/AlarmDecision_SBST_Forward.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_sbst/AlarmDecision_SBST_Sideward.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_sbst/Detecter_SBST_ForwardAndBackword.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_sbst/Detecter_SBST_Sideward.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_muv/Calibration.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_muv/cut_faceroi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_muv/dsmPostprocess.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_muv/face_angle.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_muv/get_alarm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_muv/init_params.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_muv/resnet18.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_muv/run.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_muv/yolov5s.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_CameraStatus.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_DD_QRCode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Laneline_adas.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_MT_Face.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_MT_Gesture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_MUV_Face.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Pedestrian_Area.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Pedestrian_Area_SecurityMode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Pedestrian_R151.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Pedestrian_R158.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Pedestrian_R159.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Pedestrian_SBST_Backward.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Pedestrian_Standard.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_STD_Face.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_TSR_ADAS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Vehicle_Area.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Vehicle_LCA.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Vehicle_R151.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Vehicle_R158.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Vehicle_R159.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Vehicle_SBST_Backward.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Vehicle_SPG_ADAS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/alarm_decision_list/VisAlarmDecision_Vehicle_Standard.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_ADAS_160_Normal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_ADAS_160_SBST.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_ADAS_60_Normal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_DE_BSD_OD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_DSM_FB.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_DSM_Normal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_DVR.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_FORKLIFT_160_OD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_KOM_BSD_OD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_MT_DSM.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_MT_Gesture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_MUV_DSM.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_SBST_160_OD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_SBST_160_SS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_UK_BSD_OD.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DetectUnit/detecter_list/VisDetecter_UK_DD_QRCode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTClient.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTConnectClient.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTConnectServer.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTDeserializePublish.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTFormat.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTLinux.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTPacket.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTSerializePublish.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTSubscribeClient.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTSubscribeServer.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTUnsubscribeClient.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/paho_mqtt/MQTTUnsubscribeServer.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/ndsOpt/NDSMqttManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "PeripheralUnit/RTMPPush/RTMPPusher.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgAlarmEvidence.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgAlarmUp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgCallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgEncoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgEventDownload.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgEventUpload.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgFunctionLockSet.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgFunctionLockSetAnswer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgParamsQuery.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgParamsQueryAnswer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgParamsSet.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgStartRealView.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgStartRealViewAnswer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgStartRealViewControl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgUpgradeDevice.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgVesionUp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NDSMqttMsgvehicleProps.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NSDAlarmEvidenceUploadUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/G3NSDDVRFileUploadUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgAlarmEvidenceUploadControl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgAlarmEvidenceUploadFinish.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgCheckDVRFileAnswer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgCheckDVRfile.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgDVRFileUploadResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgFileUploadControl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgFileUploadControlAnswer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgGetAlarmEvidence.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgGetDVRFile.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgGetDVRFileAnswer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgHeader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommunicationUnit/G3NDSMQTT/NdsMqttMsgUniversalAnswer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/json/jsoncpp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/rtmp/srs_librtmp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_std/dsmstdPostprocess.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_std/std_cut_faceroi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_std/std_face_angle.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_std/std_get_alarm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_std/std_params.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_std/std_resnet18.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_std/std_run.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_std/std_yolov5s.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_fb/dsmfbPostprocess.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_fb/fb_get_alarm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_fb/fb_params.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_fb/fb_resnet18.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/algorithmSrc/src_dsm_fb/fb_run.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}