{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.5"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "vis_g3_software", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "vis_g3_software::@6890427a1f51a3e7e1df", "jsonFile": "target-vis_g3_software-f484a170d9e315528dcd.json", "name": "vis_g3_software", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/work/rv1126pb/work/zqxoldCode/build", "source": "/work/rv1126pb/work/zqxoldCode"}, "version": {"major": 2, "minor": 6}}