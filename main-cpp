

#include <boost/property_tree/json_parser.hpp>
#include <boost/property_tree/ptree.hpp>
#include "MainUnit/MainUnitManagr.h"
#include "SHA256Utils.h"
#include "XuLog.h"
#include "JPEGEncoder.h"
#include <netinet/udp.h>  // 提供udphdr结构体定义^:ml-citation{ref="4" data="citationList"}
#include <netinet/ip.h>    // 提供ip结构体定义

#include <pcap.h>
#include <net/ethernet.h>
#include <arpa/inet.h>

#include <iostream>
#include <pcap.h>
#include <cstdint> // 确保uint16_t定义
#define VERSION_NAME "V2.1.906"

//int playSoundFile(const char *soundFilePath) {
//    printf("********playSoundFile  fileName=%s \n",soundFilePath);
//    int ret = -1;
//    /* 采样率 */
//    RK_U32 u32SampleRate = 44100;
//    /* 音频输出通道的通道号 */
//    RK_S32 aoChn = 0;
//    /* 几声道 */
//    RK_U32 u32ChnCnt = 1;
//    /* 读取一帧的数据大小 */
//    RK_U32 u32FrameCnt = 1024;
//    /* 喇叭音量 0~100 */
//    RK_S32 s32Volume = 100;
//    /* 音频输出的参数 */
//    AO_CHN_ATTR_S ao_attr;
//
//    RK_CHAR *pDeviceName = "default";
//    SAMPLE_FORMAT_E enSampleFmt = RK_SAMPLE_FMT_U8;
//    RK_MPI_SYS_Init();
//
//
//    ao_attr.pcAudioNode = pDeviceName;
//    ao_attr.enSampleFormat = enSampleFmt;
//    ao_attr.u32NbSamples = u32FrameCnt;
//    ao_attr.u32SampleRate = u32SampleRate;
//    ao_attr.u32Channels = u32ChnCnt;
//    ret = RK_MPI_AO_SetChnAttr(aoChn, &ao_attr);
//    ret |= RK_MPI_AO_EnableChn(aoChn);
//    if (ret == 0) {
////        RK_S32 s32CurrentVolmue = -1;
////        RK_MPI_AO_GetVolume(0, &s32CurrentVolmue);
////        if (ret) {
////            printf("Get Volume(before) failed! ret=%d\n", ret);
////        }
////        printf("#Before Volume set, volume=%d\n", s32CurrentVolmue);
//
//        ret = RK_MPI_AO_SetVolume(aoChn, s32Volume);
//        if (ret) {
//            printf("Set Volume failed! ret=%d\n", ret);
//        }
//
//    } else {
//        printf("ERROR: create ao[0] failed! ret=%d\n", ret);
//    }
//    FILE *file = fopen(soundFilePath, "r");
//    if (!file) {
//        printf("ERROR: open %s failed!  error=%s\n", soundFilePath, strerror(errno));
//    } else {
//        /* 先每次都重新设置一下音量 */
//        ret = RK_MPI_AO_SetVolume(aoChn, s32Volume);
//        if (ret) {
//            printf("Set Volume failed! ret=%d\n", ret);
//        }
//
//        MEDIA_BUFFER mb = nullptr;
//        RK_U32 u32Timeval = u32FrameCnt * 1000000 / u32SampleRate; // us
//        RK_U32 u32ReadSize;
//        MB_AUDIO_INFO_S stSampleInfo = {ao_attr.u32Channels, ao_attr.u32SampleRate,
//                                        ao_attr.u32NbSamples, ao_attr.enSampleFormat};
//
//        while (1) {
//            mb = RK_MPI_MB_CreateAudioBufferExt(&stSampleInfo, RK_FALSE, 0);
//            if (!mb) {
//                printf("ERROR: create audio buffer\n");
//                break;
//            }
//            u32ReadSize = RK_MPI_MB_GetSize(mb);
////                printf("# TimeVal:%dus, ReadSize:%d\n", u32Timeval, u32ReadSize);
//            ret = fread(RK_MPI_MB_GetPtr(mb), 1, u32ReadSize, file);
//            if (!ret) {
//                printf("# Get end of file!\n");
//                break;
//            }
//
//            ret = RK_MPI_SYS_SendMediaBuffer(RK_ID_AO, 0, mb);
//            if (ret) {
//                printf("ERROR: RK_MPI_SYS_SendMediaBuffer failed! ret = %d\n", ret);
//                break;
//            }
//            usleep(u32Timeval);
//            RK_MPI_MB_ReleaseBuffer(mb);
//            mb = nullptr;
//            ret = 0;
//        }
//
//        if (mb) {
//            RK_MPI_MB_ReleaseBuffer(mb);
//        }
//
//
//
//        fclose(file);
//
//        RK_MPI_AO_DisableChn(aoChn);
//    }
//
//    return ret;
//}





/**
 * 生成报警提示音的demo
 *
 * @param material ：原始素材（一个BEEP的音频）
 * @param output_file ：生成的文件路径
 * @param count ： 总共BEEP多少次
 * @param blanktime ： 每两个BEEP之间隔多久的静音（单位ms）
 * @return 结果
 */
bool concatenatePCMFilesWithSilence(const std::string& material, const std::string& output_file, const int count,const int blanktime) {
    uint8_t materialData[1024*512] = {0x00};
    /* 素材文件的数据 */
    int materialLen = XuFile::getInstance().readFile(material.c_str(),materialData, sizeof(materialData));


    if (materialLen <= 0) {
        std::cerr << "Error opening one of the files." << std::endl;
        return false;
    }

    // 创建空白的静音数据
    int blankDataLen = 44100 * (blanktime / 1000);
    std::vector<uint8_t> blankData(blankDataLen, 0); // 假设是单声道
    // 如果是双声道，创建样本数量加倍的静音数据
    // std::vector<SampleType> silence(sample_rate * 2, 0);

    int outputFileLen = 0;
    FILE *fd = fopen(output_file.c_str(),"wb");
    if(!fd){
        std::cerr << "Error opening testpcm.pcm." << std::endl;
    }

    for(int i = 0; i < count; i ++){
        if( i == (count -1)){
            /* 是最后一次BEEP，那么就只需要插入BEPP，不需要插入静音了 */
            outputFileLen += fwrite(materialData,1,materialLen,fd);
        }else{
            /* 先插入BEPP */
            outputFileLen += fwrite(materialData,1,materialLen,fd);
            /* 再插入静音 */
            outputFileLen += fwrite(blankData.data(),1,blankDataLen,fd);
        }
    }

    /*  */
    fflush(fd);
    fclose(fd);

    return true;
}


/**
 * main 入口
 * @return
 */
int main() {
//    /* 如果TF卡里和/tmp/里没有debug.g3文件  则关闭ADB */
//    if (!XuFile::getInstance().fileExists("/mnt/sdcard/debug.g3") && !XuFile::getInstance().fileExists("/tmp/debug.g3")) {
//        XuShell::getInstance().runShellWithTimeout("echo \" \" > /tmp/.usb_config && /etc/init.d/S50usbdevice restart",2000);
//    }
    /* 先看看自身是否携带了最新的启动脚本 */
    if(XuFile::getInstance().fileExists("/userdata/mrv220luch.sh")){
        /* 自身携带了最新的启动脚本了，那么获取下现在的启动脚本的MD5值 */
        uint8_t vispectLucherMd5[16] = {};
        if(XuFile::getInstance().getMD5FromFile("/oem/vispect/vispectLunch.sh",vispectLucherMd5) > 0){
            /* 最新正确的脚本MD5值直接写死这里，免得文件被损坏算错 */
            uint8_t newFileMd5Value[16] = {};
            XuString::getInstance().stringToByteArray(NEW_MRV220_LUCHER_FILE_MD5,newFileMd5Value, sizeof(newFileMd5Value));
            /* 获取成功了，比对下MD5值，看看与最新的是否不同 */
            if(!XuString::getInstance().cmpTwoByteArray(newFileMd5Value, sizeof(newFileMd5Value), vispectLucherMd5, sizeof(vispectLucherMd5))){
                /* MD5值不同，说明现在的启动脚本不是最新的，那么再看下自身携带的启动脚本的MD5 */
                uint8_t myLucherMd5[16] = {};
                if(XuFile::getInstance().getMD5FromFile("/userdata/mrv220luch.sh",myLucherMd5) > 0){
                    /* 获取 获取自身携带的启动脚本MD5成功，看看与最新的是否相同 */
                    if(XuString::getInstance().cmpTwoByteArray(newFileMd5Value, sizeof(newFileMd5Value), myLucherMd5, sizeof(myLucherMd5))){
                        /* 相同，那么就把自身携带的脚本给替换掉旧的脚本 */
                        std::string cpcmd = "cp ";
                        cpcmd.append("/userdata/mrv220luch.sh");
                        cpcmd.append(" ");
                        cpcmd.append("/oem/vispect/vispectLunch.sh");
                        cpcmd.append(" && sync && reboot");
                        system(cpcmd.c_str());
                    }else{
                        /* 不相同，那么文件可能被损坏了，什么都不做 */
                        ; //not to do
                    }
                }else{
                    /* 获取自身携带的启动脚本MD5失败，那么就什么都不做 */
                    ; //not to do
                }
            }else{
                /* 相同的话就说明已经是最新了，那么就什么都不做 */
                ; //not to do
            }
        }else{
            /* 计算MD5失败，那么就什么都不做 */
            ; //not to do
        }
    }else{
        /* 自身没携带启动脚本，那么就什么都不做 */
        ; //not to do
    }
    /* 判断一下文件节点*/
    if(XuFile::getInstance().fileExists("/tmp/rx")){
        /* 文件节点存在，那么先读出内容 */
        uint8_t rxValue[8] = {};
        int readLen = XuFile::getInstance().readFile("/tmp/rx",rxValue, sizeof(rxValue));
        printf("================readLen=%d   rxValue=%s  byteStr=%s \n",readLen,rxValue,XuString::getInstance().byteArrayToString(rxValue,readLen).c_str());
        /* 看看是不是有读出东西 */
        if(readLen > 0){
            /* 内容末尾带了个0x0a换行，故要去掉 */
            rxValue[readLen -1] = 0x00;
            /* 根据内容判断是什么类型 */
            if(strstr("tp2815", reinterpret_cast<const char *>(rxValue))){
                /* 用的tp2815  是MRV240 */
                G3_Configuration::getInstance().setCoreBoardType(COREBOARDTYPE_MRV240);
            }else if(strstr("rn6752", reinterpret_cast<const char *>(rxValue))){
                /* rn6752  是MRV220K */
                G3_Configuration::getInstance().setCoreBoardType(COREBOARDTYPE_MRV220K);
            }else if(strstr("pr2000", reinterpret_cast<const char *>(rxValue))){
                /* pr2000  是MRV220 */
                G3_Configuration::getInstance().setCoreBoardType(COREBOARDTYPE_MRV220);
            }else if(strstr("error", reinterpret_cast<const char *>(rxValue))){
                /* 错误也算MRV220 */
                G3_Configuration::getInstance().setCoreBoardType(COREBOARDTYPE_MRV220);
                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, "CHECK CORE BOARD TYPE") << "/tmp/rx=error" << XU_LOG_END;
            }else{
                /* 其他情况都算MRV220 */
                G3_Configuration::getInstance().setCoreBoardType(COREBOARDTYPE_MRV220);
            }
        }else{
            /* 没有读出东西，算MRV220 */
            G3_Configuration::getInstance().setCoreBoardType(COREBOARDTYPE_MRV220);
        }
    }else{
        /* 文件节点不存在，就认为是MRV220  */
        G3_Configuration::getInstance().setCoreBoardType(COREBOARDTYPE_MRV220);
    }

    /* 在这里启动一下恢复出厂设置的APP */
    XuShell::getInstance().startResetApp();

    /* 打印一下版本号 */
    printf("************************G3 VERSION NAME = %s  ************************\n", VERSION_NAME);
    /* 把版本号存到文件 */
    std::string versionFileName;
    versionFileName.append("echo ");
    versionFileName.append(VERSION_NAME);
    versionFileName.append(" > G3_SOFTWARD_VERSION_NAME");
    XuShell::getInstance().runShellWithTimeout(versionFileName.c_str());
    /* 设置一下各种版本的默认值 */
    setenv("G3_SOFTWARD_VERSION_NAME", VERSION_NAME, 1);
    setenv("G3_MCU_VERSION_NAME", "-1", 1);
    setenv("G3_L4_VERSION_NAME", "-1", 1);
    setenv("G3_L3_VERSION_NAME", "-1", 1);
    setenv("G3_L2_VERSION_NAME", "-1", 1);
    setenv("G3_GPSBOX_VERSION_NAME", "-1", 1);
    setenv("G3_CAM1_ALG_VERSION", "-1", 1);
    setenv("G3_CAM2_ALG_VERSION", "-1", 1);
    setenv("G3_CAM3_ALG_VERSION", "-1", 1);
    setenv("G3_CAM4_ALG_VERSION", "-1", 1);
    /* 读下固件版本，之所以不从env读是因为env只在命令行下能读到固件版本 */
    if(XuFile::getInstance().fileExists("/tmp/MRV220.ver")){
        std::string versionContent = {};
        std::getline(std::ifstream("/tmp/MRV220.ver"),versionContent);
        if(!versionContent.empty()){
            setenv("G3_FIRMWARE_VERSION", versionContent.c_str(), 1);
        }else{
            setenv("G3_FIRMWARE_VERSION", "0.0.0", 1);
        }
    }else{
        setenv("G3_FIRMWARE_VERSION", "0.0.0", 1);
    }

    vis::MainUnitManagr mainUnitManagr;
    mainUnitManagr.start();

    while (true) {
        sleep(1);

    }


    return 0;


}

#include "XuDecoder.h"
XuDecoder decoder;
uint8_t *curYUVBuf = nullptr;

static void *GetMediaBuffer(void *arg) {

    while (true){
        int ret = decoder.getYUVData(curYUVBuf);
        printf("decoder.getYUVData=%d  \n",ret);
    }

}

class TMCallback : public DEKOMAVTPCameraCallback{
public:
    void init();
    int onDEKOMIPCH264Get(const CameraH264Data &cameraH264Data) override;
private:



};

int TMCallback::onDEKOMIPCH264Get(const CameraH264Data &cameraH264Data) {
    printf("TMCallback::onDEKOMIPCH264Get  cameraId=%d   len=%d   \n",cameraH264Data.getCameraId(),cameraH264Data.getDataLen());

    decoder.toDecodeH264(&cameraH264Data);

    return 0;
}

void TMCallback::init() {
    decoder.initDecoder(1280,720,0);
    curYUVBuf = reinterpret_cast<uint8_t *>(malloc(1024 * 1024 * 3));
    pthread_t read_thread;
    pthread_create(&read_thread, NULL, GetMediaBuffer, NULL);

}

#include "DEKOMAVTPCameraOpt.h"
#include "DEKOMAVTPCameraImageGet.h"

//int main() {
//    try {
//
//
//    DEKOMAVTPCameraOpt opt;
//
////    std::vector<DEKOMAVTPCameraOpt::ScanResultItemInfo> scanRet = opt.scanCamera(1000*60);
//
//
//
//       int ret = 0;
//
//        do {
//            opt.release();
//            if(opt.init("*************",17215)){
//                ret = opt.openCamera();
//                printf("openCamera ret=%d   \n",ret);
//                sleep(2);
//            }
//        } while (!ret);
//        printf("-----------%s--------%d   \n",__func__ ,__LINE__);
//        TMCallback callback;
//        callback.init();
//        printf("-----------%s--------%d   \n",__func__ ,__LINE__);
//        DEKOMAVTPCameraImageGet imageGet;
//        printf("-----------%s--------%d   \n",__func__ ,__LINE__);
//        imageGet.init(0, 1280, 720, callback);
//        printf("-----------%s--------%d   \n",__func__ ,__LINE__);
//            Poco::Thread warMp4RecorderThread;
//            printf("-----------%s--------%d   \n",__func__ ,__LINE__);
//            warMp4RecorderThread.start(imageGet);
//            printf("----- ------%s--------%d   \n",__func__ ,__LINE__);
//
//
//
//
//    while (1){
//        printf("-----------%s--------%d   \n",__func__ ,__LINE__);
//        sleep(1);
//    }
//        return 0;
//
//    } catch (Poco::SystemException declaration) {
//        printf("--------declaration=%s \n",declaration.what());
//    }
//
////    if(!scanRet.empty()){
////        for(int i = 0; i < scanRet.size(); i ++){
////            printf("ip=%s    MAC: %02x:%02x:%02x:%02x:%02x:%02x \n",scanRet[i].ipadress.c_str(),
////                   scanRet[i].mac[0], scanRet[i].mac[1],
////                   scanRet[i].mac[2], scanRet[i].mac[3],
////                   scanRet[i].mac[4], scanRet[i].mac[5]);
////            /* 打开一下 */
////
////
////            ret = opt.startStream();
////            printf("ret=%d   \n",ret);
////        }
////
////    }
//
//
//}