#include <cstdio>
#include <iostream>
#include <unistd.h>
#include <string>
#include <sys/time.h>
#include "MediaUnit/MediaUnitManagr.h"
#include "MediaUnit/CameraOpt/CamCap.h"
#include "MediaUnit/H264Encodec/H264EncoderRunnable.h"
#include "MainUnit/MainUnitManagr.h"
#include "utils/CameraYUVData.h"
#include "MediaUnit/MultimediaDataCallback.h"
#include "utils/G3_Configuration.h"
#include "utils/XuFile.h"
#include "utils/XuRGAUtils.h"
#include "Poco/Thread.h"
class MyMultimediaCallback : public MultimediaDataCallback {
    // 实现必要的回调函数
};

// 辅助函数：保存转换后的图像数据
bool saveConvertedImage(uint8_t* imageData, int dataSize, int width, int height, const std::string& format) {
    // 确保/userdata目录存在
    XuFile::getInstance().mkpath("/userdata");

    // 生成带时间戳的文件名
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    long timestamp = tv.tv_sec * 1000 + tv.tv_usec / 1000;

    std::string filename = "/userdata/converted_" + std::to_string(width) + "x" + std::to_string(height) +
                          "_" + std::to_string(timestamp) + "." + format;

    int saveRet = XuFile::getInstance().writeFile(filename.c_str(), imageData, dataSize);

    if (saveRet > 0) {
        printf("图像转换并保存成功: %s, 尺寸: %dx%d, 大小: %d bytes\n",
               filename.c_str(), width, height, saveRet);
        return true;
    } else {
        printf("图像保存失败: %s\n", filename.c_str());
        return false;
    }
}

// 测试图像转换功能的独立函数
void testImageTransformation() {
    printf("=== 测试图像转换功能 ===\n");

    // 创建XuRGAUtils实例
    XuRGAUtils rgaUtils;

    // 分配测试用的源图像缓冲区 (YUV420SP格式)
    int srcWidth = 1280, srcHeight = 720;
    int srcDataSize = srcWidth * srcHeight * 3 / 2;  // YUV420SP格式
    uint8_t* srcBuffer = new uint8_t[srcDataSize];

    // 分配目标图像缓冲区 (RGBA8888格式)
    int dstWidth = 1280, dstHeight = 720;
    int dstDataSize = dstWidth * dstHeight * 4;  // RGBA8888格式
    uint8_t* dstBuffer = new uint8_t[dstDataSize];

    // 初始化源图像数据（创建一个简单的测试图案）
    memset(srcBuffer, 128, srcDataSize);  // 填充灰色

    printf("开始图像转换: %dx%d YUV420SP -> %dx%d RGBA8888\n",
           srcWidth, srcHeight, dstWidth, dstHeight);

    // 执行图像格式转换
    int convertRet = rgaUtils.imageTransformation(
        srcWidth, srcHeight, XuRGAUtils::IMG_TYPE_NV21, srcBuffer,    // 源图像
        dstWidth, dstHeight, XuRGAUtils::IMG_TYPE_RGBA8888, dstBuffer // 目标图像
    );

    if (convertRet > 0) {
        printf("图像转换成功，输出数据大小: %d bytes\n", convertRet);

        // 保存转换后的图像
        if (saveConvertedImage(dstBuffer, convertRet, dstWidth, dstHeight, "rgba")) {
            printf("图像已成功保存到 /userdata/ 目录\n");
        }
    } else {
        printf("图像转换失败，错误码: %d\n", convertRet);
    }

    // 释放内存
    delete[] srcBuffer;
    delete[] dstBuffer;

    printf("=== 图像转换测试完成 ===\n\n");
}

int main() {
    // 首先测试图像转换功能
    testImageTransformation();

    // 初始化相机
    CamCap camera("/dev/video0");  // 假设使用video0设备
    printf("camera: %d\n", camera.isCamera());
    if (!camera.isCamera()) {
        std::cout << "Failed to find camera device!" << std::endl;
        return -1;
    }

    // 设置相机格式，并指定输入信号类型为AHD 720P
    if (camera.setFmt(1280, 720, 0, CAMERA_INPUT_TYPE_AHD_720P) < 0) {
        std::cout << "Failed to set camera format!" << std::endl;
        return -1;
    }

    // 启动视频流
    if (camera.streamOn() < 0) {
        std::cout << "Failed to start camera stream!" << std::endl;
        return -1;
    }

    // 启动相机捕获线程 - 这是关键步骤！
    Poco::Thread cameraThread;
    cameraThread.start(camera);

    // 等待相机线程启动并开始捕获数据
    printf("Waiting for camera thread to start...\n");
    sleep(2);  // 等待2秒让相机线程初始化

    // 初始化H264编码器
    H264EncoderRunnable encoder;
    MyMultimediaCallback callback;
    printf("encoder: %d\n", encoder.isEncoderOpened());
    // 配置编码器参数 (cameraId, width, height, frameRate, bitRate, venChn, callback)
    encoder.init(0, 1280, 720, 25, 4000000, 0, callback);

    // 分配缓冲区用于存储camera数据
    uint8_t* buffer = new uint8_t[1280 * 720 * 3/2];  // YUV420格式
    printf("strart--buffer: %p\n", buffer);

    // 分配缓冲区用于存储转换后的RGBA数据
    uint8_t* rgbaBuffer = new uint8_t[1280 * 720 * 4];  // RGBA8888格式

    // 创建XuRGAUtils实例
    XuRGAUtils rgaUtils;

    printf("Starting main capture loop...\n");
    // 主循环
    while (true) {
        // 获取摄像头数据
        int ret = camera.getCamData(buffer, 1280 * 720 * 3/2);
        if (ret >= 0) {
            // 创建YUV数据对象
            CameraYUVData yuvData;
            // TODO: 设置YUV数据的相关参数
            printf("YUV数据: %d\n", ret);
            // 发送数据到编码器
            encoder.setCurCameraYuvData(yuvData);

            // 执行图像格式转换：YUV420SP -> RGBA8888
            int convertRet = rgaUtils.imageTransformation(
                1280, 720, XuRGAUtils::IMG_TYPE_NV21, buffer,    // 源图像：1280x720 YUV420SP
                1280, 720, XuRGAUtils::IMG_TYPE_RGBA8888, rgbaBuffer  // 目标图像：1280x720 RGBA8888
            );

            if (convertRet > 0) {
                // 转换成功，保存图像到/userdata/目录
                saveConvertedImage(rgbaBuffer, convertRet, 1280, 720, "rgba");
            } else {
                printf("图像转换失败, 错误码: %d\n", convertRet);
            }
        }
        else {
            std::cout << "Failed to get camera data!" << std::endl;
        }
        usleep(1000); // 休眠1ms
    }

    // 停止相机捕获线程
    // Note: CamCap类需要有停止机制，这里可能需要调用特定的停止方法

    // 停止视频流
    camera.streamOff();
    camera.closeDevices();

    // 等待线程结束
    if (cameraThread.isRunning()) {
        cameraThread.join();
    }

    delete[] buffer;
    delete[] rgbaBuffer;

    return 0;
}
