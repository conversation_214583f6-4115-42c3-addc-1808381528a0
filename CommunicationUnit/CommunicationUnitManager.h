//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/9.
//

#ifndef VIS_G3_SOFTWARE_COMMUNICATIONUNITMANAGER_H
#define VIS_G3_SOFTWARE_COMMUNICATIONUNITMANAGER_H

#include "CommunicationDataCallback.h"
#include "PCConfigure/protocol/ConfigureMessageDecoder.h"
#include "PCConfigure/protocol/ConfigureMessageEncoder.h"
#include "XuCANOpt.h"
#include "RS485_G3/G3RS485Manager.h"
#include <Poco/ThreadPool.h>
#include "G3SocketComManager.h"
#include "G3AndMCUUartManager.h"
#include "G3NDSMqttManager.h"
#include "G3MODEBUSManager.h"

namespace vis {
    class CommunicationUnitManager : public CommunicationDataCallback {
    public:
        CommunicationUnitManager();

        ~CommunicationUnitManager();


        void start();

        void setInterface(CommunicationDataCallback &communicationDataCallback);


        /**
       * 获取当前的运行状态
       *
       * @return 0：一切正常   其他：有故障
       *
       * */
        int getWorkStaus();


        /**
         * 设置从socket收到的配置客户端发过来的数据
         *
         * @param buf : 数据内存指针
         * @param len : 数据长度
         *
         * */
        void setSocketDataToConfigure(const char *ip, const int port, const uint8_t *buf, int len);


        /**
         * 设置从CAN总线上读取到的数据
         *
         * @param canFrame : 封装好的can数据
         *
         * */
        void setCANBusData(const can_frame &canFrame);

        /**
         * 设置从RS485总线上读取到的数据
         *
         * @param buf : 从RS485总线上读取到的数据的指针
         * @param len ：数据长度
         *
         * */
        void setRS485BusData(const uint8_t *buf, const int len);

        /**
         * 设置从识别模块拿到的识别信息(BSD)
         *
         * @param objectInfo ： 识别信息的结构体
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

        /**
         * 设置从识别模块拿到的识别信息(DSM)
         *
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDSMInfo(G3DetectionResult &detectionInfo);

        /**
         * 设置从识别模块拿到的识别信息(手势)
         *
         * @param objectInfo ： 识别信息的结构体
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectionInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

        /**
         * 设置从识别模块拿到的报警事件
         *
         * @param cameraId : 相机ID
         * @param detectType : 识别算法类型
         * @param eventCode ： 事件码
         *
         * */
        void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

        /**
         * 设置从跟MCU通信的UART上读取到的数据
         *
         * @param buf : 从RS485总线上读取到的数据的指针
         * @param len ：数据长度
         *
         * */
        void setMCUUARTData(const uint8_t *buf, const int len);

        /**
         * 设置IO口状态
         *
         * @param portNum ： IO口编码
         * @param value ： 电平内容
         * @param valueLen ： 电平内容长度
         *
         * */
        void setIOStatus(const int portNum, const char *value, const int valueLen);


        /**
         * 设置开启设备升级的结果
         *
         * @param startG3DeviceUpgrade : 开启升级的消息内容
         * @param tcpport : 开启的TCP端口
         * @param ip ： 客户端的IP
         * @param port ： 客户端的端口
         *
         * */
        void setStartUpgardeResult(StartG3DeviceUpgrade &startG3DeviceUpgrade, int tcpport, int result, const char *ip,
                                   const int port);

        /**
         * 设置开启升级的结果
         *
         * @param devicesId : 设备ID
         * @param result : 结果
         * @param ip ： 客户端的IP
         * @param port ： 客户端的端口
         *
         * */
        void setUpgardeResult(const int devicesId, const int result, const char *ip, const int port);

        /**
         * 强制升级一下MCU软件
         * @param upgradeFilePath : 升级文件路径
         *
         * @return 结果  0：成功  其他：失败
         */
        int setUpgradeMCU(std::string &upgradeFilePath);

        /**
         * 升级一下RS485外设软件
         * @param peripheralId : 外设地址
         * @param upgradeFilePath : 升级文件路径
         *
         * @return 结果  0：成功  其他：失败
         */
        int setUpgradeRS485(const int peripheralId,const std::string &upgradeFilePath);

        /**
         * 设置最新的DVR文件列表
         *
         * @param dvrFileList ： DVR文件列表
         * @return 结果  0：成功   其他：失败
         */
        int setNewDVRFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &dvrList);

        /**
         * 设置最新的报警文件列表
         *
         * @param dvrFileList ： DVR文件列表
         * @return 结果  0：成功   其他：失败
         */
        int setNewWarFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &warList);

        /**
         * 设置从识别模块拿到的识别信息(Adas)
         *
         * @param objectInfo ： 识别信息的结构体
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

        /**
         * 设置MQTT当前的连接状态
         *
         * @param isConnected : MQTT的连接状态
         */
        void setMQTTConnectStatusChange(const bool isConnected);


        /**
         * 收到MQTT得消息
         *
         * @param mqttType ： MQTT类型
         * @param topicName ： 订阅字的指针
         * @param topicNameLen ： 订阅字的指针的长度
         * @param payload ： 内容的指针
         * @param payloadLen ： 内容的指针的长度
         */
        void setMQTTMsg(const int mqttType, const char *topicName, const int topicNameLen, const char *payload, const int payloadLen);

        /**
         * 有新的MP4文件生成了
         *
         * @param mp4FileInfo ： MP4文件的信息
         */
        void setNewMp4File(const Mp4FileInfo &mp4FileInfo);

        /**
         * 有新的JPG文件生成了
         *
         * @param jpgFileInfo ： jpg文件的信息
         */
        void setNewJPGFile(const JPGFileInfo &jpgFileInfo);


        /**
         * 设置转向信号
         *
         * @param trunL ： 是否左转
         * @param trunR ： 是否右转
         */
        void setTurnSignal(const bool trunL,const bool trunR);

        /**
        * 设置车速
        *
        * @param speed : 速度  （单位：KM\H）
        * @param baseOf ： 来源    （0：CAN  1：UART  2：GPS  3:以太网口）
        *
        * @return 无
        * */
        void setSpeed(const float speed, const int baseOf);

        /**
         * 配置外设显示参数
         *
         * @param displayMode ：显示模板
         * @param iconMode ：显示图标子集编号
         * @return 结果  0：成功  其他：失败
         */
        int setDispalyParams(const int displayMode,const int iconMode);

        /**
         * 发送配置外设显示参数的结果去以太网
         *
         * @param devicesId : 设备ID
         * @param result : 结果
         * @param ip ： 客户端的IP
         * @param port ： 客户端的端口
         */
        void setDispalyParamsResultToSocket(const int devicesId, const int result, const char *ip, const int port);


        /**
         * 获取RS485的外设版本号
         *
         * @param peripheralId ： 外设ID
         */
        void getRS485PeripheralVersion(const int peripheralId);

        /**
         * 设置从RS232上读取到的数据
         *
         * @param buf : 从RS232上读取到的数据的指针
         * @param len ：数据长度
         *
         * */
        void setRS232BusData(const uint8_t *buf, const int len);

        /**
         *
         * 设置实时车况
         *
         * @param vehicleRealtimeStatus : 实时车况
         */
        void setVehicleRealtimeStatus(Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 设置从识别模块拿到的识别信息(镜头状态)
         *
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectInfo_CameraStatus(G3DetectionResult &detectionResult);

        /**
         * 设置从识别模块拿到的识别信息(条码)
         *
         * @param detectionInfo ： 封装好的识别结果
         * @param barCodeInfoList : 条码信息列表
         *
         * */
        void setDetectInfo_BarCode(G3DetectionResult &detectionInfo, std::vector<BarCodeInfo> &barCodeInfoList);

        /**
         * 设置最新的Gsensor数据
         *
         * @param value_x ： X轴的加速度
         * @param value_y ： Y轴的加速度
         * @param value_z ： Z轴的加速度
         */
        void setGSensorData(float value_x, float value_y, float value_z);


        void onGetHeartBeat(Heartbeat &heartbeat, const char *ip, const int port) override;

        void onGetQequestG3Config(QequestG3Config &qequestG3Config, const char *ip, const int port) override;

        void onGetSetG3Config(SetG3Config &setG3Config, const char *ip, const int port) override;

        void onGetStartUDPRealview(StartUDPRealview &startUDPRealview, const char *ip, const int port) override;

        void onGetUniversalAnswer(UniversalAnswer &universalAnswe, const char *ip, const int portr) override;

        void onGetSpeedFromCANJ1939(const float speed) override;

        void onGetEngineSpeedFromCANJ1939(const float engineSpeed) override;

        void onNeedSendDataToRS485(const uint8_t *buf, const int len) override;

        void onGetGPSInfoFromRS485(G3RS485GPSInfo &gpsInfo) override;

        void onNeedSendDataToSocket(const char *ip, const int port, const uint8_t *buf, int len) override;

        void onGetRealtimeVehicleStatus(RealTimeVehicleStatus &vehicleStatus, const char *ip, const int port) override;

        void onNeedSendDataToMCUUART(const uint8_t *buf, const int len) override;

        void onNeedPlaySound(const char *soundFilePath, bool isForce) override;

        void onNeedPlaySound_JKFZ(const char *soundFilePath, bool isForce) override;

        void onGetPeripheralVersionFromMCUUART(G3AndMCUUartRespondPeripheralVersion &peripheralVersion) override;

        void
        onGetStartG3DeviceUpgrade(StartG3DeviceUpgrade &startG3DeviceUpgrade, const char *ip, const int port) override;

        int onGetSetG3SystemTime(SetG3SystemTime &setG3SystemTime, const char *ip, const int port) override;

        void
        onGetPeripheralVersion(const uint8_t peripheral, const char *softwarVersion,
                               const char *protocoVersion) override;

        std::vector<PCConfigureDataPacket::MP4FileInfo>
        onGetG3Mp4FileOpt(G3Mp4FileOpt &g3Mp4FileOpt, const char *ip, const int port) override;

        StartG3FileDownloadResult
        onGetStartG3FileDownload(StartG3FileDownload &startG3FileDownload, const char *ip, const int port) override;

        void onGetStartG3DebugMode(StartG3DebugMode &startG3DebugMode, const char *ip, const int port) override;

        void onGetSetFunctionLock(SetFunctionLock &setFunctionLock, const char *ip, const int port) override;

        void onGetRestartMRV220(RestartG3 &restartG3, const char *ip, const int port) override;

        int onGetStartUDPRealviewSeparate(StartUDPRealview_Separate &startUdpRealview, const char *ip,
                                          const int port) override;
        void onGetRealtimeVehicleStatusFromMCUUART(G3AndMCUUartVehicleRealTimeStatus &vehicleStatus) override;

        void onGetProductionTestResultFromMCUUART(G3AndMCUUartPTWrokResult &ptWrokResult) override;

        int onNeedSendDataToNDSMqtt(std::string &strData) override;

        int onGetRTMPPushOpt(const std::string url, const int cameraId, const int opt) override;

        void onGetUpgradeResultFromRS485(G3RS485UpgradeResult &upgradeResult) override;

        void onGetSetDispalyParamsResultFromRS485(G3RS485SetDispalyParamsResult &setDispalyParamsResult) override;

        int onGetSetPeripheralDisplayParams(SetPeripheralDisplayParams displayParams, const char *ip, const int port) override;

        void onGetGetG3AndG4DeviceVersion(GetG3AndG4DevicesVersion &getG3AndG4DevicesVersion, const char *ip, const int port) override;

        void onGetStopButtonStatusFromMODBUS(vis::MODBUS_StopButtonStatus &stopButtonStatus) override;

        void onNeedSendDataToRS232(const uint8_t *buf, const int len) override;

        void onGetPeripheralUpgradeResultFromMCUUART(G3AndMCUUartPeripheralUpgradeResult &peripheralUpgradeResult) override;

        StartMRV220LogFileDownloadResult onGetStartMRV220LogFileDownload(StartMRV220LogFileDownload &startMrv220LogFileDownload, const char *ip, const int port) override;

        int onGetSetMultimediaFileEncryptionKey(SetMultimediaFileEncryptionKey &setMultimediaFileEncryptionKey, const char *ip, const int port) override;

        int onGetSetCameraInputTypeFilterConfig(SetCameraInputTypeFilterConfig &setCameraInputTypeFilterConfig, const char *ip, const int port) override;

        void onGetChangeRS485Baudrate(const int baudrate) override;

        int onGetSetCameraReduceEffectInfoFromSocket(SetCameraReduceEffectInfo &setCameraReduceEffectInfo, const char *ip, const int port) override;

        int onGetSetProductionTestingSwitchFromSocket(SetProductionTestingSwitch &setProductionTestingSwitch, const char *ip, const int port) override;

        int onGetStartDeviceUpgradeFromNDS(const int fileType, const std::string &fileUrl) override;

        int onGetTCPRealviewOptFromSocket(TCPRealviewOpt &tcpRealviewOpt, const char *ip, const int port) override;

    private:
        /*  */
        CommunicationDataCallback *callback;

        /* RS485通讯的控制类 */
        G3RS485Manager g3Rs485Manager;

        /* 以太网Socket通信的控制类 */
        G3SocketComManager g3SocketComManager;

        /* 跟底板MCU的UART通信类 */
        G3AndMCUUartManager g3AndMcuUartManager;

        /*NDS的通信类*/
        G3NDSMqttManager ndsMqttManager;

        /*NDS的通信类*/
        G3MODEBUSManager modebusManager;


        /* 是否是第一次收到MQTT连接成功的状态 */
        bool isFristMQTTConnected = true;



        Poco::ThreadPool communicationUnitThreadPool;


        /* 报警间隔时间  2s */
        const uint64_t ALARM_INTERVAL_TIME = 2;
        /* 上次发送行人报警的时间 */
        uint64_t lastEventTime = 0;
        /* 上次发送的报警事件 */
        int lastEventCode = -1;


    };
}

#endif //VIS_G3_SOFTWARE_COMMUNICATIONUNITMANAGER_H
