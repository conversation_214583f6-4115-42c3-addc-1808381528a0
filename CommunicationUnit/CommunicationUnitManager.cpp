//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/9.
//

#include <cstdio>
#include <XuTimeUtil.h>
#include "CommunicationUnitManager.h"
namespace vis {

    void CommunicationUnitManager::onGetHeartBeat(Heartbeat &heartbeat, const char *ip, const int port) {
        printf("get a heartbeat  flowId=%d \n", heartbeat.getFlowdId());
    }

    void
    CommunicationUnitManager::onGetQequestG3Config(QequestG3Config &qequestG3Config, const char *ip, const int port) {
        printf("get a qequest G3 Config  flowId=%d \n", qequestG3Config.getFlowdId());
    }

    void CommunicationUnitManager::onGetSetG3Config(SetG3Config &setG3Config, const char *ip, const int port) {
        printf("get a set G3 Config  flowId=%d \n", setG3Config.getFlowdId());
    }

    void
    CommunicationUnitManager::onGetStartUDPRealview(StartUDPRealview &startUDPRealview, const char *ip,
                                                    const int port) {
        printf("get a start Udp Realview  cameraId=%d \n", startUDPRealview.getCameraId());
        callback->onGetStartUDPRealview(startUDPRealview, ip, port);
    }

    void
    CommunicationUnitManager::onGetUniversalAnswer(UniversalAnswer &universalAnswer, const char *ip, const int port) {
        printf("get a universalAnswer  qMsgId=%d  qFllowId=%d  reuslt=%d \n", universalAnswer.getQMsgId(),
               universalAnswer.getQFlowId(), universalAnswer.getResult());

    }

    CommunicationUnitManager::CommunicationUnitManager() : communicationUnitThreadPool(5, 5) {

    }

    CommunicationUnitManager::~CommunicationUnitManager() {

    }

    void CommunicationUnitManager::start() {
//        j1939MessageDecoder.setInterface(*this);

        g3SocketComManager.init(*this);
        if (communicationUnitThreadPool.available() > 0) {
            communicationUnitThreadPool.start(g3SocketComManager);
        }

        g3AndMcuUartManager.init(*this);
        modebusManager.init(*this);
        g3Rs485Manager.init(*this);
        /* 看看是不是V6模式 */
        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6){
            /* 不是V6模式，那么正常启动UART、RS485、RS232的通信 */
            if (communicationUnitThreadPool.available() > 0) {
                communicationUnitThreadPool.start(g3AndMcuUartManager);
            }
            if (communicationUnitThreadPool.available() > 0) {
                communicationUnitThreadPool.start(modebusManager);
            }
            if (communicationUnitThreadPool.available() > 0) {
                communicationUnitThreadPool.start(g3Rs485Manager);
            }

        }else{
            /* 如果是V6，那启动了以太网通信就行了，其他的不用动 */
            ; //not to do
        }




    }

    void CommunicationUnitManager::setRS232BusData(const uint8_t *buf, const int len) {
        modebusManager.setMODBUSData(buf,len);
    }

    void
    CommunicationUnitManager::setSocketDataToConfigure(const char *ip, const int port, const uint8_t *buf, int len) {
        g3SocketComManager.setSocketDataToConfigure(ip, port, buf, len);

    }

    void CommunicationUnitManager::onGetSpeedFromCANJ1939(const float speed) {
        printf("CommunicationUnitManager::onGetSpeedFromCANJ1939    speed=%f \n", speed);
        callback->onGetSpeedFromCANJ1939(speed);
    }

    void CommunicationUnitManager::onGetEngineSpeedFromCANJ1939(const float engineSpeed) {
        printf("CommunicationUnitManager::onGetEngineSpeedFromCANJ1939    engineSpeed=%f \n", engineSpeed);
        callback->onGetEngineSpeedFromCANJ1939(engineSpeed);
    }

    void CommunicationUnitManager::setCANBusData(const can_frame &canFrame) {
    }

    void CommunicationUnitManager::setInterface(CommunicationDataCallback &communicationDataCallback) {
        callback = &communicationDataCallback;

    }

    void CommunicationUnitManager::onNeedSendDataToRS485(const uint8_t *buf, const int len) {
        callback->onNeedSendDataToRS485(buf, len);
    }

    void CommunicationUnitManager::setRS485BusData(const uint8_t *buf, const int len) {
//    printf("CommunicationUnitManager::onGetDataFromRS485  data size=%d \n",len);
        g3Rs485Manager.setRS485BusData(buf, len);
    }

    int CommunicationUnitManager::getWorkStaus() {
        /* 暂时想不出这个模块有什么好检查的  先直接返回正常 */
        return 0;
    }

    void CommunicationUnitManager::onGetGPSInfoFromRS485(G3RS485GPSInfo &gpsInfo) {
        callback->onGetGPSInfoFromRS485(gpsInfo);
    }

    void CommunicationUnitManager::onNeedSendDataToSocket(const char *ip, const int port, const uint8_t *buf, int len) {
        callback->onNeedSendDataToSocket(ip, port, buf, len);
    }


    void CommunicationUnitManager::onGetRealtimeVehicleStatus(RealTimeVehicleStatus &vehicleStatus, const char *ip,
                                                              const int port) {
        callback->onGetRealtimeVehicleStatus(vehicleStatus, ip, port);
    }

    void CommunicationUnitManager::onNeedSendDataToMCUUART(const uint8_t *buf, const int len) {
        callback->onNeedSendDataToMCUUART(buf, len);
    }

    void CommunicationUnitManager::setMCUUARTData(const uint8_t *buf, const int len) {
        g3AndMcuUartManager.setDataFromMCUUart(buf, len);

    }

    void CommunicationUnitManager::onNeedPlaySound(const char *soundFilePath, bool isForce) {

        callback->onNeedPlaySound(soundFilePath, isForce);
    }

    void CommunicationUnitManager::setIOStatus(const int portNum, const char *value, const int valueLen) {
        g3AndMcuUartManager.setIOStatus(portNum, value, valueLen);
        g3SocketComManager.setIOStatus(portNum, value, valueLen);
    }

    void CommunicationUnitManager::onNeedPlaySound_JKFZ(const char *soundFilePath, bool isForce) {
        callback->onNeedPlaySound_JKFZ(soundFilePath, isForce);
    }

    void
    CommunicationUnitManager::onGetPeripheralVersionFromMCUUART(
            G3AndMCUUartRespondPeripheralVersion &peripheralVersion) {
        callback->onGetPeripheralVersionFromMCUUART(peripheralVersion);
    }

    void
    CommunicationUnitManager::setStartUpgardeResult(StartG3DeviceUpgrade &startG3DeviceUpgrade, int tcpport, int result,
                                                    const char *ip, const int port) {
        g3SocketComManager.setStartUpgardeResult(startG3DeviceUpgrade, tcpport, result, ip, port);
    }

    void
    CommunicationUnitManager::setUpgardeResult(const int devicesId, const int result, const char *ip, const int port) {


        g3SocketComManager.setUpgardeResult(devicesId, result, ip, port);
    }

    void CommunicationUnitManager::onGetStartG3DeviceUpgrade(StartG3DeviceUpgrade &startG3DeviceUpgrade, const char *ip,
                                                             const int port) {
        callback->onGetStartG3DeviceUpgrade(startG3DeviceUpgrade, ip, port);
    }

    void
    CommunicationUnitManager::setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
        g3SocketComManager.setDetectionInfo_BSD(objectInfo, detectionResult);
        g3Rs485Manager.setDetectionInfo_BSD(objectInfo, detectionResult);
        g3AndMcuUartManager.setDetectionInfo_BSD(objectInfo, detectionResult);
    }

    void
    CommunicationUnitManager::setDetectionEvent(CameraType &curCameraType, int eventCode,
                                                AlarmEventInfo &alarmEventInfo) {
        uint64_t userTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec() - lastEventTime;
        /* 报警需要冷却时间，    但是车辆TTC、车辆HMW、行人HMW、LDW不需要冷却时间，因为他们有开始结束的标志 */
        if (userTime >= ALARM_INTERVAL_TIME || (eventCode == EVENT_ADAS_PEDESTRIAN_HMW || eventCode == EVENT_ADAS_VEHICLE_HMW || eventCode == EVENT_ADAS_VEHICLE_TTC || eventCode == EVENT_ADAS_LDW_LEFT_SOLID || eventCode == EVENT_ADAS_LDW_LEFT_DASH || eventCode == EVENT_ADAS_LDW_RIGHT_SOLID || eventCode == EVENT_ADAS_LDW_RIGHT_DASH)) {
            lastEventTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
            lastEventCode = -1;
//            printf("userTime >= PEDSESTRIAN_ALARM_INTERVAL_TIME \n");
        } else {
//            printf("userTime < PEDSESTRIAN_ALARM_INTERVAL_TIME \n");
        }

        if (eventCode != lastEventCode) {
            g3SocketComManager.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            g3Rs485Manager.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            g3AndMcuUartManager.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            ndsMqttManager.setDetectEvent(curCameraType, eventCode, alarmEventInfo);

            lastEventCode = eventCode;
        }

    }

    int
    CommunicationUnitManager::onGetSetG3SystemTime(SetG3SystemTime &setG3SystemTime, const char *ip, const int port) {
        return callback->onGetSetG3SystemTime(setG3SystemTime, ip, port);
    }

    void CommunicationUnitManager::setDSMInfo(G3DetectionResult &detectionInfo) {
        g3SocketComManager.setDSMInfo(detectionInfo);
//    g3Rs485Manager.setDetectionInfo_BSD(objectInfo, detectionResult);
//    g3AndMcuUartManager.setDetectionInfo_BSD(objectInfo, detectionResult);
    }

    void
    CommunicationUnitManager::setDetectionInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
        g3SocketComManager.setDetectionInfo_GES(objectInfo, detectionResult);
    }

    int CommunicationUnitManager::setUpgradeMCU(std::string &upgradeFilePath) {
        g3AndMcuUartManager.setUpgradeMCU(upgradeFilePath);
        return 0;
    }

    void CommunicationUnitManager::onGetPeripheralVersion(const uint8_t peripheral, const char *softwarVersion,
                                                          const char *protocoVersion) {
        callback->onGetPeripheralVersion(peripheral, softwarVersion, protocoVersion);
    }

    int CommunicationUnitManager::setNewDVRFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &dvrList) {
        g3SocketComManager.setNewDVRFileList(dvrList);
        ndsMqttManager.setNewDVRFileList(dvrList);
        return 0;
    }

    int CommunicationUnitManager::setNewWarFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &warList) {
        g3SocketComManager.setNewWarFileList(warList);
        ndsMqttManager.setNewWarFileList(warList);
        return 0;
    }

    std::vector<PCConfigureDataPacket::MP4FileInfo>
    CommunicationUnitManager::onGetG3Mp4FileOpt(G3Mp4FileOpt &g3Mp4FileOpt, const char *ip, const int port) {
        return callback->onGetG3Mp4FileOpt(g3Mp4FileOpt, ip, port);
    }

    StartG3FileDownloadResult
    CommunicationUnitManager::onGetStartG3FileDownload(StartG3FileDownload &startG3FileDownload, const char *ip,
                                                       const int port) {
        return callback->onGetStartG3FileDownload(startG3FileDownload, ip, port);
    }

    void
    CommunicationUnitManager::onGetStartG3DebugMode(StartG3DebugMode &startG3DebugMode, const char *ip,
                                                    const int port) {

        callback->onGetStartG3DebugMode(startG3DebugMode, ip, port);
    }

    void
    CommunicationUnitManager::onGetSetFunctionLock(SetFunctionLock &setFunctionLock, const char *ip, const int port) {
        callback->onGetSetFunctionLock(setFunctionLock, ip, port);
    }

    void CommunicationUnitManager::onGetRestartMRV220(RestartG3 &restartG3, const char *ip, const int port) {
        callback->onGetRestartMRV220(restartG3, ip, port);
    }

    void
    CommunicationUnitManager::setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
        g3SocketComManager.setDetectionInfo_Adas(objectInfo, detectionResult);
        g3Rs485Manager.setDetectionInfo_Adas(objectInfo, detectionResult);
    }

    int
    CommunicationUnitManager::onGetStartUDPRealviewSeparate(StartUDPRealview_Separate &startUdpRealview, const char *ip,
                                                            const int port) {
        return callback->onGetStartUDPRealviewSeparate(startUdpRealview, ip, port);
    }

    void
    CommunicationUnitManager::onGetRealtimeVehicleStatusFromMCUUART(G3AndMCUUartVehicleRealTimeStatus &vehicleStatus) {
        callback->onGetRealtimeVehicleStatusFromMCUUART(vehicleStatus);
    }

    void CommunicationUnitManager::onGetProductionTestResultFromMCUUART(G3AndMCUUartPTWrokResult &ptWrokResult) {
        callback->onGetProductionTestResultFromMCUUART(ptWrokResult);
    }

    void CommunicationUnitManager::setMQTTConnectStatusChange(const bool isConnected) {
        /* 保存一下MQTT的连接状态 */
        G3_Configuration::getInstance().setNdsConnected(isConnected);
        /* 根据连接状态看看是否启动通信 */
        if(isFristMQTTConnected && isConnected){
            isFristMQTTConnected = false;
            ndsMqttManager.init(*this);
            if (communicationUnitThreadPool.available() > 0) {
                communicationUnitThreadPool.start(ndsMqttManager);
            }
        }

    }

    void CommunicationUnitManager::setMQTTMsg(const int mqttType, const char *topicName, const int topicNameLen,
                                              const char *payload, const int payloadLen) {
        std::string strMsg;
        strMsg.append(payload,payloadLen);
        ndsMqttManager.setMqttMsg(strMsg);
    }

    int CommunicationUnitManager::onNeedSendDataToNDSMqtt(std::string &strData) {
        return callback->onNeedSendDataToNDSMqtt(strData);
    }

    void CommunicationUnitManager::setNewMp4File(const Mp4FileInfo &mp4FileInfo) {
        ndsMqttManager.setNewMp4File(mp4FileInfo);
    }

    int CommunicationUnitManager::onGetRTMPPushOpt(const std::string url, const int cameraId, const int opt) {
        return callback->onGetRTMPPushOpt(url, cameraId, opt);
    }

    void CommunicationUnitManager::setTurnSignal(const bool trunL, const bool trunR) {
        g3Rs485Manager.setTurnSignal(trunL,trunR);
    }

    void CommunicationUnitManager::setSpeed(const float speed, const int baseOf) {
        g3Rs485Manager.setSpeed(speed,baseOf);
        g3SocketComManager.setCurSpeed(speed);
    }

    int CommunicationUnitManager::setUpgradeRS485(const int peripheralId,const std::string &upgradeFilePath) {
        return g3Rs485Manager.setUpgradeRS485(peripheralId,upgradeFilePath);
    }

    void CommunicationUnitManager::onGetUpgradeResultFromRS485(G3RS485UpgradeResult &upgradeResult) {
        callback->onGetUpgradeResultFromRS485(upgradeResult);
    }

    void CommunicationUnitManager::onGetSetDispalyParamsResultFromRS485(
            G3RS485SetDispalyParamsResult &setDispalyParamsResult) {
        callback->onGetSetDispalyParamsResultFromRS485(setDispalyParamsResult);
    }

    int CommunicationUnitManager::setDispalyParams(const int displayMode, const int iconMode) {
        return g3Rs485Manager.setDispalyParams(displayMode,iconMode);
    }

    int
    CommunicationUnitManager::onGetSetPeripheralDisplayParams(SetPeripheralDisplayParams displayParams, const char *ip,
                                                              const int port) {
        return callback->onGetSetPeripheralDisplayParams(displayParams, ip, port);
    }

    void CommunicationUnitManager::setDispalyParamsResultToSocket(const int devicesId, const int result, const char *ip,
                                                                  const int port) {
        g3SocketComManager.setDispalyParamsResultToSocket(devicesId,result,ip,port);

    }

    void CommunicationUnitManager::onGetGetG3AndG4DeviceVersion(GetG3AndG4DevicesVersion &getG3AndG4DevicesVersion,
                                                                const char *ip, const int port) {
        callback->onGetGetG3AndG4DeviceVersion(getG3AndG4DevicesVersion, ip, port);
    }

    void CommunicationUnitManager::getRS485PeripheralVersion(const int peripheralId) {
        g3Rs485Manager.getRS485PeripheralVersion(peripheralId);

    }

    void CommunicationUnitManager::onGetStopButtonStatusFromMODBUS(MODBUS_StopButtonStatus &stopButtonStatus) {
        callback->onGetStopButtonStatusFromMODBUS(stopButtonStatus);
    }

    void CommunicationUnitManager::onNeedSendDataToRS232(const uint8_t *buf, const int len) {
        callback->onNeedSendDataToRS232(buf, len);
    }

    void CommunicationUnitManager::onGetPeripheralUpgradeResultFromMCUUART(
            G3AndMCUUartPeripheralUpgradeResult &peripheralUpgradeResult) {
        callback->onGetPeripheralUpgradeResultFromMCUUART(peripheralUpgradeResult);

    }

    void CommunicationUnitManager::setVehicleRealtimeStatus(Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        g3Rs485Manager.setVehicleRealtimeStatus(vehicleRealtimeStatus);
        g3SocketComManager.setVehicleRealtimeStatus(vehicleRealtimeStatus);
    }

    StartMRV220LogFileDownloadResult
    CommunicationUnitManager::onGetStartMRV220LogFileDownload(StartMRV220LogFileDownload &startMrv220LogFileDownload,
                                                              const char *ip, const int port) {
        return callback->onGetStartMRV220LogFileDownload(startMrv220LogFileDownload, ip, port);
    }

    int CommunicationUnitManager::onGetSetMultimediaFileEncryptionKey(
            SetMultimediaFileEncryptionKey &setMultimediaFileEncryptionKey, const char *ip, const int port) {
        return callback->onGetSetMultimediaFileEncryptionKey(setMultimediaFileEncryptionKey, ip, port);
    }

    void CommunicationUnitManager::setDetectInfo_CameraStatus(G3DetectionResult &detectionResult) {
        g3SocketComManager.setDetectInfo_CameraStatus(detectionResult);
        g3Rs485Manager.setDetectInfo_CameraStatus(detectionResult);
    }

    int CommunicationUnitManager::onGetSetCameraInputTypeFilterConfig(
            SetCameraInputTypeFilterConfig &setCameraInputTypeFilterConfig, const char *ip, const int port) {
        /* 直接让外面处理 */
        return callback->onGetSetCameraInputTypeFilterConfig(setCameraInputTypeFilterConfig, ip, port);
    }

    void CommunicationUnitManager::onGetChangeRS485Baudrate(const int baudrate) {
        /* 直接让外面处理 */
        callback->onGetChangeRS485Baudrate(baudrate);
    }

    void CommunicationUnitManager::setDetectInfo_BarCode(G3DetectionResult &detectionInfo,
                                                           std::vector<BarCodeInfo> &barCodeInfoList) {
        /* 目前只发给Socket通信 */
        g3SocketComManager.setDetectInfo_BarCode(detectionInfo,barCodeInfoList);
        /* 发给MCU */
        g3AndMcuUartManager.setDetectInfo_BarCode(detectionInfo,barCodeInfoList);
    }

    int CommunicationUnitManager::onGetSetCameraReduceEffectInfoFromSocket(
            SetCameraReduceEffectInfo &setCameraReduceEffectInfo, const char *ip, const int port) {
        return callback->onGetSetCameraReduceEffectInfoFromSocket(setCameraReduceEffectInfo, ip, port);
    }

    int CommunicationUnitManager::onGetSetProductionTestingSwitchFromSocket(
            SetProductionTestingSwitch &setProductionTestingSwitch, const char *ip, const int port) {
        return callback->onGetSetProductionTestingSwitchFromSocket(setProductionTestingSwitch, ip, port);
    }

    void CommunicationUnitManager::setGSensorData(float value_x, float value_y, float value_z) {
        g3SocketComManager.setGSensorData(value_x,value_y,value_z);
    }

    int CommunicationUnitManager::onGetStartDeviceUpgradeFromNDS(const int fileType, const std::string &fileUrl) {
        return callback->onGetStartDeviceUpgradeFromNDS(fileType, fileUrl);
    }

    void CommunicationUnitManager::setNewJPGFile(const JPGFileInfo &jpgFileInfo) {
        ndsMqttManager.setNewJPGFile(jpgFileInfo);
    }

    int CommunicationUnitManager::onGetTCPRealviewOptFromSocket(TCPRealviewOpt &tcpRealviewOpt, const char *ip,
                                                                const int port) {
        return callback->onGetTCPRealviewOptFromSocket(tcpRealviewOpt, ip, port);
    }


}

