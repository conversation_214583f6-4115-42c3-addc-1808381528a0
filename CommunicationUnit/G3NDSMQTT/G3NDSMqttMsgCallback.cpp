//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/1.
//

#include "G3NDSMqttMsgCallback.h"

namespace vis {
    void G3NDSMqttMsgCallback::onGetNdsMqttMsgUniversalAnswer(NdsMqttMsgUniversalAnswer &universalAnswer) {
        printf("G3NDSMqttMsgCallback::onGetNdsMqttMsgUniversalAnswer \n");
    }

    void G3NDSMqttMsgCallback::onGetNdsMqttMsgGetAlarmEvidence(NdsMqttMsgGetAlarmEvidence &getAlarmEvidence) {
        printf("G3NDSMqttMsgCallback::onGetNdsMqttMsgGetAlarmEvidence \n");
    }

    void G3NDSMqttMsgCallback::onGetNdsMqttMsgStartRealView(G3NDSMqttMsgStartRealView &startRealView) {
        printf("G3NDSMqttMsgCallback::onGetNdsMqttMsgStartRealView \n");
    }

    void G3NDSMqttMsgCallback::onGetNdsMqttMsgCheckDVRfile(NdsMqttMsgCheckDVRfile &checkDvRfile) {
        printf("G3NDSMqttMsgCallback::onGetNdsMqttMsgCheckDVRfile \n");
    }

    void G3NDSMqttMsgCallback::onGetNdsMqttMsgGetDVRFile(NdsMqttMsgGetDVRFile &getDvrFile) {
        printf("G3NDSMqttMsgCallback::onGetNdsMqttMsgGetDVRFile \n");
    }

    void G3NDSMqttMsgCallback::onGetAlarmEvidenceUploadResult(
            NdsMqttMsgAlarmEvidenceUploadFinish &alarmEvidenceUploadFinish) {
        printf("G3NDSMqttMsgCallback::onGetAlarmEvidenceUploadResult \n");
    }

    void G3NDSMqttMsgCallback::onGetDVRFileUploadResult(NdsMqttMsgDVRFileUploadResult &dvrFileUploadResult) {
        printf("G3NDSMqttMsgCallback::onGetDVRFileUploadResult \n");
    }

    void
    G3NDSMqttMsgCallback::onGetG3NDSMqttMsgStartRealViewControl(G3NDSMqttMsgStartRealViewControl &realViewControl) {
        printf("G3NDSMqttMsgCallback::onGetG3NDSMqttMsgStartRealViewControl \n");
    }

    void G3NDSMqttMsgCallback::onGetG3NDSMqttMsgParamsSet(G3NDSMqttMsgParamsSet &paramsSet) {
        printf("G3NDSMqttMsgCallback::onGetG3NDSMqttMsgParamsSet \n");
    }

    void G3NDSMqttMsgCallback::onGetG3NDSMqttMsgParamsQuery(G3NDSMqttMsgParamsQuery &paramsQuery) {
        printf("G3NDSMqttMsgCallback::onGetG3NDSMqttMsgParamsQuery \n");
    }

    void G3NDSMqttMsgCallback::onGetG3NDSMqttMsgFunctionLockSet(G3NDSMqttMsgFunctionLockSet &functionLockSet) {
        printf("G3NDSMqttMsgCallback::onGetG3NDSMqttMsgFunctionLockSet \n");
    }

    void G3NDSMqttMsgCallback::onGetG3NDSMqttMsgUpgradeDevice(G3NDSMqttMsgUpgradeDevice &upgradeDevice) {
        printf("G3NDSMqttMsgCallback::onGetG3NDSMqttMsgUpgradeDevice \n");
    }
} // vis