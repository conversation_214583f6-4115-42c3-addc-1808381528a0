//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/1.
//

#include "NdsMqttMsgCheckDVRfile.h"

namespace vis {
    NdsMqttMsgCheckDVRfile::NdsMqttMsgCheckDVRfile() {
        setMsgId(NDSMQTTMSGID_CHECK_DVRFILE);
    }

    int NdsMqttMsgCheckDVRfile::toDecode(const std::string &str) {
        int ret = -1;
        if(!str.empty()){
            Json::Reader reader;
            Json::Value value;
            reader.parse(str, value);

            if(value.isMember("cameraId")){
                cameraId = value["cameraId"].asInt();
            }
            if(value.isMember("timestamp")){
                timestamp = value["timestamp"].asUInt();
            }
            if(value.isMember("timezone")){
                timezone = value["timezone"].asString();
            }

            ret = 0;
        }
        return ret;
    }

    int NdsMqttMsgCheckDVRfile::getCameraId() const {
        return cameraId;
    }

    void NdsMqttMsgCheckDVRfile::setCameraId(int cameraId) {
        NdsMqttMsgCheckDVRfile::cameraId = cameraId;
    }

    uint32_t NdsMqttMsgCheckDVRfile::getTimestamp() const {
        return timestamp;
    }

    void NdsMqttMsgCheckDVRfile::setTimestamp(uint32_t timestamp) {
        NdsMqttMsgCheckDVRfile::timestamp = timestamp;
    }

    const std::string &NdsMqttMsgCheckDVRfile::getTimezone() const {
        return timezone;
    }

    void NdsMqttMsgCheckDVRfile::setTimezone(const std::string &timezone) {
        NdsMqttMsgCheckDVRfile::timezone = timezone;
    }


} // vis