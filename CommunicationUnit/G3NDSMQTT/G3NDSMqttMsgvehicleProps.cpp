//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/31.
//

#include "G3NDSMqttMsgvehicleProps.h"

namespace vis {
    int G3NDSMqttMsgvehicleProps::getOdo() const {
        return ODO;
    }

    void G3NDSMqttMsgvehicleProps::setOdo(int odo) {
        ODO = odo;
    }

    int G3NDSMqttMsgvehicleProps::getGpsStatus() const {
        return gpsStatus;
    }

    void G3NDSMqttMsgvehicleProps::setGpsStatus(int gpsStatus) {
        G3NDSMqttMsgvehicleProps::gpsStatus = gpsStatus;
    }

    int G3NDSMqttMsgvehicleProps::getNetworkDbm() const {
        return networkDbm;
    }

    void G3NDSMqttMsgvehicleProps::setNetworkDbm(int networkDbm) {
        G3NDSMqttMsgvehicleProps::networkDbm = networkDbm;
    }

    const std::string &G3NDSMqttMsgvehicleProps::getTimezone() const {
        return timezone;
    }

    void G3NDSMqttMsgvehicleProps::setTimezone(const std::string &timezone) {
        G3NDSMqttMsgvehicleProps::timezone = timezone;
    }
} // vis