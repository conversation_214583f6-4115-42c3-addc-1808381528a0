//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/31.
//

#include "G3NDSMqttMsgEventDownload.h"

namespace vis {
    G3NDSMqttMsgEventDownload::G3NDSMqttMsgEventDownload() {
        setMsgId(NDSMQTTMSGID_EVENT_DOWNLOAD);
    }

    int G3NDSMqttMsgEventDownload::toDecode(const std::string &str) {
        int ret = -1;
        if(!str.empty()){
            Json::Reader reader;
            Json::Value value;
            reader.parse(str, value);

            if(value.isMember("eventName")){
                eventName = value["eventName"].asUInt();
            }
            if(value.isMember("eventValue")){
                eventValue = value["eventValue"].asString();
            }
            ret = 0;
        }
        return ret;
    }

    const std::string &G3NDSMqttMsgEventDownload::getEventName() const {
        return eventName;
    }

    void G3NDSMqttMsgEventDownload::setEventName(const std::string &eventName) {
        G3NDSMqttMsgEventDownload::eventName = eventName;
    }

    const std::string &G3NDSMqttMsgEventDownload::getEventValue() const {
        return eventValue;
    }

    void G3NDSMqttMsgEventDownload::setEventValue(const std::string &eventValue) {
        G3NDSMqttMsgEventDownload::eventValue = eventValue;
    }
} // vis