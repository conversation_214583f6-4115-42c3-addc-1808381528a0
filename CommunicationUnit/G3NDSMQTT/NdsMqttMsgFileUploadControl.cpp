//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/1.
//

#include "NdsMqttMsgFileUploadControl.h"

namespace vis {
    NdsMqttMsgFileUploadControl::NdsMqttMsgFileUploadControl() {
        setMsgId(NDSMQTTMSGID_FILE_UPLOAD_CONTROL);

    }

    int NdsMqttMsgFileUploadControl::toDecode(const std::string &str) {
        int ret = -1;
        if(!str.empty()){
            Json::Reader reader;
            Json::Value value;
            reader.parse(str, value);

            if(value.isMember("opt")){
                opt = value["reSerialNoptum"].asUInt();
            }
            if(value.isMember("fileKey")){
                fileKey = value["fileKey"].asString();
            }

            ret = 0;
        }
        return ret;
    }

    int NdsMqttMsgFileUploadControl::getOpt() const {
        return opt;
    }

    void NdsMqttMsgFileUploadControl::setOpt(int opt) {
        NdsMqttMsgFileUploadControl::opt = opt;
    }
} // vis