//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/1.
//

#include "NdsMqttMsgAlarmEvidenceUploadControl.h"

namespace vis {
    NdsMqttMsgAlarmEvidenceUploadControl::NdsMqttMsgAlarmEvidenceUploadControl() {
        setMsgId(NDSMQTTMSGID_ALARM_EVIDENCE_UPLOAD_CONTROL);
    }

    int NdsMqttMsgAlarmEvidenceUploadControl::toDecode(const std::string &str) {
        int ret = -1;
        if(!str.empty()){
            Json::Reader reader;
            Json::Value value;
            reader.parse(str, value);

            if(value.isMember("reSerialNum")){
                reSerialNum = value["reSerialNum"].asUInt();
            }
            if(value.isMember("command")){
                command = value["command"].asUInt();
            }
            if(value.isMember("taskUuid")){
                taskUuid = value["taskUuid"].asString();
            }
            if(value.isMember("bosToken")){
                bosToken = value["bosToken"].asString();
            }
            ret = 0;
        }
        return ret;
    }


} // vis