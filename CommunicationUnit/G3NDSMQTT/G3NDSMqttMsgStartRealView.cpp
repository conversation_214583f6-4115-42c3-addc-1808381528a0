//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/31.
//

#include "G3NDSMqttMsgStartRealView.h"

namespace vis {
    G3NDSMqttMsgStartRealView::G3NDSMqttMsgStartRealView() {
        setMsgId(NDSMQTTMSGID_START_REALVIEW);
    }

    int G3NDSMqttMsgStartRealView::toDecode(const std::string &str) {
        int ret = -1;
        if(!str.empty()){
            Json::Reader reader;
            Json::Value value;
            reader.parse(str, value);
            ret = 0;

            decode(str);

            if(value.isMember("address")){
                address = value["address"].asString();
            }else{
                ret = -1;
            }
            if(value.isMember("channel")){
                channel = value["channel"].asInt();
            }else{
                ret = -1;
            }
            if(value.isMember("dataType")){
                dataType = value["dataType"].asInt();
            }else{
                ret = -1;
            }
            if(value.isMember("streamType")){
                streamType = value["streamType"].asInt();
            }else{
                ret = -1;
            }

        }
        return ret;
    }

    const std::string &G3NDSMqttMsgStartRealView::getAddress() const {
        return address;
    }

    void G3NDSMqttMsgStartRealView::setAddress(const std::string &address) {
        G3NDSMqttMsgStartRealView::address = address;
    }

    int G3NDSMqttMsgStartRealView::getChannel() const {
        return channel;
    }

    void G3NDSMqttMsgStartRealView::setChannel(int channel) {
        channel = channel;
    }

    int G3NDSMqttMsgStartRealView::getDataType() const {
        return dataType;
    }

    void G3NDSMqttMsgStartRealView::setDataType(int dataType) {
        G3NDSMqttMsgStartRealView::dataType = dataType;
    }

    int G3NDSMqttMsgStartRealView::getStreamType() const {
        return streamType;
    }

    void G3NDSMqttMsgStartRealView::setStreamType(int streamType) {
        G3NDSMqttMsgStartRealView::streamType = streamType;
    }


} // vis