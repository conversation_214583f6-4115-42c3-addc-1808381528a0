//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/5.
//

#include "G3NSDDVRFileUploadUtils.h"
#include "XuHttpUtil.h"
namespace vis {
    G3NSDDVRFileUploadUtils::G3NSDDVRFileUploadUtils() {

    }

    void G3NSDDVRFileUploadUtils::init(const std::string curl, const std::string filePath,G3NDSMqttMsgCallback &ndsMqttMsgCallback) {
        uploadCurl.clear();
        uploadCurl.append(curl.c_str());
        uploadFialePath.clear();
        uploadFialePath.append(filePath.c_str());
        callback = &ndsMqttMsgCallback;
    }

    void G3NSDDVRFileUploadUtils::start() {
        sendThread.start(*this);
    }

    void G3NSDDVRFileUploadUtils::run() {
        std::string pthreadName = "NSDDVRUp";
        pthread_setname_np(pthread_self(), pthreadName.c_str());
        working = true;
        try {
            std::string uploadRet;
            XuHttpUtil::getInstance().postDVRFileToNDS(uploadCurl,uploadFialePath,uploadRet);
            printf("uploadRet=%s \n",uploadRet.c_str());

            Json::Reader reader;
            Json::Value value;
            reader.parse(uploadRet, value);

            if(value.isMember("result")){
                if(value["result"].asUInt() == 0){
                    dvrFileUploadResult.setResult(NdsMqttMsgDVRFileUploadResult::RESULT_SUCCESS);
                }else{
                    dvrFileUploadResult.setResult(NdsMqttMsgDVRFileUploadResult::RESULT_FAILED);
                }

            }else{
                dvrFileUploadResult.setResult(NdsMqttMsgDVRFileUploadResult::RESULT_FAILED);
            }

            callback->onGetDVRFileUploadResult(dvrFileUploadResult);

        }catch (...){
            printf("postAttchmentFileToNDS has error! %s \n",strerror(errno));
        }
        working = false;
        pthread_setname_np(pthread_self(), "Finish");
    }

    bool G3NSDDVRFileUploadUtils::isWorking() const {
        return working;
    }

    void G3NSDDVRFileUploadUtils::setWorking(bool working) {
        G3NSDDVRFileUploadUtils::working = working;
    }

    const NdsMqttMsgDVRFileUploadResult &G3NSDDVRFileUploadUtils::getDvrFileUploadResult() const {
        return dvrFileUploadResult;
    }

    void G3NSDDVRFileUploadUtils::setDvrFileUploadResult(const NdsMqttMsgDVRFileUploadResult &dvrFileUploadResult) {
        G3NSDDVRFileUploadUtils::dvrFileUploadResult.setFileKey(dvrFileUploadResult.getFileKey());
    }
} // vis