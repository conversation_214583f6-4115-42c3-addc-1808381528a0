//
// Created by <PERSON><PERSON>g<PERSON><PERSON><PERSON> on 2023/8/1.
//

#include "NdsMqttMsgGetDVRFile.h"

namespace vis {
    NdsMqttMsgGetDVRFile::NdsMqttMsgGetDVRFile() {
        setMsgId(NDSMQTTMSGID_GET_DVRFILE);
    }


    int NdsMqttMsgGetDVRFile::toDecode(const std::string &str) {
        int ret = -1;
        if(!str.empty()){
            Json::Reader reader;
            Json::Value value;
            reader.parse(str, value);

            if(value.isMember("cameraId")){
                cameraId = value["cameraId"].asUInt();
            }
            if(value.isMember("filename")){
                filename = value["filename"].asString();
            }
            if(value.isMember("fileKey")){
                fileKey = value["fileKey"].asString();
            }
            if(value.isMember("uploadUrl")){
                uploadUrl = value["uploadUrl"].asString();
            }
            if(value.isMember("offset")){
                offset = value["offset"].asUInt();
            }

            ret = 0;
        }
        return ret;
    }

    int NdsMqttMsgGetDVRFile::getCameraId() const {
        return cameraId;
    }

    void NdsMqttMsgGetDVRFile::setCameraId(int cameraId) {
        NdsMqttMsgGetDVRFile::cameraId = cameraId;
    }

    const std::string &NdsMqttMsgGetDVRFile::getFilename() const {
        return filename;
    }

    void NdsMqttMsgGetDVRFile::setFilename(const std::string &filename) {
        NdsMqttMsgGetDVRFile::filename = filename;
    }

    const std::string &NdsMqttMsgGetDVRFile::getFileKey() const {
        return fileKey;
    }

    void NdsMqttMsgGetDVRFile::setFileKey(const std::string &fileKey) {
        NdsMqttMsgGetDVRFile::fileKey = fileKey;
    }

    const std::string &NdsMqttMsgGetDVRFile::getUploadUrl() const {
        return uploadUrl;
    }

    void NdsMqttMsgGetDVRFile::setUploadUrl(const std::string &uploadUrl) {
        NdsMqttMsgGetDVRFile::uploadUrl = uploadUrl;
    }

    int NdsMqttMsgGetDVRFile::getOffset() const {
        return offset;
    }

    void NdsMqttMsgGetDVRFile::setOffset(int offset) {
        NdsMqttMsgGetDVRFile::offset = offset;
    }


} // vis