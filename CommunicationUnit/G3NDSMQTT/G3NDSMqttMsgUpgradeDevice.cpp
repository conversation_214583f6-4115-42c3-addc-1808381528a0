//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/1.
//

#include "G3NDSMqttMsgUpgradeDevice.h"

namespace vis {
    G3NDSMqttMsgUpgradeDevice::G3NDSMqttMsgUpgradeDevice() {
        setMsgId(NDSMQTTMSGID_UPGRADE_DEVICE);
    }

    int G3NDSMqttMsgUpgradeDevice::toDecode(const std::string &str) {
        Json::Reader reader;
        Json::Value value;
        reader.parse(str, value);

        if(value.isMember("softwareType")){
            softwareType = value["softwareType"].asUInt();
        }

        if(value.isMember("version")){
            version = value["version"].asString();
        }

        if(value.isMember("fileUrl")){
            fileUrl = value["fileUrl"].asString();
        }


        return 0;
    }

    int G3NDSMqttMsgUpgradeDevice::getSoftwareType() const {
        return softwareType;
    }

    void G3NDSMqttMsgUpgradeDevice::setSoftwareType(int softwareType) {
        G3NDSMqttMsgUpgradeDevice::softwareType = softwareType;
    }


    const std::string &G3NDSMqttMsgUpgradeDevice::getFileUrl() const {
        return fileUrl;
    }

    void G3NDSMqttMsgUpgradeDevice::setFileUrl(const std::string &fileUrl) {
        G3NDSMqttMsgUpgradeDevice::fileUrl = fileUrl;
    }

    const std::string &G3NDSMqttMsgUpgradeDevice::getVersion() const {
        return version;
    }

    void G3NDSMqttMsgUpgradeDevice::setVersion(const std::string &version) {
        G3NDSMqttMsgUpgradeDevice::version = version;
    }
} // vis