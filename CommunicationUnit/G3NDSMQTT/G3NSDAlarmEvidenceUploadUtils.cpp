//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/2.
//

#include <cstring>
#include "G3NSDAlarmEvidenceUploadUtils.h"
#include "XuHttpUtil.h"

namespace vis {
    G3NSDAlarmEvidenceUploadUtils::G3NSDAlarmEvidenceUploadUtils() {


    }

    void G3NSDAlarmEvidenceUploadUtils::run() {
        std::string pthreadName = "NSDEvidenceUp";
        pthread_setname_np(pthread_self(), pthreadName.c_str());
        working = true;
        try {
            std::string uploadRet;
            printf("run url:%s  path:%s \n", uploadCurl.c_str(), uploadFialePath.c_str());
            XuHttpUtil::getInstance().postAttchmentFileToNDS(uploadCurl,uploadFialePath,uploadRet);
            printf("uploadRet=%s \n",uploadRet.c_str());

            Json::Reader reader;
            Json::Value value;
            reader.parse(uploadRet, value);

            if(value.isMember("result")){
                if(value["result"].asUInt() == 0){
                    alarmEvidenceUploadFinish.setResult(NdsMqttMsgAlarmEvidenceUploadFinish::RESULT_SUCCESS);
                }else{
                    alarmEvidenceUploadFinish.setResult(NdsMqttMsgAlarmEvidenceUploadFinish::RESULT_FAILED);
                }

            }else{
                alarmEvidenceUploadFinish.setResult(NdsMqttMsgAlarmEvidenceUploadFinish::RESULT_FAILED);
            }

            callback->onGetAlarmEvidenceUploadResult(alarmEvidenceUploadFinish);


        }catch (...){
            printf("postAttchmentFileToNDS has error! %s \n",strerror(errno));
        }
        working = false;
        pthread_setname_np(pthread_self(), "Finish");
    }

    void G3NSDAlarmEvidenceUploadUtils::init(const std::string curl,const std::string filePath,G3NDSMqttMsgCallback &ndsMqttMsgCallback) {
        uploadCurl.clear();
        uploadCurl.append(curl.c_str());
        uploadFialePath.clear();
        uploadFialePath.append(filePath.c_str());
        callback = &ndsMqttMsgCallback;
    }

    void G3NSDAlarmEvidenceUploadUtils::start() {
        sendThread.start(*this);
    }

    bool G3NSDAlarmEvidenceUploadUtils::isWorking() const {
        return working;
    }

    void G3NSDAlarmEvidenceUploadUtils::setWorking(bool working) {
        G3NSDAlarmEvidenceUploadUtils::working = working;
    }

    const NdsMqttMsgAlarmEvidenceUploadFinish &G3NSDAlarmEvidenceUploadUtils::getAlarmEvidenceUploadFinish() const {
        return alarmEvidenceUploadFinish;
    }

    void G3NSDAlarmEvidenceUploadUtils::setAlarmEvidenceUploadFinish(
            const NdsMqttMsgAlarmEvidenceUploadFinish &alarmEvidenceUploadFinish) {
        G3NSDAlarmEvidenceUploadUtils::alarmEvidenceUploadFinish.setReSerialNum(alarmEvidenceUploadFinish.getReSerialNum());
        G3NSDAlarmEvidenceUploadUtils::alarmEvidenceUploadFinish.setTaskUuid(alarmEvidenceUploadFinish.getTaskUuid());
    }


} // vis