//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/28.
//

#include "G3NDSMqttManager.h"

#include "XuTimeUtil.h"
#include "XuFile.h"
#include "G3NSDAlarmEvidenceUploadUtils.h"
#include "ntp_client.hpp"
#include "XuShell.h"
#include "WarMp4Recorder.h"
#include <unistd.h>

namespace vis {


    G3NDSMqttManager::G3NDSMqttManager() {

    }

    void G3NDSMqttManager::run() {
        std::string pthreadName = "NDSCom";
        pthread_setname_np(pthread_self(), pthreadName.c_str());
        uint64_t lastSendTime = 0;
        bool isSendVersion = true;

        /* �����Է��Ͷ�λ���� */
        needToStop = false;
        while (!needToStop) {
            /* �ȿ����Ƿ��Ѱ󶨣����û�󶨾ͷ��Ͱ󶨵���Ϣ ���˾ͷ��������� */
            if (isBinded) {
                /*  ����һ�°汾�� */
                if(isSendVersion){
                    G3NDSMqttMsgVesionUp versionUp;
                    std::string versionStr = "";
                    versionStr.append("0");
                    versionStr.append("-");
                    versionStr.append("0");
                    versionStr.append("-");
                    versionStr.append(getenv("G3_SOFTWARD_VERSION_NAME"));
                    versionStr.append("-");
                    versionStr.append(getenv("G3_FIRMWARE_VERSION"));
                    versionStr.append("-");
                    versionStr.append("0");
                    versionStr.append("-");
                    versionStr.append("0");
                    versionStr.append("-");
                    versionStr.append((strcmp(getenv("G3_MCU_VERSION_NAME"),"-1") == 0) ? "0" : getenv("G3_MCU_VERSION_NAME"));

                    versionUp.setUuid(G3_Configuration::getInstance().getDeviceUuid());
                    versionUp.setVersion(versionStr);
                    std::string versionUpStr = encoder.generateVersionUpStr(versionUp);
                    if (!versionUpStr.empty()) {
                        callback->onNeedSendDataToNDSMqtt(versionUpStr);
                    }
                    isSendVersion = false;
                }


                /* �ȳ���һ�·����ط��б�����Ķ��� */
                reSendMsg();

                /*acc״̬*/
                curAlarmUp.setAccStatus(1);
                /* �������� */
                curAlarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_NULL);
                /* ���� */
                Json::Value vehicleprops = curAlarmUp.getVehicleProps();
                vehicleprops["gpsStatus"] = G3_Configuration::getInstance().getCurGpsInfo().status;
                vehicleprops["networkDbm"] = -1;
                vehicleprops["MRV220ConnectStatus"][0] = 2;
                std::string timezone = G3_Configuration::getInstance().getLocalTimeZone();
                if (timezone.data()[0] == ':') {
                    timezone = G3_Configuration::getInstance().getLocalTimeZone().substr(1,
                                                                                         G3_Configuration::getInstance().getLocalTimeZone().size() -
                                                                                         1);
                }
                vehicleprops["timezone"] = timezone.c_str();
                curAlarmUp.setVehicleProps(vehicleprops);
                /* γ��  */
                curAlarmUp.setLatitude(G3_Configuration::getInstance().getCurGpsInfo().latitude == -1.0 ? 0 : G3_Configuration::getInstance().getCurGpsInfo().latitude);
                /* ����  */
                curAlarmUp.setLongitude(G3_Configuration::getInstance().getCurGpsInfo().longitude == -1.0 ? 0 : G3_Configuration::getInstance().getCurGpsInfo().longitude);
                /* ���θ߶�  */
                curAlarmUp.setHeight(G3_Configuration::getInstance().getCurGpsInfo().altitude == 0xFFFF ? 0 : G3_Configuration::getInstance().getCurGpsInfo().altitude);
                /* �ٶ�  */
                curAlarmUp.setSpeed(curSpeed);
                /* ���� */
                curAlarmUp.setDirect(G3_Configuration::getInstance().getCurGpsInfo().direction == 0xFFFF ? 0 : G3_Configuration::getInstance().getCurGpsInfo().direction);
                /* ������ʼʱ�䣬unixʱ��� */
                uint64_t curTime = XuTimeUtil::getInstance().currentTimeMillis() / 1000;
                curAlarmUp.setTime(curTime);
                /* ������ʼʱ�䣬unixʱ��� */
                curAlarmUp.setAlarmEndTime(curTime);
                /* ÿ��MRV220�Ĺ���������Ϣ */
                std::vector<std::string> functionLock;
                functionLock.push_back(G3_Configuration::getInstance().getLockstr());
                curAlarmUp.setFunctionLock(functionLock);
                /* ������״̬ */
                Json::Value functionLockStatus;
                functionLockStatus["MRV220A"]["connection"] = true;
                functionLockStatus["MRV220A"]["sendCMD"] = true;
                functionLockStatus["MRV220A"]["result"] = 3;
                curAlarmUp.setFunctionLockStatus(functionLockStatus.toStyledString());
                /* ������ʶ�� */
                curAlarmUp.setAlarmRefKey("");
                /* ����״̬ */

                if(XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec() - lastSendTime >= 20){
                    Json::Value runningStatus;
                    runningStatus["MRV330_work_mode"] = 3;
                    Json::Value deviceCameList;
                    std::vector<CameraType> cameraList = G3_Configuration::getInstance().getCameraTypeList();
                    for(int i = 0; i < cameraList.size(); i ++){
                        deviceCameList[i]["cameraId"] = cameraList[i].cameraId;
                        deviceCameList[i]["cameraType"] = 0;
                        bool isCameraOpen = true;
                        switch (cameraList[i].cameraId) {
                            case CAMERA_ID_1:{
                                isCameraOpen = G3_Configuration::getInstance().isCamera1Opened();
                            }
                                break;
                            case CAMERA_ID_2:{
                                isCameraOpen = G3_Configuration::getInstance().isCamera2Opened();
                            }
                                break;
                            case CAMERA_ID_3:{
                                isCameraOpen = G3_Configuration::getInstance().isCamera3Opened();
                            }
                                break;
                            case CAMERA_ID_4:{
                                isCameraOpen = G3_Configuration::getInstance().isCamera4Opened();
                            }
                                break;
                        }
                        deviceCameList[i]["connectStatus"] = isCameraOpen;
                    }



                    runningStatus["Device_cam_list"] = deviceCameList;

                    curAlarmUp.setRunningStatus(runningStatus);
                    lastSendTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
                }else{
                    Json::Value runningStatus;
                    curAlarmUp.setRunningStatus(runningStatus);
                }


                std::string alarmUpStr = encoder.generateAlarmUpStr(curAlarmUp);
                sendMsg(alarmUpStr);
                sleep(5);

//                /* ����һ��ʱ�� */
//                updateTimeByNDS();


            } else {
                /* û�󶨣�����һ���󶨵���Ϣ */

                Json::Value bindInfo;
                bindInfo["provinceId"] = 44;
                bindInfo["cityId"] = 100;
                bindInfo["licenseNumber"] = "��A88888";
                bindInfo["color"] = 1;
                bindInfo["iccid"] = "89860619050060190021";
                G3NDSMqttMsgEventUpload eventUpload;

                eventUpload.setEventName("bind");
                eventUpload.setEventValue(bindInfo.toStyledString());

                std::string bindStr = encoder.generateEventUploadStr(eventUpload);
                if (!bindStr.empty()) {
                    callback->onNeedSendDataToNDSMqtt(bindStr);
                }

                sleep(5);
            }


        }
        pthread_setname_np(pthread_self(), "Finish");

    }

    void G3NDSMqttManager::setMqttMsg(std::string msgStr) {
        decoder.paraseNDSMqttMsg(msgStr);
    }

    void G3NDSMqttManager::onGetNdsMqttMsgUniversalAnswer(vis::NdsMqttMsgUniversalAnswer &universalAnswer) {
        printf("onGetNdsMqttMsgUniversalAnswer %s \n", universalAnswer.toString().c_str());
        switch (universalAnswer.getReMsgId()) {
            case NdsMqttMsgHeader::NDSMQTTMSGID_EVENT_UPLOAD: {
                if (universalAnswer.getResult() == 0) {
                    isBinded = true;
                } else {
                    printf("bind failed! \n");
                }

            }
                break;

        }
    }

    void G3NDSMqttManager::stop() {
        needToStop = true;
    }

    void G3NDSMqttManager::init(CommunicationDataCallback &communicationDataCallback) {
        callback = &communicationDataCallback;
        curClientId = G3_Configuration::getInstance().getNdsMqttConnectParams().clientId;
        decoder.init(curClientId, *this);
        reSendList.resize(MAX_RESEND_MSG_LIST_SIZE);
    }

    void G3NDSMqttManager::setDetectEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
//        printf("========================G3NDSMqttManager::setDetectEvent \n");

        if (!needToStop) {


            G3NDSMqttMsgAlarmUp alarmUp;

            /*acc״̬*/
            alarmUp.setAccStatus(1);

            /* ���� */
            Json::Value vehicleprops = alarmUp.getVehicleProps();
            vehicleprops["gpsStatus"] = G3_Configuration::getInstance().getCurGpsInfo().status;
            vehicleprops["networkDbm"] = -1;
            vehicleprops["MRV220ConnectStatus"][0] = 2;
            std::string timezone = G3_Configuration::getInstance().getLocalTimeZone();
            if (timezone.data()[0] == ':') {
                timezone = G3_Configuration::getInstance().getLocalTimeZone().substr(1,
                                                                                     G3_Configuration::getInstance().getLocalTimeZone().size() -
                                                                                     1);
            }
            vehicleprops["timezone"] = timezone.c_str();
            alarmUp.setVehicleProps(vehicleprops);
            /* γ��  */
            alarmUp.setLatitude(G3_Configuration::getInstance().getCurGpsInfo().latitude);
            /* ����  */
            alarmUp.setLongitude(G3_Configuration::getInstance().getCurGpsInfo().longitude);
            /* ���θ߶�  */
            alarmUp.setHeight(G3_Configuration::getInstance().getCurGpsInfo().altitude);
            /* �ٶ�  */
            alarmUp.setSpeed(curSpeed);
            /* ���� */
            alarmUp.setDirect(G3_Configuration::getInstance().getCurGpsInfo().direction);
            /* ������ʼʱ�䣬unixʱ��� */
            uint64_t curTime = XuTimeUtil::getInstance().currentTimeMillis();
            alarmUp.setTime(curTime / 1000);
            /* ������ʼʱ�䣬unixʱ��� */
            alarmUp.setAlarmEndTime(curTime / 1000);
            /* ÿ��MRV220�Ĺ���������Ϣ */
            std::vector<std::string> functionLock;
            functionLock.push_back(G3_Configuration::getInstance().getLockstr());
            alarmUp.setFunctionLock(functionLock);
            /* ������״̬ */
            Json::Value functionLockStatus;
            functionLockStatus["MRV220A"]["connection"] = true;
            functionLockStatus["MRV220A"]["sendCMD"] = true;
            functionLockStatus["MRV220A"]["result"] = 3;
            alarmUp.setFunctionLockStatus(functionLockStatus.toStyledString());


            switch (eventCode) {
                case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
                case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL1);
                }
                    break;

                case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL1);
                }
                    break;

                case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
                case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL2);
                }
                    break;

                case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL2);
                }
                    break;

                case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
                case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL1);
                }
                    break;

                case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL1);
                }
                    break;

                case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
                case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL2);
                }
                    break;

                case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL2);
                }
                    break;

                case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL1);
                }
                    break;

                case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL2);
                }
                    break;

                case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL1);
                }
                    break;

                case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL2);
                }
                    break;


                case EVENT_BSD_FORWARD_VEHICLE_LEVEL1: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL1);
                }
                    break;

                case EVENT_BSD_FORWARD_VEHICLE_LEVEL2: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL2);
                }
                    break;

                case EVENT_BSD_BACKWARD_VEHICLE_LEVEL1: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL1);
                }
                    break;

                case EVENT_BSD_BACKWARD_VEHICLE_LEVEL2: {
                    /* �������� */
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL1);
                }
                    break;

//            case EVENT_DSM: {
//
//            }
//                break;

                case EVENT_SBST_FORWARD_PDW_LEVEL1: {
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_PEDESTRIAN_COLLISION);
                }
                    break;

                case EVENT_SBST_FORWARD_PDW_LEVEL2: {
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_PEDESTRIAN_COLLISION);
                }
                    break;

                case EVENT_SBST_FORWARD_FCW_LEVEL1: {
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_FORWARD_COLLISION);
                }
                    break;

                case EVENT_SBST_FORWARD_FCW_LEVEL2: {
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_FORWARD_COLLISION);
                }
                    break;

                case EVENT_SBST_FORWARD_LDW_LEFT_DASHED: {
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_LEFT_LANE_DEPARTURE);
                }
                    break;

                case EVENT_SBST_FORWARD_LDW_LEFT_SOLID: {
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_LEFT_LANE_DEPARTURE);
                }
                    break;

                case EVENT_SBST_FORWARD_LDW_RIGHT_DASHED: {
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_RIGHT_LANE_DEPARTURE);
                }
                    break;

                case EVENT_SBST_FORWARD_LDW_RIGHT_SOLID: {
                    alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_RIGHT_LANE_DEPARTURE);
                }
                    break;


                case EVENT_ADAS_VEHICLE_TTC: {
                    if (alarmEventInfo.adasAlarmInfo.eventStatus != EVENTSTATUS_STOP) {
                        alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_FORWARD_COLLISION);
                    }
                }
                    break;

                case EVENT_ADAS_VEHICLE_HMW: {
                    if (alarmEventInfo.adasAlarmInfo.eventStatus != EVENTSTATUS_STOP) {
                        alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_HMW);
                    }
                }
                    break;

                case EVENT_ADAS_PEDESTRIAN_HMW: {
                    if (alarmEventInfo.adasAlarmInfo.eventStatus != EVENTSTATUS_STOP) {
                        alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_PEDESTRIAN_COLLISION);
                    }

                }
                    break;

                case EVENT_ADAS_LDW_LEFT_SOLID: {
                    if (alarmEventInfo.adasAlarmInfo.eventStatus != EVENTSTATUS_STOP) {
                        alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_LEFT_LANE_DEPARTURE);
                    }
                }
                    break;

                case EVENT_ADAS_LDW_LEFT_DASH: {
                    if (alarmEventInfo.adasAlarmInfo.eventStatus != EVENTSTATUS_STOP) {
                        alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_LEFT_LANE_DEPARTURE);
                    }
                }
                    break;

                case EVENT_ADAS_LDW_RIGHT_SOLID: {
                    if (alarmEventInfo.adasAlarmInfo.eventStatus != EVENTSTATUS_STOP) {
                        alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_RIGHT_LANE_DEPARTURE);
                    }
                }
                    break;

                case EVENT_ADAS_LDW_RIGHT_DASH: {
                    if (alarmEventInfo.adasAlarmInfo.eventStatus != EVENTSTATUS_STOP) {
                        alarmUp.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_RIGHT_LANE_DEPARTURE);
                    }
                }
                    break;
            }

            std::string alarmRefKey;
            /* ��ʱ��� */
            alarmRefKey.append(std::to_string(alarmEventInfo.alarmTime) + "_");
            /* ����ͷID */
            alarmRefKey.append(std::to_string(curCameraType.cameraId) + "_");
            /* �¼����� */
            alarmRefKey.append(std::to_string(eventCode));
            /* ������ʶ�� */
            alarmUp.setAlarmRefKey(alarmRefKey);

            std::string alarmUpStr = encoder.generateAlarmUpStr(alarmUp);
            int sendRet = sendMsg(alarmUpStr);
            /* �����¼���Ҫ���ط� */
            if (sendRet != 0) {
                addToSendMsgList(alarmUpStr);
            }
        }
    }

    void G3NDSMqttManager::setNewMp4File(const Mp4FileInfo &mp4FileInfo) {

        if (!needToStop) {
            printf("====================================G3NDSMqttManager::setNewMp4File path=%s\n",
                   mp4FileInfo.getFilePath().c_str());

            /* �յ��µı���֤���ˣ����ϱ�һ�� */
            G3NDSMqttMsgAlarmEvidence alarmEvidence;

            alarmEvidence.setChannel(mp4FileInfo.getCameraId() + 1);
            alarmEvidence.setStartTime(mp4FileInfo.getStartTime() / 1000);
            alarmEvidence.setEndTime(mp4FileInfo.getStopTime() / 1000);
            alarmEvidence.setDataType(0);
            alarmEvidence.setStreamType(0);
            alarmEvidence.setStorageType(0);
            alarmEvidence.setFileName(mp4FileInfo.getFilePath());
            alarmEvidence.setFileSize(XuFile::getInstance().getFileLength(mp4FileInfo.getFilePath().c_str()));
            switch (mp4FileInfo.getFileType()) {
                case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
                case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1:
                case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
                case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1:
                case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1:
                case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
                    /* �������� */
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL1);
                }
                    break;

                case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_FORWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_BACKWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_BACKWARD_VEHICLE_LEVEL2: {
                    /* �������� */
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL1);
                }
                    break;

                case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
                case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2:
                case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2:
                case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
                case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2:
                case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
                    /* �������� */
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL2);
                }
                    break;

                case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_FORWARD_VEHICLE_LEVEL2: {
                    /* �������� */
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL2);
                }
                    break;

                case EVENT_SBST_FORWARD_PDW_LEVEL1:
                case EVENT_SBST_FORWARD_PDW_LEVEL2:
                case EVENT_ADAS_PEDESTRIAN_HMW: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_PEDESTRIAN_COLLISION);
                }
                    break;

                case EVENT_SBST_FORWARD_FCW_LEVEL1:
                case EVENT_SBST_FORWARD_FCW_LEVEL2:
                case EVENT_ADAS_VEHICLE_TTC: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_FORWARD_COLLISION);
                }
                    break;

                case EVENT_SBST_FORWARD_LDW_LEFT_DASHED:
                case EVENT_SBST_FORWARD_LDW_LEFT_SOLID:
                case EVENT_ADAS_LDW_LEFT_SOLID:
                case EVENT_ADAS_LDW_LEFT_DASH: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_LEFT_LANE_DEPARTURE);
                }
                    break;

                case EVENT_SBST_FORWARD_LDW_RIGHT_DASHED:
                case EVENT_ADAS_LDW_RIGHT_DASH:
                case EVENT_ADAS_LDW_RIGHT_SOLID:
                case EVENT_SBST_FORWARD_LDW_RIGHT_SOLID: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_RIGHT_LANE_DEPARTURE);
                }
                    break;

                case EVENT_ADAS_VEHICLE_HMW: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_HMW);
                }
                    break;
            }

            std::string alarmRefKey;
            /* ��ʱ��� */
            alarmRefKey.append(std::to_string(mp4FileInfo.getAlarmTime()) + "_");
            /* ����ͷID */
            alarmRefKey.append(std::to_string(mp4FileInfo.getCameraId()) + "_");
            /* �¼����� */
            alarmRefKey.append(std::to_string(mp4FileInfo.getFileType()));

            alarmEvidence.setAlarmRefKey(alarmRefKey);


            std::string alarmEvidenceStr = encoder.generateAlarmEvidenceStr(alarmEvidence);
            int sendRet = sendMsg(alarmEvidenceStr);
            /* ������Ƶ��Ҫ���ط� */
            if (sendRet != 0) {
                addToSendMsgList(alarmEvidenceStr);
            }
        }
    }

    void G3NDSMqttManager::onGetNdsMqttMsgGetAlarmEvidence(NdsMqttMsgGetAlarmEvidence &getAlarmEvidence) {
        /* ��ȡһ���ļ�Ŀǰ��ǿ�Ƹ�MRV330����ȫ·���ϴ��� */

        printf("****************G3NDSMqttManager::onGetNdsMqttMsgGetAlarmEvidence url=%s  file=%s\n",
               getAlarmEvidence.getUrl().c_str(), getAlarmEvidence.getFileName().c_str());


        if (!XuFile::getInstance().fileExists(getAlarmEvidence.getFileName().c_str())) {
            NdsMqttMsgAlarmEvidenceUploadFinish alarmEvidenceUploadFinish;
            alarmEvidenceUploadFinish.setReSerialNum(getAlarmEvidence.getSerialNum());
            alarmEvidenceUploadFinish.setResult(NdsMqttMsgAlarmEvidenceUploadFinish::RESULT_NOT_EXIST);
            alarmEvidenceUploadFinish.setTaskUuid(getAlarmEvidence.getTaskUuid());
            std::string alarmEvidenceUploadFinishStr = encoder.generateAlarmEvidenceUploadFinishStr(
                    alarmEvidenceUploadFinish);
            sendMsg(alarmEvidenceUploadFinishStr);
        } else {
            int useIndex = -1;
            for (int i = 0; i < static_cast<int>(sizeof(alarmEvidenceUploadUtilList)); i++) {
                if (!alarmEvidenceUploadUtilList[i].isWorking()) {
                    useIndex = i;
                    break;
                }
            }
            if (useIndex != -1) {
                NdsMqttMsgAlarmEvidenceUploadFinish alarmEvidenceUploadFinish;
                alarmEvidenceUploadFinish.setReSerialNum(getAlarmEvidence.getSerialNum());
                alarmEvidenceUploadFinish.setTaskUuid(getAlarmEvidence.getTaskUuid());


                alarmEvidenceUploadUtilList[useIndex].init(getAlarmEvidence.getUrl(), getAlarmEvidence.getFileName(),
                                                           *this);
                alarmEvidenceUploadUtilList[useIndex].setAlarmEvidenceUploadFinish(alarmEvidenceUploadFinish);
                alarmEvidenceUploadUtilList[useIndex].start();


            } else {
                /* �ϴ����г��������ˣ�ֱ�ӻظ���æ */
                NdsMqttMsgAlarmEvidenceUploadFinish alarmEvidenceUploadFinish;
                alarmEvidenceUploadFinish.setReSerialNum(getAlarmEvidence.getSerialNum());
                alarmEvidenceUploadFinish.setResult(NdsMqttMsgAlarmEvidenceUploadFinish::RESULT_NOT_BUSY);
                alarmEvidenceUploadFinish.setTaskUuid(getAlarmEvidence.getTaskUuid());
                std::string alarmEvidenceUploadFinishStr = encoder.generateAlarmEvidenceUploadFinishStr(
                        alarmEvidenceUploadFinish);
                sendMsg(alarmEvidenceUploadFinishStr);
            }


        }


    }

    void G3NDSMqttManager::onGetNdsMqttMsgStartRealView(G3NDSMqttMsgStartRealView &startRealView) {
        /* �յ�һ�������ֱ������������ */
        int ret = callback->onGetRTMPPushOpt(startRealView.getAddress(), (startRealView.getChannel() - 1), 1);

        G3NDSMqttMsgStartRealViewAnswer startRealViewAnswer;
        startRealViewAnswer.setReSerialNum(startRealView.getSerialNum());
        startRealViewAnswer.setReMsgId(startRealView.getMsgId());
        if (ret == 0) {
            startRealViewAnswer.setResult(0);
        } else {
            startRealViewAnswer.setResult(1);
        }
        startRealViewAnswer.setAddress(startRealView.getAddress());

        std::string startRealViewAnswerStr = encoder.generateStartRealViewAnswerStr(startRealViewAnswer);
        sendMsg(startRealViewAnswerStr);

    }

    void G3NDSMqttManager::onGetNdsMqttMsgCheckDVRfile(NdsMqttMsgCheckDVRfile &checkDvRfile) {
        if (!dvrFileList.empty()) {
            dvrFileListLock.lock();
            uint64_t checkTime = checkDvRfile.getTimestamp();
            /* �����ļ��ҳ����������� */
            int decFileIndex = -1;
            for (int i = 0; i < static_cast<int>(dvrFileList.size()); i++) {
                if (dvrFileList[i].cameraId == (checkDvRfile.getCameraId())) {
                    uint64_t fileStartTime = XuTimeUtil::getInstance().bcdToTimestamp_Sec(dvrFileList[i].bcdTime,
                                                                                          G3_Configuration::getInstance().getLocalTimeZone());
                    uint64_t fileStopTime = fileStartTime + 60;

//                    printf("CheckDVRfile:   checkTime:%llu fileStartTime=%llu  fileStopTime=%llu     finaName=%s    bcd:%s \n",checkTime,fileStartTime,fileStopTime,dvrFileList[i].fileName.c_str(),XuString::getInstance().byteArrayToString(dvrFileList[i].bcdTime,
//                                                                                                                                                                                                                                              sizeof(dvrFileList[i].bcdTime)).c_str());

                    if ((checkTime >= fileStartTime && checkTime <= fileStopTime)) {
                        decFileIndex = i;
                        break;
                    }
                }
            }

            if (decFileIndex != -1) {
                std::string filePath = G3_Configuration::getInstance().getCurFileStorageRoot();
                /* �ȸ������ID��ȡDVR�ļ���Ŀ¼ */
                switch ((checkDvRfile.getCameraId())) {
                    case CAMERA_ID_1: {
                        filePath.append(DVR_FILE_PATH_OF_CAMERA1);
                    }
                        break;
                    case CAMERA_ID_2: {
                        filePath.append(DVR_FILE_PATH_OF_CAMERA2);
                    }
                        break;
                    case CAMERA_ID_3: {
                        filePath.append(DVR_FILE_PATH_OF_CAMERA3);
                    }
                        break;
                    case CAMERA_ID_4: {
                        filePath.append(DVR_FILE_PATH_OF_CAMERA4);
                    }
                        break;
                }
                /* �����ļ����������·�� */
                filePath.append(reinterpret_cast<const char *>(dvrFileList[decFileIndex].fileName),
                                dvrFileList[decFileIndex].fileNameLen);


                NdsMqttMsgCheckDVRFileAnswer checkDvrFileAnswer;
                long long fileLen = XuFile::getInstance().getFileLength(filePath.c_str());
                if (fileLen > 0) {
                    checkDvrFileAnswer.setResult(NdsMqttMsgCheckDVRFileAnswer::RESULT_SUCCESS);
                    checkDvrFileAnswer.setCameraId(checkDvRfile.getCameraId());
                    std::string filename = "";
                    filename.append(reinterpret_cast<const char *>(dvrFileList[decFileIndex].fileName),
                                    dvrFileList[decFileIndex].fileNameLen);
                    checkDvrFileAnswer.setFilename(filename);
                    checkDvrFileAnswer.setFileLeng(fileLen);
                    uint8_t md5value[16] = {0x00};
                    XuFile::getInstance().getMD5FromFile(filePath.c_str(), md5value);
                    checkDvrFileAnswer.setFileMd5(
                            XuString::getInstance().byteArrayToString(md5value, sizeof(md5value)));

                } else {
                    checkDvrFileAnswer.setResult(NdsMqttMsgCheckDVRFileAnswer::RESULT_FAILED);
                    checkDvrFileAnswer.setCameraId(checkDvRfile.getCameraId());
                    checkDvrFileAnswer.setFilename("");
                    checkDvrFileAnswer.setFileLeng(-1);
                    checkDvrFileAnswer.setFileMd5("");
                }
                std::string checkDvrFileAnswerStr = encoder.generateCheckDVRFileAnswerStr(checkDvrFileAnswer);
                sendMsg(checkDvrFileAnswerStr);
            } else {
                NdsMqttMsgCheckDVRFileAnswer checkDvrFileAnswer;
                checkDvrFileAnswer.setResult(NdsMqttMsgCheckDVRFileAnswer::RESULT_FAILED);
                checkDvrFileAnswer.setCameraId(checkDvRfile.getCameraId());
                checkDvrFileAnswer.setFilename("");
                checkDvrFileAnswer.setFileLeng(-1);
                checkDvrFileAnswer.setFileMd5("");
                std::string checkDvrFileAnswerStr = encoder.generateCheckDVRFileAnswerStr(checkDvrFileAnswer);
                sendMsg(checkDvrFileAnswerStr);
            }
            dvrFileListLock.unlock();
        } else {
            NdsMqttMsgCheckDVRFileAnswer checkDvrFileAnswer;
            checkDvrFileAnswer.setResult(NdsMqttMsgCheckDVRFileAnswer::RESULT_FAILED);
            checkDvrFileAnswer.setCameraId(checkDvRfile.getCameraId());
            checkDvrFileAnswer.setFilename("");
            checkDvrFileAnswer.setFileLeng(0);
            checkDvrFileAnswer.setFileMd5("");
            std::string checkDvrFileAnswerStr = encoder.generateCheckDVRFileAnswerStr(checkDvrFileAnswer);
            sendMsg(checkDvrFileAnswerStr);
        }
    }

    int G3NDSMqttManager::setNewDVRFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &dvrList) {
        if (!needToStop) {
            dvrFileListLock.lock();
            dvrFileList.clear();
            if (!dvrList.empty()) {
                for (int i = 0; i < static_cast<int>(dvrList.size()); i++) {
                    dvrFileList.push_back(dvrList[i]);
                }
            }
            dvrFileListLock.unlock();
        }
        return 0;
    }

    int G3NDSMqttManager::setNewWarFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &warList) {
        if (!needToStop) {
            warFileListLock.lock();
            warFileList.clear();
            if (!warList.empty()) {
                for (int i = 0; i < static_cast<int>(warList.size()); i++) {
                    warFileList.push_back(warList[i]);
                }
            }
            warFileListLock.unlock();
        }
        return 0;
    }

    void G3NDSMqttManager::onGetNdsMqttMsgGetDVRFile(NdsMqttMsgGetDVRFile &getDvrFile) {
        /* ��ȡһ��DVR */
        printf("****************G3NDSMqttManager::onGetNdsMqttMsgGetDVRFile url=%s  file=%s\n",
               getDvrFile.getUploadUrl().c_str(), getDvrFile.getFilename().c_str());

        std::string filePath = G3_Configuration::getInstance().getCurFileStorageRoot();
        /* �ȸ������ID��ȡDVR�ļ���Ŀ¼ */
        switch ((getDvrFile.getCameraId())) {
            case CAMERA_ID_1: {
                filePath.append(DVR_FILE_PATH_OF_CAMERA1);
            }
                break;
            case CAMERA_ID_2: {
                filePath.append(DVR_FILE_PATH_OF_CAMERA2);
            }
                break;
            case CAMERA_ID_3: {
                filePath.append(DVR_FILE_PATH_OF_CAMERA3);
            }
                break;
            case CAMERA_ID_4: {
                filePath.append(DVR_FILE_PATH_OF_CAMERA4);
            }
                break;
        }
        /* �����ļ����������·�� */
        filePath.append(getDvrFile.getFilename());
        /* �����ļ��ڲ��ڣ����ھͷ���ʧ�ܣ��ھ�ֱ���ϴ� */
        if (!XuFile::getInstance().fileExists(filePath.c_str())) {
            NdsMqttMsgGetDVRFileAnswer getDvrFileAnswer;
            getDvrFileAnswer.setResult(NdsMqttMsgGetDVRFileAnswer::RESULT_NOT_EXIST);
            getDvrFileAnswer.setFileKey(getDvrFile.getFileKey());
            std::string getDVRFileAnswerStr = encoder.generateGetDVRFileAnswerStr(getDvrFileAnswer);
            sendMsg(getDVRFileAnswerStr);
        } else {
            /* �����Ƿ��п��е��ϴ��߳� */
            int useIndex = -1;
            for (int i = 0; i < static_cast<int>(sizeof(dvrFileUploadUtilList)); i++) {
                if (!dvrFileUploadUtilList[i].isWorking()) {
                    useIndex = i;
                    break;
                }
            }
            /* �п��е��ϴ��߳̾��ϴ���û�оͷ��ط�æ */
            if (useIndex != -1) {
                NdsMqttMsgDVRFileUploadResult dvrFileUploadResult;
                dvrFileUploadResult.setFileKey(getDvrFile.getFileKey());
                dvrFileUploadUtilList[useIndex].init(getDvrFile.getUploadUrl(), filePath.c_str(), *this);
                dvrFileUploadUtilList[useIndex].setDvrFileUploadResult(dvrFileUploadResult);
                dvrFileUploadUtilList[useIndex].start();
            } else {
                /* �ϴ����г���һ���ˣ�ֱ�ӻظ���æ */
                NdsMqttMsgGetDVRFileAnswer getDvrFileAnswer;
                getDvrFileAnswer.setResult(NdsMqttMsgGetDVRFileAnswer::RESULT_BUSY);
                getDvrFileAnswer.setFileKey(getDvrFile.getFileKey());
                std::string getDVRFileAnswerStr = encoder.generateGetDVRFileAnswerStr(getDvrFileAnswer);
                sendMsg(getDVRFileAnswerStr);
            }


        }
    }

    void
    G3NDSMqttManager::onGetAlarmEvidenceUploadResult(NdsMqttMsgAlarmEvidenceUploadFinish &alarmEvidenceUploadFinish) {
        std::string alarmEvidenceUploadFinishStr = encoder.generateAlarmEvidenceUploadFinishStr(
                alarmEvidenceUploadFinish);
        sendMsg(alarmEvidenceUploadFinishStr);
    }

    void G3NDSMqttManager::onGetDVRFileUploadResult(NdsMqttMsgDVRFileUploadResult &dvrFileUploadResult) {
        std::string dvrFileUploadResultStr = encoder.generateDVRFileUploadResultStr(dvrFileUploadResult);

    }

    int G3NDSMqttManager::sendMsg(std::string &msgStr) {
        int ret = -1;
        if (isBinded && !msgStr.empty()) {
            ret = callback->onNeedSendDataToNDSMqtt(msgStr);
        }
        return ret;
    }

    void G3NDSMqttManager::reSendMsg() {
        if (!reSendList.empty()) {
            reSendListLock.lock();
            std::vector<std::string> sendListTemp;
            sendListTemp.insert(sendListTemp.begin(), reSendList.begin(), reSendList.end());
            reSendListIndex = 0;
            reSendListLock.unlock();

            for (int i = 0; i < static_cast<int>(sendListTemp.size()); i++) {
                sendMsg(sendListTemp[i]);
                /* ÿ����һ����10ms�ٷ���һ�� */
                usleep(10 * 1000);
            }

        }
    }

    void G3NDSMqttManager::onGetG3NDSMqttMsgStartRealViewControl(G3NDSMqttMsgStartRealViewControl &realViewControl) {
        /* �յ�һ������ֱ������ָ�� */
        int ret = -1;

        /* Ŀǰֻ֧�ֹر� */
        if (realViewControl.getCommand() == 0) {
            ret = callback->onGetRTMPPushOpt("", (realViewControl.getChannel() - 1), 0);
        }


        NdsMqttMsgUniversalAnswer universalAnswer;
        universalAnswer.setReSerialNum(realViewControl.getSerialNum());
        universalAnswer.setReMsgId(realViewControl.getMsgId());
        if (ret == 0) {
            universalAnswer.setResult(0);
        } else {
            universalAnswer.setResult(1);
        }

        std::string startRealViewAnswerStr = encoder.generateUniversalAnswerStr(universalAnswer);
        sendMsg(startRealViewAnswerStr);
    }

    void G3NDSMqttManager::onGetG3NDSMqttMsgParamsQuery(G3NDSMqttMsgParamsQuery &paramsQuery) {
        G3NDSMqttMsgParamsQueryAnswer paramsQueryAnswer;
        paramsQueryAnswer.setReSerialNum(paramsQuery.getSerialNum());
        paramsQueryAnswer.setTotalParam(1);
        std::vector<NdsMqttMsgParamsItem> paramsList;
        NdsMqttMsgParamsItem paramsItem;
        paramsItem.paramId = 0x0094;
        Json::Value value;
        value["cpuType"] = 1;
        value["version"] = G3_Configuration::getInstance().getConfigVersionCode();
        value["content"] = G3_Configuration::getInstance().getConfigurationStr().c_str();
        paramsItem.paramValue = value.toStyledString();
        paramsList.push_back(paramsItem);
        paramsQueryAnswer.setParamsList(paramsList);


        std::string paramsQueryAnswerStr = encoder.generateParamsQueryAnswerStr(paramsQueryAnswer);
        sendMsg(paramsQueryAnswerStr);
    }

    void G3NDSMqttManager::onGetG3NDSMqttMsgParamsSet(G3NDSMqttMsgParamsSet &paramsSet) {
        int ret = -1;
        if (paramsSet.getTotalParam() > 0) {
            std::vector<NdsMqttMsgParamsItem> paramList = paramsSet.getParamsList();
            for (int i = 0; i < static_cast<int>(paramList.size()); i++) {
                if (paramList[i].paramId == 0x0094) {
                    Json::Reader reader;
                    Json::Value value;
                    reader.parse(paramList[i].paramValue, value);
                    if (value["cpuType"].asUInt() == 1) {
                        std::string paramsContent = value["content"].asString();
                        ret = G3_Configuration::getInstance().loadFromStr(paramsContent.c_str(), paramsContent.size());
                        break;
                    }
                }
            }
        }


        NdsMqttMsgUniversalAnswer universalAnswer;
        universalAnswer.setReSerialNum(paramsSet.getSerialNum());
        universalAnswer.setReMsgId(paramsSet.getMsgId());
        if (ret == 0) {
            universalAnswer.setResult(0);
        } else {
            universalAnswer.setResult(1);
        }

        std::string universalAnswerStr = encoder.generateUniversalAnswerStr(universalAnswer);
        sendMsg(universalAnswerStr);
    }

    void G3NDSMqttManager::updateTimeByNDS() {

        NTPClient client{G3_Configuration::getInstance().getNdsMqttHost(), 123};

        auto epoch_server_ms = client.request_time();

        if (0 == epoch_server_ms) {
            return;
        }

        // The function ctime receives the timestamps in seconds.
        time_t epoch_server = (uint32_t) (epoch_server_ms / 1000);

        std::cout << "Server time: " << ctime(&epoch_server);
        std::cout << "Timestamp server: " << (uint32_t) epoch_server << "\n\n";

        struct timeval tv;
        struct timezone tz;
        /* ��ȡһ�µ�ǰʱ�� */
        gettimeofday(&tv, &tz);
        /* �ȶ��£����ʱ��������2�룬��ô�Ͳ���ҪУ�� */
        if (abs((tv.tv_sec - epoch_server)) < 2) {
            return;
        }

        /* ���������ø�timeval */
        tv.tv_sec = epoch_server;
        tv.tv_usec = 0;
        /* ��������ϵͳʱ�� */
        if (settimeofday(&tv, &tz) < 0) {
            return;
        }
        /* ϵͳʱ�����óɹ���   ͬ����Ӳ��ʱ��  ��ΪӲ��ʱ����UTCʱ��  ����������ͬ�� */
        XuShell::getInstance().runShellWithTimeout("hwclock -w",1000);

    }

    void G3NDSMqttManager::addToSendMsgList(std::string &msgStr) {
        if (!msgStr.empty()) {
            reSendListLock.lock();
            reSendList[reSendListIndex].clear();
            reSendList[reSendListIndex].append(msgStr);
            reSendListIndex++;
            if (reSendListIndex >= MAX_RESEND_MSG_LIST_SIZE) {
                reSendListIndex = 0;
            }
            reSendListLock.unlock();
        }
    }

    void G3NDSMqttManager::onGetG3NDSMqttMsgFunctionLockSet(G3NDSMqttMsgFunctionLockSet &functionLockSet) {
        int ret = -1;
        /* ���������������ǲ����г��� */
        if (!functionLockSet.getFunctionLock().empty()) {
            /* �г��Ⱦ���Ϊ�ǶԵģ�ֱ��д�� */
            int writeLen =XuFile::getInstance().writeFile(FUNCTIONLOCKFILEPATH,(uint8_t *) functionLockSet.getFunctionLock().c_str(), functionLockSet.getFunctionLock().size());
            if(writeLen == static_cast<int>(functionLockSet.getFunctionLock().size())){
                ret = 0;
            }
        }
        G3NDSMqttMsgFunctionLockSetAnswer functionLockSetAnswer;
        functionLockSetAnswer.setReSerialNum(functionLockSet.getSerialNum());
        if (ret == 0) {
            functionLockSetAnswer.setResult(0);
        } else {
            functionLockSetAnswer.setResult(1);
        }
        std::string functionLockSetAnswerStr = encoder.generateFunctionLockSetAnswerStr(functionLockSetAnswer);
        sendMsg(functionLockSetAnswerStr);
        /* ���¹�����() */
        XuShell::getInstance().killallG3sofeward("NDS set function lock ");
    }

    void G3NDSMqttManager::onGetG3NDSMqttMsgUpgradeDevice(G3NDSMqttMsgUpgradeDevice &upgradeDevice) {
        printf("G3NDSMqttManager::onGetG3NDSMqttMsgUpgradeDevice! \n");
        int ret = -1;
        /* ��ת���ļ�����(ֻ֧��MRV220��������G3��MCU����) */
        int fileType = -1;
        switch (upgradeDevice.getSoftwareType()) {
            case 0:{
                fileType = 0;
            }
                break;
            case 2:{
                fileType = 1;
            }
                break;
        }
        if(fileType >= 0){
            int startRet = callback->onGetStartDeviceUpgradeFromNDS(fileType,upgradeDevice.getFileUrl());
            ret = (startRet == 0) ? 0 : 1;
        }else{
            ret = 3;
        }
        NdsMqttMsgUniversalAnswer universalAnswer;
        universalAnswer.setReSerialNum(upgradeDevice.getSerialNum());
        universalAnswer.setReMsgId(upgradeDevice.getMsgId());
        if (ret == 0) {
            universalAnswer.setResult(0);
        } else if(ret == 1){
            universalAnswer.setResult(1);
        }else{
            universalAnswer.setResult(3);
        }

        std::string universalAnswerStr = encoder.generateUniversalAnswerStr(universalAnswer);
        sendMsg(universalAnswerStr);


    }

    void G3NDSMqttManager::setNewJPGFile(const JPGFileInfo &jpgFileInfo) {
        if (!needToStop) {
            printf("====================================G3NDSMqttManager::setNewJPGFile path=%s\n",
                   jpgFileInfo.getFilePath().c_str());

            /* �յ��µı���֤���ˣ����ϱ�һ�� */
            G3NDSMqttMsgAlarmEvidence alarmEvidence;

            alarmEvidence.setChannel(jpgFileInfo.getCameraId() + 1);
            alarmEvidence.setStartTime(jpgFileInfo.getAlarmTime() / 1000);
            alarmEvidence.setEndTime(jpgFileInfo.getAlarmTime() / 1000);
            alarmEvidence.setDataType(1);
            alarmEvidence.setStreamType(0);
            alarmEvidence.setStorageType(0);
            alarmEvidence.setFileName(jpgFileInfo.getFilePath());
            alarmEvidence.setFileSize(XuFile::getInstance().getFileLength(jpgFileInfo.getFilePath().c_str()));
            switch (jpgFileInfo.getFileType()) {
                case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
                case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1:
                case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
                case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1:
                case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1:
                case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
                    /* �������� */
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL1);
                }
                    break;

                case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_FORWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_BACKWARD_VEHICLE_LEVEL1:
                case EVENT_BSD_BACKWARD_VEHICLE_LEVEL2: {
                    /* �������� */
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL1);
                }
                    break;

                case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
                case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2:
                case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2:
                case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
                case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2:
                case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
                    /* �������� */
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_PEDESTRIAN_LEVEL2);
                }
                    break;

                case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2:
                case EVENT_BSD_FORWARD_VEHICLE_LEVEL2: {
                    /* �������� */
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_BSD_VEHICLE_LEVEL2);
                }
                    break;

                case EVENT_SBST_FORWARD_PDW_LEVEL1:
                case EVENT_SBST_FORWARD_PDW_LEVEL2:
                case EVENT_ADAS_PEDESTRIAN_HMW: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_PEDESTRIAN_COLLISION);
                }
                    break;

                case EVENT_SBST_FORWARD_FCW_LEVEL1:
                case EVENT_SBST_FORWARD_FCW_LEVEL2:
                case EVENT_ADAS_VEHICLE_TTC: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_FORWARD_COLLISION);
                }
                    break;

                case EVENT_SBST_FORWARD_LDW_LEFT_DASHED:
                case EVENT_SBST_FORWARD_LDW_LEFT_SOLID:
                case EVENT_ADAS_LDW_LEFT_SOLID:
                case EVENT_ADAS_LDW_LEFT_DASH: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_LEFT_LANE_DEPARTURE);
                }
                    break;

                case EVENT_SBST_FORWARD_LDW_RIGHT_DASHED:
                case EVENT_ADAS_LDW_RIGHT_DASH:
                case EVENT_ADAS_LDW_RIGHT_SOLID:
                case EVENT_SBST_FORWARD_LDW_RIGHT_SOLID: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_RIGHT_LANE_DEPARTURE);
                }
                    break;

                case EVENT_ADAS_VEHICLE_HMW: {
                    alarmEvidence.setAlarmType(G3NDSMqttMsgAlarmUp::EVENTTYPE_HMW);
                }
                    break;
            }

            std::string alarmRefKey;
            /* ��ʱ��� */
            alarmRefKey.append(std::to_string(jpgFileInfo.getAlarmTime()) + "_");
            /* ����ͷID */
            alarmRefKey.append(std::to_string(jpgFileInfo.getCameraId()) + "_");
            /* �¼����� */
            alarmRefKey.append(std::to_string(jpgFileInfo.getFileType()));

            alarmEvidence.setAlarmRefKey(alarmRefKey);

            std::string alarmEvidenceStr = encoder.generateAlarmEvidenceStr(alarmEvidence);
            printf("NDSMqttManager::setNewJPGFile alarmEvidenceStr=%s\n", alarmEvidenceStr.c_str(),G3_Configuration::getInstance().getNdsMqttConnectParams().pubTopic.c_str());

            int sendRet = sendMsg(alarmEvidenceStr);
            /* ������Ƶ��Ҫ���ط� */
            if (sendRet != 0) {
                addToSendMsgList(alarmEvidenceStr);
            }
        }
    }
} // vis