//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/28.
//

#include "G3NDSMqttMsgEncoder.h"

namespace vis {
    uint32_t G3NDSMqttMsgEncoder::getCurSerialNum() {
        uint32_t temp = curSerialNum;
        curSerialNum = curSerialNum + 1;
        return temp;
    }

    std::string G3NDSMqttMsgEncoder::generateAlarmUpStr(G3NDSMqttMsgAlarmUp &alarmUp) {
        alarmUp.setSerialNum(getCurSerialNum());
        alarmUp.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return alarmUp.toCode();
    }

    std::string G3NDSMqttMsgEncoder::generateEventUploadStr(G3NDSMqttMsgEventUpload &eventUpload) {
        eventUpload.setSerialNum(getCurSerialNum());
        eventUpload.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return eventUpload.toCode();
    }

    std::string G3NDSMqttMsgEncoder::generateAlarmEvidenceStr(G3NDSMqttMsgAlarmEvidence &alarmEvidence) {
        alarmEvidence.setSerialNum(getCurSerialNum());
        alarmEvidence.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return alarmEvidence.toCode();
    }

    std::string G3NDSMqttMsgEncoder::generateAlarmEvidenceUploadFinishStr(
            NdsMqttMsgAlarmEvidenceUploadFinish &alarmEvidenceUploadFinish) {
        alarmEvidenceUploadFinish.setSerialNum(getCurSerialNum());
        alarmEvidenceUploadFinish.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return alarmEvidenceUploadFinish.toCode();
    }

    std::string G3NDSMqttMsgEncoder::generateStartRealViewAnswerStr(G3NDSMqttMsgStartRealViewAnswer &startRealViewAnswer) {
        startRealViewAnswer.setSerialNum(getCurSerialNum());
        startRealViewAnswer.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return startRealViewAnswer.toCode();
    }

    std::string G3NDSMqttMsgEncoder::generateCheckDVRFileAnswerStr(NdsMqttMsgCheckDVRFileAnswer &checkDvrFileAnswer) {
        checkDvrFileAnswer.setSerialNum(getCurSerialNum());
        checkDvrFileAnswer.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return checkDvrFileAnswer.toCode();
    }

    std::string G3NDSMqttMsgEncoder::generateGetDVRFileAnswerStr(NdsMqttMsgGetDVRFileAnswer &getDvrFileAnswer) {
        getDvrFileAnswer.setSerialNum(getCurSerialNum());
        getDvrFileAnswer.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return getDvrFileAnswer.toCode();
    }

    std::string
    G3NDSMqttMsgEncoder::generateDVRFileUploadResultStr(NdsMqttMsgDVRFileUploadResult &dvrFileUploadResult) {
        dvrFileUploadResult.setSerialNum(getCurSerialNum());
        dvrFileUploadResult.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return dvrFileUploadResult.toCode();
    }

    std::string G3NDSMqttMsgEncoder::generateUniversalAnswerStr(NdsMqttMsgUniversalAnswer &universalAnswer) {
        universalAnswer.setSerialNum(getCurSerialNum());
        universalAnswer.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return universalAnswer.toCode();
    }

    std::string G3NDSMqttMsgEncoder::generateParamsQueryAnswerStr(G3NDSMqttMsgParamsQueryAnswer &paramsQueryAnswer) {
        paramsQueryAnswer.setSerialNum(getCurSerialNum());
        paramsQueryAnswer.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return paramsQueryAnswer.toCode();
    }

    std::string
    G3NDSMqttMsgEncoder::generateFunctionLockSetAnswerStr(G3NDSMqttMsgFunctionLockSetAnswer &functionLockSetAnswer) {
        functionLockSetAnswer.setSerialNum(getCurSerialNum());
        functionLockSetAnswer.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return functionLockSetAnswer.toCode();
    }

    std::string G3NDSMqttMsgEncoder::generateVersionUpStr(G3NDSMqttMsgVesionUp &vesionUp) {
        vesionUp.setSerialNum(getCurSerialNum());
        vesionUp.setClientId(G3_Configuration::getInstance().getNdsMqttConnectParams().clientId);
        return vesionUp.toCode();
    }


} // vis