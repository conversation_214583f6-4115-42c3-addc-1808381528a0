//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/1.
//

#include "G3NDSMqttMsgFunctionLockSet.h"

namespace vis {
    G3NDSMqttMsgFunctionLockSet::G3NDSMqttMsgFunctionLockSet() {
        setMsgId(NDSMQTTMSGID_SET_FUNCTIONLOCK);
    }

    int G3NDSMqttMsgFunctionLockSet::toDecode(const std::string &str) {
        int ret = -1;
        if(!str.empty()){
            Json::Reader reader;
            Json::Value value;
            reader.parse(str, value);
            if(value.isMember("FunctionLock")){
                FunctionLock = value["FunctionLock"].asString();
            }
            ret = 0;
        }
        return ret;
    }

    const std::string &G3NDSMqttMsgFunctionLockSet::getFunctionLock() const {
        return FunctionLock;
    }

    void G3NDSMqttMsgFunctionLockSet::setFunctionLock(const std::string &functionLock) {
        FunctionLock = functionLock;
    }
} // vis