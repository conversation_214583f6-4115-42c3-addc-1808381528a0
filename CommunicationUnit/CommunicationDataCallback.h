//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/14.
//

#ifndef VIS_G3_SOFTWARE_COMMUNICATIONDATACALLBACK_H
#define VIS_G3_SOFTWARE_COMMUNICATIONDATACALLBACK_H

#include "PCConfigure/protocol/Heartbeat.h"
#include "PCConfigure/protocol/UniversalAnswer.h"
#include "PCConfigure/protocol/QequestG3Config.h"
#include "PCConfigure/protocol/SetG3Config.h"
#include "PCConfigure/protocol/StartUDPRealview.h"
#include "PCConfigure/protocol/RealTimeVehicleStatus.h"
#include "PCConfigure/protocol/TCPRealviewOpt.h"
#include "PCConfigure/protocol/StartG3DeviceUpgrade.h"
#include "RS485_G3/G3RS485GPSInfo.h"
#include "RS485_G3/G3RS485GetUpgradeFileData.h"

#include "G3AndMCUUartUniversalAnswer.h"
#include "G3AndMCUUartVehicleRealTimeStatus.h"
#include "G3AndMCUUartRespondPeripheralVersion.h"
#include "G3AndMCUUartGetPeripheralUpgradeData.h"
#include "G3AndMCUUartPeripheralUpgradeResult.h"
#include "SetG3SystemTime.h"
#include "GetG3AndG4DevicesVersion.h"
#include "myparams.h"
#include "gtShareDefine.h"
#include "GetCPUSerialNum.h"
#include "SetG3UUID.h"
#include "GetG3UUID.h"
#include "GetG3UTCTime.h"
#include "GetG3Mp4FileList.h"
#include "G3Mp4FileOpt.h"
#include "StartG3FileDownload.h"
#include "StartG3FileDownloadResult.h"
#include "StartG3DebugMode.h"
#include "SetFunctionLock.h"
#include "GetFunctionLockInfo.h"
#include "RestartG3.h"
#include "StartUDPRealview_Separate.h"
#include "G3AndMCUUartPTWrokResult.h"
#include "G3RS485UpgradeResult.h"
#include "G3RS485SetDispalyParamsResult.h"
#include "SetPeripheralDisplayParams.h"

#include "MODBUS_StopButtonStatus.h"
#include "GetMRV220LogFileList.h"
#include "StartMRV220LogFileDownload.h"
#include "StartMRV220LogFileDownloadResult.h"
#include "GetMultimediaFileEncryptionKey.h"
#include "SetMultimediaFileEncryptionKey.h"
#include "GetAlarmSoundFileConf.h"
#include "GetCameraInputTypeFilterConfig.h"
#include "SetCameraInputTypeFilterConfig.h"
#include "G3RS485StartUpgradeL4Result.h"
#include "G3RS485GetL4UpgradeFileData.h"
#include "G3RS485UpgradeL4Result.h"
#include "MODBUS_QContrPedalStatus.h"
#include "GetCameraReduceEffectInfo.h"
#include "SetCameraReduceEffectInfo.h"
#include "SetProductionTestingSwitch.h"
#include "G3NDSMqttMsgUpgradeDevice.h"

namespace vis {

    class CommunicationDataCallback {


    public:

        /**
         * 获取到从Sokcet来的心跳包
         *
         * @param heartbeat ：心跳包信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetHeartBeat(Heartbeat &heartbeat, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的通用应答包
         *
         * @param universalAnswer ： 通用应答包的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetUniversalAnswer(UniversalAnswer &universalAnswer, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的查询G3配置表
         *
         * @param qequestG3Config ： 查询配置表的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetQequestG3Config(QequestG3Config &qequestG3Config, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的设置G3配置表
         *
         * @param setG3Config ： 设置配置表的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetSetG3Config(SetG3Config &setG3Config, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的开启UDP实景传输（UDP广播）
         *
         * @param startUDPRealview ： 开启UDP实景传输的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetStartUDPRealview(StartUDPRealview &startUDPRealview, const char *ip, const int port);


        /**
         * 获取到从Sokcet来的开始升级的指令
         *
         * @param startG3DeviceUpgrade ： 开启G3设备更新的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void
        onGetStartG3DeviceUpgrade(StartG3DeviceUpgrade &startG3DeviceUpgrade, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的设置G3系统时间的的指令
         *
         * @param setG3SystemTime ： 设置系统事件的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         * @return 0：成功  其他：失败
         */
        virtual int onGetSetG3SystemTime(SetG3SystemTime &setG3SystemTime, const char *ip, const int port);


        /**
         * 获取到从Sokcet来的获取G3/G4设备版本得指令
         *
         * @param getG3AndG4DevicesVersion ： 获取设备版本号的指令的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void
        onGetGetG3AndG4DeviceVersion(GetG3AndG4DevicesVersion &getG3AndG4DevicesVersion, const char *ip,
                                     const int port);


        /**
         * 获取到从Sokcet来的获取G3/G4的CPU序列号指令
         *
         * @param getCpuSerialNum
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetGetG3AndG4CPUSerialNumber(GetCPUSerialNum &getCpuSerialNum, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的设置UUID的指令
         *
         * @param setG3Uuid ： 设置G3UUID请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetSetG3UUID(SetG3UUID &setG3Uuid, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的获取UUID的指令
         *
         * @param getG3Uuid ： 获取UUID的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetGetG3UUID(GetG3UUID &getG3Uuid, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的获取G3的UTC时间的指令
         *
         * @param getG3UtcTime ： 获取G3的UTC时间的指令
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetGetG3UTCTime(GetG3UTCTime &getG3UtcTime, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的获取G3的视频文件的指令
         *
         * @param getG3Mp4FileList ： 获取G3视频文件的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetGetG3Mp4FileList(GetG3Mp4FileList &getG3Mp4FileList, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的操作G3的视频文件的指令
         *
         * @param g3Mp4FileOpt ： 操作G3文件的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         * @return 视频文件信息列表
         */
        virtual std::vector<PCConfigureDataPacket::MP4FileInfo>
        onGetG3Mp4FileOpt(G3Mp4FileOpt &g3Mp4FileOpt, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的下载G3的文件的指令（如果成功，会打开对应的线程和端口给客户端连接下载）
         *
         * @param startG3FileDownload : 下载G3文件的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         * @return 请求下载的结果
         */
        virtual StartG3FileDownloadResult
        onGetStartG3FileDownload(StartG3FileDownload &startG3FileDownload, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的打开G3调试者模式的指令
         *
         * @param startG3DebugMode : 打开调试者模式的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetStartG3DebugMode(StartG3DebugMode &startG3DebugMode, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的设置功能锁的指令
         *
         * @param setFunctionLock : 设置功能锁的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetSetFunctionLock(SetFunctionLock &setFunctionLock, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的获取功能锁的指令
         *
         * @param getFunctionLockInfo : 获取功能锁的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetGetFunctionLockInfo(GetFunctionLockInfo &getFunctionLockInfo, const char *ip, const int port);


        /**
         * 获取到从Sokcet来得重启MRV220的指令
         *
         * @param restartG3 ： 重启G3的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetRestartMRV220(RestartG3 &restartG3, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的打开实景传输（UDP单播）的命令
         *
         * @param startUdpRealview ： 启动UPD实景（单播）的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         * @return 0：成功   其他：失败
         */
        virtual int
        onGetStartUDPRealviewSeparate(StartUDPRealview_Separate &startUdpRealview, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的一包配置外设显示参数
         *
         * @param displayParams : 外设显示参数
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         *
         * @return 0:成功  1：失败
         */
        virtual int onGetSetPeripheralDisplayParams(SetPeripheralDisplayParams displayParams, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的一包获取报警声音配置参数的命令
         *
         * @param displayParams : 获取报警声音配置文件的命令
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         *
         * @return 报警声音配置参数的长度
         */
        virtual int onGetGetAlarmSoundFileConf(GetAlarmSoundFileConf getAlarmSoundFileConf, const char *ip, const int port);


        /**
         * 获取到从CAN的J1939协议来的车辆速度
         *
         * @param speed ： 车辆速度
         */
        virtual void onGetSpeedFromCANJ1939(const float speed);

        /**
         * 获取到从CAN的J1939协议来的发动机转速
         *
         * @param engineSpeed ： 发动机转速
         */
        virtual void onGetEngineSpeedFromCANJ1939(const float engineSpeed);

        /**
         * 需要发送数据到RS485总线了
         *
         * @param buf ：需要发送出去的数据的指针
         * @param len ：需要发送出去的数据的长度
         */
        virtual void onNeedSendDataToRS485(const uint8_t *buf, const int len);

        /**
         * 获取到一个RS485外设的版本号
         *
         * @param peripheral ： 外设ID
         * @param softwarVersion ： 软件版本号 （定长4字节）
         * @param protocoVersion ： 硬件版本号 （定长4字节）
         */
        virtual void
        onGetPeripheralVersion(const uint8_t peripheral, const char *softwarVersion, const char *protocoVersion);

        /**
         * 从RS485总线上拿到GPS定位信息
         *
         * @param gpsInfo ： GPS信息
         */
        virtual void onGetGPSInfoFromRS485(G3RS485GPSInfo &gpsInfo);

        /**
         * 从RS485总线上请求升级数据的信息
         *
         * @param getUpgradeFileData ： 外设获取升级数据的信息
         */
        virtual void onGetGetUpgradeFileDataFromRS485(G3RS485GetUpgradeFileData &getUpgradeFileData);

        /**
        * 从RS485总线上升级结果的信息
        *
        * @param upgradeResult ： 外设升级结果的信息
        */
        virtual void onGetUpgradeResultFromRS485(G3RS485UpgradeResult &upgradeResult);

        /**
        * 从RS485总线上配置外设显示参数结果信息
        *
        * @param upgradeResult ： 外设升级结果的信息
        */
        virtual void onGetSetDispalyParamsResultFromRS485(G3RS485SetDispalyParamsResult &setDispalyParamsResult);

        /**
        * 从RS485总线上来的开始L4升级信息的结果
        *
        * @param startUpgradeL4Result ： 开始L4升级信息的结果
        */
        virtual void onGetStartUpgradeL4ResultFromRS485(G3RS485StartUpgradeL4Result &startUpgradeL4Result);

        /**
         * 从RS485总线上请求L4升级数据的信息
         *
         * @param getlL4UpgradeFileData ： 外设获取升级数据的信息
         */
        virtual void onGetGetL4UpgradeFileDataFromRS485(G3RS485GetL4UpgradeFileData &getlL4UpgradeFileData);

        /**
         * 从RS485总线上收到L4升级结果
         *
         * @param upgradeL4Result ： L4升级结果
         */
        virtual void onGetGetL4UpgradeResultFromRS485(G3RS485UpgradeL4Result &upgradeL4Result);

        /**
         * 需要发送数据到指定的socket客户端
         *
         * @param ip ： 目标IP
         * @param port ： 目标端口
         * @param buf ： 需要发送的数据的指针
         * @param len ： 需要发送的数据的长度
         */
        virtual void onNeedSendDataToSocket(const char *ip, const int port, const uint8_t *buf, int len);

        /**
         * 获取到从Socket来的车况信息
         *
         * @param vehicleStatus ： 车况信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetRealtimeVehicleStatus(RealTimeVehicleStatus &vehicleStatus, const char *ip, const int port);

        /**
         * 获取到从跟MCU的UART来的通用应答包
         *
         * @param universalAnswer ： 通用应答包的信息
         */
        virtual void onGetUniversalAnswerFromMCUUART(G3AndMCUUartUniversalAnswer &universalAnswer);

        /**
         * 获取到从跟MCU的UART来的车况信息
         *
         * @param vehicleStatus ： 车况信息
         */
        virtual void onGetRealtimeVehicleStatusFromMCUUART(G3AndMCUUartVehicleRealTimeStatus &vehicleStatus);

        /**
         * 获取到一个跟MCU的UART来的外设的版本号
         *
         * @param peripheralVersion ： 外设版本号信息
         */
        virtual void onGetPeripheralVersionFromMCUUART(G3AndMCUUartRespondPeripheralVersion &peripheralVersion);

        /**
         * 获取到一个跟MCU的UART来的获取外设升级数据的请求
         *
         * @param getPeripheralUpgradeData ： 外设升级请求的信息
         */
        virtual void
        onGetGetPeripheralUpgradeDataFromMCUUART(G3AndMCUUartGetPeripheralUpgradeData &getPeripheralUpgradeData);

        /**
         * 获取到一个跟MCU的UART来的获取外设升级结果
         *
         * @param peripheralUpgradeResult ： 外设升级结果
         */
        virtual void
        onGetPeripheralUpgradeResultFromMCUUART(G3AndMCUUartPeripheralUpgradeResult &peripheralUpgradeResult);

        /**
         * 需要喇叭播放一个声音
         *
         * @param soundFilePath ： 声音文件的路径
         * @param isForce ： 是否立马播放（立马播放会中断当前正在播放的语音）
         *
         * */
        virtual void onNeedPlaySound(const char *soundFilePath, bool isForce);

        /**
         * 需要喇叭播放一个声音(播放机科发展得语音文件)
         *
         * @param soundFilePath ： 声音文件的路径
         * @param isForce ： 是否立马播放（立马播放会中断当前正在播放的语音）
         *
         * */
        virtual void onNeedPlaySound_JKFZ(const char *soundFilePath, bool isForce);

        /**
         * 需要发送数据到跟MCU通信的UART了
         *
         * @param buf ： 需要发送的数据的指针
         * @param len ： 需要发送的数据的长度
         */
        virtual void onNeedSendDataToMCUUART(const uint8_t *buf, const int len);

        /**
         * 获取到一包MCU生产测试的结果
         *
         * @param ptWrokResult ： 生产测试的结果
         *
         * */
        virtual void onGetProductionTestResultFromMCUUART(G3AndMCUUartPTWrokResult &ptWrokResult);

        /**
         * 需要发送数据到跟NDS通信的Mqtt了
         *
         * @param strData ： 需要发送的数据
         *
         * @return 发送结果 0：成功  其他：失败
         */
        virtual int onNeedSendDataToNDSMqtt(std::string &strData);



        /**
         * 收到一包RTMP的推流操作请求
         *
         * @param url : 推流地址
         * @param cameraId ： 相机ID
         * @param opt ： 操作   0：关闭  1：打开
         *
         * @return 0:成功  1：失败
         */
        virtual int onGetRTMPPushOpt(const std::string url,const int cameraId, const int opt);


        /**
         * 从MODBUS上面获取到停止报警按钮的状态
         *
         * @param stopButtonStatus ： 停止报警按钮的状态的信息封装
         */
        virtual void onGetStopButtonStatusFromMODBUS(MODBUS_StopButtonStatus &stopButtonStatus);

        /**
         * 从MODBUS上面获取到Q-Control-01项目的踏板状态
         *
         * @param pedalStatus ： Q-Control-01项目的踏板状态的信息封装
         */
        virtual void onGetQContr01PedalStatusFromMODBUS(MODBUS_QContrPedalStatus &pedalStatus);

        /**
         * 需要发送数据到RS232串口了
         *
         * @param buf ：需要发送出去的数据的指针
         * @param len ：需要发送出去的数据的长度
         */
        virtual void onNeedSendDataToRS232(const uint8_t *buf, const int len);

        /**
         * 从MODBUS上面收到一包读离散量的错误包
         *
         * @param errorCode ： 错误码   0x01-不合法功能码   0x02-不合法数据地址   0x03-不合法数据   0x04-从机故障
         */
        virtual void onGetReadDiscreteInputErrorFromMODBUS(const uint8_t errorCode);

        /**
         * 获取到从Sokcet来的获取MRV220的log文件列表的指令
         *
         * @param getMrv220LogFileList ： 获取MRV220的log文件列表的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetGetMRV220LogFileList(GetMRV220LogFileList &getMrv220LogFileList, const char *ip, const int port);



        /**
         * 获取到从Sokcet来的请求传输MRV220的log文件的指令（如果成功，会打开对应的线程和端口给客户端连接下载）
         *
         * @param startMrv220LogFileDownload : 请求传输的MRV220Log文件的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         * @return 请求下载的结果
         */
        virtual StartMRV220LogFileDownloadResult
        onGetStartMRV220LogFileDownload(StartMRV220LogFileDownload &startMrv220LogFileDownload, const char *ip, const int port);


        /**
         * 获取到从Sokcet来的获取多媒体文件加密使用的密钥的指令
         *
         * @param getMultimediaFileEncryptionKey ： 获取获取多媒体文件加密使用的密钥的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         */
        virtual void onGetGetMultimediaFileEncryptionKey(GetMultimediaFileEncryptionKey &getMultimediaFileEncryptionKey, const char *ip, const int port);


        /**
         * 获取到从Sokcet来的设置多媒体文件加密使用的密钥的指令
         *
         * @param setMultimediaFileEncryptionKey ： 设置多媒体文件加密使用的密钥的请求的信息
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         *
         * @return 0:成功  其他：失败
         *
         */
        virtual int onGetSetMultimediaFileEncryptionKey(SetMultimediaFileEncryptionKey &setMultimediaFileEncryptionKey, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的获取镜头输入类型过滤信息的指令
         *
         * @param getCameraInputTypeFilterConfig ： 设取镜头输入类型过滤信息的请求的封装
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         *
         */
        virtual void onGetGetCameraInputTypeFilterConfig(GetCameraInputTypeFilterConfig &getCameraInputTypeFilterConfig, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的设置镜头输入类型过滤信息的指令
         *
         * @param setCameraInputTypeFilterConfig ： 设置镜头输入类型过滤信息的请求的封装
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         *
         * @return 0:成功  其他：失败
         *
         */
        virtual int onGetSetCameraInputTypeFilterConfig(SetCameraInputTypeFilterConfig &setCameraInputTypeFilterConfig, const char *ip, const int port);

        /**
        * 收到需要改变RS485波特率的命令
        *
        * @param baudrate ： 需要使用的目标波特率
        */
        virtual void onGetChangeRS485Baudrate(const int baudrate);

        /**
         * 获取到从Sokcet来的获取镜头降效信息的指令
         *
         * @param getCameraReduceEffectInfo ： 获取镜头降效信息的请求的封装
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         *
         */
        virtual void onGetGetCameraReduceEffectInfoFromSocket(GetCameraReduceEffectInfo &getCameraReduceEffectInfo, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的设置镜头降效信息的指令
         *
         * @param setCameraInputTypeFilterConfig ： 设置镜头降效信息的指令的请求的封装
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         *
         * @return 0:成功  其他：失败
         *
         */
        virtual int onGetSetCameraReduceEffectInfoFromSocket(SetCameraReduceEffectInfo &setCameraReduceEffectInfo, const char *ip, const int port);

        /**
         * 获取到从Sokcet来的设置产测模式开关的指令
         *
         * @param setProductionTestingSwitch ： 设置产测模式开关的指令的请求的封装
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         *
         * @return 0:成功  其他：失败
         *
         */
        virtual int onGetSetProductionTestingSwitchFromSocket(SetProductionTestingSwitch &setProductionTestingSwitch, const char *ip, const int port);

        /**
         * 获取到从NDS来的升级终端的指令
         *
         * @param fileType ： 文件类型
         * @param fileUrl ： 下载链接
         *
         * @return 是否开启升级成功    -1：失败  0：成功  1：繁忙
         */
        virtual int onGetStartDeviceUpgradeFromNDS(const int fileType, const std::string &fileUrl);

        /**
         * 获取到从Sokcet来的开/关TCP实景数据流的指令
         *
         * @param tcpRealviewOpt ： 开/关TCP实景数据流的指令的封装
         * @param ip ： 发送端的IP
         * @param port ： 发送端的端口号
         *
         * @return 0:成功  其他：失败
         *
         */
        virtual int onGetTCPRealviewOptFromSocket(TCPRealviewOpt &tcpRealviewOpt, const char *ip, const int port);


    private:

    };

}

#endif //VIS_G3_SOFTWARE_COMMUNICATIONDATACALLBACK_H
