//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/14.
//

#include <cstdio>
#include "CommunicationDataCallback.h"

namespace vis {

    void CommunicationDataCallback::onGetHeartBeat(Heartbeat &heartbeat, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetHeartBeat \n");

    }

    void
    CommunicationDataCallback::onGetUniversalAnswer(UniversalAnswer &universalAnswer, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetUniversalAnswer \n");
    }

    void
    CommunicationDataCallback::onGetQequestG3Config(QequestG3Config &qequestG3Config, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetQequestG3Config \n");
    }

    void CommunicationDataCallback::onGetSetG3Config(SetG3Config &setG3Config, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetSetG3Config \n");
    }

    void
    CommunicationDataCallback::onGetStartUDPRealview(StartUDPRealview &startUDPRealview, const char *ip,
                                                     const int port) {
        printf("CommunicationDataCallback::onGetStartUDPRealview \n");
    }

    void CommunicationDataCallback::onGetSpeedFromCANJ1939(const float speed) {
        printf("CommunicationDataCallback::onGetSpeedFromCANJ1939 \n");
    }

    void CommunicationDataCallback::onGetEngineSpeedFromCANJ1939(const float engineSpeed) {
        printf("CommunicationDataCallback::onGetEngineSpeedFromCANJ1939 \n");
    }

    void CommunicationDataCallback::onNeedSendDataToRS485(const uint8_t *buf, const int len) {
        printf("CommunicationDataCallback::onNeedSendDataToRS485 \n");
    }

    void CommunicationDataCallback::onGetPeripheralVersion(const uint8_t peripheral, const char *softwarVersion,
                                                           const char *protocoVersion) {
        printf("CommunicationDataCallback::onGetPeripheralVersion \n");
    }

    void CommunicationDataCallback::onGetGPSInfoFromRS485(G3RS485GPSInfo &gpsInfo) {
        printf("CommunicationDataCallback::onGetGPSInfoFromRS485 \n");
    }

    void
    CommunicationDataCallback::onNeedSendDataToSocket(const char *ip, const int port, const uint8_t *buf, int len) {
        printf("CommunicationDataCallback::onNeedSendDataToSocket \n");
    }

    void CommunicationDataCallback::onGetRealtimeVehicleStatus(RealTimeVehicleStatus &vehicleStatus, const char *ip,
                                                               const int port) {
        printf("CommunicationDataCallback::onGetRealtimeVehicleStatus \n");
    }

    void CommunicationDataCallback::onGetUniversalAnswerFromMCUUART(G3AndMCUUartUniversalAnswer &universalAnswer) {
        printf("CommunicationDataCallback::onGetUniversalAnswerFromMCUUART \n");
    }

    void
    CommunicationDataCallback::onGetRealtimeVehicleStatusFromMCUUART(G3AndMCUUartVehicleRealTimeStatus &vehicleStatus) {
        printf("CommunicationDataCallback::onGetRealtimeVehicleStatusFromMCUUART \n");
    }

    void
    CommunicationDataCallback::onGetPeripheralVersionFromMCUUART(
            G3AndMCUUartRespondPeripheralVersion &peripheralVersion) {
        printf("CommunicationDataCallback::onGetPeripheralVersionFromMCUUART \n");
    }

    void CommunicationDataCallback::onGetGetPeripheralUpgradeDataFromMCUUART(
            G3AndMCUUartGetPeripheralUpgradeData &getPeripheralUpgradeData) {
        printf("CommunicationDataCallback::onGetGetPeripheralUpgradeDataFromMCUUART \n");

    }

    void CommunicationDataCallback::onGetPeripheralUpgradeResultFromMCUUART(
            G3AndMCUUartPeripheralUpgradeResult &peripheralUpgradeResult) {
        printf("CommunicationDataCallback::onGetPeripheralUpgradeResultFromMCUUART \n");
    }

    void CommunicationDataCallback::onNeedSendDataToMCUUART(const uint8_t *buf, const int len) {
        printf("CommunicationDataCallback::onNeedSendDataToMCUUART \n");
    }

    void CommunicationDataCallback::onNeedPlaySound(const char *soundFilePath, bool isForce) {
        printf("CommunicationDataCallback::onNeedPlaySound \n");
    }

    void CommunicationDataCallback::onNeedPlaySound_JKFZ(const char *soundFilePath, bool isForce) {
        printf("CommunicationDataCallback::onNeedPlaySound_JKFZ \n");
    }

    void
    CommunicationDataCallback::onGetStartG3DeviceUpgrade(StartG3DeviceUpgrade &startG3DeviceUpgrade, const char *ip,
                                                         const int port) {
        printf("CommunicationDataCallback::onGetStartG3DeviceUpgrade \n");
    }

    int
    CommunicationDataCallback::onGetSetG3SystemTime(SetG3SystemTime &setG3SystemTime, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetSetG3SystemTime \n");
        return -1;
    }

    void CommunicationDataCallback::onGetGetG3AndG4DeviceVersion(GetG3AndG4DevicesVersion &getG3AndG4DevicesVersion,
                                                                 const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetGetG3AndG4DeviceVersion \n");
    }

    void CommunicationDataCallback::onGetGetG3AndG4CPUSerialNumber(GetCPUSerialNum &getCpuSerialNum, const char *ip,
                                                                   const int port) {
        printf("CommunicationDataCallback::onGetGetG3AndG4CPUSerialNumber \n");
    }

    void CommunicationDataCallback::onGetSetG3UUID(SetG3UUID &setG3Uuid, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetSetG3UUID \n");
    }

    void CommunicationDataCallback::onGetGetG3UUID(GetG3UUID &getG3Uuid, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetSetG3UUID \n");
    }

    void CommunicationDataCallback::onGetGetG3UTCTime(GetG3UTCTime &getG3UtcTime, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetGetG3UTCTime \n");
    }

    void
    CommunicationDataCallback::onGetGetG3Mp4FileList(GetG3Mp4FileList &getG3Mp4FileList, const char *ip,
                                                     const int port) {
        printf("CommunicationDataCallback::onGetGetG3Mp4FileList \n");
    }

    std::vector<PCConfigureDataPacket::MP4FileInfo>
    CommunicationDataCallback::onGetG3Mp4FileOpt(G3Mp4FileOpt &g3Mp4FileOpt, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetG3Mp4FileOpt \n");
        std::vector<PCConfigureDataPacket::MP4FileInfo> ret = g3Mp4FileOpt.getFileList();
        return ret;
    }

    StartG3FileDownloadResult
    CommunicationDataCallback::onGetStartG3FileDownload(StartG3FileDownload &startG3FileDownload, const char *ip,
                                                        const int port) {
        printf("CommunicationDataCallback::onGetStartG3FileDownload \n");
        StartG3FileDownloadResult result;
        return result;
    }

    void
    CommunicationDataCallback::onGetStartG3DebugMode(StartG3DebugMode &startG3DebugMode, const char *ip,
                                                     const int port) {
        printf("CommunicationDataCallback::onGetStartG3DebugMode \n");
    }

    void
    CommunicationDataCallback::onGetSetFunctionLock(SetFunctionLock &setFunctionLock, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetSetFunctionLock \n");
    }

    void CommunicationDataCallback::onGetGetFunctionLockInfo(GetFunctionLockInfo &getFunctionLockInfo, const char *ip,
                                                             const int port) {
        printf("CommunicationDataCallback::onGetGetFunctionLockInfo \n");

    }

    void CommunicationDataCallback::onGetRestartMRV220(RestartG3 &getFunctionLockInfo, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetRestartMRV220 \n");
    }

    int
    CommunicationDataCallback::onGetStartUDPRealviewSeparate(StartUDPRealview_Separate &startUdpRealview,
                                                             const char *ip,
                                                             const int port) {
        printf("CommunicationDataCallback::onGetStartUDPRealviewSeparate \n");
        return -1;
    }

    void CommunicationDataCallback::onGetProductionTestResultFromMCUUART(G3AndMCUUartPTWrokResult &ptWrokResult) {
        printf("CommunicationDataCallback::onGetProductionTestResultFromMCUUART \n");
    }

    int CommunicationDataCallback::onNeedSendDataToNDSMqtt(std::string &strData) {
        printf("CommunicationDataCallback::onNeedSendDataToNDSMqtt \n");
        return -1;
    }

    int CommunicationDataCallback::onGetRTMPPushOpt(const std::string url, const int cameraId, const int opt) {
        printf("CommunicationDataCallback::onGetRTMPPushOpt \n");
        return -1;
    }

    void CommunicationDataCallback::onGetGetUpgradeFileDataFromRS485(G3RS485GetUpgradeFileData &getUpgradeFileData) {
        printf("CommunicationDataCallback::onGetGetUpgradeFileDataFromRS485 \n");
    }

    void CommunicationDataCallback::onGetUpgradeResultFromRS485(G3RS485UpgradeResult &upgradeResult) {
        printf("CommunicationDataCallback::onGetUpgradeResultFromRS485 \n");
    }

    void CommunicationDataCallback::onGetSetDispalyParamsResultFromRS485(
            G3RS485SetDispalyParamsResult &setDispalyParamsResult) {
        printf("CommunicationDataCallback::onGetSetDispalyParamsResultFromRS485 \n");

    }

    int
    CommunicationDataCallback::onGetSetPeripheralDisplayParams(SetPeripheralDisplayParams displayParams, const char *ip,
                                                               const int port) {
        printf("CommunicationDataCallback::onGetSetPeripheralDisplayParams \n");
        return -1;
    }

    void CommunicationDataCallback::onGetStopButtonStatusFromMODBUS(MODBUS_StopButtonStatus &stopButtonStatus) {
        printf("CommunicationDataCallback::onGetStopButtonStatusFromMODBUS \n");
    }

    void CommunicationDataCallback::onNeedSendDataToRS232(const uint8_t *buf, const int len) {
        printf("CommunicationDataCallback::onNeedSendDataToRS232 \n");
    }

    void CommunicationDataCallback::onGetReadDiscreteInputErrorFromMODBUS(const uint8_t errorCode) {
        printf("CommunicationDataCallback::onGetReadDiscreteInputError \n");
    }

    void
    CommunicationDataCallback::onGetGetMRV220LogFileList(GetMRV220LogFileList &getMrv220LogFileList, const char *ip,
                                                         const int port) {
        printf("CommunicationDataCallback::onGetGetMRV220LogFileList \n");
    }

    StartMRV220LogFileDownloadResult
    CommunicationDataCallback::onGetStartMRV220LogFileDownload(StartMRV220LogFileDownload &startMrv220LogFileDownload,
                                                               const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetStartMRV220LogFileDownload \n");
        return StartMRV220LogFileDownloadResult();
    }

    void CommunicationDataCallback::onGetGetMultimediaFileEncryptionKey(
            GetMultimediaFileEncryptionKey &getMultimediaFileEncryptionKey, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetGetMultimediaFileEncryptionKey \n");
    }

    int CommunicationDataCallback::onGetSetMultimediaFileEncryptionKey(
            SetMultimediaFileEncryptionKey &setMultimediaFileEncryptionKey, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetSetMultimediaFileEncryptionKey \n");
        return -1;
    }

    int
    CommunicationDataCallback::onGetGetAlarmSoundFileConf(GetAlarmSoundFileConf getAlarmSoundFileConf, const char *ip,
                                                          const int port) {
        printf("CommunicationDataCallback::onGetGetAlarmSoundFileConf \n");
        return -1;
    }

    void CommunicationDataCallback::onGetGetCameraInputTypeFilterConfig(
            GetCameraInputTypeFilterConfig &getCameraInputTypeFilterConfig, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetGetCameraInputTypeFilterConfig \n");
    }

    int CommunicationDataCallback::onGetSetCameraInputTypeFilterConfig(
            SetCameraInputTypeFilterConfig &setCameraInputTypeFilterConfig, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetSetCameraInputTypeFilterConfig \n");
        return -1;
    }

    void
    CommunicationDataCallback::onGetStartUpgradeL4ResultFromRS485(G3RS485StartUpgradeL4Result &startUpgradeL4Result) {
        printf("CommunicationDataCallback::onGetStartUpgradeL4ResultFromRS485 \n");
    }

    void CommunicationDataCallback::onGetChangeRS485Baudrate(const int baudrate) {
        printf("CommunicationDataCallback::onGetChangeRS485Baudrate \n");
    }

    void
    CommunicationDataCallback::onGetGetL4UpgradeFileDataFromRS485(G3RS485GetL4UpgradeFileData &getlL4UpgradeFileData) {
        printf("CommunicationDataCallback::onGetGetL4UpgradeFileDataFromRS485 \n");
    }

    void CommunicationDataCallback::onGetGetL4UpgradeResultFromRS485(G3RS485UpgradeL4Result &upgradeL4Result) {
        printf("CommunicationDataCallback::onGetGetL4UpgradeResultFromRS485 \n");
    }

    void CommunicationDataCallback::onGetQContr01PedalStatusFromMODBUS(MODBUS_QContrPedalStatus &pedalStatus) {
        printf("CommunicationDataCallback::onGetQContr01PedalStatusFromMODBUS \n");
    }

    void CommunicationDataCallback::onGetGetCameraReduceEffectInfoFromSocket(
            GetCameraReduceEffectInfo &getCameraReduceEffectInfo, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetGetCameraReduceEffectInfoFromSocket \n");
    }

    int CommunicationDataCallback::onGetSetCameraReduceEffectInfoFromSocket(
            SetCameraReduceEffectInfo &setCameraReduceEffectInfo, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetSetCameraReduceEffectInfoFromSocket \n");
        return -1;
    }

    int CommunicationDataCallback::onGetSetProductionTestingSwitchFromSocket(
            SetProductionTestingSwitch &setProductionTestingSwitch, const char *ip, const int port) {
        printf("CommunicationDataCallback::onGetSetProductionTestingSwitchFromSocket \n");
        return -1;
    }

    int CommunicationDataCallback::onGetStartDeviceUpgradeFromNDS(const int fileType, const std::string &fileUrl) {

        printf("CommunicationDataCallback::onGetStartDeviceUpgradeFromNDS \n");
        return -1;
    }

    int CommunicationDataCallback::onGetTCPRealviewOptFromSocket(TCPRealviewOpt &tcpRealviewOpt, const char *ip,
                                                                 const int port) {
        printf("CommunicationDataCallback::onGetTCPRealviewOptFromSocket \n");
        return -1;
    }

}





