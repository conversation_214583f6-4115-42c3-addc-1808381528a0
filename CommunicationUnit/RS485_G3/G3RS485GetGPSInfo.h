//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/11.
//

#ifndef VIS_G3_SOFTWARE_G3RS485GETGPSINFO_H
#define VIS_G3_SOFTWARE_G3RS485GETGPSINFO_H

#include "G3RS485DataPackage.h"

class G3RS485GetGPSInfo : public G3RS485DataPackage {
public:
    G3RS485GetGPSInfo();

    int toCode(uint8_t *buf) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;


};


#endif //VIS_G3_SOFTWARE_G3RS485GETGPSINFO_H
