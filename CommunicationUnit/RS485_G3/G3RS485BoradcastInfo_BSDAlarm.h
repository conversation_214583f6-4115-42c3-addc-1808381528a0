//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/12.
//

#ifndef VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_BSDALARM_H
#define VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_BSDALARM_H

#include "G3RS485BoradcastInfo.h"
#include "utils/CodeUtils.h"
#include "G3DetectionDefind.h"
class G3RS485BoradcastInfo_BSDAlarm : public G3RS485BoradcastInfo {
public:
    struct RS485BSDAlarmLevel1{
        /* Bit0	左侧行人接近报警 */
        bool pedestrian_left : 1;
        /* Bit1	左侧车辆接近报警 */
        bool vehicle_left : 1;
        /* Bit2	右侧行人接近报警 */
        bool pedestrian_right : 1;
        /* Bit3	右侧车辆接近报警 */
        bool vehicle_right : 1;
        /* Bit4	前方行人接近报警 */
        bool pedestrian_forward : 1;
        /* Bit5	前方车辆接近报警 */
        bool vehicle_forward : 1;
        /* Bit6	后方行人接近报警 */
        bool pedestrian_backward : 1;
        /* Bit7	后方车辆接近报警 */
        bool vehicle_backward : 1;
        /* Bit8-Bit15	保留 */
        uint reserve : 8;
    };

    struct RS485BSDAlarmLevel2{
        /* Bit0	左侧行人接近报警 */
        bool pedestrian_left : 1;
        /* Bit1	左侧车辆接近报警 */
        bool vehicle_left : 1;
        /* Bit2	右侧行人接近报警 */
        bool pedestrian_right : 1;
        /* Bit3	右侧车辆接近报警 */
        bool vehicle_right : 1;
        /* Bit4	前方行人接近报警 */
        bool pedestrian_forward : 1;
        /* Bit5	前方车辆接近报警 */
        bool vehicle_forward : 1;
        /* Bit6	后方行人接近报警 */
        bool pedestrian_backward : 1;
        /* Bit7	后方车辆接近报警 */
        bool vehicle_backward : 1;
        /* Bit8-Bit15	保留 */
        uint reserve : 8;
    };

    G3RS485BoradcastInfo_BSDAlarm();

    int toCode(uint8_t *buf) override;

    const RS485BSDAlarmLevel1 &getAlarmLevel1() const;

    void setAlarmLevel1(const RS485BSDAlarmLevel1 &alarmLevel1);

    const RS485BSDAlarmLevel2 &getAlarmLevel2() const;

    void setAlarmLevel2(const RS485BSDAlarmLevel2 &alarmLevel2);

private:

    /* 1级报警 */
    RS485BSDAlarmLevel1 alarmLevel1;
    /* 2级报警 */
    RS485BSDAlarmLevel2 alarmLevel2;

};


#endif //VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_BSDALARM_H
