//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/18.
//

#include "G3RS485GetUpgradeFileData.h"

G3RS485GetUpgradeFileData::G3RS485GetUpgradeFileData() {
    setMsgId(RS485_MSGID_GET_UPGRADE_FILE_DATA);
}

uint8_t G3RS485GetUpgradeFileData::getPeripheralId() const {
    return peripheralId;
}

void G3RS485GetUpgradeFileData::setPeripheralId(uint8_t peripheralId) {
    G3RS485GetUpgradeFileData::peripheralId = peripheralId;
}

uint32_t G3RS485GetUpgradeFileData::getOffset() const {
    return offset;
}

void G3RS485GetUpgradeFileData::setOffset(uint32_t offset) {
    G3RS485GetUpgradeFileData::offset = offset;
}

int G3RS485GetUpgradeFileData::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 7;   /* 第8位就是正式内容了 */
    /* 设置内容长度 */
    setContentLen(5);
    /* 塞入外设编号 */
    buf[index] = peripheralId;
    index++;
    /* 塞入偏移地址编号 */
    CodeUtils::getInstance().int32ToBb(offset, buf + index);
    index += 4;
    /* 加上头尾那些东西 */
    ret = G3RS485DataPackage::toCode(buf);
    return ret;
}

int G3RS485GetUpgradeFileData::decode(uint8_t *buf, int len) {
    /* 直接从数据内容开始取 */
    int index = 7;
    /* 取外设ID */
    setPeripheralId(buf[index]);
    index++;
    /* 取偏移地址 */
    setOffset(CodeUtils::getInstance().BbToint32(buf + index));
    index += 4;
    /* 去数据内容之外的东西 */
    return G3RS485DataPackage::decode(buf, len);
}
