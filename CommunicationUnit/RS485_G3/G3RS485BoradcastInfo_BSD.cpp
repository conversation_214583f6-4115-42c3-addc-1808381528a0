//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/6.
//

#include "G3RS485BoradcastInfo_BSD.h"


G3RS485BoradcastInfo_BSD::G3RS485BoradcastInfo_BSD() {
    setDataId(DATAID_BSD);
    setDatalen(9);
}

int G3RS485BoradcastInfo_BSD::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 3; //前面留三个字节用来放ID跟长度
    /* 整理行人状态 */
    pedestrianStatus = 0;
    if (leftAlarmArea2Status) {
        pedestrianStatus |= 0x01;
    }
    if (rightAlarmArea2Status) {
        pedestrianStatus |= 0x02;
    }
    if (forwardAlarmArea2Status) {
        pedestrianStatus |= 0x04;
    }
    if (backwardAlarmArea2Status) {
        pedestrianStatus |= 0x08;
    }
    if (leftAlarmArea1Status) {
        pedestrianStatus |= 0x10;
    }
    if (rightAlarmArea1Status) {
        pedestrianStatus |= 0x20;
    }
    if (forwardAlarmArea1Status) {
        pedestrianStatus |= 0x40;
    }
    if (backwardAlarmArea1Status) {
        pedestrianStatus |= 0x80;
    }
    /* 塞行人状态 */
    buf[index] = pedestrianStatus;
    index++;



    /* 整理镜头遮挡状态 */
    cameraCoverStatus = 0;
    if (leftCoverStatus) {
        cameraCoverStatus |= 0x01;
    }
    if (rightCoverStatus) {
        cameraCoverStatus |= 0x02;
    }
    if (forwardCoverStatus) {
        cameraCoverStatus |= 0x04;
    }
    if (backwardCoverStatus) {
        cameraCoverStatus |= 0x08;
    }
    /* 塞镜头遮挡状态 */
    buf[index] = cameraCoverStatus;
    index++;

    /* 整理镜头bypass状态 */
    cameraBypassStatus = 0;
    if (leftBypassStatus) {
        cameraBypassStatus |= 0x01;
    }
    if (rightBypassStatus) {
        cameraBypassStatus |= 0x02;
    }
    if (forwardBypassStatus) {
        cameraBypassStatus |= 0x04;
    }
    if (backwardBypassStatus) {
        cameraBypassStatus |= 0x08;
    }

    /* 塞镜头bypass状态 */
    buf[index] = cameraBypassStatus;
    index++;




    /* 整理车辆状态 */
    vehicleStatus = 0;
    if (leftAlarmArea2Status_vehicle) {
        vehicleStatus |= 0x01;
    }
    if (rightAlarmArea2Status_vehicle) {
        vehicleStatus |= 0x02;
    }
    if (forwardAlarmArea2Status_vehicle) {
        vehicleStatus |= 0x04;
    }
    if (backwardAlarmArea2Status_vehicle) {
        vehicleStatus |= 0x08;
    }
    if (leftAlarmArea1Status_vehicle) {
        vehicleStatus |= 0x10;
    }
    if (rightAlarmArea1Status_vehicle) {
        vehicleStatus |= 0x20;
    }
    if (forwardAlarmArea1Status_vehicle) {
        vehicleStatus |= 0x40;
    }
    if (backwardAlarmArea1Status_vehicle) {
        vehicleStatus |= 0x80;
    }
    /* 塞车辆状态 */
    buf[index] = vehicleStatus;
    index++;
    /* 塞预留字节 */
    memcpy(buf + index, reserveByte, sizeof(reserveByte));
    index += sizeof(reserveByte);
    G3RS485BoradcastInfo::toCode(buf);
    ret = index;
    return ret;
}

int G3RS485BoradcastInfo_BSD::getLeftAlarmArea1Status() const {
    return leftAlarmArea1Status;
}

void G3RS485BoradcastInfo_BSD::setLeftAlarmArea1Status(int leftAlarmArea1Status) {
    G3RS485BoradcastInfo_BSD::leftAlarmArea1Status = leftAlarmArea1Status;
}

int G3RS485BoradcastInfo_BSD::getLeftAlarmArea2Status() const {
    return leftAlarmArea2Status;
}

void G3RS485BoradcastInfo_BSD::setLeftAlarmArea2Status(int leftAlarmArea2Status) {
    G3RS485BoradcastInfo_BSD::leftAlarmArea2Status = leftAlarmArea2Status;
}

int G3RS485BoradcastInfo_BSD::getRightAlarmArea1Status() const {
    return rightAlarmArea1Status;
}

void G3RS485BoradcastInfo_BSD::setRightAlarmArea1Status(int rightAlarmArea1Status) {
    G3RS485BoradcastInfo_BSD::rightAlarmArea1Status = rightAlarmArea1Status;
}

int G3RS485BoradcastInfo_BSD::getRightAlarmArea2Status() const {
    return rightAlarmArea2Status;
}

void G3RS485BoradcastInfo_BSD::setRightAlarmArea2Status(int rightAlarmArea2Status) {
    G3RS485BoradcastInfo_BSD::rightAlarmArea2Status = rightAlarmArea2Status;
}

int G3RS485BoradcastInfo_BSD::getForwardAlarmArea1Status() const {
    return forwardAlarmArea1Status;
}

void G3RS485BoradcastInfo_BSD::setForwardAlarmArea1Status(int forwardAlarmArea1Status) {
    G3RS485BoradcastInfo_BSD::forwardAlarmArea1Status = forwardAlarmArea1Status;
}

int G3RS485BoradcastInfo_BSD::getForwardAlarmArea2Status() const {
    return forwardAlarmArea2Status;
}

void G3RS485BoradcastInfo_BSD::setForwardAlarmArea2Status(int forwardAlarmArea2Status) {
    G3RS485BoradcastInfo_BSD::forwardAlarmArea2Status = forwardAlarmArea2Status;
}

int G3RS485BoradcastInfo_BSD::getBackwardAlarmArea1Status() const {
    return backwardAlarmArea1Status;
}

void G3RS485BoradcastInfo_BSD::setBackwardAlarmArea1Status(int backwardAlarmArea1Status) {
    G3RS485BoradcastInfo_BSD::backwardAlarmArea1Status = backwardAlarmArea1Status;
}

int G3RS485BoradcastInfo_BSD::getBackwardAlarmArea2Status() const {
    return backwardAlarmArea2Status;
}

void G3RS485BoradcastInfo_BSD::setBackwardAlarmArea2Status(int backwardAlarmArea2Status) {
    G3RS485BoradcastInfo_BSD::backwardAlarmArea2Status = backwardAlarmArea2Status;
}

int G3RS485BoradcastInfo_BSD::getLeftCoverStatus() const {
    return leftCoverStatus;
}

void G3RS485BoradcastInfo_BSD::setLeftCoverStatus(int leftCoverStatus) {
    G3RS485BoradcastInfo_BSD::leftCoverStatus = leftCoverStatus;
}

int G3RS485BoradcastInfo_BSD::getRightCoverStatus() const {
    return rightCoverStatus;
}

void G3RS485BoradcastInfo_BSD::setRightCoverStatus(int rightCoverStatus) {
    G3RS485BoradcastInfo_BSD::rightCoverStatus = rightCoverStatus;
}

int G3RS485BoradcastInfo_BSD::getForwardCoverStatus() const {
    return forwardCoverStatus;
}

void G3RS485BoradcastInfo_BSD::setForwardCoverStatus(int forwardCoverStatus) {
    G3RS485BoradcastInfo_BSD::forwardCoverStatus = forwardCoverStatus;
}

int G3RS485BoradcastInfo_BSD::getBackwardCoverStatus() const {
    return backwardCoverStatus;
}

void G3RS485BoradcastInfo_BSD::setBackwardCoverStatus(int backwardCoverStatus) {
    G3RS485BoradcastInfo_BSD::backwardCoverStatus = backwardCoverStatus;
}

int G3RS485BoradcastInfo_BSD::getLeftBypassStatus() const {
    return leftBypassStatus;
}

void G3RS485BoradcastInfo_BSD::setLeftBypassStatus(int leftBypassStatus) {
    G3RS485BoradcastInfo_BSD::leftBypassStatus = leftBypassStatus;
}

int G3RS485BoradcastInfo_BSD::getRightBypassStatus() const {
    return rightBypassStatus;
}

void G3RS485BoradcastInfo_BSD::setRightBypassStatus(int rightBypassStatus) {
    G3RS485BoradcastInfo_BSD::rightBypassStatus = rightBypassStatus;
}

int G3RS485BoradcastInfo_BSD::getForwardBypassStatus() const {
    return forwardBypassStatus;
}

void G3RS485BoradcastInfo_BSD::setForwardBypassStatus(int forwardBypassStatus) {
    G3RS485BoradcastInfo_BSD::forwardBypassStatus = forwardBypassStatus;
}

int G3RS485BoradcastInfo_BSD::getBackwardBypassStatus() const {
    return backwardBypassStatus;
}

void G3RS485BoradcastInfo_BSD::setBackwardBypassStatus(int backwardBypassStatus) {
    G3RS485BoradcastInfo_BSD::backwardBypassStatus = backwardBypassStatus;
}

int G3RS485BoradcastInfo_BSD::getLeftAlarmArea1StatusVehicle() const {
    return leftAlarmArea1Status_vehicle;
}

void G3RS485BoradcastInfo_BSD::setLeftAlarmArea1StatusVehicle(int leftAlarmArea1StatusVehicle) {
    leftAlarmArea1Status_vehicle = leftAlarmArea1StatusVehicle;
}

int G3RS485BoradcastInfo_BSD::getLeftAlarmArea2StatusVehicle() const {
    return leftAlarmArea2Status_vehicle;
}

void G3RS485BoradcastInfo_BSD::setLeftAlarmArea2StatusVehicle(int leftAlarmArea2StatusVehicle) {
    leftAlarmArea2Status_vehicle = leftAlarmArea2StatusVehicle;
}

int G3RS485BoradcastInfo_BSD::getRightAlarmArea1StatusVehicle() const {
    return rightAlarmArea1Status_vehicle;
}

void G3RS485BoradcastInfo_BSD::setRightAlarmArea1StatusVehicle(int rightAlarmArea1StatusVehicle) {
    rightAlarmArea1Status_vehicle = rightAlarmArea1StatusVehicle;
}

int G3RS485BoradcastInfo_BSD::getRightAlarmArea2StatusVehicle() const {
    return rightAlarmArea2Status_vehicle;
}

void G3RS485BoradcastInfo_BSD::setRightAlarmArea2StatusVehicle(int rightAlarmArea2StatusVehicle) {
    rightAlarmArea2Status_vehicle = rightAlarmArea2StatusVehicle;
}

int G3RS485BoradcastInfo_BSD::getForwardAlarmArea1StatusVehicle() const {
    return forwardAlarmArea1Status_vehicle;
}

void G3RS485BoradcastInfo_BSD::setForwardAlarmArea1StatusVehicle(int forwardAlarmArea1StatusVehicle) {
    forwardAlarmArea1Status_vehicle = forwardAlarmArea1StatusVehicle;
}

int G3RS485BoradcastInfo_BSD::getForwardAlarmArea2StatusVehicle() const {
    return forwardAlarmArea2Status_vehicle;
}

void G3RS485BoradcastInfo_BSD::setForwardAlarmArea2StatusVehicle(int forwardAlarmArea2StatusVehicle) {
    forwardAlarmArea2Status_vehicle = forwardAlarmArea2StatusVehicle;
}

int G3RS485BoradcastInfo_BSD::getBackwardAlarmArea1StatusVehicle() const {
    return backwardAlarmArea1Status_vehicle;
}

void G3RS485BoradcastInfo_BSD::setBackwardAlarmArea1StatusVehicle(int backwardAlarmArea1StatusVehicle) {
    backwardAlarmArea1Status_vehicle = backwardAlarmArea1StatusVehicle;
}

int G3RS485BoradcastInfo_BSD::getBackwardAlarmArea2StatusVehicle() const {
    return backwardAlarmArea2Status_vehicle;
}

void G3RS485BoradcastInfo_BSD::setBackwardAlarmArea2StatusVehicle(int backwardAlarmArea2StatusVehicle) {
    backwardAlarmArea2Status_vehicle = backwardAlarmArea2StatusVehicle;
}

