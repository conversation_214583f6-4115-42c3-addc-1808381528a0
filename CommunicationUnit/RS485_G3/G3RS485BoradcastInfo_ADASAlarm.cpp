//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/12.
//

#include "G3RS485BoradcastInfo_ADASAlarm.h"

G3RS485BoradcastInfo_ADASAlarm::G3RS485BoradcastInfo_ADASAlarm() {
    setDataId(DATAID_ADAS_EVENT);
    setDatalen(8);
}

const G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 &G3RS485BoradcastInfo_ADASAlarm::getAdasAlarmType1() const {
    return adasAlarmType1;
}

void G3RS485BoradcastInfo_ADASAlarm::setAdasAlarmType1(
        const G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 &adasAlarmType1) {
    G3RS485BoradcastInfo_ADASAlarm::adasAlarmType1 = adasAlarmType1;
}

const G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType2 &G3RS485BoradcastInfo_ADASAlarm::getAdasAlarmType2() const {
    return adasAlarmType2;
}

void G3RS485BoradcastInfo_ADASAlarm::setAdasAlarmType2(
        const G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType2 &adasAlarmType2) {
    G3RS485BoradcastInfo_ADASAlarm::adasAlarmType2 = adasAlarmType2;
}

uint16_t G3RS485BoradcastInfo_ADASAlarm::getAdasAlarmType3() const {
    return adasAlarmType3;
}

void G3RS485BoradcastInfo_ADASAlarm::setAdasAlarmType3(uint16_t adasAlarmType3) {
    G3RS485BoradcastInfo_ADASAlarm::adasAlarmType3 = adasAlarmType3;
}

uint8_t G3RS485BoradcastInfo_ADASAlarm::getVehicleCollisionTime() const {
    return vehicleCollisionTime;
}

void G3RS485BoradcastInfo_ADASAlarm::setVehicleCollisionTime(uint8_t vehicleCollisionTime) {
    G3RS485BoradcastInfo_ADASAlarm::vehicleCollisionTime = vehicleCollisionTime;
}

uint8_t G3RS485BoradcastInfo_ADASAlarm::getPedestrianCollisionTime() const {
    return pedestrianCollisionTime;
}

void G3RS485BoradcastInfo_ADASAlarm::setPedestrianCollisionTime(uint8_t pedestrianCollisionTime) {
    G3RS485BoradcastInfo_ADASAlarm::pedestrianCollisionTime = pedestrianCollisionTime;
}

int G3RS485BoradcastInfo_ADASAlarm::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 3; //前面留三个字节用来放ID跟长度

    uint16_t tempAlarm = 0;
    /* 报警/事件类型1 */
    memcpy(&tempAlarm,&adasAlarmType1,2);
    CodeUtils::getInstance().uint16ToBb(tempAlarm,buf+index);
    index = index + 2;
    /* 报警/事件类型2 */
    memcpy(&tempAlarm,&adasAlarmType2,2);
    CodeUtils::getInstance().uint16ToBb(tempAlarm,buf+index);
    index = index + 2;
    /* 报警/事件类型3 */
    memcpy(&tempAlarm,&adasAlarmType3,2);
    CodeUtils::getInstance().uint16ToBb(tempAlarm,buf+index);
    index = index + 2;
    /* 车辆碰撞时间 */
    buf[index] = vehicleCollisionTime;
    index = index + 1;
    /* 行人碰撞时间 */
    buf[index] = pedestrianCollisionTime;
    index = index + 1;

    G3RS485BoradcastInfo::toCode(buf);
    ret = index;
    return ret;
}

