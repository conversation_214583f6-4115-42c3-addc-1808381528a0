//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//

#include "G3RS485BoradcastInfo.h"

uint16_t G3RS485BoradcastInfo::getDataId() const {
    return dataId;
}

void G3RS485BoradcastInfo::setDataId(uint16_t dataId) {
    G3RS485BoradcastInfo::dataId = dataId;
}

uint8_t G3RS485BoradcastInfo::getDatalen() const {
    return datalen;
}

void G3RS485BoradcastInfo::setDatalen(uint8_t datalen) {
    G3RS485BoradcastInfo::datalen = datalen;
}

int G3RS485BoradcastInfo::toCode(uint8_t *buf) {
    int index = 0;
    CodeUtils::getInstance().uint16ToBb(dataId, buf + index);
    index += 2;
    buf[index] = datalen;
    index++;
    return index;
}
