//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/8.
//

#include "G3RS485BoradcastInfo_SystemInfo.h"

G3RS485BoradcastInfo_SystemInfo::G3RS485BoradcastInfo_SystemInfo() {
    setDataId(DATAID_SYSINFO);
    setDatalen(4);
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getTfCardError() const {
    return tfCardError;
}

void G3RS485BoradcastInfo_SystemInfo::setTfCardError(uint8_t tfCardError) {
    G3RS485BoradcastInfo_SystemInfo::tfCardError = tfCardError;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getHighTemperature() const {
    return highTemperature;
}

void G3RS485BoradcastInfo_SystemInfo::setHighTemperature(uint8_t highTemperature) {
    G3RS485BoradcastInfo_SystemInfo::highTemperature = highTemperature;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getAdasCameraError() const {
    return adasCameraError;
}

void G3RS485BoradcastInfo_SystemInfo::setAdasCameraError(uint8_t adasCameraError) {
    G3RS485BoradcastInfo_SystemInfo::adasCameraError = adasCameraError;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getBsdCameraErrorL() const {
    return bsdCameraError_L;
}

void G3RS485BoradcastInfo_SystemInfo::setBsdCameraErrorL(uint8_t bsdCameraErrorL) {
    bsdCameraError_L = bsdCameraErrorL;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getBsdCameraErrorR() const {
    return bsdCameraError_R;
}

void G3RS485BoradcastInfo_SystemInfo::setBsdCameraErrorR(uint8_t bsdCameraErrorR) {
    bsdCameraError_R = bsdCameraErrorR;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getDsmCameraError() const {
    return dsmCameraError;
}

void G3RS485BoradcastInfo_SystemInfo::setDsmCameraError(uint8_t dsmCameraError) {
    G3RS485BoradcastInfo_SystemInfo::dsmCameraError = dsmCameraError;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getAdasAlgorithmError() const {
    return adasAlgorithmError;
}

void G3RS485BoradcastInfo_SystemInfo::setAdasAlgorithmError(uint8_t adasAlgorithmError) {
    G3RS485BoradcastInfo_SystemInfo::adasAlgorithmError = adasAlgorithmError;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getBsdAlgorithmErrorL() const {
    return bsdAlgorithmError_L;
}

void G3RS485BoradcastInfo_SystemInfo::setBsdAlgorithmErrorL(uint8_t bsdAlgorithmErrorL) {
    bsdAlgorithmError_L = bsdAlgorithmErrorL;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getBsdAlgorithmErrorR() const {
    return bsdAlgorithmError_R;
}

void G3RS485BoradcastInfo_SystemInfo::setBsdAlgorithmErrorR(uint8_t bsdAlgorithmErrorR) {
    bsdAlgorithmError_R = bsdAlgorithmErrorR;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getDsmAlgorithmError() const {
    return dsmAlgorithmError;
}

void G3RS485BoradcastInfo_SystemInfo::setDsmAlgorithmError(uint8_t dsmAlgorithmError) {
    G3RS485BoradcastInfo_SystemInfo::dsmAlgorithmError = dsmAlgorithmError;
}

int G3RS485BoradcastInfo_SystemInfo::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 3; //前面留三个字节用来放ID跟长度

    uint32_t systemInfo = 0x00000000;

    /* 塞TF卡状态 */
    if (tfCardError == 1) {
        systemInfo |= 0x01;
    }
    /* 塞高温报警 */
    if (highTemperature == 1) {
        systemInfo |= 0x02;
    }
    /* 塞ADAS摄像头 */
    if (adasCameraError == 1) {
        systemInfo |= 0x04;
    }

    /* 塞左侧摄像头 */
    if (bsdCameraError_L == 1) {
        systemInfo |= 0x08;
    }

    /* 塞右侧摄像头 */
    if (bsdCameraError_R == 1) {
        systemInfo |= 0x10;
    }

    /* 塞DSM摄像头 */
    if (dsmCameraError == 1) {
        systemInfo |= 0x20;
    }

    /* 塞ADAS算法 */
    if (adasAlgorithmError == 1) {
        systemInfo |= 0x40;
    }

    /* 塞 左侧算法 */
    if (bsdAlgorithmError_L == 1) {
        systemInfo |= 0x80;
    }

    /* 塞右侧算法 */
    if (bsdAlgorithmError_R == 1) {
        systemInfo |= 0x100;
    }

    /* 塞DSM算法 */
    if (dsmAlgorithmError == 1) {
        systemInfo |= 0x200;
    }

    /* 塞1号摄像头遮挡 */
    if (cameraCover_1 == 1) {
        systemInfo |= 0x10000;
    }
    /* 塞2号摄像头遮挡 */
    if (cameraCover_2 == 1) {
        systemInfo |= 0x20000;
    }
    /* 塞3号摄像头遮挡 */
    if (cameraCover_3 == 1) {
        systemInfo |= 0x40000;
    }
    /* 塞4号摄像头遮挡 */
    if (cameraCover_4 == 1) {
        systemInfo |= 0x80000;
    }
    /* 塞5号摄像头遮挡 */
    if (cameraCover_5 == 1) {
        systemInfo |= 0x100000;
    }
    /* 塞6号摄像头遮挡 */
    if (cameraCover_6 == 1) {
        systemInfo |= 0x200000;
    }
    /* 塞7号摄像头遮挡 */
    if (cameraCover_7 == 1) {
        systemInfo |= 0x400000;
    }
    /* 塞8号摄像头遮挡 */
    if (cameraCover_8 == 1) {
        systemInfo |= 0x800000;
    }


    CodeUtils::getInstance().uint32ToBb(systemInfo, buf + index);
    index += 4;
    G3RS485BoradcastInfo::toCode(buf);

    ret = index;

    return ret;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getCameraCover1() const {
    return cameraCover_1;
}

void G3RS485BoradcastInfo_SystemInfo::setCameraCover1(uint8_t cameraCover1) {
    cameraCover_1 = cameraCover1;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getCameraCover2() const {
    return cameraCover_2;
}

void G3RS485BoradcastInfo_SystemInfo::setCameraCover2(uint8_t cameraCover2) {
    cameraCover_2 = cameraCover2;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getCameraCover3() const {
    return cameraCover_3;
}

void G3RS485BoradcastInfo_SystemInfo::setCameraCover3(uint8_t cameraCover3) {
    cameraCover_3 = cameraCover3;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getCameraCover4() const {
    return cameraCover_4;
}

void G3RS485BoradcastInfo_SystemInfo::setCameraCover4(uint8_t cameraCover4) {
    cameraCover_4 = cameraCover4;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getCameraCover5() const {
    return cameraCover_5;
}

void G3RS485BoradcastInfo_SystemInfo::setCameraCover5(uint8_t cameraCover5) {
    cameraCover_5 = cameraCover5;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getCameraCover6() const {
    return cameraCover_6;
}

void G3RS485BoradcastInfo_SystemInfo::setCameraCover6(uint8_t cameraCover6) {
    cameraCover_6 = cameraCover6;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getCameraCover7() const {
    return cameraCover_7;
}

void G3RS485BoradcastInfo_SystemInfo::setCameraCover7(uint8_t cameraCover7) {
    cameraCover_7 = cameraCover7;
}

uint8_t G3RS485BoradcastInfo_SystemInfo::getCameraCover8() const {
    return cameraCover_8;
}

void G3RS485BoradcastInfo_SystemInfo::setCameraCover8(uint8_t cameraCover8) {
    cameraCover_8 = cameraCover8;
}
