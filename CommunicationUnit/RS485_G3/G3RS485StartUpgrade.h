//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/15.
//

#ifndef VIS_G3_SOFTWARE_G3RS485STARTUPGRADE_H
#define VIS_G3_SOFTWARE_G3RS485STARTUPGRADE_H

#include "G3RS485DataPackage.h"

class G3RS485StartUpgrade : public G3RS485DataPackage {
public:
    G3RS485StartUpgrade();

    int toCode(uint8_t *buf) override;

    int decode(uint8_t *buf, int len) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;

};


#endif //VIS_G3_SOFTWARE_G3RS485STARTUPGRADE_H