//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/8.
//

#include "G3RS485GetPeripheralVersion.h"

G3RS485GetPeripheralVersion::G3RS485GetPeripheralVersion() {
    setMsgId(RS485_MSGID_GET_PERIPHERAL_VERSION);
}

int G3RS485GetPeripheralVersion::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 7;   /* 第8位就是正式内容了 */
    /* 设置内容长度 */
    setContentLen(1);
    /* 塞入外设编号 */
    buf[index] = peripheralId;
    index++;
    /* 加上头尾那些东西 */
    ret = G3RS485DataPackage::toCode(buf);
    return ret;
}

uint8_t G3RS485GetPeripheralVersion::getPeripheralId() const {
    return peripheralId;
}

void G3RS485GetPeripheralVersion::setPeripheralId(uint8_t peripheralId) {
    G3RS485GetPeripheralVersion::peripheralId = peripheralId;
}
