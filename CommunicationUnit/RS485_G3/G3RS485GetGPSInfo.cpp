//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/11.
//

#include "G3RS485GetGPSInfo.h"

G3RS485GetGPSInfo::G3RS485GetGPSInfo() {
    setMsgId(RS485_MSGID_GET_GPS_INFO);
}

int G3RS485GetGPSInfo::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 7;   /* 第8位就是正式内容了 */
    /* 设置内容长度 */
    setContentLen(1);
    /* 塞入外设编号 */
    buf[index] = peripheralId;
    index++;
    /* 加上头尾那些东西 */
    ret = G3RS485DataPackage::toCode(buf);
    return ret;
}

uint8_t G3RS485GetGPSInfo::getPeripheralId() const {
    return peripheralId;
}

void G3RS485GetGPSInfo::setPeripheralId(uint8_t peripheralId) {
    G3RS485GetGPSInfo::peripheralId = peripheralId;
}
