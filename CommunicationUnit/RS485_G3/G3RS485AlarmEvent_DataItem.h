//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/28.
//

#ifndef VIS_G3_SOFTWARE_G3RS485ALARMEVENT_DATAITEM_H
#define VIS_G3_SOFTWARE_G3RS485ALARMEVENT_DATAITEM_H

#include <cstdint>
#include <cstring>
#include <CodeUtils.h>

class G3RS485AlarmEvent_DataItem {
public:
    /* 报警事件数据项ID_ADAS报警信息 */
    static const uint16_t DATAID_ADAS_ALARM_EVENT = 0x0064;
    /* 报警事件数据项ID_DSM报警信息 */
    static const uint16_t DATAID_DSM_ALARM_EVENT = 0x0065;
    /* 报警事件数据项ID_BSD报警信息 */
    static const uint16_t DATAID_BSD_ALARM_EVENT = 0x0067;



    /* 报警标志状态-------- 0x00：不可用 */
    static const uint16_t EVENTSTATUS_NONE = 0x00;
    /* 报警标志状态-------- 0x01：开始标志 */
    static const uint16_t EVENTSTATUS_START = 0x01;
    /* 报警标志状态-------- 0x02：结束标志 */
    static const uint16_t EVENTSTATUS_STOP = 0x02;


    G3RS485AlarmEvent_DataItem();

    ~G3RS485AlarmEvent_DataItem();

    virtual int toCode(uint8_t *buf);

    uint16_t getDataId() const;

    void setDataId(uint16_t dataId);

    uint32_t getDataLen() const;

    void setDataLen(uint32_t dataLen);

private:
    /* 数据项ID */
    uint16_t dataId = -1;
    /* 数据项的长度 */
    uint32_t dataLen = 0;
};


#endif //VIS_G3_SOFTWARE_G3RS485ALARMEVENT_DATAITEM_H
