//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/18.
//

#ifndef VIS_G3_SOFTWARE_G3RS485RESPONDUPGRADEFILEDATA_H
#define VIS_G3_SOFTWARE_G3RS485RESPONDUPGRADEFILEDATA_H

#include "G3RS485DataPackage.h"

class G3RS485RespondUpgradeFileData : public G3RS485DataPackage {
public:
    G3RS485RespondUpgradeFileData();

    int toCode(uint8_t *buf) override;

    int decode(uint8_t *buf, int len) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

    uint32_t getOffset() const;

    void setOffset(uint32_t offset);

    void setFileData(const uint8_t *data, const int len);

private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;
    /* 文件偏移地址 */
    uint32_t offset = 0;
    /* 文件数据 每包数据定长不满252就补0xFF  校验和就只一位*/
    uint8_t fileData[252];


    /* 一包文件数据的长度 */
    int ONE_FILE_DATA_LEN = 252;
};


#endif //VIS_G3_SOFTWARE_G3RS485RESPONDUPGRADEFILEDATA_H
