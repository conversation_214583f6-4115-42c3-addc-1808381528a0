//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/13.
//

#include <unistd.h>
#include <termios.h>
#include "G3RS485Manager.h"
#include "XuString.h"
#include "XuFile.h"
#include "G3RS485BoradcastInfo_CameraStatus.h"
#include "XuTimeUtil.h"


namespace vis {

    void G3RS485Manager::init(CommunicationDataCallback &communicationDataCallback) {
        callback = &communicationDataCallback;
        rs485MessageDecoder.init();
        rs485MessageDecoder.setInterface(*this);
        isNeedStopG3RS485Manager = false;
        upgradeFileData = static_cast<uint8_t *>(malloc(1024 * 1024 * 2));
        rs485L4Upgradedata = static_cast<uint8_t *>(malloc(129 * 1024));
        rs485L4UpgradePacketdata = static_cast<uint8_t *>(malloc(64 * 1024));
        upgradeFileDataLen = 0;
    }

    void G3RS485Manager::run() {
        std::string pthreadName = "RS485Com";
        pthread_setname_np(pthread_self(), pthreadName.c_str());


        uint8_t rs485Packetdata[500];
        memset(rs485Packetdata, 0x00, sizeof(rs485Packetdata));

        uint8_t rs485data[1000];
        memset(rs485data, 0x00, sizeof(rs485data));



        /* 直接在这里休眠2s，等待485连接成功,如果用户觉得体验不好，就再优化 */
        sleep(2);
        /* 如果没拿到L4版本号  就要拿一下 */
        if (strcmp(getenv("G3_L4_VERSION_NAME"), "-1") == 0) {
            int dataLen = rs485MessageEncoder.generateGetPeripheralVersion(G3RS485DataPackage::RS485_DEVICE_ID_L4,
                                                                           rs485Packetdata);
            int senddataLen = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, dataLen, 1,
                                                                              dataLen - 2,
                                                                              rs485data);
            callback->onNeedSendDataToRS485(rs485data, senddataLen);
            usleep(1000 * 100);
        }

        /* 如果没拿到L3版本号  就要拿一下 */
        if (strcmp(getenv("G3_L3_VERSION_NAME"), "-1") == 0) {
            int dataLen = rs485MessageEncoder.generateGetPeripheralVersion(G3RS485DataPackage::RS485_DEVICE_ID_L3,
                                                                           rs485Packetdata);
            int senddataLen = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, dataLen, 1,
                                                                              dataLen - 2,
                                                                              rs485data);
            callback->onNeedSendDataToRS485(rs485data, senddataLen);
            usleep(1000 * 100);
        }

        /* 如果没拿到L2版本号  就要拿一下 */
        if (strcmp(getenv("G3_L2_VERSION_NAME"), "-1") == 0) {
            int dataLen = rs485MessageEncoder.generateGetPeripheralVersion(G3RS485DataPackage::RS485_DEVICE_ID_L2,
                                                                           rs485Packetdata);
            int senddataLen = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, dataLen, 1, dataLen - 2,
                                                                              rs485data);
            callback->onNeedSendDataToRS485(rs485data, senddataLen);
            usleep(1000 * 100);
        }

        /* 如果没拿到GPS盒子版本号  就要拿一下 */
        if (strcmp(getenv("G3_GPSBOX_VERSION_NAME"), "-1") == 0) {
            int dataLen = rs485MessageEncoder.generateGetPeripheralVersion(G3RS485DataPackage::RS485_DEVICE_ID_GPS,
                                                                           rs485Packetdata);
            int senddataLen = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, dataLen, 1, dataLen - 2,
                                                                              rs485data);
            callback->onNeedSendDataToRS485(rs485data, senddataLen);
            usleep(1000 * 100);
        }


        while (!isNeedStopG3RS485Manager) {

            G3RS485AlarmBroadcast g3485AlarmBroadcast;
            g3485AlarmBroadcast.setRs485DeviceId(G3RS485DataPackage::RS485_DEVICE_ID_BROADCAST);
            /* 定义系统错误信息的对象 */
            G3RS485BoradcastInfo_SystemInfo g3Rs485BoradcastInfoSystemInfo;
            /* 是否有TF卡 */
            g3Rs485BoradcastInfoSystemInfo.setTfCardError(G3_Configuration::getInstance().isHasTfCard() ? 0 : 1);
            /* 是否高温 */
            g3Rs485BoradcastInfoSystemInfo.setHighTemperature(
                    G3_Configuration::getInstance().isHasHightTemperature() ? 1 : 0);


            /* 塞一条车辆运行状态信息 */
            G3RS485BoradcastInfo_VehicleRunningStatus vehicleRunningStatus;
            vehicleRunningStatus.setSpeed(curVehicleStatus.speed * 10);
            G3RS485BoradcastInfo_VehicleRunningStatus::VehicleStatus vehicleStatus;
            memset(&vehicleStatus,0x00, sizeof(vehicleStatus));
            /* 设置车辆状态 */
            vehicleStatus.acc = true;
            vehicleStatus.Reverse = curVehicleStatus.reverse;
            vehicleStatus.turnL = curVehicleStatus.turnL;
            vehicleStatus.turnR = curVehicleStatus.turnR;
            vehicleStatus.bigLight = curVehicleStatus.bigLight;
            vehicleStatus.wiper = curVehicleStatus.wiper;
            vehicleStatus.brake = curVehicleStatus.brake;
            vehicleStatus.sosSign = curVehicleStatus.sosSign;
            vehicleStatus.safepassSign = curVehicleStatus.safepassSign;
            vehicleRunningStatus.setCurVehicleStatus(vehicleStatus);

            g3485AlarmBroadcast.addDataItem(vehicleRunningStatus);
            /* 塞镜头状态 */
            G3RS485BoradcastInfo_CameraStatus g3Rs485BoradcastInfoCameraStatus;
            G3RS485BoradcastInfo_CameraStatus::G3RS485BoradcastCameraStatusInfo camera1StatusInfo{true, false,0};
            G3RS485BoradcastInfo_CameraStatus::G3RS485BoradcastCameraStatusInfo camera2StatusInfo{true, false,0};
            /* 看看试试有镜头被pass掉了 */
            if(G3_Configuration::getInstance().getVehicleStatusFromIo().safepass){
                camera1StatusInfo.sofePassed = G3_Configuration::getInstance().getBsdCameraBypassInfo().camera1;
                camera2StatusInfo.sofePassed = G3_Configuration::getInstance().getBsdCameraBypassInfo().camera2;
            }
            g3Rs485BoradcastInfoCameraStatus.setCamera1Status(camera1StatusInfo);
            g3Rs485BoradcastInfoCameraStatus.setCamera2Status(camera2StatusInfo);
            g3485AlarmBroadcast.addDataItem(g3Rs485BoradcastInfoCameraStatus);

            /* 整理一下识别信息 */
            detectionInfoLock.lock();
            G3RS485BoradcastInfo_BSD g3Rs485BoradcastInfoBsd;
            bool hasBSDInfo = false;
            G3RS485BoradcastInfo_ADAS g3Rs485BoradcastInfoAdas;
            bool hasADASInfo = false;
            G3RS485BoradcastInfo_ADASAlarm g3Rs485BoradcastInfoAdasAlarm;
            bool hasADASAlarmInfo = false;
            G3RS485BoradcastInfo_BSDAlarm g3Rs485BoradcastInfoBsdAlarm;
            bool hasBSDAlarmInfo = false;
            G3RS485BoradcastInfo_R151Info g3Rs485BoradcastInfoR151Info;
            bool hasR151Info = false;
            G3RS485BoradcastInfo_R159Info g3Rs485BoradcastInfoR159Info;
            bool hasR159Info = false;

            /* 遍历列表，然后根据报警决策整理信息 */
            for (std::size_t i = 0; i < curDetectionResultList.size(); i++) {
                /* 检查下是否有镜头遮挡,有的话就塞到系统错误信息里 */
                if(curDetectionResultList[i].isCameraCover){
                    switch (curDetectionResultList[i].curCameraType.cameraId) {
                        case CAMERA_ID_1:{
                            g3Rs485BoradcastInfoSystemInfo.setCameraCover1(1);
                        }
                            break;
                        case CAMERA_ID_2:{
                            g3Rs485BoradcastInfoSystemInfo.setCameraCover2(1);
                        }
                            break;
                        case CAMERA_ID_3:{
                            g3Rs485BoradcastInfoSystemInfo.setCameraCover3(1);
                        }
                            break;
                        case CAMERA_ID_4:{
                            g3Rs485BoradcastInfoSystemInfo.setCameraCover4(1);
                        }
                            break;
                    }
                }

                /* 整理下区域行人跟车的 */
                if (curDetectionResultList[i].curCameraType.adType.pedestrian_area ||
                    curDetectionResultList[i].curCameraType.adType.vehicle_area ||
                    curDetectionResultList[i].curCameraType.adType.pedestrian_SBST_Backward ||
                    curDetectionResultList[i].curCameraType.adType.vehicle_SBST_Backward) {
                    g3Rs485BoradcastInfoBsd.leftAlarmArea1Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.left_level1;
                    g3Rs485BoradcastInfoBsd.leftAlarmArea2Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.left_level2;
                    g3Rs485BoradcastInfoBsd.leftAlarmArea1Status_vehicle |= curDetectionResultList[i].bsdDetectionInfo.vehicleStatus.left_level1;
                    g3Rs485BoradcastInfoBsd.leftAlarmArea2Status_vehicle |= curDetectionResultList[i].bsdDetectionInfo.vehicleStatus.left_level2;

                    g3Rs485BoradcastInfoBsd.rightAlarmArea1Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.right_level1;
                    g3Rs485BoradcastInfoBsd.rightAlarmArea2Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.right_level2;
                    g3Rs485BoradcastInfoBsd.rightAlarmArea1Status_vehicle |= curDetectionResultList[i].bsdDetectionInfo.vehicleStatus.right_level1;
                    g3Rs485BoradcastInfoBsd.rightAlarmArea2Status_vehicle |= curDetectionResultList[i].bsdDetectionInfo.vehicleStatus.right_level2;

                    g3Rs485BoradcastInfoBsd.forwardAlarmArea1Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.forward_level1;
                    g3Rs485BoradcastInfoBsd.forwardAlarmArea2Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.forward_level2;
                    g3Rs485BoradcastInfoBsd.forwardAlarmArea1Status_vehicle |= curDetectionResultList[i].bsdDetectionInfo.vehicleStatus.forward_level1;
                    g3Rs485BoradcastInfoBsd.forwardAlarmArea2Status_vehicle |= curDetectionResultList[i].bsdDetectionInfo.vehicleStatus.forward_level2;

                    g3Rs485BoradcastInfoBsd.backwardAlarmArea1Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.backward_level1;
                    g3Rs485BoradcastInfoBsd.backwardAlarmArea2Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.backward_level2;
                    g3Rs485BoradcastInfoBsd.backwardAlarmArea1Status_vehicle |= curDetectionResultList[i].bsdDetectionInfo.vehicleStatus.backward_level1;
                    g3Rs485BoradcastInfoBsd.backwardAlarmArea2Status_vehicle |= curDetectionResultList[i].bsdDetectionInfo.vehicleStatus.backward_level2;

                    g3Rs485BoradcastInfoBsd.leftCoverStatus |= curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.left;
                    g3Rs485BoradcastInfoBsd.rightCoverStatus |= curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.right;
                    g3Rs485BoradcastInfoBsd.forwardCoverStatus |= curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.forward;
                    g3Rs485BoradcastInfoBsd.backwardCoverStatus |= curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.backward;

                    g3Rs485BoradcastInfoBsd.leftBypassStatus |= curDetectionResultList[i].bsdDetectionInfo.bypassStatus.left;
                    g3Rs485BoradcastInfoBsd.rightBypassStatus |= curDetectionResultList[i].bsdDetectionInfo.bypassStatus.right;
                    g3Rs485BoradcastInfoBsd.forwardBypassStatus |= curDetectionResultList[i].bsdDetectionInfo.bypassStatus.forward;
                    g3Rs485BoradcastInfoBsd.backwardBypassStatus |= curDetectionResultList[i].bsdDetectionInfo.bypassStatus.backward;

                    hasBSDInfo = true;
                }
                /* 整理下TTC行人跟车跟车道线跟交通标识的 */
                if (curDetectionResultList[i].curCameraType.adType.pedestrian_ttc ||
                    curDetectionResultList[i].curCameraType.adType.vehicle_ttc ||
                    curDetectionResultList[i].curCameraType.adType.laneline ||
                    curDetectionResultList[i].curCameraType.adType.trafficSign ||
                        curDetectionResultList[i].curCameraType.adType.spg_adas_vehicle) {

                    ADASDetectionInfo_alarmStatus alarmStatusTemp = g3Rs485BoradcastInfoAdas.getAlarmStatus();

                    /* 人的 */
                    if (curDetectionResultList[i].curCameraType.adType.pedestrian_ttc) {


                        if (g3Rs485BoradcastInfoAdas.getPedestrianCollisionTime() >
                            curDetectionResultList[i].adasDetectionInfo.pedestrianCollisionTime) {
                            g3Rs485BoradcastInfoAdas.setPedestrianCollisionTime(
                                    curDetectionResultList[i].adasDetectionInfo.pedestrianCollisionTime);
                            alarmStatusTemp.hasPedestrian |= curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasPedestrian;
                        }

                        if (g3Rs485BoradcastInfoAdas.getPedestrianYDistance() >
                            curDetectionResultList[i].adasDetectionInfo.pedestrianYDistance) {
                            g3Rs485BoradcastInfoAdas.setPedestrianYDistance(
                                    curDetectionResultList[i].adasDetectionInfo.pedestrianYDistance);
                            alarmStatusTemp.hasPedestrian |= curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasPedestrian;
                        }
                    }


                    /* 车的 */
                    if (curDetectionResultList[i].curCameraType.adType.vehicle_ttc || curDetectionResultList[i].curCameraType.adType.spg_adas_vehicle) {

                        if (g3Rs485BoradcastInfoAdas.getVehicleCollisionTime() >
                            curDetectionResultList[i].adasDetectionInfo.vehicleCollisionTime) {
                            g3Rs485BoradcastInfoAdas.setVehicleCollisionTime(
                                    curDetectionResultList[i].adasDetectionInfo.vehicleCollisionTime);
                            alarmStatusTemp.hasVehicleAlarm |= curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasVehicleAlarm;
                        }

                        if (g3Rs485BoradcastInfoAdas.getVehicleYDistance() >
                            curDetectionResultList[i].adasDetectionInfo.vehicleYDistance) {
                            g3Rs485BoradcastInfoAdas.setVehicleYDistance(
                                    curDetectionResultList[i].adasDetectionInfo.vehicleYDistance);
                            alarmStatusTemp.hasVehicleAlarm |= curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasVehicleAlarm;
                        }
                    }

                    /* 线的 */
                    if (curDetectionResultList[i].curCameraType.adType.laneline) {
                        if (g3Rs485BoradcastInfoAdas.getLineXDistanceLeft() >
                            curDetectionResultList[i].adasDetectionInfo.lineXDistance_left) {
                            g3Rs485BoradcastInfoAdas.setLineXDistanceLeft(
                                    curDetectionResultList[i].adasDetectionInfo.lineXDistance_left);
                            alarmStatusTemp.hasLanelineAlarm_left |= curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasLanelineAlarm_left;
                            /* 直接用离车最近的这条线的类型 */
                            alarmStatusTemp.lanelineType_left = curDetectionResultList[i].adasDetectionInfo.alarmStatus.lanelineType_left;
                        }

                        if (g3Rs485BoradcastInfoAdas.getLineXDistanceRight() >
                            curDetectionResultList[i].adasDetectionInfo.lineXDistance_right) {
                            g3Rs485BoradcastInfoAdas.setLineXDistanceRight(
                                    curDetectionResultList[i].adasDetectionInfo.lineXDistance_right);
                            alarmStatusTemp.hasLanelineAlarm_right |= curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasLanelineAlarm_right;
                            /* 直接用离车最近的这条线的类型 */
                            alarmStatusTemp.lanelineType_right = curDetectionResultList[i].adasDetectionInfo.alarmStatus.lanelineType_right;
                        }
                    }

                    /* 限速 */
                    if (curDetectionResultList[i].curCameraType.adType.trafficSign) {
                        if (g3Rs485BoradcastInfoAdas.getSpeedLimitMax() >
                            curDetectionResultList[i].adasDetectionInfo.speedLimit_max) {
                            g3Rs485BoradcastInfoAdas.setSpeedLimitMax(
                                    curDetectionResultList[i].adasDetectionInfo.speedLimit_max);
                        }

                        if (g3Rs485BoradcastInfoAdas.getSpeedLimitMin() <
                            curDetectionResultList[i].adasDetectionInfo.speedLimit_min) {
                            g3Rs485BoradcastInfoAdas.setSpeedLimitMin(
                                    curDetectionResultList[i].adasDetectionInfo.speedLimit_min);
                        }
                    }


                    g3Rs485BoradcastInfoAdas.setAlarmStatus(alarmStatusTemp);


                    hasADASInfo = true;
                }



                /* 整理下R151行人跟车的 */
                if (curDetectionResultList[i].curCameraType.adType.pedestrian_r151 ||
                    curDetectionResultList[i].curCameraType.adType.vehicle_r151) {
                    G3RS485BoradcastInfo_R151Info::R151AreaInfo area1Info = g3Rs485BoradcastInfoR151Info.getArea1Info();
                    G3RS485BoradcastInfo_R151Info::R151AreaInfo area2Info = g3Rs485BoradcastInfoR151Info.getArea2Info();
                    G3RS485BoradcastInfo_R151Info::R151AreaInfo area3Info = g3Rs485BoradcastInfoR151Info.getArea3Info();
                    G3RS485BoradcastInfo_R151Info::R151AreaInfo area4Info = g3Rs485BoradcastInfoR151Info.getArea4Info();

                    area1Info.hasPedestrian |= curDetectionResultList[i].r151BsdDetectionInfo.hasPedestrianInAlarmArea1;
                    area1Info.hasVehicle |= curDetectionResultList[i].r151BsdDetectionInfo.hasVehicleInAlarmArea1;
                    area1Info.isCameraCover |= curDetectionResultList[i].r151BsdDetectionInfo.hasCameraCoverInAlarmArea1;

                    area2Info.hasPedestrian |= curDetectionResultList[i].r151BsdDetectionInfo.hasPedestrianInAlarmArea2;
                    area2Info.hasVehicle |= curDetectionResultList[i].r151BsdDetectionInfo.hasVehicleInAlarmArea2;
                    area2Info.isCameraCover |= curDetectionResultList[i].r151BsdDetectionInfo.hasCameraCoverInAlarmArea2;

                    area3Info.hasPedestrian |= curDetectionResultList[i].r151BsdDetectionInfo.hasPedestrianInAlarmArea3;
                    area3Info.hasVehicle |= curDetectionResultList[i].r151BsdDetectionInfo.hasVehicleInAlarmArea3;
                    area3Info.isCameraCover |= curDetectionResultList[i].r151BsdDetectionInfo.hasCameraCoverInAlarmArea3;

                    area4Info.hasPedestrian |= curDetectionResultList[i].r151BsdDetectionInfo.hasPedestrianInAlarmArea4;
                    area4Info.hasVehicle |= curDetectionResultList[i].r151BsdDetectionInfo.hasVehicleInAlarmArea4;
                    area4Info.isCameraCover |= curDetectionResultList[i].r151BsdDetectionInfo.hasCameraCoverInAlarmArea4;

                    g3Rs485BoradcastInfoR151Info.setArea1Info(area1Info);
                    g3Rs485BoradcastInfoR151Info.setArea2Info(area2Info);
                    g3Rs485BoradcastInfoR151Info.setArea3Info(area3Info);
                    g3Rs485BoradcastInfoR151Info.setArea4Info(area4Info);
                    hasR151Info = true;
                }


                /* 整理下R159行人跟车的 */
                if (curDetectionResultList[i].curCameraType.adType.pedestrian_r159 ||
                    curDetectionResultList[i].curCameraType.adType.vehicle_r159) {
                    G3RS485BoradcastInfo_R159Info::R159AreaInfo area1Info = g3Rs485BoradcastInfoR159Info.getArea1Info();
                    G3RS485BoradcastInfo_R159Info::R159AreaInfo area2Info = g3Rs485BoradcastInfoR159Info.getArea2Info();
                    G3RS485BoradcastInfo_R159Info::R159AreaInfo area3Info = g3Rs485BoradcastInfoR159Info.getArea3Info();

                    area1Info.hasPedestrian |= curDetectionResultList[i].r159BsdDetectionInfo.hasPedestrianInAlarmArea1;
                    area1Info.hasVehicle |= curDetectionResultList[i].r159BsdDetectionInfo.hasVehicleInAlarmArea1;
                    area1Info.isCameraCover |= curDetectionResultList[i].r159BsdDetectionInfo.hasCameraCoverInAlarmArea1;

                    area2Info.hasPedestrian |= curDetectionResultList[i].r159BsdDetectionInfo.hasPedestrianInAlarmArea2;
                    area2Info.hasVehicle |= curDetectionResultList[i].r159BsdDetectionInfo.hasVehicleInAlarmArea2;
                    area2Info.isCameraCover |= curDetectionResultList[i].r159BsdDetectionInfo.hasCameraCoverInAlarmArea2;

                    area3Info.hasPedestrian |= curDetectionResultList[i].r159BsdDetectionInfo.hasPedestrianInAlarmArea3;
                    area3Info.isCameraCover |= curDetectionResultList[i].r159BsdDetectionInfo.hasCameraCoverInAlarmArea3;


                    g3Rs485BoradcastInfoR159Info.setArea1Info(area1Info);
                    g3Rs485BoradcastInfoR159Info.setArea2Info(area2Info);
                    g3Rs485BoradcastInfoR159Info.setArea3Info(area3Info);
                    hasR159Info = true;
                }

            }


            if (!alarmEventInfoList.empty()) {
                for (std::size_t i = 0; i < alarmEventInfoList.size(); ++i) {
                    /* 如果发送次数没到达最大次数，那么就需要接着发 */
                    if (alarmEventInfoList[i].sendSum < MAX_ALARM_RS485_SEND_TIMES) {
                        /* 整理下各大报警信息 */
                        switch (alarmEventInfoList[i].eventCode) {
                            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
                            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1: {
                                G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel1 alarmLevel1 = g3Rs485BoradcastInfoBsdAlarm.getAlarmLevel1();
                                alarmLevel1.pedestrian_left = 1;
                                g3Rs485BoradcastInfoBsdAlarm.setAlarmLevel1(alarmLevel1);
                                hasBSDAlarmInfo = true;
                            }
                                break;


                            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
                            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2: {
                                G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel2 alarmLevel2 = g3Rs485BoradcastInfoBsdAlarm.getAlarmLevel2();
                                alarmLevel2.pedestrian_left = 1;
                                g3Rs485BoradcastInfoBsdAlarm.setAlarmLevel2(alarmLevel2);
                                hasBSDAlarmInfo = true;
                            }
                                break;

                            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
                            case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1: {
                                G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel1 alarmLevel1 = g3Rs485BoradcastInfoBsdAlarm.getAlarmLevel1();
                                alarmLevel1.pedestrian_right = 1;
                                g3Rs485BoradcastInfoBsdAlarm.setAlarmLevel1(alarmLevel1);
                                hasBSDAlarmInfo = true;
                            }
                                break;

                            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
                            case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2: {
                                G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel2 alarmLevel2 = g3Rs485BoradcastInfoBsdAlarm.getAlarmLevel2();
                                alarmLevel2.pedestrian_right = 1;
                                g3Rs485BoradcastInfoBsdAlarm.setAlarmLevel2(alarmLevel2);
                                hasBSDAlarmInfo = true;
                            }
                                break;

                            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1: {
                                G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel1 alarmLevel1 = g3Rs485BoradcastInfoBsdAlarm.getAlarmLevel1();
                                alarmLevel1.pedestrian_forward = 1;
                                g3Rs485BoradcastInfoBsdAlarm.setAlarmLevel1(alarmLevel1);
                                hasBSDAlarmInfo = true;
                            }
                                break;

                            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2: {
                                G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel2 alarmLevel2 = g3Rs485BoradcastInfoBsdAlarm.getAlarmLevel2();
                                alarmLevel2.pedestrian_forward = 1;
                                g3Rs485BoradcastInfoBsdAlarm.setAlarmLevel2(alarmLevel2);
                                hasBSDAlarmInfo = true;
                            }
                                break;

                            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
                                G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel1 alarmLevel1 = g3Rs485BoradcastInfoBsdAlarm.getAlarmLevel1();
                                alarmLevel1.pedestrian_backward = 1;
                                g3Rs485BoradcastInfoBsdAlarm.setAlarmLevel1(alarmLevel1);
                                hasBSDAlarmInfo = true;
                            }
                                break;

                            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
                                G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel2 alarmLevel2 = g3Rs485BoradcastInfoBsdAlarm.getAlarmLevel2();
                                alarmLevel2.pedestrian_backward = 1;
                                g3Rs485BoradcastInfoBsdAlarm.setAlarmLevel2(alarmLevel2);
                                hasBSDAlarmInfo = true;
                            }
                                break;

                            case EVENT_ADAS_VEHICLE_TTC: {
                                if (alarmEventInfoList[i].eventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START) {
                                    G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 alarmType1 = g3Rs485BoradcastInfoAdasAlarm.getAdasAlarmType1();
                                    alarmType1.ttc = 1;
                                    g3Rs485BoradcastInfoAdasAlarm.setAdasAlarmType1(alarmType1);
                                    g3Rs485BoradcastInfoAdasAlarm.setVehicleCollisionTime(
                                            alarmEventInfoList[i].eventInfo.adasAlarmInfo.ttc * 10);
                                    hasADASAlarmInfo = true;
                                }


                            }
                                break;

                            case EVENT_ADAS_VEHICLE_HMW: {
                                if (alarmEventInfoList[i].eventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START) {
                                    G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 alarmType1 = g3Rs485BoradcastInfoAdasAlarm.getAdasAlarmType1();
                                    alarmType1.headway = 1;
                                    g3Rs485BoradcastInfoAdasAlarm.setAdasAlarmType1(alarmType1);
                                    g3Rs485BoradcastInfoAdasAlarm.setVehicleCollisionTime(
                                            alarmEventInfoList[i].eventInfo.adasAlarmInfo.headway * 10);
                                    hasADASAlarmInfo = true;
                                }
                            }
                                break;

                            case EVENT_ADAS_PEDESTRIAN_HMW: {
                                if (alarmEventInfoList[i].eventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START) {
                                    G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 alarmType1 = g3Rs485BoradcastInfoAdasAlarm.getAdasAlarmType1();
                                    alarmType1.pcw = 1;
                                    g3Rs485BoradcastInfoAdasAlarm.setAdasAlarmType1(alarmType1);
                                    g3Rs485BoradcastInfoAdasAlarm.setPedestrianCollisionTime(
                                            alarmEventInfoList[i].eventInfo.adasAlarmInfo.headway * 10);
                                    hasADASAlarmInfo = true;
                                }
                            }
                                break;

                            case EVENT_ADAS_LDW_LEFT_SOLID: {
                                if (alarmEventInfoList[i].eventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START) {
                                    G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 alarmType1 = g3Rs485BoradcastInfoAdasAlarm.getAdasAlarmType1();
                                    alarmType1.ldw_left_solid = 1;
                                    g3Rs485BoradcastInfoAdasAlarm.setAdasAlarmType1(alarmType1);
                                    hasADASAlarmInfo = true;
                                }
                            }
                                break;

                            case EVENT_ADAS_LDW_LEFT_DASH: {
                                if (alarmEventInfoList[i].eventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START) {
                                    G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 alarmType1 = g3Rs485BoradcastInfoAdasAlarm.getAdasAlarmType1();
                                    alarmType1.ldw_left_dash = 1;
                                    g3Rs485BoradcastInfoAdasAlarm.setAdasAlarmType1(alarmType1);
                                    hasADASAlarmInfo = true;
                                }
                            }
                                break;

                            case EVENT_ADAS_LDW_RIGHT_SOLID: {
                                if (alarmEventInfoList[i].eventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START) {
                                    G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 alarmType1 = g3Rs485BoradcastInfoAdasAlarm.getAdasAlarmType1();
                                    alarmType1.ldw_right_solid = 1;
                                    g3Rs485BoradcastInfoAdasAlarm.setAdasAlarmType1(alarmType1);
                                    hasADASAlarmInfo = true;
                                }
                            }
                                break;

                            case EVENT_ADAS_LDW_RIGHT_DASH: {
                                if (alarmEventInfoList[i].eventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START) {
                                    G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 alarmType1 = g3Rs485BoradcastInfoAdasAlarm.getAdasAlarmType1();
                                    alarmType1.ldw_right_dash = 1;
                                    g3Rs485BoradcastInfoAdasAlarm.setAdasAlarmType1(alarmType1);
                                    hasADASAlarmInfo = true;
                                }
                            }
                                break;

                            case EVENT_ADAS_VEHICLE_VB: {
                                    G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 alarmType1 = g3Rs485BoradcastInfoAdasAlarm.getAdasAlarmType1();
                                    alarmType1.vb = 1;
                                    g3Rs485BoradcastInfoAdasAlarm.setAdasAlarmType1(alarmType1);
                                    hasADASAlarmInfo = true;
                            }
                                break;

                            case EVENT_ADAS_VEHICLE_GO: {
                                    G3RS485BoradcastInfo_ADASAlarm::RS485ADASAlarmType1 alarmType1 = g3Rs485BoradcastInfoAdasAlarm.getAdasAlarmType1();
                                    alarmType1.go = 1;
                                    g3Rs485BoradcastInfoAdasAlarm.setAdasAlarmType1(alarmType1);
                                    hasADASAlarmInfo = true;
                            }
                                break;







                        }

                        alarmEventInfoList[i].sendSum = alarmEventInfoList[i].sendSum + 1;

                    } else { ; //not to do

                    }

                }

            }


            if (hasBSDInfo) {
                g3485AlarmBroadcast.addDataItem(g3Rs485BoradcastInfoBsd);
            }

            if (hasADASInfo) {
                g3485AlarmBroadcast.addDataItem(g3Rs485BoradcastInfoAdas);
            }

            if (hasBSDAlarmInfo) {
                g3485AlarmBroadcast.addDataItem(g3Rs485BoradcastInfoBsdAlarm);
            }

            if (hasADASAlarmInfo) {
                g3485AlarmBroadcast.addDataItem(g3Rs485BoradcastInfoAdasAlarm);
            }

            if (hasR151Info) {
                g3485AlarmBroadcast.addDataItem(g3Rs485BoradcastInfoR151Info);
            }

            if (hasR159Info) {
                g3485AlarmBroadcast.addDataItem(g3Rs485BoradcastInfoR159Info);
            }

            /* 把系统错误信息塞进去 */
            g3485AlarmBroadcast.addDataItem(g3Rs485BoradcastInfoSystemInfo);


            detectionInfoLock.unlock();

            if (!upgradeing) {

                /* 先发送识别信息和报警信息 */
                int datal = rs485MessageEncoder.generateG3RS485AlarmBroadcast(rs485Packetdata, g3485AlarmBroadcast);
                int senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, datal, 1, datal - 2,
                                                                                rs485data);


                callback->onNeedSendDataToRS485(rs485data, senddatal);
                usleep(1000 * 30);
                /* 再发送获取GPS信息的指令 */
                memset(rs485Packetdata, 0x00, sizeof(rs485Packetdata));
                memset(rs485data, 0x00, sizeof(rs485data));
                datal = rs485MessageEncoder.generateGetGPSInfo(G3RS485DataPackage::RS485_DEVICE_ID_GPS,
                                                               rs485Packetdata);
                senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, datal, 1, datal - 2,
                                                                            rs485data);
                callback->onNeedSendDataToRS485(rs485data, senddatal);
                usleep(1000 * 70);
                /* 其他指令，比如获取版本号 */
                if (!otherMsgList.empty()) {
                    for (std::size_t i = 0; i < otherMsgList.size(); i++) {
                        callback->onNeedSendDataToRS485(otherMsgList[i].msgContent, otherMsgList[i].msgLen);
                        usleep(1000 * otherMsgList[i].sleepTime);
                    }
                    otherMsgList.clear();
                }


            }else{
                /* 当前处于升级状态，那么就需要判断下是不是升级超时了 */
                int curWaitTime = (XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - startUpgradeTime);
                if(curWaitTime >= MAX_UPGRADE_WAIT_TIME){
                    /* 超时了，算升级失败，退出升级状态 */
                    G3RS485UpgradeResult upgradeResult;
                    upgradeResult.setRs485DeviceId(curUpgradePeripheralId);
                    upgradeResult.setUpgradeResult(G3RS485UpgradeResult::UPGRADE_RESULT_FAILED);
                    callback->onGetUpgradeResultFromRS485(upgradeResult);
                    upgradeing = false;
                    printf("RS485 device upgrade timeout! PeripheralId = %d \n",curUpgradePeripheralId);

                }
            }


        }
        pthread_setname_np(pthread_self(), "Finish");
    }

    void G3RS485Manager::setRS485BusData(const uint8_t *buf, const int len) {
        rs485MessageDecoder.onRecvRS485Data(buf, len);
    }

    void G3RS485Manager::setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {

        detectionInfoLock.lock();
        /* 在现有的列表里找下  如果有修改对应的值 没有就添加到列表里 */
        bool isInList = false;
        for (std::size_t i = 0; i < curDetectionResultList.size(); i++) {
            /* 先判断一下相机ID是否相同 */
            if (curDetectionResultList[i].curCameraType.cameraId == detectionResult.curCameraType.cameraId) {
                /* 再判断一下报警决策是否相同 */
                if (curDetectionResultList[i].alarmDecisionType == detectionResult.alarmDecisionType) {
                    isInList = true;
                    /* 由于结构体里面没有裸指针，故直接用=就好了 */
                    curDetectionResultList[i] = detectionResult;
                }

            }
        }

        if (!isInList) {
            curDetectionResultList.push_back(detectionResult);
        }
        detectionInfoLock.unlock();
    }

    void G3RS485Manager::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
        /* 看看是否发送到485总线 */
        switch (eventCode) {
            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1: {
                if (!G3_Configuration::getInstance().getBsdAlarmLevel1SendtoRs485Enable()) {
                    return;
                }
            }
                break;

            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2: {
                if (!G3_Configuration::getInstance().getBsdAlarmLevel2SendtoRs485Enable()) {
                    return;
                }
            }
                break;

        }
        /*  */
        if (!alarmEventInfoList.empty()) {
            /* 先找下这个事件是不是已经有过，有过就复用，没有就新加 */
            int sameEventIndex = -1;
            for (std::size_t i = 0; i < alarmEventInfoList.size(); i++) {
                if (alarmEventInfoList[i].eventCode == eventCode) {
                    sameEventIndex = i;
                    break;
                }
            }
            if (sameEventIndex != -1) {
                alarmEventInfoList[sameEventIndex].eventCode = eventCode;
                memcpy(&alarmEventInfoList[sameEventIndex].eventInfo, &alarmEventInfo,
                       sizeof(alarmEventInfoList[sameEventIndex].eventInfo));
                alarmEventInfoList[sameEventIndex].sendSum = 0;

            } else {
                G3RS485Manager::RS485AlarmEventInfo alarmEventInfoTemp;
                alarmEventInfoTemp.eventCode = eventCode;
                memcpy(&alarmEventInfoTemp.eventInfo, &alarmEventInfo, sizeof(alarmEventInfoTemp.eventInfo));
                alarmEventInfoTemp.sendSum = 0;
                alarmEventInfoList.push_back(alarmEventInfoTemp);
            }

        } else {
            G3RS485Manager::RS485AlarmEventInfo alarmEventInfoTemp;
            alarmEventInfoTemp.eventCode = eventCode;
            memcpy(&alarmEventInfoTemp.eventInfo, &alarmEventInfo, sizeof(alarmEventInfoTemp.eventInfo));
            alarmEventInfoTemp.sendSum = 0;
            alarmEventInfoList.push_back(alarmEventInfoTemp);
        }



//
//        uint8_t rs485Packetdata[512];
//        memset(rs485Packetdata, 0x00, sizeof(rs485Packetdata));
//
//        uint8_t rs485data[1024];
//        memset(rs485data, 0x00, sizeof(rs485data));
//
//        G3RS485AlarmEvent rs485AlarmEvent;
//
//        G3RS485AlarmEvent_BSDEVent bsdEvent;
//        G3RS485AlarmEvent_ADASEVent adasEvent;
//
//
//        switch (eventCode) {
//            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
//            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1: {
//                bsdEvent.setEventStatus(0x00);
//                bsdEvent.setEventType(G3RS485AlarmEvent_BSDEVent::EVENT_TYPE_LEFT_PEDESTRIAN);
//                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
//                bsdEvent.setEventLevel(G3RS485AlarmEvent_BSDEVent::EVENT_LEVEL_LEVEL1);
//                rs485AlarmEvent.addBSDAlarmEventInfo(bsdEvent);
//            }
//                break;
//
//
//            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
//            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2: {
//                bsdEvent.setEventStatus(0x00);
//                bsdEvent.setEventType(G3RS485AlarmEvent_BSDEVent::EVENT_TYPE_LEFT_PEDESTRIAN);
//                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
//                bsdEvent.setEventLevel(G3RS485AlarmEvent_BSDEVent::EVENT_LEVEL_LEVEL2);
//                rs485AlarmEvent.addBSDAlarmEventInfo(bsdEvent);
//            }
//                break;
//
//            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
//            case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1: {
//                bsdEvent.setEventStatus(0x00);
//                bsdEvent.setEventType(G3RS485AlarmEvent_BSDEVent::EVENT_TYPE_RIGHT_PEDESTRIAN);
//                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
//                bsdEvent.setEventLevel(G3RS485AlarmEvent_BSDEVent::EVENT_LEVEL_LEVEL1);
//                rs485AlarmEvent.addBSDAlarmEventInfo(bsdEvent);
//            }
//                break;
//
//            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
//            case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2: {
//                bsdEvent.setEventStatus(0x00);
//                bsdEvent.setEventType(G3RS485AlarmEvent_BSDEVent::EVENT_TYPE_RIGHT_PEDESTRIAN);
//                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
//                bsdEvent.setEventLevel(G3RS485AlarmEvent_BSDEVent::EVENT_LEVEL_LEVEL2);
//                rs485AlarmEvent.addBSDAlarmEventInfo(bsdEvent);
//            }
//                break;
//
//            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1: {
//                bsdEvent.setEventStatus(0x00);
//                bsdEvent.setEventType(G3RS485AlarmEvent_BSDEVent::EVENT_TYPE_FORWARD_PEDESTRIAN);
//                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
//                bsdEvent.setEventLevel(G3RS485AlarmEvent_BSDEVent::EVENT_LEVEL_LEVEL1);
//                rs485AlarmEvent.addBSDAlarmEventInfo(bsdEvent);
//            }
//                break;
//
//            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2: {
//                bsdEvent.setEventStatus(0x00);
//                bsdEvent.setEventType(G3RS485AlarmEvent_BSDEVent::EVENT_TYPE_FORWARD_PEDESTRIAN);
//                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
//                bsdEvent.setEventLevel(G3RS485AlarmEvent_BSDEVent::EVENT_LEVEL_LEVEL2);
//                rs485AlarmEvent.addBSDAlarmEventInfo(bsdEvent);
//            }
//                break;
//
//            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
//                bsdEvent.setEventStatus(0x00);
//                bsdEvent.setEventType(G3RS485AlarmEvent_BSDEVent::EVENT_TYPE_BACKWARD_PEDESTRIAN);
//                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
//                bsdEvent.setEventLevel(G3RS485AlarmEvent_BSDEVent::EVENT_LEVEL_LEVEL1);
//                rs485AlarmEvent.addBSDAlarmEventInfo(bsdEvent);
//            }
//                break;
//
//            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
//                bsdEvent.setEventStatus(0x00);
//                bsdEvent.setEventType(G3RS485AlarmEvent_BSDEVent::EVENT_TYPE_BACKWARD_PEDESTRIAN);
//                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
//                bsdEvent.setEventLevel(G3RS485AlarmEvent_BSDEVent::EVENT_LEVEL_LEVEL2);
//                rs485AlarmEvent.addBSDAlarmEventInfo(bsdEvent);
//            }
//                break;
//
//            case EVENT_ADAS_VEHICLE_TTC: {
//                switch (alarmEventInfo.adasAlarmInfo.eventStatus) {
//                    case EVENTSTATUS_UNAVAILABLE: {
//                        adasEvent.setEventStatus(G3RS485AlarmEvent_ADASEVent::EVENTSTATUS_NONE);
//                    }
//                        break;
//                    case EVENTSTATUS_START: {
//                        adasEvent.setEventStatus(G3RS485AlarmEvent_ADASEVent::EVENTSTATUS_START);
//                    }
//                        break;
//                    case EVENTSTATUS_STOP: {
//                        adasEvent.setEventStatus(G3RS485AlarmEvent_ADASEVent::EVENTSTATUS_STOP);
//                    }
//                        break;
//
//                }
//                adasEvent.setEventType(G3RS485AlarmEvent_ADASEVent::EVENT_TYPE_TTC);
//                adasEvent.setHeadway(alarmEventInfo.adasAlarmInfo.ttc);
//                adasEvent.setLdwType(0xFF);
//                adasEvent.setRoadSignsType(0xFF);
//                rs485AlarmEvent.addADASAlarmEventInfo(adasEvent);
//            }
//                break;
//
//            case EVENT_ADAS_VEHICLE_HMW: {
//                switch (alarmEventInfo.adasAlarmInfo.eventStatus) {
//                    case EVENTSTATUS_UNAVAILABLE: {
//                        adasEvent.setEventStatus(G3RS485AlarmEvent_ADASEVent::EVENTSTATUS_NONE);
//                    }
//                        break;
//                    case EVENTSTATUS_START: {
//                        adasEvent.setEventStatus(G3RS485AlarmEvent_ADASEVent::EVENTSTATUS_START);
//                    }
//                        break;
//                    case EVENTSTATUS_STOP: {
//                        adasEvent.setEventStatus(G3RS485AlarmEvent_ADASEVent::EVENTSTATUS_STOP);
//                    }
//                        break;
//
//                }
//                adasEvent.setEventType(G3RS485AlarmEvent_ADASEVent::EVENT_TYPE_HMW);
//                adasEvent.setHeadway(alarmEventInfo.adasAlarmInfo.headway);
//                adasEvent.setLdwType(0xFF);
//                adasEvent.setRoadSignsType(0xFF);
//                rs485AlarmEvent.addADASAlarmEventInfo(adasEvent);
//            }
//                break;
//
//            case EVENT_ADAS_PEDESTRIAN_HMW: {
//                switch (alarmEventInfo.adasAlarmInfo.eventStatus) {
//                    case EVENTSTATUS_UNAVAILABLE: {
//                        adasEvent.setEventStatus(G3RS485AlarmEvent_ADASEVent::EVENTSTATUS_NONE);
//                    }
//                        break;
//                    case EVENTSTATUS_START: {
//                        adasEvent.setEventStatus(G3RS485AlarmEvent_ADASEVent::EVENTSTATUS_START);
//                    }
//                        break;
//                    case EVENTSTATUS_STOP: {
//                        adasEvent.setEventStatus(G3RS485AlarmEvent_ADASEVent::EVENTSTATUS_STOP);
//                    }
//                        break;
//
//                }
//                adasEvent.setEventType(G3RS485AlarmEvent_ADASEVent::EVENT_TYPE_PDW);
//                adasEvent.setHeadway(alarmEventInfo.adasAlarmInfo.headway);
//                adasEvent.setLdwType(0xFF);
//                adasEvent.setRoadSignsType(0xFF);
//                rs485AlarmEvent.addADASAlarmEventInfo(adasEvent);
//            }
//                break;
//
//
//        }
//
//
//        int datal = rs485MessageEncoder.generateG3RS485AlarmEvent(rs485Packetdata, rs485AlarmEvent);
//        int senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, datal, 1, datal - 2,
//                                                                        rs485data);
//
//        callback->onNeedSendDataToRS485(rs485data, senddatal);
//
//
//    printf("send to rs485 %d bytes,content:\n",datal);
//    for(int i = 0; i < senddatal; i ++){
//        printf("%02x ",rs485Packetdata[i]);
//    }
//    printf("\n");
    }

    void G3RS485Manager::setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
        //TODO  把ADAS的信息发出去  如果是新加坡的  还需要转多一份BSD的出来
        detectionInfoLock.lock();
        /* 在现有的列表里找下  如果有修改对应的值 没有就添加到列表里 */
        bool isInList = false;
        for (std::size_t i = 0; i < curDetectionResultList.size(); i++) {
            if (curDetectionResultList[i].curCameraType.cameraId == detectionResult.curCameraType.cameraId &&
                curDetectionResultList[i].alarmDecisionType == detectionResult.alarmDecisionType) {
                isInList = true;
                /* 根据报警决策类型整理一下 */
                switch (curDetectionResultList[i].alarmDecisionType) {
                    case ALARM_DECISION_TYPE_PEDSTRIAN_TTC: {
                        curDetectionResultList[i].adasDetectionInfo.pedestrianYDistance = detectionResult.adasDetectionInfo.pedestrianYDistance;
                        curDetectionResultList[i].adasDetectionInfo.pedestrianCollisionTime = detectionResult.adasDetectionInfo.pedestrianCollisionTime;
                        curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasPedestrian = detectionResult.adasDetectionInfo.alarmStatus.hasPedestrian;
                    }
                        break;
                    case ALARM_DECISION_TYPE_VEHICLE_TTC:
                    case ALARM_DECISION_TYPE_SPG_ADAS_VEHICLE: {
                        curDetectionResultList[i].adasDetectionInfo.vehicleYDistance = detectionResult.adasDetectionInfo.vehicleYDistance;
                        curDetectionResultList[i].adasDetectionInfo.vehicleCollisionTime = detectionResult.adasDetectionInfo.vehicleCollisionTime;
                        curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasVehicleAlarm = detectionResult.adasDetectionInfo.alarmStatus.hasVehicleAlarm;
                    }
                        break;
                    case ALARM_DECISION_TYPE_LANELINE: {
                        curDetectionResultList[i].adasDetectionInfo.lineXDistance_left = detectionResult.adasDetectionInfo.lineXDistance_left;
                        curDetectionResultList[i].adasDetectionInfo.lineXDistance_right = detectionResult.adasDetectionInfo.lineXDistance_right;
                        curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasLanelineAlarm_left = detectionResult.adasDetectionInfo.alarmStatus.hasLanelineAlarm_left;
                        curDetectionResultList[i].adasDetectionInfo.alarmStatus.hasLanelineAlarm_right = detectionResult.adasDetectionInfo.alarmStatus.hasLanelineAlarm_right;
                    }
                        break;
                    case ALARM_DECISION_TYPE_TRAFFIC_SIGN: {
                        curDetectionResultList[i].adasDetectionInfo.speedLimit_max = detectionResult.adasDetectionInfo.speedLimit_max;
                        curDetectionResultList[i].adasDetectionInfo.speedLimit_min = detectionResult.adasDetectionInfo.speedLimit_min;
                    }
                        break;
                    default:{
                        ; // not to do
                    }
                        break;
                }


            }
        }
        if (!isInList) {
            curDetectionResultList.push_back(detectionResult);
        }
        detectionInfoLock.unlock();


    }

    void G3RS485Manager::setTurnSignal(const bool trunL, const bool trunR) {
        curVehicleStatus.turnL |= trunL;
        curVehicleStatus.turnR |= trunR;
    }

    void G3RS485Manager::setSpeed(const float speed, const int baseOf) {
        if(speed >= 0){
            curVehicleStatus.speed = speed;
        }

    }

    void G3RS485Manager::onGetGPSInfoFromRS485(G3RS485GPSInfo &gpsInfo) {
        callback->onGetGPSInfoFromRS485(gpsInfo);
    }

    int G3RS485Manager::setUpgradeRS485(const int peripheralId, const std::string &upgradeFilePath) {
        int ret = -1;
        if (XuFile::getInstance().fileExists(upgradeFilePath.c_str())) {
            FILE *fileFd = fopen(upgradeFilePath.c_str(), "rb");


            /* 看看是不是要升级L4，且L4的协议版本号大于05.0 */
            if(peripheralId == G3RS485DataPackage::RS485_DEVICE_ID_L4 && G3_Configuration::getInstance().getL4ProtocoVersion() >= 5.0){
                /* 是L4，那么走特殊的升级流程 */
                if(fileFd){
                    /* 读出真正的数据 */
                    upgradeFileDataLen = fread(upgradeFileData, 1, 1024 * 1024 * 2, fileFd);
                    fclose(fileFd);
                    if (upgradeFileDataLen > 0) {
                        ret = 0;
                        startUpgradeTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                        curUpgradePeripheralId = G3RS485DataPackage::RS485_DEVICE_ID_L4;
                        /* 发出去 */
                        upgradeing = true;

                        G3RS485StartUpgradeL4 startUpgradeL4;
                        /* 程序版本号	char[4]	ASCII码 */
                        startUpgradeL4.setVersionCode(upgradeFileData+18);
                        /* 图片文件编号	uint16_t */
                        startUpgradeL4.setImgFileNum(CodeUtils::getInstance().BbToUint16(upgradeFileData+2));
                        /* 图片文件版本号	uint16_t */
                        startUpgradeL4.setImgFileVersionCode(CodeUtils::getInstance().BbToUint16(upgradeFileData+4));
                        /* 文件总长度	uint32_t	以byte为单位 */
                        startUpgradeL4.setFileLen(upgradeFileDataLen);
                        /* 文件总CRC16值	uint16_t */
                        startUpgradeL4.setFileCrc(CodeUtils::getInstance().generateCrc16(upgradeFileData,upgradeFileDataLen));
                        /* 需修改的波特率，不修改则填0；目前支持修改到57600；115200；*/
                        startUpgradeL4.setDecBaudrate(38400);
                        uint8_t rs485Packetdata[1024];
                        memset(rs485Packetdata, 0x00, sizeof(rs485Packetdata));

                        uint8_t rs485data[2048];
                        memset(rs485data, 0x00, sizeof(rs485data));

                        int dataLen = rs485MessageEncoder.generateStartUpgradeL4(rs485Packetdata, startUpgradeL4);
                        int senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, dataLen, 1, dataLen - 2,
                                                                                        rs485data);
                        callback->onNeedSendDataToRS485(rs485data, senddatal);
                        printf("send start L4 upgrade! \n");




                    }




                }

            }else{
               /* 不是升级L4，那么就走标准的升级流程 */
                if (fileFd) {
                    /* 需要去掉前面30个字节  */
                    fseek(fileFd, 30, SEEK_SET);
                    /* 读出真正的数据 */
                    upgradeFileDataLen = fread(upgradeFileData, 1, 1024 * 1024 * 2, fileFd);
                    fclose(fileFd);
                    if (upgradeFileDataLen > 0) {
                        /* 发送开始升级的命令 */
                        startUpgrade(peripheralId);
                        ret = 0;
                    }
                }
            }
        }
        return ret;
    }

    void G3RS485Manager::startUpgrade(const int peripheralId) {
        startUpgradeTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
        curUpgradePeripheralId = peripheralId;
        upgradeing = true;
        uint8_t rs485Packetdata[1024];
        memset(rs485Packetdata, 0x00, sizeof(rs485Packetdata));

        uint8_t rs485data[2048];
        memset(rs485data, 0x00, sizeof(rs485data));


        G3RS485StartUpgrade startPeripheralUpgrade;
        startPeripheralUpgrade.setPeripheralId(peripheralId);

        int dataLen = rs485MessageEncoder.generateStartUpgrade(rs485Packetdata, startPeripheralUpgrade);
        int senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, dataLen, 1, dataLen - 2,
                                                                        rs485data);
        callback->onNeedSendDataToRS485(rs485data, senddatal);

//        printf("startUpgrade   send to G3 RS485 bus %d bytes,content:\n", senddatal);
//        for (int i = 0; i < senddatal; i++) {
//            printf("%02x ", rs485data[i]);
//        }
//        printf("\n");
    }

    void G3RS485Manager::onGetGetUpgradeFileDataFromRS485(G3RS485GetUpgradeFileData &getUpgradeFileData) {
        printf("onGetGetUpgradeFileDataFromRS485 upgradeFileDataLen=%d offset=%d  \n", upgradeFileDataLen,getUpgradeFileData.getOffset());
        startUpgradeTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
        /* 获取升级文件了  那么就给发送出去 */
        if (upgradeFileDataLen > 0) {
            G3RS485RespondUpgradeFileData respondUpgradeFileData;
            respondUpgradeFileData.setPeripheralId(getUpgradeFileData.getPeripheralId());
            respondUpgradeFileData.setOffset(getUpgradeFileData.getOffset());

            int needSendFileDataLen = upgradeFileDataLen - getUpgradeFileData.getOffset();
            if (needSendFileDataLen >= 252) {
                /* 剩下的数据够252  直接读252 */
                needSendFileDataLen = 252;
                respondUpgradeFileData.setFileData(upgradeFileData + getUpgradeFileData.getOffset(),needSendFileDataLen);
            } else if (needSendFileDataLen <= 0) {
                /* 没剩下数据了  直接发送和校验 */
                needSendFileDataLen = 1;
                uint64_t sumCheck = 0;
                for (int i = 0; i < upgradeFileDataLen; i++) {
                    sumCheck += upgradeFileData[i];
                }


                respondUpgradeFileData.setContentLen(5 + 1);
                respondUpgradeFileData.setOffset(0xFFFFFFFF);
                uint8_t sumCheckCode[1] = {static_cast<uint8_t>((sumCheck & 0xFF))};
                respondUpgradeFileData.setFileData(sumCheckCode, sizeof(sumCheckCode));


            } else {
                /* 剩下的数据 <252  && >0   就发实际剩下的  */
                respondUpgradeFileData.setContentLen(5 + needSendFileDataLen);
                respondUpgradeFileData.setFileData(upgradeFileData + getUpgradeFileData.getOffset(),
                                                   needSendFileDataLen);
            }
            int dataLen = rs485MessageEncoder.generateRespondUpgradeFileData(rs485UpgradePacketdata,
                                                                             respondUpgradeFileData);
            int senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(rs485UpgradePacketdata, dataLen, 1,
                                                                            dataLen - 2,
                                                                            rs485Upgradedata);


//            printf("send to RS485 %d bytes for send upgrade data ,content:\n", senddatal);
//            for (int i = 0; i < dataLen; i++) {
//                printf("%02x ", rs485UpgradePacketdata[i]);
//            }
//            printf("\n");
            callback->onNeedSendDataToRS485(rs485Upgradedata, senddatal);

        } else {
            printf("upgradeFileDataLen < 0   upgradeFileDataLen=%d \n", upgradeFileDataLen);

        }
    }

    void G3RS485Manager::onGetUpgradeResultFromRS485(G3RS485UpgradeResult &upgradeResult) {
        printf("G3RS485Manager::onGetUpgradeResultFromRS485  Rs485DeviceId=%d  result=%d  \n",
               upgradeResult.getRs485DeviceId(), upgradeResult.getUpgradeResult());
        callback->onGetUpgradeResultFromRS485(upgradeResult);
        /* 当收到结果后  不论成功失败  直接退出更新 */
        upgradeing = false;
        getRS485PeripheralVersion(upgradeResult.getPeripheralId());
    }

    void G3RS485Manager::onGetPeripheralVersion(const uint8_t peripheral, const char *softwarVersion,
                                                const char *protocoVersion) {
        printf("****************G3RS485Manager::onGetPeripheralVersion   peripheralId:%d  softwarVersion:%s   protocoVersion:%s \n",peripheral,softwarVersion,protocoVersion);
        callback->onGetPeripheralVersion(peripheral, softwarVersion, protocoVersion);
    }

    void G3RS485Manager::onGetSetDispalyParamsResultFromRS485(G3RS485SetDispalyParamsResult &setDispalyParamsResult) {
        callback->onGetSetDispalyParamsResultFromRS485(setDispalyParamsResult);
    }

    int G3RS485Manager::setDispalyParams(const int displayMode, const int iconMode) {
        uint8_t rs485Packetdata[50] = {0x00};
        uint8_t rs485data[100] = {0x00};
        G3RS485SetDispalyParams dispalyParams;
        dispalyParams.setDisplayMode(displayMode);
        dispalyParams.setIconMode(iconMode);
        int dataLen = rs485MessageEncoder.generateSetDispalyParams(rs485Packetdata, dispalyParams);
        int senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, dataLen, 1, dataLen - 2,
                                                                        rs485data);

        RS485OtherMsgInfo temp;
        memcpy(temp.msgContent, rs485data, senddatal);
        temp.msgLen = senddatal;
        temp.sleepTime = 20;
        otherMsgList.push_back(temp);

//        callback->onNeedSendDataToRS485(rs485data, senddatal);
        return 0;
    }

    void G3RS485Manager::getRS485PeripheralVersion(const int peripheralId) {
        uint8_t rs485Packetdata[50] = {0x00};
        uint8_t rs485data[100] = {0x00};
        int dataLen = rs485MessageEncoder.generateGetPeripheralVersion(peripheralId,
                                                                       rs485Packetdata);
        int senddataLen = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, dataLen, 1,
                                                                          dataLen - 2,
                                                                          rs485data);
        RS485OtherMsgInfo temp;
        memcpy(temp.msgContent, rs485data, senddataLen);
        temp.msgLen = senddataLen;
        temp.sleepTime = 20;
        otherMsgList.push_back(temp);

//        callback->onNeedSendDataToRS485(rs485data, senddataLen);

    }

    void G3RS485Manager::setVehicleRealtimeStatus(Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
         /* 先特殊处理一下车速 */
        if(vehicleRealtimeStatus.speed < 0){
            vehicleRealtimeStatus.speed = curVehicleStatus.speed;
        }
        curVehicleStatus = vehicleRealtimeStatus;
    }

    void G3RS485Manager::setDetectInfo_CameraStatus(G3DetectionResult &detectionResult) {
        detectionInfoLock.lock();
        /* 在现有的列表里找下  如果有修改对应的值 没有就添加到列表里 */
        bool isInList = false;
        for (std::size_t i = 0; i < curDetectionResultList.size(); i++) {
            /* 先判断一下相机ID是否相同 */
            if (curDetectionResultList[i].curCameraType.cameraId == detectionResult.curCameraType.cameraId) {
                /* 再判断一下报警决策是否相同 */
                if (curDetectionResultList[i].alarmDecisionType == detectionResult.alarmDecisionType) {
                    isInList = true;
                    /* 由于结构体里面没有裸指针，故直接用=就好了 */
                    curDetectionResultList[i] = detectionResult;
                }

            }
        }

        if (!isInList) {
            curDetectionResultList.push_back(detectionResult);
        }
        detectionInfoLock.unlock();
    }

    void G3RS485Manager::onGetStartUpgradeL4ResultFromRS485(G3RS485StartUpgradeL4Result &startUpgradeL4Result) {
        printf("G3RS485Manager::onGetStartUpgradeL4ResultFromRS485  result=%d  baudrate=%d \n",startUpgradeL4Result.getResult(),startUpgradeL4Result.getDecBaudrate());
        /* 先检查下结果 */
        if(startUpgradeL4Result.getResult() == G3RS485StartUpgradeL4Result::STARTUPGRADEL4RESULT_SUCCESS){
            /* 开始升级成功，那么就切换一下波特率，准备接收请求升级数据的命令 */
            callback->onGetChangeRS485Baudrate(startUpgradeL4Result.getDecBaudrate());
        }else{
            /* 失败，直接退出升级 */
            G3RS485UpgradeResult upgradeResult;
            upgradeResult.setRs485DeviceId(G3RS485DataPackage::RS485_DEVICE_ID_L4);
            upgradeResult.setUpgradeResult(G3RS485UpgradeResult::UPGRADE_RESULT_FAILED);
            callback->onGetUpgradeResultFromRS485(upgradeResult);
            upgradeing = false;
        }



    }

    void G3RS485Manager::onGetGetL4UpgradeResultFromRS485(G3RS485UpgradeL4Result &upgradeL4Result) {
        printf("G3RS485Manager::onGetGetL4UpgradeResultFromRS485   result=%d  \n",upgradeL4Result.getResult());

        int result = 0;
        /* 先检查下结果 */
        if(upgradeL4Result.getResult() == G3RS485UpgradeL4Result::UPGRADE_L4_RESULT_SUCCESS){
            /* 结果设置为成功 */
            result = G3RS485UpgradeResult::UPGRADE_RESULT_SUCCESS;
        }else{
            /* 结果设置为失败 */
            result = G3RS485UpgradeResult::UPGRADE_RESULT_FAILED;
        }
        /* 回复一下L4需要的指令 */
        uint8_t rs485Packetdata[50] = {0x00};
        uint8_t rs485data[100] = {0x00};
        G3RS485ResponeUpgradeL4Result responeUpgradeL4Result;
        int dataLen = rs485MessageEncoder.generateRespondL4UpgradeResult(rs485Packetdata, responeUpgradeL4Result);
        int senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(rs485Packetdata, dataLen, 1, dataLen - 2,
                                                                        rs485data);
        callback->onNeedSendDataToRS485(rs485data, senddatal);
        /* 把结果报上去给客户端 */
        G3RS485UpgradeResult upgradeResult;
        upgradeResult.setRs485DeviceId(G3RS485DataPackage::RS485_DEVICE_ID_L4);
        upgradeResult.setUpgradeResult(result);
        callback->onGetUpgradeResultFromRS485(upgradeResult);
        /* 改变波特率 */
        callback->onGetChangeRS485Baudrate(38400);
        /* 退出升级状态 */
        upgradeing = false;

    }

    void G3RS485Manager::onGetGetL4UpgradeFileDataFromRS485(G3RS485GetL4UpgradeFileData &getlL4UpgradeFileData) {
        printf("G3RS485Manager::onGetGetL4UpgradeFileDataFromRS485   offset=%d  dateLen=%d \n",getlL4UpgradeFileData.getOffset(),getlL4UpgradeFileData.getDataLen());
        startUpgradeTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
        /* 获取升级文件了  那么就给发送出去 */
        if (upgradeFileDataLen > 0) {
            G3RS485RespondL4UpgradeFileData respondL4UpgradeFileData;
            /* 偏移量 */
            respondL4UpgradeFileData.setOffset(getlL4UpgradeFileData.getOffset());

            int needSendFileDataLen = upgradeFileDataLen - getlL4UpgradeFileData.getOffset();
            if (needSendFileDataLen >= getlL4UpgradeFileData.getDataLen()) {
                /* 剩下的数据够需要的长度  直接读需要的长度 */
                needSendFileDataLen = getlL4UpgradeFileData.getDataLen();
                respondL4UpgradeFileData.setFileData(upgradeFileData + getlL4UpgradeFileData.getOffset());
                respondL4UpgradeFileData.setDataLen(needSendFileDataLen);
            } else {
                /* 剩下的数据不够需要的长度了   就发实际剩下的  */
                respondL4UpgradeFileData.setFileData(upgradeFileData + getlL4UpgradeFileData.getOffset());
                respondL4UpgradeFileData.setDataLen(needSendFileDataLen);
            }
            int dataLen = rs485MessageEncoder.generateRespondL4UpgradeFileData(rs485L4UpgradePacketdata,respondL4UpgradeFileData);
            int senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(rs485L4UpgradePacketdata, dataLen, 1,
                                                                            dataLen - 2,
                                                                            rs485L4Upgradedata);
            callback->onNeedSendDataToRS485(rs485L4Upgradedata, senddatal);
        } else {
            printf("onGetGetL4UpgradeFileDataFromRS485  upgradeFileDataLen < 0   upgradeFileDataLen=%d \n", upgradeFileDataLen);
        }

    }

}


