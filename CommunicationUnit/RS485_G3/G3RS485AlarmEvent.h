//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/28.
//

#ifndef VIS_G3_SOFTWARE_G3RS485ALARMEVENT_H
#define VIS_G3_SOFTWARE_G3RS485ALARMEVENT_H

#include "G3RS485DataPackage.h"
#include "G3RS485BoradcastInfo.h"
#include "vector"
#include "G3RS485AlarmEvent_ADASEVent.h"
#include "G3RS485AlarmEvent_BSDEVent.h"

class G3RS485AlarmEvent : public G3RS485DataPackage {
public:
    G3RS485AlarmEvent();

    ~G3RS485AlarmEvent();

    int addBSDAlarmEventInfo(G3RS485AlarmEvent_BSDEVent &bsdeVent);

    int addADASAlarmEventInfo(G3RS485AlarmEvent_ADASEVent &adaseVent);

    int toCode(uint8_t *buf) override;

private:


    /* 识别信息项总数 */
    uint8_t infoListSize = 0;
    /* BSD报警数据项的数组 */
    std::vector<G3RS485AlarmEvent_BSDEVent> dataItemList_BSD;
    /* ADAS报警数据项的数组 */
    std::vector<G3RS485AlarmEvent_ADASEVent> dataItemList_ADAS;


};


#endif //VIS_G3_SOFTWARE_G3RS485ALARMEVENT_H
