//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/8.
//

#ifndef VIS_G3_SOFTWARE_G3RS485RESPONDPERIPHERALVERSION_H
#define VIS_G3_SOFTWARE_G3RS485RESPONDPERIPHERALVERSION_H

#include "G3RS485DataPackage.h"

class G3RS485RespondPeripheralVersion : public G3RS485DataPackage {
public:
    G3RS485RespondPeripheralVersion();

    int toCode(uint8_t *buf) override;

    int decode(uint8_t *buf, int len) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

    const char *getSoftwareVersion() const;

    const char *getProtocolVersion() const;


private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;
    /* 软件版本号 */
    char softwareVersion[4] = {0x00, 0x00, 0x00, 0x00};
    /* 通信协议版本号 */
    char protocolVersion[4] = {0x00, 0x00, 0x00, 0x00};


};


#endif //VIS_G3_SOFTWARE_G3RS485RESPONDPERIPHERALVERSION_H
