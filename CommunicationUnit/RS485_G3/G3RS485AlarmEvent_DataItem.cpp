//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/28.
//

#include "G3RS485AlarmEvent_DataItem.h"

G3RS485AlarmEvent_DataItem::G3RS485AlarmEvent_DataItem() {

}

G3RS485AlarmEvent_DataItem::~G3RS485AlarmEvent_DataItem() {

}

int G3RS485AlarmEvent_DataItem::toCode(uint8_t *buf) {
    return 0;
}

uint16_t G3RS485AlarmEvent_DataItem::getDataId() const {
    return dataId;
}

void G3RS485AlarmEvent_DataItem::setDataId(uint16_t dataId) {
    G3RS485AlarmEvent_DataItem::dataId = dataId;
}

uint32_t G3RS485AlarmEvent_DataItem::getDataLen() const {
    return dataLen;
}

void G3RS485AlarmEvent_DataItem::setDataLen(uint32_t dataLen) {
    G3RS485AlarmEvent_DataItem::dataLen = dataLen;
}
