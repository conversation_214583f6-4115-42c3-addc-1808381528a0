//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/18.
//

#include "G3RS485RespondUpgradeFileData.h"

G3RS485RespondUpgradeFileData::G3RS485RespondUpgradeFileData() {
    setMsgId(RS485_MSGID_RESPOND_UPGRADE_FILE_DATA);
    /* 设置内容长度 */
    setContentLen(257);
}

uint8_t G3RS485RespondUpgradeFileData::getPeripheralId() const {
    return peripheralId;
}

void G3RS485RespondUpgradeFileData::setPeripheralId(uint8_t peripheralId) {
    G3RS485RespondUpgradeFileData::peripheralId = peripheralId;
}

uint32_t G3RS485RespondUpgradeFileData::getOffset() const {
    return offset;
}

void G3RS485RespondUpgradeFileData::setOffset(uint32_t offset) {
    G3RS485RespondUpgradeFileData::offset = offset;
}

void G3RS485RespondUpgradeFileData::setFileData(const uint8_t *data, const int len) {
//    int needFillLen = ONE_FILE_DATA_LEN - len;
    memcpy(fileData, data, len);
//    if (needFillLen > 0) {
//        memset(fileData + len, 0xFF, needFillLen);
//    }
}

int G3RS485RespondUpgradeFileData::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 7;   /* 第8位就是正式内容了 */

    /* 塞入外设编号 */
    buf[index] = peripheralId;
    index++;
    /* 塞入偏移地址编号 */
    CodeUtils::getInstance().int32ToBb(offset, buf + index);
    index += 4;
    /* 塞入文件数据 */
    memcpy(buf + index, fileData, ONE_FILE_DATA_LEN);
    index += ONE_FILE_DATA_LEN;
    /* 加上头尾那些东西 */
    ret = G3RS485DataPackage::toCode(buf);
    return ret;
}

int G3RS485RespondUpgradeFileData::decode(uint8_t *buf, int len) {
    /* 直接从数据内容开始取 */
    int index = 7;
    /* 取外设ID */
    setPeripheralId(buf[index]);
    index++;
    /* 取偏移地址 */
    setOffset(CodeUtils::getInstance().BbToint32(buf + index));
    index += 4;
    /* 取入文件数据 */
    memcpy(fileData, buf + index, ONE_FILE_DATA_LEN);
    index += ONE_FILE_DATA_LEN;
    /* 去数据内容之外的东西 */
    return G3RS485DataPackage::decode(buf, len);
}
