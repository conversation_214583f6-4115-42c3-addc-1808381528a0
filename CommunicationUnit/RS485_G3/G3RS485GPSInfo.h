//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/11.
//

#ifndef VIS_G3_SOFTWARE_G3RS485GPSINFO_H
#define VIS_G3_SOFTWARE_G3RS485GPSINFO_H

#include <string>
#include "utils/XuString.h"
#include "G3RS485DataPackage.h"

class G3RS485GPSInfo : public G3RS485DataPackage {
public:
    G3RS485GPSInfo();

    int decode(uint8_t *buf, int len) override;

    uint16_t getGpsSpeed() const;

    void setGpsSpeed(uint16_t gpsSpeed);

    uint8_t getStatus() const;

    void setStatus(uint8_t status);

    uint16_t getDirection() const;

    void setDirection(uint16_t direction);

    int16_t getAltitude() const;

    void setAltitude(int16_t altitude);

    int32_t getLatitude() const;

    void setLatitude(int32_t latitude);

    int32_t getLongitude() const;

    void setLongitude(int32_t longitude);


    const uint8_t *getGpsTimeDate() const;


    std::string toString();

private:
    /* GPS车速	WORD	单位 0.1km/h。范围 0~2500 */
    uint16_t gpsSpeed;
    /* 定位状态	BYTE	0--未定位；1--已定位 */
    uint8_t status;
    /* 方向	WORD	0--正北；最大360。顺时针 */
    uint16_t direction;
    /* 高程	WORD	海拔高度，单位为米（m） */
    int16_t altitude;
    /* 纬度	DWORD	以度为单位的纬度值乘以10 的6次方，精确到百万分之一度 */
    int32_t latitude;
    /* 经度	DWORD	以度为单位的纬度值乘以10 的6次方，精确到百万分之一度 */
    int32_t longitude;
    /* 日期时间	BCD[6]	YY-MM-DD-hh-mm-ss （GMT+0时间） */
    uint8_t gpsTimeDate[6] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

};


#endif //VIS_G3_SOFTWARE_G3RS485GPSINFO_H
