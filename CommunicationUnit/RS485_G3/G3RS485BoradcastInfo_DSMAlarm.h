//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/12.
//

#ifndef VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_DSMALARM_H
#define VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_DSMALARM_H

#include "G3RS485BoradcastInfo.h"
#include "utils/CodeUtils.h"
#include "G3DetectionDefind.h"
class G3RS485BoradcastInfo_DSMAlarm : public G3RS485BoradcastInfo {
public:
    struct RS485DSMAlarmType1{
        /* Bit0	一级疲劳驾驶报警 */
        bool tired_level1 : 1;
        /* Bit1	接打电话报警 */
        bool phone : 1;
        /* Bit2	抽烟报警 */
        bool smoking : 1;
        /* Bit3	分神驾驶报警 */
        bool lookAround : 1;
        /* Bit4	驾驶员异常报警 */
        bool faceMiss : 1;
        /* Bit5	二级疲劳驾驶报警 */
        bool tired_level2 : 1;
        /* Bit6	打哈欠 */
        bool yawn : 1;
        /* Bit7	驾驶员变更事件 */
        bool driverChange : 1;
        /* Bit8	主动抓拍事件 */
        bool captureEvent : 1;
        /* Bit9-Bit15	保留 */
        uint reserve : 7;
    };


    G3RS485BoradcastInfo_DSMAlarm();

    int toCode(uint8_t *buf) override;

    const RS485DSMAlarmType1 &getAlarmType1() const;

    void setAlarmType1(const RS485DSMAlarmType1 &alarmType1);

    uint16_t getAlarmType2() const;

    void setAlarmType2(uint16_t alarmType2);


private:
    /* 报警/事件类型1 */
    RS485DSMAlarmType1 alarmType1 = {0x00};
    /* 报警/事件类型2 */
    uint16_t alarmType2 = {0x00};

};


#endif //VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_DSMALARM_H
