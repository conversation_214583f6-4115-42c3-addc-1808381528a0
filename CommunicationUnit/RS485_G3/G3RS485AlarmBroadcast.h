//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//

#ifndef VIS_G3_SOFTWARE_G3RS485ALARMBROADCAST_H
#define VIS_G3_SOFTWARE_G3RS485ALARMBROADCAST_H

#include "G3RS485DataPackage.h"
#include "G3RS485BoradcastInfo.h"
#include "vector"

class G3RS485AlarmBroadcast : public G3RS485DataPackage {
public:
    G3RS485AlarmBroadcast();

    ~G3RS485AlarmBroadcast();

    int toCode(uint8_t *buf) override;

    uint8_t getListSize() const;

    void setListSize(uint8_t listSize);


    int addDataItem(G3RS485BoradcastInfo &g3Rs485BoradcastInfo);

private:
    uint8_t listSize;
    std::vector<G3RS485BoradcastInfo *> dataList;
};


#endif //VIS_G3_SOFTWARE_G3RS485ALARMBROADCAST_H
