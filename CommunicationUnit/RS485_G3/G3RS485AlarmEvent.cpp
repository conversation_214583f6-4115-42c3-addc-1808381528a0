//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/28.
//

#include "G3RS485AlarmEvent.h"

G3RS485AlarmEvent::G3RS485AlarmEvent() {
    setMsgId(RS485_MSGID_ALARM_EVENT);
}

G3RS485AlarmEvent::~G3RS485AlarmEvent() {

}

int G3RS485AlarmEvent::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 7;   /* 第8位就是正式内容了 */
    /* 定义一个长度变量  用来计算内容长度 */
    uint16_t contentLen = 0;
    infoListSize = dataItemList_ADAS.size() + dataItemList_BSD.size();
    /* 参数总数*/
    buf[index] = infoListSize;
    index++;
    contentLen++;

    /* 把ADAS内容一个个写进去 */
    if (dataItemList_ADAS.size() > 0) {
        for (std::size_t i = 0; i < dataItemList_ADAS.size(); i++) {
            int itemLen = dataItemList_ADAS[i].toCode(buf + index);
            index += itemLen;
            contentLen += itemLen;
        }
    }

    /* 把BSD内容一个个写进去 */
    if (dataItemList_BSD.size() > 0) {
        for (std::size_t i = 0; i < dataItemList_BSD.size(); i++) {
            int itemLen = dataItemList_BSD[i].toCode(buf + index);
            index += itemLen;
            contentLen += itemLen;
        }
    }

    /* 再把内容长度写进去 */
    setContentLen(contentLen);
    /* 加上头尾那些东西 */
    ret = G3RS485DataPackage::toCode(buf);
    return ret;

}

int G3RS485AlarmEvent::addBSDAlarmEventInfo(G3RS485AlarmEvent_BSDEVent &bsdeVent) {
    dataItemList_BSD.push_back(bsdeVent);
    return 0;
}

int G3RS485AlarmEvent::addADASAlarmEventInfo(G3RS485AlarmEvent_ADASEVent &adaseVent) {
    dataItemList_ADAS.push_back(adaseVent);
    return 0;
}
