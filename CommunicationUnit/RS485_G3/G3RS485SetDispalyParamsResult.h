//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/8.
//

#ifndef VIS_G3_SOFTWARE_G3RS485SETDISPALYPARAMSRESULT_H
#define VIS_G3_SOFTWARE_G3RS485SETDISPALYPARAMSRESULT_H

#include "G3RS485DataPackage.h"
class G3RS485SetDispalyParamsResult : public G3RS485DataPackage {
public:
    G3RS485SetDispalyParamsResult();

    int decode(uint8_t *buf, int len) override;

    uint8_t getResult() const;

    void setResult(uint8_t result);

private:
    uint8_t result = 0xFF;

};


#endif //VIS_G3_SOFTWARE_G3RS485SETDISPALYPARAMSRESULT_H
