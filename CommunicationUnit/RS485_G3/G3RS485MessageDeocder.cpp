//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//


#include "G3RS485MessageDeocder.h"


namespace vis {

    void G3RS485MessageDeocder::init() {
        dataCache = static_cast<uint8_t *>(malloc(MAX_DATA_CACHE_SIZE));
        packageCache = static_cast<uint8_t *>(malloc(MAX_DATA_CACHE_SIZE));
    }

    void G3RS485MessageDeocder::onRecvRS485Data(const uint8_t *buf, const int len) {
        for (int i = 0; i < len; i++) {
            if (buf[i] == IDENTIFICATION_HEAD) {
                /* 看看是不是已经拿到过一个头了 */
                if (hasHead) {
                    /* 看看是不是头尾刚好粘在一起了 */
                    if (dataCacheIndex < 2) {
                        dataCacheIndex = 0;
                        addOneByteToCache(buf[i]);
                    } else {
                        addOneByteToCache(buf[i]);
                        parseDataFromDataCache();
                        hasHead = false;
                        dataCacheIndex = 0;
                    }
                } else {
                    hasHead = true;
                    dataCacheIndex = 0;
                    addOneByteToCache(buf[i]);
                }
            } else {
                addOneByteToCache(buf[i]);
            }
        }
    }

    void G3RS485MessageDeocder::addOneByteToCache(uint8_t oneByte) {
        if (dataCacheIndex >= MAX_DATA_CACHE_SIZE) {
            dataCacheIndex = 0;
        }
        dataCache[dataCacheIndex] = oneByte;
        dataCacheIndex++;

    }

    void G3RS485MessageDeocder::parseDataFromDataCache() {
        //先转义一下
        int packagetDataLen = CodeUtils::getInstance().doEscape4ReceiveFromRS485(dataCache, dataCacheIndex, 1,
                                                                                 dataCacheIndex - 2, packageCache);


//    printf("\npackageCache connect: ");
//    for(int i = 0; i < packagetDataLen; i ++){
//        printf("%02x ",packageCache[i]);
//    }
//    printf("\n");

        //解析出校验码
        uint16_t checkCode = CodeUtils::getInstance().BbToUint16(packageCache + (packagetDataLen - 3));
        //自己计算下校验码
        uint16_t mCheckCode = CodeUtils::getInstance().generateCrc16(packageCache + 1, packagetDataLen - 4);
        // 判断校验码是否正确
        if (checkCode != mCheckCode) {
            printf("RS485 checkCode error!  PacketCheckCode=%d   mCheckCode=%d  \n", checkCode, mCheckCode);
            printf("dataCache connect: ");
            for (int i = 0; i < dataCacheIndex; i++) {
                printf("%02x ", dataCache[i]);
            }
            printf("\n");
            return;
        }
        //根据命令字判断用什么类装
        switch (packageCache[4]) {
            case G3RS485DataPackage::RS485_MSGID_RESPOND_PERIPHERAL_VERSION : {

                G3RS485RespondPeripheralVersion peripheralVersion;
                if (peripheralVersion.decode(packageCache, packagetDataLen) == 0) {

                    /* 外设版本这个比较特殊，由于L4把内容里面的外设ID给写错了成了1，还发货了，导致只能取头部那里的外设ID了 */
                    callback->onGetPeripheralVersion(peripheralVersion.getRs485DeviceId(),
                                                     peripheralVersion.getSoftwareVersion(),
                                                     peripheralVersion.getProtocolVersion());
//                char softwareversion[5] = {0x00, 0x00, 0x00, 0x00, 0x00};
//                char protocoversion[5] = {0x00, 0x00, 0x00, 0x00, 0x00};
//                memcpy(softwareversion, peripheralVersion.getSoftwareVersion(), 4);
//                memcpy(protocoversion, peripheralVersion.getProtocolVersion(), 4);
//                printf("peripheral id=%d  software version=%s protoco version=%s \n",
//                       peripheralVersion.getPeripheralId(), softwareversion, protocoversion);
                }
            }
                break;


            case G3RS485DataPackage::RS485_MSGID_RESPOND_GPS_INFO : {
                G3RS485GPSInfo g3485GpsInfo;
                if (g3485GpsInfo.decode(packageCache, packagetDataLen) == 0) {
                    callback->onGetGPSInfoFromRS485(g3485GpsInfo);
//                char softwareversion[5] = {0x00, 0x00, 0x00, 0x00, 0x00};
//                char protocoversion[5] = {0x00, 0x00, 0x00, 0x00, 0x00};
//                memcpy(softwareversion, peripheralVersion.getSoftwareVersion(), 4);
//                memcpy(protocoversion, peripheralVersion.getProtocolVersion(), 4);
//                printf("peripheral id=%d  software version=%s protoco version=%s \n",
//                       peripheralVersion.getPeripheralId(), softwareversion, protocoversion);
                }
            }
                break;

            case G3RS485DataPackage::RS485_MSGID_GET_UPGRADE_FILE_DATA : {
                G3RS485GetUpgradeFileData g3Rs485GetUpgradeFileData;
                if (g3Rs485GetUpgradeFileData.decode(packageCache, packagetDataLen) == 0) {
                    callback->onGetGetUpgradeFileDataFromRS485(g3Rs485GetUpgradeFileData);
                }
            }
                break;

            case G3RS485DataPackage::RS485_MSGID_UPGRADE_RESULT : {
                G3RS485UpgradeResult upgradeResult;
                if (upgradeResult.decode(packageCache, packagetDataLen) == 0) {
                    callback->onGetUpgradeResultFromRS485(upgradeResult);
                }
            }
                break;

            case G3RS485DataPackage::RS485_MSGID_RESPOND_SET_DISPALY_PARAMS_RESULT : {
                G3RS485SetDispalyParamsResult setDispalyParamsResult;
                if (setDispalyParamsResult.decode(packageCache, packagetDataLen) == 0) {
                    callback->onGetSetDispalyParamsResultFromRS485(setDispalyParamsResult);
                }
            }
                break;

            case G3RS485DataPackage::RS485_MSGID_RESPOND_START_UPGRADE_L4 : {
                G3RS485StartUpgradeL4Result startUpgradeL4Result;
                if (startUpgradeL4Result.decode(packageCache, packagetDataLen) == 0) {
                    callback->onGetStartUpgradeL4ResultFromRS485(startUpgradeL4Result);
                }
            }
                break;

            case G3RS485DataPackage::RS485_MSGID_GET_L4_UPGRADE_FILE_DATA : {
                G3RS485GetL4UpgradeFileData getL4UpgradeFileData;
                if (getL4UpgradeFileData.decode(packageCache, packagetDataLen) == 0) {
                    callback->onGetGetL4UpgradeFileDataFromRS485(getL4UpgradeFileData);
                }
            }
                break;

            case G3RS485DataPackage::RS485_MSGID_UPGRADE_L4_RESULT : {
                G3RS485UpgradeL4Result upgradeL4Result;
                if (upgradeL4Result.decode(packageCache, packagetDataLen) == 0) {
                    callback->onGetGetL4UpgradeResultFromRS485(upgradeL4Result);
                }
            }
                break;


        }
    }

    void G3RS485MessageDeocder::setInterface(CommunicationDataCallback &communicationDataCallback) {
        callback = &communicationDataCallback;
    }

}
