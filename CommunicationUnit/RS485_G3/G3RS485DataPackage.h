//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//

#ifndef VIS_G3_SOFTWARE_G3RS485DATAPACKAGE_H
#define VIS_G3_SOFTWARE_G3RS485DATAPACKAGE_H

#include <cstdint>
#include "utils/CodeUtils.h"

class G3RS485DataPackage {
public:
    /* 外设ID   广播地址 */
    const static int RS485_DEVICE_ID_BROADCAST = 0x00;
    /* 外设ID   提示器 */
    const static int RS485_DEVICE_ID_BUZZER = 0x01;
    /* 外设ID   超声波传感器 */
    const static int RS485_DEVICE_ID_ULTRASONIC_SENSOR = 0x02;
    /* 外设ID   整合器C1 */
    const static int RS485_DEVICE_ID_C1 = 0x03;
    /* 外设ID   BSD 显示器L2 */
    const static int RS485_DEVICE_ID_L2 = 0x04;
    /* 外设ID   外挂GPS模块 */
    const static int RS485_DEVICE_ID_GPS = 0x05;
    /* 外设ID   BSD 显示器L3 */
    const static int RS485_DEVICE_ID_L3 = 0x06;
    /* 外设ID   通用显示器L4 */
    const static int RS485_DEVICE_ID_L4 = 0x07;




    /* 命令字   通用应答 */
    const static int RS485_MSGID_UNIVERSALANSWER = 0x10;
    /* 命令字   报警广播包 */
    const static int RS485_MSGID_ALARM_BROADCAST = 0xC3;
    /* 命令字   报警事件 */
    const static int RS485_MSGID_ALARM_EVENT = 0x40;
    /* 命令字   获取外设版本号 */
    const static int RS485_MSGID_GET_PERIPHERAL_VERSION = 0xA3;
    /* 命令字   回复外设版本号 */
    const static int RS485_MSGID_RESPOND_PERIPHERAL_VERSION = 0x23;
    /* 命令字   获取GPS信息 */
    const static int RS485_MSGID_GET_GPS_INFO = 0x2D;
    /* 命令字   回复GPS信息 */
    const static int RS485_MSGID_RESPOND_GPS_INFO = 0xAD;
    /* 命令字   请求升级 */
    const static int RS485_MSGID_START_UPGRADE = 0xAB;
    /* 命令字   请求升级文件数据 */
    const static int RS485_MSGID_GET_UPGRADE_FILE_DATA = 0x2C;
    /* 命令字   回复升级文件数据 */
    const static int RS485_MSGID_RESPOND_UPGRADE_FILE_DATA = 0xAC;
    /* 命令字   升级结果 */
    const static int RS485_MSGID_UPGRADE_RESULT = 0x2B;
    /* 命令字   配置外设显示参数 */
    const static int RS485_MSGID_SET_DISPALY_PARAMS = 0xB1;
    /* 命令字   获取配置外设显示参数的结果 */
    const static int RS485_MSGID_RESPOND_SET_DISPALY_PARAMS_RESULT = 0x31;
    /* 命令字   发起下载提示器程序图片合成包文件 */
    const static int RS485_MSGID_START_UPGRADE_L4 = 0xB2;
    /* 命令字   回复发起下载提示器程序图片合成包文件的结果 */
    const static int RS485_MSGID_RESPOND_START_UPGRADE_L4 = 0x32;
    /* 命令字   提示器请求程序图片合成包文件数据 */
    const static int RS485_MSGID_GET_L4_UPGRADE_FILE_DATA = 0x33;
    /* 命令字   回复提示器请求程序图片合成包文件数据 */
    const static int RS485_MSGID_RESPOND_L4_UPGRADE_FILE_DATA = 0xB3;
    /* 命令字   提示器请求程序图片合成包文件数据 */
    const static int RS485_MSGID_UPGRADE_L4_RESULT = 0x34;
    /* 命令字   回复提示器请求程序图片合成包文件数据 */
    const static int RS485_MSGID_RESPOND_UPGRADE_L4_RESULT = 0xB4;

    /**
     * 把对象转成一包16进制数据包
     *
     * @param buf ： 用来存放数据包的内存指针
     * @param len ： 内存指针的大小
     *
     * @return 16进制数据包的大小
     *
     * */
    virtual int toCode(uint8_t *buf);

    /**
     * 解析一包数据包转成对象
     *
     * @param buf ： 用来存放数据包的内存指针
     * @param len ： 内存指针的大小
     *
     * @return 结果  0：成功   其他：失败
     *
     * */
    virtual int decode(uint8_t *buf, int len);


    uint8_t getHead() const;

    void setHead(uint8_t head);

    uint16_t getFlowdId() const;

    void setFlowdId(uint16_t flowdId);

    uint8_t getRs485DeviceId() const;

    void setRs485DeviceId(uint8_t rs485DeviceId);

    uint8_t getMsgId() const;

    void setMsgId(uint8_t msgId);

    uint16_t getContentLen() const;

    void setContentLen(uint16_t contentLen);

    uint8_t *getContentBuf() const;

    void setContentBuf(uint8_t *contentBuf);

    uint16_t getCheckCode() const;

    void setCheckCode(uint16_t checkCode);

    uint8_t getTail() const;

    void setTail(uint8_t tail);

private:
    /* 协议头 */
    uint8_t head = 0x5E;
    /* 流水号 */
    uint16_t flowdId = 0;
    /* 外设ID    这个字段为需要接收这条命令的目标ID */
    uint8_t rs485DeviceId = 0;
    /* 命令字 */
    uint8_t msgId = 0;
    /* 内容长度 */
    uint16_t contentLen = 0;
    /* 内容 */
    uint8_t *contentBuf;
    /* 校验码 */
    uint16_t checkCode;
    /* 协议尾 */
    uint8_t tail = 0x5E;


};


#endif //VIS_G3_SOFTWARE_G3RS485DATAPACKAGE_H
