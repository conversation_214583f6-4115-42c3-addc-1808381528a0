//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//

#ifndef VIS_G3_SOFTWARE_G3RS485MESSAGEDEOCDER_H
#define VIS_G3_SOFTWARE_G3RS485MESSAGEDEOCDER_H

#include "CommunicationDataCallback.h"
#include "G3RS485DataPackage.h"
#include "G3RS485RespondPeripheralVersion.h"
#include <malloc.h>
#include "G3RS485GPSInfo.h"

namespace vis {

    class G3RS485MessageDeocder {
    public:


        void init();

        void setInterface(CommunicationDataCallback &communicationDataCallback);

        void onRecvRS485Data(const uint8_t *buf, const int len);

        void addOneByteToCache(uint8_t oneByte);

        void parseDataFromDataCache();


    private:

        const static uint8_t IDENTIFICATION_HEAD = 0x5E;
        const static int MAX_DATA_CACHE_SIZE = 1024 * 1024 * 1;

        CommunicationDataCallback *callback;
        bool hasHead = false;
        uint8_t *dataCache;
        int dataCacheIndex = 0;
        uint8_t *packageCache;
    };

}
#endif //VIS_G3_SOFTWARE_G3RS485MESSAGEDEOCDER_H
