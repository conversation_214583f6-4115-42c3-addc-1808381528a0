//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/2/4.
//

#ifndef VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_ADAS_H
#define VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_ADAS_H

#include "G3RS485BoradcastInfo.h"
#include "utils/CodeUtils.h"
#include "G3DetectionDefind.h"

class G3RS485BoradcastInfo_ADAS : public G3RS485BoradcastInfo {
public:
    G3RS485BoradcastInfo_ADAS();

    int toCode(uint8_t *buf) override;


    uint8_t getVehicleCollisionTime() const;

    void setVehicleCollisionTime(uint8_t vehicleCollisionTime);

    uint16_t getVehicleYDistance() const;

    void setVehicleYDistance(uint16_t vehicleYDistance);

    int16_t getLineXDistanceLeft() const;

    void setLineXDistanceLeft(int16_t lineXDistanceLeft);

    int16_t getLineXDistanceRight() const;

    void setLineXDistanceRight(int16_t lineXDistanceRight);

    uint8_t getPedestrianCollisionTime() const;

    void setPedestrianCollisionTime(uint8_t pedestrianCollisionTime);

    uint16_t getPedestrianYDistance() const;

    void setPedestrianYDistance(uint16_t pedestrianYDistance);

    const ADASDetectionInfo_alarmStatus &getAlarmStatus() const;

    void setAlarmStatus(const ADASDetectionInfo_alarmStatus &alarmStatus);

    uint8_t getSpeedLimitMax() const;

    void setSpeedLimitMax(uint8_t speedLimitMax);

    uint8_t getSpeedLimitMin() const;

    void setSpeedLimitMin(uint8_t speedLimitMin);

private:
    /* 车距监控时间	uint8_t	单位 0.1s。0-80,0XFF表示没检测到车 */
    uint8_t vehicleCollisionTime = 0xFF;
    /* 车距监控距离	uint16_t	单位0.01m。0-30000,其它值表示没检测到车 */
    uint16_t vehicleYDistance = 0xFFFF;
    /* 左车道线距离	int16_t	单位mm。-5000~5000,其它值表示没检测到线 */
    int16_t lineXDistance_left = 0x7FFF;
    /* 右车道线距离	int16_t	单位mm。-5000~5000,其它值表示没检测到线 */
    int16_t lineXDistance_right = 0x7FFF;
    /* 行人碰撞时间	uint8_t	单位 0.1s。0-80,0XFF表示没检测到人 */
    uint8_t pedestrianCollisionTime = 0xFF;
    /* 行人距离	uint16_t	单位0.01m。0-5000,其它值表示没检测到人 */
    uint16_t pedestrianYDistance = 0xFFFF;
    /* 状态 */
    ADASDetectionInfo_alarmStatus alarmStatus = {0,0,0,0,0,0,0};
    /* 速度上限 0-0xfe（254）；0xff为无效值 */
    uint8_t speedLimit_max = 0xFF;
    /* 速度下限0-0xfe（254）；0xff为无效值 */
    uint8_t speedLimit_min = 0xFF;

};


#endif //VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_ADAS_H
