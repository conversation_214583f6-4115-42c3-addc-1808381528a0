//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/8.
//

#ifndef VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_SYSTEMINFO_H
#define VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_SYSTEMINFO_H

#include "G3RS485BoradcastInfo.h"
#include "utils/CodeUtils.h"

class G3RS485BoradcastInfo_SystemInfo : public G3RS485BoradcastInfo {
public:
    G3RS485BoradcastInfo_SystemInfo();

    int toCode(uint8_t *buf) override;


    uint8_t getTfCardError() const;

    void setTfCardError(uint8_t tfCardError);

    uint8_t getHighTemperature() const;

    void setHighTemperature(uint8_t highTemperature);

    uint8_t getAdasCameraError() const;

    void setAdasCameraError(uint8_t adasCameraError);

    uint8_t getBsdCameraErrorL() const;

    void setBsdCameraErrorL(uint8_t bsdCameraErrorL);

    uint8_t getBsdCameraErrorR() const;

    void setBsdCameraErrorR(uint8_t bsdCameraErrorR);

    uint8_t getDsmCameraError() const;

    void setDsmCameraError(uint8_t dsmCameraError);

    uint8_t getAdasAlgorithmError() const;

    void setAdasAlgorithmError(uint8_t adasAlgorithmError);

    uint8_t getBsdAlgorithmErrorL() const;

    void setBsdAlgorithmErrorL(uint8_t bsdAlgorithmErrorL);

    uint8_t getBsdAlgorithmErrorR() const;

    void setBsdAlgorithmErrorR(uint8_t bsdAlgorithmErrorR);

    uint8_t getDsmAlgorithmError() const;

    void setDsmAlgorithmError(uint8_t dsmAlgorithmError);

    uint8_t getCameraCover1() const;

    void setCameraCover1(uint8_t cameraCover1);

    uint8_t getCameraCover2() const;

    void setCameraCover2(uint8_t cameraCover2);

    uint8_t getCameraCover3() const;

    void setCameraCover3(uint8_t cameraCover3);

    uint8_t getCameraCover4() const;

    void setCameraCover4(uint8_t cameraCover4);

    uint8_t getCameraCover5() const;

    void setCameraCover5(uint8_t cameraCover5);

    uint8_t getCameraCover6() const;

    void setCameraCover6(uint8_t cameraCover6);

    uint8_t getCameraCover7() const;

    void setCameraCover7(uint8_t cameraCover7);

    uint8_t getCameraCover8() const;

    void setCameraCover8(uint8_t cameraCover8);

private:
    /* Bit0	TF卡状态	0	TF卡正常    1	TF卡坏或无卡 */
    uint8_t tfCardError = 0;
    /* Bit1	高温报警	0	温度正常    1	温度超标。>90度 */
    uint8_t highTemperature = 0;
    /* Bit2	ADAS摄像头	0	ADAS摄像头工作正常    1	ADAS摄像头工作异常 */
    uint8_t adasCameraError = 0;
    /* Bit3	左侧摄像头	0	左侧摄像头工作正常    1	左侧摄像头工作异常 */
    uint8_t bsdCameraError_L = 0;
    /* Bit4	右侧摄像头	0	右侧摄像头工作正常    1	右侧摄像头工作异常 */
    uint8_t bsdCameraError_R = 0;
    /* Bit5	DSM摄像头	0	DSM摄像头工作正常     1	DSM摄像头工作异常  */
    uint8_t dsmCameraError = 0;
    /* Bit6	ADAS算法	0	ADAS算法正常    1	ADAS算法异常 */
    uint8_t adasAlgorithmError = 0;
    /* Bit7	左侧算法	0	左侧算法正常     1	左侧算法异常 */
    uint8_t bsdAlgorithmError_L = 0;
    /* Bit8	右侧算法	0	右侧算法正常     1	右侧算法异常 */
    uint8_t bsdAlgorithmError_R = 0;
    /* Bit9	DSM算法	0	DSM算法正常     1	DSM算法异常 */
    uint8_t dsmAlgorithmError = 0;
    /* Bit16			0：正常，1：1号摄像头遮挡 */
    uint8_t cameraCover_1 = 0;
    /* Bit17			0：正常，1：2号摄像头遮挡 */
    uint8_t cameraCover_2 = 0;
    /* Bit18			0：正常，1：3号摄像头遮挡 */
    uint8_t cameraCover_3 = 0;
    /* Bit19			0：正常，1：4号摄像头遮挡 */
    uint8_t cameraCover_4 = 0;
    /* Bit20			0：正常，1：5号摄像头遮挡 */
    uint8_t cameraCover_5 = 0;
    /* Bit21			0：正常，1：6号摄像头遮挡 */
    uint8_t cameraCover_6 = 0;
    /* Bit22			0：正常，1：7号摄像头遮挡 */
    uint8_t cameraCover_7 = 0;
    /* Bit23			0：正常，1：8号摄像头遮挡 */
    uint8_t cameraCover_8 = 0;


};


#endif //VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_SYSTEMINFO_H
