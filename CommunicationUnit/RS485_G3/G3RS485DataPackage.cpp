//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//

#include "G3RS485DataPackage.h"


int G3RS485DataPackage::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 0;
    /* 先塞标识符 */
    buf[index] = head;
    index++;
    /* 再塞通信序号 */
    CodeUtils::getInstance().uint16ToBb(flowdId, buf + index);
    index += 2;
    /* 再塞外设地址 */
    buf[index] = rs485DeviceId;
    index++;
    /* 再塞通信命令 */
    buf[index] = msgId;
    index++;
    /* 再塞数据长度 */
    CodeUtils::getInstance().uint16ToBb(contentLen, buf + index);
    index += 2;
    index += contentLen;
    /* 再塞校验字 */
    checkCode = CodeUtils::getInstance().generateCrc16(buf + 1, index - 1);
    CodeUtils::getInstance().uint16ToBb(checkCode, buf + index);
    index += 2;
    /* 再塞标识符 */
    buf[index] = tail;
    index++;
    ret = index;
    return ret;
}

int G3RS485DataPackage::decode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 头是固定的，就不用解析了 */
    setHead(buf[index]);
    index++;
    /* 设置流水号 */
    setFlowdId(CodeUtils::getInstance().BbToint32(buf + index));
    index += 2;
    /* 设置外设地址 */
    setRs485DeviceId(buf[index]);
    index++;
    /* 命令字是固定的 也不用解析了 */
    setMsgId(buf[index]);
    index++;
    /* 内容长度 */
    setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
    index += 2;
    /* 跳过内容  直接解析校验码 */
    index += getContentLen();
    setCheckCode(CodeUtils::getInstance().BbToint16((buf + index)));
    index += 2;
    /* 尾巴是固定的，就不用解析了 */
    setTail(buf[index]);
    ret = 0;
    return ret;
}

uint8_t G3RS485DataPackage::getHead() const {
    return head;
}

void G3RS485DataPackage::setHead(uint8_t head) {
    G3RS485DataPackage::head = head;
}

uint16_t G3RS485DataPackage::getFlowdId() const {
    return flowdId;
}

void G3RS485DataPackage::setFlowdId(uint16_t flowdId) {
    G3RS485DataPackage::flowdId = flowdId;
}

uint8_t G3RS485DataPackage::getRs485DeviceId() const {
    return rs485DeviceId;
}

void G3RS485DataPackage::setRs485DeviceId(uint8_t rs485DeviceId) {
    G3RS485DataPackage::rs485DeviceId = rs485DeviceId;
}

uint8_t G3RS485DataPackage::getMsgId() const {
    return msgId;
}

void G3RS485DataPackage::setMsgId(uint8_t msgId) {
    G3RS485DataPackage::msgId = msgId;
}

uint16_t G3RS485DataPackage::getContentLen() const {
    return contentLen;
}

void G3RS485DataPackage::setContentLen(uint16_t contentLen) {
    G3RS485DataPackage::contentLen = contentLen;
}

uint8_t *G3RS485DataPackage::getContentBuf() const {
    return contentBuf;
}

void G3RS485DataPackage::setContentBuf(uint8_t *contentBuf) {
    G3RS485DataPackage::contentBuf = contentBuf;
}

uint16_t G3RS485DataPackage::getCheckCode() const {
    return checkCode;
}

void G3RS485DataPackage::setCheckCode(uint16_t checkCode) {
    G3RS485DataPackage::checkCode = checkCode;
}

uint8_t G3RS485DataPackage::getTail() const {
    return tail;
}

void G3RS485DataPackage::setTail(uint8_t tail) {
    G3RS485DataPackage::tail = tail;
}
