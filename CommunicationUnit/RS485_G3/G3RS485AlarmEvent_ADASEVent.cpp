//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/28.
//



#include "G3RS485AlarmEvent_ADASEVent.h"

int G3RS485AlarmEvent_ADASEVent::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度 */
    buf[index] = getDataLen();
    index++;
    /* 标志状态 */
    buf[index] = getEventStatus();
    index++;
    /* 报警/事件类型 */
    buf[index] = getEventType();
    index++;
    /* 碰撞时间 */
    buf[index] = getHeadway();
    index++;
    /* 偏离类型 */
    buf[index] = getLdwType();
    index++;
    /* 道路标志识别类型 */
    buf[index] = getRoadSignsType();
    index++;
    /* 预留 */
    memcpy(buf + index, getReserved(), 4);
    index += 4;

    ret = index;
    return ret;
}

uint8_t G3RS485AlarmEvent_ADASEVent::getEventStatus() const {
    return eventStatus;
}

void G3RS485AlarmEvent_ADASEVent::setEventStatus(uint8_t eventStatus) {
    G3RS485AlarmEvent_ADASEVent::eventStatus = eventStatus;
}

uint8_t G3RS485AlarmEvent_ADASEVent::getEventType() const {
    return eventType;
}

void G3RS485AlarmEvent_ADASEVent::setEventType(uint8_t eventType) {
    G3RS485AlarmEvent_ADASEVent::eventType = eventType;
}

uint8_t G3RS485AlarmEvent_ADASEVent::getHeadway() const {
    return headway;
}

void G3RS485AlarmEvent_ADASEVent::setHeadway(uint8_t headway) {
    G3RS485AlarmEvent_ADASEVent::headway = headway;
}

uint8_t G3RS485AlarmEvent_ADASEVent::getLdwType() const {
    return ldwType;
}

void G3RS485AlarmEvent_ADASEVent::setLdwType(uint8_t ldwType) {
    G3RS485AlarmEvent_ADASEVent::ldwType = ldwType;
}

uint8_t G3RS485AlarmEvent_ADASEVent::getRoadSignsType() const {
    return roadSignsType;
}

void G3RS485AlarmEvent_ADASEVent::setRoadSignsType(uint8_t roadSignsType) {
    G3RS485AlarmEvent_ADASEVent::roadSignsType = roadSignsType;
}

const uint8_t *G3RS485AlarmEvent_ADASEVent::getReserved() const {
    return reserved;
}

G3RS485AlarmEvent_ADASEVent::G3RS485AlarmEvent_ADASEVent() {
    setDataId(DATAID_ADAS_ALARM_EVENT);
    setDataLen(9);

}

G3RS485AlarmEvent_ADASEVent::~G3RS485AlarmEvent_ADASEVent() {

}
