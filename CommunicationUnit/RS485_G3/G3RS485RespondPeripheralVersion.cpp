//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/8.
//

#include "G3RS485RespondPeripheralVersion.h"

G3RS485RespondPeripheralVersion::G3RS485RespondPeripheralVersion() {
    setMsgId(RS485_MSGID_RESPOND_PERIPHERAL_VERSION);
}

int G3RS485RespondPeripheralVersion::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 7;   /* 第8位就是正式内容了 */
    /* 设置内容长度 */
    setContentLen(9);
    /* 塞入外设编号 */
    buf[index] = peripheralId;
    index++;
    /* 塞入软件版本号 */
    memcpy(buf + index, softwareVersion, 4);
    index += 4;
    /* 塞入通信协议版本号 */
    memcpy(buf + index, protocolVersion, 4);
    index += 4;
    /* 加上头尾那些东西 */
    ret = G3RS485DataPackage::toCode(buf);
    return ret;
}
#include "XuString.h"
int G3RS485RespondPeripheralVersion::decode(uint8_t *buf, int len) {
    printf("G3RS485RespondPeripheralVersion::decode buf=%s  \n",XuString::getInstance().byteArrayToString(buf,len).c_str());
    /* 直接从数据内容开始取 */
    int index = 7;
    /* 取外设ID */
    setPeripheralId(buf[index]);
    index++;
    /* 取软件版本号 */
    memcpy(softwareVersion, buf + index, 4);
    index += 4;
    /* 取通信协议版本号 */
    memcpy(protocolVersion, buf + index, 4);
    index += 4;
    /* 去数据内容之外的东西 */
    return G3RS485DataPackage::decode(buf, len);
}

uint8_t G3RS485RespondPeripheralVersion::getPeripheralId() const {
    return peripheralId;
}

void G3RS485RespondPeripheralVersion::setPeripheralId(uint8_t peripheralId) {
    G3RS485RespondPeripheralVersion::peripheralId = peripheralId;
}

const char *G3RS485RespondPeripheralVersion::getSoftwareVersion() const {
    return softwareVersion;
}

const char *G3RS485RespondPeripheralVersion::getProtocolVersion() const {
    return protocolVersion;
}
