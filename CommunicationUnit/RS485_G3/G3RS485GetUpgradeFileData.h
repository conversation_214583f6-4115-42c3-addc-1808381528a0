//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/18.
//

#ifndef VIS_G3_SOFTWARE_G3RS485GETUPGRADEFILEDATA_H
#define VIS_G3_SOFTWARE_G3RS485GETUPGRADEFILEDATA_H

#include "G3RS485DataPackage.h"

class G3RS485GetUpgradeFileData : public G3RS485DataPackage {
public:
    G3RS485GetUpgradeFileData();

    int toCode(uint8_t *buf) override;

    int decode(uint8_t *buf, int len) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

    uint32_t getOffset() const;

    void setOffset(uint32_t offset);


private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;
    /* 文件偏移地址 */
    uint32_t offset = 0;

};


#endif //VIS_G3_SOFTWARE_G3RS485GETUPGRADEFILEDATA_H
