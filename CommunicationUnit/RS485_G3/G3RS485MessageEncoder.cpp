//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//

#include "G3RS485MessageEncoder.h"

uint16_t G3RS485MessageEncoder::getFlowId() {
    flowId++;
    if (flowId >= 0xFFFF) {
        flowId = 0;
    }
    return flowId;
}

int G3RS485MessageEncoder::generateG3RS485AlarmBroadcast(uint8_t *buf, G3RS485AlarmBroadcast &g3485AlarmBroadcast) {
    g3485AlarmBroadcast.setFlowdId(getFlowId());
    int ret = g3485AlarmBroadcast.toCode(buf);
    return ret;
}


int G3RS485MessageEncoder::generateGetPeripheralVersion(const int peripheralId, uint8_t *buf) {
    G3RS485GetPeripheralVersion getPeripheralVersion;
    getPeripheralVersion.setRs485DeviceId(peripheralId);
    getPeripheralVersion.setFlowdId(getFlowId());
    getPeripheralVersion.setPeripheralId(peripheralId);
    int ret = getPeripheralVersion.toCode(buf);
    return ret;
}

int G3RS485MessageEncoder::generateGetGPSInfo(const int peripheralId, uint8_t *buf) {
    G3RS485GetGPSInfo gpsInfo;
    gpsInfo.setFlowdId(getFlowId());
    gpsInfo.setRs485DeviceId(peripheralId);
    gpsInfo.setPeripheralId(peripheralId);
    int ret = gpsInfo.toCode(buf);
    return ret;
}

int G3RS485MessageEncoder::generateG3RS485AlarmEvent(uint8_t *buf, G3RS485AlarmEvent &g3Rs485AlarmEvent) {
    g3Rs485AlarmEvent.setFlowdId(getFlowId());
    int ret = g3Rs485AlarmEvent.toCode(buf);
    return ret;
}

int G3RS485MessageEncoder::generateStartUpgrade(uint8_t *buf, G3RS485StartUpgrade &startUpgrade) {
    startUpgrade.setRs485DeviceId(startUpgrade.getPeripheralId());
    startUpgrade.setFlowdId(getFlowId());
    int ret = startUpgrade.toCode(buf);
    return ret;
}

int G3RS485MessageEncoder::generateRespondUpgradeFileData(uint8_t *buf,
                                                          G3RS485RespondUpgradeFileData &respondUpgradeFileData) {
    respondUpgradeFileData.setRs485DeviceId(respondUpgradeFileData.getPeripheralId());
    respondUpgradeFileData.setFlowdId(getFlowId());
    int ret = respondUpgradeFileData.toCode(buf);
    return ret;
}

int G3RS485MessageEncoder::generateSetDispalyParams(uint8_t *buf, G3RS485SetDispalyParams &setDispalyParams) {
    setDispalyParams.setRs485DeviceId(G3RS485DataPackage::RS485_DEVICE_ID_L4);
    setDispalyParams.setFlowdId(getFlowId());
    int ret = setDispalyParams.toCode(buf);
    return ret;
}

int G3RS485MessageEncoder::generateStartUpgradeL4(uint8_t *buf, G3RS485StartUpgradeL4 &startUpgrade) {
    startUpgrade.setRs485DeviceId(G3RS485DataPackage::RS485_DEVICE_ID_L4);
    startUpgrade.setFlowdId(getFlowId());
    int ret = startUpgrade.toCode(buf);
    return ret;
}

int G3RS485MessageEncoder::generateRespondL4UpgradeFileData(uint8_t *buf,
                                                            G3RS485RespondL4UpgradeFileData &l4UpgradeFileData) {
    l4UpgradeFileData.setRs485DeviceId(G3RS485DataPackage::RS485_DEVICE_ID_L4);
    l4UpgradeFileData.setFlowdId(getFlowId());
    int ret = l4UpgradeFileData.toCode(buf);
    return ret;
}

int
G3RS485MessageEncoder::generateRespondL4UpgradeResult(uint8_t *buf, G3RS485ResponeUpgradeL4Result &upgradeL4Result) {
    upgradeL4Result.setRs485DeviceId(G3RS485DataPackage::RS485_DEVICE_ID_L4);
    upgradeL4Result.setFlowdId(getFlowId());
    int ret = upgradeL4Result.toCode(buf);
    return ret;
}


