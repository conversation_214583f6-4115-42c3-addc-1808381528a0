//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/12.
//

#ifndef VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_ADASALARM_H
#define VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_ADASALARM_H

#include "G3RS485BoradcastInfo.h"
#include "utils/CodeUtils.h"
#include "G3DetectionDefind.h"



class G3RS485BoradcastInfo_ADASAlarm : public G3RS485BoradcastInfo {
public:
    struct RS485ADASAlarmType1{
        /* Bit0	前向碰撞报警 */
        bool ttc : 1;
        /* Bit1	车距过近报警 */
        bool headway : 1;
        /* Bit2	行人碰撞报警 */
        bool pcw : 1;
        /* Bit3	防溜车提醒 */
        bool vb : 1;
        /* Bit4	前车启动提醒 */
        bool go : 1;
        /* Bit5	频繁变道报警 */
        bool ldw_fc : 1;
        /* Bit6	左侧虚线偏离 */
        bool ldw_left_dash : 1;
        /* Bit7	左侧实线偏离 */
        bool ldw_left_solid : 1;
        /* Bit8	右侧虚线偏离 */
        bool ldw_right_dash : 1;
        /* Bit9	右侧实线偏离 */
        bool ldw_right_solid : 1;
        /* Bit10	双向偏离 */
        bool ldw_unknow : 1;
        /* Bit11-Bit15	保留 */
        uint reserve : 5;
    };

    struct RS485ADASAlarmType2{
        /* Bit0	速度超限报警 */
        bool speeding : 1;
        /* Bit1	高度超限报警 */
        bool heightExceedingLimit : 1;
        /* Bit2	载重超限报警 */
        bool overload : 1;
        /* Bit3	主动抓拍事件 */
        bool captureEvent : 1;
        /* Bit4-Bit15 保留 */
        uint reserve : 12;
    };


    G3RS485BoradcastInfo_ADASAlarm();

    int toCode(uint8_t *buf) override;

    const RS485ADASAlarmType1 &getAdasAlarmType1() const;

    void setAdasAlarmType1(const RS485ADASAlarmType1 &adasAlarmType1);

    const RS485ADASAlarmType2 &getAdasAlarmType2() const;

    void setAdasAlarmType2(const RS485ADASAlarmType2 &adasAlarmType2);

    uint16_t getAdasAlarmType3() const;

    void setAdasAlarmType3(uint16_t adasAlarmType3);

    uint8_t getVehicleCollisionTime() const;

    void setVehicleCollisionTime(uint8_t vehicleCollisionTime);

    uint8_t getPedestrianCollisionTime() const;

    void setPedestrianCollisionTime(uint8_t pedestrianCollisionTime);

private:
    /* 报警/事件类型1	uint16_t */
    RS485ADASAlarmType1 adasAlarmType1 = {0x00};
    /* 报警/事件类型2	uint16_t */
    RS485ADASAlarmType2 adasAlarmType2 = {0x00};
    /* 报警/事件类型3	uint16_t */
    uint16_t adasAlarmType3 = 0x00;
    /* 车辆碰撞时间	uint8_t	单位100ms，范围0~100，0xff为无效值。 */
    uint8_t vehicleCollisionTime = 0xFF;
    /* 行人碰撞时间	uint8_t	单位100ms，范围0~100，0xff为无效值。 */
    uint8_t pedestrianCollisionTime = 0xFF;


};


#endif //VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_ADASALARM_H
