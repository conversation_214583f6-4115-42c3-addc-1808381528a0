//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/28.
//

#ifndef VIS_G3_SOFTWARE_G3RS485ALARMEVENT_ADASEVENT_H
#define VIS_G3_SOFTWARE_G3RS485ALARMEVENT_ADASEVENT_H

#include "G3RS485AlarmEvent_DataItem.h"

class G3RS485AlarmEvent_ADASEVent : public G3RS485AlarmEvent_DataItem {
public:
    /* 0x01：前向碰撞报警 */
    static const uint8_t EVENT_TYPE_TTC = 0x01;
    /* 0x02：车道偏离报警 */
    static const uint8_t EVENT_TYPE_LDW = 0x02;
    /* 0x03：车距过近报警 */
    static const uint8_t EVENT_TYPE_HMW = 0x03;
    /* 0x04：行人碰撞报警 */
    static const uint8_t EVENT_TYPE_PDW = 0x04;
    /* 0x05：频繁变道报警 */
    static const uint8_t EVENT_TYPE_FLCW = 0x05;
    /* 0x06：道路标识超限报警 */
    static const uint8_t EVENT_TYPE_RSELW = 0x06;
    /* 0x07：防溜车提醒 */
    static const uint8_t EVENT_TYPE_VB = 0x07;
    /* 0x08：前车启动提醒 */
    static const uint8_t EVENT_TYPE_GO = 0x08;
    /* 0x10：道路标志识别事件 */
    static const uint8_t EVENT_TYPE_RSD = 0x10;
    /* 0x11：主动抓拍事件 */
    static const uint8_t EVENT_TYPE_SNAP = 0x11;

    static const uint8_t EVENT_LEVEL_NOT = 0x00;
    static const uint8_t EVENT_LEVEL_LEVEL1 = 0x01;
    static const uint8_t EVENT_LEVEL_LEVEL2 = 0x02;



    G3RS485AlarmEvent_ADASEVent();

    ~G3RS485AlarmEvent_ADASEVent();

    int toCode(uint8_t *buf) override;

    uint8_t getEventStatus() const;

    void setEventStatus(uint8_t eventStatus);

    uint8_t getEventType() const;

    void setEventType(uint8_t eventType);

    uint8_t getHeadway() const;

    void setHeadway(uint8_t headway);

    uint8_t getLdwType() const;

    void setLdwType(uint8_t ldwType);

    uint8_t getRoadSignsType() const;

    void setRoadSignsType(uint8_t roadSignsType);

    const uint8_t *getReserved() const;

private:
    /* 标志状态	uint8_t	0x00：不可用   0x01：开始标志   0x02：结束标志  该字段仅适用于有开始和结束标志类型的报警或事件，报警类型或事件类型无开始和结束标志，则该位不可用，填入0x00即可 */
    uint8_t eventStatus = 0;
    /* 报警/事件类型 0x01：前向碰撞报警  0x02：车道偏离报警  0x03：车距过近报警  0x04：行人碰撞报警  0x05：频繁变道报警  0x06：道路标识超限报警  0x07：防溜车提醒  0x08：前车启动提醒  0x10：道路标志识别事件  0x11：主动抓拍事件  0x12~0x1F：用户自定义  */
    uint8_t eventType;
    /* 碰撞时间  单位100ms，范围0~100，仅报警类型为0x01、0x03和0x04时有效。 */
    uint8_t headway;
    /* 偏离类型  0x01：左侧虚线偏离  0x02：右侧虚线偏离  0x03：左侧实线偏离  0x04：右侧实线偏离  0x05：双侧偏离  仅报警类型为0x02时有效 */
    uint8_t ldwType;
    /* 道路标志识别类型   0x01：限速标志  0x02：限高标志  0x03：限重标志  仅报警类型为0x06和0x10时有效。 */
    uint8_t roadSignsType;
    /* 预留字节 */
    uint8_t reserved[4] = {0x00};
};


#endif //VIS_G3_SOFTWARE_G3RS485ALARMEVENT_ADASEVENT_H
