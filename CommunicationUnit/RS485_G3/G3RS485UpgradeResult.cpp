//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/18.
//

#include "G3RS485UpgradeResult.h"

G3RS485UpgradeResult::G3RS485UpgradeResult() {
    setMsgId(RS485_MSGID_UPGRADE_RESULT);

}

int G3RS485UpgradeResult::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 7;   /* 第8位就是正式内容了 */
    /* 设置内容长度 */
    setContentLen(2);
    /* 塞入外设编号 */
    buf[index] = peripheralId;
    index++;
    /* 塞入升级结果 */
    buf[index] = upgradeResult;
    index++;
    /* 加上头尾那些东西 */
    ret = G3RS485DataPackage::toCode(buf);
    return ret;
}

int G3RS485UpgradeResult::decode(uint8_t *buf, int len) {
    /* 直接从数据内容开始取 */
    int index = 7;
    /* 取外设ID */
    setPeripheralId(buf[index]);
    index++;
    /* 取偏移地址 */
    setUpgradeResult(buf[index]);
    index++;
    /* 去数据内容之外的东西 */
    return G3RS485DataPackage::decode(buf, len);
}

uint8_t G3RS485UpgradeResult::getPeripheralId() const {
    return peripheralId;
}

void G3RS485UpgradeResult::setPeripheralId(uint8_t peripheralId) {
    G3RS485UpgradeResult::peripheralId = peripheralId;
}

uint8_t G3RS485UpgradeResult::getUpgradeResult() const {
    return upgradeResult;
}

void G3RS485UpgradeResult::setUpgradeResult(uint8_t upgradeResult) {
    G3RS485UpgradeResult::upgradeResult = upgradeResult;
}
