//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/12.
//

#include "G3RS485BoradcastInfo_BSDAlarm.h"

G3RS485BoradcastInfo_BSDAlarm::G3RS485BoradcastInfo_BSDAlarm() {
    setDataId(DATAID_BSD_EVENT);
    setDatalen(4);
}

int G3RS485BoradcastInfo_BSDAlarm::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 3; //前面留三个字节用来放ID跟长度

    uint16_t tempAlarm = 0;
    /* 报警/事件类型1 */
    memcpy(&tempAlarm,&alarmLevel1,2);
    CodeUtils::getInstance().uint16ToBb(tempAlarm,buf+index);
    index = index + 2;
    /* 报警/事件类型2 */
    memcpy(&tempAlarm,&alarmLevel1,2);
    CodeUtils::getInstance().uint16ToBb(tempAlarm,buf+index);
    index = index + 2;

    G3RS485BoradcastInfo::toCode(buf);
    ret = index;
    return ret;
}

const G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel1 &G3RS485BoradcastInfo_BSDAlarm::getAlarmLevel1() const {
    return alarmLevel1;
}

void
G3RS485BoradcastInfo_BSDAlarm::setAlarmLevel1(const G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel1 &alarmLevel1) {
    G3RS485BoradcastInfo_BSDAlarm::alarmLevel1 = alarmLevel1;
}

const G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel2 &G3RS485BoradcastInfo_BSDAlarm::getAlarmLevel2() const {
    return alarmLevel2;
}

void
G3RS485BoradcastInfo_BSDAlarm::setAlarmLevel2(const G3RS485BoradcastInfo_BSDAlarm::RS485BSDAlarmLevel2 &alarmLevel2) {
    G3RS485BoradcastInfo_BSDAlarm::alarmLevel2 = alarmLevel2;
}
