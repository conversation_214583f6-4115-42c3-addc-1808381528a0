//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/6.
//

#ifndef VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_BSD_H
#define VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_BSD_H

#include "G3RS485BoradcastInfo.h"
#include "utils/CodeUtils.h"

class G3RS485BoradcastInfo_BSD : public G3RS485BoradcastInfo {
public:
    /* 报警区域有人 */
    const static int HAS_PEDESTRIAN = 1;
    /* 报警区域没人 */
    const static int NOT_PEDESTRIAN = 0;
    /* 镜头被遮挡 */
    const static int COVERED = 1;
    /* 镜头没被遮挡 */
    const static int NOT_COVER = 0;
    /* 镜头被bypass */
    const static int BYPASSED = 1;
    /* 镜头没被bypass */
    const static int NOT_BYPASS = 0;


    /* 左边报警区域1是否有人 */
    int leftAlarmArea1Status = 0;
    /* 左边报警区域2是否有人 */
    int leftAlarmArea2Status = 0;
    /* 右边报警区域1是否有人 */
    int rightAlarmArea1Status = 0;
    /* 右边报警区域2是否有人 */
    int rightAlarmArea2Status = 0;
    /* 前面报警区域1是否有人 */
    int forwardAlarmArea1Status = 0;
    /* 前面报警区域2是否有人 */
    int forwardAlarmArea2Status = 0;
    /* 后面报警区域1是否有人 */
    int backwardAlarmArea1Status = 0;
    /* 后面报警区域2是否有人 */
    int backwardAlarmArea2Status = 0;

    /* 左边报警区域1是否有车 */
    int leftAlarmArea1Status_vehicle = 0;
    /* 左边报警区域2是否有车 */
    int leftAlarmArea2Status_vehicle = 0;
    /* 右边报警区域1是否有车 */
    int rightAlarmArea1Status_vehicle = 0;
    /* 右边报警区域2是否有车 */
    int rightAlarmArea2Status_vehicle = 0;
    /* 前面报警区域1是否有车 */
    int forwardAlarmArea1Status_vehicle = 0;
    /* 前面报警区域2是否有车 */
    int forwardAlarmArea2Status_vehicle = 0;
    /* 后面报警区域1是否有车 */
    int backwardAlarmArea1Status_vehicle = 0;
    /* 后面报警区域2是否有车 */
    int backwardAlarmArea2Status_vehicle = 0;


    /* 左边镜头是否被遮挡 */
    int leftCoverStatus = 0;
    /* 右边镜头是否被遮挡 */
    int rightCoverStatus = 0;
    /* 前面镜头是否被遮挡 */
    int forwardCoverStatus = 0;
    /* 后面镜头是否被遮挡 */
    int backwardCoverStatus = 0;


    /* 左边镜头是否被Bypass */
    int leftBypassStatus = 0;
    /* 左边镜头是否被Bypass */
    int rightBypassStatus = 0;
    /* 前面镜头是否被Bypass */
    int forwardBypassStatus = 0;
    /* 后面镜头是否被Bypass */
    int backwardBypassStatus = 0;


    G3RS485BoradcastInfo_BSD();


    int toCode(uint8_t *buf) override;


    int getLeftAlarmArea1Status() const;

    void setLeftAlarmArea1Status(int leftAlarmArea1Status);

    int getLeftAlarmArea2Status() const;

    void setLeftAlarmArea2Status(int leftAlarmArea2Status);

    int getRightAlarmArea1Status() const;

    void setRightAlarmArea1Status(int rightAlarmArea1Status);

    int getRightAlarmArea2Status() const;

    void setRightAlarmArea2Status(int rightAlarmArea2Status);

    int getForwardAlarmArea1Status() const;

    void setForwardAlarmArea1Status(int forwardAlarmArea1Status);

    int getForwardAlarmArea2Status() const;

    void setForwardAlarmArea2Status(int forwardAlarmArea2Status);

    int getBackwardAlarmArea1Status() const;

    void setBackwardAlarmArea1Status(int backwardAlarmArea1Status);

    int getBackwardAlarmArea2Status() const;

    void setBackwardAlarmArea2Status(int backwardAlarmArea2Status);

    int getLeftCoverStatus() const;

    void setLeftCoverStatus(int leftCoverStatus);

    int getRightCoverStatus() const;

    void setRightCoverStatus(int rightCoverStatus);

    int getForwardCoverStatus() const;

    void setForwardCoverStatus(int forwardCoverStatus);

    int getBackwardCoverStatus() const;

    void setBackwardCoverStatus(int backwardCoverStatus);

    int getLeftBypassStatus() const;

    void setLeftBypassStatus(int leftBypassStatus);

    int getRightBypassStatus() const;

    void setRightBypassStatus(int rightBypassStatus);

    int getForwardBypassStatus() const;

    void setForwardBypassStatus(int forwardBypassStatus);

    int getBackwardBypassStatus() const;

    void setBackwardBypassStatus(int backwardBypassStatus);

    int getLeftAlarmArea1StatusVehicle() const;

    void setLeftAlarmArea1StatusVehicle(int leftAlarmArea1StatusVehicle);

    int getLeftAlarmArea2StatusVehicle() const;

    void setLeftAlarmArea2StatusVehicle(int leftAlarmArea2StatusVehicle);

    int getRightAlarmArea1StatusVehicle() const;

    void setRightAlarmArea1StatusVehicle(int rightAlarmArea1StatusVehicle);

    int getRightAlarmArea2StatusVehicle() const;

    void setRightAlarmArea2StatusVehicle(int rightAlarmArea2StatusVehicle);

    int getForwardAlarmArea1StatusVehicle() const;

    void setForwardAlarmArea1StatusVehicle(int forwardAlarmArea1StatusVehicle);

    int getForwardAlarmArea2StatusVehicle() const;

    void setForwardAlarmArea2StatusVehicle(int forwardAlarmArea2StatusVehicle);

    int getBackwardAlarmArea1StatusVehicle() const;

    void setBackwardAlarmArea1StatusVehicle(int backwardAlarmArea1StatusVehicle);

    int getBackwardAlarmArea2StatusVehicle() const;

    void setBackwardAlarmArea2StatusVehicle(int backwardAlarmArea2StatusVehicle);


private:
    /* 行人标志  BYTE   0 表示没人；1 表示有人   Bit0 左侧二级区域；bit1 右侧左侧二级区域; Bit2 前面二级区域；bit3 后面左侧二级区域  Bit4 左侧一级区域；bit5 右侧左侧一级区域; Bit6 前面一级区域；bit7 后面左侧一级区域*/
    uint8_t pedestrianStatus = 0x00;
    /* 摄像头遮挡状态BYTE   0 表示正常；1 表示被遮档    Bit0 指示左侧；bit1 指示右侧；Bit2 指示前面；bit3 指示后面。 */
    uint8_t cameraCoverStatus = 0x00;
    /* 摄像头Bypass状态BYTE   0 表示正常；1 表示被bapass    Bit0 指示左侧；bit1 指示右侧；Bit2 指示前面；bit3 指示后面。 */
    uint8_t cameraBypassStatus = 0x00;
    /* 车辆标志  BYTE   0表示报警区域内没车；1表示有车   Bit0 左侧二级区域；bit1 右侧左侧二级区域; Bit2 前面二级区域；bit3 后面左侧二级区域  Bit4 左侧一级区域；bit5 右侧左侧一级区域; Bit6 前面一级区域；bit7 后面左侧一级区域*/
    uint8_t vehicleStatus = 0x00;
    /* 保留字节 */
    uint8_t reserveByte[5] = {0x00, 0x00, 0x00, 0x00, 0x00};


};


#endif //VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_BSD_H
