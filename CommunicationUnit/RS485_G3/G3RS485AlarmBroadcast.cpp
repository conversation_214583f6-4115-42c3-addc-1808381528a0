//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//

#include "G3RS485AlarmBroadcast.h"

G3RS485AlarmBroadcast::G3RS485AlarmBroadcast() {
    setMsgId(RS485_MSGID_ALARM_BROADCAST);
}

G3RS485AlarmBroadcast::~G3RS485AlarmBroadcast() {

}

int G3RS485AlarmBroadcast::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 7;   /* 第8位就是正式内容了 */
    /* 定义一个长度变量  用来计算内容长度 */
    uint16_t contentLen = 0;
    listSize = dataList.size();
    /* 参数总数*/
    buf[index] = listSize;
    index++;
    contentLen += 1;

    /* 把内容一个个写进去 */
    for (int i = 0; i < listSize; i++) {
        int itemLen = dataList[i]->toCode(buf + index);
        index += itemLen;
        contentLen += itemL<PERSON>;
    }
    /* 再把内容长度写进去 */
    setContentL<PERSON>(contentLen);
    /* 加上头尾那些东西 */
    ret = G3RS485DataPackage::toCode(buf);
    return ret;
}

uint8_t G3RS485AlarmBroadcast::getListSize() const {
    return listSize;
}

void G3RS485AlarmBroadcast::setListSize(uint8_t listSize) {
    G3RS485AlarmBroadcast::listSize = listSize;
}


int G3RS485AlarmBroadcast::addDataItem(G3RS485BoradcastInfo &g3Rs485BoradcastInfo) {
    dataList.push_back(&g3Rs485BoradcastInfo);
    return 0;
}


