//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/11.
//

#include "G3RS485GPSInfo.h"

G3RS485GPSInfo::G3RS485GPSInfo() {
    setMsgId(RS485_MSGID_RESPOND_GPS_INFO);
}


int G3RS485GPSInfo::decode(uint8_t *buf, int len) {
    /* 直接从数据内容开始取 */
    int index = 7;
    /* 取GPS车速 */
    setGpsSpeed(CodeUtils::getInstance().BbToUint16(buf + index));
    index += 2;
    /* 取定位状态 */
    setStatus(buf[index]);
    index++;
    /* 取方向 */
    setDirection(CodeUtils::getInstance().BbToUint16(buf + index));
    index += 2;
    /* 取高程 */
    setAltitude(CodeUtils::getInstance().BbToUint16(buf + index));
    index += 2;
    /* 取纬度 */
    setLatitude(CodeUtils::getInstance().BbToUint32(buf + index));
    index += 4;
    /* 取经度 */
    setLongitude(CodeUtils::getInstance().BbToUint32(buf + index));
    index += 4;
    /* 取时间 */
    memcpy(gpsTimeDate, buf + index, 6);
    index += 6;
    /* 去数据内容之外的东西 */
    return G3RS485DataPackage::decode(buf, len);
}

std::string G3RS485GPSInfo::toString() {
    std::string gpsInfoStr;
    gpsInfoStr.append("GPS Info=[  ");

    gpsInfoStr.append("gpsSpeed:");
    gpsInfoStr.append(std::to_string(gpsSpeed));

    gpsInfoStr.append("  status:");
    gpsInfoStr.append(std::to_string(status));

    gpsInfoStr.append("  direction:");
    gpsInfoStr.append(std::to_string(direction));

    gpsInfoStr.append("  altitude:");
    gpsInfoStr.append(std::to_string(altitude));

    gpsInfoStr.append("  latitude:");
    gpsInfoStr.append(std::to_string(latitude));

    gpsInfoStr.append("  longitude:");
    gpsInfoStr.append(std::to_string(longitude));

    gpsInfoStr.append("  time:");
    gpsInfoStr.append(XuString::getInstance().bcd2String(gpsTimeDate, 6));


    gpsInfoStr.append("  ]");
    return gpsInfoStr;
}

uint16_t G3RS485GPSInfo::getGpsSpeed() const {
    return gpsSpeed;
}

void G3RS485GPSInfo::setGpsSpeed(uint16_t gpsSpeed) {
    G3RS485GPSInfo::gpsSpeed = gpsSpeed;
}

uint8_t G3RS485GPSInfo::getStatus() const {
    return status;
}

void G3RS485GPSInfo::setStatus(uint8_t status) {
    G3RS485GPSInfo::status = status;
}

uint16_t G3RS485GPSInfo::getDirection() const {
    return direction;
}

void G3RS485GPSInfo::setDirection(uint16_t direction) {
    G3RS485GPSInfo::direction = direction;
}

int16_t G3RS485GPSInfo::getAltitude() const {
    return altitude;
}

void G3RS485GPSInfo::setAltitude(int16_t altitude) {
    G3RS485GPSInfo::altitude = altitude;
}

int32_t G3RS485GPSInfo::getLatitude() const {
    return latitude;
}

void G3RS485GPSInfo::setLatitude(int32_t latitude) {
    G3RS485GPSInfo::latitude = latitude;
}

int32_t G3RS485GPSInfo::getLongitude() const {
    return longitude;
}

void G3RS485GPSInfo::setLongitude(int32_t longitude) {
    G3RS485GPSInfo::longitude = longitude;
}

const uint8_t *G3RS485GPSInfo::getGpsTimeDate() const {
    return gpsTimeDate;
}


