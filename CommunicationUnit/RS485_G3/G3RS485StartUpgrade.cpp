//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/15.
//

#include "G3RS485StartUpgrade.h"

G3RS485StartUpgrade::G3RS485StartUpgrade() {
    setMsgId(RS485_MSGID_START_UPGRADE);

}

uint8_t G3RS485StartUpgrade::getPeripheralId() const {
    return peripheralId;
}

void G3RS485StartUpgrade::setPeripheralId(uint8_t peripheralId) {
    G3RS485StartUpgrade::peripheralId = peripheralId;
}

int G3RS485StartUpgrade::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 7;   /* 第8位就是正式内容了 */
    /* 设置内容长度 */
    setContentLen(1);
    /* 塞入外设编号 */
    buf[index] = peripheralId;
    index++;
    /* 加上头尾那些东西 */
    ret = G3RS485DataPackage::toCode(buf);
    return ret;
}

int G3RS485StartUpgrade::decode(uint8_t *buf, int len) {
    /* 直接从数据内容开始取 */
    int index = 7;
    /* 取外设ID */
    setPeripheralId(buf[index]);
    index++;
    /* 去数据内容之外的东西 */
    return G3RS485DataPackage::decode(buf, len);
}
