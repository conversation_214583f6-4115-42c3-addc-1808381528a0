//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//

#ifndef VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_H
#define VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_H


#include <cstdint>
#include "utils/CodeUtils.h"

class G3RS485BoradcastInfo {
public:

    /* ADAS数据包 */
    const uint16_t DATAID_ADAS = 0x0001;
    /* DSM数据包，预留 */
    const uint16_t DATAID_DSM = 0x0002;
    /* BSD数据包 */
    const uint16_t DATAID_BSD = 0x0003;
    /* GPS数据包 */
    const uint16_t DATAID_GPS = 0x0004;
    /* 系统错误信息 */
    const uint16_t DATAID_SYSINFO = 0x0006;
    /* R151区域显示 */
    const uint16_t DATAID_R151INFO = 0x0007;
    /* R159区域显示 */
    const uint16_t DATAID_R159INFO = 0x0008;
    /* 车辆运行信息 */
    const uint16_t DATAID_CARSTATUS = 0x0010;
    /* ADAS告警包 */
    const uint16_t DATAID_ADAS_EVENT = 0x0011;
    /* DSM告警包 */
    const uint16_t DATAID_DSM_EVENT = 0x0012;
    /* BSD告警包 */
    const uint16_t DATAID_BSD_EVENT = 0x0013;
    /* 摄像头状态 */
    const uint16_t DATAID_CAMERA_STATUS = 0x0014;
    /* 多路视频切换 */
    const uint16_t DATAID_VIDEO_CHANGE = 0x0101;




    uint16_t getDataId() const;

    void setDataId(uint16_t dataId);

    uint8_t getDatalen() const;

    void setDatalen(uint8_t datalen);

    /**
     * 转成16进制数组
     *
     * @param buf ： 存放数据的内存指针
     *
     *
     * @return 转换之后的数据长度
     *
     * */
    virtual int toCode(uint8_t *buf);

private:
    uint16_t dataId;
    uint8_t datalen;
};


#endif //VIS_G3_SOFTWARE_G3RS485BORADCASTINFO_H
