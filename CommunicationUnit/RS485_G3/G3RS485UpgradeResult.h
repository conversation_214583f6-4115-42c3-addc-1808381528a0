//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/18.
//

#ifndef VIS_G3_SOFTWARE_G3RS485UPGRADERESULT_H
#define VIS_G3_SOFTWARE_G3RS485UPGRADERESULT_H

#include "G3RS485DataPackage.h"

class G3RS485UpgradeResult : public G3RS485DataPackage {
public:
    /* 升级成功 */
    static const int UPGRADE_RESULT_SUCCESS = 0;
    /* 升级失败 */
    static const int UPGRADE_RESULT_FAILED = 1;


    G3RS485UpgradeResult();

    int toCode(uint8_t *buf) override;

    int decode(uint8_t *buf, int len) override;


    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

    uint8_t getUpgradeResult() const;

    void setUpgradeResult(uint8_t upgradeResult);


private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;
    /* 升级结果 */
    uint8_t upgradeResult = 0xFF;
};


#endif //VIS_G3_SOFTWARE_G3RS485UPGRADERESULT_H
