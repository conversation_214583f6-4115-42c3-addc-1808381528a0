//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/8.
//

#ifndef VIS_G3_SOFTWARE_G3RS485SETDISPALYPARAMS_H
#define VIS_G3_SOFTWARE_G3RS485SETDISPALYPARAMS_H

#include "G3RS485DataPackage.h"
class G3RS485SetDispalyParams : public G3RS485DataPackage {
public:
    G3RS485SetDispalyParams();

    int toCode(uint8_t *buf) override;

    uint16_t getDisplayMode() const;

    void setDisplayMode(uint16_t displayMode);

    uint8_t getIconMode() const;

    void setIconMode(uint8_t iconMode);

    const uint8_t *getReserved() const;

private:
    uint16_t displayMode = 0xFFFF;
    uint8_t iconMode = 0xFF;
    uint8_t reserved[3] = {0x00};


};


#endif //VIS_G3_SOFTWARE_G3RS485SETDISPALYPARAMS_H
