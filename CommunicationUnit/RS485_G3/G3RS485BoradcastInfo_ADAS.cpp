//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/2/4.
//

#include "G3RS485BoradcastInfo_ADAS.h"

G3RS485BoradcastInfo_ADAS::G3RS485BoradcastInfo_ADAS() {
    setDataId(DATAID_ADAS);
    setDatalen(13);
}

int G3RS485BoradcastInfo_ADAS::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 3; //前面留三个字节用来放ID跟长度

    /* 塞车距监控时间 */
    buf[index] = vehicleCollisionTime;
    index = index + 1;
    /* 塞车距监控距离 */
    CodeUtils::getInstance().uint16ToBb(vehicleYDistance, buf + index);
    index = index + 2;
    /* 塞左车道线距离 */
    CodeUtils::getInstance().uint16ToBb(lineXDistance_left, buf + index);
    index = index + 2;
    /* 塞右车道线距离 */
    CodeUtils::getInstance().uint16ToBb(lineXDistance_right, buf + index);
    index = index + 2;
    /* 塞行人碰撞时间 */
    buf[index] = pedestrianCollisionTime;
    index = index + 1;
    /* 塞行人距离 */
    CodeUtils::getInstance().uint16ToBb(pedestrianYDistance, buf + index);
    index = index + 2;
    /* 塞状态 */
    memcpy(buf+index,&alarmStatus, sizeof(alarmStatus));
    index = index + 1;
    /* 速度上限 */
    buf[index] = speedLimit_max;
    index = index + 1;
    /* 速度下限 */
    buf[index] = speedLimit_min;
    index = index + 1;

    G3RS485BoradcastInfo::toCode(buf);
    ret = index;
    return ret;
}

uint8_t G3RS485BoradcastInfo_ADAS::getVehicleCollisionTime() const {
    return vehicleCollisionTime;
}

void G3RS485BoradcastInfo_ADAS::setVehicleCollisionTime(uint8_t vehicleCollisionTime) {
    G3RS485BoradcastInfo_ADAS::vehicleCollisionTime = vehicleCollisionTime;
}

uint16_t G3RS485BoradcastInfo_ADAS::getVehicleYDistance() const {
    return vehicleYDistance;
}

void G3RS485BoradcastInfo_ADAS::setVehicleYDistance(uint16_t vehicleYDistance) {
    G3RS485BoradcastInfo_ADAS::vehicleYDistance = vehicleYDistance;
}

int16_t G3RS485BoradcastInfo_ADAS::getLineXDistanceLeft() const {
    return lineXDistance_left;
}

void G3RS485BoradcastInfo_ADAS::setLineXDistanceLeft(int16_t lineXDistanceLeft) {
    lineXDistance_left = lineXDistanceLeft;
}

int16_t G3RS485BoradcastInfo_ADAS::getLineXDistanceRight() const {
    return lineXDistance_right;
}

void G3RS485BoradcastInfo_ADAS::setLineXDistanceRight(int16_t lineXDistanceRight) {
    lineXDistance_right = lineXDistanceRight;
}

uint8_t G3RS485BoradcastInfo_ADAS::getPedestrianCollisionTime() const {
    return pedestrianCollisionTime;
}

void G3RS485BoradcastInfo_ADAS::setPedestrianCollisionTime(uint8_t pedestrianCollisionTime) {
    G3RS485BoradcastInfo_ADAS::pedestrianCollisionTime = pedestrianCollisionTime;
}

uint16_t G3RS485BoradcastInfo_ADAS::getPedestrianYDistance() const {
    return pedestrianYDistance;
}

void G3RS485BoradcastInfo_ADAS::setPedestrianYDistance(uint16_t pedestrianYDistance) {
    G3RS485BoradcastInfo_ADAS::pedestrianYDistance = pedestrianYDistance;
}

uint8_t G3RS485BoradcastInfo_ADAS::getSpeedLimitMax() const {
    return speedLimit_max;
}

void G3RS485BoradcastInfo_ADAS::setSpeedLimitMax(uint8_t speedLimitMax) {
    speedLimit_max = speedLimitMax;
}

uint8_t G3RS485BoradcastInfo_ADAS::getSpeedLimitMin() const {
    return speedLimit_min;
}

void G3RS485BoradcastInfo_ADAS::setSpeedLimitMin(uint8_t speedLimitMin) {
    speedLimit_min = speedLimitMin;
}

const ADASDetectionInfo_alarmStatus &G3RS485BoradcastInfo_ADAS::getAlarmStatus() const {
    return alarmStatus;
}

void G3RS485BoradcastInfo_ADAS::setAlarmStatus(const ADASDetectionInfo_alarmStatus &alarmStatus) {
    G3RS485BoradcastInfo_ADAS::alarmStatus = alarmStatus;
}
