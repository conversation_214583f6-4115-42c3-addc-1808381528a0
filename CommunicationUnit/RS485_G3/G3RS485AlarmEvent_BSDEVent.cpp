//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/28.
//


#include "G3RS485AlarmEvent_BSDEVent.h"

G3RS485AlarmEvent_BSDEVent::G3RS485AlarmEvent_BSDEVent() {
    setDataId(DATAID_BSD_ALARM_EVENT);
    setDataLen(9);
}

G3RS485AlarmEvent_BSDEVent::~G3RS485AlarmEvent_BSDEVent() {

}

int G3RS485AlarmEvent_BSDEVent::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度 */
    buf[index] = getDataLen();
    index++;
    /* 标志状态 */
    buf[index] = getEventStatus();
    index++;
    /* 报警/事件类型 */
    buf[index] = getEventType();
    index++;
    /* 碰撞时间 */
    CodeUtils::getInstance().uint16ToBb(getDistance(), buf + index);
    index += 2;
    /* 报警级别 */
    buf[index] = getEventLevel();
    index++;
    /* 预留 */
    memcpy(buf + index, getReserved(), 4);
    index += 4;

    ret = index;
    return ret;
}

uint8_t G3RS485AlarmEvent_BSDEVent::getEventStatus() const {
    return eventStatus;
}

void G3RS485AlarmEvent_BSDEVent::setEventStatus(uint8_t eventStatus) {
    G3RS485AlarmEvent_BSDEVent::eventStatus = eventStatus;
}

uint8_t G3RS485AlarmEvent_BSDEVent::getEventType() const {
    return eventType;
}

void G3RS485AlarmEvent_BSDEVent::setEventType(uint8_t eventType) {
    G3RS485AlarmEvent_BSDEVent::eventType = eventType;
}

uint16_t G3RS485AlarmEvent_BSDEVent::getDistance() const {
    return distance;
}

void G3RS485AlarmEvent_BSDEVent::setDistance(uint16_t distance) {
    G3RS485AlarmEvent_BSDEVent::distance = distance;
}

uint16_t G3RS485AlarmEvent_BSDEVent::getEventLevel() const {
    return EventLevel;
}

void G3RS485AlarmEvent_BSDEVent::setEventLevel(uint16_t eventLevel) {
    EventLevel = eventLevel;
}

const uint8_t *G3RS485AlarmEvent_BSDEVent::getReserved() const {
    return reserved;
}
