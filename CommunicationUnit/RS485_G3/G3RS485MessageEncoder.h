//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
//

#ifndef VIS_G3_SOFTWARE_G3RS485MESSAGEENCODER_H
#define VIS_G3_SOFTWARE_G3RS485MESSAGEENCODER_H


#include "G3RS485AlarmBroadcast.h"
#include "G3RS485GetPeripheralVersion.h"
#include "G3RS485GetGPSInfo.h"
#include "G3RS485AlarmEvent.h"
#include "G3RS485StartUpgrade.h"
#include "G3RS485RespondUpgradeFileData.h"
#include "G3RS485SetDispalyParams.h"
#include "G3RS485StartUpgradeL4.h"
#include "G3RS485RespondL4UpgradeFileData.h"
#include "G3RS485ResponeUpgradeL4Result.h"

class G3RS485MessageEncoder {
public:

    int generateG3RS485AlarmBroadcast(uint8_t *buf, G3RS485AlarmBroadcast &g3485AlarmBroadcast);

    int generateG3RS485AlarmEvent(uint8_t *buf, G3RS485AlarmEvent &g3Rs485AlarmEvent);

    int generateGetPeripheralVersion(const int peripheralId, uint8_t *buf);

    int generateGetGPSInfo(const int peripheralId, uint8_t *buf);

    int generateStartUpgrade(uint8_t *buf, G3RS485StartUpgrade &startUpgrade);

    int generateRespondUpgradeFileData(uint8_t *buf, G3RS485RespondUpgradeFileData &respondUpgradeFileData);

    int generateSetDispalyParams(uint8_t *buf, G3RS485SetDispalyParams &setDispalyParams);

    int generateStartUpgradeL4(uint8_t *buf, G3RS485StartUpgradeL4 &startUpgrade);

    int generateRespondL4UpgradeFileData(uint8_t *buf, G3RS485RespondL4UpgradeFileData &l4UpgradeFileData);

    int generateRespondL4UpgradeResult(uint8_t *buf, G3RS485ResponeUpgradeL4Result &upgradeL4Result);

    uint16_t getFlowId();

private:
    /* 流水号 */
    uint32_t flowId = -1;


};


#endif //VIS_G3_SOFTWARE_G3RS485MESSAGEENCODER_H
