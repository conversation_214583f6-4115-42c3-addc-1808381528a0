//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/12.
//

#include "G3RS485BoradcastInfo_DSMAlarm.h"

G3RS485BoradcastInfo_DSMAlarm::G3RS485BoradcastInfo_DSMAlarm() {
    setDataId(DATAID_DSM_EVENT);
    setDatalen(4);
}

int G3RS485BoradcastInfo_DSMAlarm::toCode(uint8_t *buf) {
    int ret = -1;
    int index = 3; //前面留三个字节用来放ID跟长度

    uint16_t tempAlarm = 0;
    /* 报警/事件类型1 */
    memcpy(&tempAlarm,&alarmType1,2);
    CodeUtils::getInstance().uint16ToBb(tempAlarm,buf+index);
    index = index + 2;
    /* 报警/事件类型2 */
    memcpy(&tempAlarm,&alarmType2,2);
    CodeUtils::getInstance().uint16ToBb(tempAlarm,buf+index);
    index = index + 2;
    
    G3RS485BoradcastInfo::toCode(buf);
    ret = index;
    return ret;
}

const G3RS485BoradcastInfo_DSMAlarm::RS485DSMAlarmType1 &G3RS485BoradcastInfo_DSMAlarm::getAlarmType1() const {
    return alarmType1;
}

void G3RS485BoradcastInfo_DSMAlarm::setAlarmType1(const G3RS485BoradcastInfo_DSMAlarm::RS485DSMAlarmType1 &alarmType1) {
    G3RS485BoradcastInfo_DSMAlarm::alarmType1 = alarmType1;
}

uint16_t G3RS485BoradcastInfo_DSMAlarm::getAlarmType2() const {
    return alarmType2;
}

void G3RS485BoradcastInfo_DSMAlarm::setAlarmType2(uint16_t alarmType2) {
    G3RS485BoradcastInfo_DSMAlarm::alarmType2 = alarmType2;
}
