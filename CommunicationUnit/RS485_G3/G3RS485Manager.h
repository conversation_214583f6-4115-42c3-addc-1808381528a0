//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/13.
//

#ifndef VIS_G3_SOFTWARE_G3RS485MANAGER_H
#define VIS_G3_SOFTWARE_G3RS485MANAGER_H


#include <Poco/Runnable.h>
#include "CommunicationDataCallback.h"
#include "G3RS485MessageEncoder.h"
#include "G3RS485MessageDeocder.h"
#include "G3RS485BoradcastInfo_BSD.h"
#include "G3RS485BoradcastInfo_SystemInfo.h"
#include "G3DetectionDefind.h"
#include <mutex>
#include "G3RS485AlarmEvent.h"
#include  "tdShareDefine.h"
#include "dasShareDefine.h"
#include "G3RS485BoradcastInfo_ADAS.h"
#include "G3RS485BoradcastInfo_ADASAlarm.h"
#include "G3RS485BoradcastInfo_BSDAlarm.h"
#include "G3RS485BoradcastInfo_R151Info.h"
#include "G3RS485BoradcastInfo_VehicleRunningStatus.h"
#include "G3RS485BoradcastInfo_R159Info.h"

namespace vis {

    class G3RS485Manager : public Poco::Runnable, public CommunicationDataCallback {
    public:

        void init(CommunicationDataCallback &communicationDataCallback);

        /**
         * 设置从RS485总线上读取到的数据
         *
         * @param buf : 从RS485总线上读取到的数据的指针
         * @param len ：数据长度
         *
         * */
        void setRS485BusData(const uint8_t *buf, const int len);

        /**
         * 设置从识别模块拿到的识别信息（BSD）
         *
         * @param objectInfo ： 识别信息的结构体
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

        /**
         * 设置从识别模块拿到的识别信息（ADAS）
         *
         * @param objectInfo ： 识别信息的结构体
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);


        /**
         * 设置从识别模块拿到的报警事件
         *
         * @param cameraId : 相机ID
         * @param detectType : 识别算法类型
         * @param eventCode ： 事件码
         *
         * */
        void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

        void run() override;


        /**
         * 设置转向信号
         *
         * @param trunL ： 是否左转
         * @param trunR ： 是否右转
         */
        void setTurnSignal(const bool trunL,const bool trunR);

        /**
        * 设置车速
        *
        * @param speed : 速度  （单位：KM\H）
        * @param baseOf ： 来源    （0：CAN  1：UART  2：GPS  3:以太网口）
        *
        * @return 无
        * */
        void setSpeed(const float speed, const int baseOf);

        /**
         *
         * 设置实时车况
         *
         * @param vehicleRealtimeStatus : 实时车况
         */
        void setVehicleRealtimeStatus(Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 开启升级
         *
         * @param peripheralId : 外设ID
         *
         * */
        void startUpgrade(const int peripheralId);


        /**
         * 升级一下RS485上面的外设
         * @param upgradeFilePath : 升级文件路径
         *
         * @return 结果  0：成功  其他：失败
         */
        int setUpgradeRS485(const int peripheralId,const std::string &upgradeFilePath);

        /**
         * 配置外设显示参数
         *
         * @param displayMode ：显示模板
         * @param iconMode ：显示图标子集编号
         * @return 结果  0：成功  其他：失败
         */
        int setDispalyParams(const int displayMode,const int iconMode);

        /**
         * 获取RS485的外设版本号
         *
         * @param peripheralId ： 外设ID
         */
        void getRS485PeripheralVersion(const int peripheralId);

        /**
         * 设置从识别模块拿到的识别信息(镜头状态)
         *
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectInfo_CameraStatus(G3DetectionResult &detectionResult);

//        /**
//         * 发送除了周期报警之外的RS485信息
//         */
//        void sendOtherRS485Msg();


        void onGetGPSInfoFromRS485(G3RS485GPSInfo &gpsInfo) override;

        void onGetGetUpgradeFileDataFromRS485(G3RS485GetUpgradeFileData &getUpgradeFileData) override;

        void onGetUpgradeResultFromRS485(G3RS485UpgradeResult &upgradeResult) override;

        void onGetSetDispalyParamsResultFromRS485(G3RS485SetDispalyParamsResult &setDispalyParamsResult) override;

        void onGetPeripheralVersion(const uint8_t peripheral, const char *softwarVersion, const char *protocoVersion) override;

        void onGetStartUpgradeL4ResultFromRS485(G3RS485StartUpgradeL4Result &startUpgradeL4Result) override;

        void onGetGetL4UpgradeFileDataFromRS485(G3RS485GetL4UpgradeFileData &getlL4UpgradeFileData) override;

        void onGetGetL4UpgradeResultFromRS485(G3RS485UpgradeL4Result &upgradeL4Result) override;







    private:
        struct RS485OtherMsgInfo{
            /* RS485消息的内容 */
            uint8_t msgContent[100] = {};
            /* RS485消息的内容的长度 */
            int msgLen = 0;
            /* 发送完后要等待多少毫秒 */
            int sleepTime = 0;
        };


        /* 每个报警最大的485发送次数 */
        const int MAX_ALARM_RS485_SEND_TIMES = 3;
        struct RS485AlarmEventInfo{
            int eventCode = 0;
            int sendSum = 0;
            AlarmEventInfo eventInfo = {};
        };


        CommunicationDataCallback *callback;

        /* G3的RS485总线数据的解析类 */
        G3RS485MessageDeocder rs485MessageDecoder;

        G3RS485MessageEncoder rs485MessageEncoder;

        /* */
        std::vector<G3DetectionResult> curDetectionResultList;

        std::mutex detectionInfoLock;

        /* 是否需要RS485的操作 */
        bool isNeedStopG3RS485Manager = true;

        /* 是否已经拿到L3的版本号了 */
        bool hasL3Version = false;

        std::vector<RS485AlarmEventInfo> alarmEventInfoList;

        /* 当前的车况 */
        Vehicle_RealtimeStatus curVehicleStatus = {};



        /* 是否正在升级中 */
        bool upgradeing = false;

        uint8_t rs485UpgradePacketdata[700] = {0x00};

        uint8_t rs485Upgradedata[1400] = {0x00};

        uint8_t *rs485L4UpgradePacketdata = nullptr;
        uint8_t *rs485L4Upgradedata = nullptr;

        uint8_t *upgradeFileData;

        int upgradeFileDataLen = -1;

        std::vector<RS485OtherMsgInfo> otherMsgList;

        /* 当前正在升级的外设ID */
        int curUpgradePeripheralId = -1;
        /* 开启升级的时间 */
        uint64_t startUpgradeTime = 0;
        /* 最大允许等待升级请求的时间 */
        const int MAX_UPGRADE_WAIT_TIME = 20 * 1000;





    };

}

#endif //VIS_G3_SOFTWARE_G3RS485MANAGER_H
