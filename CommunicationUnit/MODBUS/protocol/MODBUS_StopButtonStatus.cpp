//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/21.
//

#include "MODBUS_StopButtonStatus.h"

namespace vis {
    int MODBUS_StopButtonStatus::decode(const uint8_t *buf, const int len) {
        todDecode(buf,len);
        mainButtonOn = ((buf[3] & 0x01) == 0x01);
        extensionButtonOn = ((buf[3] & 0x02) == 0x02);
        return 0;
    }


    bool MODBUS_StopButtonStatus::isExtensionButtonOn() const {
        return extensionButtonOn;
    }

    void MODBUS_StopButtonStatus::setExtensionButtonOn(bool extensionButtonOn) {
        MODBUS_StopButtonStatus::extensionButtonOn = extensionButtonOn;
    }

    bool MODBUS_StopButtonStatus::isMainButtonOn() const {
        return mainButtonOn;
    }

    void MODBUS_StopButtonStatus::setMainButtonOn(bool mainButtonOn) {
        MODBUS_StopButtonStatus::mainButtonOn = mainButtonOn;
    }
} // vis