//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/17.
//

#ifndef VIS_G3_SOFTWARE_G3SOCKETCOMMANAGER_H
#define VIS_G3_SOFTWARE_G3SOCKETCOMMANAGER_H

#include <Poco/ThreadPool.h>
#include "CommunicationDataCallback.h"
#include "ConfigureMessageDecoder.h"
#include "ConfigureMessageEncoder.h"
#include  "tdShareDefine.h"
#include "DetectionInfo.h"
#include <mutex>
#include "G3DetectionDefind.h"
#include "G3_Configuration.h"
#include "dasShareDefine.h"

namespace vis {

    class G3SocketComManager : public Poco::Runnable, public CommunicationDataCallback {
    public:

        /* 把原始的BSD识别信息跟整理好的识别信息放到一起，用以区分算法决策类型 */
        struct BSDDetectionInfoAll {
            G3DetectionResult detectionResult;
            td::objectInfo_t curobjectInfo;
        };

        /* 把原始的ADAS识别信息跟整理好的识别信息放到一起，用以区分算法决策类型 */
        struct ADASDetectionInfoAll {
            G3DetectionResult detectionResult;
            das::objectInfo_t curobjectInfo;
        };

        /* 把原始的条码别信息跟整理好的识别信息放到一起，用以区分算法决策类型 */
        struct BarCodeDetectionInfoAll {
            G3DetectionResult detectionResult;
            std::vector<BarCodeInfo> barcodeDetectInfoList;
        };



        G3SocketComManager();

        ~G3SocketComManager();

        void init(CommunicationDataCallback &communicationDataCallback);

        /**
         * 设置从识别模块拿到的识别信息
         *
         * @param objectInfo ： 识别信息的结构体
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

        /**
         * 设置从识别模块拿到的识别信息(DSM)
         *
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDSMInfo(G3DetectionResult &detectionInfo);

        /**
         * 设置从识别模块拿到的识别信息(手势)
         *
         * @param objectInfo ： 识别信息的结构体
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectionInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

        /**
         * 设置从识别模块拿到的报警事件
         *
         * @param cameraId : 相机ID
         * @param detectType : 识别算法类型
         * @param eventCode ： 事件码
         *
         * */
        void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

        /**
         * 设置从socket收到的配置客户端发过来的数据
         *
         * @param buf : 数据内存指针
         * @param len : 数据长度
         *
         * */
        void setSocketDataToConfigure(const char *ip, const int port, const uint8_t *buf, int len);


        /**
         * 设置开启设备升级的结果
         *
         * @param startG3DeviceUpgrade : 开启升级的消息内容
         * @param tcpport : 开启的TCP端口
         * @param ip ： 客户端的IP
         * @param port ： 客户端的端口
         *
         * */
        void setStartUpgardeResult(StartG3DeviceUpgrade &startG3DeviceUpgrade, int tcpport, int result, const char *ip,
                                   const int port);

        /**
         * 设置设备报警的结果
         *
         * @param deviceId : 设备ID
         * @param result : 结果
         * @param ip ： 客户端的IP
         * @param port ： 客户端的端口
         *
         * */
        void setUpgardeResult(const int deviceId, const int result, const char *ip, const int port);

        /**
         * 设置最新的DVR文件列表
         *
         * @param dvrFileList ： DVR文件列表
         * @return 结果  0：成功   其他：失败
         */
        int setNewDVRFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &dvrList);

        /**
         * 设置最新的报警文件列表
         *
         * @param dvrFileList ： DVR文件列表
         * @return 结果  0：成功   其他：失败
         */
        int setNewWarFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &warList);

        /**
         * 把MP4文件从旧到新排序
         * @param fileList ： 需要排序的vector
         *
         * @return 结果  0：成功  其他：失败
         */
        int sortMp4FileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &fileList);

        /* 把MP4文件从旧到新排序(根据录制时间) */
        static bool
        doMP4FileAscending(PCConfigureDataPacket::MP4FileInfo infoA, PCConfigureDataPacket::MP4FileInfo infoB);

        /**
         * 设置从识别模块拿到的识别信息(Adas)
         *
         * @param objectInfo ： 识别信息的结构体
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

        /**
         * 发送配置外设显示参数的结果去以太网
         *
         * @param devicesId : 设备ID
         * @param result : 结果
         * @param ip ： 客户端的IP
         * @param port ： 客户端的端口
         */
        void setDispalyParamsResultToSocket(const int devicesId, const int result, const char *ip, const int port);

        /**
         * 设置从识别模块拿到的识别信息(镜头状态)
         *
         * @param detectionResult : 整理过后的识别结果
         *
         * */
        void setDetectInfo_CameraStatus(G3DetectionResult &detectionResult);

        float getCurSpeed() const;

        void setCurSpeed(float curSpeed);

        /**
         * 检查镜头是否全黑并记录下来
         *
         * @param detectionResult
         */
        void checkCameraFullBlackStatus(G3DetectionResult &detectionResult);

        /**
         * 设置从识别模块拿到的识别信息(条码)
         *
         * @param detectionInfo ： 封装好的识别结果
         * @param barCodeInfoList : 条码信息列表
         *
         * */
        void setDetectInfo_BarCode(G3DetectionResult &detectionResult, std::vector<BarCodeInfo> &barCodeInfoList);

        /**
         * 设置最新的Gsensor数据
         *
         * @param value_x ： X轴的加速度
         * @param value_y ： Y轴的加速度
         * @param value_z ： Z轴的加速度
         */
        void setGSensorData(float value_x, float value_y, float value_z);

        /**
         *
         * 设置实时车况
         *
         * @param vehicleRealtimeStatus : 实时车况
         */
        void setVehicleRealtimeStatus(Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 设置IO口状态
         *
         * @param portNum ： IO口编码
         * @param value ： 电平内容
         * @param valueLen ： 电平内容长度
         *
         * */
        void setIOStatus(const int portNum, const char *value, const int valueLen);


        void run() override;

        void onGetHeartBeat(Heartbeat &heartbeat, const char *ip, const int port) override;

        void onGetQequestG3Config(QequestG3Config &qequestG3Config, const char *ip, const int port) override;

        void onGetSetG3Config(SetG3Config &setG3Config, const char *ip, const int port) override;

        void onGetStartUDPRealview(StartUDPRealview &startUDPRealview, const char *ip, const int port) override;

        void onGetUniversalAnswer(UniversalAnswer &universalAnswer, const char *ip, const int port) override;

        void onGetRealtimeVehicleStatus(RealTimeVehicleStatus &vehicleStatus, const char *ip, const int port) override;

        void
        onGetStartG3DeviceUpgrade(StartG3DeviceUpgrade &startG3DeviceUpgrade, const char *ip, const int port) override;

        int onGetSetG3SystemTime(SetG3SystemTime &setG3SystemTime, const char *ip, const int port) override;

        void onGetGetG3AndG4DeviceVersion(GetG3AndG4DevicesVersion &getG3AndG4DevicesVersion, const char *ip,
                                          const int port) override;

        void onGetGetG3AndG4CPUSerialNumber(GetCPUSerialNum &getCpuSerialNum, const char *ip, const int port) override;

        void onGetSetG3UUID(SetG3UUID &setG3Uuid, const char *ip, const int port) override;

        void onGetGetG3UUID(GetG3UUID &getG3Uuid, const char *ip, const int port) override;

        void onGetGetG3UTCTime(GetG3UTCTime &getG3UtcTime, const char *ip, const int port) override;

        void onGetGetG3Mp4FileList(GetG3Mp4FileList &getG3Mp4FileList, const char *ip, const int port) override;

        std::vector<PCConfigureDataPacket::MP4FileInfo>
        onGetG3Mp4FileOpt(G3Mp4FileOpt &g3Mp4FileOpt, const char *ip, const int port) override;

        StartG3FileDownloadResult
        onGetStartG3FileDownload(StartG3FileDownload &startG3FileDownload, const char *ip, const int port) override;

        void onGetStartG3DebugMode(StartG3DebugMode &startG3DebugMode, const char *ip, const int port) override;

        void onGetSetFunctionLock(SetFunctionLock &setFunctionLock, const char *ip, const int port) override;

        void
        onGetGetFunctionLockInfo(GetFunctionLockInfo &getFunctionLockInfo, const char *ip, const int port) override;

        void onGetRestartMRV220(RestartG3 &restartG3, const char *ip, const int port) override;

        int onGetStartUDPRealviewSeparate(StartUDPRealview_Separate &startUdpRealview, const char *ip,
                                          const int port) override;

        int onGetSetPeripheralDisplayParams(SetPeripheralDisplayParams displayParams, const char *ip, const int port) override;

        void onGetGetMRV220LogFileList(GetMRV220LogFileList &getMrv220LogFileList, const char *ip, const int port) override;

        StartMRV220LogFileDownloadResult onGetStartMRV220LogFileDownload(StartMRV220LogFileDownload &startMrv220LogFileDownload, const char *ip, const int port) override;

        void onGetGetMultimediaFileEncryptionKey(GetMultimediaFileEncryptionKey &getMultimediaFileEncryptionKey, const char *ip, const int port) override;

        int onGetSetMultimediaFileEncryptionKey(SetMultimediaFileEncryptionKey &setMultimediaFileEncryptionKey, const char *ip, const int port) override;

        int onGetGetAlarmSoundFileConf(GetAlarmSoundFileConf getAlarmSoundFileConf, const char *ip, const int port) override;

        void onGetGetCameraInputTypeFilterConfig(GetCameraInputTypeFilterConfig &getCameraInputTypeFilterConfig, const char *ip, const int port) override;

        int onGetSetCameraInputTypeFilterConfig(SetCameraInputTypeFilterConfig &setCameraInputTypeFilterConfig, const char *ip, const int port) override;

        void onGetGetCameraReduceEffectInfoFromSocket(GetCameraReduceEffectInfo &getCameraReduceEffectInfo, const char *ip, const int port) override;

        int onGetSetCameraReduceEffectInfoFromSocket(SetCameraReduceEffectInfo &setCameraReduceEffectInfo, const char *ip, const int port) override;

        int onGetSetProductionTestingSwitchFromSocket(SetProductionTestingSwitch &setProductionTestingSwitch, const char *ip, const int port) override;

        int onGetTCPRealviewOptFromSocket(TCPRealviewOpt &tcpRealviewOpt, const char *ip, const int port) override;





    private:
        bool isNeedStop = true;
        CommunicationDataCallback *callback;
        ConfigureMessageDecoder decoder;
        ConfigureMessageEncoder encoder;
        DetectionInfo curDetectionInfo;
        std::mutex detectionInfoLock;
        /* 系统信息 */
        DetectionInfo_SystemInfo systemInfo;
        /* 当前的车速 */
        float curSpeed = -1;
        /* 算法耗时信息 */
        DetectionInfo_AlgDetectTime algDetectTime;

        /* 当前的IO状态 */
        DetectionInfo_GPIOIOStatus::IOStatusInfo ioStatus;
        /* 当前的车辆状态 */
        DetectionInfo_SignalAndStatus::VehicleSignal vehicleSignal;
        /* 当前的设备信息 */
        DetectionInfo_SignalAndStatus::DeviceStatus deviceStatus;


        /* DVR文件列表 */
        std::vector<PCConfigureDataPacket::MP4FileInfo> dvrFileList;
        std::mutex dvrFileListLock;
        /* 报警文件列表 */
        std::vector<PCConfigureDataPacket::MP4FileInfo> warFileList;
        std::mutex warFileListLock;

        /* 临时存放的BSD报警信息 */
        std::vector<BSDDetectionInfoAll> bsdDetectionInfoList;
        /* 临时存放的ADAS报警信息 */
        std::vector<ADASDetectionInfoAll> adasDetectionInfoList;
        /* 临时存放的条码识别信息 */
        std::vector<BarCodeDetectionInfoAll> barCodeDetectionInfoList;

        /* 临时存放GSensor数据的数组 */
        std::vector<DetectionInfo_GSensorData::GSensorDataItem> gsensorList;
        std::mutex gsensorListLock;

        /* 报警ID	uint32_t	按照报警先后，从0开始循环累加，不区分报警类型。 */
        uint32_t alarmId = 0;

        /* 镜头1是否全黑 */
        bool cam1FullBlack = false;
        /* 镜头2是否全黑 */
        bool cam2FullBlack = false;
        /* 镜头3是否全黑 */
        bool cam3FullBlack = false;
        /* 镜头4是否全黑 */
        bool cam4FullBlack = false;
        /* 镜头1是否遮挡 */
        bool camera1Covered = false;
        /* 镜头2是否遮挡 */
        bool camera2Covered = false;
        /* 镜头3是否遮挡 */
        bool camera3Covered = false;
        /* 镜头4是否遮挡 */
        bool camera4Covered = false;


    };

}
#endif //VIS_G3_SOFTWARE_G3SOCKETCOMMANAGER_H
