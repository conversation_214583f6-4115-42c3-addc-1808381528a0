//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/17.
//

#include <unistd.h>
#include "G3SocketComManager.h"

#include "XuFile.h"
#include "XuTimeUtil.h"
#include "XuLog.h"
#include "XuMemGeter.h"
#include "XuShell.h"

namespace vis {

    G3SocketComManager::G3SocketComManager() {

    }

    G3SocketComManager::~G3SocketComManager() {

    }


    void G3SocketComManager::run() {
        std::string pthreadName = "SocketCom";
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        isNeedStop = false;
        uint8_t *dataCache = static_cast<uint8_t *>(malloc(1024 * 1024 * 1));
        uint8_t *sendDataCache = static_cast<uint8_t *>(malloc(1024 * 1024 * 2));

        DetectionInfo_SystemError systemError;


        int len = 0;
        while (!isNeedStop) {
            /* 100ms 到了发送下识别信息 */
            detectionInfoLock.lock();
            /* 添加一包系统错误信息 */
            /* 填入TF卡状态 */
            systemError.setTfCardError(G3_Configuration::getInstance().isHasTfCard() ? 0 : 1);
            /* 填入高温状态 */
            systemError.setHighTemperature(G3_Configuration::getInstance().isHasHightTemperature() ? 1 : 0);
            /* 填入镜头1图像输入状态 */
            systemError.setCamera1ImgInputError(G3_Configuration::getInstance().isCamera1Opened() ? 0 : 1);
            /* 填入镜头2图像输入状态 */
            systemError.setCamera2ImgInputError(G3_Configuration::getInstance().isCamera2Opened() ? 0 : 1);
            /* 填入镜头3图像输入状态 */
            systemError.setCamera3ImgInputError(G3_Configuration::getInstance().isCamera3Opened() ? 0 : 1);
            /* 填入镜头4图像输入状态 */
            systemError.setCamera4ImgInputError(G3_Configuration::getInstance().isCamera4Opened() ? 0 : 1);
            /* 填入镜头1全黑状态 */
            systemError.setCamera1FullBlack(cam1FullBlack ? 1 : 0);
            /* 填入镜头2全黑状态 */
            systemError.setCamera2FullBlack(cam2FullBlack ? 1 : 0);
            /* 填入镜头3全黑状态 */
            systemError.setCamera3FullBlack(cam3FullBlack ? 1 : 0);
            /* 填入镜头4全黑状态 */
            systemError.setCamera4FullBlack(cam4FullBlack ? 1 : 0);
            curDetectionInfo.addDetectionInfoItem_SystemError(systemError);

            /* 整合一下镜头遮挡状态 */
            DetectionInfo_SystemInfo::DetectionInfo_SystemInfo_CameraCoverStatus cameraCoverStatus = {false, false, 0};
            cameraCoverStatus.camera1Covered = camera1Covered;
            cameraCoverStatus.camera2Covered = camera2Covered;
            cameraCoverStatus.camera3Covered = camera3Covered;
            cameraCoverStatus.camera4Covered = camera4Covered;
            systemInfo.setCameraCoverStatus(cameraCoverStatus);
            /* 整合一下限速标志 */

            /* 添加一包系统信息 */
            curDetectionInfo.addDetectionInfoItem_SystemInfo(systemInfo);
            /* 添加一包算法耗时信息 */
            curDetectionInfo.addDetectionInfoItem_AlgDetectTime(algDetectTime);
            /* 添加GSensor信息 */
            if(!gsensorList.empty()){
                DetectionInfo_GSensorData gSensorData;
                gSensorData.setGsensorDataList(gsensorList);
                curDetectionInfo.addDetectionInfo_GSensorData(gSensorData);
                gsensorList.clear();
            }
            /* 添加一包GPIO状态包 */
            DetectionInfo_GPIOIOStatus gpioioStatus;
            gpioioStatus.setGpioStatusInfo(ioStatus);
            curDetectionInfo.addDetectionInfo_GPIOIOStatus(gpioioStatus);
            /* 添加一包车辆信息和设备状态包 */
            DetectionInfo_SignalAndStatus signalAndStatus;
            signalAndStatus.setVehicleSignal(vehicleSignal);
            signalAndStatus.setDeviceStatus(deviceStatus);
            curDetectionInfo.addDetectionInfo_SignalAndStatus(signalAndStatus);

            len = encoder.generateDetectionInfo(dataCache, 1024 * 1024 * 1, curDetectionInfo);

            /* 在这里给它设置一下车速，免得有叼毛不选报警决策导致车速不对 */
            systemInfo.setSpeed(curSpeed * 10);


            detectionInfoLock.unlock();

            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(dataCache, len, 1, len - 2,
                                                                                  sendDataCache);
            callback->onNeedSendDataToSocket("ALL", -1, sendDataCache, sendLen);
            usleep(100 * 1000);


        }
        pthread_setname_np(pthread_self(), "Finish");
    }

    void G3SocketComManager::init(CommunicationDataCallback &communicationDataCallback) {
        callback = &communicationDataCallback;
        decoder.setInterface(*this);
    }


    void G3SocketComManager::onGetHeartBeat(Heartbeat &heartbeat, const char *ip, const int port) {
        /* 先直接回复通用应答 */
        uint8_t universalAnswerCode[20];
        uint8_t sendData[40];
        int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, heartbeat,
                                                      PCConfigureDataPacket::RESULT_SUCCESS);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateUniversalAnswerCode fialed! \n");
        }
    }

    void G3SocketComManager::onGetQequestG3Config(QequestG3Config &qequestG3Config, const char *ip, const int port) {
        printf("G3SocketComManager::onGetQequestG3Config   \n");
        std::string configstr = G3_Configuration::getInstance().getConfigurationStr();
        if (!configstr.empty()) {
            printf("configstr.szie=%d   \n", configstr.size());
            uint8_t *g3ConfiglAnswerCode = static_cast<uint8_t *>(malloc(1024 * 1024 * 1));
            uint8_t *sendData = static_cast<uint8_t *>(malloc(1024 * 1024 * 2));
            int len = encoder.generateRespondG3ConfigCode(g3ConfiglAnswerCode, 1024 * 1024 * 1,
                                                          const_cast<char *>(configstr.c_str()), configstr.size() + 1);
            if (len > 0) {
                int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(g3ConfiglAnswerCode, len, 1,
                                                                                      len - 2,
                                                                                      sendData);
//                printf("onGetQequestG3Config to %s:%d  sendLen:%d  content:", ip, port, sendLen);
//                for (int i = 0; i < sendLen; i++) {
//                    printf("%02x ", sendData[i]);
//                }
//                printf("\n");
                callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
            } else {
                printf("generateUniversalAnswerCode fialed! \n");
            }
            free(g3ConfiglAnswerCode);
            free(sendData);
        }


    }

    void G3SocketComManager::onGetSetG3Config(SetG3Config &setG3Config, const char *ip, const int port) {
//    CommunicationDataCallback::onGetSetG3Config(setG3Config,ip,port);

        callback->onGetSetG3Config(setG3Config, ip, port);

        printf("onGetSetG3Config! ip=%s  port=%d  len=%d   content:\n%s \n", ip, port, setG3Config.getConfigStrLen(),
               setG3Config.getConfigStr());

        /* 刷新下参数成设置过来的 */
        int ret = G3_Configuration::getInstance().loadFromStr(
                reinterpret_cast<const char *>(setG3Config.getConfigStr()),
                setG3Config.getConfigStrLen());
        /* 先直接回复通用应答 */
        uint8_t universalAnswerCode[20];
        uint8_t sendData[40];
        int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, setG3Config,
                                                      (ret == 0 ? PCConfigureDataPacket::RESULT_SUCCESS
                                                                : PCConfigureDataPacket::RESULT_FAILED));
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("onGetSetG3Config  generateUniversalAnswerCode fialed! \n");
        }
    }

    void G3SocketComManager::onGetStartUDPRealview(StartUDPRealview &startUDPRealview, const char *ip, const int port) {
        callback->onGetStartUDPRealview(startUDPRealview, ip, port);
        /* 比对下相机ID  匹配上就直接回复成功 */
        uint8_t resultCode[30];
        uint8_t sendData[60];
        int len = -1;
        switch (startUDPRealview.getCameraId()) {
            case CAMERA_ID_1: {
                len = encoder.generateStartUDPRealviewResultCode(resultCode, 20, startUDPRealview.getCameraId(),
                                                                 PCConfigureDataPacket::RESULT_SUCCESS,
                                                                 G3_Configuration::getInstance().getCameraUdpPort1());
            }
                break;
            case CAMERA_ID_2: {
                len = encoder.generateStartUDPRealviewResultCode(resultCode, 20, startUDPRealview.getCameraId(),
                                                                 PCConfigureDataPacket::RESULT_SUCCESS,
                                                                 G3_Configuration::getInstance().getCameraUdpPort2());
            }
                break;
            case CAMERA_ID_3: {
                len = encoder.generateStartUDPRealviewResultCode(resultCode, 20, startUDPRealview.getCameraId(),
                                                                 PCConfigureDataPacket::RESULT_SUCCESS,
                                                                 G3_Configuration::getInstance().getCameraUdpPort3());
            }
                break;
            case CAMERA_ID_4: {
                len = encoder.generateStartUDPRealviewResultCode(resultCode, 20, startUDPRealview.getCameraId(),
                                                                 PCConfigureDataPacket::RESULT_SUCCESS,
                                                                 G3_Configuration::getInstance().getCameraUdpPort4());
            }
                break;
            default: {
                len = encoder.generateStartUDPRealviewResultCode(resultCode, 20, -1,
                                                                 PCConfigureDataPacket::RESULT_FAILED,
                                                                 -1);
            }
                break;

        }
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(resultCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateUniversalAnswerCode fialed! \n");
        }
    }

    void G3SocketComManager::onGetUniversalAnswer(UniversalAnswer &universalAnswer, const char *ip, const int port) {

//        printf("onGetUniversalAnswer! ip:%s  port:%d \n", ip, port);
//    CommunicationDataCallback::onGetUniversalAnswer(universalAnswer, ip, port);
    }

    void G3SocketComManager::setSocketDataToConfigure(const char *ip, const int port, const uint8_t *buf, int len) {
        decoder.onDataReceived(ip, port, buf, len);
    }

    void
    G3SocketComManager::onGetRealtimeVehicleStatus(RealTimeVehicleStatus &vehicleStatus, const char *ip,
                                                   const int port) {
        callback->onGetRealtimeVehicleStatus(vehicleStatus, ip, port);
    }

    void G3SocketComManager::onGetStartG3DeviceUpgrade(StartG3DeviceUpgrade &startG3DeviceUpgrade, const char *ip,
                                                       const int port) {
        callback->onGetStartG3DeviceUpgrade(startG3DeviceUpgrade, ip, port);
    }

    void G3SocketComManager::setStartUpgardeResult(StartG3DeviceUpgrade &startG3DeviceUpgrade, int tcpport, int result,
                                                   const char *ip,
                                                   const int port) {
        int8_t g3DeviceUpgradeResultCodes[512];
        uint8_t sendData[1024];

        StartG3DeviceUpgradeResult g3DeviceUpgradeResult;
        g3DeviceUpgradeResult.setG3Id(startG3DeviceUpgrade.getG3Id());
        g3DeviceUpgradeResult.setTcpPort(tcpport);
        g3DeviceUpgradeResult.setResult(result);
        int len = encoder.generateStartG3DeviceUpgradeResult(reinterpret_cast<uint8_t *>(g3DeviceUpgradeResultCodes),
                                                             512,
                                                             g3DeviceUpgradeResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(
                    reinterpret_cast<uint8_t *>(g3DeviceUpgradeResultCodes), len, 1, len - 2,
                    sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateStartG3DeviceUpgradeResult fialed! \n");
        }
    }

    void G3SocketComManager::setUpgardeResult(const int deviceId, const int result, const char *ip, const int port) {
        int8_t upgradeResultCodes[50];
        uint8_t sendData[100];


        G3DeviceUpgradeResult upgradeResult;
        upgradeResult.setResult(result);
        upgradeResult.setDeviceId(deviceId);

        int len = encoder.generateG3DeviceUpgradeResult(reinterpret_cast<uint8_t *>(upgradeResultCodes), 50,
                                                        upgradeResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(
                    reinterpret_cast<uint8_t *>(upgradeResultCodes), len, 1, len - 2,
                    sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateStartG3DeviceUpgradeResult fialed! \n");
        }
    }

    void G3SocketComManager::setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {

        /* 先判断下这个报警决策是不是已经缓存起来了，有就修改，没有就添加 */
        if (!bsdDetectionInfoList.empty()) {
            /* 先看看有没有同个相机ID和算法的数据的数据 */
            bool hasSameInfo = false;
            /* 先找同一报警决策类型的 */
            for (int i = 0; i < (int)bsdDetectionInfoList.size(); i++) {
                /* 如果有同一报警决策类型的那么就找下同一相机ID的 */
                if (bsdDetectionInfoList[i].detectionResult.alarmDecisionType == detectionResult.alarmDecisionType) {
                    /* 如果还是同一相机ID的，那么就修改它 */
                    if (bsdDetectionInfoList[i].detectionResult.curCameraType.cameraId ==
                        detectionResult.curCameraType.cameraId) {
                        /* 由于结构体里面没有裸指针，故直接用=就好了 */
                        bsdDetectionInfoList[i].detectionResult = detectionResult;
                        bsdDetectionInfoList[i].curobjectInfo.objects.clear();
                        bsdDetectionInfoList[i].curobjectInfo.objects.insert(
                                bsdDetectionInfoList[i].curobjectInfo.objects.begin(), objectInfo.objects.begin(),
                                objectInfo.objects.end());

                        hasSameInfo = true;
                    }
                }
            }

            if (!hasSameInfo) {
                BSDDetectionInfoAll temp;
                /* 由于结构体里面没有裸指针，故直接用=就好了 */
                temp.detectionResult = detectionResult;
                temp.curobjectInfo.objects.insert(temp.curobjectInfo.objects.begin(), objectInfo.objects.begin(),
                                                  objectInfo.objects.end());
                bsdDetectionInfoList.push_back(temp);
            }
        } else {
            BSDDetectionInfoAll temp;
            /* 由于结构体里面没有裸指针，故直接用=就好了 */
            temp.detectionResult = detectionResult;
            temp.curobjectInfo.objects.insert(temp.curobjectInfo.objects.begin(), objectInfo.objects.begin(),
                                              objectInfo.objects.end());
            bsdDetectionInfoList.push_back(temp);
        }
        /* 定义BSD的数据 */
        DetectionInfo_ObjectInfo detectionInfoObjectInfo;
        detectionInfoObjectInfo.setEventCode(0);
        detectionInfoObjectInfo.setHasCamcoverd(detectionResult.isCameraCover);
        /* 定义 */

        /* 把同一个相机ID所有报警决策对应的object放到一起的都放到一起 */
        for (std::size_t i = 0; i < bsdDetectionInfoList.size(); i++) {
            if (bsdDetectionInfoList[i].detectionResult.curCameraType.cameraId ==
                detectionResult.curCameraType.cameraId) {
                for (std::size_t j = 0; j < bsdDetectionInfoList[i].curobjectInfo.objects.size(); j++) {
                    DetectionInfo_ObjectInfo_object_ tempObject_;
                    memcpy(tempObject_.label, bsdDetectionInfoList[i].curobjectInfo.objects[j].label, 16);
                    tempObject_.mId = bsdDetectionInfoList[i].curobjectInfo.objects[j].mId;
                    tempObject_.mTop = bsdDetectionInfoList[i].curobjectInfo.objects[j].mTop;
                    tempObject_.mBottom = bsdDetectionInfoList[i].curobjectInfo.objects[j].mBottom;
                    tempObject_.mLeft = bsdDetectionInfoList[i].curobjectInfo.objects[j].mLeft;
                    tempObject_.mRight = bsdDetectionInfoList[i].curobjectInfo.objects[j].mRight;
                    tempObject_.mXDistance = bsdDetectionInfoList[i].curobjectInfo.objects[j].mXDistance;
                    tempObject_.mYDistance = bsdDetectionInfoList[i].curobjectInfo.objects[j].mYDistance;
                    tempObject_.mHeadway = bsdDetectionInfoList[i].curobjectInfo.objects[j].mHeadway * 10;
                    tempObject_.mRealWidth = bsdDetectionInfoList[i].curobjectInfo.objects[j].mRealWidth;
                    tempObject_.mRealHeight = bsdDetectionInfoList[i].curobjectInfo.objects[j].mRealHeight;
                    tempObject_.mXVelocity = bsdDetectionInfoList[i].curobjectInfo.objects[j].mXVelocity;
                    tempObject_.mYVelocity = bsdDetectionInfoList[i].curobjectInfo.objects[j].mYVelocity;
                    tempObject_.mTrend = bsdDetectionInfoList[i].curobjectInfo.objects[j].mTrend;
                    tempObject_.mScore = bsdDetectionInfoList[i].curobjectInfo.objects[j].mScore * 100;

                    tempObject_.shadow = ((bsdDetectionInfoList[i].curobjectInfo.objects[j].mMaxScore >=
                                           detectionResult.classThreshold) &&
                                          (bsdDetectionInfoList[i].curobjectInfo.objects[j].mMinScore >=
                                           detectionResult.classThreshold / 2.0f)) ? 0 : 1;
                    tempObject_.mMaxScore = bsdDetectionInfoList[i].curobjectInfo.objects[j].mMaxScore * 100;
                    tempObject_.mMinScore = bsdDetectionInfoList[i].curobjectInfo.objects[j].mMinScore * 100;
                    detectionInfoObjectInfo.addObjectToList(tempObject_);
                }
            }
        }

        detectionInfoObjectInfo.setCameraId(detectionResult.curCameraType.cameraId);


        uint8_t bypassInfo = 0x00;
        memcpy(&bypassInfo, &detectionResult.bypassStatusByCamera, 1);
        systemInfo.setBypassInfo(bypassInfo);
        systemInfo.setSpeed(detectionResult.speed * 10);
        /* 设置镜头遮挡状态和算法耗时 */
        switch (detectionResult.curCameraType.cameraId) {
            case CAMERA_ID_1: {
                camera1Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera1(detectionResult.detectUseTime);

            }
                break;
            case CAMERA_ID_2: {
                camera2Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera2(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_3: {
                camera3Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera3(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_4: {
                camera4Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera4(detectionResult.detectUseTime);
            }
                break;
        }


        detectionInfoLock.lock();
        /* 添加一下BSD的信息 */
        curDetectionInfo.addDetectionInfoItem_ObjectInfo(detectionInfoObjectInfo);
        /* 添加一下MaskBuf的信息  如果发现算法类型是ADAS，那么就不发MASKbuf*/
        if (detectionResult.maskBufLen > 0) {
            DetectionInfo_MaskBuf detectionInfoMaskBuf;
            detectionInfoMaskBuf.setCameraId(detectionResult.curCameraType.cameraId);
            detectionInfoMaskBuf.setMaskWidth(detectionResult.maskBufWidth);
            detectionInfoMaskBuf.setMaskHeight(detectionResult.maskBufHeight);
            detectionInfoMaskBuf.setMaskBufLen(detectionResult.maskBufLen);
            detectionInfoMaskBuf.setMaskBuf(detectionResult.maskBuf, detectionResult.maskBufLen);
            curDetectionInfo.addDetectionInfoItem_MaskBuf(detectionInfoMaskBuf);
        }

        /* 添加一下灯控制信息 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4GJ_UNIT_G3 ||
            G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4GJ_G4_UNIT) {
            DetectionInfo_Control lampControl;
            DetectionInfo_Control::DetectionInfo_Control_ChannelControl channelControl = {};
            switch (detectionResult.curCameraType.cameraId) {
                case CAMERA_ID_1: {
                    switch (detectionResult.lampType) {
                        case 0: {
                            channelControl.lamp1_open_norma1 = detectionResult.isLampOpen;
                        }
                            break;

                        case 1: {
                            channelControl.lamp_open_infrared1 = detectionResult.isLampOpen;
                        }
                            break;
                    }

                }
                    break;
                case CAMERA_ID_2: {
                    switch (detectionResult.lampType) {
                        case 0: {
                            channelControl.lamp1_open_norma2 = detectionResult.isLampOpen;
                        }
                            break;

                        case 1: {
                            channelControl.lamp_open_infrared2 = detectionResult.isLampOpen;
                        }
                            break;
                    }
                }
                    break;
                case CAMERA_ID_3: {
                    //NOTE 暂时不支持
                    ; //not to do
                }
                    break;
                case CAMERA_ID_4: {
                    //NOTE 暂时不支持
                    ; //not to do
                }
                    break;
            }
            lampControl.setChannelControl(channelControl);
            curDetectionInfo.addDetectionInfoItem_LampContro(lampControl);
        }
        /* 看看是不是添加一下R151的东西 */
        if (detectionResult.curCameraType.adType.pedestrian_r151 || detectionResult.curCameraType.adType.vehicle_r151) {

            DetectionInfo_R151Info detectionInfoR151Info;
            DetectionInfo_R151Info::R151Info_AreaInfo areaInfo1 = {};
            DetectionInfo_R151Info::R151Info_AreaInfo areaInfo2 = {};
            DetectionInfo_R151Info::R151Info_AreaInfo areaInfo3 = {};
            DetectionInfo_R151Info::R151Info_AreaInfo areaInfo4 = {};

            detectionInfoR151Info.setCameraId(detectionResult.curCameraType.cameraId);
            detectionInfoR151Info.setInstallPosition(detectionResult.r151BsdDetectionInfo.installPosition);


            /* 把同一个相机ID所有R151的识别信息放到一起 */
            for (std::size_t i = 0; i < bsdDetectionInfoList.size(); i++) {
                if (bsdDetectionInfoList[i].detectionResult.curCameraType.cameraId ==
                    detectionResult.curCameraType.cameraId) {
                    areaInfo1.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasPedestrianInAlarmArea1;
                    areaInfo1.hasVehicle |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasVehicleInAlarmArea1;
                    areaInfo1.camCovered |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasCameraCoverInAlarmArea1;

                    areaInfo2.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasPedestrianInAlarmArea2;
                    areaInfo2.hasVehicle |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasVehicleInAlarmArea2;
                    areaInfo2.camCovered |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasCameraCoverInAlarmArea2;

                    areaInfo3.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasPedestrianInAlarmArea3;
                    areaInfo3.hasVehicle |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasVehicleInAlarmArea3;
                    areaInfo3.camCovered |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasCameraCoverInAlarmArea3;

                    areaInfo4.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasPedestrianInAlarmArea4;
                    areaInfo4.hasVehicle |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasVehicleInAlarmArea4;
                    areaInfo4.camCovered |= bsdDetectionInfoList[i].detectionResult.r151BsdDetectionInfo.hasCameraCoverInAlarmArea4;

                }
            }
            detectionInfoR151Info.setAreaInfo1(areaInfo1);
            detectionInfoR151Info.setAreaInfo2(areaInfo2);
            detectionInfoR151Info.setAreaInfo3(areaInfo3);
            detectionInfoR151Info.setAreaInfo4(areaInfo4);

            curDetectionInfo.addDetectionInfoItem_R151Info(detectionInfoR151Info);
        }


        /* 看看是不是添加一下R158的东西 */
        if (detectionResult.curCameraType.adType.pedestrian_r158 || detectionResult.curCameraType.adType.vehicle_r158) {

            DetectionInfo_R158Info detectionInfoR158Info;
            DetectionInfo_R158Info::R158Info_AreaInfo areaInfo1 = {};
            DetectionInfo_R158Info::R158Info_AreaInfo areaInfo2 = {};
            DetectionInfo_R158Info::R158Info_AreaInfo areaInfo3 = {};

            detectionInfoR158Info.setCameraId(detectionResult.curCameraType.cameraId);


            /* 把同一个相机ID所有R158的识别信息放到一起 */
            for (std::size_t i = 0; i < bsdDetectionInfoList.size(); i++) {
                if (bsdDetectionInfoList[i].detectionResult.curCameraType.cameraId ==
                    detectionResult.curCameraType.cameraId) {
                    areaInfo1.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea1;
                    areaInfo1.hasVehicle |= bsdDetectionInfoList[i].detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea1;
                    areaInfo1.camCovered |= bsdDetectionInfoList[i].detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea1;

                    areaInfo2.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea2;
                    areaInfo2.hasVehicle |= bsdDetectionInfoList[i].detectionResult.r158BsdDetectionInfo.hasVehicleInAlarmArea2;
                    areaInfo2.camCovered |= bsdDetectionInfoList[i].detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea2;

                    areaInfo3.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r158BsdDetectionInfo.hasPedestrianInAlarmArea3;
                    areaInfo3.hasVehicle = false;
                    areaInfo3.camCovered |= bsdDetectionInfoList[i].detectionResult.r158BsdDetectionInfo.hasCameraCoverInAlarmArea3;
                }
            }


            detectionInfoR158Info.setAreaInfo1(areaInfo1);
            detectionInfoR158Info.setAreaInfo2(areaInfo2);
            detectionInfoR158Info.setAreaInfo3(areaInfo3);

            curDetectionInfo.addDetectionInfoItem_R158Info(detectionInfoR158Info);
        }

        /* 看看是不是添加一下R159的东西 */
        if (detectionResult.curCameraType.adType.pedestrian_r159 || detectionResult.curCameraType.adType.vehicle_r159) {

            DetectionInfo_R159Info detectionInfoR159Info;
            DetectionInfo_R159Info::R159Info_AreaInfo areaInfo1 = {};
            DetectionInfo_R159Info::R159Info_AreaInfo areaInfo2 = {};
            DetectionInfo_R159Info::R159Info_AreaInfo areaInfo3 = {};
            detectionInfoR159Info.setCameraId(detectionResult.curCameraType.cameraId);
            /* 把同一个相机ID所有R159的识别信息放到一起 */
            for (std::size_t i = 0; i < bsdDetectionInfoList.size(); i++) {
                if (bsdDetectionInfoList[i].detectionResult.curCameraType.cameraId ==
                    detectionResult.curCameraType.cameraId) {
                    areaInfo1.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea1;
                    areaInfo1.hasVehicle |= bsdDetectionInfoList[i].detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea1;
                    areaInfo1.camCovered |= bsdDetectionInfoList[i].detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea1;

                    areaInfo2.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea2;
                    areaInfo2.hasVehicle |= bsdDetectionInfoList[i].detectionResult.r159BsdDetectionInfo.hasVehicleInAlarmArea2;
                    areaInfo2.camCovered |= bsdDetectionInfoList[i].detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea2;

                    areaInfo3.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.r159BsdDetectionInfo.hasPedestrianInAlarmArea3;
                    areaInfo3.hasVehicle = false;
                    areaInfo3.camCovered |= bsdDetectionInfoList[i].detectionResult.r159BsdDetectionInfo.hasCameraCoverInAlarmArea3;
                }
            }

            detectionInfoR159Info.setAreaInfo1(areaInfo1);
            detectionInfoR159Info.setAreaInfo2(areaInfo2);
            detectionInfoR159Info.setAreaInfo3(areaInfo3);

            curDetectionInfo.addDetectionInfoItem_R159Info(detectionInfoR159Info);
        }

        /* 看看是不是添加一下人（Security区域）的东西 */
        if (detectionResult.curCameraType.adType.pedestrian_area_security_model) {
            DetectionInfo_SecurityModeInfo detectionInfoSecurityModeInfo;
            DetectionInfo_SecurityModeInfo::SecurityModeInfo_AreaInfo areaInfo1 = {};
            DetectionInfo_SecurityModeInfo::SecurityModeInfo_AreaInfo areaInfo2 = {};
            detectionInfoSecurityModeInfo.setCameraId(detectionResult.curCameraType.cameraId);
            /* 把同一个相机ID所有人（Security区域）的识别信息放到一起 */
            for (std::size_t i = 0; i < bsdDetectionInfoList.size(); i++) {
                if (bsdDetectionInfoList[i].detectionResult.curCameraType.cameraId == detectionResult.curCameraType.cameraId) {
                    areaInfo1.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.securityModeDetectionInfo.hasPedestrianInAlarmArea1;
                    areaInfo1.hasVehicle = false;
                    areaInfo2.hasPedestrian |= bsdDetectionInfoList[i].detectionResult.securityModeDetectionInfo.hasPedestrianInAlarmArea2;
                    areaInfo2.hasVehicle = false;

                }
            }
            detectionInfoSecurityModeInfo.setAreaInfo1(areaInfo1);
            detectionInfoSecurityModeInfo.setAreaInfo2(areaInfo2);
            curDetectionInfo.addDetectionInfoItem_SecurityModeInfo(detectionInfoSecurityModeInfo);
        }
        /* 检查下镜头的全黑状态 */
        checkCameraFullBlackStatus(detectionResult);

        detectionInfoLock.unlock();
    }

    void
    G3SocketComManager::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {


        uint8_t detectionEventCodes[512];
        uint8_t sendData[1024];
        DetectionAlarmEvent detectionAlarmEvent;



        /* 封装一下通用信息 */
        DetectionAlarmEvent_GeneralInfo generalInfo;
        if (alarmId >= 0xFFFFFFFF) {
            alarmId = 0;
        }
        generalInfo.setEventId(alarmId);
        alarmId++;
        generalInfo.setSpeed(alarmEventInfo.speed);
        if (alarmEventInfo.curGpsInfo.status == 1) {
            generalInfo.setAltitude(alarmEventInfo.curGpsInfo.altitude);
            generalInfo.setLatitude(alarmEventInfo.curGpsInfo.latitude * 1000000);
            generalInfo.setLongitude(alarmEventInfo.curGpsInfo.longitude * 1000000);
            generalInfo.setTimeBcd(alarmEventInfo.curGpsInfo.bcdTime);
        } else {
            generalInfo.setAltitude(0xFFFF);
            generalInfo.setLatitude(0xFFFFFFFF);
            generalInfo.setLongitude(0xFFFFFFFF);
            uint8_t timebcd[6] = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF};
            generalInfo.setTimeBcd(timebcd);
        }
        detectionAlarmEvent.addGeneralInfo(generalInfo);




        /* 封装对应的报警事件信息 */
        DetectionAlarmEvent_BSDEvent bsdEvent;
        bsdEvent.setCameraId(curCameraType.cameraId);
        bsdEvent.setIsShadow(alarmEventInfo.isShadow ? 1 : 0);
        DetectionAlarmEvent_DSMEvent dsmEvent;
        dsmEvent.setCurCameraId(curCameraType.cameraId);
        DetectionAlarmEvent_ADASEvent adasEvent;
        adasEvent.setCameraId(curCameraType.cameraId);
        adasEvent.setIsShadow(alarmEventInfo.isShadow ? 1 : 0);
        DetectionAlarmEvent_R151Event r151Event;
        r151Event.setCameraId(curCameraType.cameraId);
        r151Event.setIsShadow(alarmEventInfo.isShadow ? 1 : 0);
        DetectionAlarmEvent_R158Event r158Event;
        r158Event.setCameraId(curCameraType.cameraId);
        r158Event.setIsShadow(alarmEventInfo.isShadow ? 1 : 0);
        DetectionAlarmEvent_R159Event r159Event;
        r159Event.setCameraId(curCameraType.cameraId);
        r159Event.setIsShadow(alarmEventInfo.isShadow ? 1 : 0);

        switch (eventCode) {
            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_LEFT_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL1);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1:
            case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_LEFT_VEHICLE);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL1);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_LEFT_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL2);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2:
            case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_LEFT_VEHICLE);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL2);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_RIGHT_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL1);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1:
            case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_RIGHT_VEHICLE);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL1);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_RIGHT_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL2);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2:
            case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_RIGHT_VEHICLE);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL2);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_FORWARD_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL1);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_FORWARD_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL2);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_BACKWARD_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL1);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_BACKWARD_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL2);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;


            case EVENT_BSD_FORWARD_VEHICLE_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_FORWARD_VEHICLE);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL1);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_FORWARD_VEHICLE_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_FORWARD_VEHICLE);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL2);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_BACKWARD_VEHICLE_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_BACKWARD_VEHICLE);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL1);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_BSD_BACKWARD_VEHICLE_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventType(DetectionAlarmEvent_BSDEvent::EVENT_TYPE_BACKWARD_VEHICLE);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(DetectionAlarmEvent_BSDEvent::EVENT_LEVEL_LEVEL2);
                bsdEvent.setAlarmPedestrianId(alarmEventInfo.bsdAlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addBSDEvent(bsdEvent);
            }
                break;

            case EVENT_DSM: {
                dsmEvent.setRespiratorAlarm(alarmEventInfo.dsmAlarmInfo.respirator_alarm ? 1 : 0);
                dsmEvent.setEyeAlarm(alarmEventInfo.dsmAlarmInfo.eye_alarm ? 1 : 0);
                dsmEvent.setMouthAlarm(alarmEventInfo.dsmAlarmInfo.mouth_alarm ? 1 : 0);
                dsmEvent.setLookaroundAlarm(alarmEventInfo.dsmAlarmInfo.lookaround_alarm ? 1 : 0);
                dsmEvent.setFacemissingAlarm(alarmEventInfo.dsmAlarmInfo.facemissing_alarm ? 1 : 0);
                dsmEvent.setCamcoverAlarm(alarmEventInfo.dsmAlarmInfo.camcover_alarm ? 1 : 0);
                dsmEvent.setSmokingAlarm(alarmEventInfo.dsmAlarmInfo.smoking_alarm ? 1 : 0);
                dsmEvent.setPhoneAlarm(alarmEventInfo.dsmAlarmInfo.phone_alarm ? 1 : 0);
                dsmEvent.setFatigueRank(alarmEventInfo.dsmAlarmInfo.fatigue_rank ? 1 : 0);
                dsmEvent.setBeltAlarm(alarmEventInfo.dsmAlarmInfo.seatbelt_alarm ? 1 : 0);
                dsmEvent.setBlindspotAlarm(alarmEventInfo.dsmAlarmInfo.blindspot_alarm ? 1 : 0);
                dsmEvent.setHatAlarm(alarmEventInfo.dsmAlarmInfo.hat_alarm ? 1 : 0);
                detectionAlarmEvent.addDSMEvent(dsmEvent);
            }
                break;

            case EVENT_SBST_FORWARD_PDW_LEVEL1: {
                adasEvent.setEventStatus(0x00);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_PDW);
                adasEvent.setHeadway(alarmEventInfo.sbstForwardAlarmInfo.headway * 10);
                adasEvent.setLdwType(0x00);
                adasEvent.setRoadSignsType(0x00);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.pdw_level1 = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);

                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_SBST_FORWARD_PDW_LEVEL2: {
                adasEvent.setEventStatus(0x00);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_PDW);
                adasEvent.setHeadway(alarmEventInfo.sbstForwardAlarmInfo.headway * 10);
                adasEvent.setLdwType(0x00);
                adasEvent.setRoadSignsType(0x00);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.pdw_level2 = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);

                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_SBST_FORWARD_FCW_LEVEL1: {
                adasEvent.setEventStatus(0x00);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_FCW);
                /* 1级模仿成TTC 这里先定下用TTC 到时候等他们确定 */
                adasEvent.setHeadway(alarmEventInfo.sbstForwardAlarmInfo.ttc * 10);
                adasEvent.setLdwType(0x00);
                adasEvent.setRoadSignsType(0x00);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.fcw_ttc_level1 = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);

                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_SBST_FORWARD_FCW_LEVEL2: {
                adasEvent.setEventStatus(0x00);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_HMW);
                adasEvent.setHeadway(alarmEventInfo.sbstForwardAlarmInfo.headway * 10);
                adasEvent.setLdwType(0x00);
                adasEvent.setRoadSignsType(0x00);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.fcw_hmw_level1 = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);

                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_SBST_FORWARD_LDW_LEFT_DASHED: {
                adasEvent.setEventStatus(0x00);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_LDW);
                adasEvent.setHeadway(alarmEventInfo.sbstForwardAlarmInfo.headway * 10);
                adasEvent.setLdwType(DetectionAlarmEvent_ADASEvent::LDW_TYPE_LEFT_DASHED);
                adasEvent.setRoadSignsType(0x00);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.ldw_left_dashed = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);

                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_SBST_FORWARD_LDW_LEFT_SOLID: {
                adasEvent.setEventStatus(0x00);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_LDW);
                adasEvent.setHeadway(alarmEventInfo.sbstForwardAlarmInfo.headway * 10);
                adasEvent.setLdwType(DetectionAlarmEvent_ADASEvent::LDW_TYPE_LEFT_SOLID);
                adasEvent.setRoadSignsType(0x00);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.ldw_left_solid = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);

                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_SBST_FORWARD_LDW_RIGHT_DASHED: {
                adasEvent.setEventStatus(0x00);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_LDW);
                adasEvent.setHeadway(alarmEventInfo.sbstForwardAlarmInfo.headway * 10);
                adasEvent.setLdwType(DetectionAlarmEvent_ADASEvent::LDW_TYPE_RIGHT_DASHED);
                adasEvent.setRoadSignsType(0x00);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.ldw_right_dashed = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);

                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_SBST_FORWARD_LDW_RIGHT_SOLID: {
                adasEvent.setEventStatus(0x00);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_LDW);
                adasEvent.setHeadway(alarmEventInfo.sbstForwardAlarmInfo.headway * 10);
                adasEvent.setLdwType(DetectionAlarmEvent_ADASEvent::LDW_TYPE_RIGHT_SOLID);
                adasEvent.setRoadSignsType(0x00);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {false};
                alarmlLevelInfo.ldw_right_solid = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {false};
                adasEvent.setFaultInfo(faultInfo);

                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;


            case EVENT_ADAS_VEHICLE_TTC: {
                adasEvent.setEventStatus(alarmEventInfo.adasAlarmInfo.eventStatus);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_FCW);
                adasEvent.setHeadway(alarmEventInfo.adasAlarmInfo.ttc * 10);
                adasEvent.setLdwType(0xFF);
                adasEvent.setRoadSignsType(0xFF);

                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {false};
                alarmlLevelInfo.fcw_ttc_level1 = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {false};
                adasEvent.setFaultInfo(faultInfo);
                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_ADAS_VEHICLE_HMW: {
                adasEvent.setEventStatus(alarmEventInfo.adasAlarmInfo.eventStatus);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_HMW);
                adasEvent.setHeadway(alarmEventInfo.adasAlarmInfo.headway * 10);
                adasEvent.setLdwType(0xFF);
                adasEvent.setRoadSignsType(0xFF);

                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {false};
                alarmlLevelInfo.fcw_hmw_level1 = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {false};
                adasEvent.setFaultInfo(faultInfo);
                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_ADAS_PEDESTRIAN_HMW: {
                adasEvent.setEventStatus(alarmEventInfo.adasAlarmInfo.eventStatus);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_PDW);
                adasEvent.setHeadway(alarmEventInfo.adasAlarmInfo.headway * 10);
                adasEvent.setLdwType(0xFF);
                adasEvent.setRoadSignsType(0xFF);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {false};
                alarmlLevelInfo.pdw_level1 = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {false};
                adasEvent.setFaultInfo(faultInfo);
                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_ADAS_LDW_LEFT_SOLID: {
                adasEvent.setEventStatus(alarmEventInfo.adasAlarmInfo.eventStatus);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_LDW);
                adasEvent.setHeadway(0xFF);
                adasEvent.setLdwType(DetectionAlarmEvent_ADASEvent::LDW_TYPE_LEFT_SOLID);
                adasEvent.setRoadSignsType(0xFF);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.ldw_left_solid = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);
                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_ADAS_LDW_LEFT_DASH: {
                adasEvent.setEventStatus(alarmEventInfo.adasAlarmInfo.eventStatus);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_LDW);
                adasEvent.setHeadway(0xFF);
                adasEvent.setLdwType(DetectionAlarmEvent_ADASEvent::LDW_TYPE_LEFT_DASHED);
                adasEvent.setRoadSignsType(0xFF);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.ldw_left_dashed = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);
                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_ADAS_LDW_RIGHT_SOLID: {
                adasEvent.setEventStatus(alarmEventInfo.adasAlarmInfo.eventStatus);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_LDW);
                adasEvent.setHeadway(0xFF);
                adasEvent.setLdwType(DetectionAlarmEvent_ADASEvent::LDW_TYPE_RIGHT_SOLID);
                adasEvent.setRoadSignsType(0xFF);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.ldw_right_solid = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);
                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_ADAS_LDW_RIGHT_DASH: {
                adasEvent.setEventStatus(alarmEventInfo.adasAlarmInfo.eventStatus);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_LDW);
                adasEvent.setHeadway(0xFF);
                adasEvent.setLdwType(DetectionAlarmEvent_ADASEvent::LDW_TYPE_RIGHT_DASHED);
                adasEvent.setRoadSignsType(0xFF);
                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
                alarmlLevelInfo.ldw_right_dashed = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
                adasEvent.setFaultInfo(faultInfo);
                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;

            case EVENT_BSD_R151_AREA1_PEDESTRIAN: {
                r151Event.setStatus(0);
                r151Event.setEventType(DetectionAlarmEvent_R151Event::R151EVENTTYPE_PEDESTRIAN);
                r151Event.setAreaNum(1);
                r151Event.setObjId(alarmEventInfo.r151AlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addR151Event(r151Event);
            }
                break;

            case EVENT_BSD_R151_AREA2_PEDESTRIAN: {
                r151Event.setStatus(0);
                r151Event.setEventType(DetectionAlarmEvent_R151Event::R151EVENTTYPE_PEDESTRIAN);
                r151Event.setAreaNum(2);
                r151Event.setObjId(alarmEventInfo.r151AlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addR151Event(r151Event);
            }
                break;

            case EVENT_BSD_R151_AREA3_PEDESTRIAN: {
                r151Event.setStatus(0);
                r151Event.setEventType(DetectionAlarmEvent_R151Event::R151EVENTTYPE_PEDESTRIAN);
                r151Event.setAreaNum(3);
                r151Event.setObjId(alarmEventInfo.r151AlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addR151Event(r151Event);
            }
                break;

            case EVENT_BSD_R151_AREA4_PEDESTRIAN: {
                r151Event.setStatus(0);
                r151Event.setEventType(DetectionAlarmEvent_R151Event::R151EVENTTYPE_PEDESTRIAN);
                r151Event.setAreaNum(4);
                r151Event.setObjId(alarmEventInfo.r151AlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addR151Event(r151Event);
            }
                break;

            case EVENT_BSD_R151_AREA1_VEHICLE: {
                r151Event.setStatus(0);
                r151Event.setEventType(DetectionAlarmEvent_R151Event::R151EVENTTYPE_VEHICLE);
                r151Event.setAreaNum(1);
                r151Event.setObjId(alarmEventInfo.r151AlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addR151Event(r151Event);
            }
                break;

            case EVENT_BSD_R151_AREA2_VEHICLE: {
                r151Event.setStatus(0);
                r151Event.setEventType(DetectionAlarmEvent_R151Event::R151EVENTTYPE_VEHICLE);
                r151Event.setAreaNum(2);
                r151Event.setObjId(alarmEventInfo.r151AlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addR151Event(r151Event);
            }
                break;

            case EVENT_BSD_R151_CAMERA_COVER: {
                r151Event.setStatus(0);
                r151Event.setEventType(DetectionAlarmEvent_R151Event::R151EVENTTYPE_CAMERACOVER);
                r151Event.setAreaNum(0xFF);
                r151Event.setObjId(0xFFFFFFFF);
                detectionAlarmEvent.addR151Event(r151Event);
            }
                break;

            case EVENT_BSD_R158_AREA1_PEDESTRIAN: {
                r158Event.setStatus(0);
                r158Event.setEventType(DetectionAlarmEvent_R158Event::R158EVENTTYPE_PEDESTRIAN);
                r158Event.setAreaNum(1);
                r158Event.setObjId(alarmEventInfo.r158AlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addR158Event(r158Event);
            }
                break;

            case EVENT_BSD_R158_AREA2_PEDESTRIAN: {
                r158Event.setStatus(0);
                r158Event.setEventType(DetectionAlarmEvent_R158Event::R158EVENTTYPE_PEDESTRIAN);
                r158Event.setAreaNum(2);
                r158Event.setObjId(alarmEventInfo.r158AlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addR158Event(r158Event);
            }
                break;

            case EVENT_BSD_R158_AREA3_PEDESTRIAN: {
                r158Event.setStatus(0);
                r158Event.setEventType(DetectionAlarmEvent_R158Event::R158EVENTTYPE_PEDESTRIAN);
                r158Event.setAreaNum(3);
                r158Event.setObjId(alarmEventInfo.r158AlarmInfo.alarmPedestrianId);
                detectionAlarmEvent.addR158Event(r158Event);
            }
                break;


            case EVENT_BSD_R158_AREA1_VEHICLE: {
                r158Event.setStatus(0);
                r158Event.setEventType(DetectionAlarmEvent_R158Event::R158EVENTTYPE_VEHICLE);
                r158Event.setAreaNum(1);
                r158Event.setObjId(alarmEventInfo.r158AlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addR158Event(r158Event);
            }
                break;

            case EVENT_BSD_R158_AREA2_VEHICLE: {
                r158Event.setStatus(0);
                r158Event.setEventType(DetectionAlarmEvent_R158Event::R158EVENTTYPE_VEHICLE);
                r158Event.setAreaNum(2);
                r158Event.setObjId(alarmEventInfo.r158AlarmInfo.alarmVehicleId);
                detectionAlarmEvent.addR158Event(r158Event);
            }
                break;

            case EVENT_BSD_R158_CAMERA_COVER: {
                r158Event.setStatus(0);
                r158Event.setEventType(DetectionAlarmEvent_R158Event::R158EVENTTYPE_CAMERACOVER);
                r158Event.setAreaNum(0xFF);
                r158Event.setObjId(0xFFFFFFFF);
                detectionAlarmEvent.addR158Event(r158Event);
            }
                break;

            case EVENT_BSD_R159_AREA1_PEDESTRIAN: {
                r159Event.setStatus(0);
                r159Event.setEventType(DetectionAlarmEvent_R159Event::R159EVENTTYPE_PEDESTRIAN);
                r159Event.setAreaNum(1);
                r159Event.setObjId(alarmEventInfo.r159AlarmInfo.alarmPedestrianId);
                r159Event.setHeadway(0xFFFFFFFF);
                detectionAlarmEvent.addR159Event(r159Event);
            }
                break;

            case EVENT_BSD_R159_AREA2_PEDESTRIAN: {
                r159Event.setStatus(0);
                r159Event.setEventType(DetectionAlarmEvent_R159Event::R159EVENTTYPE_PEDESTRIAN);
                r159Event.setAreaNum(2);
                r159Event.setObjId(alarmEventInfo.r159AlarmInfo.alarmPedestrianId);
                r159Event.setHeadway(0xFFFFFFFF);
                detectionAlarmEvent.addR159Event(r159Event);
            }
                break;

            case EVENT_BSD_R159_AREA3_PEDESTRIAN: {
                r159Event.setStatus(0);
                r159Event.setEventType(DetectionAlarmEvent_R159Event::R159EVENTTYPE_PEDESTRIAN);
                r159Event.setAreaNum(3);
                r159Event.setObjId(alarmEventInfo.r159AlarmInfo.alarmPedestrianId);
                r159Event.setHeadway(0xFFFFFFFF);
                detectionAlarmEvent.addR159Event(r159Event);
            }
                break;


            case EVENT_BSD_R159_AREA1_VEHICLE: {
                r159Event.setStatus(0);
                r159Event.setEventType(DetectionAlarmEvent_R159Event::R159EVENTTYPE_VEHICLE);
                r159Event.setAreaNum(1);
                r159Event.setObjId(alarmEventInfo.r159AlarmInfo.alarmVehicleId);
                r159Event.setHeadway(0xFFFFFFFF);
                detectionAlarmEvent.addR159Event(r159Event);
            }
                break;

            case EVENT_BSD_R159_AREA2_VEHICLE: {
                r159Event.setStatus(0);
                r159Event.setEventType(DetectionAlarmEvent_R159Event::R159EVENTTYPE_VEHICLE);
                r159Event.setAreaNum(2);
                r159Event.setObjId(alarmEventInfo.r159AlarmInfo.alarmVehicleId);
                r159Event.setHeadway(0xFFFFFFFF);
                detectionAlarmEvent.addR159Event(r159Event);
            }
                break;

            case EVENT_BSD_R159_CAMERA_COVER: {
                r159Event.setStatus(0);
                r159Event.setEventType(DetectionAlarmEvent_R159Event::R159EVENTTYPE_CAMERACOVER);
                r159Event.setAreaNum(0xFF);
                r159Event.setObjId(0xFFFFFFFF);
                r159Event.setHeadway(0xFFFFFFFF);
                detectionAlarmEvent.addR159Event(r159Event);
            }
                break;


            case EVENT_ADAS_VEHICLE_VB: {
                adasEvent.setEventStatus(alarmEventInfo.adasAlarmInfo.eventStatus);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_VB);
                adasEvent.setHeadway(alarmEventInfo.adasAlarmInfo.headway * 10);
                adasEvent.setLdwType(0xFF);
                adasEvent.setRoadSignsType(0xFF);

                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {false};
                alarmlLevelInfo.fcw_vb = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {false};
                adasEvent.setFaultInfo(faultInfo);
                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;
            case EVENT_ADAS_VEHICLE_GO: {
                adasEvent.setEventStatus(alarmEventInfo.adasAlarmInfo.eventStatus);
                adasEvent.setEventType(DetectionAlarmEvent_ADASEvent::EVENT_TYPE_GO);
                adasEvent.setHeadway(alarmEventInfo.adasAlarmInfo.headway * 10);
                adasEvent.setLdwType(0xFF);
                adasEvent.setRoadSignsType(0xFF);

                /* 把报警分级信息传过去 */
                DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {false};
                alarmlLevelInfo.fcw_go = true;
                adasEvent.setAlarmlLevelInfo(alarmlLevelInfo);
                /* 故障这个再考虑考虑 先不发 */
                DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {false};
                adasEvent.setFaultInfo(faultInfo);
                detectionAlarmEvent.addADASEvent(adasEvent);
            }
                break;
        }
        int len = encoder.generateDetectionAlarmEvent(detectionEventCodes, sizeof(detectionEventCodes),
                                                      detectionAlarmEvent);
        if (len > 0) {

//            printf("encoder.generateDetectionAlarmEvent  len:%d  content:", len);
//            for (int i = 0; i < len; i++) {
//                printf("%02x ", detectionEventCodes[i]);
//            }
//            printf("\n");

            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(detectionEventCodes, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket("ALL", -1, sendData, sendLen);
        } else {
            printf("generateUniversalAnswerCode fialed! \n");
        }
    }

    int G3SocketComManager::onGetSetG3SystemTime(SetG3SystemTime &setG3SystemTime, const char *ip, const int port) {
        int ret = callback->onGetSetG3SystemTime(setG3SystemTime, ip, port);

        /* 先直接回复通用应答 */
        uint8_t universalAnswerCode[20];
        uint8_t sendData[40];
        int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, setG3SystemTime,
                                                      (ret == 0 ? PCConfigureDataPacket::RESULT_SUCCESS
                                                                : PCConfigureDataPacket::RESULT_FAILED));
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);

        } else {
            printf("generateUniversalAnswerCode fialed! \n");
        }

        return 0;
    }

    void
    G3SocketComManager::onGetGetG3AndG4DeviceVersion(GetG3AndG4DevicesVersion &getG3AndG4DevicesVersion, const char *ip,
                                                     const int port) {
        /* 直接回复 */
        uint8_t respondG3AndG4DevicesVersionCodes[50];
        uint8_t sendData[100];

        std::string versionStr = "";
        switch (getG3AndG4DevicesVersion.getDevicesId()) {
            case GetG3AndG4DevicesVersion::DEVICESID_G3_1:
            case GetG3AndG4DevicesVersion::DEVICESID_G3_2: {
                versionStr.append(getenv("G3_SOFTWARD_VERSION_NAME"));
                versionStr.append("_");
                versionStr.append(getenv("G3_FIRMWARE_VERSION"));
                versionStr.append("_");
                versionStr.append(getenv("G3_CAM1_ALG_VERSION"));
                versionStr.append("_");
                versionStr.append(getenv("G3_CAM2_ALG_VERSION"));


            }
                break;

            case GetG3AndG4DevicesVersion::DEVICESID_MCU: {
                versionStr.append(getenv("G3_MCU_VERSION_NAME"));
            }
                break;

            case GetG3AndG4DevicesVersion::DEVICESID_L3: {
                versionStr.append(getenv("G3_L3_VERSION_NAME"));
            }
                break;

            case GetG3AndG4DevicesVersion::DEVICESID_L4: {
                versionStr.append(getenv("G3_L4_VERSION_NAME"));
            }
                break;
            case GetG3AndG4DevicesVersion::DEVICESID_GPSBOX: {
                versionStr.append(getenv("G3_GPSBOX_VERSION_NAME"));
            }
                break;
        }
        RespondG3AndG4DevicesVersion g3AndG4DevicesVersion;
        g3AndG4DevicesVersion.setDevicesId(getG3AndG4DevicesVersion.getDevicesId());
        g3AndG4DevicesVersion.setVersionStr(versionStr);

        int len = encoder.generateG3AndG4DeviceVersion(respondG3AndG4DevicesVersionCodes, 50, g3AndG4DevicesVersion);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(respondG3AndG4DevicesVersionCodes,
                                                                                  len, 1,
                                                                                  len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);

//        printf("onGetSetG3SystemTime to %s:%d  sendLen:%d  content:", ip, port, sendLen);
//        for (int i = 0; i < sendLen; i++) {
//            printf("%02x ", sendData[i]);
//        }
//        printf("\n");

        } else {
            printf("generateG3AndG4DeviceVersion fialed! \n");
        }

        /* 把这条消息传递上去，去再获取一次版本号 */
        callback->onGetGetG3AndG4DeviceVersion(getG3AndG4DevicesVersion, ip, port);

    }

    void G3SocketComManager::setDSMInfo(G3DetectionResult &detectionInfo) {
        /* 塞人脸数据 */
        DetectionInfo_DSM detectionInfoDsmTemp;
        detectionInfoDsmTemp.setCurCameraId(detectionInfo.curCameraType.cameraId);
        if (detectionInfo.faceList[0].face_point.size() > 0) {
            detectionInfoDsmTemp.setHasFace(1);
            detectionInfoDsmTemp.setIsRespirator(detectionInfo.faceList[0].respirator_alarm);
            detectionInfoDsmTemp.setIsSameDriver(detectionInfo.faceList[0].isSameDriver);
            detectionInfoDsmTemp.setFaceScore(detectionInfo.faceList[0].face_score * 100);
            detectionInfoDsmTemp.setFaceTop(detectionInfo.faceList[0].face_point[0].y);
            detectionInfoDsmTemp.setFaceBottom(detectionInfo.faceList[0].face_point[1].y);
            detectionInfoDsmTemp.setFaceLeft(detectionInfo.faceList[0].face_point[0].x);
            detectionInfoDsmTemp.setFaceRight(detectionInfo.faceList[0].face_point[1].x);
            detectionInfoDsmTemp.setFaceangle(detectionInfo.faceList[0].face_angle);
            if (detectionInfo.faceList[0].five_point.size() > 0) {
                uint8_t fivePointsTemp[detectionInfo.faceList[0].five_point.size() * 4];
                for (std::size_t i = 0; i < detectionInfo.faceList[0].five_point.size(); i++) {
                    CodeUtils::getInstance().uint16ToBb(detectionInfo.faceList[0].five_point[i].x,
                                                        fivePointsTemp + (i * 4));
                    CodeUtils::getInstance().uint16ToBb(detectionInfo.faceList[0].five_point[i].y,
                                                        fivePointsTemp + (i * 4) + 2);
                }
                detectionInfoDsmTemp.setFaceCharacteristicPoint(fivePointsTemp,
                                                                detectionInfo.faceList[0].five_point.size() * 4);
            } else {
                detectionInfoDsmTemp.setFaceCharacteristicPointLen(0);
            }
        } else {
            detectionInfoDsmTemp.setHasFace(0);
        }


        detectionInfoLock.lock();
        uint8_t bypassInfo = 0x00;
        memcpy(&bypassInfo, &detectionInfo.bypassStatusByCamera, 1);
        systemInfo.setBypassInfo(bypassInfo);
        systemInfo.setSpeed(detectionInfo.speed * 10);
        /* 设置镜头遮挡状态和算法耗时 */
        switch (detectionInfo.curCameraType.cameraId) {
            case CAMERA_ID_1: {
                camera1Covered = detectionInfo.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera1(detectionInfo.detectUseTime);
            }
                break;
            case CAMERA_ID_2: {
                camera2Covered = detectionInfo.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera2(detectionInfo.detectUseTime);
            }
                break;
            case CAMERA_ID_3: {
                camera3Covered = detectionInfo.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera3(detectionInfo.detectUseTime);
            }
                break;
            case CAMERA_ID_4: {
                camera4Covered = detectionInfo.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera4(detectionInfo.detectUseTime);
            }
                break;
        }
        /* 设置镜头全黑状态 */
        checkCameraFullBlackStatus(detectionInfo);
        /* 把人脸的识别信息存起来 */
        curDetectionInfo.addDetectionInfoItem_DSMInfo(detectionInfoDsmTemp);

        detectionInfoLock.unlock();

        /* 塞安全带数据 */
        if (!detectionInfo.seatbeltList.empty()) {
            if (!detectionInfo.seatbeltList[0].belt_point.empty()) {
                DetectionInfo_SeatBelt detectionInfoSeatBelt;
                detectionInfoSeatBelt.setCurCameraId(detectionInfo.curCameraType.cameraId);
                uint8_t beltPointsTemp[detectionInfo.seatbeltList[0].belt_point.size() * 4];
                for (std::size_t i = 0; i < detectionInfo.seatbeltList[0].belt_point.size(); i++) {
                    CodeUtils::getInstance().uint16ToBb(detectionInfo.seatbeltList[0].belt_point[i].x,
                                                        beltPointsTemp + (i * 4));
                    CodeUtils::getInstance().uint16ToBb(detectionInfo.seatbeltList[0].belt_point[i].y,
                                                        beltPointsTemp + (i * 4) + 2);
                }
                detectionInfoSeatBelt.setSeatbeltPoint(beltPointsTemp,
                                                       (detectionInfo.seatbeltList[0].belt_point.size() * 4));

                detectionInfoLock.lock();
                curDetectionInfo.addDetectionInfoItem_SeatbeltInfo(detectionInfoSeatBelt);
                detectionInfoLock.unlock();

            } else { ; //not to do
            }
        }


    }

    void G3SocketComManager::setDetectionInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
        DetectionInfo_Gesture detectionInfoGestureTemp;
        detectionInfoGestureTemp.setCurCameraId(detectionResult.curCameraType.cameraId);
        detectionInfoGestureTemp.setGestureListSize(objectInfo.objects.size());
        if (objectInfo.objects.size() > 0) {
            for (std::size_t i = 0; i < objectInfo.objects.size(); i++) {
                DetectionInfo_Gesture_object_ temp;
                (void) memcpy(temp.label, objectInfo.objects[i].label, sizeof(temp.label));
                temp.mId = objectInfo.objects[i].mId;
                temp.mTop = objectInfo.objects[i].mTop;
                temp.mBottom = objectInfo.objects[i].mBottom;
                temp.mLeft = objectInfo.objects[i].mLeft;
                temp.mRight = objectInfo.objects[i].mRight;
                temp.mScore = objectInfo.objects[i].mScore * 100;
                detectionInfoGestureTemp.addObjectToList(temp);
            }
        } else { ; //not to do
        }
        detectionInfoLock.lock();
        uint8_t bypassInfo = 0x00;
        memcpy(&bypassInfo, &detectionResult.bypassStatusByCamera, 1);
        systemInfo.setBypassInfo(bypassInfo);
        systemInfo.setSpeed(detectionResult.speed * 10);
        /* 设置镜头遮挡状态和算法耗时 */
        switch (detectionResult.curCameraType.cameraId) {
            case CAMERA_ID_1: {
                camera1Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera1(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_2: {
                camera2Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera2(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_3: {
                camera3Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera3(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_4: {
                camera4Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera4(detectionResult.detectUseTime);
            }
                break;
        }
        curDetectionInfo.addDetectionInfoItem_GestureInfo(detectionInfoGestureTemp);

        /* 检查下镜头的全黑状态 */
        checkCameraFullBlackStatus(detectionResult);

        detectionInfoLock.unlock();


    }

    void
    G3SocketComManager::onGetGetG3AndG4CPUSerialNumber(GetCPUSerialNum &getCpuSerialNum, const char *ip,
                                                       const int port) {
        /* 收到获取CPU序列号的指令  这里直接回复 */
        uint8_t respondGetCPUSerialNumResultCodes[50];
        uint8_t sendData[100];
        RespondGetCPUSerialNumResult getCpuSerialNumResult;
        getCpuSerialNumResult.setResult(RespondGetCPUSerialNumResult::RESULT_SUCCESS);
        std::string cpuSerialNumberList;
        cpuSerialNumberList.append("00" + G3_Configuration::getInstance().getCpuSerialNumber() + ";");
        getCpuSerialNumResult.setCPUSerialNumber(cpuSerialNumberList);
        int len = encoder.generateGetCPUSerialNumberResult(respondGetCPUSerialNumResultCodes, 50,
                                                           getCpuSerialNumResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(respondGetCPUSerialNumResultCodes,
                                                                                  len, 1,
                                                                                  len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);

//        printf("onGetSetG3SystemTime to %s:%d  sendLen:%d  content:", ip, port, sendLen);
//        for (int i = 0; i < sendLen; i++) {
//            printf("%02x ", sendData[i]);
//        }
//        printf("\n");

        } else {
            printf("generateGetCPUSerialNumberResult fialed! \n");
        }
    }

    void G3SocketComManager::onGetSetG3UUID(SetG3UUID &setG3Uuid, const char *ip, const int port) {
        /* 收到设置UUID的指令  这里直接回复 */
        uint8_t result = RespondGetCPUSerialNumResult::RESULT_SUCCESS;
        if (XuFile::getInstance().fileExists("uuid")) {
            result = RespondGetCPUSerialNumResult::RESULT_FAILED;
        } else {
            int writeLen = XuFile::getInstance().writeFile(G3_Configuration::getInstance().UUID_FILE_PATH,
                                            (uint8_t *) setG3Uuid.getUuidStr().c_str(), setG3Uuid.getUuidStrLen());
            if (writeLen == static_cast<int>(setG3Uuid.getUuidStrLen())) {
                result = RespondGetCPUSerialNumResult::RESULT_SUCCESS;
                G3_Configuration::getInstance().setDeviceUuid(setG3Uuid.getUuidStr());
            } else {
                result = RespondGetCPUSerialNumResult::RESULT_FAILED;
                XuShell::getInstance().runShellWithTimeout("rm uuid");
            }
        }
        uint8_t respondSetUuidResultCodes[50];
        uint8_t sendData[100];
        RespondSetUUIDResult respondSetUuidResult;
        respondSetUuidResult.setResult(result);
        std::string cpuSerialNumberList;
        cpuSerialNumberList.append("00" + G3_Configuration::getInstance().getCpuSerialNumber() + ";");


        respondSetUuidResult.setCPUSerialNumber(cpuSerialNumberList);
        int len = encoder.generateSetG3UUIDResult(respondSetUuidResultCodes, 50, respondSetUuidResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(respondSetUuidResultCodes, len, 1,
                                                                                  len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
//
//        printf("onGetSetG3SystemTime to %s:%d  sendLen:%d  content:", ip, port, sendLen);
//        for (int i = 0; i < sendLen; i++) {
//            printf("%02x ", sendData[i]);
//        }
//        printf("\n");

        } else {
            printf("generateSetG3UUIDResult fialed! \n");
        }
    }

    void G3SocketComManager::onGetGetG3UUID(GetG3UUID &getG3Uuid, const char *ip, const int port) {
        /* 收到获取UUID的指令  这里直接回复 */
        RespondGetUUIDResult respondgetUuidResult;
        if (!G3_Configuration::getInstance().getDeviceUuid().empty()) {
            respondgetUuidResult.setResult(RespondSetUUIDResult::RESULT_SUCCESS);
            respondgetUuidResult.setCurUUID(G3_Configuration::getInstance().getDeviceUuid());
        } else {
            respondgetUuidResult.setResult(RespondSetUUIDResult::RESULT_FAILED);
            respondgetUuidResult.setStrLen(0);
        }

        uint8_t respondGetUuidResultCodes[50];
        uint8_t sendData[100];

        int len = encoder.generateGetG3UUIDResult(respondGetUuidResultCodes, 50, respondgetUuidResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(respondGetUuidResultCodes, len, 1,
                                                                                  len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);

        } else {
            printf("generateGetG3UUIDResult fialed! \n");
        }
    }

    void G3SocketComManager::onGetGetG3UTCTime(GetG3UTCTime &getG3UtcTime, const char *ip, const int port) {
        /* 收到获取设备的BCD格式的UTC时间  这里直接回复 */
        RespondGetG3UTCTimeResult getG3UtcTimeResult;
        uint8_t bdcTime[8];
        if (XuTimeUtil::getInstance().getUTCTime_BCD(bdcTime)) {
            getG3UtcTimeResult.setResult(RespondSetUUIDResult::RESULT_SUCCESS);
            getG3UtcTimeResult.setBcdTime(bdcTime);
        } else {
            getG3UtcTimeResult.setResult(RespondSetUUIDResult::RESULT_FAILED);
        }
        uint8_t respondGetG3TimeResultCodes[50];
        uint8_t sendData[100];

        int len = encoder.generateGetG3UTCTime(respondGetG3TimeResultCodes, 50, getG3UtcTimeResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(respondGetG3TimeResultCodes, len, 1,
                                                                                  len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);

        } else {
            printf("generateGetG3UTCTime fialed! \n");
        }
    }

    int G3SocketComManager::setNewDVRFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &dvrList) {

        if (!dvrList.empty()) {
            dvrFileListLock.lock();
            dvrFileList.clear();
            for (std::size_t i = 0; i < dvrList.size(); i++) {
                dvrFileList.push_back(dvrList[i]);
            }
            dvrFileListLock.unlock();
        }

//
//    for(int i = 0; i < dvrFileList.size(); i ++){
//        printf("*****i=%d    fileType=%d    detectContent=%d    fileStatus=%d  cameraId=%d  bcdTime=%s fileNameLen=%d fileName=%s\n",i,dvrFileList[i].fileType,dvrFileList[i].detectContent,dvrFileList[i].fileStatus,dvrFileList[i].cameraId,XuString::getInstance().byteArrayToString(dvrFileList[i].bcdTime,sizeof(dvrFileList[i].bcdTime)).c_str(),dvrFileList[i].fileNameLen,dvrFileList[i].fileName.c_str());
//    }

        return 0;
    }

    int G3SocketComManager::setNewWarFileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &warList) {

        if (warList.size() > 0) {
            warFileListLock.lock();
            warFileList.clear();
            warFileList.assign(warList.begin(), warList.end());
            warFileListLock.unlock();
        }


//    for(int i = 0; i < warFileList.size(); i ++){
//        printf("****war i=%d    fileType=%d    detectContent=%d    fileStatus=%d  cameraId=%d  bcdTime=%s fileNameLen=%d fileName=%s\n",i,warFileList[i].fileType,warFileList[i].detectContent,warFileList[i].fileStatus,warFileList[i].cameraId,XuString::getInstance().byteArrayToString(warFileList[i].bcdTime,sizeof(warFileList[i].bcdTime)).c_str(),warFileList[i].fileNameLen,warFileList[i].fileName);
//    }


        return 0;
    }

    void G3SocketComManager::onGetGetG3Mp4FileList(GetG3Mp4FileList &getG3Mp4FileList, const char *ip, const int port) {
        std::vector<PCConfigureDataPacket::MP4FileInfo> allFileList;

        /* 看看需不需要获取DVR */
        if (getG3Mp4FileList.getListContent().hasDVR) {
            printf("need get DVR*********************\n");
            if (!dvrFileList.empty()) {
                dvrFileListLock.lock();
                /* 根据相机ID获取DVR */
                for (std::size_t i = 0; i < dvrFileList.size(); i++) {
                    /* 如果是0xFF就说明所有的镜头都要，所以不判断镜头，全加进去 */
                    if (getG3Mp4FileList.getFileCameraId() == 0xFF) {
                        allFileList.push_back(dvrFileList[i]);
                    } else {
                        /* 如果不是FF，就说明是要拿对应的镜头的，那么需要判断镜头ID */
                        if (getG3Mp4FileList.getFileCameraId() == dvrFileList[i].cameraId) {
                            allFileList.push_back(dvrFileList[i]);
                        }
                    }


                }
                dvrFileListLock.unlock();
            } else { ; // not to do
            }
        }

        /* 看看需不需要获取报警 */
        if (getG3Mp4FileList.getListContent().hasBSD_Left || getG3Mp4FileList.getListContent().hasBSD_Right ||
            getG3Mp4FileList.getListContent().hasBSD_Forward || getG3Mp4FileList.getListContent().hasBSD_Backward) {
            if (!warFileList.empty()) {
                warFileListLock.lock();
                for (std::size_t i = 0; i < warFileList.size(); i++) {
                    /* 如果是0xFF就说明所有的镜头都要，所以不判断镜头，全加进去 */
                    if (getG3Mp4FileList.getFileCameraId() == 0xFF) {
                        /* 根据报警类型获取报警视频文件 */
                        if ((getG3Mp4FileList.getListContent().hasBSD_Left &&
                             warFileList[i].fileType == PCConfigureDataPacket::MP4FILETYPE_BSD_LEFT)
                            || (getG3Mp4FileList.getListContent().hasBSD_Right &&
                                warFileList[i].fileType == PCConfigureDataPacket::MP4FILETYPE_BSD_RIGHT)
                            || (getG3Mp4FileList.getListContent().hasBSD_Forward &&
                                warFileList[i].fileType == PCConfigureDataPacket::MP4FILETYPE_BSD_FORWARD)
                            || (getG3Mp4FileList.getListContent().hasBSD_Backward &&
                                warFileList[i].fileType == PCConfigureDataPacket::MP4FILETYPE_BSD_BACKWARD)) {
                            allFileList.push_back(warFileList[i]);
                        } else { ; // not to do
                        }
                    } else {
                        /* 不是0xFF就说明只取某个镜头的，根据相机ID获取报警视频文件 */
                        if (getG3Mp4FileList.getFileCameraId() == warFileList[i].cameraId) {
                            /* 根据报警类型获取报警视频文件 */
                            if ((getG3Mp4FileList.getListContent().hasBSD_Left &&
                                 warFileList[i].fileType == PCConfigureDataPacket::MP4FILETYPE_BSD_LEFT)
                                || (getG3Mp4FileList.getListContent().hasBSD_Right &&
                                    warFileList[i].fileType == PCConfigureDataPacket::MP4FILETYPE_BSD_RIGHT)
                                || (getG3Mp4FileList.getListContent().hasBSD_Forward &&
                                    warFileList[i].fileType == PCConfigureDataPacket::MP4FILETYPE_BSD_FORWARD)
                                || (getG3Mp4FileList.getListContent().hasBSD_Backward &&
                                    warFileList[i].fileType == PCConfigureDataPacket::MP4FILETYPE_BSD_BACKWARD)) {
                                allFileList.push_back(warFileList[i]);
                            } else { ; // not to do
                            }
                        }
                    }
                }
                warFileListLock.unlock();

            } else { ; // not to do
            }
        }


        /* 从旧到新排列一下 */
        sortMp4FileList(allFileList);
        /* 看看是否需要从新到旧 */
        if (getG3Mp4FileList.getSortOrder() == PCConfigureDataPacket::MP4FILELISTORDER_NEW_TO_OLD) {
            std::reverse(allFileList.begin(), allFileList.end());
        }

        /* 截取出规定的部分内容 */
        std::vector<PCConfigureDataPacket::MP4FileInfo> fileListRet;
        if (getG3Mp4FileList.getStartIndex() < allFileList.size()) {
            if (getG3Mp4FileList.getStartIndex() + getG3Mp4FileList.getFileCount() < allFileList.size()) {
                fileListRet.assign(allFileList.begin() + getG3Mp4FileList.getStartIndex(),
                                   allFileList.begin() + getG3Mp4FileList.getStartIndex() +
                                   getG3Mp4FileList.getFileCount());
            } else {
                fileListRet.assign(allFileList.begin() + getG3Mp4FileList.getStartIndex(), allFileList.end());
            }
        }



        /* 收到获取设备的视频文件列表  这里直接回复 */
        GetG3Mp4FileListResult getG3Mp4FileListResult;
        if (fileListRet.empty()) {
            getG3Mp4FileListResult.setResult(GetG3Mp4FileListResult::RESULT_FAILED);
            getG3Mp4FileListResult.setFileArraysSize(0);
        } else {
            getG3Mp4FileListResult.setResult(GetG3Mp4FileListResult::RESULT_SUCCESS);
            getG3Mp4FileListResult.setFileArraysSize(fileListRet.size());
            getG3Mp4FileListResult.setFileInfoList(fileListRet);
        }

        uint8_t respondGetG3Mp4FileListResultCodes[1024 * 1024 * 1];
        uint8_t sendData[1024 * 1024 * 2];

        int len = encoder.generateGetG3Mp4FileListResult(respondGetG3Mp4FileListResultCodes,
                                                         sizeof(respondGetG3Mp4FileListResultCodes),
                                                         getG3Mp4FileListResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(respondGetG3Mp4FileListResultCodes,
                                                                                  len,
                                                                                  1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);

        } else {
            printf("generateGetG3Mp4FileListResult fialed! \n");
        }
    }

    int G3SocketComManager::sortMp4FileList(std::vector<PCConfigureDataPacket::MP4FileInfo> &fileList) {
        std::sort(fileList.begin(), fileList.end(), doMP4FileAscending);
        return 0;
    }

    bool G3SocketComManager::doMP4FileAscending(PCConfigureDataPacket::MP4FileInfo infoA,
                                                PCConfigureDataPacket::MP4FileInfo infoB) {
        uint64_t fileAtime = std::atoll(XuString::getInstance().byteArrayToString(infoA.bcdTime, 8).c_str());
        uint64_t fileBtime = std::atoll(XuString::getInstance().byteArrayToString(infoB.bcdTime, 8).c_str());


        return fileAtime < fileBtime;
    }

    std::vector<PCConfigureDataPacket::MP4FileInfo>
    G3SocketComManager::onGetG3Mp4FileOpt(G3Mp4FileOpt &g3Mp4FileOpt, const char *ip, const int port) {
        printf("G3SocketComManager::onGetG3Mp4FileOpt \n");
        G3Mp4FileOptResult optResult;
        uint8_t respondG3Mp4FileOptResultCodes[1024 * 1024 * 1];
        uint8_t sendData[1024 * 1024 * 2];

        if (g3Mp4FileOpt.getFileListSize() > 0) {
            std::vector<PCConfigureDataPacket::MP4FileInfo> allFailedFileList = callback->onGetG3Mp4FileOpt(
                    g3Mp4FileOpt,
                    ip, port);

            if (allFailedFileList.size() >= g3Mp4FileOpt.getFileListSize()) {
                optResult.setResult(PCConfigureDataPacket::RESULT_FAILED);
                optResult.setFailedFileListSize(0);
            } else {
                if (allFailedFileList.size() == 0) {
                    optResult.setResult(PCConfigureDataPacket::RESULT_SUCCESS);
                    optResult.setFailedFileListSize(0);
                } else {
                    optResult.setResult(PCConfigureDataPacket::RESULT_FAILED_SOME);
                    optResult.setFailedFileListSize(allFailedFileList.size());
                    optResult.setFailedFileList(allFailedFileList);

                }
            }
        } else {
            optResult.setResult(PCConfigureDataPacket::RESULT_FAILED);
            optResult.setFailedFileListSize(0);
        }


        int len = encoder.generateG3Mp4FileOptResult(respondG3Mp4FileOptResultCodes,
                                                     sizeof(respondG3Mp4FileOptResultCodes),
                                                     optResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(respondG3Mp4FileOptResultCodes, len,
                                                                                  1,
                                                                                  len - 2, sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateG3Mp4FileOptResult fialed! \n");
        }

        return g3Mp4FileOpt.getFileList();
    }

    StartG3FileDownloadResult
    G3SocketComManager::onGetStartG3FileDownload(StartG3FileDownload &startG3FileDownload, const char *ip,
                                                 const int port) {
        StartG3FileDownloadResult result = callback->onGetStartG3FileDownload(startG3FileDownload, ip, port);

        uint8_t respondStartG3FileDownloadResultCodes[1024 * 100];
        uint8_t sendData[1024 * 100 * 1];
        int len = encoder.generateStartG3FileDownloadResult(respondStartG3FileDownloadResultCodes,
                                                            sizeof(respondStartG3FileDownloadResultCodes), result);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(respondStartG3FileDownloadResultCodes,
                                                                                  len, 1, len - 2, sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateStartG3FileDownloadResult fialed! \n");
        }
        return result;
    }

    void G3SocketComManager::onGetStartG3DebugMode(StartG3DebugMode &startG3DebugMode, const char *ip, const int port) {

        /* 直接回复失败 */
        uint8_t universalAnswerCode[20];
        uint8_t sendData[40];
        int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, startG3DebugMode,
                                                      PCConfigureDataPacket::RESULT_FAILED);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);

        } else {
            printf("generateUniversalAnswerCode fialed! \n");
        }


        callback->onGetStartG3DebugMode(startG3DebugMode, ip, port);
    }

    void G3SocketComManager::onGetSetFunctionLock(SetFunctionLock &setFunctionLock, const char *ip, const int port) {
        int ret = -1;
        printf("G3SocketComManager::onGetSetFunctionLock  strlock=%s \n", setFunctionLock.getLockStr().c_str());
        /* 看看功能锁内容是不是有长度 */
        if (setFunctionLock.getLockStrLen() > 0) {
            /* 有长度就认为是对的，直接写入 */
            int writeLen =XuFile::getInstance().writeFile(FUNCTIONLOCKFILEPATH,
                                                          (uint8_t *) setFunctionLock.getLockStr().c_str(), setFunctionLock.getLockStr().size());
            if(writeLen == static_cast<int>(setFunctionLock.getLockStr().size())){
                ret = 0;
            }
        }

        /* 先直接回复通用应答 */
        uint8_t universalAnswerCode[20];
        uint8_t sendData[40];
        int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, setFunctionLock,
                                                      (ret == 0 ? PCConfigureDataPacket::RESULT_SUCCESS
                                                                : PCConfigureDataPacket::RESULT_FAILED));
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);

//        printf("onGetSetG3Config to %s:%d  sendLen:%d  content:", ip, port, sendLen);
//        for (int i = 0; i < sendLen; i++) {
//            printf("%02x ", sendData[i]);
//        }
//        printf("\n");

        } else {
            printf("onGetSetFunctionLock generateUniversalAnswerCode fialed! \n");
        }

        callback->onGetSetFunctionLock(setFunctionLock, ip, port);
    }

    void
    G3SocketComManager::onGetGetFunctionLockInfo(GetFunctionLockInfo &getFunctionLockInfo, const char *ip,
                                                 const int port) {
        RespondGetFunctionLockInfoResult getFunctionLockInfoResult;
        uint8_t readBuf[256] = {0x00};
        int readLen = XuFile::getInstance().readFile(G3_Configuration::getInstance().FUNCTIONE_LOCK_FILE_PATH,readBuf, sizeof(readBuf));
        if(readLen > 0){
            getFunctionLockInfoResult.setResult(GetFunctionLockInfo::RESULT_SUCCESS);
            getFunctionLockInfoResult.setStrLen(readLen);
            std::string strlock;
            strlock.append(reinterpret_cast<const char *>(readBuf));
            getFunctionLockInfoResult.setStrfunctionlock(strlock);
        }else{
            getFunctionLockInfoResult.setResult(GetFunctionLockInfo::RESULT_FAILED);
            getFunctionLockInfoResult.setStrLen(0);
        }

        /* 回复 */
        uint8_t getFunctionlockCode[512];
        uint8_t sendData[1024];
        int len = encoder.generateGetFunctionLockInfoResult(getFunctionlockCode, sizeof(getFunctionlockCode),
                                                            getFunctionLockInfoResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(getFunctionlockCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);

        } else {
            printf("generateUniversalAnswerCode fialed! \n");
        }


    }

    void G3SocketComManager::onGetRestartMRV220(RestartG3 &restartG3, const char *ip, const int port) {
        /* 收到重启的指令 先判断一下OPT类型  再直接回复一下通用应答 */
        uint8_t universalAnswerCode[20];
        uint8_t sendData[40];
        /* 如果OPT不是定义的  就显示失败 */
        int ret = 1;
        switch (restartG3.getOpt()) {
            case RestartG3::RESTART_OPT_ONLY_SOFTWARD:
            case RestartG3::RESTART_OPT_REBOOT_MRV220: {
                ret = 0;
            }
                break;
            default: {
                ret = 1;
            }
                break;

        }
        int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, restartG3,
                                                      (ret == 0 ? PCConfigureDataPacket::RESULT_SUCCESS
                                                                : PCConfigureDataPacket::RESULT_FAILED));
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("onGetRestartMRV220 generateUniversalAnswerCode fialed! \n");
        }

        /* 进行对应的重启操作 */
        callback->onGetRestartMRV220(restartG3, ip, port);
    }

    void G3SocketComManager::setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {


        /* 先判断下这个报警决策是不是已经缓存起来了，有就修改，没有就添加 */
        if (!adasDetectionInfoList.empty()) {
            /* 先看看有没有同个相机ID和算法的数据的数据 */
            bool hasSameInfo = false;
            /* 先找同一相机ID的 */
            for (std::size_t i = 0; i < adasDetectionInfoList.size(); i++) {
                /* 如果是同一相机ID的，那么就修改它 */
                if ((adasDetectionInfoList[i].detectionResult.curCameraType.cameraId ==
                     detectionResult.curCameraType.cameraId) &&
                    (detectionResult.alarmDecisionType == adasDetectionInfoList[i].detectionResult.alarmDecisionType)) {
                    /* 根据算法类型不同，提取不同的东西 */
                    switch (detectionResult.alarmDecisionType) {
                        case ALARM_DECISION_TYPE_PEDSTRIAN_TTC: {
                            /* 行人跟车就提取objects */
                            /* 由于结构体里面没有裸指针，故直接用=就好了 */
                            adasDetectionInfoList[i].detectionResult = detectionResult;
                            adasDetectionInfoList[i].curobjectInfo.objects.clear();
                            adasDetectionInfoList[i].curobjectInfo.objects.insert(
                                    adasDetectionInfoList[i].curobjectInfo.objects.begin(), objectInfo.objects.begin(),
                                    objectInfo.objects.end());

                        }
                            break;
                        case ALARM_DECISION_TYPE_VEHICLE_TTC:
                        case ALARM_DECISION_TYPE_SPG_ADAS_VEHICLE: {
                            /* 行人跟车就提取objects */
                            /* 由于结构体里面没有裸指针，故直接用=就好了 */
                            adasDetectionInfoList[i].detectionResult = detectionResult;
                            adasDetectionInfoList[i].curobjectInfo.objects.clear();
                            adasDetectionInfoList[i].curobjectInfo.objects.insert(
                                    adasDetectionInfoList[i].curobjectInfo.objects.begin(), objectInfo.objects.begin(),
                                    objectInfo.objects.end());

                        }
                            break;
                        case ALARM_DECISION_TYPE_LANELINE: {
                            /* 车道线就提取lanes */
                            /* 由于结构体里面没有裸指针，故直接用=就好了 */
                            adasDetectionInfoList[i].detectionResult = detectionResult;
                            adasDetectionInfoList[i].curobjectInfo.lanes.clear();
                            adasDetectionInfoList[i].curobjectInfo.lanes.insert(
                                    adasDetectionInfoList[i].curobjectInfo.lanes.begin(), objectInfo.lanes.begin(),
                                    objectInfo.lanes.end());

                        }
                            break;

                        case ALARM_DECISION_TYPE_TRAFFIC_SIGN: {
                            /* 交通标示就提取traffics */
                            /* 由于结构体里面没有裸指针，故直接用=就好了 */
                            adasDetectionInfoList[i].detectionResult = detectionResult;
                            adasDetectionInfoList[i].curobjectInfo.traffics.clear();
                            for(std::size_t j = 0; j < objectInfo.traffics.size(); j ++){
                                adasDetectionInfoList[i].curobjectInfo.traffics.push_back(objectInfo.traffics[j]);
                            }

                        }
                            break;

                        default:{
                            ; // not to do
                        }
                            break;

                    }

                    /* maskbuf一般情况下都是相同的 */
                    memcpy(adasDetectionInfoList[i].curobjectInfo.maskBuf, objectInfo.maskBuf,
                           sizeof(adasDetectionInfoList[i].curobjectInfo.maskBuf));
                    hasSameInfo = true;

                }

            }

            if (!hasSameInfo) {

                ADASDetectionInfoAll temp;
                /* 由于结构体里面没有裸指针，故直接用=就好了 */
                temp.detectionResult = detectionResult;
                temp.curobjectInfo.lanes.insert(temp.curobjectInfo.lanes.begin(), objectInfo.lanes.begin(),
                                                objectInfo.lanes.end());
                temp.curobjectInfo.traffics.insert(temp.curobjectInfo.traffics.begin(), objectInfo.traffics.begin(),
                                                objectInfo.traffics.end());
                memcpy(temp.curobjectInfo.maskBuf, objectInfo.maskBuf, sizeof(objectInfo.maskBuf));
                adasDetectionInfoList.push_back(temp);
            }
        } else {
            ADASDetectionInfoAll temp;
            /* 由于结构体里面没有裸指针，故直接用=就好了 */
            temp.detectionResult = detectionResult;
            temp.curobjectInfo.lanes.insert(temp.curobjectInfo.lanes.begin(), objectInfo.lanes.begin(),
                                            objectInfo.lanes.end());
            temp.curobjectInfo.traffics.insert(temp.curobjectInfo.traffics.begin(), objectInfo.traffics.begin(),
                                            objectInfo.traffics.end());
            memcpy(temp.curobjectInfo.maskBuf, objectInfo.maskBuf, sizeof(objectInfo.maskBuf));
            adasDetectionInfoList.push_back(temp);

        }
        /* 定义ADAS的数据 */
        DetectionInfo_ADAS detectionInfoAdasTemp;
        detectionInfoAdasTemp.setCurCameraId(detectionResult.curCameraType.cameraId);
        /* 把同一个镜头ID的数据放在一起 */
        for (std::size_t i = 0; i < adasDetectionInfoList.size(); i++) {
            if (adasDetectionInfoList[i].detectionResult.curCameraType.cameraId ==
                detectionResult.curCameraType.cameraId) {
                /* 先塞物体数组 */
                if (!adasDetectionInfoList[i].curobjectInfo.objects.empty()) {
                    for (std::size_t j = 0; j < adasDetectionInfoList[i].curobjectInfo.objects.size(); j++) {
                        DetectionInfo_ADAS_object_ objectTemp;
                        memcpy(objectTemp.label, adasDetectionInfoList[i].curobjectInfo.objects[j].label,
                               sizeof(objectTemp.label));
                        objectTemp.mId = adasDetectionInfoList[i].curobjectInfo.objects[j].mId;
                        objectTemp.mTop = adasDetectionInfoList[i].curobjectInfo.objects[j].mTop;
                        objectTemp.mBottom = adasDetectionInfoList[i].curobjectInfo.objects[j].mBottom;
                        objectTemp.mLeft = adasDetectionInfoList[i].curobjectInfo.objects[j].mLeft;
                        objectTemp.mRight = adasDetectionInfoList[i].curobjectInfo.objects[j].mRight;
                        objectTemp.mXDistance = adasDetectionInfoList[i].curobjectInfo.objects[j].mXDistance;
                        objectTemp.mYDistance = adasDetectionInfoList[i].curobjectInfo.objects[j].mYDistance;
                        objectTemp.mRealWidth = adasDetectionInfoList[i].curobjectInfo.objects[j].mRealWidth;
                        objectTemp.mRealHeight = adasDetectionInfoList[i].curobjectInfo.objects[j].mRealHeight;
                        objectTemp.mXVelocity = adasDetectionInfoList[i].curobjectInfo.objects[j].mXVelocity;
                        objectTemp.mYVelocity = adasDetectionInfoList[i].curobjectInfo.objects[j].mYVelocity;
                        objectTemp.mTrend = adasDetectionInfoList[i].curobjectInfo.objects[j].mTrend;
                        objectTemp.mHeadway = adasDetectionInfoList[i].curobjectInfo.objects[j].mHeadway * 10;
                        objectTemp.mTTC = adasDetectionInfoList[i].curobjectInfo.objects[j].mTTC * 10;
                        objectTemp.mIsAheadCar = adasDetectionInfoList[i].curobjectInfo.objects[j].mIsAheadObj;
                        objectTemp.mScore = adasDetectionInfoList[i].curobjectInfo.objects[j].mScore * 100;
//                        objectTemp.mDetScore = adasDetectionInfoList[i].curobjectInfo.objects[j].mScore * 100;
                        detectionInfoAdasTemp.addObjectList(objectTemp);


                    }
                }



                /* 再塞线数组 */
                if (!adasDetectionInfoList[i].curobjectInfo.lanes.empty()) {
                    for (std::size_t j = 0; j < adasDetectionInfoList[i].curobjectInfo.lanes.size(); j++) {
                        DetectionInfo_ADAS_Laneline_ lanelineTemp;
                        memcpy(lanelineTemp.label, adasDetectionInfoList[i].curobjectInfo.lanes[j].label,
                               sizeof(lanelineTemp.label));
                        lanelineTemp.mId = adasDetectionInfoList[i].curobjectInfo.lanes[j].mId;
                        lanelineTemp.mEndpointAX = adasDetectionInfoList[i].curobjectInfo.lanes[j].mEndpointAX;
                        lanelineTemp.mEndpointAY = adasDetectionInfoList[i].curobjectInfo.lanes[j].mEndpointAY;
                        lanelineTemp.mEndpointBX = adasDetectionInfoList[i].curobjectInfo.lanes[j].mEndpointBX;
                        lanelineTemp.mEndpointBY = adasDetectionInfoList[i].curobjectInfo.lanes[j].mEndpointBY;
                        lanelineTemp.mXDistance = adasDetectionInfoList[i].curobjectInfo.lanes[j].mXDistance;
                        lanelineTemp.mCrossSpeed = adasDetectionInfoList[i].curobjectInfo.lanes[j].mCrossSpeed;
                        lanelineTemp.mTTLC = adasDetectionInfoList[i].curobjectInfo.lanes[j].mTTLC;
                        lanelineTemp.mAngleToEgo = adasDetectionInfoList[i].curobjectInfo.lanes[j].mAngleToEgo;


                        uint8_t tempBytes[8] = {0x00};


                        memcpy(&tempBytes, &adasDetectionInfoList[i].curobjectInfo.lanes[j].mA0, sizeof(tempBytes));
                        for (int k = 0; k < 8; k++) {
                            lanelineTemp.mA0[k] = tempBytes[7 - k];
                        }
                        memcpy(&tempBytes, &adasDetectionInfoList[i].curobjectInfo.lanes[j].mA1, sizeof(tempBytes));
                        for (int k = 0; k < 8; k++) {
                            lanelineTemp.mA1[k] = tempBytes[7 - k];
                        }
                        memcpy(&tempBytes, &adasDetectionInfoList[i].curobjectInfo.lanes[j].mA2, sizeof(tempBytes));
                        for (int k = 0; k < 8; k++) {
                            lanelineTemp.mA2[k] = tempBytes[7 - k];
                        }
                        memcpy(&tempBytes, &adasDetectionInfoList[i].curobjectInfo.lanes[j].mA3, sizeof(tempBytes));
                        for (int k = 0; k < 8; k++) {
                            lanelineTemp.mA3[k] = tempBytes[7 - k];
                        }

                        lanelineTemp.mIsLeftLane = adasDetectionInfoList[i].curobjectInfo.lanes[j].mIsLeftLane;
                        lanelineTemp.mIsRightLane = adasDetectionInfoList[i].curobjectInfo.lanes[j].mIsRightLane;
                        detectionInfoAdasTemp.addLaneLineList(lanelineTemp);

//                        printf("after i=%d  PA=%d,%d   PB=%d,%d   A0=%d  A1=%d  A2=%d  A3=%d\n",i,lanelineTemp.mEndpointAX,lanelineTemp.mEndpointAY,lanelineTemp.mEndpointBX,lanelineTemp.mEndpointBY,lanelineTemp.mA0,lanelineTemp.mA1,lanelineTemp.mA2,lanelineTemp.mA3);

                    }
                }

                /* 再塞交通标志数组 */
                if (!adasDetectionInfoList[i].curobjectInfo.traffics.empty()) {
                    for (std::size_t j = 0; j < adasDetectionInfoList[i].curobjectInfo.traffics.size(); j++) {
                        DetectionInfo_ADAS_markers_ markerTemp;
                        memcpy(markerTemp.label, adasDetectionInfoList[i].curobjectInfo.traffics[j].label,
                               sizeof(markerTemp.label));
                        markerTemp.mId = adasDetectionInfoList[i].curobjectInfo.traffics[j].mId;
                        markerTemp.mTop = adasDetectionInfoList[i].curobjectInfo.traffics[j].mTop;
                        markerTemp.mBottom = adasDetectionInfoList[i].curobjectInfo.traffics[j].mBottom;
                        markerTemp.mLeft = adasDetectionInfoList[i].curobjectInfo.traffics[j].mLeft;
                        markerTemp.mRight = adasDetectionInfoList[i].curobjectInfo.traffics[j].mRight;
                        markerTemp.mRealWidth = adasDetectionInfoList[i].curobjectInfo.traffics[j].mRealWidth;
                        markerTemp.mRealHeight = adasDetectionInfoList[i].curobjectInfo.traffics[j].mRealHeight;
                        markerTemp.mXVelocity = adasDetectionInfoList[i].curobjectInfo.traffics[j].mXVelocity;
                        markerTemp.mYVelocity = adasDetectionInfoList[i].curobjectInfo.traffics[j].mYVelocity;
                        markerTemp.mSpeedLimit = adasDetectionInfoList[i].curobjectInfo.traffics[j].mSpeedLimit;
                        memcpy(markerTemp.mTrafficLight,
                               adasDetectionInfoList[i].curobjectInfo.traffics[j].mTrafficLight,
                               sizeof(markerTemp.mTrafficLight));
                        memcpy(markerTemp.mTrafficSign, adasDetectionInfoList[i].curobjectInfo.traffics[j].mTrafficSign,
                               sizeof(markerTemp.mTrafficSign));
                        markerTemp.mTrafficSignValue = adasDetectionInfoList[i].curobjectInfo.traffics[j].mTrafficSignValue;
                        detectionInfoAdasTemp.addMarkersList(markerTemp);
                    }
                }

                //FIXME 这里如果同时出现两个TSR且结果不相同，这里无法整合所有的，需要考虑下
                /* 最高最低限速值填一下 */
                systemInfo.setSpeedLimitMax(adasDetectionInfoList[i].detectionResult.adasDetectionInfo.speedLimit_max);
                systemInfo.setSpeedLimitMin(adasDetectionInfoList[i].detectionResult.adasDetectionInfo.speedLimit_min);
                /* 再塞maskBuf */
                detectionInfoAdasTemp.setMaskBuf(adasDetectionInfoList[i].curobjectInfo.maskBuf,
                                                 sizeof(adasDetectionInfoList[i].curobjectInfo.maskBuf));


            }

        }
        detectionInfoLock.lock();
        uint8_t bypassInfo = 0x00;
        memcpy(&bypassInfo, &detectionResult.bypassStatusByCamera, 1);

        systemInfo.setBypassInfo(bypassInfo);
        systemInfo.setSpeed(detectionResult.speed * 10);

        /* 设置镜头遮挡状态和算法耗时 */
        switch (detectionResult.curCameraType.cameraId) {
            case CAMERA_ID_1: {
                camera1Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera1(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_2: {
                camera2Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera2(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_3: {
                camera3Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera3(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_4: {
                camera4Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera4(detectionResult.detectUseTime);
            }
                break;
        }

        curDetectionInfo.addDetectionInfoItem_ADASInfo(detectionInfoAdasTemp);

        /* 检查下镜头的全黑状态 */
        checkCameraFullBlackStatus(detectionResult);

        detectionInfoLock.unlock();
    }

    int G3SocketComManager::onGetStartUDPRealviewSeparate(StartUDPRealview_Separate &startUdpRealview, const char *ip,
                                                          const int port) {
        int ret = callback->onGetStartUDPRealviewSeparate(startUdpRealview, ip, port);
        /* 比对下相机ID  匹配上就直接回复成功 */
        uint8_t resultCode[30];
        uint8_t sendData[60];
        int len = encoder.generateStartUDPRealviewResultSeparate(resultCode, sizeof(resultCode),
                                                                 startUdpRealview.getCameraId(),
                                                                 ((ret == 0) ? PCConfigureDataPacket::RESULT_SUCCESS
                                                                             : PCConfigureDataPacket::RESULT_FAILED),
                                                                 startUdpRealview.getUdpPort());
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(resultCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateStartUDPRealviewResultSeparate fialed! \n");
        }
        return ret;
    }

    void G3SocketComManager::setDispalyParamsResultToSocket(const int devicesId, const int result, const char *ip,
                                                            const int port) {

        uint8_t resultCode[30];
        uint8_t sendData[60];


        SetPeripheralDisplayParamsResult setPeripheralDisplayParamsResult;
        setPeripheralDisplayParamsResult.setResult(result);

        int len = encoder.generateSetPeripheralDisplayParamsResult(resultCode, sizeof(resultCode),
                                                                   setPeripheralDisplayParamsResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(resultCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("setDispalyParamsResultToSocket fialed! \n");
        }

    }

    int G3SocketComManager::onGetSetPeripheralDisplayParams(SetPeripheralDisplayParams displayParams, const char *ip,
                                                            const int port) {
        return callback->onGetSetPeripheralDisplayParams(displayParams, ip, port);
    }

    float G3SocketComManager::getCurSpeed() const {
        return curSpeed;
    }

    void G3SocketComManager::setCurSpeed(float curSpeed) {
        G3SocketComManager::curSpeed = curSpeed;
    }

    void G3SocketComManager::checkCameraFullBlackStatus(G3DetectionResult &detectionResult) {
        bool isFullBlack = detectionResult.cameraFullBlack;
        switch (detectionResult.curCameraType.cameraId) {
            case CAMERA_ID_1: {
                cam1FullBlack = isFullBlack;
            }
                break;
            case CAMERA_ID_2: {
                cam2FullBlack = isFullBlack;
            }
                break;
            case CAMERA_ID_3: {
                cam3FullBlack = isFullBlack;
            }
                break;
            case CAMERA_ID_4: {
                cam4FullBlack = isFullBlack;
            }
                break;
        }
    }

    void G3SocketComManager::onGetGetMRV220LogFileList(GetMRV220LogFileList &getMrv220LogFileList, const char *ip,
                                                       const int port) {
        std::vector<std::string> logFileList = XuLog::getInstance().getAllLogFile();
        GetMRV220LogFileListResult fileListResult;
        fileListResult.setResult(PCConfigureDataPacket::RESULT_SUCCESS);
        if (!logFileList.empty()) {
            fileListResult.setFileArraysSize(logFileList.size());
            std::vector<PCConfigureDataPacket::MRV220LogFileInfo> fileInfoList;
            for (std::size_t i = 0; i < logFileList.size(); i++) {
                PCConfigureDataPacket::MRV220LogFileInfo logFileInfo;
                logFileInfo.fileNameLen = logFileList[i].size();
                memcpy(logFileInfo.fileName, logFileList[i].c_str(), logFileList[i].size());
                fileInfoList.push_back(logFileInfo);
            }
            fileListResult.setFileInfoList(fileInfoList);
        } else {
            fileListResult.setFileArraysSize(0);
        }

        uint8_t resultCode[1024 * 200];
        uint8_t sendData[1024 * 400];

        int len = encoder.generateGetMRV220LogFileListResult(resultCode, sizeof(resultCode),
                                                             fileListResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(resultCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateGetMRV220LogFileListResult fialed! \n");
        }
    }

    StartMRV220LogFileDownloadResult
    G3SocketComManager::onGetStartMRV220LogFileDownload(StartMRV220LogFileDownload &startMrv220LogFileDownload,
                                                        const char *ip, const int port) {
        StartMRV220LogFileDownloadResult result = callback->onGetStartMRV220LogFileDownload(startMrv220LogFileDownload,
                                                                                            ip, port);

        uint8_t respondStartG3FileDownloadResultCodes[1024 * 100];
        uint8_t sendData[1024 * 100 * 2];
        int len = encoder.generateStartMRV220LogFileDownloadResult(respondStartG3FileDownloadResultCodes,
                                                                   sizeof(respondStartG3FileDownloadResultCodes),
                                                                   result);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(respondStartG3FileDownloadResultCodes,
                                                                                  len, 1, len - 2, sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateStartMRV220LogFileDownloadResult fialed! \n");
        }
        return result;
    }

    void G3SocketComManager::onGetGetMultimediaFileEncryptionKey(
            GetMultimediaFileEncryptionKey &getMultimediaFileEncryptionKey, const char *ip, const int port) {

        /* 先定义内存 */
        uint8_t respondMultimediaFileEncryptionKeyResultCodes[128];
        uint8_t sendData[256];
        RespondMultimediaFileEncryptionKey respondMultimediaFileEncryptionKey;
        /* 尝试读出密钥文件内容 */
        uint8_t fileBuf[100] = {0x00};
        int readLen = XuFile::getInstance().readFile(MULTIMEDIAFILEENCRYPTIONKEYPATH, fileBuf, sizeof(fileBuf));
        /* 根据读密钥文件的结果返回对应的结构 */
        if (readLen > 0) {
            respondMultimediaFileEncryptionKey.setResult(PCConfigureDataPacket::RESULT_SUCCESS);
            respondMultimediaFileEncryptionKey.setKeyContecnt(fileBuf, readLen);
        } else {
            respondMultimediaFileEncryptionKey.setResult(PCConfigureDataPacket::RESULT_FAILED);
            respondMultimediaFileEncryptionKey.setKeyLen(0);
        }
        /* 封装然后发送 */
        int len = encoder.generateRespondMultimediaFileEncryptionKey(respondMultimediaFileEncryptionKeyResultCodes,
                                                                     sizeof(respondMultimediaFileEncryptionKeyResultCodes),
                                                                     respondMultimediaFileEncryptionKey);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(
                    respondMultimediaFileEncryptionKeyResultCodes,
                    len, 1, len - 2, sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateRespondMultimediaFileEncryptionKey fialed! \n");
        }

    }

    int G3SocketComManager::onGetSetMultimediaFileEncryptionKey(
            SetMultimediaFileEncryptionKey &setMultimediaFileEncryptionKey, const char *ip, const int port) {

        /* 先直接回复通用应答 */
        uint8_t universalAnswerCode[20];
        uint8_t sendData[40];

        /* 先让外面处理一下 */
        int setResult = callback->onGetSetMultimediaFileEncryptionKey(setMultimediaFileEncryptionKey, ip, port);
        /* 结果封装好发出去 */
        int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, setMultimediaFileEncryptionKey,
                                                      (setResult == 0) ? PCConfigureDataPacket::RESULT_SUCCESS
                                                                       : PCConfigureDataPacket::RESULT_FAILED);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateUniversalAnswerCode fialed! \n");
        }

        return 0;
    }

    int G3SocketComManager::onGetGetAlarmSoundFileConf(GetAlarmSoundFileConf getAlarmSoundFileConf, const char *ip,
                                                       const int port) {

        /* 读出配置文件 */
        VIS::XuMemGeter<uint8_t> confContent(1024 * 512);

        int confLen = XuFile::getInstance().readFile(ALARM_SOUND_FILE_CONFIG_PATH, confContent.getPtr(),
                                                     confContent.getSize());
        RespondAlarmSoundFileConf respondAlarmSoundFileConf;
        if (confLen > 0) {
            respondAlarmSoundFileConf.setResult(0x00);
            respondAlarmSoundFileConf.setConfLen(confLen);
            respondAlarmSoundFileConf.setConfContent(confContent.getPtr());
        } else {
            respondAlarmSoundFileConf.setResult(0x01);
        }

        /* 结果封装好发出去 */
        VIS::XuMemGeter<uint8_t> answerCode(1024 * 513);
        VIS::XuMemGeter<uint8_t> sendData(1024 * 1024);


        int len = encoder.generateRespondAlarmSoundFileConf(answerCode.getPtr(), answerCode.getSize(),
                                                            respondAlarmSoundFileConf);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(answerCode.getPtr(), len, 1, len - 2,
                                                                                  sendData.getPtr());
            callback->onNeedSendDataToSocket(ip, port, sendData.getPtr(), sendLen);
        } else {
            printf("generateRespondAlarmSoundFileConf fialed! \n");
        }


        return 0;
    }

    void G3SocketComManager::setDetectInfo_CameraStatus(G3DetectionResult &detectionResult) {
        detectionInfoLock.lock();
        uint8_t bypassInfo = 0x00;
        memcpy(&bypassInfo, &detectionResult.bypassStatusByCamera, 1);
        systemInfo.setBypassInfo(bypassInfo);
        systemInfo.setSpeed(detectionResult.speed * 10);
        /* 设置镜头遮挡状态和算法耗时 */
        switch (detectionResult.curCameraType.cameraId) {
            case CAMERA_ID_1: {
                camera1Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera1(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_2: {
                camera2Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera2(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_3: {
                camera3Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera3(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_4: {
                camera4Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera4(detectionResult.detectUseTime);
            }
                break;
        }
        /* 检查下镜头的全黑状态 */
        checkCameraFullBlackStatus(detectionResult);

        detectionInfoLock.unlock();
    }

    void G3SocketComManager::onGetGetCameraInputTypeFilterConfig(
            GetCameraInputTypeFilterConfig &getCameraInputTypeFilterConfig, const char *ip, const int port) {
        /* 先定义内存 */
        uint8_t respondCameraInputTypeFilterConfigResultCodes[20];
        uint8_t sendData[40];
        RespondGetCameraInputTypeFilterConfigResult result;
        // 结果先直接强制设置成功好了
        result.setResult(RespondGetCameraInputTypeFilterConfigResult::RESULT_SUCCESS);
        // 直接从全局变量读过滤器的信息
        uint8_t cameraInputTypeFilterConfig = 0x00;
        Camera_Input_Type_Filter filterConfig = G3_Configuration::getInstance().getCameraInputTypeFilter();
        memcpy(&cameraInputTypeFilterConfig,&filterConfig,1);
        printf("cameraInputTypeFilterConfig=%d \n",cameraInputTypeFilterConfig);
        result.setCameraInputTypeFilterConfig(cameraInputTypeFilterConfig);

        /* 封装然后发送 */
        int len = encoder.generateRespondGetCameraInputTypeFilterConfigResult(respondCameraInputTypeFilterConfigResultCodes,
                                                                     sizeof(respondCameraInputTypeFilterConfigResultCodes),
                                                                              result);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(
                    respondCameraInputTypeFilterConfigResultCodes,
                    len, 1, len - 2, sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateRespondGetCameraInputTypeFilterConfigResult fialed! \n");
        }

    }

    int G3SocketComManager::onGetSetCameraInputTypeFilterConfig(
            SetCameraInputTypeFilterConfig &setCameraInputTypeFilterConfig, const char *ip, const int port) {
            /* 先直接回复通用应答 */
            uint8_t universalAnswerCode[20];
            uint8_t sendData[40];

            /* 先让外面处理一下 */
            int setResult = callback->onGetSetCameraInputTypeFilterConfig(setCameraInputTypeFilterConfig, ip, port);
            /* 结果封装好发出去 */
            int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, setCameraInputTypeFilterConfig,
                                                          (setResult == 0) ? PCConfigureDataPacket::RESULT_SUCCESS
                                                                           : PCConfigureDataPacket::RESULT_FAILED);
            if (len > 0) {
                int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                      sendData);
                callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
            } else {
                printf("generateUniversalAnswerCode fialed! \n");
            }

            return 0;
        }

    void G3SocketComManager::setDetectInfo_BarCode(G3DetectionResult &detectionResult,
                                                   std::vector<BarCodeInfo> &barCodeInfoList) {

        /* 先判断下这个报警决策是不是已经缓存起来了，有就修改，没有就添加 */
        if (!barCodeDetectionInfoList.empty()) {
            /* 先看看有没有同个相机ID和算法的数据的数据 */
            bool hasSameInfo = false;
            /* 先找同一报警决策类型的 */
            for (int i = 0; i < (int)barCodeDetectionInfoList.size(); i++) {
                /* 如果有同一报警决策类型的那么就找下同一相机ID的 */
                if (barCodeDetectionInfoList[i].detectionResult.alarmDecisionType == detectionResult.alarmDecisionType) {
                    /* 如果还是同一相机ID的，那么就修改它 */
                    if (barCodeDetectionInfoList[i].detectionResult.curCameraType.cameraId ==
                        detectionResult.curCameraType.cameraId) {
                        /* 由于结构体里面没有裸指针，故直接用=就好了 */
                        barCodeDetectionInfoList[i].detectionResult = detectionResult;
                        barCodeDetectionInfoList[i].barcodeDetectInfoList.clear();
                        barCodeDetectionInfoList[i].barcodeDetectInfoList.insert(
                                barCodeDetectionInfoList[i].barcodeDetectInfoList.begin(), barCodeInfoList.begin(),
                                barCodeInfoList.end());

                        hasSameInfo = true;
                    }
                }
            }

            if (!hasSameInfo) {
                BarCodeDetectionInfoAll temp;
                /* 由于结构体里面没有裸指针，故直接用=就好了 */
                temp.detectionResult = detectionResult;
                temp.barcodeDetectInfoList.insert(temp.barcodeDetectInfoList.begin(), barCodeInfoList.begin(),
                                                  barCodeInfoList.end());
                barCodeDetectionInfoList.push_back(temp);
            }
        } else {
            BarCodeDetectionInfoAll temp;
            /* 由于结构体里面没有裸指针，故直接用=就好了 */
            temp.detectionResult = detectionResult;
            temp.barcodeDetectInfoList.insert(temp.barcodeDetectInfoList.begin(), barCodeInfoList.begin(),
                                              barCodeInfoList.end());
            barCodeDetectionInfoList.push_back(temp);
        }
        /* 把同一个相机ID所有报警决策对应的条码识别信息都放到一起 */
        DetectionInfo_BarCodeInfos detectionInfoBarCodeInfos;
        detectionInfoBarCodeInfos.setCameraId(detectionResult.curCameraType.cameraId);
        for (std::size_t i = 0; i < barCodeDetectionInfoList.size(); i++) {
            if (barCodeDetectionInfoList[i].detectionResult.curCameraType.cameraId ==
                detectionResult.curCameraType.cameraId) {
                for (std::size_t j = 0; j < barCodeDetectionInfoList[i].barcodeDetectInfoList.size(); j++) {
                    DetectionInfo_BarCodeInfos::G3_BarCodeInfo tempBarcodeInfo;
                    /* 条码信息的长度不用填 */

                    /* 条码是否可用 */
                    tempBarcodeInfo.isCodeInfoAvailable =  barCodeDetectionInfoList[i].barcodeDetectInfoList[j].isCodeInfoAvailable;
                    /* 框的顶部y坐标 */
                    tempBarcodeInfo.mTop =  barCodeDetectionInfoList[i].barcodeDetectInfoList[j].mTop;
                    /* 框的底部y坐标 */
                    tempBarcodeInfo.mBottom =  barCodeDetectionInfoList[i].barcodeDetectInfoList[j].mBottom;
                    /* 框的左边x坐标 */
                    tempBarcodeInfo.mLeft =  barCodeDetectionInfoList[i].barcodeDetectInfoList[j].mLeft;
                    /* 框的右边x坐标 */
                    tempBarcodeInfo.mRight =  barCodeDetectionInfoList[i].barcodeDetectInfoList[j].mRight;
                    /* 条码类型 */
                    switch (barCodeDetectionInfoList[i].barcodeDetectInfoList[j].barcodeType) {
                        case BARCODE_TYPE_QRCODE:{
                            tempBarcodeInfo.barcodeType = DetectionInfo_BarCodeInfos::BARCODE_TYPE_QRCODE;
                        }
                            break;
                        case BARCODE_TYPE_CODE128:{
                            tempBarcodeInfo.barcodeType = DetectionInfo_BarCodeInfos::BARCODE_TYPE_CODE128;
                        }
                            break;
                    }
                    /* 完整信息 */
                    tempBarcodeInfo.code_complete_info =  barCodeDetectionInfoList[i].barcodeDetectInfoList[j].code_complete_info;
                    /* 关键信息 */
                    tempBarcodeInfo.code_key_info =  barCodeDetectionInfoList[i].barcodeDetectInfoList[j].code_key_info;
                    /* 添加进去 */
                    detectionInfoBarCodeInfos.addBarCodeInfo(tempBarcodeInfo);

                    printf("===========i=%d   %s  \n",i,tempBarcodeInfo.code_complete_info.c_str());
                }
            }
        }
        /* 这里就不设置车速了 */
        systemInfo.setSpeed(detectionResult.speed * 10);
        /* 设置镜头遮挡状态和算法耗时 */
        switch (detectionResult.curCameraType.cameraId) {
            case CAMERA_ID_1: {
                camera1Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera1(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_2: {
                camera2Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera2(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_3: {
                camera3Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera3(detectionResult.detectUseTime);
            }
                break;
            case CAMERA_ID_4: {
                camera4Covered = detectionResult.isCameraCover;
                algDetectTime.setAlgDtectTimeCamera4(detectionResult.detectUseTime);
            }
                break;
        }

        detectionInfoLock.lock();
        /* 添加一下条码识别的信息 */
        curDetectionInfo.addDetectionInfoItem_BarCodeInfos(detectionInfoBarCodeInfos);
        /* 检查下镜头的全黑状态 */
        checkCameraFullBlackStatus(detectionResult);
        detectionInfoLock.unlock();
    }

    void
    G3SocketComManager::onGetGetCameraReduceEffectInfoFromSocket(GetCameraReduceEffectInfo &getCameraReduceEffectInfo,
                                                                 const char *ip, const int port) {
        /* 先定义内存 */
        uint8_t respondGetCameraReduceEffectInfoResultCodes[50];
        uint8_t sendData[100];
        RespondGetCameraReduceEffectInfoResult result;
        /* 结果先直接强制设置成功好了 */
        result.setResult(RespondGetCameraInputTypeFilterConfigResult::RESULT_SUCCESS);
        /* 直接从全局变量读过滤器的信息 */
        result.setCameraReduceEffectSwitch(G3_Configuration::getInstance().getCurCameraReduceEffectInfo());
        /* 直接从全局变量读有效画面缩放比例 */
        result.setImgZoomRate(G3_Configuration::getInstance().getReduceEffectImgZoomRate());
        /* 封装然后发送 */
        int len = encoder.generateRespondGetCameraReduceEffectInfoResult(respondGetCameraReduceEffectInfoResultCodes,
                                                                              sizeof(respondGetCameraReduceEffectInfoResultCodes),
                                                                              result);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(
                    respondGetCameraReduceEffectInfoResultCodes,
                    len, 1, len - 2, sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateRespondGetCameraReduceEffectInfoResult fialed! \n");
        }
    }

    int
    G3SocketComManager::onGetSetCameraReduceEffectInfoFromSocket(SetCameraReduceEffectInfo &setCameraReduceEffectInfo,
                                                                 const char *ip, const int port) {
            /* 先直接回复通用应答 */
            uint8_t universalAnswerCode[20];
            uint8_t sendData[40];

            /* 先让外面处理一下 */
            int setResult = callback->onGetSetCameraReduceEffectInfoFromSocket(setCameraReduceEffectInfo, ip, port);
            /* 结果封装好发出去 */
            int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, setCameraReduceEffectInfo,
                                                          (setResult == 0) ? PCConfigureDataPacket::RESULT_SUCCESS
                                                                           : PCConfigureDataPacket::RESULT_FAILED);
            if (len > 0) {
                int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                      sendData);
                callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
            } else {
                printf("generateUniversalAnswerCode fialed! \n");
            }

            return 0;
    }

    int G3SocketComManager::onGetSetProductionTestingSwitchFromSocket(
            SetProductionTestingSwitch &setProductionTestingSwitch, const char *ip, const int port) {
        /* 先直接回复通用应答 */
        uint8_t universalAnswerCode[20];
        uint8_t sendData[40];

        /* 先让外面处理一下 */
        int setResult = callback->onGetSetProductionTestingSwitchFromSocket(setProductionTestingSwitch, ip, port);
        /* 结果封装好发出去 */
        int len = encoder.generateUniversalAnswerCode(universalAnswerCode, 20, setProductionTestingSwitch,
                                                      (setResult == 0) ? PCConfigureDataPacket::RESULT_SUCCESS
                                                                       : PCConfigureDataPacket::RESULT_FAILED);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(universalAnswerCode, len, 1, len - 2,
                                                                                  sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateUniversalAnswerCode fialed! \n");
        }

        return 0;
    }

    void G3SocketComManager::setGSensorData(float value_x, float value_y, float value_z) {
        DetectionInfo_GSensorData::GSensorDataItem gSensorDataItem;
        gSensorDataItem.accel_x = value_x;
        gSensorDataItem.accel_y = value_y;
        gSensorDataItem.accel_z = value_z;
        gsensorListLock.lock();
        gsensorList.push_back(gSensorDataItem);
        gsensorListLock.unlock();
    }

    int
    G3SocketComManager::onGetTCPRealviewOptFromSocket(TCPRealviewOpt &tcpRealviewOpt, const char *ip, const int port) {
        int ret = callback->onGetTCPRealviewOptFromSocket(tcpRealviewOpt, ip, port);
        /* 比对下相机ID  匹配上就直接回复成功 */
        uint8_t resultCode[30];
        uint8_t sendData[60];
        int len = -1;
        TCPRealviewOptResult tcpRealviewOptResult;
        tcpRealviewOptResult.setCameraId(tcpRealviewOpt.getCameraId());
        tcpRealviewOptResult.setResult((ret == 0) ? PCConfigureDataPacket::RESULT_SUCCESS : PCConfigureDataPacket::RESULT_FAILED);
        switch (tcpRealviewOpt.getCameraId()) {
            case CAMERA_ID_1: {
                tcpRealviewOptResult.setPort(G3_TCP_CAMERA1_PORT);
            }
                break;
            case CAMERA_ID_2: {
                tcpRealviewOptResult.setPort(G3_TCP_CAMERA2_PORT);
            }
                break;
            case CAMERA_ID_3: {
                tcpRealviewOptResult.setPort(G3_TCP_CAMERA3_PORT);
            }
                break;
            case CAMERA_ID_4: {
                tcpRealviewOptResult.setPort(G3_TCP_CAMERA4_PORT);
            }
                break;
            default: {
                tcpRealviewOptResult.setPort(-1);
            }
                break;
        }
        len = encoder.generateTCPRealviewOptResult(resultCode, sizeof(resultCode),tcpRealviewOptResult);
        if (len > 0) {
            int sendLen = CodeUtils::getInstance().doEscape4SendFromG3PCConfigure(resultCode, len, 1, len - 2, sendData);
            callback->onNeedSendDataToSocket(ip, port, sendData, sendLen);
        } else {
            printf("generateTCPRealviewOptResult fialed! \n");
        }
    }

    void G3SocketComManager::setIOStatus(const int portNum, const char *value, const int valueLen) {
        //NOTE input口之所有高低电平反过来，为了跟G4保持一致，表示外部电路的ON跟OFF
        switch (portNum) {
            case IO_IN_1_NUM:{
                ioStatus.input_1 = (std::atoi(value) == 1) ? false : true;
            }
                break;
            case IO_IN_2_NUM:{
                ioStatus.input_2 = (std::atoi(value) == 1) ? false : true;
            }
                break;
            case IO_IN_3_NUM:{
                ioStatus.input_3 = (std::atoi(value) == 1) ? false : true;
            }
                break;
            case IO_OUT_1_NUM:{
                ioStatus.output_1 = (std::atoi(value) == 0) ? false : true;
            }
                break;
            case IO_OUT_2_NUM:{
                ioStatus.output_2 = (std::atoi(value) == 0) ? false : true;
            }
                break;
        }
    }

    void G3SocketComManager::setVehicleRealtimeStatus(Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 整理车辆信号 */
        vehicleSignal.acc = vehicleRealtimeStatus.acc;
        vehicleSignal.turnL = vehicleRealtimeStatus.turnL;
        vehicleSignal.turnR = vehicleRealtimeStatus.turnR;
        vehicleSignal.bigLight = vehicleRealtimeStatus.bigLight;
        vehicleSignal.wiper = vehicleRealtimeStatus.wiper;
        vehicleSignal.brake = vehicleRealtimeStatus.brake;
        vehicleSignal.frontDoor = vehicleRealtimeStatus.frontDoor;
        vehicleSignal.backDoor = vehicleRealtimeStatus.backDoor;
        vehicleSignal.reverse = vehicleRealtimeStatus.reverse;
        vehicleSignal.sos = vehicleRealtimeStatus.sosSign;
        vehicleSignal.safepass = vehicleRealtimeStatus.safepassSign;
        vehicleSignal.safebelt = (vehicleRealtimeStatus.safebeltStatus == 0x01);
        vehicleSignal.SecurityMode = vehicleRealtimeStatus.SecurityModeStatus;
        vehicleSignal.c3Switch_c31 = vehicleRealtimeStatus.c3Switch_c31;
        /* 整理发动机转速 */
        systemInfo.setEngineSpeed(vehicleRealtimeStatus.engineSpeed);
        /* 整理设备状态 */
        deviceStatus.internet = G3_Configuration::getInstance().isInternetConnected();
        deviceStatus.ndsRegister = G3_Configuration::getInstance().isNdsRegistered();
        deviceStatus.ndsConnected = G3_Configuration::getInstance().isNdsConnected();

    }


}


