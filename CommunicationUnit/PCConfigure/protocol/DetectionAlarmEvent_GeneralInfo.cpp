//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//


#include "DetectionAlarmEvent_GeneralInfo.h"

DetectionAlarmEvent_GeneralInfo::DetectionAlarmEvent_GeneralInfo() {
    setDataId(DATAID_GENERAL_INFORMATION);
    setDataLen(25);
}

DetectionAlarmEvent_GeneralInfo::~DetectionAlarmEvent_GeneralInfo() {

}

uint32_t DetectionAlarmEvent_GeneralInfo::getEventId() const {
    return eventId;
}

void DetectionAlarmEvent_GeneralInfo::setEventId(uint32_t eventId) {
    DetectionAlarmEvent_GeneralInfo::eventId = eventId;
}

const uint8_t *DetectionAlarmEvent_GeneralInfo::getReserved() const {
    return reserved;
}

uint16_t DetectionAlarmEvent_GeneralInfo::getSpeed() const {
    return speed;
}

void DetectionAlarmEvent_GeneralInfo::setSpeed(uint16_t speed) {
    DetectionAlarmEvent_GeneralInfo::speed = speed;
}

int16_t DetectionAlarmEvent_GeneralInfo::getAltitude() const {
    return altitude;
}

void DetectionAlarmEvent_GeneralInfo::setAltitude(int16_t altitude) {
    DetectionAlarmEvent_GeneralInfo::altitude = altitude;
}

int32_t DetectionAlarmEvent_GeneralInfo::getLatitude() const {
    return latitude;
}

void DetectionAlarmEvent_GeneralInfo::setLatitude(int32_t latitude) {
    DetectionAlarmEvent_GeneralInfo::latitude = latitude;
}

int32_t DetectionAlarmEvent_GeneralInfo::getLongitude() const {
    return longitude;
}

void DetectionAlarmEvent_GeneralInfo::setLongitude(int32_t longitude) {
    DetectionAlarmEvent_GeneralInfo::longitude = longitude;
}

const uint8_t *DetectionAlarmEvent_GeneralInfo::getTimeBcd() const {
    return timeBCD;
}

int DetectionAlarmEvent_GeneralInfo::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度 */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index += 4;
    /* 再塞报警ID */
    CodeUtils::getInstance().uint32ToBb(getEventId(), buf + index);
    index += 4;
    /* 再塞预留字节 */
    memcpy(buf + index, getReserved(), 3);
    index += 3;
    /* 再塞车速 */
    CodeUtils::getInstance().uint16ToBb(getSpeed(), buf + index);
    index += 2;
    /* 再塞高程 */
    CodeUtils::getInstance().int16ToBb(getAltitude(), buf + index);
    index += 2;
    /* 再塞纬度 */
    CodeUtils::getInstance().int32ToBb(getLatitude(), buf + index);
    index += 4;
    /* 再塞经度 */
    CodeUtils::getInstance().int32ToBb(getLongitude(), buf + index);
    index += 4;
    /* 再塞时间 */
    memcpy(buf + index, getTimeBcd(), 6);
    index += 6;

    ret = index;
    return ret;
}

void DetectionAlarmEvent_GeneralInfo::setTimeBcd(uint8_t *timebcd) {
    memcpy(timeBCD, timebcd, 6);
}
