//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/5.
//

#ifndef VIS_G3_SOFTWARE_STARTG3FILEDOWNLOAD_H
#define VIS_G3_SOFTWARE_STARTG3FILEDOWNLOAD_H

#include "PCConfigureDataPacket.h"

class StartG3FileDownload : public PCConfigureDataPacket {
public:
    StartG3FileDownload();

    int decode(uint8_t *buf, int len) override;

    const MP4FileInfo &getFileInfo() const;

    void setFileInfo(const MP4FileInfo &fileInfo);

private:
    PCConfigureDataPacket::MP4FileInfo fileInfo;
};


#endif //VIS_G3_SOFTWARE_STARTG3FILEDOWNLOAD_H
