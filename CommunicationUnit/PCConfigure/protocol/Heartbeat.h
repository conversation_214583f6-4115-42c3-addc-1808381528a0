//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/10.
//

#ifndef VIS_G3_SOFTWARE_HEARTBEAT_H
#define VIS_G3_SOFTWARE_HEARTBEAT_H

#include "PCConfigureDataPacket.h"

class Heartbeat : public PCConfigureDataPacket {
public:
    Heartbeat();

    int toCode(uint8_t *buf, int len) override;

    int decode(uint8_t *buf, int len) override;


private:
    uint8_t g3DeviceId = 0;


};


#endif //VIS_G3_SOFTWARE_HEARTBEAT_H
