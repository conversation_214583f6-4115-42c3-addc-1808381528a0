//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/8.
//

#ifndef VIS_G3_SOFTWARE_SETG3UUID_H
#define VIS_G3_SOFTWARE_SETG3UUID_H

#include <string>
#include "PCConfigureDataPacket.h"

class SetG3UUID : public PCConfigureDataPacket {
public:
    SetG3UUID();

    int decode(uint8_t *buf, int len) override;

    uint32_t getUuidStrLen() const;

    void setUuidStrLen(uint32_t uuidStrLen);

    const std::string &getUuidStr() const;

    void setUuidStr(const std::string &uuidStr);

private:
    /* 配置表内容的长度 */
    uint32_t uuidStrLen;
    /* 配置表的内容 */
    std::string uuidStr;
};


#endif //VIS_G3_SOFTWARE_SETG3UUID_H
