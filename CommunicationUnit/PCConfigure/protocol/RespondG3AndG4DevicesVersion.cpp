//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/15.
//

#include "RespondG3AndG4DevicesVersion.h"

RespondG3AndG4DevicesVersion::RespondG3AndG4DevicesVersion() {
    setMsgId(MSGID_RESPOND_G3_AND_G4_VERSION);
}

uint8_t RespondG3AndG4DevicesVersion::getDevicesId() const {
    return devicesId;
}

void RespondG3AndG4DevicesVersion::setDevicesId(uint8_t devicesId) {
    RespondG3AndG4DevicesVersion::devicesId = devicesId;
}

uint32_t RespondG3AndG4DevicesVersion::getVersionStrLen() const {
    return versionStrLen;
}

void RespondG3AndG4DevicesVersion::setVersionStrLen(uint32_t versionStrLen) {
    RespondG3AndG4DevicesVersion::versionStrLen = versionStrLen;
}

const std::string &RespondG3AndG4DevicesVersion::getVersionStr() const {
    return versionStr;
}

void RespondG3AndG4DevicesVersion::setVersionStr(const std::string &versionStr) {
    RespondG3AndG4DevicesVersion::versionStr = versionStr;
    RespondG3AndG4DevicesVersion::versionStrLen = versionStr.size();

}

int RespondG3AndG4DevicesVersion::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 先随便塞个长度 */
    int realContent = 0;
    setContentLen(0);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 塞设备ID */
    buf[index] = getDevicesId();
    index++;
    realContent++;
    /* 塞版本内容的长度 */
    CodeUtils::getInstance().int32ToBb(getVersionStrLen(), buf + index);
    index += 4;
    realContent += 4;
    /* 塞版本内容 */
    memcpy(buf + index, versionStr.c_str(), getVersionStrLen());
    index += getVersionStrLen();
    realContent += getVersionStrLen();



    /* 塞进去真正得长度 */
    setContentLen(realContent);
    CodeUtils::getInstance().int32ToBb(realContent, buf + 6);
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}
