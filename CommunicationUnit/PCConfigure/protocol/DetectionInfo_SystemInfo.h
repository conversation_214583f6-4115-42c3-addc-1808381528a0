//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/15.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONINFO_SYSTEMINFO_H
#define VIS_G3_SOFTWARE_DETECTIONINFO_SYSTEMINFO_H

#include <vector>
#include <CodeUtils.h>
#include "DetectionInfo_DataItem.h"

class DetectionInfo_SystemInfo : public DetectionInfo_DataItem {
public:
    struct DetectionInfo_SystemInfo_CameraCoverStatus{
        /* bit0-镜头1是否被遮挡 */
        bool camera1Covered : 1;
        /* bit1-镜头2是否被遮挡 */
        bool camera2Covered : 1;
        /* bit2-镜头3是否被遮挡 */
        bool camera3Covered : 1;
        /* bit3-镜头4是否被遮挡 */
        bool camera4Covered : 1;
        int reserved : 4;
    };


    DetectionInfo_SystemInfo();

    ~DetectionInfo_SystemInfo();

    int toCode(uint8_t *buf, int len) override;

    uint16_t getSpeed() const;

    void setSpeed(uint16_t speed);

    uint8_t getBypassInfo() const;

    void setBypassInfo(uint8_t bypassInfo);

    const uint8_t *getReserved() const;

    const DetectionInfo_SystemInfo_CameraCoverStatus &getCameraCoverStatus() const;

    void setCameraCoverStatus(const DetectionInfo_SystemInfo_CameraCoverStatus &cameraCoverStatus);

    uint8_t getSpeedLimitMax() const;

    void setSpeedLimitMax(uint8_t speedLimitMax);

    uint8_t getSpeedLimitMin() const;

    void setSpeedLimitMin(uint8_t speedLimitMin);

    uint16_t getEngineSpeed() const;

    void setEngineSpeed(uint16_t engineSpeed);

private:
    /* 单位 0.1km/h。范围 0~2500 */
    uint16_t speed = 0x00;
    /* 用bit位表示开关  0-否   1-是      bit0-镜头1是否bypass    bit1-镜头2是否bypass */
    uint8_t bypassInfo = 0x00;
    /* 镜头遮挡状态 用bit位表示是否被遮挡  0-否   1-是             bit0-镜头1是否被遮挡    bit1-镜头2是否被遮挡 */
    DetectionInfo_SystemInfo_CameraCoverStatus cameraCoverStatus = {false,false,false,false,0};
    /* 速度上限 0-0xfe（254）；0xff为无效值 */
    uint8_t speedLimit_max = 0xFF;
    /* 速度下限0-0xfe（254）；0xff为无效值 */
    uint8_t speedLimit_min = 0xFF;
    // 发动机转速 单位rpm
    uint16_t engineSpeed = 0xFFFF;
    /* 保留字节 */
    uint8_t reserved[16] = {0x00};


};


#endif //VIS_G3_SOFTWARE_DETECTIONINFO_SYSTEMINFO_H
