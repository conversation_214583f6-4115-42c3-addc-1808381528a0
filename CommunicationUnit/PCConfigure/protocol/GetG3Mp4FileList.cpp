//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/23.
//

#include "GetG3Mp4FileList.h"

GetG3Mp4FileList::GetG3Mp4FileList() {
    setMsgId(MSGID_GET_G3_MP4_FILE_LIST);
}

int GetG3Mp4FileList::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToUint32(buf + index));
        index = index + 4;

        int listContentTemp = CodeUtils::getInstance().BbToUint16(buf + index);
        memcpy(&listContent, &listContentTemp, 2);
        index = index + 2;

        int detectContentTemp = CodeUtils::getInstance().BbToUint16(buf + index);
        memcpy(&detectContent, &detectContentTemp, 2);
        index = index + 2;

        fileCameraId = buf[index];
        index = index + 1;

        sortOrder = buf[index];
        index = index + 1;

        startIndex = CodeUtils::getInstance().BbToUint32(buf + index);
        index = index + 4;

        fileCount = CodeUtils::getInstance().BbToUint16(buf + index);
        index = index + 2;

        ret = 0;
    }
    return ret;
}

const PCConfigureDataPacket::MP4FileListContent &GetG3Mp4FileList::getListContent() const {
    return listContent;
}

void GetG3Mp4FileList::setListContent(const MP4FileListContent &listContent) {
    GetG3Mp4FileList::listContent = listContent;
}

const PCConfigureDataPacket::WarFileDetectContent &GetG3Mp4FileList::getDetectContent() const {
    return detectContent;
}

void GetG3Mp4FileList::setDetectContent(const WarFileDetectContent &detectContent) {
    GetG3Mp4FileList::detectContent = detectContent;
}

uint8_t GetG3Mp4FileList::getFileCameraId() const {
    return fileCameraId;
}

void GetG3Mp4FileList::setFileCameraId(uint8_t fileCameraId) {
    GetG3Mp4FileList::fileCameraId = fileCameraId;
}

uint8_t GetG3Mp4FileList::getSortOrder() const {
    return sortOrder;
}

void GetG3Mp4FileList::setSortOrder(uint8_t sortOrder) {
    GetG3Mp4FileList::sortOrder = sortOrder;
}

uint32_t GetG3Mp4FileList::getStartIndex() const {
    return startIndex;
}

void GetG3Mp4FileList::setStartIndex(uint32_t startIndex) {
    GetG3Mp4FileList::startIndex = startIndex;
}

uint16_t GetG3Mp4FileList::getFileCount() const {
    return fileCount;
}

void GetG3Mp4FileList::setFileCount(uint16_t fileCount) {
    GetG3Mp4FileList::fileCount = fileCount;
}
