//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/19.
//

#include "RespondGetFunctionLockInfoResult.h"

RespondGetFunctionLockInfoResult::RespondGetFunctionLockInfoResult() {
    setMsgId(MSGID_RESPOND_GET_FUNCTIONLOCK_INFO_RESULT);

}

RespondGetFunctionLockInfoResult::~RespondGetFunctionLockInfoResult() {

}

int RespondGetFunctionLockInfoResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 先随便塞个长度 */
    int realContent = 0;
    setContentLen(0);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 塞结果 */
    buf[index] = getResult();
    index++;
    realContent++;
    /* 塞内容的长度 */
    buf[index] = getStrLen();
    index++;
    realContent++;
    /* 塞版本内容 */
    if (getStrLen() > 0) {
        memcpy(buf + index, strfunctionlock.c_str(), getStrLen());
        index += getStrLen();
        realContent += getStrLen();
    }

    /* 塞进去真正得长度 */
    setContentLen(realContent);
    CodeUtils::getInstance().int32ToBb(realContent, buf + 6);
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

uint8_t RespondGetFunctionLockInfoResult::getResult() const {
    return result;
}

void RespondGetFunctionLockInfoResult::setResult(uint8_t result) {
    RespondGetFunctionLockInfoResult::result = result;
}

uint8_t RespondGetFunctionLockInfoResult::getStrLen() const {
    return strLen;
}

void RespondGetFunctionLockInfoResult::setStrLen(uint8_t strLen) {
    RespondGetFunctionLockInfoResult::strLen = strLen;
}

const std::string &RespondGetFunctionLockInfoResult::getStrfunctionlock() const {
    return strfunctionlock;
}

void RespondGetFunctionLockInfoResult::setStrfunctionlock(const std::string &strfunctionlock) {
    RespondGetFunctionLockInfoResult::strfunctionlock = strfunctionlock;
}
