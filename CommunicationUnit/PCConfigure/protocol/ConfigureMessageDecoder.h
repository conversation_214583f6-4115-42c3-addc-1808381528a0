//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/9.
//

#ifndef VIS_G3_SOFTWARE_CONFIGUREMESSAGEDECODER_H
#define VIS_G3_SOFTWARE_CONFIGUREMESSAGEDECODER_H

#include "UniversalAnswer.h"
#include "PCConfigureDataPacket.h"
#include "UniversalAnswer.h"
#include "Heartbeat.h"
#include "QequestG3Config.h"
#include "SetG3Config.h"
#include "StartUDPRealview.h"
#include "RealTimeVehicleStatus.h"
#include "CommunicationDataCallback.h"
#include "SetG3SystemTime.h"
#include "GetG3AndG4DevicesVersion.h"

namespace vis {

    class ConfigureMessageDecoder {
    public:
        const static uint8_t IDENTIFICATION_HEAD = 0x5A;
        const static uint8_t IDENTIFICATION_TAIL = 0x5B;
        const static int MAX_DATA_DATA_SIZE = 1024 * 1024;
        const static int MSG_MIN_SIZE = 13;


        ConfigureMessageDecoder();

        void onDataReceived(const char *ip, const int port, const uint8_t *buf, int len);

        void resetData();

        void addANewCharToCache(uint8_t nc);

        void doDecode(uint8_t *buf, int len, const char *ip, const int port);

        void setInterface(CommunicationDataCallback &communicationDataCallback);

    private:
        /* 临时放数据的缓存 */
        uint8_t *dataCache;
        /* 临时放数据的缓存当前被读写到了哪里 */
        int point;
        /* 转义后的正式的数据包的内存 */
        uint8_t *formalDataBuf;

        /* 返回解析结果用的callback */
        CommunicationDataCallback *callback;
        // 这是从doDecode函数里的MSGID_RESPOND_REALTIME_VEHICLE_STATUS命令字那里复制过来的
        // 之所以复制到这个地方是因为这个变量不能重复初始化它，否则，当收到一个数据项总数为0或者收到
        // 一个数据项不完整的消息的时候，缺失的数据项会被初始化成0
        RealTimeVehicleStatus mRealTimeVehicleStatus;

        // 上次接收到的命令的流水号
        int lastRecvFlowId = -1;

    };

}
#endif //VIS_G3_SOFTWARE_CONFIGUREMESSAGEDECODER_H
