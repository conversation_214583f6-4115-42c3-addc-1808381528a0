//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/5.
//

#ifndef VIS_G3_SOFTWARE_STARTG3FILEDOWNLOADRESULT_H
#define VIS_G3_SOFTWARE_STARTG3FILEDOWNLOADRESULT_H

#include "PCConfigureDataPacket.h"

class StartG3FileDownloadResult : public PCConfigureDataPacket {
public:
    StartG3FileDownloadResult();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getResult() const;

    void setResult(uint8_t result);

    uint16_t getTcpPort() const;

    void setTcpPort(uint16_t tcpPort);

    uint32_t getFileLen() const;

    void setFileLen(uint32_t fileLen);

    const uint8_t *getFileMd5() const;

    void setFileMD5(const uint8_t *md5);

private:
    uint8_t result = RESULT_FAILED;
    uint16_t tcpPort = -1;
    uint32_t fileLen = 0;
    uint8_t fileMD5[16] = {0x00};

};


#endif //VIS_G3_SOFTWARE_STARTG3FILEDOWNLOADRESULT_H
