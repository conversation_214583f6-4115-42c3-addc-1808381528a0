//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/10.
//

#ifndef VIS_G3_SOFTWARE_UNIVERSALANSWER_H
#define VIS_G3_SOFTWARE_UNIVERSALANSWER_H

#include "PCConfigureDataPacket.h"

class UniversalAnswer : public PCConfigureDataPacket {
public:


    UniversalAnswer();

    ~UniversalAnswer();

    uint32_t getQFlowId() const;

    void setQFlowId(uint32_t qFlowId);

    uint8_t getQMsgId() const;

    void setQMsgId(uint8_t qMsgId);

    uint8_t getResult() const;

    void setResult(uint8_t result);

    int decode(uint8_t *buf, int len) override;

    int toCode(uint8_t *buf, int len) override;

private:
    /* 被应答的消息的流水号 */
    uint32_t qFlowId;
    /* 被应答的消息的命令字 */
    uint8_t qMsgId;
    /* 结果 */
    uint8_t result;


};


#endif //VIS_G3_SOFTWARE_UNIVERSALANSWER_H
