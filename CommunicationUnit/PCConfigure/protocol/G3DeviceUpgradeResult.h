//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/31.
//

#ifndef VIS_G3_SOFTWARE_G3DEVICEUPGRADERESULT_H
#define VIS_G3_SOFTWARE_G3DEVICEUPGRADERESULT_H

#include "PCConfigureDataPacket.h"

class G3DeviceUpgradeResult : public PCConfigureDataPacket {
public:
    G3DeviceUpgradeResult();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getDeviceId() const;

    void setDeviceId(uint8_t deviceId);

    uint8_t getResult() const;

    void setResult(uint8_t result);

private:
    /* 设备ID */
    uint8_t deviceId = 0;
    /* 结果 */
    uint8_t result;
};


#endif //VIS_G3_SOFTWARE_G3DEVICEUPGRADERESULT_H
