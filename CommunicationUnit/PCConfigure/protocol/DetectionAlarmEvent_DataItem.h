//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONALARMEVENT_DATAITEM_H
#define VIS_G3_SOFTWARE_DETECTIONALARMEVENT_DATAITEM_H

#include <cstdint>

class DetectionAlarmEvent_DataItem {
public:
    /* 报警事件数据项ID_公用信息 */
    static const uint16_t DATAID_GENERAL_INFORMATION = 0x0001;
    /* 报警事件数据项ID_ADAS报警信息 */
    static const uint16_t DATAID_ADAS_ALARM_EVENT = 0x0002;
    /* 报警事件数据项ID_BSD报警信息 */
    static const uint16_t DATAID_BSD_ALARM_EVENT = 0x0003;
    /* 报警事件数据项ID_DSM报警信息 */
    static const uint16_t DATAID_DSM_ALARM_EVENT = 0x0004;
    /* 报警事件数据项ID_R151报警信息 */
    static const uint16_t DATAID_R151_ALARM_EVENT = 0x0005;
    /* 报警事件数据项ID_R158报警信息 */
    static const uint16_t DATAID_R158_ALARM_EVENT = 0x0006;
    /* 报警事件数据项ID_R159报警信息 */
    static const uint16_t DATAID_R159_ALARM_EVENT = 0x0007;


    DetectionAlarmEvent_DataItem();

    ~DetectionAlarmEvent_DataItem();

    virtual int toCode(uint8_t *buf, int len);

    uint16_t getDataId() const;

    void setDataId(uint16_t dataId);

    uint32_t getDataLen() const;

    void setDataLen(uint32_t dataLen);

private:
    /* 数据项ID */
    uint16_t dataId = -1;
    /* 数据项的长度 */
    uint32_t dataLen = 0;
};


#endif //VIS_G3_SOFTWARE_DETECTIONALARMEVENT_DATAITEM_H
