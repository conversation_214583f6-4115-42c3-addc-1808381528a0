//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/2.
//

#ifndef VIS_G3_SOFTWARE_G3MP4FILEOPT_H
#define VIS_G3_SOFTWARE_G3MP4FILEOPT_H

#include <vector>
#include "PCConfigureDataPacket.h"

class G3Mp4FileOpt : public PCConfigureDataPacket {
public:
    G3Mp4FileOpt();

    int decode(uint8_t *buf, int len) override;

    uint8_t getOptType() const;

    void setOptType(uint8_t optType);

    uint16_t getFileListSize() const;

    void setFileListSize(uint16_t fileListSize);

    const std::vector<PCConfigureDataPacket::MP4FileInfo> &getFileList() const;

    void setFileList(const std::vector<PCConfigureDataPacket::MP4FileInfo> &fileList);


private:
    uint8_t optType;
    uint16_t fileListSize;
    std::vector<PCConfigureDataPacket::MP4FileInfo> fileList;


};


#endif //VIS_G3_SOFTWARE_G3MP4FILEOPT_H
