//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/11.
//

#ifndef VIS_G3_SOFTWARE_SETPERIPHERALDISPLAYPARAMS_H
#define VIS_G3_SOFTWARE_SETPERIPHERALDISPLAYPARAMS_H

#include "PCConfigureDataPacket.h"
class SetPeripheralDisplayParams : public PCConfigureDataPacket {
public:
    SetPeripheralDisplayParams();

    int decode(uint8_t *buf, int len) override;

    uint16_t getDisplayMode() const;

    void setDisplayMode(uint16_t displayMode);

    uint8_t getIconMode() const;

    void setIconMode(uint8_t iconMode);

    const uint8_t *getReserved() const;

private:
    uint16_t displayMode = 0xFFFF;
    uint8_t iconMode = 0xFF;
    uint8_t reserved[3] = {0x00};

};


#endif //VIS_G3_SOFTWARE_SETPERIPHERALDISPLAYPARAMS_H
