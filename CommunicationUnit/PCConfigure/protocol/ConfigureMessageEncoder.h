//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/9.
//

#ifndef VIS_G3_SOFTWARE_CONFIGUREMESSAGEENCODER_H
#define VIS_G3_SOFTWARE_CONFIGUREMESSAGEENCODER_H

#include "UniversalAnswer.h"
#include "RespondG3Config.h"
#include "StartUDPRealviewResult.h"
#include "DetectionInfo.h"
#include "DetectionAlarmEvent.h"
#include "StartG3DeviceUpgradeResult.h"
#include "G3DeviceUpgradeResult.h"
#include "RespondG3AndG4DevicesVersion.h"
#include "GetG3AndG4DevicesVersion.h"
#include "RespondGetCPUSerialNumResult.h"
#include "RespondSetUUIDResult.h"
#include "RespondGetUUIDResult.h"
#include "RespondGetG3UTCTimeResult.h"
#include "GetG3Mp4FileListResult.h"
#include "G3Mp4FileOptResult.h"
#include "StartG3FileDownloadResult.h"
#include "RespondGetFunctionLockInfoResult.h"
#include "StartUDPRealviewResult_Separate.h"
#include "SetPeripheralDisplayParamsResult.h"
#include "GetMRV220LogFileListResult.h"
#include "StartMRV220LogFileDownloadResult.h"
#include "RespondMultimediaFileEncryptionKey.h"
#include "RespondAlarmSoundFileConf.h"
#include "RespondGetCameraInputTypeFilterConfigResult.h"
#include "RespondGetCameraReduceEffectInfoResult.h"
#include "TCPRealviewOptResult.h"
class ConfigureMessageEncoder {
public:
    /**
     * 生
     * */
    int generateUniversalAnswerCode(uint8_t *buf, int len, PCConfigureDataPacket &pcConfigureDataPacket, int result);

    int generateRespondG3ConfigCode(uint8_t *buf, int len, char *configStr, int strLen);

    int generateStartUDPRealviewResultCode(uint8_t *buf, int len, uint8_t cameraId, uint8_t result, uint16_t port);

    int generateDetectionInfo(uint8_t *buf, int len, DetectionInfo &detectionInfo);

    int generateDetectionAlarmEvent(uint8_t *buf, int len, DetectionAlarmEvent &detectionAlarmEvent);

    int
    generateStartG3DeviceUpgradeResult(uint8_t *buf, int len, StartG3DeviceUpgradeResult &startG3DeviceUpgradeResult);

    int generateG3DeviceUpgradeResult(uint8_t *buf, int len, G3DeviceUpgradeResult &upgradeResult);

    int generateG3AndG4DeviceVersion(uint8_t *buf, int len, RespondG3AndG4DevicesVersion &g3AndG4DevicesVersion);

    int generateGetCPUSerialNumberResult(uint8_t *buf, int len, RespondGetCPUSerialNumResult &getCpuSerialNumResult);

    int generateSetG3UUIDResult(uint8_t *buf, int len, RespondSetUUIDResult &setUuidResult);

    int generateGetG3UUIDResult(uint8_t *buf, int len, RespondGetUUIDResult &getUuidResult);

    int generateGetG3UTCTime(uint8_t *buf, int len, RespondGetG3UTCTimeResult &getG3UtcTimeResult);

    int generateGetG3Mp4FileListResult(uint8_t *buf, int len, GetG3Mp4FileListResult &getG3Mp4FileListResult);

    int generateG3Mp4FileOptResult(uint8_t *buf, int len, G3Mp4FileOptResult &g3Mp4FileOptResult);

    int generateStartG3FileDownloadResult(uint8_t *buf, int len, StartG3FileDownloadResult &g3FileDownloadResult);

    int generateGetFunctionLockInfoResult(uint8_t *buf, int len,
                                          RespondGetFunctionLockInfoResult &getFunctionLockInfoResult);

    int generateStartUDPRealviewResultSeparate(uint8_t *buf, int len, uint8_t cameraId, uint8_t result, uint16_t port);

    int generateSetPeripheralDisplayParamsResult(uint8_t *buf, int len, SetPeripheralDisplayParamsResult &setPeripheralDisplayParamsResult);

    int generateGetMRV220LogFileListResult(uint8_t *buf, int len, GetMRV220LogFileListResult &getMrv220LogFileListResult);

    int generateStartMRV220LogFileDownloadResult(uint8_t *buf, int len, StartMRV220LogFileDownloadResult &startMrv220LogFileDownloadResult);

    int generateRespondMultimediaFileEncryptionKey(uint8_t *buf, int len, RespondMultimediaFileEncryptionKey &respondMultimediaFileEncryptionKey);

    int generateRespondAlarmSoundFileConf(uint8_t *buf, int len, RespondAlarmSoundFileConf &respondAlarmSoundFileConf);

    int generateRespondGetCameraInputTypeFilterConfigResult(uint8_t *buf, int len, RespondGetCameraInputTypeFilterConfigResult &respondGetCameraInputTypeFilterConfigResult);

    int generateRespondGetCameraReduceEffectInfoResult(uint8_t *buf, int len, RespondGetCameraReduceEffectInfoResult &getCameraReduceEffectInfoResult);

    int generateTCPRealviewOptResult(uint8_t *buf, int len,TCPRealviewOptResult result);


    uint32_t getFlowId();

private:
    /* 流水号 */
    uint32_t flowId = -1;

};


#endif //VIS_G3_SOFTWARE_CONFIGUREMESSAGEENCODER_H
