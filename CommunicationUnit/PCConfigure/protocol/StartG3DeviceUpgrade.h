//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/31.
//

#ifndef VIS_G3_SOFTWARE_STARTG3DEVICEUPGRADE_H
#define VIS_G3_SOFTWARE_STARTG3DEVICEUPGRADE_H

#include "PCConfigureDataPacket.h"

class StartG3DeviceUpgrade : public PCConfigureDataPacket {
public:
    StartG3DeviceUpgrade();

    int decode(uint8_t *buf, int len) override;

    int toCode(uint8_t *buf, int len) override;


    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

    uint8_t getFileType() const;

    void setFileType(uint8_t fileType);

    uint32_t getFileLen() const;

    void setFileLen(uint32_t fileLen);

    const uint8_t *getFileMd5() const;

    void setFileMd5(uint8_t *md5, const int len);

    uint32_t getFileVersion() const;

    void setFileVersion(uint32_t fileVersion);

private:
    /* G3ID */
    uint8_t g3Id = 0;
    /* 文件类型 0：G3设备软件    1：G3的MCU软件    2：G4的MCU软件    3：GPS盒子的软件   4：L3的软件   5：提示器的软件  */
    uint8_t fileType;
    /* 文件的长度 */
    uint32_t fileLen;
    /* 文件的MD5 */
    uint8_t fileMD5[16] = {0x00};
    /* 文件的版本号 */
    uint32_t fileVersion = 0;
};


#endif //VIS_G3_SOFTWARE_STARTG3DEVICEUPGRADE_H
