//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/14.
//

#include "RealTimeVehicleStatus.h"

RealTimeVehicleStatus::RealTimeVehicleStatus() {
    setMsgId(MSGID_RESPOND_REALTIME_VEHICLE_STATUS);
}

RealTimeVehicleStatus::~RealTimeVehicleStatus() {

}

int RealTimeVehicleStatus::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        // 数据总数
        int count = buf[index] & 0xff;
        index++;
        // 开始循环解析参数
        if (count > 0) {
            for (int i = 0; i < count; i++) {
                // 获取参数ID
                uint16_t paramId = CodeUtils::getInstance().BbToUint16(buf + index);
                index += 2;
                // 获取参数长度
                uint8_t paramLen = buf[index];
                index++;
                // 解析参数
                parseParam(paramId, buf + index);
                index += paramLen;
            }
            ret = 0;
        }
    }
    return ret;
}

int RealTimeVehicleStatus::parseParam(uint16_t paramId, uint8_t *data) {
    // 根据ID赋值给不同的变量
    switch (paramId) {
        case ID_BATTERTVOLTAGE: {
            // 电瓶电压
            if (CodeUtils::getInstance().BbToUint16(data) != 0xFFFF) {
                batteryVoltage = (float) CodeUtils::getInstance().BbToUint16(data) / (float) 100;
            }

        }
            break;
        case ID_ENGINESPEED: {
            // 发动机转速
            if (CodeUtils::getInstance().BbToUint16(data) != 0xFFFF) {
                engineSpeed = CodeUtils::getInstance().BbToUint16(data);
            }

        }
            break;
        case ID_SPEED: {
            // 车速
            if(CodeUtils::getInstance().BbToUint16(data) != 0xFFFF){
                speed = (float) ( CodeUtils::getInstance().BbToUint16(data) / (float) 10);
            }else{
                speed = G3_Configuration::getInstance().getDefaultSpeed();
            }

        }
            break;
        case ID_COOLANTTEMPERATURE: {
            // 冷却液温度
            if (data[0] != 0xFF) {
                coolantTemperature = data[0] & 0xff;
            }

        }
            break;
        case ID_INSTANTANEOUSFUELCONSUMPTION: {
            // 瞬时油耗
            if (CodeUtils::getInstance().BbToUint16(data) != 0xFFFF) {
                instantaneousFuelConsumption = (float) CodeUtils::getInstance().BbToUint16(data) / (float) 100;
            }

        }
            break;
        case ID_RESIDUALOIL: {
            // 剩余油量
            if (data[0] != 0xFF) {
                residualOil = data[0] & 0xff;
            }

        }
            break;
        case ID_CARSTATUS: {
            // 车辆状态
            uint16_t carStatusInt = CodeUtils::getInstance().BbToUint16(data);
            memcpy(&carStatus, &carStatusInt, 2);
        }
            break;

        case ID_CURRENTMILEAGE: {
            // 当前行驶里程
            if (CodeUtils::getInstance().BbToUint16(data) != 0xFFFF) {
                currentMileage = (float) CodeUtils::getInstance().BbToUint16(data) / (float) 10;
            }

        }
            break;
        case ID_TRAVELTIME: {
            // 本次行驶时间
            if (CodeUtils::getInstance().BbToUint16(data) != 0xFFFF) {
                travelTime = CodeUtils::getInstance().BbToUint16(data);
            }

        }
            break;
        case ID_IDLINGTIME: {
            // 本次怠速时间
            if (CodeUtils::getInstance().BbToUint16(data) != 0xFFFF) {
                idlingTime = CodeUtils::getInstance().BbToUint16(data);
            }
        }
            break;
        case ID_ACCUMULATIVEOILCONSUMPTION: {
            // 累计耗油量
            if (CodeUtils::getInstance().BbToUint32(data) != 0xFFFFFFFF) {
                AccumulativeOilConsumption = CodeUtils::getInstance().BbToUint32(data);
            }
        }
            break;
        case ID_ODO: {
            // 累计行驶里程
            if (CodeUtils::getInstance().BbToUint32(data) != 0xFFFFFFFF) {
                ODO = (float) CodeUtils::getInstance().BbToUint32(data) / (float) 10;
            }
        }
            break;
        case ID_CUMULATIVEDRIVINGTIME: {
            // 累计行驶时间
            if (CodeUtils::getInstance().BbToUint32(data) != 0xFFFFFFFF) {
                cumulativeDrivingTime = CodeUtils::getInstance().BbToUint32(data);
            }
        }
            break;
        case ID_CUMULATIVEIDLINGTIME: {
            // 累计怠速时间
            if (CodeUtils::getInstance().BbToUint32(data) != 0xFFFFFFFF) {
                cumulativeIdlingTime = CodeUtils::getInstance().BbToUint32(data);
            }
        }
            break;
        case ID_GPS: {
            // GPS 数据
            int index = 0;
            gpsData.gpsSpeed = (float) CodeUtils::getInstance().BbToUint16(data + index) / (float) 10;
            index += 2;
            gpsData.status = data[index];
            index++;
            gpsData.direction = CodeUtils::getInstance().BbToUint16(data + index);
            index += 2;
            gpsData.altitude = CodeUtils::getInstance().BbToUint16(data + index);
            index += 2;
            gpsData.latitude = (double) CodeUtils::getInstance().BbToUint32(data + index) / (double) 1000000;
            index += 4;
            gpsData.longitude = (double) CodeUtils::getInstance().BbToUint32(data + index) / (double) 1000000;
            index += 4;
            memcpy(gpsData.bcdTime, data + index, 6);
        }
            break;
        case ID_MACHINERY_ACTION_BARS: {
            // 工程机械操作杆状态
            if (data[0] != 0xFF) {
                machineryActionBars = data[0] & 0xff;
            }
        }
            break;

        case ID_CAMERA_ALARM_SWITCH: {
            // 相机报警开关状态
            if (data[0] != 0xFF) {
                memcpy(&cameraAlarmSwitchStatus,data,1);
            }
        }
            break;

        case ID_G4MT_CAMERA_SWITCH: {
            // G4-MT中镜头切换脚的状态
            g4MT_camera_switch = data[0];
        }
            break;

        case ID_LIGHT_CONTROL_STATUS: {
            // 灯控状态
            memcpy(&lightControlStatus,data,4);
        }
            break;

        case ID_SEAT_BELT_STATUS: {
            // 安全带状态
            seatbeltStatus = data[0];
        }
            break;
        case ID_SECURITY_SWITCH_STATUS: {
            // Security开关状态
            securitySwitchStatus = data[0];
        }
            break;
        case ID_C3_SWITCH_STATUS: {
            /* C3状态 */
            memcpy(&c3SwitchStatus,data,1);
        }
            break;

    }
    return 0;
}

int RealTimeVehicleStatus::getBatteryVoltage() const {
    return batteryVoltage;
}

uint16_t RealTimeVehicleStatus::getEngineSpeed() const {
    return engineSpeed;
}

float RealTimeVehicleStatus::getSpeed() const {
    return speed;
}

uint8_t RealTimeVehicleStatus::getCoolantTemperature() const {
    return coolantTemperature;
}

float RealTimeVehicleStatus::getInstantaneousFuelConsumption() const {
    return instantaneousFuelConsumption;
}

uint8_t RealTimeVehicleStatus::getResidualOil() const {
    return residualOil;
}


float RealTimeVehicleStatus::getCurrentMileage() const {
    return currentMileage;
}

uint16_t RealTimeVehicleStatus::getTravelTime() const {
    return travelTime;
}

uint16_t RealTimeVehicleStatus::getIdlingTime() const {
    return idlingTime;
}

uint32_t RealTimeVehicleStatus::getAccumulativeOilConsumption() const {
    return AccumulativeOilConsumption;
}

float RealTimeVehicleStatus::getOdo() const {
    return ODO;
}

uint32_t RealTimeVehicleStatus::getCumulativeDrivingTime() const {
    return cumulativeDrivingTime;
}

uint32_t RealTimeVehicleStatus::getCumulativeIdlingTime() const {
    return cumulativeIdlingTime;
}

const GPSInfo &RealTimeVehicleStatus::getGpsData() const {
    return gpsData;
}

const VehicleStatus &RealTimeVehicleStatus::getCarStatus() const {
    return carStatus;
}

uint8_t RealTimeVehicleStatus::getMachineryActionBars() const {
    return machineryActionBars;
}

void RealTimeVehicleStatus::setMachineryActionBars(uint8_t machineryActionBars) {
    RealTimeVehicleStatus::machineryActionBars = machineryActionBars;
}

const Camera_Alarm_Switch_Status &RealTimeVehicleStatus::getCameraAlarmSwitchStatus() const {
    return cameraAlarmSwitchStatus;
}

void RealTimeVehicleStatus::setCameraAlarmSwitchStatus(const Camera_Alarm_Switch_Status &cameraAlarmSwitchStatus) {
    RealTimeVehicleStatus::cameraAlarmSwitchStatus = cameraAlarmSwitchStatus;
}

uint8_t RealTimeVehicleStatus::getG4MtCameraSwitch() const {
    return g4MT_camera_switch;
}

void RealTimeVehicleStatus::setG4MtCameraSwitch(uint8_t g4MtCameraSwitch) {
    g4MT_camera_switch = g4MtCameraSwitch;
}

uint8_t RealTimeVehicleStatus::getSeatbeltStatus() const {
    return seatbeltStatus;
}

void RealTimeVehicleStatus::setSeatbeltStatus(uint8_t seatbeltStatus) {
    RealTimeVehicleStatus::seatbeltStatus = seatbeltStatus;
}

uint8_t RealTimeVehicleStatus::getSecuritySwitchStatus() const {
    return securitySwitchStatus;
}

void RealTimeVehicleStatus::setSecuritySwitchStatus(uint8_t securitySwitchStatus) {
    RealTimeVehicleStatus::securitySwitchStatus = securitySwitchStatus;
}

const C3_SWITCH_STATUS &RealTimeVehicleStatus::getC3SwitchStatus() const {
    return c3SwitchStatus;
}

void RealTimeVehicleStatus::setC3SwitchStatus(const C3_SWITCH_STATUS &c3SwitchStatus) {
    RealTimeVehicleStatus::c3SwitchStatus = c3SwitchStatus;
}
