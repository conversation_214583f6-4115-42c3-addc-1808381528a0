//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/1/9.
//

#ifndef VIS_G3_SOFTWARE_RESTARTG3_H
#define VIS_G3_SOFTWARE_RESTARTG3_H

#include "PCConfigureDataPacket.h"

class RestartG3 : public PCConfigureDataPacket {
public:
    /* 只重启软件 */
    static const uint8_t RESTART_OPT_ONLY_SOFTWARD = 0;
    /* 重启整块MRV220 */
    static const uint8_t RESTART_OPT_REBOOT_MRV220 = 1;

    RestartG3();

    int decode(uint8_t *buf, int len) override;

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

    uint8_t getOpt() const;

    void setOpt(uint8_t opt);

    const uint8_t *getReserved() const;

private:
    /* G3ID */
    uint8_t g3Id = 0;
    /* 操作类型  0：只重启软件 1：重启整块MRV220 */
    uint8_t opt;
    /* 预留字节  */
    uint8_t reserved[4] = {0x00};
};


#endif //VIS_G3_SOFTWARE_RESTARTG3_H
