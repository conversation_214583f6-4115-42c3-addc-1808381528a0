//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/19.
//

#ifndef VIS_G3_SOFTWARE_GETFUNCTIONLOCKINFO_H
#define VIS_G3_SOFTWARE_GETFUNCTIONLOCKINFO_H

#include "PCConfigureDataPacket.h"

class GetFunctionLockInfo : public PCConfigureDataPacket {
public:
    GetFunctionLockInfo();

    int decode(uint8_t *buf, int len) override;

private:

};


#endif //VIS_G3_SOFTWARE_GETFUNCTIONLOCKINFO_H
