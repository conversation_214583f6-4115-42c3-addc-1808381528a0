//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/11.
//

#ifndef VIS_G3_SOFTWARE_SETPERIPHERALDISPLAYPARAMSRESULT_H
#define VIS_G3_SOFTWARE_SETPERIPHERALDISPLAYPARAMSRESULT_H

#include "PCConfigureDataPacket.h"
class SetPeripheralDisplayParamsResult : public PCConfigureDataPacket {
public:
    SetPeripheralDisplayParamsResult();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getResult() const;

    void setResult(uint8_t result);

private:
    uint8_t result = 0xFF;

};


#endif //VIS_G3_SOFTWARE_SETPERIPHERALDISPLAYPARAMSRESULT_H
