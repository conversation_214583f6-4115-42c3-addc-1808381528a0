//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/9.
//

#include "StartUDPRealview_Separate.h"

StartUDPRealview_Separate::StartUDPRealview_Separate() {
    setMsgId(MSGID_UDP_REALVIEW_SEPARATE_OPT);
}

int StartUDPRealview_Separate::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        setG3Id(buf[index]);
        index++;
        setCameraId(buf[index]);
        index++;
        setOpt(buf[index]);
        index++;
        setUdpPort(CodeUtils::getInstance().BbToUint16(buf+index));
        index += 2;
        setAddrStrLen(buf[index]);
        index++;
        std::string addr;
        addr.append(reinterpret_cast<const char *>(buf + index), getAddrStrLen());
        setAddressStr(addr);
        index += getAddrStrLen();
        ret = 0;
    }
    return ret;
}

uint8_t StartUDPRealview_Separate::getG3Id() const {
    return g3Id;
}

void StartUDPRealview_Separate::setG3Id(uint8_t g3Id) {
    StartUDPRealview_Separate::g3Id = g3Id;
}

uint8_t StartUDPRealview_Separate::getCameraId() const {
    return cameraId;
}

void StartUDPRealview_Separate::setCameraId(uint8_t cameraId) {
    StartUDPRealview_Separate::cameraId = cameraId;
}

uint8_t StartUDPRealview_Separate::getOpt() const {
    return opt;
}

void StartUDPRealview_Separate::setOpt(uint8_t opt) {
    StartUDPRealview_Separate::opt = opt;
}

uint16_t StartUDPRealview_Separate::getUdpPort() const {
    return udpPort;
}

void StartUDPRealview_Separate::setUdpPort(uint16_t udpPort) {
    StartUDPRealview_Separate::udpPort = udpPort;
}

uint8_t StartUDPRealview_Separate::getAddrStrLen() const {
    return addrStrLen;
}

void StartUDPRealview_Separate::setAddrStrLen(uint8_t addrStrLen) {
    StartUDPRealview_Separate::addrStrLen = addrStrLen;
}

const std::string &StartUDPRealview_Separate::getAddressStr() const {
    return addressStr;
}

void StartUDPRealview_Separate::setAddressStr(const std::string &addressStr) {
    StartUDPRealview_Separate::addressStr = addressStr;
}
