//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//

#include "DetectionAlarmEvent_BSDEvent.h"

DetectionAlarmEvent_BSDEvent::DetectionAlarmEvent_BSDEvent() {
    setDataId(DATAID_BSD_ALARM_EVENT);
    setDataLen(13);
}

DetectionAlarmEvent_BSDEvent::~DetectionAlarmEvent_BSDEvent() {

}

int DetectionAlarmEvent_BSDEvent::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度 */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index += 4;
    /* 标志状态 */
    buf[index] = getEventStatus();
    index++;
    /* 报警/事件类型 */
    buf[index] = getEventType();
    index++;
    /* 碰撞时间 */
    CodeUtils::getInstance().uint16ToBb(getDistance(), buf + index);
    index += 2;
    /* 报警级别 */
    buf[index] = getEventLevel();
    index++;
    /* 相机ID */
    buf[index] = getCameraId();
    index++;
    /* 再塞目标ID */
    CodeUtils::getInstance().uint32ToBb(getAlarmPedestrianId(), buf + index);
    index += 4;
    /* 是否影子 */
    buf[index] = getIsShadow();
    index++;
    /* 预留 */
    memcpy(buf + index, getReserved(), 2);
    index += 2;
    ret = index;
    return ret;
}

uint8_t DetectionAlarmEvent_BSDEvent::getEventStatus() const {
    return eventStatus;
}

void DetectionAlarmEvent_BSDEvent::setEventStatus(uint8_t eventStatus) {
    DetectionAlarmEvent_BSDEvent::eventStatus = eventStatus;
}

uint8_t DetectionAlarmEvent_BSDEvent::getEventType() const {
    return eventType;
}

void DetectionAlarmEvent_BSDEvent::setEventType(uint8_t eventType) {
    DetectionAlarmEvent_BSDEvent::eventType = eventType;
}

uint16_t DetectionAlarmEvent_BSDEvent::getDistance() const {
    return distance;
}

void DetectionAlarmEvent_BSDEvent::setDistance(uint16_t distance) {
    DetectionAlarmEvent_BSDEvent::distance = distance;
}

uint16_t DetectionAlarmEvent_BSDEvent::getEventLevel() const {
    return EventLevel;
}

void DetectionAlarmEvent_BSDEvent::setEventLevel(uint16_t eventLevel) {
    EventLevel = eventLevel;
}

const uint8_t *DetectionAlarmEvent_BSDEvent::getReserved() const {
    return reserved;
}

uint8_t DetectionAlarmEvent_BSDEvent::getCameraId() const {
    return cameraId;
}

void DetectionAlarmEvent_BSDEvent::setCameraId(uint8_t cameraId) {
    DetectionAlarmEvent_BSDEvent::cameraId = cameraId;
}

uint32_t DetectionAlarmEvent_BSDEvent::getAlarmPedestrianId() const {
    return alarmPedestrianId;
}

void DetectionAlarmEvent_BSDEvent::setAlarmPedestrianId(uint32_t alarmPedestrianId) {
    DetectionAlarmEvent_BSDEvent::alarmPedestrianId = alarmPedestrianId;
}

uint8_t DetectionAlarmEvent_BSDEvent::getIsShadow() const {
    return isShadow;
}

void DetectionAlarmEvent_BSDEvent::setIsShadow(uint8_t isShadow) {
    DetectionAlarmEvent_BSDEvent::isShadow = isShadow;
}
