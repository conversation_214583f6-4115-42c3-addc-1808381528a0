//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/7.
//

#include "StartG3DebugMode.h"

StartG3DebugMode::StartG3DebugMode() {
    setMsgId(MSGID_START_G3_DEBUG_MODEL);
}

int StartG3DebugMode::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index = index + 4;
        optType = buf[index];
        index = index + 1;
        memcpy(pwd, buf + index, sizeof(pwd));
        index = index + sizeof(pwd);
        ret = 0;
    }
    return ret;
}

uint8_t StartG3DebugMode::getOptType() const {
    return optType;
}

void StartG3DebugMode::setOptType(uint8_t optType) {
    StartG3DebugMode::optType = optType;
}

const uint8_t *StartG3DebugMode::getPwd() const {
    return pwd;
}

void StartG3DebugMode::setPwd(uint8_t *buf) {
    memcpy(pwd, buf, sizeof(pwd));
}
