//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/31.
//

#ifndef VIS_G3_SOFTWARE_STARTG3DEVICEUPGRADERESULT_H
#define VIS_G3_SOFTWARE_STARTG3DEVICEUPGRADERESULT_H


#include "PCConfigureDataPacket.h"

class StartG3DeviceUpgradeResult : public PCConfigureDataPacket {
public:
    StartG3DeviceUpgradeResult();

    int toCode(uint8_t *buf, int len) override;


    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

    uint8_t getResult() const;

    void setResult(uint8_t result);

    uint16_t getTcpPort() const;

    void setTcpPort(uint16_t tcpPort);

private:
    /* G3ID */
    uint8_t g3Id = 0;
    /* 结果 */
    uint8_t result;
    /* TCP端口 */
    uint16_t tcpPort;

};


#endif //VIS_G3_SOFTWARE_STARTG3DEVICEUPGRADERESULT_H
