//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/15.
//

#ifndef VIS_G3_SOFTWARE_RESPONDGETUUIDRESULT_H
#define VIS_G3_SOFTWARE_RESPONDGETUUIDRESULT_H

#include <string>
#include "PCConfigureDataPacket.h"

class RespondGetUUIDResult : public PCConfigureDataPacket {
public:
    RespondGetUUIDResult();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getResult() const;

    void setResult(uint8_t result);

    uint32_t getStrLen() const;

    void setStrLen(uint32_t strLen);

    const std::string &getCurUuidStr() const;

    void setCurUUID(const std::string &deviceUUID);

private:
    /* 结果  0：成功  1：失败 */
    uint8_t result;
    /* UUID内容的长度 */
    uint32_t strLen;
    /* UUID内容 */
    std::string curUUIDStr;


};


#endif //VIS_G3_SOFTWARE_RESPONDGETUUIDRESULT_H
