//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/19.
//

#ifndef VIS_G3_SOFTWARE_RESPONDGETFUNCTIONLOCKINFORESULT_H
#define VIS_G3_SOFTWARE_RESPONDGETFUNCTIONLOCKINFORESULT_H

#include "PCConfigureDataPacket.h"

class RespondGetFunctionLockInfoResult : public PCConfigureDataPacket {
public:
    RespondGetFunctionLockInfoResult();

    ~RespondGetFunctionLockInfoResult();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getResult() const;

    void setResult(uint8_t result);

    uint8_t getStrLen() const;

    void setStrLen(uint8_t strLen);

    const std::string &getStrfunctionlock() const;

    void setStrfunctionlock(const std::string &strfunctionlock);

private:
    uint8_t result;
    uint8_t strLen;
    std::string strfunctionlock;

};


#endif //VIS_G3_SOFTWARE_RESPONDGETFUNCTIONLOCKINFORESULT_H
