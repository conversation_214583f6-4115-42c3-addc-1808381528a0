//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/8/9.
//

#ifndef VIS_G3_SOFTWARE_GETCAMERAINPUTTYPEFILTERCONFIG_H
#define VIS_G3_SOFTWARE_GETCAMERAINPUTTYPEFILTERCONFIG_H

#include "PCConfigureDataPacket.h"
class GetCameraInputTypeFilterConfig : public PCConfigureDataPacket {
public:
    GetCameraInputTypeFilterConfig();

    int decode(uint8_t *buf, int len) override;

private:

};


#endif //VIS_G3_SOFTWARE_GETCAMERAINPUTTYPEFILTERCONFIG_H
