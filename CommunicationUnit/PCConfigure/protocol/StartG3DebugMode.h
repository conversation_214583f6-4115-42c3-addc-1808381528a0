//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/7.
//

#ifndef VIS_G3_SOFTWARE_STARTG3DEBUGMODE_H
#define VIS_G3_SOFTWARE_STARTG3DEBUGMODE_H

#include "PCConfigureDataPacket.h"

class StartG3DebugMode : public PCConfigureDataPacket {
public:
    const static uint8_t G3_DEBUG_MODE_OPT_OPEN = 0x00;
    const static uint8_t G3_DEBUG_MODE_OPT_CLOSE = 0x01;

    StartG3DebugMode();

    int decode(uint8_t *buf, int len) override;

    uint8_t getOptType() const;

    void setOptType(uint8_t optType);

    const uint8_t *getPwd() const;

    void setPwd(uint8_t *buf);

private:
    uint8_t optType;
    uint8_t pwd[8] = {0x00};

};


#endif //VIS_G3_SOFTWARE_STARTG3DEBUGMODE_H
