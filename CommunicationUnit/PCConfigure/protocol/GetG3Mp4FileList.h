//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/23.
//

#ifndef VIS_G3_SOFTWARE_GETG3MP4FILELIST_H
#define VIS_G3_SOFTWARE_GETG3MP4FILELIST_H

#include "PCConfigureDataPacket.h"


class GetG3Mp4FileList : public PCConfigureDataPacket {
public:
    GetG3Mp4FileList();

    int decode(uint8_t *buf, int len) override;

    const MP4FileListContent &getListContent() const;

    void setListContent(const MP4FileListContent &listContent);

    const WarFileDetectContent &getDetectContent() const;

    void setDetectContent(const WarFileDetectContent &detectContent);

    uint8_t getFileCameraId() const;

    void setFileCameraId(uint8_t fileCameraId);

    uint8_t getSortOrder() const;

    void setSortOrder(uint8_t sortOrder);

    uint32_t getStartIndex() const;

    void setStartIndex(uint32_t startIndex);

    uint16_t getFileCount() const;

    void setFileCount(uint16_t fileCount);

private:
    /* 文件列表内容类型 */
    MP4FileListContent listContent = {0x00};
    /* 报警目标类型 */
    WarFileDetectContent detectContent = {0x01};
    /* 相机ID */
    uint8_t fileCameraId;
    /* 排序方式 0：从旧到新  1：从新到旧 */
    uint8_t sortOrder;
    /* 起始序号 在前面符合条件的总表里的哪一条开始取文件信息 */
    uint32_t startIndex;
    /* 文件条数 从起始序号开始往后取多少条文件信息 */
    uint16_t fileCount;


};


#endif //VIS_G3_SOFTWARE_GETG3MP4FILELIST_H
