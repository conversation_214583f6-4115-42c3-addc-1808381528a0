//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/7.
//

#ifndef VIS_G3_SOFTWARE_GETMULTIMEDIAFILEENCRYPTION_H
#define VIS_G3_SOFTWARE_GETMULTIMEDIAFILEENCRYPTION_H

#include "PCConfigureDataPacket.h"

class GetMultimediaFileEncryptionKey : public PCConfigureDataPacket {
public:
    GetMultimediaFileEncryptionKey();

    int decode(uint8_t *buf, int len) override;


private:


};


#endif //VIS_G3_SOFTWARE_GETMULTIMEDIAFILEENCRYPTION_H
