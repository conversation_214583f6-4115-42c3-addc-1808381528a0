//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/21.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONINFO_MASKBUF_H
#define VIS_G3_SOFTWARE_DETECTIONINFO_MASKBUF_H

#include "DetectionInfo_DataItem.h"
class DetectionInfo_MaskBuf : public DetectionInfo_DataItem {
public:
    DetectionInfo_MaskBuf();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getCameraId() const;

    void setCameraId(uint8_t cameraId);

    uint16_t getMaskWidth() const;

    void setMaskWidth(uint16_t maskWidth);

    uint16_t getMaskHeight() const;

    void setMaskHeight(uint16_t maskHeight);

    uint32_t getMaskBufLen() const;

    void setMaskBufLen(uint32_t maskBufLen);

    const uint8_t *getMaskBuf() const;

    void setMaskBuf(const uint8_t *buf,const uint32_t len);


private:
    /* 相机ID */
    uint8_t cameraId;
    /* maskBuf对应的宽 */
    uint16_t maskWidth;
    /* maskBuf对应的高 */
    uint16_t maskHeight;
    /* maskBuf的长度 */
    uint32_t maskBufLen = 0;
    /* maskBuf的内容 */
    uint8_t maskBuf[57600] = {0x00};

};


#endif //VIS_G3_SOFTWARE_DETECTIONINFO_MASKBUF_H
