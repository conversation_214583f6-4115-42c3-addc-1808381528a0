//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/2.
//

#ifndef VIS_G3_SOFTWARE_G3MP4FILEOPTRESULT_H
#define VIS_G3_SOFTWARE_G3MP4FILEOPTRESULT_H

#include <vector>
#include "PCConfigureDataPacket.h"

class G3Mp4FileOptResult : public PCConfigureDataPacket {
public:
    G3Mp4FileOptResult();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getResult() const;

    void setResult(uint8_t result);

    uint16_t getFailedFileListSize() const;

    void setFailedFileListSize(uint16_t failedFileListSize);

    const std::vector<PCConfigureDataPacket::MP4FileInfo> &getFailedFileList() const;

    void setFailedFileList(const std::vector<PCConfigureDataPacket::MP4FileInfo> &failedFileList);

private:
    uint8_t result;
    uint16_t failedFileListSize = 0;
    std::vector<PCConfigureDataPacket::MP4FileInfo> failedFileList;
};


#endif //VIS_G3_SOFTWARE_G3MP4FILEOPTRESULT_H
