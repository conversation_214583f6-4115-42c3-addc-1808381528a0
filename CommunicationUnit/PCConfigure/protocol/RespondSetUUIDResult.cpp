//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/8.
//

#include "RespondSetUUIDResult.h"

RespondSetUUIDResult::RespondSetUUIDResult() {
    setMsgId(MSGID_RESPOND_SET_G3_AND_G4_UUID_RESULT);
}

int RespondSetUUIDResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 先随便塞个长度 */
    int realContent = 0;
    setContentLen(0);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 塞结果 */
    buf[index] = getResult();
    index++;
    realContent++;
    /* 塞序列号内容的长度 */
    CodeUtils::getInstance().int32ToBb(strLen, buf + index);
    index += 4;
    realContent += 4;
    /* 塞序列号内容 */
    memcpy(buf + index, curCPUSerialNumStr.c_str(), strLen);
    index += strLen;
    realContent += strLen;

    /* 塞进去真正得长度 */
    setContentLen(realContent);
    CodeUtils::getInstance().int32ToBb(realContent, buf + 6);
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

uint8_t RespondSetUUIDResult::getResult() const {
    return result;
}

void RespondSetUUIDResult::setResult(uint8_t result) {
    RespondSetUUIDResult::result = result;
}

void RespondSetUUIDResult::setCPUSerialNumber(std::string cpuSerialNum) {
    curCPUSerialNumStr.clear();
    curCPUSerialNumStr.append(cpuSerialNum);
    strLen = curCPUSerialNumStr.size();
}
