//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/13.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONINFO_H
#define VIS_G3_SOFTWARE_DETECTIONINFO_H

#include "PCConfigureDataPacket.h"
#include "DetectionInfo_ObjectInfo.h"
#include "DetectionInfo_SystemInfo.h"
#include "DetectionInfo_SystemError.h"
#include "DetectionInfo_DSM.h"
#include "DetectionInfo_Gesture.h"
#include "DetectionInfo_ADAS.h"
#include "DetectionInfo_MaskBuf.h"
#include "DetectionInfo_Control.h"
#include "DetectionInfo_R151Info.h"
#include "DetectionInfo_R158Info.h"
#include "DetectionInfo_R159Info.h"
#include "DetectionInfo_SeatBelt.h"
#include "DetectionInfo_AlgDetectTime.h"
#include "DetectionInfo_BarCodeInfos.h"
#include "DetectionInfo_SecurityModeInfo.h"
#include "DetectionInfo_GSensorData.h"
#include "DetectionInfo_GPIOIOStatus.h"
#include "DetectionInfo_SignalAndStatus.h"
class DetectionInfo : public PCConfigureDataPacket {
public:
    DetectionInfo();

    ~DetectionInfo();

    int decode(uint8_t *buf, int len) override;

    int toCode(uint8_t *buf, int len) override;

    void addDetectionInfoItem_ObjectInfo(DetectionInfo_ObjectInfo &dataItem);

    void addDetectionInfoItem_SystemInfo(DetectionInfo_SystemInfo &systemInfo);

    void addDetectionInfoItem_SystemError(DetectionInfo_SystemError &systemError);

    void addDetectionInfoItem_DSMInfo(DetectionInfo_DSM &dsmInfo);

    void addDetectionInfoItem_GestureInfo(DetectionInfo_Gesture &gestureInfo);

    void addDetectionInfoItem_ADASInfo(DetectionInfo_ADAS &adasInfo);

    void addDetectionInfoItem_MaskBuf(DetectionInfo_MaskBuf &maskBufInfo);

    void addDetectionInfoItem_LampContro(DetectionInfo_Control &lampContro);

    void addDetectionInfoItem_R151Info(DetectionInfo_R151Info &r151Info);

    void addDetectionInfoItem_R158Info(DetectionInfo_R158Info &r159Info);

    void addDetectionInfoItem_R159Info(DetectionInfo_R159Info &r159Info);

    void addDetectionInfoItem_SeatbeltInfo(DetectionInfo_SeatBelt &seatbeltInfo);

    void addDetectionInfoItem_AlgDetectTime(DetectionInfo_AlgDetectTime &algDetectTime);

    void addDetectionInfoItem_BarCodeInfos(DetectionInfo_BarCodeInfos &barCodeInfos);

    void addDetectionInfoItem_SecurityModeInfo(DetectionInfo_SecurityModeInfo &securityModeInfos);

    void addDetectionInfo_GSensorData(DetectionInfo_GSensorData &gSensorData);

    void addDetectionInfo_GPIOIOStatus(DetectionInfo_GPIOIOStatus &gpioioStatus);

    void addDetectionInfo_SignalAndStatus(DetectionInfo_SignalAndStatus &signalAndStatus);

    uint8_t getInfoListSize() const;

    void setInfoListSize(uint8_t infoListSize);

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

    const std::vector<DetectionInfo_MaskBuf> &getMaskBufInfoList() const;


private:
    /* G3Id */
    uint8_t g3Id = 0;
    /* 识别信息项总数 */
    uint8_t infoListSize = 0;
    /* 数据项的数组 */
    std::vector<DetectionInfo_ObjectInfo> objectInfoList;
    std::vector<DetectionInfo_SystemInfo> systemInfoList;
    std::vector<DetectionInfo_SystemError> systemErrorList;
    std::vector<DetectionInfo_DSM> dsmInfoList;
    std::vector<DetectionInfo_Gesture> gestureInfoList;
    std::vector<DetectionInfo_ADAS> adasInfoList;
    std::vector<DetectionInfo_MaskBuf> maskBufInfoList;
    std::vector<DetectionInfo_Control> lampControlList;
    std::vector<DetectionInfo_R151Info> r151InfoList;
    std::vector<DetectionInfo_R158Info> r158InfoList;
    std::vector<DetectionInfo_R159Info> r159InfoList;
    std::vector<DetectionInfo_SeatBelt> seatbeltList;
    std::vector<DetectionInfo_AlgDetectTime> algDetectTimeList;
    std::vector<DetectionInfo_BarCodeInfos> barcodeInfoList;
    std::vector<DetectionInfo_SecurityModeInfo> securityModeInfoList;
    std::vector<DetectionInfo_GSensorData> gsensorDataList;
    std::vector<DetectionInfo_GPIOIOStatus> gpioStatusDataList;
    std::vector<DetectionInfo_SignalAndStatus> signalAndStatusDataList;


};


#endif //VIS_G3_SOFTWARE_DETECTIONINFO_H
