//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/1/9.
//

#include "RestartG3.h"

RestartG3::RestartG3() {
    setMsgId(MSGID_REBOOT_MRV220);

}

int RestartG3::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index = index + 4;
        /* 获取G3 ID */
        setG3Id(buf[index]);
        index = index + 1;
        /* 获取操作类型 */
        setOpt(buf[index]);
        index = index + 1;
        /* 获取预留字节 */
        memcpy(reserved, buf + index, sizeof(reserved));
        index = index + 4;
        ret = 0;
    }
    return ret;
}

uint8_t RestartG3::getG3Id() const {
    return g3Id;
}

void RestartG3::setG3Id(uint8_t g3Id) {
    RestartG3::g3Id = g3Id;
}

uint8_t RestartG3::getOpt() const {
    return opt;
}

void RestartG3::setOpt(uint8_t opt) {
    RestartG3::opt = opt;
}

const uint8_t *RestartG3::getReserved() const {
    return reserved;
}
