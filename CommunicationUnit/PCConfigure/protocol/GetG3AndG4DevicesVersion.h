//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/15.
//

#ifndef VIS_G3_SOFTWARE_GETG3ANDG4DEVICESVERSION_H
#define VIS_G3_SOFTWARE_GETG3ANDG4DEVICESVERSION_H


#include "PCConfigureDataPacket.h"

class GetG3AndG4DevicesVersion : public PCConfigureDataPacket {
public:
    const static int DEVICESID_G3_1 = 0;

    const static int DEVICESID_G3_2 = 1;

    const static int DEVICESID_C3 = 2;

    const static int DEVICESID_MCU = 3;

    const static int DEVICESID_L3 = 4;

    const static int DEVICESID_L4 = 5;

    const static int DEVICESID_GPSBOX = 6;

    GetG3AndG4DevicesVersion();

    int decode(uint8_t *buf, int len) override;

    int toCode(uint8_t *buf, int len) override;

    uint8_t getDevicesId() const;

    void setDevicesId(uint8_t devicesId);

private:
    /* 设备ID */
    uint8_t devicesId = 0;

};


#endif //VIS_G3_SOFTWARE_GETG3ANDG4DEVICESVERSION_H
