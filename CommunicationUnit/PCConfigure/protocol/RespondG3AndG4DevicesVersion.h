//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/15.
//

#ifndef VIS_G3_SOFTWARE_RESPONDG3ANDG4DEVICESVERSION_H
#define VIS_G3_SOFTWARE_RESPONDG3ANDG4DEVICESVERSION_H

#include <string>
#include "PCConfigureDataPacket.h"

class RespondG3AndG4DevicesVersion : public PCConfigureDataPacket {
public:
    RespondG3AndG4DevicesVersion();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getDevicesId() const;

    void setDevicesId(uint8_t devicesId);

    uint32_t getVersionStrLen() const;

    void setVersionStrLen(uint32_t versionStrLen);

    const std::string &getVersionStr() const;

    void setVersionStr(const std::string &versionStr);

private:
    /* 设备ID */
    uint8_t devicesId = 0;
    /* 版本内容的长度 */
    uint32_t versionStrLen = 0;
    /* 版本内容 */
    std::string versionStr = "";

};


#endif //VIS_G3_SOFTWARE_RESPONDG3ANDG4DEVICESVERSION_H
