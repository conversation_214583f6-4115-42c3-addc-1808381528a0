//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/1.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONINFO_ADAS_H
#define VIS_G3_SOFTWARE_DETECTIONINFO_ADAS_H

#include "DetectionInfo_DataItem.h"
#include <vector>

struct DetectionInfo_ADAS_object_ {
    /* 类型 */
    char label[16];
    /* id号 */
    uint32_t mId;
    /* 识别框的上 */
    uint16_t mTop;
    /* 识别框的下 */
    uint16_t mBottom;
    /* 识别框的左 */
    uint16_t mLeft;
    /* 识别框的右 */
    uint16_t mRight;
    /* 框离车道线x轴上的真实距离（物理上），单位：毫米 */
    int32_t mXDistance;
    /* 框离车道线y轴上的真实距离（物理上），单位：毫米 */
    int32_t mYDistance;
    /* 框的真实宽度（物理上），单位：毫米 */
    uint32_t mRealWidth;
    /* 框的真实高度（物理上），单位：毫米 */
    uint32_t mRealHeight;
    /* x坐标的速度（单位：m/s） */
    uint32_t mXVelocity;
    /* y坐标的速度（单位：m/s） */
    uint32_t mYVelocity;
    /* 趋势 */
    uint32_t mTrend;
    /* 单位0.1s */
    uint32_t mHeadway;
    /* 单位0.1s */
    uint32_t mTTC;
    /* 是否前方有碰撞危险的车辆 */
    uint8_t mIsAheadCar;
    /* ÷100之后才是正确值  for classify debug */
    uint32_t mScore;
    /* 是否影子	BYTE	0：不是影子  1：是影子 */
    uint8_t isShadow = 0;
    /* mMaxScore（最大得分）	uint16_t	÷100之后才是正确值 */
    uint16_t mMaxScore = 0;
    /* mMinScore（最小得分）	uint16_t	÷100之后才是正确值 */
    uint16_t mMinScore = 0;
    /* 预留字节 */
    uint8_t reserved[2] = {0x00};
};

struct DetectionInfo_ADAS_Laneline_ {
    /* 类型 */
    char label[16];
    /* id号 */
    uint32_t mId;
    /* bottom x */
    uint16_t mEndpointAX;
    /* bottom y */
    uint16_t mEndpointAY;
    /* up x */
    uint16_t mEndpointBX;
    /* up y */
    uint16_t mEndpointBY;
    /* mm */
    int32_t mXDistance;
    /*  */
    uint32_t mCrossSpeed;
    /* cross in time */
    uint32_t mTTLC;
    /*  */
    uint32_t mAngleToEgo;
    /* 为了避免精度丢失，这里直接传double的内存内容（大端） */
    uint8_t mA0[8] = {0x00};
    /* 为了避免精度丢失，这里直接传double的内存内容（大端） */
    uint8_t mA1[8] = {0x00};
    /* 为了避免精度丢失，这里直接传double的内存内容（大端） */
    uint8_t mA2[8] = {0x00};
    /* 为了避免精度丢失，这里直接传double的内存内容（大端） */
    uint8_t mA3[8] = {0x00};
    /* 是否属于离车辆最近的一条左车道线   */
    uint8_t mIsLeftLane = 0;
    /* 是否属于离车辆最近的一条右车道线   */
    uint8_t mIsRightLane = 0;
};

struct DetectionInfo_ADAS_markers_ {
    /* 类型 */
    char label[16];
    /* id号 */
    uint32_t mId;
    /* 识别框的上 */
    uint16_t mTop;
    /* 识别框的下 */
    uint16_t mBottom;
    /* 识别框的左 */
    uint16_t mLeft;
    /* 识别框的右 */
    uint16_t mRight;
    /* 框的真实宽度（物理上），单位：毫米 */
    uint32_t mRealWidth;
    /* 框的真实高度（物理上），单位：毫米 */
    uint32_t mRealHeight;
    /* x坐标的速度（单位：m/s） */
    uint32_t mXVelocity;
    /* y坐标的速度（单位：m/s） */
    uint32_t mYVelocity;
    /* 单位：km/h , 限速多少， 如限速60，120等， 一般 0-200 */
    uint32_t mSpeedLimit;
    /* red, green, yellow .... */
    char mTrafficLight[16];
    /* 限高， 限重， 解除限速 .... */
    char mTrafficSign[16];
    /* 限高数值， 限重数值 ....  */
    uint32_t mTrafficSignValue;
};

class DetectionInfo_ADAS : public DetectionInfo_DataItem {
public:
    DetectionInfo_ADAS();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getCurCameraId() const;

    void setCurCameraId(uint8_t curCameraId);

    void addObjectList(DetectionInfo_ADAS_object_ adasObjectInfo);

    void addLaneLineList(DetectionInfo_ADAS_Laneline_ adasLanelineInfo);

    void addMarkersList(DetectionInfo_ADAS_markers_ adasMarkersInfo);

    void setMaskBuf(const uint8_t *const buf, const int len);

    uint8_t getObjectListLen() const;

    const std::vector<DetectionInfo_ADAS_object_> &getObjectList() const;

    uint8_t getLaneLineListLen() const;

    const std::vector<DetectionInfo_ADAS_Laneline_> &getLaneLineList() const;

    uint8_t getMarkersListLen() const;

    const std::vector<DetectionInfo_ADAS_markers_> &getMarkersList() const;

    uint32_t getMaskBufLen() const;

    const uint8_t *getMaskBuf() const;

private:
    /* 相机ID */
    uint8_t curCameraId;
    /* 物体数组的长度 */
    uint8_t objectListLen = 0;
    /* 物体数组 */
    std::vector<DetectionInfo_ADAS_object_> objectList;
    /* 车道线数组的长度 */
    uint8_t laneLineListLen = 0;
    /* 车道线数组 */
    std::vector<DetectionInfo_ADAS_Laneline_> laneLineList;
    /* 标志物数组的长度 */
    uint8_t markersListLen = 0;
    /* 标志物数组 */
    std::vector<DetectionInfo_ADAS_markers_> markersList;
    /* maskBuf的长度 */
    uint32_t maskBufLen = 0;
    /* maskBuf的长度 */
    uint8_t maskBuf[57600] = {0x00};
};


#endif //VIS_G3_SOFTWARE_DETECTIONINFO_ADAS_H
