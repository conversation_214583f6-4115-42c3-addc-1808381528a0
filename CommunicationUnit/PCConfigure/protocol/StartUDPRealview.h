//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/10.
//

#ifndef VIS_G3_SOFTWARE_STARTUDPREALVIEW_H
#define VIS_G3_SOFTWARE_STARTUDPREALVIEW_H

#include "PCConfigureDataPacket.h"

class StartUDPRealview : public PCConfigureDataPacket {
public:

    StartUDPRealview();

    int decode(uint8_t *buf, int len) override;

    int toCode(uint8_t *buf, int len) override;

    uint8_t getCameraId() const;

    void setCameraId(uint8_t cameraId);

    uint8_t getOpt() const;

    void setOpt(uint8_t opt);

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);


private:
    /* G3ID */
    uint8_t g3Id = 0;
    /* 要操作实景的相机ID */
    uint8_t cameraId;
    /* 操作类型  0：关闭   1：开启  */
    uint8_t opt;

};


#endif //VIS_G3_SOFTWARE_STARTUDPREALVIEW_H
