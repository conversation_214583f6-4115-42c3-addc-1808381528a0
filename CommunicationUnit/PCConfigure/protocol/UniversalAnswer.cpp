//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/10.
//

#include <cstdio>
#include "UniversalAnswer.h"

uint32_t UniversalAnswer::getQFlowId() const {
    return qFlowId;
}

void UniversalAnswer::setQFlowId(uint32_t qFlowId) {
    UniversalAnswer::qFlowId = qFlowId;
}

uint8_t UniversalAnswer::getQMsgId() const {
    return qMsgId;
}

void UniversalAnswer::setQMsgId(uint8_t qMsgId) {
    UniversalAnswer::qMsgId = qMsgId;
}

uint8_t UniversalAnswer::getResult() const {
    return result;
}

void UniversalAnswer::setResult(uint8_t result) {
    UniversalAnswer::result = result;
}

int UniversalAnswer::decode(uint8_t *buf, int len) {
    int ret = -1;
    if (PCConfigureDataPacket::decode(buf, len) == 0) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        setContentLen(CodeUtils::getInstance().BbToint32(buf + 6));
        setQFlowId(CodeUtils::getInstance().BbToint32(buf + 10));
        setQMsgId(buf[14]);
        setResult(buf[15]);
        ret = 0;
    }
    return ret;
}

int UniversalAnswer::toCode(uint8_t *buf, int len) {
    int ret = -1;
    if (len >= (9 + 10)) {
        /* 塞内容长度 */
        int index = 6;
        setContentLen(6);
        CodeUtils::getInstance().int32ToBb(6, buf + index);
        index += 4;
        /* 再塞应答的消息的流水号 */
        CodeUtils::getInstance().int32ToBb(getQFlowId(), buf + index);
        index += 4;
        /* 再塞应答的消息的命令字 */
        buf[index] = getQMsgId();
        index++;
        /* 再塞结果 */
        buf[index] = getResult();
        index++;
        /* 加上头尾那些东西 */
        ret = PCConfigureDataPacket::toCode(buf, len);
    }
    return ret;
}

UniversalAnswer::UniversalAnswer() {
    setMsgId(MSGID_UNIVERSALANSWER);

}

UniversalAnswer::~UniversalAnswer() {

}
