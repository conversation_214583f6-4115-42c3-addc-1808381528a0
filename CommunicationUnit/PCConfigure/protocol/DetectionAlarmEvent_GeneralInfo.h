//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONALARMEVENT_GENERALINFO_H
#define VIS_G3_SOFTWARE_DETECTIONALARMEVENT_GENERALINFO_H

#include "DetectionAlarmEvent_DataItem.h"
#include <CodeUtils.h>
#include <string>

class DetectionAlarmEvent_GeneralInfo : public DetectionAlarmEvent_DataItem {
public:
    DetectionAlarmEvent_GeneralInfo();

    ~DetectionAlarmEvent_GeneralInfo();

    int toCode(uint8_t *buf, int len) override;

    uint32_t getEventId() const;

    void setEventId(uint32_t eventId);

    const uint8_t *getReserved() const;

    uint16_t getSpeed() const;

    void setSpeed(uint16_t speed);

    int16_t getAltitude() const;

    void setAltitude(int16_t altitude);

    int32_t getLatitude() const;

    void setLatitude(int32_t latitude);

    int32_t getLongitude() const;

    void setLongitude(int32_t longitude);

    const uint8_t *getTimeBcd() const;

    void setTimeBcd(uint8_t *timebcd);


private:
    /* 报警ID	按照报警先后，从0开始循环累加，不区分报警类型。 */
    uint32_t eventId;
    /* 预留字节 */
    uint8_t reserved[3] = {0x00};
    /* 车速	单位0.1Km/h。范围0~2500 */
    uint16_t speed;
    /* 高程	海拔高度，单位为米（m） */
    int16_t altitude;
    /* 纬度	以度为单位的纬度值乘以10 的6次方，精确到百万分之一度（北为正，南为负） */
    int32_t latitude;
    /* 经度	以度为单位的经度值乘以10 的6次方，精确到百万分之一度(东为正，西为负) */
    int32_t longitude;
    /* 时间	BCD[6]	YY-MM-DD-hh-mm-ss （GMT+0时间） */
    uint8_t timeBCD[6];

};


#endif //VIS_G3_SOFTWARE_DETECTIONALARMEVENT_GENERALINFO_H
