//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/10/27.
//

#include <cstdlib>
#include <cstring>
#include "DetectionInfo_DSM.h"
#include "CodeUtils.h"

DetectionInfo_DSM::DetectionInfo_DSM() {
    setDataId(DATAID_DSM);
}

DetectionInfo_DSM::~DetectionInfo_DSM() {
}

int DetectionInfo_DSM::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度(随便塞一个) */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index += 4;
    int contentlen = 0;
    /* 再塞相机ID */
    buf[index] = curCameraId;
    index = index + 1;
    /* 再塞是否有人脸 */
    buf[index] = hasFace;
    index = index + 1;
    /* 再塞人脸是否是相同驾驶员 */
    buf[index] = isSameDriver;
    index = index + 1;
    /* 再塞人脸是否戴口罩 */
    buf[index] = isRespirator;
    index = index + 1;
    /* 再塞人脸得分 */
    CodeUtils::getInstance().uint16ToBb(face_score, buf + index);
    index = index + 2;
    /* 再塞人脸框-上 */
    CodeUtils::getInstance().uint16ToBb(face_top, buf + index);
    index = index + 2;
    /* 再塞人脸框-下 */
    CodeUtils::getInstance().uint16ToBb(face_bottom, buf + index);
    index = index + 2;
    /* 再塞人脸框-左 */
    CodeUtils::getInstance().uint16ToBb(face_left, buf + index);
    index = index + 2;
    /* 再塞人脸框-右 */
    CodeUtils::getInstance().uint16ToBb(face_right, buf + index);
    index = index + 2;
    /* 再塞人脸特征点数组长度 */
    buf[index] = faceCharacteristicPointLen;
    index = index + 1;
    /* 再塞人脸特征点 */
    if (faceCharacteristicPointLen > 0) {
        (void) memcpy(buf + index, faceCharacteristicPoint, faceCharacteristicPointLen);
        index = index + faceCharacteristicPointLen;
    }
    /* 再塞人脸角度 */
    CodeUtils::getInstance().int32ToBb(faceangle, buf + index);
    index = index + 4;
    /* 把真实的长度塞进去 */
    contentlen = index - 6;
    CodeUtils::getInstance().uint32ToBb(contentlen, buf + 2);
    ret = index;
    return ret;
}

uint8_t DetectionInfo_DSM::getCurCameraId() const {
    return curCameraId;
}

void DetectionInfo_DSM::setCurCameraId(uint8_t curCameraId) {
    DetectionInfo_DSM::curCameraId = curCameraId;
}

uint8_t DetectionInfo_DSM::getIsSameDriver() const {
    return isSameDriver;
}

void DetectionInfo_DSM::setIsSameDriver(uint8_t isSameDriver) {
    DetectionInfo_DSM::isSameDriver = isSameDriver;
}

uint16_t DetectionInfo_DSM::getFaceScore() const {
    return face_score;
}

void DetectionInfo_DSM::setFaceScore(uint16_t faceScore) {
    face_score = faceScore;
}

uint16_t DetectionInfo_DSM::getFaceTop() const {
    return face_top;
}

void DetectionInfo_DSM::setFaceTop(uint16_t faceTop) {
    face_top = faceTop;
}

uint16_t DetectionInfo_DSM::getFaceBottom() const {
    return face_bottom;
}

void DetectionInfo_DSM::setFaceBottom(uint16_t faceBottom) {
    face_bottom = faceBottom;
}

uint16_t DetectionInfo_DSM::getFaceLeft() const {
    return face_left;
}

void DetectionInfo_DSM::setFaceLeft(uint16_t faceLeft) {
    face_left = faceLeft;
}

uint16_t DetectionInfo_DSM::getFaceRight() const {
    return face_right;
}

void DetectionInfo_DSM::setFaceRight(uint16_t faceRight) {
    face_right = faceRight;
}

uint8_t DetectionInfo_DSM::getFaceCharacteristicPointLen() const {
    return faceCharacteristicPointLen;
}


void DetectionInfo_DSM::setFaceCharacteristicPoint(const uint8_t *const buf, const int len) {
    if (len == 20) {
        faceCharacteristicPointLen = len;
        (void) memcpy(faceCharacteristicPoint, buf, len);
    }
}

void DetectionInfo_DSM::setFaceCharacteristicPointLen(uint8_t faceCharacteristicPointLen) {
    DetectionInfo_DSM::faceCharacteristicPointLen = faceCharacteristicPointLen;
}

uint8_t DetectionInfo_DSM::getHasFace() const {
    return hasFace;
}

void DetectionInfo_DSM::setHasFace(uint8_t hasFace) {
    DetectionInfo_DSM::hasFace = hasFace;
}

uint8_t DetectionInfo_DSM::getIsRespirator() const {
    return isRespirator;
}

void DetectionInfo_DSM::setIsRespirator(uint8_t isRespirator) {
    DetectionInfo_DSM::isRespirator = isRespirator;
}

int32_t DetectionInfo_DSM::getFaceangle() const {
    return faceangle;
}

void DetectionInfo_DSM::setFaceangle(int32_t faceangle) {
    DetectionInfo_DSM::faceangle = faceangle;
}


