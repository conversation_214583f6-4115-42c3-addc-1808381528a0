//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//

#include "DetectionAlarmEvent_ADASEvent.h"

DetectionAlarmEvent_ADASEvent::DetectionAlarmEvent_ADASEvent() {
    setDataId(DATAID_ADAS_ALARM_EVENT);
    setDataLen(15);
}

DetectionAlarmEvent_ADASEvent::~DetectionAlarmEvent_ADASEvent() {

}

int DetectionAlarmEvent_ADASEvent::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index = index + 2;
    /* 再塞总长度 */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index = index + 4;
    /* 标志状态 */
    buf[index] = getEventStatus();
    index = index + 1;
    /* 报警/事件类型 */
    buf[index] = getEventType();
    index = index + 1;
    /* 碰撞时间 */
    buf[index] = getHeadway();
    index = index + 1;
    /* 偏离类型 */
    buf[index] = getLdwType();
    index = index + 1;
    /* 道路标志识别类型 */
    buf[index] = getRoadSignsType();
    index = index + 1;
    /* 相机ID */
    buf[index] = getCameraId();
    index = index + 1;
    /* ADAS报警分级信息 */
    uint32_t tempint;
    memcpy(&tempint, &alarmlLevelInfo, 4);
    CodeUtils::getInstance().uint32ToBb(tempint, buf + index);
    index = index + 4;
    /* ADAS故障信息 */
    memcpy(&tempint, &faultInfo, 4);
    CodeUtils::getInstance().uint32ToBb(tempint, buf + index);
    index = index + 4;
    /* 是否影子 */
    buf[index] = getIsShadow();
    index = index + 1;

    ret = index;
    return ret;
}

uint8_t DetectionAlarmEvent_ADASEvent::getEventStatus() const {
    return eventStatus;
}

void DetectionAlarmEvent_ADASEvent::setEventStatus(uint8_t eventStatus) {
    DetectionAlarmEvent_ADASEvent::eventStatus = eventStatus;
}

uint8_t DetectionAlarmEvent_ADASEvent::getEventType() const {
    return eventType;
}

void DetectionAlarmEvent_ADASEvent::setEventType(uint8_t eventType) {
    DetectionAlarmEvent_ADASEvent::eventType = eventType;
}

uint8_t DetectionAlarmEvent_ADASEvent::getHeadway() const {
    return headway;
}

void DetectionAlarmEvent_ADASEvent::setHeadway(uint8_t headway) {
    DetectionAlarmEvent_ADASEvent::headway = headway;
}

uint8_t DetectionAlarmEvent_ADASEvent::getLdwType() const {
    return ldwType;
}

void DetectionAlarmEvent_ADASEvent::setLdwType(uint8_t ldwType) {
    DetectionAlarmEvent_ADASEvent::ldwType = ldwType;
}

uint8_t DetectionAlarmEvent_ADASEvent::getRoadSignsType() const {
    return roadSignsType;
}

void DetectionAlarmEvent_ADASEvent::setRoadSignsType(uint8_t roadSignsType) {
    DetectionAlarmEvent_ADASEvent::roadSignsType = roadSignsType;
}


uint8_t DetectionAlarmEvent_ADASEvent::getCameraId() const {
    return cameraId;
}

void DetectionAlarmEvent_ADASEvent::setCameraId(uint8_t cameraId) {
    DetectionAlarmEvent_ADASEvent::cameraId = cameraId;
}

const DetectionAlarmEvent_ADASEvent_alarmlLevelInfo &DetectionAlarmEvent_ADASEvent::getAlarmlLevelInfo() const {
    return alarmlLevelInfo;
}

void DetectionAlarmEvent_ADASEvent::setAlarmlLevelInfo(
        const DetectionAlarmEvent_ADASEvent_alarmlLevelInfo &alarmlLevelInfo) {
    DetectionAlarmEvent_ADASEvent::alarmlLevelInfo = alarmlLevelInfo;
}

const DetectionAlarmEvent_ADASEvent_faultInfo &DetectionAlarmEvent_ADASEvent::getFaultInfo() const {
    return faultInfo;
}

void DetectionAlarmEvent_ADASEvent::setFaultInfo(const DetectionAlarmEvent_ADASEvent_faultInfo &faultInfo) {
    DetectionAlarmEvent_ADASEvent::faultInfo = faultInfo;
}

uint8_t DetectionAlarmEvent_ADASEvent::getIsShadow() const {
    return isShadow;
}

void DetectionAlarmEvent_ADASEvent::setIsShadow(uint8_t isShadow) {
    DetectionAlarmEvent_ADASEvent::isShadow = isShadow;
}
