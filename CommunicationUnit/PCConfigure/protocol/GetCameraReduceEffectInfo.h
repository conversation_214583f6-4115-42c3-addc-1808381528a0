//
// Created by Administrator on 2024/12/27.
//

#ifndef VIS_G3_SOFTWARE_GETCAMERAREDUCEEFFECTINFO_H
#define VIS_G3_SOFTWARE_GETCAMERAREDUCEEFFECTINFO_H

#include "PCConfigureDataPacket.h"
class GetCameraReduceEffectInfo : public PCConfigureDataPacket {
public:
    GetCameraReduceEffectInfo();

    int decode(uint8_t *buf, int len) override;

private:
};


#endif //VIS_G3_SOFTWARE_GETCAMERAREDUCEEFFECTINFO_H
