//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/23.
//

#include "GetG3Mp4FileListResult.h"

GetG3Mp4FileListResult::GetG3Mp4FileListResult() {
    setMsgId(MSGID_RESPOND_GET_G3_MP4_FILE_LIST_RESULT);
}

int GetG3Mp4FileListResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 先随便写一个长度 */
    CodeUtils::getInstance().int32ToBb(0, buf + index);
    index = index + 4;
    int realLen = 0;
    /* 结果 */
    buf[index] = result;
    index++;
    realLen++;
    /* 文件信息数组长度 */
    fileArraysSize = fileInfoList.size();
    CodeUtils::getInstance().uint16ToBb(fileArraysSize, buf + index);
    index = index + 2;
    realLen = realLen + 2;
    printf("***************fileInfoList.size=%d \n", fileInfoList.size());
    /* 文件信息数组 */
    if (fileArraysSize > 0) {
        for (int i = 0; i < fileArraysSize; i++) {
            /* 文件类型 */
            buf[index] = fileInfoList[i].fileType;
            index = index + 1;
            realLen = realLen + 1;
            /* 报警目标类型 */
            CodeUtils::getInstance().uint16ToBb(0xff, buf + index);
            index = index + 2;
            realLen = realLen + 2;
            /* 文件状态 */
            buf[index] = fileInfoList[i].fileStatus;
            index = index + 1;
            realLen = realLen + 1;
            /* 相机ID */
            buf[index] = fileInfoList[i].cameraId;
            index = index + 1;
            realLen = realLen + 1;
            /* 录制时间 */
            memcpy(buf + index, fileInfoList[i].bcdTime, sizeof(fileInfoList[i].bcdTime));
            index = index + 7;
            realLen = realLen + 7;
            /* 文件名的长度 */
            buf[index] = fileInfoList[i].fileNameLen;
            index = index + 1;
            realLen = realLen + 1;
            /* 文件名 */
            memcpy(buf + index, fileInfoList[i].fileName, fileInfoList[i].fileNameLen);
            index = index + fileInfoList[i].fileNameLen;
            realLen = realLen + fileInfoList[i].fileNameLen;
        }
    }
    /* 塞真实的长度 */
    setContentLen(realLen);
    CodeUtils::getInstance().int32ToBb(realLen, buf + 6);

    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

uint16_t GetG3Mp4FileListResult::getFileArraysSize() const {
    return fileArraysSize;
}

void GetG3Mp4FileListResult::setFileArraysSize(uint16_t fileArraysSize) {
    GetG3Mp4FileListResult::fileArraysSize = fileArraysSize;
}

const std::vector<PCConfigureDataPacket::MP4FileInfo> &GetG3Mp4FileListResult::getFileInfoList() const {
    return fileInfoList;
}

void GetG3Mp4FileListResult::setFileInfoList(const std::vector<MP4FileInfo> &fileInfoList) {
    GetG3Mp4FileListResult::fileInfoList = fileInfoList;

}

uint8_t GetG3Mp4FileListResult::getResult() const {
    return result;
}

void GetG3Mp4FileListResult::setResult(uint8_t result) {
    GetG3Mp4FileListResult::result = result;
}
