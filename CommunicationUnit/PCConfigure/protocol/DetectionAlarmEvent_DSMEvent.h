//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/10/27.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONALARMEVENT_DSMEVENT_H
#define VIS_G3_SOFTWARE_DETECTIONALARMEVENT_DSMEVENT_H

#include "DetectionAlarmEvent_DataItem.h"
#include <CodeUtils.h>
#include <cstdint>

class DetectionAlarmEvent_DSMEvent : public DetectionAlarmEvent_DataItem {
public:
    DetectionAlarmEvent_DSMEvent();

    ~DetectionAlarmEvent_DSMEvent();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getCurCameraId() const;

    void setCurCameraId(uint8_t curCameraId);

    uint8_t getRespiratorAlarm() const;

    void setRespiratorAlarm(uint8_t respiratorAlarm);

    uint8_t getEyeAlarm() const;

    void setEyeAlarm(uint8_t eyeAlarm);

    uint8_t getMouthAlarm() const;

    void setMouthAlarm(uint8_t mouthAlarm);

    uint8_t getLookaroundAlarm() const;

    void setLookaroundAlarm(uint8_t lookaroundAlarm);

    uint8_t getFacemissingAlarm() const;

    void setFacemissingAlarm(uint8_t facemissingAlarm);

    uint8_t getCamcoverAlarm() const;

    void setCamcoverAlarm(uint8_t camcoverAlarm);

    uint8_t getSmokingAlarm() const;

    void setSmokingAlarm(uint8_t smokingAlarm);

    uint8_t getPhoneAlarm() const;

    void setPhoneAlarm(uint8_t phoneAlarm);

    uint8_t getFatigueRank() const;

    void setFatigueRank(uint8_t fatigueRank);

    const uint8_t &getBeltAlarm() const;

    void setBeltAlarm(const uint8_t &beltAlarm);

    const uint8_t &getBlindspotAlarm() const;

    void setBlindspotAlarm(const uint8_t &blindspotAlarm);

    uint8_t getHatAlarm() const;

    void setHatAlarm(uint8_t hatAlarm);

private:
    uint8_t curCameraId;
    //口罩报警状态
    uint8_t respirator_alarm;
    //闭眼报警状态
    uint8_t eye_alarm;
    //哈欠报警状态
    uint8_t mouth_alarm;
    //左顾右盼报警状态
    uint8_t lookaround_alarm;
    //离岗报警状态
    uint8_t facemissing_alarm;
    //遮挡报警状态
    uint8_t camcover_alarm;
    //抽烟报警状态
    uint8_t smoking_alarm;
    //打电话报警状态
    uint8_t phone_alarm;
    //疲劳程度
    uint8_t fatigue_rank;
    /* 安全带报警 */
    uint8_t belt_alarm;
    /* 盲区报警 */
    uint8_t blindspot_alarm;
    /* 帽子报警 */
    uint8_t hat_alarm;

};


#endif //VIS_G3_SOFTWARE_DETECTIONALARMEVENT_DSMEVENT_H
