//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/11.
//

#include "QequestG3Config.h"

QequestG3Config::QequestG3Config() {
    setMsgId(MSGID_QEQUEST_G3_CONFIG);

}

int QequestG3Config::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    setContentLen(1);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    buf[index] = getG3Id();
    index++;
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

int QequestG3Config::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        g3Id = buf[index];
        index++;
        ret = 0;
    }
    return ret;
}

uint8_t QequestG3Config::getG3Id() const {
    return g3Id;
}

void QequestG3Config::setG3Id(uint8_t g3Id) {
    QequestG3Config::g3Id = g3Id;
}
