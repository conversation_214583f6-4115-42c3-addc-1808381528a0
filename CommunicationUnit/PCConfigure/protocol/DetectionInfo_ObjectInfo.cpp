//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/13.
//



#include "DetectionInfo_ObjectInfo.h"

DetectionInfo_ObjectInfo::DetectionInfo_ObjectInfo() {
    setDataId(DATAID_OBJECTINFO);
}

DetectionInfo_ObjectInfo::~DetectionInfo_ObjectInfo() {

}


void DetectionInfo_ObjectInfo::addObjectToList(DetectionInfo_ObjectInfo_object_ &object) {
    objectList.push_back(object);
    objectListSize = objectList.size();

}


uint8_t DetectionInfo_ObjectInfo::getObjectListSize() const {
    return objectListSize;
}

void DetectionInfo_ObjectInfo::setObjectListSize(uint8_t objectListSize) {
    DetectionInfo_ObjectInfo::objectListSize = objectListSize;
}

int32_t DetectionInfo_ObjectInfo::getEventCode() const {
    return eventCode;
}

void DetectionInfo_ObjectInfo::setEventCode(int32_t eventCode) {
    DetectionInfo_ObjectInfo::eventCode = eventCode;
}

uint8_t DetectionInfo_ObjectInfo::getHasCamcoverd() const {
    return hasCamcoverd;
}

void DetectionInfo_ObjectInfo::setHasCamcoverd(uint8_t hasCamcoverd) {
    DetectionInfo_ObjectInfo::hasCamcoverd = hasCamcoverd;
}

int DetectionInfo_ObjectInfo::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度(随便塞一个) */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index += 4;
    int contentlen = 0;
    /* 再塞object_数组的长度 */
    buf[index] = objectListSize;
    index++;
    /* 再塞object_数组 */
    if (objectListSize > 0) {
        for (std::size_t i = 0; i < objectList.size(); i++) {
            memcpy(buf + index, objectList[i].label, 16);
            index += 16;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mId, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mTop, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mBottom, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mLeft, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mRight, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mXDistance, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mYDistance, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mHeadway, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mRealWidth, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mRealHeight, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mXVelocity, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mYVelocity, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mTrend, buf + index);
            index += 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mScore, buf + index);
            index += 4;
            buf[index] = objectList[i].shadow;
            index ++;
            CodeUtils::getInstance().uint16ToBb(objectList[i].mMaxScore, buf + index);
            index += 2;
            CodeUtils::getInstance().uint16ToBb(objectList[i].mMinScore, buf + index);
            index += 2;
            memcpy(buf + index,objectList[i].reserved, sizeof(objectList[i].reserved));
            index += sizeof(objectList[i].reserved);
        }
    }
    /* 再塞事件码 */
    CodeUtils::getInstance().int32ToBb(eventCode, buf + index);
    index += 4;
    /* 再塞是否遮挡 */
    buf[index] = hasCamcoverd;
    index++;
    /* 再塞相机Id */
    buf[index] = cameraId;
    index++;
    /* 把真实的长度塞进去 */
    contentlen = index - 6;
    CodeUtils::getInstance().uint32ToBb(contentlen, buf + 2);
    setDataLen(contentlen);

    ret = index;
    return ret;
}

uint8_t DetectionInfo_ObjectInfo::getCameraId() const {
    return cameraId;
}

void DetectionInfo_ObjectInfo::setCameraId(uint8_t curcameraId) {
    cameraId = curcameraId;
}

const std::vector<DetectionInfo_ObjectInfo_object_> &DetectionInfo_ObjectInfo::getObjectList() const {
    return objectList;
}
