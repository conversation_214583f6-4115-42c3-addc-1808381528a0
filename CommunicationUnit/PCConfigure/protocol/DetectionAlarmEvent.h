//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONALARMEVENT_H
#define VIS_G3_SOFTWARE_DETECTIONALARMEVENT_H

#include "PCConfigureDataPacket.h"
#include <vector>
#include "DetectionAlarmEvent_BSDEvent.h"
#include "DetectionAlarmEvent_GeneralInfo.h"
#include "DetectionAlarmEvent_ADASEvent.h"
#include "DetectionAlarmEvent_DSMEvent.h"
#include "DetectionAlarmEvent_R151Event.h"
#include "DetectionAlarmEvent_R158Event.h"
#include "DetectionAlarmEvent_R159Event.h"

class DetectionAlarmEvent : public PCConfigureDataPacket {
public:
    DetectionAlarmEvent();

    ~DetectionAlarmEvent();

    int addBSDEvent(DetectionAlarmEvent_BSDEvent &detectionAlarmEventBsdEvent);

    int addGeneralInfo(DetectionAlarmEvent_GeneralInfo &detectionAlarmEventGeneralInfo);

    int addADASEvent(DetectionAlarmEvent_ADASEvent &detectionAlarmEventAdasEvent);

    int addDSMEvent(DetectionAlarmEvent_DSMEvent &detectionAlarmEventDSMEvent);

    int addR151Event(DetectionAlarmEvent_R151Event &detectionAlarmEventR151Event);

    int addR158Event(DetectionAlarmEvent_R158Event &detectionAlarmEventR158Event);

    int addR159Event(DetectionAlarmEvent_R159Event &detectionAlarmEventR159Event);

    int toCode(uint8_t *buf, int len) override;

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

private:
    /* g3Id */
    uint8_t g3Id = 0;
    /* 识别信息项总数 */
    uint8_t infoListSize = 0;
    /* 报警公用数据项的数组 */
    std::vector<DetectionAlarmEvent_GeneralInfo> dataItemList_GeneralInfo;
    /* BSD报警数据项的数组 */
    std::vector<DetectionAlarmEvent_BSDEvent> dataItemList_BSD;
    /* ADAS报警数据项的数组 */
    std::vector<DetectionAlarmEvent_ADASEvent> dataItemList_ADAS;
    /* DSM报警数据项的数组 */
    std::vector<DetectionAlarmEvent_DSMEvent> dataItemList_DSM;
    /* R151报警数据项的数组 */
    std::vector<DetectionAlarmEvent_R151Event> dataItemList_r151;
    /* R158报警数据项的数组 */
    std::vector<DetectionAlarmEvent_R158Event> dataItemList_r158;
    /* R159报警数据项的数组 */
    std::vector<DetectionAlarmEvent_R159Event> dataItemList_r159;

};


#endif //VIS_G3_SOFTWARE_DETECTIONALARMEVENT_H
