//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/13.
//

#include "SetG3SystemTime.h"

SetG3SystemTime::SetG3SystemTime() {
    setMsgId(MSGID_SET_G3_SYSTEM_TIME);
}

int SetG3SystemTime::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        /* 解析G3的ID */
        setG3Id(buf[index]);
        index++;
        /* 时区内容的长度 */
        setTimeZoneStrLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        /* 时区内容 */
        timeZoneStr.clear();
        timeZoneStr.append(reinterpret_cast<const char *>(buf + index), getTimeZoneStrLen());
        index += getTimeZoneStrLen();
        /* 日期时间 */
        memcpy(bcdTime, buf + index, sizeof(bcdTime));
        index += sizeof(bcdTime);
        ret = 0;
    }
    return ret;
}

uint8_t SetG3SystemTime::getG3Id() const {
    return g3Id;
}

void SetG3SystemTime::setG3Id(uint8_t g3Id) {
    SetG3SystemTime::g3Id = g3Id;
}

uint32_t SetG3SystemTime::getTimeZoneStrLen() const {
    return timeZoneStrLen;
}

void SetG3SystemTime::setTimeZoneStrLen(uint32_t timeZoneStrLen) {
    SetG3SystemTime::timeZoneStrLen = timeZoneStrLen;
}

const std::string &SetG3SystemTime::getTimeZoneStr() const {
    return timeZoneStr;
}

void SetG3SystemTime::setTimeZoneStr(const std::string &timeZoneStr) {
    SetG3SystemTime::timeZoneStr = timeZoneStr;
}

const uint8_t *SetG3SystemTime::getBcdTime() const {
    return bcdTime;
}
