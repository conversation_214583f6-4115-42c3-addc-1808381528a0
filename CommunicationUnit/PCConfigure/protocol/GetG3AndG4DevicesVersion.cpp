//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/15.
//

#include "GetG3AndG4DevicesVersion.h"

GetG3AndG4DevicesVersion::GetG3AndG4DevicesVersion() {
    setMsgId(MSGID_GET_G3_AND_G4_VERSION);
}

uint8_t GetG3AndG4DevicesVersion::getDevicesId() const {
    return devicesId;
}

void GetG3AndG4DevicesVersion::setDevicesId(uint8_t devicesId) {
    GetG3AndG4DevicesVersion::devicesId = devicesId;
}

int GetG3AndG4DevicesVersion::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        setDevicesId(buf[index]);
        index++;
        ret = 0;
    }
    return ret;
}

int GetG3AndG4DevicesVersion::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    setContentLen(1);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 塞设备ID */
    buf[index] = getDevicesId();
    index++;
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);

    return ret;
}
