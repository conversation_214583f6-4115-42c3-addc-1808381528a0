//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/15.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONINFO_SYSTEMERROR_H
#define VIS_G3_SOFTWARE_DETECTIONINFO_SYSTEMERROR_H

#include <CodeUtils.h>
#include "DetectionInfo_DataItem.h"

class DetectionInfo_SystemError : public DetectionInfo_DataItem {
public:
    DetectionInfo_SystemError();

    ~DetectionInfo_SystemError();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getTfCardError() const;

    void setTfCardError(uint8_t tfCardError);

    uint8_t getHighTemperature() const;

    void setHighTemperature(uint8_t highTemperature);

    uint8_t getAdasCameraError() const;

    void setAdasCameraError(uint8_t adasCameraError);

    uint8_t getBsdCameraErrorL() const;

    void setBsdCameraErrorL(uint8_t bsdCameraErrorL);

    uint8_t getBsdCameraErrorR() const;

    void setBsdCameraErrorR(uint8_t bsdCameraErrorR);

    uint8_t getDsmCameraError() const;

    void setDsmCameraError(uint8_t dsmCameraError);

    uint8_t getAdasAlgorithmError() const;

    void setAdasAlgorithmError(uint8_t adasAlgorithmError);

    uint8_t getBsdAlgorithmErrorL() const;

    void setBsdAlgorithmErrorL(uint8_t bsdAlgorithmErrorL);

    uint8_t getBsdAlgorithmErrorR() const;

    void setBsdAlgorithmErrorR(uint8_t bsdAlgorithmErrorR);

    uint8_t getDsmAlgorithmError() const;

    void setDsmAlgorithmError(uint8_t dsmAlgorithmError);

    uint8_t getForntCameraError() const;

    void setForntCameraError(uint8_t forntCameraError);

    uint8_t getBackCameraError() const;

    void setBackCameraError(uint8_t backCameraError);

    uint8_t getForntAlgorithmError() const;

    void setForntAlgorithmError(uint8_t forntAlgorithmError);

    uint8_t getBackAlgorithmError() const;

    void setBackAlgorithmError(uint8_t backAlgorithmError);

    uint8_t getCamera1ImgInputError() const;

    void setCamera1ImgInputError(uint8_t camera1ImgInputError);

    uint8_t getCamera2ImgInputError() const;

    void setCamera2ImgInputError(uint8_t camera2ImgInputError);

    uint8_t getCamera1FullBlack() const;

    void setCamera1FullBlack(uint8_t camera1FullBlack);

    uint8_t getCamera2FullBlack() const;

    void setCamera2FullBlack(uint8_t camera2FullBlack);

    uint8_t getCamera3ImgInputError() const;

    void setCamera3ImgInputError(uint8_t camera3ImgInputError);

    uint8_t getCamera4ImgInputError() const;

    void setCamera4ImgInputError(uint8_t camera4ImgInputError);

    uint8_t getCamera3FullBlack() const;

    void setCamera3FullBlack(uint8_t camera3FullBlack);

    uint8_t getCamera4FullBlack() const;

    void setCamera4FullBlack(uint8_t camera4FullBlack);

private:

    /* Bit0	TF卡状态	0	TF卡正常    1	TF卡坏或无卡 */
    uint8_t tfCardError = 0;
    /* Bit1	高温报警	0	温度正常    1	温度超标。>90度 */
    uint8_t highTemperature = 0;
    /* Bit2	ADAS摄像头	0	ADAS摄像头工作正常    1	ADAS摄像头工作异常 */
    uint8_t adasCameraError = 0;
    /* Bit3	左侧摄像头	0	左侧摄像头工作正常    1	左侧摄像头工作异常 */
    uint8_t bsdCameraError_L = 0;
    /* Bit4	右侧摄像头	0	右侧摄像头工作正常    1	右侧摄像头工作异常 */
    uint8_t bsdCameraError_R = 0;
    /* Bit5	DSM摄像头	0	DSM摄像头工作正常     1	DSM摄像头工作异常  */
    uint8_t dsmCameraError = 0;
    /* Bit6	ADAS算法	0	ADAS算法正常    1	ADAS算法异常 */
    uint8_t adasAlgorithmError = 0;
    /* Bit7	左侧算法	0	左侧算法正常     1	左侧算法异常 */
    uint8_t bsdAlgorithmError_L = 0;
    /* Bit8	右侧算法	0	右侧算法正常     1	右侧算法异常 */
    uint8_t bsdAlgorithmError_R = 0;
    /* Bit9	DSM算法	0	DSM算法正常     1	DSM算法异常 */
    uint8_t dsmAlgorithmError = 0;
    /* Bit10	前侧摄像头	0	前侧摄像头工作正常    1	前侧摄像头工作异常 */
    uint8_t forntCameraError = 0;
    /* Bit11	后侧摄像头	0	后侧摄像头工作正常    1	后侧摄像头工作异常 */
    uint8_t backCameraError = 0;
    /* Bit12	前侧算法	0	前侧算法正常     1	前侧算法异常 */
    uint8_t forntAlgorithmError = 0;
    /* Bit13	后侧算法	0	后侧算法正常     1	后侧算法异常 */
    uint8_t backAlgorithmError = 0;
    /* Bit14	镜头1图像输入状态	0	图像输入正常     1	图像输入异常 */
    uint8_t camera1ImgInputError = 0;
    /* Bit15	镜头2图像输入状态	0	图像输入正常     1	图像输入异常 */
    uint8_t camera2ImgInputError = 0;
    /* Bit16	镜头1图像全黑状态	0	图像不是全黑     1	图像是全黑  */
    uint8_t camera1FullBlack = 0;
    /* Bit17	镜头2图像全黑状态	0	图像不是全黑     1	图像是全黑  */
    uint8_t camera2FullBlack = 0;
    /* Bit18	镜头3图像输入状态	0	图像输入正常     1	图像输入异常 */
    uint8_t camera3ImgInputError = 0;
    /* Bit19	镜头4图像输入状态	0	图像输入正常     1	图像输入异常 */
    uint8_t camera4ImgInputError = 0;
    /* Bit20	镜头3图像全黑状态	0	图像不是全黑     1	图像是全黑  */
    uint8_t camera3FullBlack = 0;
    /* Bit21	镜头4图像全黑状态	0	图像不是全黑     1	图像是全黑  */
    uint8_t camera4FullBlack = 0;


};


#endif //VIS_G3_SOFTWARE_DETECTIONINFO_SYSTEMERROR_H
