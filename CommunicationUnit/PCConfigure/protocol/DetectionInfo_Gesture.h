//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/10/28.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONINFO_GESTURE_H
#define VIS_G3_SOFTWARE_DETECTIONINFO_GESTURE_H

#include <vector>
#include <CodeUtils.h>
#include "DetectionInfo_DataItem.h"


struct DetectionInfo_Gesture_object_ {
    /* 类型 */
    char label[16];
    /* id号 */
    uint32_t mId;
    /* 识别框的上 */
    uint16_t mTop;
    /* 识别框的下 */
    uint16_t mBottom;
    /* 识别框的左 */
    uint16_t mLeft;
    /* 识别框的右 */
    uint16_t mRight;
    /* 得分   ÷100之后才是正确值 */
    uint32_t mScore;
};

class DetectionInfo_Gesture : public DetectionInfo_DataItem {
public:
    DetectionInfo_Gesture();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getCurCameraId() const;

    void setCurCameraId(uint8_t curCameraId);

    uint8_t getGestureListSize() const;

    void setGestureListSize(uint8_t gestureListSize);

    void addObjectToList(DetectionInfo_Gesture_object_ &object);

private:
    /* 相机ID */
    uint8_t curCameraId;
    /* 手势数组的长度 */
    uint8_t gestureListSize;
    /* 手势数组内容 */
    std::vector<DetectionInfo_Gesture_object_> gestureList;
};


#endif //VIS_G3_SOFTWARE_DETECTIONINFO_GESTURE_H
