//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/8.
//

#ifndef VIS_G3_SOFTWARE_RESPONDSETUUIDRESULT_H
#define VIS_G3_SOFTWARE_RESPONDSETUUIDRESULT_H

#include <string>
#include "PCConfigureDataPacket.h"

class RespondSetUUIDResult : public PCConfigureDataPacket {
public:
    RespondSetUUIDResult();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getResult() const;

    void setResult(uint8_t result);

    void setCPUSerialNumber(std::string cpuSerialNum);

private:
    /* 结果  0：成功  1：失败 */
    uint8_t result;
    /* CPU序列号内容的长度 */
    uint32_t strLen;
    /* CPU序列号内容的长度 */
    std::string curCPUSerialNumStr;
};


#endif //VIS_G3_SOFTWARE_RESPONDSETUUIDRESULT_H
