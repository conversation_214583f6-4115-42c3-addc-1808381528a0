//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/11.
//

#ifndef VIS_G3_SOFTWARE_SETG3CONFIG_H
#define VIS_G3_SOFTWARE_SETG3CONFIG_H

#include "PCConfigureDataPacket.h"

class SetG3Config : public PCConfigureDataPacket {
public:
    SetG3Config();

    ~SetG3Config();

    int decode(uint8_t *buf, int len) override;

    int toCode(uint8_t *buf, int len) override;

    uint32_t getConfigStrLen() const;

    void setConfigStrLen(uint32_t configStrLen);

    uint8_t *getConfigStr() const;

    void setConfigStr(uint8_t *configStr);

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

private:
    /* G3ID */
    uint8_t g3Id = 0;
    /* 配置表内容的长度 */
    uint32_t configStrLen;
    /* 配置表的内容 */
    uint8_t *configStr;

};


#endif //VIS_G3_SOFTWARE_SETG3CONFIG_H
