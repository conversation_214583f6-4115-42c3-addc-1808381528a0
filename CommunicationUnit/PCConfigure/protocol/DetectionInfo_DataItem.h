//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/13.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONINFO_DATAITEM_H
#define VIS_G3_SOFTWARE_DETECTIONINFO_DATAITEM_H

#include <cstdint>

class DetectionInfo_DataItem {
public:
    /* object数据项的ID */
    static const uint16_t DATAID_OBJECTINFO = 0x0001;
    /* 系统信息数据项的ID */
    static const uint16_t DATAID_SYSTEMINFO = 0x0002;
    /* 系统错误信息的ID */
    static const uint16_t DATAID_SYSTEMERROR = 0x0003;
    /* 人脸识别信息的ID */
    static const uint16_t DATAID_DSM = 0x0004;
    /* ADAS识别信息的ID */
    static const uint16_t DATAID_ADAS = 0x0005;
    /* 手势识别信息的ID */
    static const uint16_t DATAID_GESTURE = 0x0006;
    /* 语义分割的maskBuf */
    static const uint16_t DATAID_MASKBUF= 0x0007;
    /* 灯控信息 */
    static const uint16_t DATAID_LAMP_CONTROL= 0x0008;
    /* R151的识别信息 */
    static const uint16_t DATAID_R151_INFO= 0x0009;
    /* R158的识别信息 */
    static const uint16_t DATAID_R158_INFO= 0x000A;
    /* R159的识别信息 */
    static const uint16_t DATAID_R159_INFO= 0x000B;
    /* 安全带的识别信息 */
    static const uint16_t DATAID_SEATBELT= 0x000C;
    /* 算法耗时信息 */
    static const uint16_t DATAID_ALG_DETECT_TIME= 0x000D;
    /* 英国DD的条码识别信息 */
    static const uint16_t DATAID_BARCODE_INFO= 0x000E;
    /* 英国DD的条码识别信息 */
    static const uint16_t DATAID_SECURITYMODE_INFO= 0x000F;
    /* 加速度传感器数据 */
    static const uint16_t DATAID_GSENSOR_DATA= 0x0010;
    /* IO口状态数据 */
    static const uint16_t DATAID_GPIOSTATUS_DATA= 0x0011;
    /* 信号及状态数据 */
    static const uint16_t DATAID_SIGNALANDSTATUS_DATA= 0x0012;

    DetectionInfo_DataItem();

    ~DetectionInfo_DataItem();

    virtual int toCode(uint8_t *buf, int len);


    uint16_t getDataId() const;

    void setDataId(uint16_t dataId);

    uint32_t getDataLen() const;

    void setDataLen(uint32_t dataLen);

private:
    /* 数据项ID */
    uint16_t dataId = -1;
    /* 数据项的长度 */
    uint32_t dataLen = 0;

};


#endif //VIS_G3_SOFTWARE_DETECTIONINFO_DATAITEM_H
