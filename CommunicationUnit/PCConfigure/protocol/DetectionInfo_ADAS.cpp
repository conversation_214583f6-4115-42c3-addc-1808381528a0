//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/1.
//

#include <cstdio>
#include "DetectionInfo_ADAS.h"
#include "CodeUtils.h"

DetectionInfo_ADAS::DetectionInfo_ADAS() {
    setDataId(DATAID_ADAS);
}

int DetectionInfo_ADAS::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index = index + 2;
    /* 再塞总长度(随便塞一个) */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index = index + 4;
    int contentlen = 0;
    /* 塞相机ID */
    buf[index] = curCameraId;
    index = index + 1;
    /* 再塞物体数组的长度 */
    buf[index] = objectListLen;
    index = index + 1;
    /* 再塞物体数组 */
    if (objectListLen > 0) {
        for (std::size_t i = 0; i < objectList.size(); i++) {
            memcpy(buf + index, objectList[i].label, 16);
            index = index + 16;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mId, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint16ToBb(objectList[i].mTop, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint16ToBb(objectList[i].mBottom, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint16ToBb(objectList[i].mLeft, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint16ToBb(objectList[i].mRight, buf + index);
            index = index + 2;
            CodeUtils::getInstance().int32ToBb(objectList[i].mXDistance, buf + index);
            index = index + 4;
            CodeUtils::getInstance().int32ToBb(objectList[i].mYDistance, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mRealWidth, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mRealHeight, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mXVelocity, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mYVelocity, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mTrend, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mHeadway, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mTTC, buf + index);
            index = index + 4;
            buf[index] = objectList[i].mIsAheadCar;
            index = index + 1;
            CodeUtils::getInstance().uint32ToBb(objectList[i].mScore, buf + index);
            index = index + 4;
            buf[index] = objectList[i].isShadow;
            index = index + 1;
            CodeUtils::getInstance().uint16ToBb(objectList[i].mMaxScore, buf + index);
            index += 2;
            CodeUtils::getInstance().uint16ToBb(objectList[i].mMinScore, buf + index);
            index += 2;
            memcpy(buf + index,objectList[i].reserved, sizeof(objectList[i].reserved));
            index += sizeof(objectList[i].reserved);

//            printf("vehicle i=%d  label=%s  ttc=%d  hmw=%d  \n",i,objectList[i].label,objectList[i].mTTC,objectList[i].mHeadway);
        }
    }


    /* 再塞车道线数组的长度 */
    buf[index] = laneLineListLen;
    index = index + 1;
    /* 再塞车道线数组 */
    if (laneLineListLen > 0) {
        for (std::size_t i = 0; i < laneLineList.size(); i++) {
            memcpy(buf + index, laneLineList[i].label, 16);
            index = index + 16;
            CodeUtils::getInstance().uint32ToBb(laneLineList[i].mId, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint16ToBb(laneLineList[i].mEndpointAX, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint16ToBb(laneLineList[i].mEndpointAY, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint16ToBb(laneLineList[i].mEndpointBX, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint16ToBb(laneLineList[i].mEndpointBY, buf + index);
            index = index + 2;
            CodeUtils::getInstance().int32ToBb(laneLineList[i].mXDistance, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(laneLineList[i].mCrossSpeed, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(laneLineList[i].mTTLC, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(laneLineList[i].mAngleToEgo, buf + index);
            index = index + 4;
            memcpy(buf+index,&laneLineList[i].mA0, sizeof(laneLineList[i].mA0));
            index = index + 8;
            memcpy(buf+index,&laneLineList[i].mA1, sizeof(laneLineList[i].mA1));
            index = index + 8;
            memcpy(buf+index,&laneLineList[i].mA2, sizeof(laneLineList[i].mA2));
            index = index + 8;
            memcpy(buf+index,&laneLineList[i].mA3, sizeof(laneLineList[i].mA3));
            index = index + 8;
            buf[index] = laneLineList[i].mIsLeftLane;
            index = index + 1;
            buf[index] = laneLineList[i].mIsRightLane;
            index = index + 1;

        }
    }

    /* 再塞标志物数组的长度 */
    buf[index] = markersListLen;
    index = index + 1;
    /* 再塞标志物数组 */
    if (markersListLen > 0) {
        for (std::size_t i = 0; i < markersList.size(); i++) {
            memcpy(buf + index, markersList[i].label, 16);
            index = index + 16;
            CodeUtils::getInstance().uint32ToBb(markersList[i].mId, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint16ToBb(markersList[i].mTop, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint16ToBb(markersList[i].mBottom, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint16ToBb(markersList[i].mLeft, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint16ToBb(markersList[i].mRight, buf + index);
            index = index + 2;
            CodeUtils::getInstance().uint32ToBb(markersList[i].mRealWidth, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(markersList[i].mRealHeight, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(markersList[i].mXVelocity, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(markersList[i].mYVelocity, buf + index);
            index = index + 4;
            CodeUtils::getInstance().uint32ToBb(markersList[i].mSpeedLimit, buf + index);
            index = index + 4;
            memcpy(buf + index, markersList[i].mTrafficLight, 16);
            index = index + 16;
            memcpy(buf + index, markersList[i].mTrafficSign, 16);
            index = index + 16;
            CodeUtils::getInstance().int32ToBb(markersList[i].mTrafficSignValue, buf + index);
            index = index + 4;
        }
    }
    /* 再塞maskBuf的长度 */
    CodeUtils::getInstance().uint32ToBb(maskBufLen, buf + index);
    index = index + 4;
    /* 再塞maskBuf */
    if (maskBufLen > 0) {
        memcpy(buf + index, maskBuf, maskBufLen);
        index = index + maskBufLen;
    }

    /* 把真实的长度塞进去 */
    contentlen = index - 6;
    CodeUtils::getInstance().uint32ToBb(contentlen, buf + 2);

    ret = index;
    return ret;
}

uint8_t DetectionInfo_ADAS::getCurCameraId() const {
    return curCameraId;
}

void DetectionInfo_ADAS::setCurCameraId(uint8_t curCameraId) {
    DetectionInfo_ADAS::curCameraId = curCameraId;
}

void DetectionInfo_ADAS::addObjectList(DetectionInfo_ADAS_object_ adasObjectInfo) {
    objectList.push_back(adasObjectInfo);
    objectListLen = objectList.size();
}

void DetectionInfo_ADAS::addLaneLineList(DetectionInfo_ADAS_Laneline_ adasLanelineInfo) {
    laneLineList.push_back(adasLanelineInfo);
    laneLineListLen = laneLineList.size();

}

void DetectionInfo_ADAS::addMarkersList(DetectionInfo_ADAS_markers_ adasMarkersInfo) {
    markersList.push_back(adasMarkersInfo);
    markersListLen = markersList.size();
}

void DetectionInfo_ADAS::setMaskBuf(const uint8_t *const buf, const int len) {
    memcpy(maskBuf, buf, len);
    maskBufLen = len;
}

uint8_t DetectionInfo_ADAS::getObjectListLen() const {
    return objectListLen;
}

const std::vector<DetectionInfo_ADAS_object_> &DetectionInfo_ADAS::getObjectList() const {
    return objectList;
}

uint8_t DetectionInfo_ADAS::getLaneLineListLen() const {
    return laneLineListLen;
}

const std::vector<DetectionInfo_ADAS_Laneline_> &DetectionInfo_ADAS::getLaneLineList() const {
    return laneLineList;
}

uint8_t DetectionInfo_ADAS::getMarkersListLen() const {
    return markersListLen;
}

const std::vector<DetectionInfo_ADAS_markers_> &DetectionInfo_ADAS::getMarkersList() const {
    return markersList;
}

uint32_t DetectionInfo_ADAS::getMaskBufLen() const {
    return maskBufLen;
}

const uint8_t *DetectionInfo_ADAS::getMaskBuf() const {
    return maskBuf;
}


