//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/31.
//

#include "StartG3DeviceUpgrade.h"

StartG3DeviceUpgrade::StartG3DeviceUpgrade() {
    setMsgId(MSGID_START_UPGRADE);
}

int StartG3DeviceUpgrade::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        setG3Id(buf[index]);
        index++;
        setFileType(buf[index]);
        index++;
        setFileLen(CodeUtils::getInstance().BbToUint32(buf + index));
        index += 4;
        memcpy(fileMD5, buf + index, sizeof(fileMD5));
        index += sizeof(fileMD5);
        setFileVersion(CodeUtils::getInstance().BbToUint32(buf + index));
        index += 4;
        ret = 0;
    }
    return ret;
}

uint8_t StartG3DeviceUpgrade::getG3Id() const {
    return g3Id;
}

void StartG3DeviceUpgrade::setG3Id(uint8_t g3Id) {
    StartG3DeviceUpgrade::g3Id = g3Id;
}

uint8_t StartG3DeviceUpgrade::getFileType() const {
    return fileType;
}

void StartG3DeviceUpgrade::setFileType(uint8_t fileType) {
    StartG3DeviceUpgrade::fileType = fileType;
}

uint32_t StartG3DeviceUpgrade::getFileLen() const {
    return fileLen;
}

void StartG3DeviceUpgrade::setFileLen(uint32_t fileLen) {
    StartG3DeviceUpgrade::fileLen = fileLen;
}

const uint8_t *StartG3DeviceUpgrade::getFileMd5() const {
    return fileMD5;
}

int StartG3DeviceUpgrade::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    setContentLen(22);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 塞G3ID */
    buf[index] = getG3Id();
    index++;
    /* 再文件类型 */
    buf[index] = getFileType();
    index++;
    /* 再塞文件的长度 */
    CodeUtils::getInstance().uint32ToBb(getFileLen(), buf + index);
    index += 4;
    /* 再塞文件的MD5 */
    memcpy(buf + index, fileMD5, 16);
    index += 16;
    /* 再塞文件的版本号 */
    CodeUtils::getInstance().uint32ToBb(getFileVersion(), buf + index);
    index += 4;

    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

void StartG3DeviceUpgrade::setFileMd5(uint8_t *md5, const int len) {
    if (len > 0) {
        memcpy(fileMD5, md5, len);
    }
}

uint32_t StartG3DeviceUpgrade::getFileVersion() const {
    return fileVersion;
}

void StartG3DeviceUpgrade::setFileVersion(uint32_t fileVersion) {
    StartG3DeviceUpgrade::fileVersion = fileVersion;
}
