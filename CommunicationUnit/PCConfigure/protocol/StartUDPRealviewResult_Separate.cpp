//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/9.
//

#include "StartUDPRealviewResult_Separate.h"

StartUDPRealviewResult_Separate::StartUDPRealviewResult_Separate() {
    setMsgId(MSGID_UDP_REALVIEW_SEPARATE_OPT_RESULT);
}

int StartUDPRealviewResult_Separate::toCode(uint8_t *buf, int len) {
    int ret = -1;
    if (len >= (9 + 8)) {
        int index = 6;
        setContentLen(5);
        CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
        index += 4;
        /* 再塞G3Id */
        buf[index] = getG3Id();
        index++;
        /* 再塞需要开启实景的相机ID */
        buf[index] = getCameraId();
        index++;
        /* 再塞结果 */
        buf[index] = getResult();
        index++;
        /* 再塞端口 */
        CodeUtils::getInstance().int16ToBb(getPort(), buf + index);
        index += 2;
        /* 加上头尾那些东西 */
        ret = PCConfigureDataPacket::toCode(buf, len);
    }
    return ret;
}

uint8_t StartUDPRealviewResult_Separate::getG3Id() const {
    return g3Id;
}

void StartUDPRealviewResult_Separate::setG3Id(uint8_t g3Id) {
    StartUDPRealviewResult_Separate::g3Id = g3Id;
}

uint8_t StartUDPRealviewResult_Separate::getCameraId() const {
    return cameraId;
}

void StartUDPRealviewResult_Separate::setCameraId(uint8_t cameraId) {
    StartUDPRealviewResult_Separate::cameraId = cameraId;
}

uint8_t StartUDPRealviewResult_Separate::getResult() const {
    return result;
}

void StartUDPRealviewResult_Separate::setResult(uint8_t result) {
    StartUDPRealviewResult_Separate::result = result;
}

uint16_t StartUDPRealviewResult_Separate::getPort() const {
    return port;
}

void StartUDPRealviewResult_Separate::setPort(uint16_t port) {
    StartUDPRealviewResult_Separate::port = port;
}
