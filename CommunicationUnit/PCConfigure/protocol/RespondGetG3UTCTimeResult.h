//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/15.
//

#ifndef VIS_G3_SOFTWARE_RESPONDGETG3UTCTIMERESULT_H
#define VIS_G3_SOFTWARE_RESPONDGETG3UTCTIMERESULT_H


#include "PCConfigureDataPacket.h"

class RespondGetG3UTCTimeResult : public PCConfigureDataPacket {
public:
    RespondGetG3UTCTimeResult();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getResult() const;

    void setResult(uint8_t result);

    const uint8_t *getBcdTime() const;

    void setBcdTime(uint8_t *utctime);

private:
    /* 结果  0：成功  1：失败 */
    uint8_t result;
    /* BCD格式的UTC时间 */
    uint8_t bcdTime[8] = {0x00};

};


#endif //VIS_G3_SOFTWARE_RESPONDGETG3UTCTIMERESULT_H
