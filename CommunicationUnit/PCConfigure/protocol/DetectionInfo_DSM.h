//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/10/27.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONINFO_DSM_H
#define VIS_G3_SOFTWARE_DETECTIONINFO_DSM_H

#include "DetectionInfo_DataItem.h"

class DetectionInfo_DSM : public DetectionInfo_DataItem {
public:
    DetectionInfo_DSM();

    ~DetectionInfo_DSM();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getCurCameraId() const;

    void setCurCameraId(uint8_t curCameraId);

    uint8_t getIsSameDriver() const;

    void setIsSameDriver(uint8_t isSameDriver);

    uint16_t getFaceScore() const;

    void setFaceScore(uint16_t faceScore);

    uint16_t getFaceTop() const;

    void setFaceTop(uint16_t faceTop);

    uint16_t getFaceBottom() const;

    void setFaceBottom(uint16_t faceBottom);

    uint16_t getFaceLeft() const;

    void setFaceLeft(uint16_t faceLeft);

    uint16_t getFaceRight() const;

    void setFaceRight(uint16_t faceRight);

    uint8_t getFaceCharacteristicPointLen() const;

    void setFaceCharacteristicPointLen(uint8_t faceCharacteristicPointLen);

    void setFaceCharacteristicPoint(const uint8_t *const buf, const int len);

    uint8_t getHasFace() const;

    void setHasFace(uint8_t hasFace);

    uint8_t getIsRespirator() const;

    void setIsRespirator(uint8_t isRespirator);

    int32_t getFaceangle() const;

    void setFaceangle(int32_t faceangle);

private:
    /* 当前相机ID */
    uint8_t curCameraId;
    /* 是否有人脸 */
    uint8_t hasFace = 0;
    //人脸是否是相同驾驶员
    uint8_t isSameDriver;
    //是否戴口罩
    uint8_t isRespirator = 0;
    //人脸得分
    uint16_t face_score;
    //人脸框-上
    uint16_t face_top;
    //人脸框-下
    uint16_t face_bottom;
    //人脸框-左
    uint16_t face_left;
    //人脸框-右
    uint16_t face_right;
    //人脸特征点数组长度
    uint8_t faceCharacteristicPointLen = 0;
    //人脸五官特征点，五个点XY
    uint8_t faceCharacteristicPoint[20] = {0};
    //人脸的角度
    int32_t faceangle = 0;
};


#endif //VIS_G3_SOFTWARE_DETECTIONINFO_DSM_H
