//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/9.
//

#include <malloc.h>
#include "ConfigureMessageDecoder.h"

namespace vis {

    ConfigureMessageDecoder::ConfigureMessageDecoder() {
        dataCache = static_cast<uint8_t *>(malloc(MAX_DATA_DATA_SIZE));
        formalDataBuf = static_cast<uint8_t *>(malloc(MAX_DATA_DATA_SIZE));
    }

    void ConfigureMessageDecoder::onDataReceived(const char *ip, const int port, const uint8_t *buf, int len) {
        for (int i = 0; i < len; i++) {
            switch (buf[i]) {
                case IDENTIFICATION_HEAD:
                    resetData();
                    addANewCharToCache(buf[i]);
                    break;
                case IDENTIFICATION_TAIL:
                    addANewCharToCache(buf[i]);
                    doDecode(dataCache, point, ip, port);
                    resetData();
                    break;
                default:
                    addANewCharToCache(buf[i]);
                    break;
            }
        }
    }

    void ConfigureMessageDecoder::resetData() {
        point = 0;

    }

    void ConfigureMessageDecoder::addANewCharToCache(uint8_t nc) {
        dataCache[point] = nc;
        point++;
        if (point >= MAX_DATA_DATA_SIZE) {
            point = 0;
        }
    }

    void ConfigureMessageDecoder::doDecode(uint8_t *buf, int len, const char *ip, const int port) {

        //先转义一下
        int packagetDataLen = CodeUtils::getInstance().doEscape4ReceiveFromG3PCConfigure(buf, len, 1, len - 2,
                                                                                         formalDataBuf);

        /* 判断长度是否达到了最小的数据包长度 */
        if (packagetDataLen < PCConfigureDataPacket::MIN_DATA_LEN) {
            printf("error package len. %d  \n", packagetDataLen);
            return;
        }

        //解析出校验码
        uint16_t checkCode = CodeUtils::getInstance().BbToUint16(formalDataBuf + (packagetDataLen - 3));
        //自己计算下校验码
        uint16_t mCheckCode = CodeUtils::getInstance().generateCrc16(formalDataBuf + 1, packagetDataLen - 4);
        // 判断校验码是否正确
        if (checkCode != mCheckCode) {
            printf("Socket checkCode error!  PacketCheckCode=%d   mCheckCode=%d  \n", checkCode, mCheckCode);
            return;
        }
//        // 看看是不是流水号是不是上次的大
//        int curFlowId = CodeUtils::getInstance().BbToUint32(formalDataBuf + 1);
//        if(curFlowId <= lastRecvFlowId && curFlowId != 0xFFFFFFFF && lastRecvFlowId != 0xFFFFFFFF){
//            // 本次流水号不比上一次的大，说明是重复发送的，可能是重放攻击，直接扔掉
//            printf("curFlowId <= lastRecvFlowId!  curFlowId=%d   lastRecvFlowId=%d  \n", curFlowId, lastRecvFlowId);
//            return;
//        }
//        // 保存一下这条流水号
//        lastRecvFlowId = curFlowId;
        //根据命令字判断用什么类装
        switch (formalDataBuf[5]) {
            case PCConfigureDataPacket::MSGID_UNIVERSALANSWER: {
                /* 通用应答 */
//                printf("this msg is a universal answer!  \n");
                UniversalAnswer universalAnswer;
                if (universalAnswer.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetUniversalAnswer(universalAnswer, ip, port);
                }
            }
                break;
            case PCConfigureDataPacket::MSGID_HEARTBEAT: {
                /* 心跳 */
                printf("this msg is a heartbeat!  \n");
                Heartbeat heartbeat;
                if (heartbeat.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetHeartBeat(heartbeat, ip, port);
                }

            }
                break;

            case PCConfigureDataPacket::MSGID_START_UDP_REALVIEW: {
                /* 开启UDP实景（广播） */
                printf("this msg is a start UDP realview!  \n");
                StartUDPRealview startUdpRealview;
                if (startUdpRealview.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetStartUDPRealview(startUdpRealview, ip, port);
                } else {

                }

            }
                break;

            case PCConfigureDataPacket::MSGID_QEQUEST_G3_CONFIG: {
                /* 获取配置表 */
                QequestG3Config qequestG3Config;
                if (qequestG3Config.decode(formalDataBuf, packagetDataLen) == 0) {

                    callback->onGetQequestG3Config(qequestG3Config, ip, port);
                }

            }
                break;

            case PCConfigureDataPacket::MSGID_SET_G3_CONFIG: {
                /* 设置配置表 */
                printf("this msg is a set g3 config!  \n");
                SetG3Config setG3Config;
                if (setG3Config.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetSetG3Config(setG3Config, ip, port);
                }
            }
                break;
            case PCConfigureDataPacket::MSGID_RESPOND_REALTIME_VEHICLE_STATUS: {
                /* 实时车况 */
//                printf("this msg is a realtime vehicle status  \n");
                if (mRealTimeVehicleStatus.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetRealtimeVehicleStatus(mRealTimeVehicleStatus, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_START_UPGRADE: {
                /* 开始更新 */
                printf("this msg is a start g3 device upgrade  \n");
                StartG3DeviceUpgrade startG3DeviceUpgrade;
                if (startG3DeviceUpgrade.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetStartG3DeviceUpgrade(startG3DeviceUpgrade, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_SET_G3_SYSTEM_TIME: {
                /* 设置系统时间 */
                printf("this msg is a set G3 system time  \n");
                SetG3SystemTime setG3SystemTime;
                if (setG3SystemTime.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetSetG3SystemTime(setG3SystemTime, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_G3_AND_G4_VERSION: {
                /* 获取版本号 */
                printf("this msg is a get g3 and g4 version  \n");
                GetG3AndG4DevicesVersion getG3AndG4DevicesVersion;
                if (getG3AndG4DevicesVersion.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetG3AndG4DeviceVersion(getG3AndG4DevicesVersion, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_SET_G3_AND_G4_UUID: {
                /* 设置UUID */
                printf("this msg is a set UUID  \n");
                SetG3UUID g3UuidSet;
                if (g3UuidSet.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetSetG3UUID(g3UuidSet, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_G3_AND_G4_CPU_SERIAL_NUM: {
                /* 获取CPU串行码 */
                printf("this msg is a get CPU serial number  \n");
                GetCPUSerialNum cpuSerialNumGet;
                if (cpuSerialNumGet.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetG3AndG4CPUSerialNumber(cpuSerialNumGet, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_G3_AND_G4_UUID: {
                /* 获取UUID */
                printf("this msg is a get UUID  \n");
                GetG3UUID getUuid;
                if (getUuid.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetG3UUID(getUuid, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_G3_UTC_TIME: {
                /* 获取系统当前时间（UTC时间） */
                printf("this msg is a get UTC time  \n");
                GetG3UTCTime getUtcTime;
                if (getUtcTime.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetG3UTCTime(getUtcTime, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_G3_MP4_FILE_LIST: {
                /* 获取视频文件列表 */
                printf("this msg is a get G3 mp4 file List  \n");
                GetG3Mp4FileList getG3Mp4FileList;
                if (getG3Mp4FileList.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetG3Mp4FileList(getG3Mp4FileList, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_G3_MP4_FILE_OPT: {
                /* 操作视频文件 */
                printf("this msg is a  mp4 file opt  \n");
                G3Mp4FileOpt g3Mp4FileOpt;
                if (g3Mp4FileOpt.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetG3Mp4FileOpt(g3Mp4FileOpt, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_START_G3_MP4_FILE_TRANSFER: {
                /* 请求传输视频文件 */
                printf("this msg is a  g3 file download  \n");
                StartG3FileDownload g3FileDownload;
                if (g3FileDownload.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetStartG3FileDownload(g3FileDownload, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_START_G3_DEBUG_MODEL: {
                /* 打开DEBUG模式 */
                printf("this msg is a  start g3 debug model  \n");
                StartG3DebugMode startG3DebugMode;
                if (startG3DebugMode.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetStartG3DebugMode(startG3DebugMode, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_SET_FUNCTIONLOCK_INFO: {
                /* 设置功能锁 */
                printf("this msg is a  set function lock  \n");
                SetFunctionLock setFunctionLock;
                if (setFunctionLock.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetSetFunctionLock(setFunctionLock, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_FUNCTIONLOCK_INFO: {
                /* 获取功能锁 */
                printf("this msg is a  get function lock  \n");
                GetFunctionLockInfo getFunctionLockInfo;
                if (getFunctionLockInfo.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetFunctionLockInfo(getFunctionLockInfo, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_REBOOT_MRV220: {
                /* 重启RMV220软件/硬件 */
                printf("this msg is a  restart mrv220  \n");
                RestartG3 restartG3;
                if (restartG3.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetRestartMRV220(restartG3, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_UDP_REALVIEW_SEPARATE_OPT: {
                /* 开启UDP实景（单播） */
                printf("this msg is a  start realview separate  \n");
                StartUDPRealview_Separate startUdpRealviewSeparate;
                if (startUdpRealviewSeparate.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetStartUDPRealviewSeparate(startUdpRealviewSeparate, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_SET_RS485_PERIPHERAL_DISPLAY_PARAMS: {
                /* 设置RS485显示参数 */
                printf("this msg is a  set rs485 peripheral display papams  \n");
                SetPeripheralDisplayParams setPeripheralDisplayParams;
                if (setPeripheralDisplayParams.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetSetPeripheralDisplayParams(setPeripheralDisplayParams, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_MRV220_LOG_FILE_LIST: {
                /* 获取MRV220日志文件列表 */
                printf("this msg is a  get mrv220 log file list  \n");
                GetMRV220LogFileList getMrv220LogFileList;
                if (getMrv220LogFileList.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetMRV220LogFileList(getMrv220LogFileList, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_START_MRV220_LOG_FILE_TRANSFER: {
                /* 开启MRV220日志文件传输 */
                printf("this msg is a  start mrv220 log file transfer  \n");
                StartMRV220LogFileDownload startMrv220LogFileDownload;
                if (startMrv220LogFileDownload.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetStartMRV220LogFileDownload(startMrv220LogFileDownload, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_MULTIMEDIA_FILE_ENCRYPTION_KEY: {
                /* 获取多媒体文件加密所使用的密钥 */
                printf("this msg is get multimedia file encryption!  \n");
                GetMultimediaFileEncryptionKey getMultimediaFileEncryptionKey;
                if (getMultimediaFileEncryptionKey.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetMultimediaFileEncryptionKey(getMultimediaFileEncryptionKey, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_SET_MULTIMEDIA_FILE_ENCRYPTION_KEY: {
                /* 设置多媒体文件加密所使用的密钥 */
                printf("this msg is set multimedia file encryption!  \n");
                SetMultimediaFileEncryptionKey setMultimediaFileEncryptionKey;
                if (setMultimediaFileEncryptionKey.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetSetMultimediaFileEncryptionKey(setMultimediaFileEncryptionKey, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_ALARM_SOUND_FILE_CONFIG: {
                /* 获取报警声音文件的配置参数 */
                printf("this msg is get alaram sound file config!  \n");
                GetAlarmSoundFileConf getAlarmSoundFileConf;
                if (getAlarmSoundFileConf.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetAlarmSoundFileConf(getAlarmSoundFileConf, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_CAMERA_INPUT_TYPE_FILTER_CONFIG: {
                /* 获取镜头输入类型过滤信息 */
                printf("this msg is get camera input type filter config!  \n");
                GetCameraInputTypeFilterConfig getCameraInputTypeFilterConfig;
                if (getCameraInputTypeFilterConfig.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetCameraInputTypeFilterConfig(getCameraInputTypeFilterConfig, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_SET_CAMERA_INPUT_TYPE_FILTER_CONFIG: {
                /* 设置镜头输入类型过滤信息 */
                printf("this msg is set camera input type filter config!  \n");
                SetCameraInputTypeFilterConfig setCameraInputTypeFilterConfig;
                if (setCameraInputTypeFilterConfig.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetSetCameraInputTypeFilterConfig(setCameraInputTypeFilterConfig, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_GET_CAMERA_REDUCE_EFFECT_INFO: {
                /* 获取镜头降效信息 */
                printf("this msg is get camera reduce effect info!  \n");
                GetCameraReduceEffectInfo getCameraReduceEffectInfo;
                if (getCameraReduceEffectInfo.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetCameraReduceEffectInfoFromSocket(getCameraReduceEffectInfo, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_SET_CAMERA_REDUCE_EFFECT_INFO: {
                /* 设置镜头输入类型过滤信息 */
                printf("this msg is set camera reduce effect info!  \n");
                SetCameraReduceEffectInfo setCameraReduceEffectInfo;
                if (setCameraReduceEffectInfo.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetSetCameraReduceEffectInfoFromSocket(setCameraReduceEffectInfo, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_SET_PRODUCTION_TESTING_SWITCH: {
                /* 设置开关MRV220产测模式 */
                printf("this msg is set production testing switch status!  \n");
                SetProductionTestingSwitch setProductionTestingSwitch;
                if (setProductionTestingSwitch.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetSetProductionTestingSwitchFromSocket(setProductionTestingSwitch, ip, port);
                }
            }
                break;

            case PCConfigureDataPacket::MSGID_TCP_REALVIEW_OPT: {
                /* 开/关TCP实景数据流 */
                printf("this msg is tcp real view opt!  \n");
                TCPRealviewOpt tcpRealviewOpt;
                if (tcpRealviewOpt.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetTCPRealviewOptFromSocket(tcpRealviewOpt, ip, port);
                }
            }
                break;



        }
    }

    void ConfigureMessageDecoder::setInterface(CommunicationDataCallback &communicationDataCallback) {
        callback = &communicationDataCallback;
    }

}