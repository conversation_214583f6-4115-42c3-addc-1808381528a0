//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/5.
//

#include "StartG3FileDownload.h"

StartG3FileDownload::StartG3FileDownload() {
    setMsgId(MSGID_START_G3_MP4_FILE_TRANSFER);
}

int StartG3FileDownload::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index = index + 4;


        fileInfo.fileType = buf[index];
        index = index + 1;

        uint16_t detectTemp = CodeUtils::getInstance().BbToUint16(buf + index);
        memcpy(&fileInfo.detectContent, &detectTemp, 2);
        index = index + 2;

        fileInfo.fileStatus = buf[index];
        index = index + 1;

        fileInfo.cameraId = buf[index];
        index = index + 1;

        memcpy(&fileInfo.bcdTime, buf + index, 7);
        index = index + 7;

        fileInfo.fileNameLen = buf[index];
        index = index + 1;


        uint8_t filenameBytes[fileInfo.fileNameLen];
        memcpy(filenameBytes, buf + index, fileInfo.fileNameLen);
        memcpy(fileInfo.fileName,filenameBytes,fileInfo.fileNameLen);
        index = index + fileInfo.fileNameLen;


        ret = 0;
    }
    return ret;
}

const PCConfigureDataPacket::MP4FileInfo &StartG3FileDownload::getFileInfo() const {
    return fileInfo;
}

void StartG3FileDownload::setFileInfo(const PCConfigureDataPacket::MP4FileInfo &fileInfo) {
    StartG3FileDownload::fileInfo = fileInfo;
}
