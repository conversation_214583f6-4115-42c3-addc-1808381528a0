//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/10/28.
//

#include "DetectionInfo_Gesture.h"

DetectionInfo_Gesture::DetectionInfo_Gesture() {
    setDataId(DATAID_GESTURE);
}

int DetectionInfo_Gesture::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度(随便塞一个) */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index += 4;
    int contentlen = 0;
    /* 再塞相机ID */
    buf[index] = curCameraId;
    index++;
    contentlen++;
    /* 再塞object_数组的长度 */
    buf[index] = gestureListSize;
    index++;
    contentlen++;
    /* 再塞object_数组 */
    if (gestureListSize > 0) {
        for (std::size_t i = 0; i < gestureList.size(); i++) {
            memcpy(buf + index, gestureList[i].label, 16);
            index += 16;
            contentlen += 16;
            CodeUtils::getInstance().uint32ToBb(gestureList[i].mId, buf + index);
            index += 4;
            contentlen += 4;
            CodeUtils::getInstance().uint16ToBb(gestureList[i].mTop, buf + index);
            index += 2;
            contentlen += 2;
            CodeUtils::getInstance().uint16ToBb(gestureList[i].mBottom, buf + index);
            index += 2;
            contentlen += 2;
            CodeUtils::getInstance().uint16ToBb(gestureList[i].mLeft, buf + index);
            index += 2;
            contentlen += 2;
            CodeUtils::getInstance().uint16ToBb(gestureList[i].mRight, buf + index);
            index += 2;
            contentlen += 2;
            CodeUtils::getInstance().uint32ToBb(gestureList[i].mScore, buf + index);
            index += 4;
            contentlen += 4;
        }
    }

    /* 把真实的长度塞进去 */
    setDataLen(contentlen);
    CodeUtils::getInstance().uint32ToBb(contentlen, buf + 2);

    ret = index;
    return ret;
}

void DetectionInfo_Gesture::addObjectToList(DetectionInfo_Gesture_object_ &object) {
    gestureList.push_back(object);
    gestureListSize = gestureList.size();
}

uint8_t DetectionInfo_Gesture::getCurCameraId() const {
    return curCameraId;
}

void DetectionInfo_Gesture::setCurCameraId(uint8_t curCameraId) {
    DetectionInfo_Gesture::curCameraId = curCameraId;
}

uint8_t DetectionInfo_Gesture::getGestureListSize() const {
    return gestureListSize;
}

void DetectionInfo_Gesture::setGestureListSize(uint8_t gestureListSize) {
    DetectionInfo_Gesture::gestureListSize = gestureListSize;
}
