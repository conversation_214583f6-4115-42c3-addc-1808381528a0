//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/23.
//

#ifndef VIS_G3_SOFTWARE_GETG3MP4FILELISTRESULT_H
#define VIS_G3_SOFTWARE_GETG3MP4FILELISTRESULT_H

#include "PCConfigureDataPacket.h"
#include <vector>

class GetG3Mp4FileListResult : public PCConfigureDataPacket {
public:
    GetG3Mp4FileListResult();

    int toCode(uint8_t *buf, int len) override;

    uint16_t getFileArraysSize() const;

    void setFileArraysSize(uint16_t fileArraysSize);

    const std::vector<PCConfigureDataPacket::MP4FileInfo> &getFileInfoList() const;

    void setFileInfoList(const std::vector<PCConfigureDataPacket::MP4FileInfo> &fileInfoList);

    uint8_t getResult() const;

    void setResult(uint8_t result);

private:
    uint8_t result;
    uint16_t fileArraysSize = 0;
    std::vector<PCConfigureDataPacket::MP4FileInfo> fileInfoList;

};


#endif //VIS_G3_SOFTWARE_GETG3MP4FILELISTRESULT_H
