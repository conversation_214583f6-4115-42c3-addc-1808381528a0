//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/15.
//

#include "RespondGetG3UTCTimeResult.h"

RespondGetG3UTCTimeResult::RespondGetG3UTCTimeResult() {
    setMsgId(MSGID_RESPOND_GET_G3_UTC_TIME_RESULT);
}

int RespondGetG3UTCTimeResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 先随便塞个长度 */
    int realContent = 0;
    setContentLen(0);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 塞结果 */
    buf[index] = getResult();
    index++;
    realContent++;
    /* 塞序BCD格式UTC时间长度 */
    (void) memcpy(buf + index, bcdTime, sizeof(bcdTime));
    index += sizeof(bcdTime);
    realContent += sizeof(bcdTime);
    /* 塞进去真正得长度 */
    setContentLen(realContent);
    CodeUtils::getInstance().int32ToBb(realContent, buf + 6);
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

uint8_t RespondGetG3UTCTimeResult::getResult() const {
    return result;
}

void RespondGetG3UTCTimeResult::setResult(uint8_t result) {
    RespondGetG3UTCTimeResult::result = result;
}

const uint8_t *RespondGetG3UTCTimeResult::getBcdTime() const {
    return bcdTime;
}

void RespondGetG3UTCTimeResult::setBcdTime(uint8_t *utctime) {
    (void) memcpy(bcdTime, utctime, sizeof(bcdTime));

}
