//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/31.
//

#include "StartG3DeviceUpgradeResult.h"

StartG3DeviceUpgradeResult::StartG3DeviceUpgradeResult() {
    setMsgId(MSGID_RESPOND_START_UPGRADE_RESULT);
}

int StartG3DeviceUpgradeResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    setContentLen(4);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 再塞G3Id */
    buf[index] = getG3Id();
    index++;
    /* 再塞结果 */
    buf[index] = getResult();
    index++;
    /* 再塞端口 */
    CodeUtils::getInstance().int16ToBb(getTcpPort(), buf + index);
    index += 2;
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

uint8_t StartG3DeviceUpgradeResult::getG3Id() const {
    return g3Id;
}

void StartG3DeviceUpgradeResult::setG3Id(uint8_t g3Id) {
    StartG3DeviceUpgradeResult::g3Id = g3Id;
}

uint8_t StartG3DeviceUpgradeResult::getResult() const {
    return result;
}

void StartG3DeviceUpgradeResult::setResult(uint8_t result) {
    StartG3DeviceUpgradeResult::result = result;
}

uint16_t StartG3DeviceUpgradeResult::getTcpPort() const {
    return tcpPort;
}

void StartG3DeviceUpgradeResult::setTcpPort(uint16_t tcpPort) {
    StartG3DeviceUpgradeResult::tcpPort = tcpPort;
}
