//
// Created by z<PERSON>gqiuxu on 2022/3/9.
//

#include "ConfigureMessageEncoder.h"

uint32_t ConfigureMessageEncoder::getFlowId() {
    if(flowId < 0xFFFFFFFF){
        flowId = flowId + 1;
    }else{
        flowId = 0;
    }
    return flowId;
}

int ConfigureMessageEncoder::generateUniversalAnswerCode(uint8_t *buf, int len,
                                                         PCConfigureDataPacket &pcConfigureDataPacket, int result) {
    int ret = -1;
    UniversalAnswer universalAnswer;
    universalAnswer.setFlowdId(getFlowId());
    universalAnswer.setQFlowId(pcConfigureDataPacket.getFlowdId());
    universalAnswer.setQMsgId(pcConfigureDataPacket.getMsgId());
    universalAnswer.setResult(result);
    ret = universalAnswer.toCode(buf, len);
    return ret;
}


int ConfigureMessageEncoder::generateRespondG3ConfigCode(uint8_t *buf, int len, char *configStr, int strLen) {
    int ret = -1;
    RespondG3Config respondG3Config;
    respondG3Config.setFlowdId(getFlowId());
    respondG3Config.setConfigStr(configStr, strLen);
    ret = respondG3Config.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateStartUDPRealviewResultCode(uint8_t *buf, int len, uint8_t cameraId, uint8_t result,
                                                                uint16_t port) {
    int ret = -1;
    StartUDPRealviewResult startUdpRealviewResult;
    startUdpRealviewResult.setFlowdId(getFlowId());
    startUdpRealviewResult.setCameraId(cameraId);
    startUdpRealviewResult.setResult(result);
    startUdpRealviewResult.setPort(port);
    ret = startUdpRealviewResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateDetectionInfo(uint8_t *buf, int len, DetectionInfo &detectionInfo) {
    int ret = -1;
    detectionInfo.setFlowdId(getFlowId());
    ret = detectionInfo.toCode(buf, len);
    return ret;
}

int
ConfigureMessageEncoder::generateDetectionAlarmEvent(uint8_t *buf, int len, DetectionAlarmEvent &detectionAlarmEvent) {
    int ret = -1;
    detectionAlarmEvent.setFlowdId(getFlowId());
    ret = detectionAlarmEvent.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateStartG3DeviceUpgradeResult(uint8_t *buf, int len,
                                                                StartG3DeviceUpgradeResult &startG3DeviceUpgradeResult) {
    int ret = -1;
    startG3DeviceUpgradeResult.setFlowdId(getFlowId());
    ret = startG3DeviceUpgradeResult.toCode(buf, len);
    return ret;
}

int
ConfigureMessageEncoder::generateG3DeviceUpgradeResult(uint8_t *buf, int len, G3DeviceUpgradeResult &upgradeResult) {
    int ret = -1;
    upgradeResult.setFlowdId(getFlowId());
    ret = upgradeResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateG3AndG4DeviceVersion(uint8_t *buf, int len,
                                                          RespondG3AndG4DevicesVersion &g3AndG4DevicesVersion) {
    int ret = -1;
    g3AndG4DevicesVersion.setFlowdId(getFlowId());
    ret = g3AndG4DevicesVersion.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateGetCPUSerialNumberResult(uint8_t *buf, int len,
                                                              RespondGetCPUSerialNumResult &getCpuSerialNumResult) {
    int ret = -1;
    getCpuSerialNumResult.setFlowdId(getFlowId());
    ret = getCpuSerialNumResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateSetG3UUIDResult(uint8_t *buf, int len, RespondSetUUIDResult &setUuidResult) {
    int ret = -1;
    setUuidResult.setFlowdId(getFlowId());
    ret = setUuidResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateGetG3UUIDResult(uint8_t *buf, int len, RespondGetUUIDResult &getUuidResult) {
    int ret = -1;
    getUuidResult.setFlowdId(getFlowId());
    ret = getUuidResult.toCode(buf, len);
    return ret;
}

int
ConfigureMessageEncoder::generateGetG3UTCTime(uint8_t *buf, int len, RespondGetG3UTCTimeResult &getG3UtcTimeResult) {
    int ret = -1;
    getG3UtcTimeResult.setFlowdId(getFlowId());
    ret = getG3UtcTimeResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateGetG3Mp4FileListResult(uint8_t *buf, int len,
                                                            GetG3Mp4FileListResult &getG3Mp4FileListResult) {
    int ret = -1;
    getG3Mp4FileListResult.setFlowdId(getFlowId());
    ret = getG3Mp4FileListResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateG3Mp4FileOptResult(uint8_t *buf, int len, G3Mp4FileOptResult &g3Mp4FileOptResult) {
    int ret = -1;
    g3Mp4FileOptResult.setFlowdId(getFlowId());
    ret = g3Mp4FileOptResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateStartG3FileDownloadResult(uint8_t *buf, int len,
                                                               StartG3FileDownloadResult &g3FileDownloadResult) {
    int ret = -1;
    g3FileDownloadResult.setFlowdId(getFlowId());
    ret = g3FileDownloadResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateGetFunctionLockInfoResult(uint8_t *buf, int len,
                                                               RespondGetFunctionLockInfoResult &getFunctionLockInfoResult) {
    int ret = -1;
    getFunctionLockInfoResult.setFlowdId(getFlowId());
    ret = getFunctionLockInfoResult.toCode(buf, len);
    return ret;
}

int
ConfigureMessageEncoder::generateStartUDPRealviewResultSeparate(uint8_t *buf, int len, uint8_t cameraId, uint8_t result,
                                                                uint16_t port) {
    int ret = -1;
    StartUDPRealviewResult_Separate startUdpRealviewResult;
    startUdpRealviewResult.setFlowdId(getFlowId());
    startUdpRealviewResult.setCameraId(cameraId);
    startUdpRealviewResult.setResult(result);
    startUdpRealviewResult.setPort(port);
    ret = startUdpRealviewResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateSetPeripheralDisplayParamsResult(uint8_t *buf, int len,
                                                                      SetPeripheralDisplayParamsResult &setPeripheralDisplayParamsResult) {
    int ret = -1;
    setPeripheralDisplayParamsResult.setFlowdId(getFlowId());
    ret = setPeripheralDisplayParamsResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateGetMRV220LogFileListResult(uint8_t *buf, int len,
                                                                GetMRV220LogFileListResult &getMrv220LogFileListResult) {
    int ret = -1;
    getMrv220LogFileListResult.setFlowdId(getFlowId());
    ret = getMrv220LogFileListResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateStartMRV220LogFileDownloadResult(uint8_t *buf, int len,
                                                                      StartMRV220LogFileDownloadResult &startMrv220LogFileDownloadResult) {
    int ret = -1;
    startMrv220LogFileDownloadResult.setFlowdId(getFlowId());
    ret = startMrv220LogFileDownloadResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateRespondMultimediaFileEncryptionKey(uint8_t *buf, int len,
                                                                        RespondMultimediaFileEncryptionKey &respondMultimediaFileEncryptionKey) {
    int ret = -1;
    respondMultimediaFileEncryptionKey.setFlowdId(getFlowId());
    ret = respondMultimediaFileEncryptionKey.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateRespondAlarmSoundFileConf(uint8_t *buf, int len,
                                                               RespondAlarmSoundFileConf &respondAlarmSoundFileConf) {
    int ret = -1;
    respondAlarmSoundFileConf.setFlowdId(getFlowId());
    ret = respondAlarmSoundFileConf.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateRespondGetCameraInputTypeFilterConfigResult(uint8_t *buf, int len,
                                                                                 RespondGetCameraInputTypeFilterConfigResult &respondGetCameraInputTypeFilterConfigResult) {
    int ret = -1;
    respondGetCameraInputTypeFilterConfigResult.setFlowdId(getFlowId());
    ret = respondGetCameraInputTypeFilterConfigResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateRespondGetCameraReduceEffectInfoResult(uint8_t *buf, int len,
                                                                            RespondGetCameraReduceEffectInfoResult &getCameraReduceEffectInfoResult) {
    int ret = -1;
    getCameraReduceEffectInfoResult.setFlowdId(getFlowId());
    ret = getCameraReduceEffectInfoResult.toCode(buf, len);
    return ret;
}

int ConfigureMessageEncoder::generateTCPRealviewOptResult(uint8_t *buf, int len, TCPRealviewOptResult result) {
    int ret = -1;
    result.setFlowdId(getFlowId());
    ret = result.toCode(buf, len);
    return ret;
}
