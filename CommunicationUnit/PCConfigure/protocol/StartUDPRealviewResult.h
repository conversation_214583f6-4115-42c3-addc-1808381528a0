//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/11.
//

#ifndef VIS_G3_SOFTWARE_STARTUDPREALVIEWRESULT_H
#define VIS_G3_SOFTWARE_STARTUDPREALVIEWRESULT_H

#include "PCConfigureDataPacket.h"

class StartUDPRealviewResult : public PCConfigureDataPacket {
public:
    StartUDPRealviewResult();

    int toCode(uint8_t *buf, int len) override;

    int decode(uint8_t *buf, int len) override;

    uint8_t getCameraId() const;

    void setCameraId(uint8_t cameraId);

    uint8_t getResult() const;

    void setResult(uint8_t result);

    uint16_t getPort() const;

    void setPort(uint16_t port);

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

private:
    /* G3ID */
    uint8_t g3Id = 0;
    /* 相机ID */
    uint8_t cameraId;
    /* 结果 */
    uint8_t result;
    /* UDP端口 */
    uint16_t port;


};


#endif //VIS_G3_SOFTWARE_STARTUDPREALVIEWRESULT_H
