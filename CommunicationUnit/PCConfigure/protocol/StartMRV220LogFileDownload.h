//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/12/4.
//

#ifndef VIS_G3_SOFTWARE_STARTMRV220LOGFILEDOWNLOAD_H
#define VIS_G3_SOFTWARE_STARTMRV220LOGFILEDOWNLOAD_H
#include "PCConfigureDataPacket.h"

class StartMRV220LogFileDownload : public PCConfigureDataPacket {
public:
    StartMRV220LogFileDownload();

    int decode(uint8_t *buf, int len) override;

    const MRV220LogFileInfo &getFileInfo() const;

    void setFileInfo(const MRV220LogFileInfo &fileInfo);

private:
    PCConfigureDataPacket::MRV220LogFileInfo fileInfo;
};


#endif //VIS_G3_SOFTWARE_STARTMRV220LOGFILEDOWNLOAD_H
