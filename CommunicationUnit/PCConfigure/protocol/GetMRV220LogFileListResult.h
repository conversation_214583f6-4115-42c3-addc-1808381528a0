//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/12/4.
//

#ifndef VIS_G3_SOFTWARE_GETMRV220LOGFILELISTRESULT_H
#define VIS_G3_SOFTWARE_GETMRV220LOGFILELISTRESULT_H

#include "PCConfigureDataPacket.h"
#include <vector>
class GetMRV220LogFileListResult : public PCConfigureDataPacket {
public:
    GetMRV220LogFileListResult();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getResult() const;

    void setResult(uint8_t result);

    uint16_t getFileArraysSize() const;

    void setFileArraysSize(uint16_t fileArraysSize);

    const std::vector<PCConfigureDataPacket::MRV220LogFileInfo> &getFileInfoList() const;

    void setFileInfoList(const std::vector<PCConfigureDataPacket::MRV220LogFileInfo> &fileInfoList);

private:
    uint8_t result;
    uint16_t fileArraysSize = 0;
    std::vector<PCConfigureDataPacket::MRV220LogFileInfo> fileInfoList;
};


#endif //VIS_G3_SOFTWARE_GETMRV220LOGFILELISTRESULT_H
