//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/13.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONINFO_OBJECTINFO_H
#define VIS_G3_SOFTWARE_DETECTIONINFO_OBJECTINFO_H


#include <vector>
#include <CodeUtils.h>
#include "DetectionInfo_DataItem.h"
#include "G3_Configuration.h"
#include "tdShareDefine.h"
#include "G3DetectionDefind.h"

struct DetectionInfo_ObjectInfo_object_ {
    /* 类型 */
    char label[16];
    /* id号 */
    uint32_t mId;
    /* 识别框的上 */
    uint32_t mTop;
    /* 识别框的下 */
    uint32_t mBottom;
    /* 识别框的左 */
    uint32_t mLeft;
    /* 识别框的右 */
    uint32_t mRight;
    /* X轴距离 */
    uint32_t mXDistance;
    /* Y轴距离 */
    uint32_t mYDistance;
    /* 碰撞时间  ÷10之后才是正确值   单位：s */
    uint32_t mHeadway;
    /* 物体宽度 */
    uint32_t mRealWidth;
    /* 物体高度 */
    uint32_t mRealHeight;
    /* X轴速度 */
    uint32_t mXVelocity;
    /* Y轴速度 */
    uint32_t mYVelocity;
    /* 趋势 */
    uint32_t mTrend;
    /* 得分   ÷100之后才是正确值 */
    uint32_t mScore;
    /* 是否影子	BYTE	0：不是影子  1：是影子 */
    uint8_t shadow = 0;
    /* mMaxScore（最大得分）	uint16_t	÷100之后才是正确值 */
    uint16_t mMaxScore = 0;
    /* mMinScore（最小得分）	uint16_t	÷100之后才是正确值 */
    uint16_t mMinScore = 0;
    /* 预留字节 */
    uint8_t reserved[2] = {0x00};
};

class DetectionInfo_ObjectInfo : public DetectionInfo_DataItem {
public:


    DetectionInfo_ObjectInfo();

    ~DetectionInfo_ObjectInfo();

    int toCode(uint8_t *buf, int len) override;


    void addObjectToList(DetectionInfo_ObjectInfo_object_ &object);


    uint8_t getObjectListSize() const;

    void setObjectListSize(uint8_t objectListSize);

    int32_t getEventCode() const;

    void setEventCode(int32_t eventCode);

    uint8_t getHasCamcoverd() const;

    void setHasCamcoverd(uint8_t hasCamcoverd);

    uint8_t getCameraId() const;

    void setCameraId(uint8_t cameraId);

    const std::vector<DetectionInfo_ObjectInfo_object_> &getObjectList() const;


private:

    /* object_数组的长度 */
    uint8_t objectListSize = 0;
    /* object_数组 */
    std::vector<DetectionInfo_ObjectInfo_object_> objectList;
    /* 事件码 */
    int32_t eventCode;
    /* 是否遮挡     0:false   1:true */
    uint8_t hasCamcoverd = 0;
    /* 镜头ID */
    uint8_t cameraId = -1;




};


#endif //VIS_G3_SOFTWARE_DETECTIONINFO_OBJECTINFO_H
