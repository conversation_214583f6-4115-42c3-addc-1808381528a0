//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/15.
//

#include <cstdio>
#include "DetectionInfo_SystemInfo.h"

DetectionInfo_SystemInfo::DetectionInfo_SystemInfo() {
    setDataId(DATAID_SYSTEMINFO);
}

DetectionInfo_SystemInfo::~DetectionInfo_SystemInfo() {

}

int DetectionInfo_SystemInfo::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度(随便塞一个) */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index += 4;
    int contentlen = 0;
    /* 再塞speed */
    CodeUtils::getInstance().uint16ToBb(speed, buf + index);
    index += 2;
    contentlen += 2;
    /* 再塞bypass状态 */
    buf[index] = bypassInfo;
    index++;
    contentlen++;
    /* 再塞镜头遮挡状态 */
    memcpy(buf + index, &cameraCoverStatus, 1);
    index ++;
    contentlen++;
    /* 再塞最大限速 */
    memcpy(buf + index, &speedLimit_max, 1);
    index ++;
    contentlen++;
    /* 再塞最小限速 */
    memcpy(buf + index, &speedLimit_min, 1);
    index ++;
    contentlen++;
    /* 塞发动机转速 */
    CodeUtils::getInstance().uint16ToBb(engineSpeed,buf + index);
    index += 2;
    contentlen += 2;
    /* 最后塞保留字节 */
    memcpy(buf + index, reserved, sizeof(reserved));
    index += sizeof(reserved);
    contentlen += sizeof(reserved);
    /* 把真实的长度塞进去 */
    setDataLen(contentlen);
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + 2);

    ret = index;
    return ret;
}

uint16_t DetectionInfo_SystemInfo::getSpeed() const {
    return speed;
}

void DetectionInfo_SystemInfo::setSpeed(uint16_t speed) {
    DetectionInfo_SystemInfo::speed = speed;
}

uint8_t DetectionInfo_SystemInfo::getBypassInfo() const {
    return bypassInfo;
}

void DetectionInfo_SystemInfo::setBypassInfo(uint8_t bypassInfo) {
    DetectionInfo_SystemInfo::bypassInfo = bypassInfo;
}

const uint8_t *DetectionInfo_SystemInfo::getReserved() const {
    return reserved;
}

const DetectionInfo_SystemInfo::DetectionInfo_SystemInfo_CameraCoverStatus &
DetectionInfo_SystemInfo::getCameraCoverStatus() const {
    return cameraCoverStatus;
}

void DetectionInfo_SystemInfo::setCameraCoverStatus(
        const DetectionInfo_SystemInfo::DetectionInfo_SystemInfo_CameraCoverStatus &cameraCoverStatus) {
    DetectionInfo_SystemInfo::cameraCoverStatus = cameraCoverStatus;
}

uint8_t DetectionInfo_SystemInfo::getSpeedLimitMax() const {
    return speedLimit_max;
}

void DetectionInfo_SystemInfo::setSpeedLimitMax(uint8_t speedLimitMax) {
    speedLimit_max = speedLimitMax;
}

uint8_t DetectionInfo_SystemInfo::getSpeedLimitMin() const {
    return speedLimit_min;
}

void DetectionInfo_SystemInfo::setSpeedLimitMin(uint8_t speedLimitMin) {
    speedLimit_min = speedLimitMin;
}

uint16_t DetectionInfo_SystemInfo::getEngineSpeed() const {
    return engineSpeed;
}

void DetectionInfo_SystemInfo::setEngineSpeed(uint16_t engineSpeed) {
    DetectionInfo_SystemInfo::engineSpeed = engineSpeed;
}
