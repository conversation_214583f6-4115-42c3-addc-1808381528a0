//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//

#include "DetectionAlarmEvent.h"

DetectionAlarmEvent::DetectionAlarmEvent() {
    setMsgId(MSGID_OUTPUT_ALARM_EVENT);
}

DetectionAlarmEvent::~DetectionAlarmEvent() {

}

int DetectionAlarmEvent::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;

    int mContentLen = 0;

    /* 先随便塞个内容长度 */
    setContentLen(mContentLen);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 再塞G3Id */
    buf[index] = getG3Id();
    index++;
    mContentLen++;
    /* 再塞信息项总数 */
    infoListSize = dataItemList_GeneralInfo.size() + dataItemList_BSD.size() + dataItemList_ADAS.size() +
                   dataItemList_DSM.size() + dataItemList_r151.size() + dataItemList_r158.size() + dataItemList_r159.size();
    buf[index] = infoListSize;
    index++;
    mContentLen++;
    /* 再塞所有信息项的内容 */
    if (infoListSize > 0) {
        for (std::size_t i = 0; i < dataItemList_GeneralInfo.size(); i++) {
            int mItemLen = dataItemList_GeneralInfo[i].toCode(buf + index, len - index);
            index += mItemLen;
            mContentLen += mItemLen;
        }

        for (std::size_t i = 0; i < dataItemList_BSD.size(); i++) {
            int mItemLen = dataItemList_BSD[i].toCode(buf + index, len - index);
            index += mItemLen;
            mContentLen += mItemLen;
        }

        for (std::size_t i = 0; i < dataItemList_ADAS.size(); i++) {
            int mItemLen = dataItemList_ADAS[i].toCode(buf + index, len - index);
            index += mItemLen;
            mContentLen += mItemLen;
        }

        for (std::size_t i = 0; i < dataItemList_DSM.size(); i++) {
            int mItemLen = dataItemList_DSM[i].toCode(buf + index, len - index);
            index += mItemLen;
            mContentLen += mItemLen;
        }

        for (std::size_t i = 0; i < dataItemList_r151.size(); i++) {
            int mItemLen = dataItemList_r151[i].toCode(buf + index, len - index);
            index += mItemLen;
            mContentLen += mItemLen;
        }

        for (std::size_t i = 0; i < dataItemList_r158.size(); i++) {
            int mItemLen = dataItemList_r158[i].toCode(buf + index, len - index);
            index += mItemLen;
            mContentLen += mItemLen;
        }

        for (std::size_t i = 0; i < dataItemList_r159.size(); i++) {
            int mItemLen = dataItemList_r159[i].toCode(buf + index, len - index);
            index += mItemLen;
            mContentLen += mItemLen;
        }
    }
    /* 再把正确的长度塞进去 */
    setContentLen(mContentLen);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + 6);

    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

int DetectionAlarmEvent::addBSDEvent(DetectionAlarmEvent_BSDEvent &detectionAlarmEventBsdEvent) {
    dataItemList_BSD.push_back(detectionAlarmEventBsdEvent);
    return 0;
}

int DetectionAlarmEvent::addGeneralInfo(DetectionAlarmEvent_GeneralInfo &detectionAlarmEventGeneralInfo) {
    dataItemList_GeneralInfo.push_back(detectionAlarmEventGeneralInfo);
    return 0;
}

int DetectionAlarmEvent::addADASEvent(DetectionAlarmEvent_ADASEvent &detectionAlarmEventAdasEvent) {
    dataItemList_ADAS.push_back(detectionAlarmEventAdasEvent);
    return 0;
}

uint8_t DetectionAlarmEvent::getG3Id() const {
    return g3Id;
}

void DetectionAlarmEvent::setG3Id(uint8_t g3Id) {
    DetectionAlarmEvent::g3Id = g3Id;
}

int DetectionAlarmEvent::addDSMEvent(DetectionAlarmEvent_DSMEvent &detectionAlarmEventDSMEvent) {
    dataItemList_DSM.push_back(detectionAlarmEventDSMEvent);

    return 0;
}

int DetectionAlarmEvent::addR151Event(DetectionAlarmEvent_R151Event &detectionAlarmEventR151Event) {
    dataItemList_r151.push_back(detectionAlarmEventR151Event);
    return 0;
}

int DetectionAlarmEvent::addR158Event(DetectionAlarmEvent_R158Event &detectionAlarmEventR158Event) {
    dataItemList_r158.push_back(detectionAlarmEventR158Event);
    return 0;
}

int DetectionAlarmEvent::addR159Event(DetectionAlarmEvent_R159Event &detectionAlarmEventR159Event) {
    dataItemList_r159.push_back(detectionAlarmEventR159Event);
    return 0;
}
