//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/10.
//

#include "StartUDPRealview.h"

uint8_t StartUDPRealview::getCameraId() const {
    return cameraId;
}

void StartUDPRealview::setCameraId(uint8_t cameraId) {
    StartUDPRealview::cameraId = cameraId;
}

int StartUDPRealview::toCode(uint8_t *buf, int len) {
    int ret = -1;
    if (len >= (9 + 5)) {
        int index = 6;
        setContentLen(7);
        CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
        index += 4;
        /* 塞G3ID */
        buf[index] = getG3Id();
        index++;
        /* 再塞相机ID */
        buf[index] = getCameraId();
        index++;
        /* 再塞操作类型 */
        buf[index] = getOpt();
        index++;
        /* 加上头尾那些东西 */
        ret = PCConfigureDataPacket::toCode(buf, len);
    }
    return ret;
}

int StartUDPRealview::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        setG3Id(buf[index]);
        index++;
        setCameraId(buf[index]);
        index++;
        setOpt(buf[index]);
        index++;
        ret = 0;
    }
    return ret;
}

StartUDPRealview::StartUDPRealview() {
    setMsgId(MSGID_START_UDP_REALVIEW);
}

uint8_t StartUDPRealview::getOpt() const {
    return opt;
}

void StartUDPRealview::setOpt(uint8_t opt) {
    StartUDPRealview::opt = opt;
}

uint8_t StartUDPRealview::getG3Id() const {
    return g3Id;
}

void StartUDPRealview::setG3Id(uint8_t g3Id) {
    StartUDPRealview::g3Id = g3Id;
}
