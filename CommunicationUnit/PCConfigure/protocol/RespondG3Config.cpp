//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/11.
//

#include <malloc.h>
#include "RespondG3Config.h"

RespondG3Config::RespondG3Config() {
    setMsgId(MSGID_RESPOND_G3_CONFIG);
}

uint32_t RespondG3Config::getConfigStrLen() const {
    return configStrLen;
}

void RespondG3Config::setConfigStrLen(uint32_t configStrLen) {
    RespondG3Config::configStrLen = configStrLen;
}


int RespondG3Config::decode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 解析长度 */
    setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
    index += 4;
    setG3Id(buf[index]);
    index++;
    /* 解析配置表的长度 */
    setConfigStrLen(CodeUtils::getInstance().BbToint32(buf + index));
    index += 4;
    /* 解析配置表的内容 */
    configStr = static_cast<char *>(malloc(getConfigStrLen()));
    memcpy(configStr, buf + index, getConfigStrLen());
    /* 解析除了内容之外的东西 */
    ret = PCConfigureDataPacket::decode(buf, len);
    return ret;
}

int RespondG3Config::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 先塞长度 */
    setContentLen(getConfigStrLen() + 6);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 再塞G3Id */
    buf[index] = getG3Id();
    index++;
    /* 再塞结果 */
    buf[index] = getResult();
    index++;
    /* 再塞配置表的长度 */
    CodeUtils::getInstance().int32ToBb(getConfigStrLen(), buf + index);
    index += 4;
    /* 再塞配置表 */
    memcpy(buf + index, configStr, getConfigStrLen());
    index += getConfigStrLen();
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

RespondG3Config::~RespondG3Config() {
    if (getConfigStrLen() > 0) {
        free(configStr);
    }
}

void RespondG3Config::setConfigStr(char *buf, int len) {

    if (len > 0) {
        if(getConfigStrLen() > 0){
            if(static_cast<int>(getConfigStrLen()) < len){
                free(configStr);
            }
            setConfigStrLen(len);
            configStr = static_cast<char *>(malloc(getConfigStrLen()));
            memset(configStr, 0x00, getConfigStrLen());
            memcpy(configStr, buf, getConfigStrLen());
        }else{
            setConfigStrLen(len);
            configStr = static_cast<char *>(malloc(getConfigStrLen()));
            memset(configStr, 0x00, getConfigStrLen());
            memcpy(configStr, buf, getConfigStrLen());
        }
    } else {
        setConfigStrLen(0);
    }

}

uint8_t RespondG3Config::getG3Id() const {
    return g3Id;
}

void RespondG3Config::setG3Id(uint8_t g3Id) {
    RespondG3Config::g3Id = g3Id;
}

char *RespondG3Config::getConfigStr() const {
    return configStr;
}

void RespondG3Config::setConfigStr1(char *configStr) {
    RespondG3Config::configStr = configStr;
}

uint8_t RespondG3Config::getResult() const {
    return result;
}

void RespondG3Config::setResult(uint8_t result) {
    RespondG3Config::result = result;
}
