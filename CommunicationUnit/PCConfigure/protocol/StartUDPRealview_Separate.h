//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/9.
//

#ifndef VIS_G3_SOFTWARE_STARTUDPREALVIEW_SEPARATE_H
#define VIS_G3_SOFTWARE_STARTUDPREALVIEW_SEPARATE_H

#include "PCConfigureDataPacket.h"
class StartUDPRealview_Separate : public PCConfigureDataPacket {
public:
    const static uint8_t OPT_CLOSE = 0x00;
    const static uint8_t OPT_OPEN = 0x01;



    StartUDPRealview_Separate();

    int decode(uint8_t *buf, int len) override;

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

    uint8_t getCameraId() const;

    void setCameraId(uint8_t cameraId);

    uint8_t getOpt() const;

    void setOpt(uint8_t opt);

    uint16_t getUdpPort() const;

    void setUdpPort(uint16_t udpPort);

    uint8_t getAddrStrLen() const;

    void setAddrStrLen(uint8_t addrStrLen);

    const std::string &getAddressStr() const;

    void setAddressStr(const std::string &addressStr);


private:
    /* G3ID */
    uint8_t g3Id = 0;
    /* 要操作实景的相机ID */
    uint8_t cameraId;
    /* 操作类型  0：关闭   1：开启  */
    uint8_t opt;
    /* UDP接收端口 */
    uint16_t udpPort;
    /* IP 地址String长度 */
    uint8_t addrStrLen;
    /* IP地址 */
    std::string addressStr;

};


#endif //VIS_G3_SOFTWARE_STARTUDPREALVIEW_SEPARATE_H
