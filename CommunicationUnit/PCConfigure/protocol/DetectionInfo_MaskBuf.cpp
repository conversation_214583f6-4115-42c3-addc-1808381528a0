//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/21.
//

#include "DetectionInfo_MaskBuf.h"
#include "CodeUtils.h"

DetectionInfo_MaskBuf::DetectionInfo_MaskBuf() {
    setDataId(DATAID_MASKBUF);
}

int DetectionInfo_MaskBuf::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index = index + 2;
    /* 再塞总长度(随便塞一个) */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index = index + 4;
    int contentlen = 0;
    /* 塞相机ID */
    buf[index] = cameraId;
    index = index + 1;
    /* 塞maskBuf对应的宽 */
    CodeUtils::getInstance().uint16ToBb(getMaskWidth(), buf + index);
    index = index + 2;
    /* 塞maskBuf对应的高 */
    CodeUtils::getInstance().uint16ToBb(getMaskHeight(), buf + index);
    index = index + 2;
    /* 再塞maskBuf的长度 */
    CodeUtils::getInstance().uint32ToBb(maskBufLen, buf + index);
    index = index + 4;
    /* 再塞maskBuf */
    if (maskBufLen > 0) {
        memcpy(buf + index, maskBuf, maskBufLen);
        index = index + maskBufLen;
    }

    /* 把真实的长度塞进去 */
    contentlen = index - 6;
    CodeUtils::getInstance().uint32ToBb(contentlen, buf + 2);

    ret = index;
    return ret;
}

uint8_t DetectionInfo_MaskBuf::getCameraId() const {
    return cameraId;
}

void DetectionInfo_MaskBuf::setCameraId(uint8_t cameraId) {
    DetectionInfo_MaskBuf::cameraId = cameraId;
}

uint16_t DetectionInfo_MaskBuf::getMaskWidth() const {
    return maskWidth;
}

void DetectionInfo_MaskBuf::setMaskWidth(uint16_t maskWidth) {
    DetectionInfo_MaskBuf::maskWidth = maskWidth;
}

uint16_t DetectionInfo_MaskBuf::getMaskHeight() const {
    return maskHeight;
}

void DetectionInfo_MaskBuf::setMaskHeight(uint16_t maskHeight) {
    DetectionInfo_MaskBuf::maskHeight = maskHeight;
}

uint32_t DetectionInfo_MaskBuf::getMaskBufLen() const {
    return maskBufLen;
}

void DetectionInfo_MaskBuf::setMaskBufLen(uint32_t maskBufLen) {
    DetectionInfo_MaskBuf::maskBufLen = maskBufLen;
}

const uint8_t *DetectionInfo_MaskBuf::getMaskBuf() const {
    return maskBuf;
}

void DetectionInfo_MaskBuf::setMaskBuf(const uint8_t *buf, const uint32_t len) {
    memcpy(maskBuf,buf,len);
}
