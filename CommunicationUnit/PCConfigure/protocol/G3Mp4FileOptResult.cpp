//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/2.
//

#include "G3Mp4FileOptResult.h"

G3Mp4FileOptResult::G3Mp4FileOptResult() {
    setMsgId(MSGID_RESPOND_G3_MP4_FILE_OPT_RESULT);
}

int G3Mp4FileOptResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 先随便写一个长度 */
    CodeUtils::getInstance().int32ToBb(0, buf + index);
    index = index + 4;
    int realLen = 0;
    /* 结果 */
    buf[index] = result;
    index++;
    realLen++;
    /* 文件信息数组长度 */
    failedFileListSize = failedFileList.size();
    CodeUtils::getInstance().uint16ToBb(failedFileListSize, buf + index);
    index = index + 2;
    realLen = realLen + 2;
//    printf("***************fileInfoList.size=%d \n",failedFileList.size());
    /* 文件信息数组 */
    if (failedFileListSize > 0) {
        for (int i = 0; i < failedFileListSize; i++) {
            /* 文件类型 */
            buf[index] = failedFileList[i].fileType;
            index = index + 1;
            realLen = realLen + 1;
            /* 报警目标类型 */
            CodeUtils::getInstance().uint16ToBb(0xff, buf + index);
            index = index + 2;
            realLen = realLen + 2;
            /* 文件状态 */
            buf[index] = failedFileList[i].fileStatus;
            index = index + 1;
            realLen = realLen + 1;
            /* 相机ID */
            buf[index] = failedFileList[i].cameraId;
            index = index + 1;
            realLen = realLen + 1;
            /* 录制时间 */
            memcpy(buf + index, failedFileList[i].bcdTime, sizeof(failedFileList[i].bcdTime));
            index = index + 7;
            realLen = realLen + 7;
            /* 文件名的长度 */
            buf[index] = failedFileList[i].fileNameLen;
            index = index + 1;
            realLen = realLen + 1;
            /* 文件名 */
            memcpy(buf + index, failedFileList[i].fileName, failedFileList[i].fileNameLen);
            index = index + failedFileList[i].fileNameLen;
            realLen = realLen + failedFileList[i].fileNameLen;
        }
    }
    /* 塞真实的长度 */
    setContentLen(realLen);
    CodeUtils::getInstance().int32ToBb(realLen, buf + 6);

    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

uint8_t G3Mp4FileOptResult::getResult() const {
    return result;
}

void G3Mp4FileOptResult::setResult(uint8_t result) {
    G3Mp4FileOptResult::result = result;
}

uint16_t G3Mp4FileOptResult::getFailedFileListSize() const {
    return failedFileListSize;
}

void G3Mp4FileOptResult::setFailedFileListSize(uint16_t failedFileListSize) {
    G3Mp4FileOptResult::failedFileListSize = failedFileListSize;
}

const std::vector<PCConfigureDataPacket::MP4FileInfo> &G3Mp4FileOptResult::getFailedFileList() const {
    return failedFileList;
}

void G3Mp4FileOptResult::setFailedFileList(const std::vector<PCConfigureDataPacket::MP4FileInfo> &failedFileList) {
    G3Mp4FileOptResult::failedFileList = failedFileList;
}
