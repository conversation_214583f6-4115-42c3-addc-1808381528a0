//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/15.
//

#include "DetectionInfo_SystemError.h"

DetectionInfo_SystemError::DetectionInfo_SystemError() {
    setDataId(DATAID_SYSTEMERROR);
    setDataLen(4);
}

DetectionInfo_SystemError::~DetectionInfo_SystemError() {

}

uint8_t DetectionInfo_SystemError::getTfCardError() const {
    return tfCardError;
}

void DetectionInfo_SystemError::setTfCardError(uint8_t tfCardError) {
    DetectionInfo_SystemError::tfCardError = tfCardError;
}

uint8_t DetectionInfo_SystemError::getHighTemperature() const {
    return highTemperature;
}

void DetectionInfo_SystemError::setHighTemperature(uint8_t highTemperature) {
    DetectionInfo_SystemError::highTemperature = highTemperature;
}

uint8_t DetectionInfo_SystemError::getAdasCameraError() const {
    return adasCameraError;
}

void DetectionInfo_SystemError::setAdasCameraError(uint8_t adasCameraError) {
    DetectionInfo_SystemError::adasCameraError = adasCameraError;
}

uint8_t DetectionInfo_SystemError::getBsdCameraErrorL() const {
    return bsdCameraError_L;
}

void DetectionInfo_SystemError::setBsdCameraErrorL(uint8_t bsdCameraErrorL) {
    bsdCameraError_L = bsdCameraErrorL;
}

uint8_t DetectionInfo_SystemError::getBsdCameraErrorR() const {
    return bsdCameraError_R;
}

void DetectionInfo_SystemError::setBsdCameraErrorR(uint8_t bsdCameraErrorR) {
    bsdCameraError_R = bsdCameraErrorR;
}

uint8_t DetectionInfo_SystemError::getDsmCameraError() const {
    return dsmCameraError;
}

void DetectionInfo_SystemError::setDsmCameraError(uint8_t dsmCameraError) {
    DetectionInfo_SystemError::dsmCameraError = dsmCameraError;
}

uint8_t DetectionInfo_SystemError::getAdasAlgorithmError() const {
    return adasAlgorithmError;
}

void DetectionInfo_SystemError::setAdasAlgorithmError(uint8_t adasAlgorithmError) {
    DetectionInfo_SystemError::adasAlgorithmError = adasAlgorithmError;
}

uint8_t DetectionInfo_SystemError::getBsdAlgorithmErrorL() const {
    return bsdAlgorithmError_L;
}

void DetectionInfo_SystemError::setBsdAlgorithmErrorL(uint8_t bsdAlgorithmErrorL) {
    bsdAlgorithmError_L = bsdAlgorithmErrorL;
}

uint8_t DetectionInfo_SystemError::getBsdAlgorithmErrorR() const {
    return bsdAlgorithmError_R;
}

void DetectionInfo_SystemError::setBsdAlgorithmErrorR(uint8_t bsdAlgorithmErrorR) {
    bsdAlgorithmError_R = bsdAlgorithmErrorR;
}

uint8_t DetectionInfo_SystemError::getDsmAlgorithmError() const {
    return dsmAlgorithmError;
}

void DetectionInfo_SystemError::setDsmAlgorithmError(uint8_t dsmAlgorithmError) {
    DetectionInfo_SystemError::dsmAlgorithmError = dsmAlgorithmError;
}

int DetectionInfo_SystemError::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0; //前面留三个字节用来放ID跟长度
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度*/
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index += 4;

    uint32_t systemInfo = 0x00000000;

    /* 塞TF卡状态 */
    if (tfCardError == 1) {
        systemInfo |= 0x01;
    }
    /* 塞高温报警 */
    if (highTemperature == 1) {
        systemInfo |= 0x02;
    }
    /* 塞ADAS摄像头 */
    if (adasCameraError == 1) {
        systemInfo |= 0x04;
    }

    /* 塞左侧摄像头 */
    if (bsdCameraError_L == 1) {
        systemInfo |= 0x08;
    }

    /* 塞右侧摄像头 */
    if (bsdCameraError_R == 1) {
        systemInfo |= 0x10;
    }

    /* 塞DSM摄像头 */
    if (dsmCameraError == 1) {
        systemInfo |= 0x20;
    }

    /* 塞ADAS算法 */
    if (adasAlgorithmError == 1) {
        systemInfo |= 0x40;
    }

    /* 塞 左侧算法 */
    if (bsdAlgorithmError_L == 1) {
        systemInfo |= 0x80;
    }

    /* 塞右侧算法 */
    if (bsdAlgorithmError_R == 1) {
        systemInfo |= 0x100;
    }

    /* 塞DSM算法 */
    if (dsmAlgorithmError == 1) {
        systemInfo |= 0x200;
    }

    /* 塞前侧摄像头 */
    if (forntCameraError == 1) {
        systemInfo |= 0x400;
    }

    /* 塞后侧摄像头 */
    if (backCameraError == 1) {
        systemInfo |= 0x800;
    }

    /* 塞前侧摄像头 */
    if (forntAlgorithmError == 1) {
        systemInfo |= 0x1000;
    }

    /* 塞后侧摄像头 */
    if (backAlgorithmError == 1) {
        systemInfo |= 0x2000;
    }

    /* 塞镜头1图像输入状态 */
    if (camera1ImgInputError == 1) {
        systemInfo |= 0x4000;
    }

    /* 塞镜头2图像输入状态 */
    if (camera2ImgInputError == 1) {
        systemInfo |= 0x8000;
    }

    /* 塞镜头1图像全黑状态 */
    if (camera1FullBlack == 1) {
        systemInfo |= 0x10000;
    }

    /* 塞镜头2图像全黑状态 */
    if (camera2FullBlack == 1) {
        systemInfo |= 0x20000;
    }

    /* 塞镜头3图像输入状态 */
    if (camera3ImgInputError == 1) {
        systemInfo |= 0x40000;
    }

    /* 塞镜头4图像输入状态 */
    if (camera4ImgInputError == 1) {
        systemInfo |= 0x80000;
    }
    /* 塞镜头3图像全黑状态 */
    if (camera3FullBlack == 1) {
        systemInfo |= 0x100000;
    }

    /* 塞镜头4图像全黑状态 */
    if (camera4FullBlack == 1) {
        systemInfo |= 0x200000;
    }

    CodeUtils::getInstance().uint32ToBb(systemInfo, buf + index);
    index += 4;

    ret = index;

    return ret;
}

uint8_t DetectionInfo_SystemError::getForntCameraError() const {
    return forntCameraError;
}

void DetectionInfo_SystemError::setForntCameraError(uint8_t forntCameraError) {
    DetectionInfo_SystemError::forntCameraError = forntCameraError;
}

uint8_t DetectionInfo_SystemError::getBackCameraError() const {
    return backCameraError;
}

void DetectionInfo_SystemError::setBackCameraError(uint8_t backCameraError) {
    DetectionInfo_SystemError::backCameraError = backCameraError;
}

uint8_t DetectionInfo_SystemError::getForntAlgorithmError() const {
    return forntAlgorithmError;
}

void DetectionInfo_SystemError::setForntAlgorithmError(uint8_t forntAlgorithmError) {
    DetectionInfo_SystemError::forntAlgorithmError = forntAlgorithmError;
}

uint8_t DetectionInfo_SystemError::getBackAlgorithmError() const {
    return backAlgorithmError;
}

void DetectionInfo_SystemError::setBackAlgorithmError(uint8_t backAlgorithmError) {
    DetectionInfo_SystemError::backAlgorithmError = backAlgorithmError;
}

uint8_t DetectionInfo_SystemError::getCamera1ImgInputError() const {
    return camera1ImgInputError;
}

void DetectionInfo_SystemError::setCamera1ImgInputError(uint8_t camera1ImgInputError) {
    DetectionInfo_SystemError::camera1ImgInputError = camera1ImgInputError;
}

uint8_t DetectionInfo_SystemError::getCamera2ImgInputError() const {
    return camera2ImgInputError;
}

void DetectionInfo_SystemError::setCamera2ImgInputError(uint8_t camera2ImgInputError) {
    DetectionInfo_SystemError::camera2ImgInputError = camera2ImgInputError;
}

uint8_t DetectionInfo_SystemError::getCamera1FullBlack() const {
    return camera1FullBlack;
}

void DetectionInfo_SystemError::setCamera1FullBlack(uint8_t camera1FullBlack) {
    DetectionInfo_SystemError::camera1FullBlack = camera1FullBlack;
}

uint8_t DetectionInfo_SystemError::getCamera2FullBlack() const {
    return camera2FullBlack;
}

void DetectionInfo_SystemError::setCamera2FullBlack(uint8_t camera2FullBlack) {
    DetectionInfo_SystemError::camera2FullBlack = camera2FullBlack;
}

uint8_t DetectionInfo_SystemError::getCamera3ImgInputError() const {
    return camera3ImgInputError;
}

void DetectionInfo_SystemError::setCamera3ImgInputError(uint8_t camera3ImgInputError) {
    DetectionInfo_SystemError::camera3ImgInputError = camera3ImgInputError;
}

uint8_t DetectionInfo_SystemError::getCamera4ImgInputError() const {
    return camera4ImgInputError;
}

void DetectionInfo_SystemError::setCamera4ImgInputError(uint8_t camera4ImgInputError) {
    DetectionInfo_SystemError::camera4ImgInputError = camera4ImgInputError;
}

uint8_t DetectionInfo_SystemError::getCamera3FullBlack() const {
    return camera3FullBlack;
}

void DetectionInfo_SystemError::setCamera3FullBlack(uint8_t camera3FullBlack) {
    DetectionInfo_SystemError::camera3FullBlack = camera3FullBlack;
}

uint8_t DetectionInfo_SystemError::getCamera4FullBlack() const {
    return camera4FullBlack;
}

void DetectionInfo_SystemError::setCamera4FullBlack(uint8_t camera4FullBlack) {
    DetectionInfo_SystemError::camera4FullBlack = camera4FullBlack;
}
