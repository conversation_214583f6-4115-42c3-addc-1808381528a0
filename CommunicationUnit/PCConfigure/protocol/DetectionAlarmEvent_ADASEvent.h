//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//

#ifndef VIS_G3_SOFTWARE_DETECTIONALARMEVENT_ADASEVENT_H
#define VIS_G3_SOFTWARE_DETECTIONALARMEVENT_ADASEVENT_H

#include "DetectionAlarmEvent_DataItem.h"
#include <CodeUtils.h>

/* ADAS故障信息 */
struct DetectionAlarmEvent_ADASEvent_faultInfo {
    /* 授权失败  通常指没有读到加密芯片 */
    bool authorization_required_fail: 1;
    /* ADAS软件致命错误 */
    bool adas_software_fatal_error: 1;
    /* 指针为空 */
    bool pointer_is_null: 1;
    /* 参数取值范围检查出错 */
    bool parameter_value_range_error: 1;
    /* 非法返回值 */
    bool illegal_return_value: 1;
    /* 静态数据校验错误 */
    bool static_data_check_error: 1;
    /* 配置数据版本等校验失败 */
    bool config_data_version_check_failed: 1;
    /* 神经网络结构检查失败 */
    bool neural_network_structure_check_failed: 1;
    /* 样本数据库版本检查失败 */
    bool sample_database_version_check_failed: 1;
    /* 预留 */
    int reserved: 23;
};

/* ADAS报警分级信息 */
struct DetectionAlarmEvent_ADASEvent_alarmlLevelInfo {
    /* 行人报警level1 */
    bool pdw_level1: 1;
    /* 行人报警level2 */
    bool pdw_level2: 1;
    /* 行人报警level3 */
    bool pdw_level3: 1;
    /* 行人报警level4 */
    bool pdw_level4: 1;

    /* 车辆headway报警level1 */
    bool fcw_hmw_level1: 1;
    /* 车辆headway报警level2 */
    bool fcw_hmw_level2: 1;
    /* 车辆headway报警level3 */
    bool fcw_hmw_level3: 1;
    /* 车辆headway报警level4 */
    bool fcw_hmw_level4: 1;


    /* 车辆ttc报警level1 */
    bool fcw_ttc_level1: 1;
    /* 车辆ttc报警level2 */
    bool fcw_ttc_level2: 1;
    /* 车辆ttc报警level3 */
    bool fcw_ttc_level3: 1;
    /* 车辆ttc报警level4 */
    bool fcw_ttc_level4: 1;

    /* 车距过近，虚拟保险杠报警 */
    bool fcw_vb: 1;
    /* 前车启动报警 */
    bool fcw_go: 1;

    /* 左车道虚线偏离报警 */
    bool ldw_left_dashed: 1;
    /* 右车道虚线偏离报警 */
    bool ldw_right_dashed: 1;
    /* 左车道实线偏离报警 */
    bool ldw_left_solid: 1;
    /* 右车道实线偏离报警 */
    bool ldw_right_solid: 1;

    /* 超速报警 */
    bool over_speed_alarm: 1;

    /* 摄像头遮挡报警 */
    bool camera_covered_alarm: 1;

    /* 预留 */
    int reserved: 12;
};

class DetectionAlarmEvent_ADASEvent : public DetectionAlarmEvent_DataItem {
public:

    /* 前向碰撞报警 */
    static const uint8_t EVENT_TYPE_FCW = 0x01;
    /* 车道偏离报警 */
    static const uint8_t EVENT_TYPE_LDW = 0x02;
    /* 车距过近报警 */
    static const uint8_t EVENT_TYPE_HMW = 0x03;
    /* 行人碰撞报警 */
    static const uint8_t EVENT_TYPE_PDW = 0x04;
    /* 频繁变道报警 */
    static const uint8_t EVENT_TYPE_FLCW = 0x05;
    /* 道路标识超限报警 */
    static const uint8_t EVENT_TYPE_RSOW = 0x06;
    /* 防溜车提醒 */
    static const uint8_t EVENT_TYPE_VB = 0x07;
    /* 前车启动提醒 */
    static const uint8_t EVENT_TYPE_GO = 0x08;
    /* 道路标志识别事件 */
    static const uint8_t EVENT_TYPE_RSD = 0x09;
    /* 主动抓拍事件 */
    static const uint8_t EVENT_TYPE_TM = 0x10;


    /* 偏离类型--左侧虚线偏离 */
    static const uint8_t LDW_TYPE_LEFT_DASHED = 0x01;
    /* 偏离类型--右侧虚线偏离 */
    static const uint8_t LDW_TYPE_RIGHT_DASHED = 0x02;
    /* 偏离类型--左侧实线偏离 */
    static const uint8_t LDW_TYPE_LEFT_SOLID = 0x03;
    /* 偏离类型--右侧实线偏离 */
    static const uint8_t LDW_TYPE_RIGHT_SOLID = 0x04;
    /* 偏离类型--双侧偏离 */
    static const uint8_t LDW_TYPE_RIGHT_ADN_LEFT = 0x05;


    DetectionAlarmEvent_ADASEvent();

    ~DetectionAlarmEvent_ADASEvent();

    int toCode(uint8_t *buf, int len) override;


    uint8_t getEventStatus() const;

    void setEventStatus(uint8_t eventStatus);

    uint8_t getEventType() const;

    void setEventType(uint8_t eventType);

    uint8_t getHeadway() const;

    void setHeadway(uint8_t headway);

    uint8_t getLdwType() const;

    void setLdwType(uint8_t ldwType);

    uint8_t getRoadSignsType() const;

    void setRoadSignsType(uint8_t roadSignsType);

    uint8_t getCameraId() const;

    void setCameraId(uint8_t cameraId);

    const DetectionAlarmEvent_ADASEvent_alarmlLevelInfo &getAlarmlLevelInfo() const;

    void setAlarmlLevelInfo(const DetectionAlarmEvent_ADASEvent_alarmlLevelInfo &alarmlLevelInfo);

    const DetectionAlarmEvent_ADASEvent_faultInfo &getFaultInfo() const;

    void setFaultInfo(const DetectionAlarmEvent_ADASEvent_faultInfo &faultInfo);

    uint8_t getIsShadow() const;

    void setIsShadow(uint8_t isShadow);

private:
    /* 标志状态	uint8_t	0x00：不可用   0x01：开始标志   0x02：结束标志  该字段仅适用于有开始和结束标志类型的报警或事件，报警类型或事件类型无开始和结束标志，则该位不可用，填入0x00即可 */
    uint8_t eventStatus = 0;
    /* 报警/事件类型 0x01：前向碰撞报警  0x02：车道偏离报警  0x03：车距过近报警  0x04：行人碰撞报警  0x05：频繁变道报警  0x06：道路标识超限报警  0x07：防溜车提醒  0x08：前车启动提醒  0x10：道路标志识别事件  0x11：主动抓拍事件  0x12~0x1F：用户自定义  */
    uint8_t eventType;
    /* 碰撞时间  单位100ms，范围0~100，仅报警类型为0x01、0x03和0x04时有效。 */
    uint8_t headway;
    /* 偏离类型  0x01：左侧虚线偏离  0x02：右侧虚线偏离  0x03：左侧实线偏离  0x04：右侧实线偏离  0x05：双侧偏离  仅报警类型为0x02时有效 */
    uint8_t ldwType;
    /* 道路标志识别类型   0x01：限速标志  0x02：限高标志  0x03：限重标志  仅报警类型为0x06和0x10时有效。 */
    uint8_t roadSignsType;
    /* 相机ID */
    uint8_t cameraId;
    /* ADAS报警分级信息 */
    DetectionAlarmEvent_ADASEvent_alarmlLevelInfo alarmlLevelInfo = {0};
    /* ADAS故障信息 */
    DetectionAlarmEvent_ADASEvent_faultInfo faultInfo = {0};
    /* 是否影子 */
    uint8_t isShadow = 0;

};


#endif //VIS_G3_SOFTWARE_DETECTIONALARMEVENT_ADASEVENT_H
