//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/10.
//

#include "Heartbeat.h"

Heartbeat::Heartbeat() {
    setMsgId(MSGID_HEARTBEAT);
}

int Heartbeat::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    setContentLen(1);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    buf[index] = g3DeviceId;
    index++;
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

int Heartbeat::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        g3DeviceId = buf[index];
        index++;
        ret = 0;
    }

    return ret;
}
