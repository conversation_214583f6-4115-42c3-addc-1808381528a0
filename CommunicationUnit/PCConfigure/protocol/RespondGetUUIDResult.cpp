//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/15.
//

#include "RespondGetUUIDResult.h"

RespondGetUUIDResult::RespondGetUUIDResult() {
    setMsgId(MSGID_RESPOND_GET_G3_AND_G4_UUID_RESULT);
}

int RespondGetUUIDResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 先随便塞个长度 */
    int realContent = 0;
    setContentLen(0);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 塞结果 */
    buf[index] = getResult();
    index++;
    realContent++;
    /* 塞序列号内容的长度 */
    CodeUtils::getInstance().int32ToBb(strLen, buf + index);
    index += 4;
    realContent += 4;
    /* 塞序列号内容 */
    if (strLen > 0) {
        memcpy(buf + index, curUUIDStr.c_str(), strLen);
        index += strLen;
        realContent += strLen;
    }


    /* 塞进去真正得长度 */
    setContentLen(realContent);
    CodeUtils::getInstance().int32ToBb(realContent, buf + 6);
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

uint8_t RespondGetUUIDResult::getResult() const {
    return result;
}

void RespondGetUUIDResult::setResult(uint8_t result) {
    RespondGetUUIDResult::result = result;
}

uint32_t RespondGetUUIDResult::getStrLen() const {
    return strLen;
}

void RespondGetUUIDResult::setStrLen(uint32_t strLen) {
    RespondGetUUIDResult::strLen = strLen;
}

const std::string &RespondGetUUIDResult::getCurUuidStr() const {
    return curUUIDStr;
}

void RespondGetUUIDResult::setCurUUID(const std::string &deviceUUID) {
    curUUIDStr.clear();
    curUUIDStr.append(deviceUUID);
    strLen = curUUIDStr.size();
}


