//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/9.
//

#include <cstdio>
#include "PCConfigureDataPacket.h"


uint8_t PCConfigureDataPacket::getMsgId() const {
    return msgId;
}

void PCConfigureDataPacket::setMsgId(uint8_t msgId) {
    PCConfigureDataPacket::msgId = msgId;
}

uint32_t PCConfigureDataPacket::getContentLen() const {
    return contentLen;
}

void PCConfigureDataPacket::setContentLen(uint32_t contentLen) {
    PCConfigureDataPacket::contentLen = contentLen;
}

int PCConfigureDataPacket::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞包头 */
    buf[index] = getHead();
    index++;
    /* 再塞流水号 */
    CodeUtils::getInstance().int32ToBb(getFlowdId(), buf + index);
    index += 4;
    /* 再塞命令字 */
    buf[index] = getMsgId();
    index++;
    /* 直接跳过内容 */
    index += (getContentLen() + 4);
    /* 塞校验字 */
    uint16_t curCheckCode = CodeUtils::getInstance().generateCrc16(buf + 1, index - 1);
    CodeUtils::getInstance().int16ToBb(curCheckCode, buf + index);
    index += 2;
//        printf("UniversalAnswer  curCheckCode = %d  \n",curCheckCode);
    /* 最后塞入包尾 */
    buf[index] = getTail();
    index++;
    ret = index;
    return ret;
}

int PCConfigureDataPacket::decode(uint8_t *buf, int len) {
    int ret = -1;
    /* 头是固定的，就不用解析了 */
//    setHead(buf[0]);
    /* 设置流水号 */
    setFlowdId(CodeUtils::getInstance().BbToint32(buf + 1));
    /* 命令字是固定的 也不用解析了 */
//    setMsgId(buf[5]);
    /* 内容长度 */
    setContentLen(CodeUtils::getInstance().BbToint32(buf + 6));

    /* 判断数据内容的长度对不对 */
    if (len != MIN_DATA_LEN + static_cast<int>(getContentLen())) {
        return -1;
    }

    /* 跳过内容  直接解析校验码 */
    setCheckCode(CodeUtils::getInstance().BbToint16((buf + 10 + getContentLen())));
    /* 尾巴是固定的，就不用解析了 */
//    setTail(buf[12+getContentLen()]);

    ret = 0;
    return ret;
}

uint16_t PCConfigureDataPacket::getCheckCode() const {
    return checkCode;
}

void PCConfigureDataPacket::setCheckCode(uint16_t checkCode) {
    PCConfigureDataPacket::checkCode = checkCode;
}

uint8_t PCConfigureDataPacket::getHead() const {
    return head;
}

void PCConfigureDataPacket::setHead(uint8_t head) {
    PCConfigureDataPacket::head = head;
}

uint8_t PCConfigureDataPacket::getTail() const {
    return tail;
}

void PCConfigureDataPacket::setTail(uint8_t tail) {
    PCConfigureDataPacket::tail = tail;
}

uint8_t *PCConfigureDataPacket::getContentBuf() const {
    return contentBuf;
}

void PCConfigureDataPacket::setContentBuf(uint8_t *contentBuf) {
    PCConfigureDataPacket::contentBuf = contentBuf;
}

uint32_t PCConfigureDataPacket::getFlowdId() const {
    return flowdId;
}

void PCConfigureDataPacket::setFlowdId(uint32_t flowdId) {
    PCConfigureDataPacket::flowdId = flowdId;
}
