//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//

#include "DetectionAlarmEvent_DataItem.h"

int DetectionAlarmEvent_DataItem::toCode(uint8_t *buf, int len) {
    return 0;
}

DetectionAlarmEvent_DataItem::DetectionAlarmEvent_DataItem() {

}

DetectionAlarmEvent_DataItem::~DetectionAlarmEvent_DataItem() {

}

uint16_t DetectionAlarmEvent_DataItem::getDataId() const {
    return dataId;
}

void DetectionAlarmEvent_DataItem::setDataId(uint16_t dataId) {
    DetectionAlarmEvent_DataItem::dataId = dataId;
}

uint32_t DetectionAlarmEvent_DataItem::getDataLen() const {
    return dataLen;
}

void DetectionAlarmEvent_DataItem::setDataLen(uint32_t dataLen) {
    DetectionAlarmEvent_DataItem::dataLen = dataLen;
}
