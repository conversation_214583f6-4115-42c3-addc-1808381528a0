//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/11.
//

#ifndef VIS_G3_SOFTWARE_QEQUESTG3CONFIG_H
#define VIS_G3_SOFTWARE_QEQUESTG3CONFIG_H

#include "PCConfigureDataPacket.h"

class QequestG3Config : public PCConfigureDataPacket {
public:
    QequestG3Config();

    int toCode(uint8_t *buf, int len) override;

    int decode(uint8_t *buf, int len) override;

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

private:
    /* G3ID */
    uint8_t g3Id = 0;
};


#endif //VIS_G3_SOFTWARE_QEQUESTG3CONFIG_H
