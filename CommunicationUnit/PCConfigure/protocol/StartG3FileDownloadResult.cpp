//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/5.
//

#include "StartG3FileDownloadResult.h"

StartG3FileDownloadResult::StartG3FileDownloadResult() {
    setMsgId(MSGID_RESPOND_START_G3_MP4_FILE_TRANSFER_RESULT);
}

int StartG3FileDownloadResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    setContentLen(23);
    CodeUtils::getInstance().uint32ToBb(getContentLen(), buf + index);
    index = index + 4;
    /* 塞结果 */
    buf[index] = result;
    index = index + 1;
    /* 再塞端口 */
    CodeUtils::getInstance().uint16ToBb(tcpPort, buf + index);
    index = index + 2;
    /* 再塞文件长度 */
    CodeUtils::getInstance().uint32ToBb(fileLen, buf + index);
    index = index + 4;
    /* 再塞文件MD5 */
    memcpy(buf + index, fileMD5, 16);
    index = index + 16;

    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

uint8_t StartG3FileDownloadResult::getResult() const {
    return result;
}

void StartG3FileDownloadResult::setResult(uint8_t result) {
    StartG3FileDownloadResult::result = result;
}

uint16_t StartG3FileDownloadResult::getTcpPort() const {
    return tcpPort;
}

void StartG3FileDownloadResult::setTcpPort(uint16_t tcpPort) {
    StartG3FileDownloadResult::tcpPort = tcpPort;
}

uint32_t StartG3FileDownloadResult::getFileLen() const {
    return fileLen;
}

void StartG3FileDownloadResult::setFileLen(uint32_t fileLen) {
    StartG3FileDownloadResult::fileLen = fileLen;
}

const uint8_t *StartG3FileDownloadResult::getFileMd5() const {
    return fileMD5;
}

void StartG3FileDownloadResult::setFileMD5(const uint8_t *md5) {
    memcpy(fileMD5, md5, sizeof(fileMD5));
}
