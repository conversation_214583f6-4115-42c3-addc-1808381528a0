//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/9.
//

#ifndef VIS_G3_SOFTWARE_PCCONFIGUREDATAPACKET_H
#define VIS_G3_SOFTWARE_PCCONFIGUREDATAPACKET_H


#include <cstdint>
#include <string>
#include "utils/CodeUtils.h"


class PCConfigureDataPacket {
public:

    /* 文件列表内容类型 */
    struct MP4FileListContent {
        /* 是否应该包含DVR文件 */
        bool hasDVR: 1;
        /* 是否应该包含左侧报警文件 */
        bool hasBSD_Left: 1;
        /* 是否应该包含右侧报警文件 */
        bool hasBSD_Right: 1;
        /* 是否应该包含前侧报警文件 */
        bool hasBSD_Forward: 1;
        /* 是否应该包含后侧报警文件 */
        bool hasBSD_Backward: 1;
        /* 是否应该包含R151报警文件 */
        bool hasR151: 1;
        /* 是否应该包含R158报警文件 */
        bool hasR158: 1;
        /* 是否应该包含R159报警文件 */
        bool hasR159: 1;
        /* 预留 */
        int reserve: 8;
    };

/* 报警目标类型 */
    struct WarFileDetectContent {
        /* 是否应该包含行人检测相关的报警 */
        bool hasDVR: 1;
        /* 是否应该包含车辆检测的报警 */
        bool hasBSD_Left: 1;
        /* 预留 */
        int reserve: 14;
    };

    enum MP4FILETYPE {
        /* DVR文件 */
        MP4FILETYPE_DVR = 0,
        /* 左侧报警文件 */
        MP4FILETYPE_BSD_LEFT,
        /* 右侧报警文件 */
        MP4FILETYPE_BSD_RIGHT,
        /* 前侧报警文件 */
        MP4FILETYPE_BSD_FORWARD,
        /* 后侧报警文件 */
        MP4FILETYPE_BSD_BACKWARD,
        /* R151报警文件 */
        MP4FILETYPE_R151,
        /* R158报警文件 */
        MP4FILETYPE_R158,
        /* R159报警文件 */
        MP4FILETYPE_R159,

        /* 未知类型的报警文件（已知的都在它前面定义，这个定义不允许给客户端） */
        MP4FILETYPE_UNKNOW,
    };

    enum MP4FILESTATUS {
        /* 正常未上锁 */
        MP4FILESTATUS_NORMAL_UNLOCK = 0,
        /* 正常已上锁 */
        MP4FILESTATUS_NORMAL_LOCK,
        /* 已损坏未上锁 */
        MP4FILESTATUS_DAMAGED_UNLOCK,
        /* 已损坏已上锁 */
        MP4FILESTATUS_DAMAGED_LOCK,
    };

    enum MP4FILELISTORDER {
        /* 从旧到新 */
        MP4FILELISTORDER_OLD_TO_NEW = 0,
        /* 从新到旧 */
        MP4FILELISTORDER_NEW_TO_OLD,
    };

    enum MP4FILELISTOPTTYPE {
        /* 删除 */
        MP4FILELISTOPTTYPE_DELETE = 0,
        /* 加锁 */
        MP4FILELISTOPTTYPE_LOCK,
        /* 解锁 */
        MP4FILELISTOPTTYPE_UNLOCK,
    };

    struct MP4FileInfo {
        uint8_t fileType;
        WarFileDetectContent detectContent;
        uint8_t fileStatus;
        uint8_t cameraId;
        uint8_t bcdTime[8] = {0x00};
        uint8_t fileNameLen = 0;
        uint8_t fileName[35] = {0x00};
    };


    enum DEVICEID{
        DEVICEID_G30 = 0,
        DEVICEID_G31,
        DEVICEID_C3,
        DEVICEID_MCU,
        DEVICEID_L3,
        DEVICEID_L4,
        DEVICEID_GPSBOX,
    };

    /**
     * MRV220的log文件的文件信息
     */
    struct MRV220LogFileInfo {
        uint8_t reserve[6] = {0x00};
        uint8_t fileNameLen = 0;
        uint8_t fileName[35] = {0x00};
    };

    /* 一包数据最小的长度因为是13个字节 */
    const static int MIN_DATA_LEN = 13;


    /* 通用应答 */
    const static int MSGID_UNIVERSALANSWER = 0x01;
    /* 心跳 */
    const static int MSGID_HEARTBEAT = 0x02;
    /* 开启UDP实景数据流 */
    const static int MSGID_START_UDP_REALVIEW = 0x03;
    /* 查询G3设备参数表 */
    const static int MSGID_QEQUEST_G3_CONFIG = 0x04;
    /* 设置G3设备参数表 */
    const static int MSGID_SET_G3_CONFIG = 0x05;
    /* 返回UDP开始结果和端口 */
    const static int MSGID_START_UDP_REALVIEW_RESULT = 0xA3;
    /* 返回G3设备参数表 */
    const static int MSGID_RESPOND_G3_CONFIG = 0xA4;
    /* G3主动传输算法识别信息 */
    const static int MSGID_OUTPUT_DETECTION_INFO = 0xA5;
    /* G3主动传输报警事件 */
    const static int MSGID_OUTPUT_ALARM_EVENT = 0xA6;
    /* 实时状态数据 */
    const static int MSGID_RESPOND_REALTIME_VEHICLE_STATUS = 0x06;
    /* 开始升级 */
    const static int MSGID_START_UPGRADE = 0x07;
    /* 开始升级的结果 */
    const static int MSGID_RESPOND_START_UPGRADE_RESULT = 0xA7;
    /* G3主动上报升级结果 */
    const static int MSGID_UPGRADE_RESULT = 0xA8;
    /* 设置G3当前得系统时间 */
    const static int MSGID_SET_G3_SYSTEM_TIME = 0x08;
    /* 获取设备版本号 */
    const static int MSGID_GET_G3_AND_G4_VERSION = 0x09;
    /* 返回设备版本号 */
    const static int MSGID_RESPOND_G3_AND_G4_VERSION = 0xA9;
    /* 获取CPU序列号 */
    const static int MSGID_GET_G3_AND_G4_CPU_SERIAL_NUM = 0x0D;
    /* 返回CPU序列号 */
    const static int MSGID_RESPOND_G3_AND_G4_CPU_SERIAL_NUM = 0xAD;
    /* 写入UUID */
    const static int MSGID_SET_G3_AND_G4_UUID = 0x0E;
    /* 返回写入UUID的结果 */
    const static int MSGID_RESPOND_SET_G3_AND_G4_UUID_RESULT = 0xAE;
    /* 获取UUID */
    const static int MSGID_GET_G3_AND_G4_UUID = 0x0F;
    /* 返回获取UUID的结果 */
    const static int MSGID_RESPOND_GET_G3_AND_G4_UUID_RESULT = 0xAF;
    /* 获取设备的UTC时间 */
    const static int MSGID_GET_G3_UTC_TIME = 0x10;
    /* 返回设备的UTC时间 */
    const static int MSGID_RESPOND_GET_G3_UTC_TIME_RESULT = 0xB0;

    /* 获取G3视频文件列表 */
    const static int MSGID_GET_G3_MP4_FILE_LIST = 0x0A;
    /*  获取G3视频文件列表的结果 */
    const static int MSGID_RESPOND_GET_G3_MP4_FILE_LIST_RESULT = 0xAA;
    /* 操作G3视频文件 */
    const static int MSGID_G3_MP4_FILE_OPT = 0x0B;
    /* 操作G3视频文件的结果 */
    const static int MSGID_RESPOND_G3_MP4_FILE_OPT_RESULT = 0xAB;
    /* 开始传输G3视频文件 */
    const static int MSGID_START_G3_MP4_FILE_TRANSFER = 0x0C;
    /* 开始传输G3视频文件的结果 */
    const static int MSGID_RESPOND_START_G3_MP4_FILE_TRANSFER_RESULT = 0xAC;
    /* 开启设备的调试模式 */
    const static int MSGID_START_G3_DEBUG_MODEL = 0x11;
    /* 设置功能锁信息 */
    const static int MSGID_SET_FUNCTIONLOCK_INFO = 0x12;
    /* 获取功能锁信息 */
    const static int MSGID_GET_FUNCTIONLOCK_INFO = 0x13;
    /* 获取功能锁信息的结果 */
    const static int MSGID_RESPOND_GET_FUNCTIONLOCK_INFO_RESULT = 0xB3;
    /* 重启MRV220 */
    const static int MSGID_REBOOT_MRV220 = 0x14;
    /* 开/关UDP实景数据流（单播） */
    const static int MSGID_UDP_REALVIEW_SEPARATE_OPT  = 0x15;
    /* 返回开/关UDP实景数据流（单播）的结果 */
    const static int MSGID_UDP_REALVIEW_SEPARATE_OPT_RESULT  = 0xB5;
    /* 配置外设显示参数 */
    const static int MSGID_SET_RS485_PERIPHERAL_DISPLAY_PARAMS  = 0x16;
    /* 返回配置外设显示参数的结果 */
    const static int MSGID_RESPOND_GET_RS485_PERIPHERAL_DISPLAY_PARAMS_RESULT  = 0xB6;
    /* 获取MRV220的log文件列表 */
    const static int MSGID_GET_MRV220_LOG_FILE_LIST  = 0x17;
    /* 返回MRV220的log文件列表 */
    const static int MSGID_RESPOND_MRV220_LOG_FILE_LIST  = 0xB7;
    /* 请求传输MRV220的log文件 */
    const static int MSGID_START_MRV220_LOG_FILE_TRANSFER  = 0x18;
    /* 请求传输MRV220的log文件的结果 */
    const static int MSGID_RESPOND_START_MRV220_LOG_FILE_TRANSFER_RESULT  = 0xB8;
    /* 获取多媒体文件加密所使用的密钥 */
    const static int MSGID_GET_MULTIMEDIA_FILE_ENCRYPTION_KEY  = 0x19;
    /* 返回多媒体文件加密所使用的密钥 */
    const static int MSGID_RESPOND_MULTIMEDIA_FILE_ENCRYPTION_KEY  = 0xB9;
    /* 设置多媒体文件加密所使用的密钥 */
    const static int MSGID_SET_MULTIMEDIA_FILE_ENCRYPTION_KEY  = 0x1A;
    /* 获取报警声音文件的配置参数 */
    const static int MSGID_GET_ALARM_SOUND_FILE_CONFIG  = 0x1B;
    /* 返回报警声音文件的配置参数 */
    const static int MSGID_RESPOND_ALARM_SOUND_FILE_CONFIG  = 0xBB;
    /* 获取镜头输入类型过滤信息 */
    const static int MSGID_GET_CAMERA_INPUT_TYPE_FILTER_CONFIG  = 0x1C;
    /* 返回镜头输入类型过滤信息 */
    const static int MSGID_RESPOND_CAMERA_INPUT_TYPE_FILTER_CONFIG  = 0xBC;
    /* 设置镜头输入类型过滤信息 */
    const static int MSGID_SET_CAMERA_INPUT_TYPE_FILTER_CONFIG  = 0x1D;
    /* 获取镜头降效信息 */
    const static int MSGID_GET_CAMERA_REDUCE_EFFECT_INFO  = 0x1F;
    /* 返回镜头降效信息 */
    const static int MSGID_RESPOND_CAMERA_REDUCE_EFFECT_INFO  = 0xBF;
    /* 设置镜头降效信息 */
    const static int MSGID_SET_CAMERA_REDUCE_EFFECT_INFO  = 0x20;
    /* 设置开关MRV220产测模式 */
    const static int MSGID_SET_PRODUCTION_TESTING_SWITCH  = 0x21;
    /* 开/关TCP实景数据流 */
    const static int MSGID_TCP_REALVIEW_OPT = 0x22;
    /* 开/关TCP实景数据流的结果 */
    const static int MSGID_TCP_REALVIEW_OPT_RESULT = 0xC2;

    /* 通用应答的结果---成功 */
    const static uint8_t RESULT_SUCCESS = 0x00;
    /* 通用应答的结果---失败 */
    const static uint8_t RESULT_FAILED = 0x01;
    /* 通用应答的结果---部分失败 */
    const static uint8_t RESULT_FAILED_SOME = 0x02;


    uint8_t getHead() const;

    void setHead(uint8_t head);

    uint8_t getTail() const;

    void setTail(uint8_t tail);

    uint32_t getFlowdId() const;

    void setFlowdId(uint32_t flowdId);

    uint8_t getMsgId() const;

    void setMsgId(uint8_t msgId);

    uint32_t getContentLen() const;

    void setContentLen(uint32_t contentLen);

    uint16_t getCheckCode() const;

    void setCheckCode(uint16_t checkCode);

    uint8_t *getContentBuf() const;

    void setContentBuf(uint8_t *contentBuf);

    /**
     * 把对象转成一包16进制数据包
     *
     * @param buf ： 用来存放数据包的内存指针
     * @param len ： 内存指针的大小
     *
     * @return 16进制数据包的大小
     *
     * */
    virtual int toCode(uint8_t *buf, int len);

    /**
     * 解析一包数据包转成对象
     *
     * @param buf ： 用来存放数据包的内存指针
     * @param len ： 内存指针的大小
     *
     * @return 结果  0：成功   其他：失败
     *
     * */
    virtual int decode(uint8_t *buf, int len);

private:
    /* 协议头 */
    uint8_t head = 0x5A;
    /* 流水号 */
    uint32_t flowdId = 0;
    /* 命令字 */
    uint8_t msgId = 0;
    /* 内容长度 */
    uint32_t contentLen = 0;
    /* 内容 */
    uint8_t *contentBuf;
    /* 校验码 */
    uint16_t checkCode;
    /* 协议尾 */
    uint8_t tail = 0x5B;
};


#endif //VIS_G3_SOFTWARE_PCCONFIGUREDATAPACKET_H
