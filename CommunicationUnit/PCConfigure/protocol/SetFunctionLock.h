//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/19.
//

#ifndef VIS_G3_SOFTWARE_SETFUNCTIONLOCK_H
#define VIS_G3_SOFTWARE_SETFUNCTIONLOCK_H

#include "PCConfigureDataPacket.h"

class SetFunctionLock : public PCConfigureDataPacket {
public:

    SetFunctionLock();

    ~SetFunctionLock();

    int decode(uint8_t *buf, int len) override;

    uint8_t getLockStrLen() const;

    void setLockStrLen(uint8_t lockStrLen);

    const std::string &getLockStr() const;

    void setLockStr(const std::string &lockStr);

private:
    /* 功能锁内容的长度 */
    uint8_t lockStrLen;
    /* 功能锁内容 */
    std::string lockStr;

};


#endif //VIS_G3_SOFTWARE_SETFUNCTIONLOCK_H
