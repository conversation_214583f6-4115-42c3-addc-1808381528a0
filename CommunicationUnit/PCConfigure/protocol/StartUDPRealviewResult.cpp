//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/11.
//

#include "StartUDPRealviewResult.h"

StartUDPRealviewResult::StartUDPRealviewResult() {
    setMsgId(MSGID_START_UDP_REALVIEW_RESULT);

}

uint8_t StartUDPRealviewResult::getCameraId() const {
    return cameraId;
}

void StartUDPRealviewResult::setCameraId(uint8_t cameraId) {
    StartUDPRealviewResult::cameraId = cameraId;
}

uint8_t StartUDPRealviewResult::getResult() const {
    return result;
}

void StartUDPRealviewResult::setResult(uint8_t result) {
    StartUDPRealviewResult::result = result;
}

uint16_t StartUDPRealviewResult::getPort() const {
    return port;
}

void StartUDPRealviewResult::setPort(uint16_t port) {
    StartUDPRealviewResult::port = port;
}

int StartUDPRealviewResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    if (len >= (9 + 8)) {
        int index = 6;
        setContentLen(5);
        CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
        index += 4;
        /* 再塞G3Id */
        buf[index] = getG3Id();
        index++;
        /* 再塞需要开启实景的相机ID */
        buf[index] = getCameraId();
        index++;
        /* 再塞结果 */
        buf[index] = getResult();
        index++;
        /* 再塞端口 */
        CodeUtils::getInstance().int16ToBb(getPort(), buf + index);
        index += 2;
        /* 加上头尾那些东西 */
        ret = PCConfigureDataPacket::toCode(buf, len);
    }
    return ret;
}

int StartUDPRealviewResult::decode(uint8_t *buf, int len) {
    int ret = -1;
    if (len == (9 + 8)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        /* 解析G3ID */
        setG3Id(buf[index]);
        index++;
        /* 解析镜头ID */
        setCameraId(buf[index]);
        index++;
        /* 解析结果 */
        setResult(buf[index]);
        index++;
        /* 解析端口 */
        setPort(CodeUtils::getInstance().BbToint16(buf + index));
        index += 2;
        ret = PCConfigureDataPacket::decode(buf, len);
    }
    return ret;
}

uint8_t StartUDPRealviewResult::getG3Id() const {
    return g3Id;
}

void StartUDPRealviewResult::setG3Id(uint8_t g3Id) {
    StartUDPRealviewResult::g3Id = g3Id;
}
