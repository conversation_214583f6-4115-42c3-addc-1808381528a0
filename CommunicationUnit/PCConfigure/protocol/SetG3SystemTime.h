//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/13.
//

#ifndef VIS_G3_SOFTWARE_SETG3SYSTEMTIME_H
#define VIS_G3_SOFTWARE_SETG3SYSTEMTIME_H

#include <string>
#include "PCConfigureDataPacket.h"

class SetG3SystemTime : public PCConfigureDataPacket {
public:
    SetG3SystemTime();

    int decode(uint8_t *buf, int len) override;

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

    uint32_t getTimeZoneStrLen() const;

    void setTimeZoneStrLen(uint32_t timeZoneStrLen);

    const std::string &getTimeZoneStr() const;

    void setTimeZoneStr(const std::string &timeZoneStr);

    const uint8_t *getBcdTime() const;

private:
    /* G3ID */
    uint8_t g3Id = 0;
    /* 时区内容的长度 */
    uint32_t timeZoneStrLen = 0;
    /* 时区内容 */
    std::string timeZoneStr;
    /* BCD的时间 格式：YY-MM-DD-hh-mm-ss  该时间必须是前面给的时区对应的时间 */
    uint8_t bcdTime[6] = {0x00};

};


#endif //VIS_G3_SOFTWARE_SETG3SYSTEMTIME_H
