//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/19.
//

#include "SetFunctionLock.h"

SetFunctionLock::SetFunctionLock() {
    setMsgId(MSGID_SET_FUNCTIONLOCK_INFO);

}

int SetFunctionLock::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {

        int index = 6;
        /* 解析长度 */
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        setLockStrLen(buf[index]);
        index++;
        /* 解析配置表的内容 */
        if (getLockStrLen() > 0) {
            uint8_t tempStr[getLockStrLen() + 1];
            memset(tempStr, 0x00, getLockStrLen() + 1);
            memcpy(tempStr, buf + index, getLockStrLen());
            lockStr.append(reinterpret_cast<const char *>(tempStr));
        }
        /* 解析除了内容之外的东西 */
        ret = 0;
    }
    return ret;
}

uint8_t SetFunctionLock::getLockStrLen() const {
    return lockStrLen;
}

void SetFunctionLock::setLockStrLen(uint8_t lockStrLen) {
    SetFunctionLock::lockStrLen = lockStrLen;
}


SetFunctionLock::~SetFunctionLock() {


}

const std::string &SetFunctionLock::getLockStr() const {
    return lockStr;
}

void SetFunctionLock::setLockStr(const std::string &lockStr) {
    SetFunctionLock::lockStr = lockStr;
}
