//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/14.
//

#ifndef VIS_G3_SOFTWARE_REALTIMEVEHICLESTATUS_H
#define VIS_G3_SOFTWARE_REALTIMEVEHICLESTATUS_H

#include "PCConfigureDataPacket.h"
#include "G3_Configuration.h"



struct VehicleStatus {
    uint8_t acc: 1;
    uint8_t reverse: 1;
    uint8_t reserve1: 1;
    uint8_t turnL: 1;
    uint8_t turnR: 1;
    uint8_t bigLight: 1;
    uint8_t wiper: 1;
    uint8_t reserve2: 1;
    uint8_t brake: 1;
    uint8_t frontDoor: 1;
    uint8_t backDoor: 1;
    uint8_t reserve3: 5;
};

struct LightControlStatus {
    /* 第一个普通灯 */
    bool lamp_open_norma1 : 1;
    /* 第一个红外灯 */
    bool  lamp_open_infrared1 : 1;
    /* 保留 */
    int other_ch1 : 2;
    /* 第二个普通灯 */
    bool lamp_open_norma2 : 1;
    /* 第二个红外灯 */
    bool  lamp_open_infrared2 : 1;
    /* 保留 */
    int other_ch2 : 2;
};

/* C3的状态 */
struct C3_SWITCH_STATUS {
    /* c3_1的状态 */
    bool c3_1 : 1;
    /* c3_2的状态 */
    bool c3_2 : 1;
    /* c3_3的状态 */
    bool c3_3 : 1;
    /* c3_4的状态 */
    bool c3_4 : 1;
    /* 保留 */
    int other : 4;
};


class RealTimeVehicleStatus : public PCConfigureDataPacket {

public:

    const static uint16_t ID_BATTERTVOLTAGE = 0x0001; // 电瓶电压
    const static uint16_t ID_ENGINESPEED = 0x0002; // 发动机转速
    const static uint16_t ID_SPEED = 0x0003; // 车速
    const static uint16_t ID_COOLANTTEMPERATURE = 0x0004; // 冷却液温度
    const static uint16_t ID_INSTANTANEOUSFUELCONSUMPTION = 0x0005;// 瞬时油耗
    const static uint16_t ID_RESIDUALOIL = 0x0006; // 剩余油量
    const static uint16_t ID_CARSTATUS = 0x0007; // 车辆状态
    const static uint16_t ID_CARERRORCODE = 0x0008; // 故障码
    const static uint16_t ID_CURRENTMILEAGE = 0x0009; // 当前行驶里程
    const static uint16_t ID_TRAVELTIME = 0x000A; // 本次行驶时间
    const static uint16_t ID_IDLINGTIME = 0x000B; // 本次怠速时间
    const static uint16_t ID_ACCUMULATIVEOILCONSUMPTION = 0x000C; // 累计耗油量
    const static uint16_t ID_ODO = 0x000D; // 累计行驶里程
    const static uint16_t ID_CUMULATIVEDRIVINGTIME = 0x000E; // 累计行驶时间
    const static uint16_t ID_CUMULATIVEIDLINGTIME = 0x000F; // 累计怠速时间
    const static uint16_t ID_GPS = 0xF001; // GPS 数据
    const static uint16_t ID_MACHINERY_ACTION_BARS = 0xF002; // 工程机械操作杆状态
    const static uint16_t ID_CAMERA_ALARM_SWITCH = 0xF003; // 镜头报警开关的状态
    const static uint16_t ID_LIGHT_CONTROL_STATUS = 0xF004; // 灯控状态
    const static uint16_t ID_G4MT_CAMERA_SWITCH = 0x0010; // G4-MT中镜头切换脚的状态
    const static uint16_t ID_SEAT_BELT_STATUS = 0xF005; // 安全带状态
    const static uint16_t ID_SECURITY_SWITCH_STATUS = 0xF006; // Security开关状态
    const static uint16_t ID_C3_SWITCH_STATUS = 0xF007; // C3的状态

    RealTimeVehicleStatus();

    ~RealTimeVehicleStatus();

    int decode(uint8_t *buf, int len) override;

    int parseParam(uint16_t paramId, uint8_t *data);


    int getBatteryVoltage() const;

    uint16_t getEngineSpeed() const;

    float getSpeed() const;

    uint8_t getCoolantTemperature() const;

    float getInstantaneousFuelConsumption() const;

    uint8_t getResidualOil() const;

    const VehicleStatus &getCarStatus() const;

    float getCurrentMileage() const;

    uint16_t getTravelTime() const;

    uint16_t getIdlingTime() const;

    uint32_t getAccumulativeOilConsumption() const;

    float getOdo() const;

    uint32_t getCumulativeDrivingTime() const;

    uint32_t getCumulativeIdlingTime() const;

    const GPSInfo &getGpsData() const;

    uint8_t getMachineryActionBars() const;

    void setMachineryActionBars(uint8_t machineryActionBars);

    const Camera_Alarm_Switch_Status &getCameraAlarmSwitchStatus() const;

    void setCameraAlarmSwitchStatus(const Camera_Alarm_Switch_Status &cameraAlarmSwitchStatus);

    uint8_t getG4MtCameraSwitch() const;

    void setG4MtCameraSwitch(uint8_t g4MtCameraSwitch);

    uint8_t getSeatbeltStatus() const;

    void setSeatbeltStatus(uint8_t seatbeltStatus);

    uint8_t getSecuritySwitchStatus() const;

    void setSecuritySwitchStatus(uint8_t securitySwitchStatus);

    const C3_SWITCH_STATUS &getC3SwitchStatus() const;

    void setC3SwitchStatus(const C3_SWITCH_STATUS &c3SwitchStatus);


private:
    // 电瓶电压
    int batteryVoltage = -1;
    // 发动机转速
    uint16_t engineSpeed = -1;
    // 车速
    float speed = -1;
    // 冷却液温度
    uint8_t coolantTemperature = -1;
    // 瞬时油耗
    float instantaneousFuelConsumption = -1;
    // 剩余油量
    uint8_t residualOil = -1;
    // 车辆状态
    VehicleStatus carStatus = {0x00};
    // 当前行驶里程
    float currentMileage = -1;
    // 本次行驶时间
    uint16_t travelTime = -1;
    // 本次怠速时间
    uint16_t idlingTime = -1;
    // 累计耗油量
    uint32_t AccumulativeOilConsumption = -1;
    // 累计行驶里程
    float ODO = -1;
    // 累计行驶时间
    uint32_t cumulativeDrivingTime = -1;
    // 累计怠速时间
    uint32_t cumulativeIdlingTime = -1;
    // GPS 数据
    GPSInfo gpsData;
    //工程机械操作杆状状态
    uint8_t machineryActionBars = -1;
    //镜头报警开关状态
    Camera_Alarm_Switch_Status cameraAlarmSwitchStatus = {true,true,true};
    /* G4-MT中镜头切换脚的状态  0：当前为DSM镜头  1：当前为交通灯镜头 */
    uint8_t g4MT_camera_switch = 0;
    /* 灯控状态 */
    LightControlStatus lightControlStatus = {false, false,0,false, false,0};
    /* 从CAN中读到的安全带状态。0xFF：无效    0x00：安全带未扣上    0x01：安全带已扣上 */
    uint8_t seatbeltStatus = 0xFF;
    /* Security开关状态 0：关闭 1：开启 0xFF表示无效 */
    uint8_t securitySwitchStatus = 0xFF;
    /* C3的状态 用bit位表示，0：表示开 1：表示关 */
    C3_SWITCH_STATUS c3SwitchStatus {false,false, false, false};

};


#endif //VIS_G3_SOFTWARE_REALTIMEVEHICLESTATUS_H
