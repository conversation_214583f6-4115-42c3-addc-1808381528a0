//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/4/8.
//

#ifndef VIS_G3_SOFTWARE_GETALARMSOUNDFILECONF_H
#define VIS_G3_SOFTWARE_GETALARMSOUNDFILECONF_H

#include "PCConfigureDataPacket.h"
class GetAlarmSoundFileConf: public PCConfigureDataPacket {
public:
    GetAlarmSoundFileConf();

    int decode(uint8_t *buf, int len) override;


private:


};



#endif //VIS_G3_SOFTWARE_GETALARMSOUNDFILECONF_H
