//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/10/27.
//

#include <cstdint>
#include "DetectionAlarmEvent_DSMEvent.h"

DetectionAlarmEvent_DSMEvent::DetectionAlarmEvent_DSMEvent() {
    setDataId(DATAID_DSM_ALARM_EVENT);
    setDataLen(13);
}

DetectionAlarmEvent_DSMEvent::~DetectionAlarmEvent_DSMEvent() {

}

int DetectionAlarmEvent_DSMEvent::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 先塞ID */
    CodeUtils::getInstance().uint16ToBb(getDataId(), buf + index);
    index += 2;
    /* 再塞总长度 */
    CodeUtils::getInstance().uint32ToBb(getDataLen(), buf + index);
    index += 4;
    /* 相机ID */
    buf[index] = getCurCameraId();
    index++;
    /* 口罩报警状态 */
    buf[index] = getRespiratorAlarm();
    index++;
    /* 闭眼报警状态 */
    buf[index] = getEyeAlarm();
    index++;
    /* 哈欠报警状态 */
    buf[index] = getMouthAlarm();
    index++;
    /* 左顾右盼报警状态 */
    buf[index] = getLookaroundAlarm();
    index++;
    /* 离岗报警状态 */
    buf[index] = getFacemissingAlarm();
    index++;
    /* 遮挡报警状态 */
    buf[index] = getCamcoverAlarm();
    index++;
    /* 抽烟报警状态 */
    buf[index] = getSmokingAlarm();
    index++;
    /* 打电话报警状态 */
    buf[index] = getPhoneAlarm();
    index++;
    /* 疲劳程度 */
    buf[index] = getFatigueRank();
    index++;
    /* 安全带报警状态 */
    buf[index] = getBeltAlarm();
    index++;
    /* 盲点报警状态 */
    buf[index] = getBlindspotAlarm();
    index++;
    /* 帽子报警状态 */
    buf[index] = getHatAlarm();
    index++;
    ret = index;
    return ret;
}

uint8_t DetectionAlarmEvent_DSMEvent::getCurCameraId() const {
    return curCameraId;
}

void DetectionAlarmEvent_DSMEvent::setCurCameraId(uint8_t curCameraId) {
    DetectionAlarmEvent_DSMEvent::curCameraId = curCameraId;
}

uint8_t DetectionAlarmEvent_DSMEvent::getRespiratorAlarm() const {
    return respirator_alarm;
}

void DetectionAlarmEvent_DSMEvent::setRespiratorAlarm(uint8_t respiratorAlarm) {
    respirator_alarm = respiratorAlarm;
}

uint8_t DetectionAlarmEvent_DSMEvent::getEyeAlarm() const {
    return eye_alarm;
}

void DetectionAlarmEvent_DSMEvent::setEyeAlarm(uint8_t eyeAlarm) {
    eye_alarm = eyeAlarm;
}

uint8_t DetectionAlarmEvent_DSMEvent::getMouthAlarm() const {
    return mouth_alarm;
}

void DetectionAlarmEvent_DSMEvent::setMouthAlarm(uint8_t mouthAlarm) {
    mouth_alarm = mouthAlarm;
}

uint8_t DetectionAlarmEvent_DSMEvent::getLookaroundAlarm() const {
    return lookaround_alarm;
}

void DetectionAlarmEvent_DSMEvent::setLookaroundAlarm(uint8_t lookaroundAlarm) {
    lookaround_alarm = lookaroundAlarm;
}

uint8_t DetectionAlarmEvent_DSMEvent::getFacemissingAlarm() const {
    return facemissing_alarm;
}

void DetectionAlarmEvent_DSMEvent::setFacemissingAlarm(uint8_t facemissingAlarm) {
    facemissing_alarm = facemissingAlarm;
}

uint8_t DetectionAlarmEvent_DSMEvent::getCamcoverAlarm() const {
    return camcover_alarm;
}

void DetectionAlarmEvent_DSMEvent::setCamcoverAlarm(uint8_t camcoverAlarm) {
    camcover_alarm = camcoverAlarm;
}

uint8_t DetectionAlarmEvent_DSMEvent::getSmokingAlarm() const {
    return smoking_alarm;
}

void DetectionAlarmEvent_DSMEvent::setSmokingAlarm(uint8_t smokingAlarm) {
    smoking_alarm = smokingAlarm;
}

uint8_t DetectionAlarmEvent_DSMEvent::getPhoneAlarm() const {
    return phone_alarm;
}

void DetectionAlarmEvent_DSMEvent::setPhoneAlarm(uint8_t phoneAlarm) {
    phone_alarm = phoneAlarm;
}

uint8_t DetectionAlarmEvent_DSMEvent::getFatigueRank() const {
    return fatigue_rank;
}

void DetectionAlarmEvent_DSMEvent::setFatigueRank(uint8_t fatigueRank) {
    fatigue_rank = fatigueRank;
}

const uint8_t &DetectionAlarmEvent_DSMEvent::getBeltAlarm() const {
    return belt_alarm;
}

void DetectionAlarmEvent_DSMEvent::setBeltAlarm(const uint8_t &beltAlarm) {
    belt_alarm = beltAlarm;
}

const uint8_t &DetectionAlarmEvent_DSMEvent::getBlindspotAlarm() const {
    return blindspot_alarm;
}

void DetectionAlarmEvent_DSMEvent::setBlindspotAlarm(const uint8_t &blindspotAlarm) {
    blindspot_alarm = blindspotAlarm;
}

uint8_t DetectionAlarmEvent_DSMEvent::getHatAlarm() const {
    return hat_alarm;
}

void DetectionAlarmEvent_DSMEvent::setHatAlarm(uint8_t hatAlarm) {
    hat_alarm = hatAlarm;
}
