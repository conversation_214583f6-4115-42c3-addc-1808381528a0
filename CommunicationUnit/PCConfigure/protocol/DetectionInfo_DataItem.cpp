//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/13.
//

#include <cstdio>
#include "DetectionInfo_DataItem.h"

DetectionInfo_DataItem::DetectionInfo_DataItem() {

}

DetectionInfo_DataItem::~DetectionInfo_DataItem() {

}

uint16_t DetectionInfo_DataItem::getDataId() const {
    return dataId;
}

void DetectionInfo_DataItem::setDataId(uint16_t dataId) {
    DetectionInfo_DataItem::dataId = dataId;
}

uint32_t DetectionInfo_DataItem::getDataLen() const {
    return dataLen;
}

void DetectionInfo_DataItem::setDataLen(uint32_t dataLen) {
    DetectionInfo_DataItem::dataLen = dataLen;
}

int DetectionInfo_DataItem::toCode(uint8_t *buf, int len) {
//    printf("DetectionInfo_DataItem::toCode\n");
    return 0;
}


