//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/11/8.
//

#include "SetG3UUID.h"

SetG3UUID::SetG3UUID() {
    setMsgId(MSGID_SET_G3_AND_G4_UUID);
}

int SetG3UUID::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((len >= MIN_DATA_LEN + 5) && (PCConfigureDataPacket::decode(buf, len) == 0)) {
        int index = 6;
        /* 解析长度 */
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        /* 解析配置表的长度 */
        setUuidStrLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        /* 解析配置表的内容 */
        if (getUuidStrLen() > 0) {
            uint8_t uuid[uuidStrLen + 1];
            (void) memset(uuid, 0x00, sizeof(uuid));
            memcpy(uuid, buf + index, uuidStrLen);
            uuidStr.clear();
            uuidStr.append(reinterpret_cast<const char *>(uuid));
        }
        /* 解析除了内容之外的东西 */
        ret = 0;
    }
    return ret;
}

uint32_t SetG3UUID::getUuidStrLen() const {
    return uuidStrLen;
}

void SetG3UUID::setUuidStrLen(uint32_t uuidStrLen) {
    SetG3UUID::uuidStrLen = uuidStrLen;
}

const std::string &SetG3UUID::getUuidStr() const {
    return uuidStr;
}

void SetG3UUID::setUuidStr(const std::string &uuidStr) {
    SetG3UUID::uuidStr = uuidStr;
}
