//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/13.
//

#include <cstdio>
#include "DetectionInfo.h"

DetectionInfo::DetectionInfo() {
    setMsgId(MSGID_OUTPUT_DETECTION_INFO);
}

DetectionInfo::~DetectionInfo() {

}

int DetectionInfo::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;

    int mContentLen = 0;

    /* 先随便塞个内容长度 */
    setContentLen(mContentLen);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 再塞G3Id */
    buf[index] = getG3Id();
    index++;
    mContentLen++;
    /* 再塞信息项总数 */
    infoListSize = objectInfoList.size() + systemInfoList.size() + systemErrorList.size() + dsmInfoList.size() +
                   gestureInfoList.size() + adasInfoList.size() + maskBufInfoList.size() + lampControlList.size() +
                   r151InfoList.size() + r158InfoList.size() + r159InfoList.size() + seatbeltList.size() + algDetectTimeList.size()+
                   barcodeInfoList.size() + securityModeInfoList.size() + gsensorDataList.size()+gpioStatusDataList.size()+signalAndStatusDataList.size();
    buf[index] = infoListSize;
    index++;
    mContentLen++;
    /* 再塞所有信息项的内容 */
    if (infoListSize > 0) {

        /* 添加objectInfoList  */
        if (objectInfoList.size() > 0) {
            for (std::size_t i = 0; i < objectInfoList.size(); i++) {
                int mItemLen = objectInfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 添加systemInfoList  */
        if (systemInfoList.size() > 0) {
            for (std::size_t i = 0; i < systemInfoList.size(); i++) {
                int mItemLen = systemInfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 添加systemErrorList  */
        if (systemErrorList.size() > 0) {
            for (std::size_t i = 0; i < systemErrorList.size(); i++) {
                int mItemLen = systemErrorList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 添加dsmInfoList  */
        if (dsmInfoList.size() > 0) {
            for (std::size_t i = 0; i < dsmInfoList.size(); i++) {
                int mItemLen = dsmInfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 添加手势  */
        if (gestureInfoList.size() > 0) {
            for (std::size_t i = 0; i < gestureInfoList.size(); i++) {
                int mItemLen = gestureInfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 添加ADAS  */
        if (adasInfoList.size() > 0) {
            for (std::size_t i = 0; i < adasInfoList.size(); i++) {
                int mItemLen = adasInfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 添加MaskBuf  */
        if (maskBufInfoList.size() > 0) {
            for (std::size_t i = 0; i < maskBufInfoList.size(); i++) {
                int mItemLen = maskBufInfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 灯控制信息  */
        if (lampControlList.size() > 0) {
            for (std::size_t i = 0; i < lampControlList.size(); i++) {
                int mItemLen = lampControlList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* R151识别信息 */
        if (r151InfoList.size() > 0) {
            for (std::size_t i = 0; i < r151InfoList.size(); i++) {
                int mItemLen = r151InfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* R158识别信息 */
        if (r158InfoList.size() > 0) {
            for (std::size_t i = 0; i < r158InfoList.size(); i++) {
                int mItemLen = r158InfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* R159识别信息 */
        if (r159InfoList.size() > 0) {
            for (std::size_t i = 0; i < r159InfoList.size(); i++) {
                int mItemLen = r159InfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 安全带识别信息 */
        if (seatbeltList.size() > 0) {
            for (std::size_t i = 0; i < seatbeltList.size(); i++) {
                int mItemLen = seatbeltList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 算法耗时信息 */
        if (algDetectTimeList.size() > 0) {
            for (std::size_t i = 0; i < algDetectTimeList.size(); i++) {
                int mItemLen = algDetectTimeList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }

        /* 条码识别信息 */
        if (barcodeInfoList.size() > 0) {
            for (std::size_t i = 0; i < barcodeInfoList.size(); i++) {
                int mItemLen = barcodeInfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }
        /* 安保模式识别信息 */
        if (securityModeInfoList.size() > 0) {
            for (std::size_t i = 0; i < securityModeInfoList.size(); i++) {
                int mItemLen = securityModeInfoList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }
        /* GSsensor数据 */
        if (gsensorDataList.size() > 0) {
            for (std::size_t i = 0; i < gsensorDataList.size(); i++) {
                int mItemLen = gsensorDataList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }
        /* GPIO状态数据 */
        if (gpioStatusDataList.size() > 0) {
            for (std::size_t i = 0; i < gpioStatusDataList.size(); i++) {
                int mItemLen = gpioStatusDataList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }
        /* 车辆信号的设备状态数据 */
        if (signalAndStatusDataList.size() > 0) {
            for (std::size_t i = 0; i < signalAndStatusDataList.size(); i++) {
                int mItemLen = signalAndStatusDataList[i].toCode(buf + index, len - index);
                index += mItemLen;
                mContentLen += mItemLen;
            }
        }


    }


    /* 再把正确的长度塞进去 */
    setContentLen(mContentLen);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + 6);

    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

int DetectionInfo::decode(uint8_t *buf, int len) {
    return PCConfigureDataPacket::decode(buf, len);
}

uint8_t DetectionInfo::getInfoListSize() const {
    return infoListSize;
}

void DetectionInfo::setInfoListSize(uint8_t infoListSize) {
    DetectionInfo::infoListSize = infoListSize;
}

uint8_t DetectionInfo::getG3Id() const {
    return g3Id;
}

void DetectionInfo::setG3Id(uint8_t g3Id) {
    DetectionInfo::g3Id = g3Id;
}

void DetectionInfo::addDetectionInfoItem_ObjectInfo(DetectionInfo_ObjectInfo &dataItem) {
    if (!objectInfoList.empty()) {
        for (std::vector<DetectionInfo_ObjectInfo>::iterator iter = objectInfoList.begin();
             iter != objectInfoList.end();) {
            if (iter->getCameraId() == dataItem.getCameraId()) {
                /* 从列表里删除 */
                objectInfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }

    objectInfoList.push_back(dataItem);
}

void DetectionInfo::addDetectionInfoItem_SystemInfo(DetectionInfo_SystemInfo &systemInfo) {
    systemInfoList.clear();
    systemInfoList.push_back(systemInfo);
}

void DetectionInfo::addDetectionInfoItem_SystemError(DetectionInfo_SystemError &systemError) {
    systemErrorList.clear();
    systemErrorList.push_back(systemError);
}

void DetectionInfo::addDetectionInfoItem_DSMInfo(DetectionInfo_DSM &dsmInfo) {
    if (!dsmInfoList.empty()) {
        for (std::vector<DetectionInfo_DSM>::iterator iter = dsmInfoList.begin(); iter != dsmInfoList.end();) {
            if (iter->getCurCameraId() == dsmInfo.getCurCameraId()) {
                /* 从列表里删除 */
                dsmInfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }
    dsmInfoList.push_back(dsmInfo);
}

void DetectionInfo::addDetectionInfoItem_GestureInfo(DetectionInfo_Gesture &gestureInfo) {
    if (!gestureInfoList.empty()) {
        for (std::vector<DetectionInfo_Gesture>::iterator iter = gestureInfoList.begin();
             iter != gestureInfoList.end();) {
            if (iter->getCurCameraId() == gestureInfo.getCurCameraId()) {
                /* 从列表里删除 */
                gestureInfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }
    gestureInfoList.push_back(gestureInfo);
}

void DetectionInfo::addDetectionInfoItem_ADASInfo(DetectionInfo_ADAS &adasInfo) {
    if (!adasInfoList.empty()) {
        for (std::vector<DetectionInfo_ADAS>::iterator iter = adasInfoList.begin(); iter != adasInfoList.end();) {
            if (iter->getCurCameraId() == adasInfo.getCurCameraId()) {
                /* 从列表里删除 */
                adasInfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }
    adasInfoList.push_back(adasInfo);
}

void DetectionInfo::addDetectionInfoItem_MaskBuf(DetectionInfo_MaskBuf &maskBufInfo) {
    if (!maskBufInfoList.empty()) {
        for (std::vector<DetectionInfo_MaskBuf>::iterator iter = maskBufInfoList.begin();
             iter != maskBufInfoList.end();) {
            if (iter->getCameraId() == maskBufInfo.getCameraId()) {
                /* 从列表里删除 */
                maskBufInfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }
    maskBufInfoList.push_back(maskBufInfo);
}

const std::vector<DetectionInfo_MaskBuf> &DetectionInfo::getMaskBufInfoList() const {
    return maskBufInfoList;
}

void DetectionInfo::addDetectionInfoItem_LampContro(DetectionInfo_Control &lampControl) {
    if (!lampControlList.empty()) {
        DetectionInfo_Control::DetectionInfo_Control_ChannelControl temp = lampControlList[0].getChannelControl();
        temp.lamp1_open_norma1 |= lampControl.getChannelControl().lamp1_open_norma1;
        temp.lamp_open_infrared1 |= lampControl.getChannelControl().lamp_open_infrared1;
        temp.lamp1_open_norma2 |= lampControl.getChannelControl().lamp1_open_norma2;
        temp.lamp_open_infrared2 |= lampControl.getChannelControl().lamp_open_infrared2;
        lampControlList[0].setChannelControl(temp);
    } else {
        lampControlList.push_back(lampControl);
    }
}

void DetectionInfo::addDetectionInfoItem_R151Info(DetectionInfo_R151Info &r151Info) {
    if (!r151InfoList.empty()) {
        for (std::vector<DetectionInfo_R151Info>::iterator iter = r151InfoList.begin();
             iter != r151InfoList.end();) {
            if (iter->getCameraId() == r151Info.getCameraId()) {
                /* 从列表里删除 */
                r151InfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }
        r151InfoList.push_back(r151Info);


}

void DetectionInfo::addDetectionInfoItem_R158Info(DetectionInfo_R158Info &r158Info) {
    if (!r158InfoList.empty()) {
        for (std::vector<DetectionInfo_R158Info>::iterator iter = r158InfoList.begin();
             iter != r158InfoList.end();) {
            if (iter->getCameraId() == r158Info.getCameraId()) {
                /* 从列表里删除 */
                r158InfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }
    r158InfoList.push_back(r158Info);
}

void DetectionInfo::addDetectionInfoItem_R159Info(DetectionInfo_R159Info &r159Info) {
    if (!r159InfoList.empty()) {
        for (std::vector<DetectionInfo_R159Info>::iterator iter = r159InfoList.begin();
             iter != r159InfoList.end();) {
            if (iter->getCameraId() == r159Info.getCameraId()) {
                /* 从列表里删除 */
                r159InfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }
    r159InfoList.push_back(r159Info);
}

void DetectionInfo::addDetectionInfoItem_SeatbeltInfo(DetectionInfo_SeatBelt &seatbeltInfo) {
    if (!seatbeltList.empty()) {
        for (std::vector<DetectionInfo_SeatBelt>::iterator iter = seatbeltList.begin();
             iter != seatbeltList.end();) {
            if (iter->getCurCameraId() == seatbeltInfo.getCurCameraId()) {
                /* 从列表里删除 */
                seatbeltList.erase(iter);
            } else {
                iter++;
            }
        }
    }
    seatbeltList.push_back(seatbeltInfo);

}

void DetectionInfo::addDetectionInfoItem_AlgDetectTime(DetectionInfo_AlgDetectTime &algDetectTime) {
    algDetectTimeList.clear();
    algDetectTimeList.push_back(algDetectTime);

}

void DetectionInfo::addDetectionInfoItem_BarCodeInfos(DetectionInfo_BarCodeInfos &barCodeInfos) {
    if (!barcodeInfoList.empty()) {
        for (std::vector<DetectionInfo_BarCodeInfos>::iterator iter = barcodeInfoList.begin();
             iter != barcodeInfoList.end();) {
            if (iter->getCameraId() == barCodeInfos.getCameraId()) {
                /* 从列表里删除 */
                barcodeInfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }
    barcodeInfoList.push_back(barCodeInfos);
}

void DetectionInfo::addDetectionInfoItem_SecurityModeInfo(DetectionInfo_SecurityModeInfo &securityModeInfos) {
    if (!securityModeInfoList.empty()) {
        for (std::vector<DetectionInfo_SecurityModeInfo>::iterator iter = securityModeInfoList.begin();
             iter != securityModeInfoList.end();) {
            if (iter->getCameraId() == securityModeInfos.getCameraId()) {
                /* 从列表里删除 */
                securityModeInfoList.erase(iter);
            } else {
                iter++;
            }
        }
    }
    securityModeInfoList.push_back(securityModeInfos);
}

void DetectionInfo::addDetectionInfo_GSensorData(DetectionInfo_GSensorData &gSensorData) {
    gsensorDataList.clear();
    gsensorDataList.push_back(gSensorData);
}

void DetectionInfo::addDetectionInfo_GPIOIOStatus(DetectionInfo_GPIOIOStatus &gpioioStatus) {
    gpioStatusDataList.clear();
    gpioStatusDataList.push_back(gpioioStatus);
}

void DetectionInfo::addDetectionInfo_SignalAndStatus(DetectionInfo_SignalAndStatus &signalAndStatus) {
    signalAndStatusDataList.clear();
    signalAndStatusDataList.push_back(signalAndStatus);
}



