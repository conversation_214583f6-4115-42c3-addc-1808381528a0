//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/11.
//

#ifndef VIS_G3_SOFTWARE_RESPONDG3CONFIG_H
#define VIS_G3_SOFTWARE_RESPONDG3CONFIG_H

#include "PCConfigureDataPacket.h"

class RespondG3Config : public PCConfigureDataPacket {
public:
    RespondG3Config();

    ~RespondG3Config();

    int decode(uint8_t *buf, int len) override;

    int toCode(uint8_t *buf, int len) override;

    uint32_t getConfigStrLen() const;

    void setConfigStrLen(uint32_t configStrLen);

    char *getConfigStr() const;

    void setConfigStr1(char *configStr);

    void setConfigStr(char *buf, int len);

    uint8_t getG3Id() const;

    void setG3Id(uint8_t g3Id);

    uint8_t getResult() const;

    void setResult(uint8_t result);

private:
    /* G3ID */
    uint8_t g3Id = 0;
    /* 获取的结果 */
    uint8_t result = 0;
    /* 配置表内容的长度 */
    uint32_t configStrLen = 0;
    /* 配置表的内容 */
    char *configStr;


};


#endif //VIS_G3_SOFTWARE_RESPONDG3CONFIG_H
