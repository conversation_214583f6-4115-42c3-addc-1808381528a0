//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/2.
//

#include "G3Mp4FileOpt.h"
#include "XuString.h"

G3Mp4FileOpt::G3Mp4FileOpt() {
    setMsgId(MSGID_G3_MP4_FILE_OPT);
}

int G3Mp4FileOpt::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((PCConfigureDataPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 6;
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index = index + 4;

        optType = buf[index];
        index = index + 1;

        fileListSize = CodeUtils::getInstance().BbToint16(buf + index);
        index = index + 2;

        if (fileListSize > 0) {
            for (int i = 0; i < fileListSize; i++) {
                PCConfigureDataPacket::MP4FileInfo infoTmep;

                infoTmep.fileType = buf[index];
                index = index + 1;

                uint16_t detectTemp = CodeUtils::getInstance().BbToUint16(buf + index);
                memcpy(&infoTmep.detectContent, &detectTemp, 2);
                index = index + 2;

                infoTmep.fileStatus = buf[index];
                index = index + 1;

                infoTmep.cameraId = buf[index];
                index = index + 1;

                memcpy(&infoTmep.bcdTime, buf + index, 7);
                index = index + 7;

                infoTmep.fileNameLen = buf[index];
                index = index + 1;


                uint8_t filenameBytes[infoTmep.fileNameLen];
                memcpy(filenameBytes, buf + index, infoTmep.fileNameLen);
                memcpy(infoTmep.fileName,filenameBytes,infoTmep.fileNameLen);
                index = index + infoTmep.fileNameLen;
                printf("***fileNameLen=%d   %d  \n", infoTmep.fileNameLen, infoTmep.fileNameLen);

                fileList.push_back(infoTmep);
            }
        }
        ret = 0;
    }
    return ret;
}

uint8_t G3Mp4FileOpt::getOptType() const {
    return optType;
}

void G3Mp4FileOpt::setOptType(uint8_t optType) {
    optType = optType;
}

uint16_t G3Mp4FileOpt::getFileListSize() const {
    return fileListSize;
}

void G3Mp4FileOpt::setFileListSize(uint16_t fileListSize) {
    G3Mp4FileOpt::fileListSize = fileListSize;
}

const std::vector<PCConfigureDataPacket::MP4FileInfo> &G3Mp4FileOpt::getFileList() const {
    return fileList;
}

void G3Mp4FileOpt::setFileList(const std::vector<PCConfigureDataPacket::MP4FileInfo> &fileList) {
    G3Mp4FileOpt::fileList = fileList;
}
