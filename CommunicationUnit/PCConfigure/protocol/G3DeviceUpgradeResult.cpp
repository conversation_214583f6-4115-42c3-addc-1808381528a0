//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/31.
//

#include "G3DeviceUpgradeResult.h"

G3DeviceUpgradeResult::G3DeviceUpgradeResult() {
    setMsgId(MSGID_UPGRADE_RESULT);

}

int G3DeviceUpgradeResult::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    setContentLen(2);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    /* 再塞G3Id */
    buf[index] = getDeviceId();
    index++;
    /* 再塞结果 */
    buf[index] = getResult();
    index++;
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}


uint8_t G3DeviceUpgradeResult::getResult() const {
    return result;
}

void G3DeviceUpgradeResult::setResult(uint8_t result) {
    G3DeviceUpgradeResult::result = result;
}

uint8_t G3DeviceUpgradeResult::getDeviceId() const {
    return deviceId;
}

void G3DeviceUpgradeResult::setDeviceId(uint8_t deviceId) {
    G3DeviceUpgradeResult::deviceId = deviceId;
}
