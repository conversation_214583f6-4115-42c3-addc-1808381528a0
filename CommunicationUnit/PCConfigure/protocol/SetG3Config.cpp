//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/11.
//

#include <malloc.h>
#include "SetG3Config.h"

SetG3Config::SetG3Config() {
    setMsgId(MSGID_SET_G3_CONFIG);
}

int SetG3Config::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((len >= MIN_DATA_LEN + 5) && (PCConfigureDataPacket::decode(buf, len) == 0)) {


        int index = 6;
        /* 解析长度 */
        setContentLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        setG3Id(buf[index]);
        index++;
        /* 解析配置表的长度 */
        setConfigStrLen(CodeUtils::getInstance().BbToint32(buf + index));
        index += 4;
        /* 解析配置表的内容 */
        if (getConfigStrLen() > 0) {
            configStr = static_cast<uint8_t *>(malloc(getConfigStrLen()));
            memcpy(configStr, buf + index, getConfigStrLen());
        }
        /* 解析除了内容之外的东西 */
        ret = 0;
    }
    return ret;
}

SetG3Config::~SetG3Config() {
    if (getContentLen() > 0) {
        free(configStr);
    }
}

int SetG3Config::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 6;
    /* 先塞长度 */
    setContentLen(getConfigStrLen() + 5);
    CodeUtils::getInstance().int32ToBb(getContentLen(), buf + index);
    index += 4;
    buf[index] = g3Id;
    index++;
    /* 再塞配置表的长度 */
    CodeUtils::getInstance().int32ToBb(getConfigStrLen(), buf + index);
    index += 4;
    /* 再塞配置表 */
    memcpy(buf + index, configStr, getConfigStrLen());
    index += getConfigStrLen();
    /* 加上头尾那些东西 */
    ret = PCConfigureDataPacket::toCode(buf, len);
    return ret;
}

uint32_t SetG3Config::getConfigStrLen() const {
    return configStrLen;
}

void SetG3Config::setConfigStrLen(uint32_t configStrLen) {
    SetG3Config::configStrLen = configStrLen;
}

uint8_t *SetG3Config::getConfigStr() const {
    return configStr;
}

void SetG3Config::setConfigStr(uint8_t *configStr) {
    SetG3Config::configStr = configStr;
}

uint8_t SetG3Config::getG3Id() const {
    return g3Id;
}

void SetG3Config::setG3Id(uint8_t g3Id) {
    SetG3Config::g3Id = g3Id;
}
