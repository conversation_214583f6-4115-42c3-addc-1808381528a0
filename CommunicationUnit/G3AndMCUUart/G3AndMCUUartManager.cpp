//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/15.
//

#include <unistd.h>
#include "G3AndMCUUartManager.h"

namespace vis {

    void G3AndMCUUartManager::init(CommunicationDataCallback &communicationDataCallback) {
        callback = &communicationDataCallback;
        decoder.setInterface(*this);
        isNeedStop = false;
        upgradeFileData = static_cast<uint8_t *>(malloc(1024 * 1024 * 2));
        upgradeFileDataLen = 0;

    }


    void G3AndMCUUartManager::setDataFromMCUUart(const uint8_t *buf, int len) {
        decoder.onDataReceived(buf, len);
    }

    void G3AndMCUUartManager::run() {
        std::string pthreadName = "MCUCom";
        pthread_setname_np(pthread_self(), pthreadName.c_str());


        uint8_t uartPacketdata[1024];
        memset(uartPacketdata, 0x00, sizeof(uartPacketdata));

        uint8_t uartdata[2048];
        memset(uartdata, 0x00, sizeof(uartdata));

        // 如果是G4-MINI，那么就等待2s再启动MCU通信，陆工说这样可以修复启动花屏的问题
        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4MINI){
            sleep(2);
        }

        while (!isNeedStop) {

            /* 如果没有查询到版本号  则查询版本号 */
            if (!hasVersion) {
                /* 先查询下版本号 */
                G3AndMCUUartGetPeripheralVersion getPeripheralVersion;
                getPeripheralVersion.setPeripheralId(G3AndMCUUartPacket::PERIPHERAL_ID_MCU);
                int dataLen = encoder.generateGetPeripheralVersion(uartPacketdata, sizeof(uartPacketdata),
                                                                   getPeripheralVersion);
                int senddatal = CodeUtils::getInstance().doEscape4SendFromMCUUart(uartPacketdata, dataLen, 1,
                                                                                  dataLen - 2,
                                                                                  uartdata);
                callback->onNeedSendDataToMCUUART(uartdata, senddatal);

                sleep(1);
            } else {
                /* 查到版本号了   先检查下是否需要更新  需要更新则不发送消息 */
                if (upgradeing) {
                    sleep(1);
                } else {
                    /* 如果是在厂测模式下，且还没发送过厂测任务，就发送厂测命令  知道有结果了才发识别信息 */
                    if (G3_Configuration::getInstance().isVisPtmModel() && needProductionTest) {
                        /* 发送生产测试的命令 */
                        G3AndMCUUartStartPTWrok startPtWrok;
                        /* 目前就测试一下CAN */
                        G3AndMCUUartStartPTWrok_CANTest canTestWrok;
                        canTestWrok.setBaudRate(500);
                        startPtWrok.addCANtestList(canTestWrok);

                        int dataLen = encoder.generateStartPTWrok(uartPacketdata, sizeof(uartPacketdata),
                                                                  startPtWrok);
                        int senddatal = CodeUtils::getInstance().doEscape4SendFromMCUUart(uartPacketdata, dataLen, 1,
                                                                                          dataLen - 2,
                                                                                          uartdata);
                        callback->onNeedSendDataToMCUUART(uartdata, senddatal);

                        sleep(1);
                    } else {

                        G3AndMCUUartDetectionInfo uartDetectionInfo;
                        bool hasBSDInfo = false;
                        G3AndMCUUartDetectionInfo_BSD uartDetectionInfoBsd;

                        G3AndMCUUartDetectionInfo_IOStatus uartDetectionInfoIoStatus;
                        uartDetectionInfoIoStatus.setInputStatus(1);
                        uartDetectionInfoIoStatus.setIoIn1Level(in1Level);
                        uartDetectionInfoIoStatus.setIoIn2Level(in2Level);
                        uartDetectionInfoIoStatus.setIoIn3Level(in3Level);
                        uartDetectionInfo.addIOInfo(uartDetectionInfoIoStatus);


                        G3AndMCUUartDetectionInfo_LampControl::G3AndMCUUartDetectionInfo_LampContro_ChannelControl channelControl = {};


                        detectionInfoLock.lock();
                        /* 在现有的列表里找下  如果有修改对应的值 没有就添加到列表里 */
                        for (std::size_t i = 0; i < curDetectionResultList.size(); i++) {
                            switch (curDetectionResultList[i].curCameraType.installPosition) {
                                case INSTALL_POSITION_LEFT: {
                                    hasBSDInfo = true;
                                    uartDetectionInfoBsd.leftAlarmArea1Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.left_level1;
                                    uartDetectionInfoBsd.leftAlarmArea2Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.left_level2;
                                    uartDetectionInfoBsd.leftCoverStatus |= (curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.left);
//                            printf("cameraCoverStatus.left=%d   leftCoverStatus=%d \n",curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.left,uartDetectionInfoBsd.leftCoverStatus);
                                    uartDetectionInfoBsd.rightBypassStatus |= curDetectionResultList[i].bsdDetectionInfo.bypassStatus.left;

                                }
                                    break;

                                case INSTALL_POSITION_RIGHT: {
                                    hasBSDInfo = true;
                                    uartDetectionInfoBsd.rightAlarmArea1Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.right_level1;
                                    uartDetectionInfoBsd.rightAlarmArea2Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.right_level2;
                                    uartDetectionInfoBsd.rightCoverStatus |= curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.right;
//                            printf("cameraCoverStatus.right=%d   rightCoverStatus=%d \n",curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.right,uartDetectionInfoBsd.rightCoverStatus);
                                    uartDetectionInfoBsd.rightBypassStatus |= curDetectionResultList[i].bsdDetectionInfo.bypassStatus.right;
                                }
                                    break;

                                case INSTALL_POSITION_FRONT: {
                                    hasBSDInfo = true;
                                    uartDetectionInfoBsd.forwardAlarmArea1Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.forward_level1;
                                    uartDetectionInfoBsd.forwardAlarmArea2Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.forward_level2;
                                    uartDetectionInfoBsd.forwardCoverStatus |=  curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.forward;
//                            printf("cameraCoverStatus.right=%d   rightCoverStatus=%d \n",curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.right,uartDetectionInfoBsd.rightCoverStatus);
                                    uartDetectionInfoBsd.forwardBypassStatus |= curDetectionResultList[i].bsdDetectionInfo.bypassStatus.forward;
                                }
                                    break;

                                case INSTALL_POSITION_BACK: {
                                    hasBSDInfo = true;
                                    uartDetectionInfoBsd.backwardAlarmArea1Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.backward_level1;
                                    uartDetectionInfoBsd.backwardAlarmArea2Status |= curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.backward_level2;
                                    uartDetectionInfoBsd.backwardCoverStatus |= curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.backward;
//                            printf("cameraCoverStatus.right=%d   rightCoverStatus=%d \n",curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.right,uartDetectionInfoBsd.rightCoverStatus);
                                    uartDetectionInfoBsd.backwardBypassStatus |= curDetectionResultList[i].bsdDetectionInfo.bypassStatus.backward;
                                }
                                    break;


                            }


                            /* 整理一下灯控状态 */
                            if (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4GJ_UNIT_G3) {
                                switch (curDetectionResultList[i].curCameraType.cameraId) {
                                    case CAMERA_ID_1: {

                                        switch (curDetectionResultList[i].lampType) {
                                            case 0: {
                                                channelControl.lamp1_open_norma1 |= curDetectionResultList[i].isLampOpen;
                                            }
                                                break;
                                            case 1: {
                                                channelControl.lamp_open_infrared1 |= curDetectionResultList[i].isLampOpen;
                                            }
                                                break;
                                        }
                                    }
                                        break;
                                    case CAMERA_ID_2: {
                                        switch (curDetectionResultList[i].lampType) {
                                            case 0: {
                                                channelControl.lamp1_open_norma2 |= curDetectionResultList[i].isLampOpen;
                                            }
                                                break;
                                            case 1: {
                                                channelControl.lamp_open_infrared2 |= curDetectionResultList[i].isLampOpen;
                                            }
                                                break;
                                        }
                                    }
                                        break;
                                    case CAMERA_ID_3: {
                                        //NOTE 暂时不支持
                                        ; //not to do
                                    }
                                        break;
                                    case CAMERA_ID_4: {
                                        //NOTE 暂时不支持
                                        ; //not to do
                                    }
                                        break;

                                }
                            }
                        }
                        /* 添加一下室内定位信息 */
                        if(indoorLocationSendCount < MAX_INDOORLOCATION_SEND_COUNT){
                            uartDetectionInfo.addIndoorLocation(indoorLocation);
                            indoorLocationSendCount ++;
                        }
                        detectionInfoLock.unlock();

                        if (hasBSDInfo) {
                            uartDetectionInfo.addBSDInfo(uartDetectionInfoBsd);
                        }

                        /* 添加一下灯控制信息 */
                        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4GJ_UNIT_G3) {
                            G3AndMCUUartDetectionInfo_LampControl lampControl;
                            channelControl.lamp1_open_norma1 = true;
                            channelControl.lamp1_open_norma2 = true;
                            lampControl.setChannelControl1(channelControl);
                            uartDetectionInfo.addLampControl(lampControl);
                        }


                        int datal = encoder.generateDetectionInfo(uartPacketdata, sizeof(uartPacketdata),
                                                                  uartDetectionInfo);
                        int senddatal = CodeUtils::getInstance().doEscape4SendFromMCUUart(uartPacketdata, datal, 1,
                                                                                          datal - 2,
                                                                                          uartdata);

//        printf("send to MCU data:");
//        for(int i = 0; i < datal; i ++){
//            printf("%02x ",uartPacketdata[i]);
//        }
//        printf("\n   in1Level=%d   in2Level=%d   in3Level=%d  \n",in1Level,in2Level,in3Level);

                        callback->onNeedSendDataToMCUUART(uartdata, senddatal);
                        usleep(1000 * 100);

                    }


                }
            }
        }
        pthread_setname_np(pthread_self(), "Finish");

    }

    void G3AndMCUUartManager::onGetUniversalAnswerFromMCUUART(G3AndMCUUartUniversalAnswer &universalAnswer) {
    }

    void G3AndMCUUartManager::onGetRealtimeVehicleStatusFromMCUUART(G3AndMCUUartVehicleRealTimeStatus &vehicleStatus) {
        /* 拿下CAN的参数 */
        callback->onGetRealtimeVehicleStatusFromMCUUART(vehicleStatus);

        /* 先判断下声音控制是否有内容  有内容则需要播放 */
        if (vehicleStatus.getSoundControl().soundInfo.soundId != 0) {
            /* 如果是高优先级的  则打断当前播放的   不是的话正常播放 */
            if (vehicleStatus.getSoundControl().soundInfo.level == 1) {
                int soundId = 0;
                memcpy(&soundId, &vehicleStatus.getSoundControl().soundInfo, 1);
                callback->onNeedPlaySound_JKFZ(getSoundFilePath(DEVICE_MODE_JFFZ_BYD, soundId).c_str(), true);
            } else {
                callback->onNeedPlaySound_JKFZ(
                        getSoundFilePath(DEVICE_MODE_JFFZ_BYD,
                                         vehicleStatus.getSoundControl().soundInfo.soundId).c_str(),
                        false);
            }

        }
    }

    void
    G3AndMCUUartManager::onGetPeripheralVersionFromMCUUART(G3AndMCUUartRespondPeripheralVersion &peripheralVersion) {
//    printf("G3AndMCUUartManager::onGetPeripheralVersionFromMCUUART SoftwareVersion=%02x%02x%02x%02x  ProtocolVersion=%02x%02x%02x%02x\n",peripheralVersion.getSoftwareVersion()[0],peripheralVersion.getSoftwareVersion()[1],peripheralVersion.getSoftwareVersion()[2],peripheralVersion.getSoftwareVersion()[3],peripheralVersion.getProtocolVersion()[0],peripheralVersion.getProtocolVersion()[1],peripheralVersion.getProtocolVersion()[2],peripheralVersion.getProtocolVersion()[3]);

        callback->onGetPeripheralVersionFromMCUUART(peripheralVersion);

//        char softwareVersionStr[5] = {0x00};
//        memcpy(softwareVersionStr, peripheralVersion.getSoftwareVersion(), 4);
//        /* 把字符串的版本号转成数字的 */
//        memset(softwareVersionStr, 0x00, sizeof(softwareVersionStr));
//        softwareVersionStr[0] = peripheralVersion.getSoftwareVersion()[0];
//        softwareVersionStr[1] = peripheralVersion.getSoftwareVersion()[1];
//        softwareVersionStr[2] = peripheralVersion.getSoftwareVersion()[3];
//
//        char protocolVersionStr[4] = {0x00};
//        protocolVersionStr[0] = peripheralVersion.getProtocolVersion()[0];
//        protocolVersionStr[1] = peripheralVersion.getProtocolVersion()[1];
//        protocolVersionStr[2] = peripheralVersion.getProtocolVersion()[3];
//
//        int softwareVersion = 0;
//        int protocolVersion = 0;
//        /* 如果是在升级区需要升级  发送升级包 */
//        if (strcmp("G3UP", softwareVersionStr) == 0) {
//            softwareVersion = 0;
//            protocolVersion = 0;
//        } else {
//            softwareVersion = std::atoi(softwareVersionStr);
//            protocolVersion = std::atoi(protocolVersionStr);
//        }
//
//        /* 检查是否需要升级 */
//        if (!checkNeedUpgrade(peripheralVersion.getPeripheralId(), softwareVersion, protocolVersion)) {
//            upgradeing = false;
//        } else {
//            /* 需要升级  发送升级包 */
//            startUpgrade(peripheralVersion.getPeripheralId());
//        }

        hasVersion = true;


    }

    void G3AndMCUUartManager::onGetGetPeripheralUpgradeDataFromMCUUART(
            G3AndMCUUartGetPeripheralUpgradeData &getPeripheralUpgradeData) {

        /* 获取升级文件了  那么就给发送出去 */
        if (upgradeFileDataLen > 0) {
            G3AndMCUUartSendPeripheralUpgradeData sendPeripheralUpgradeData;
            sendPeripheralUpgradeData.setFlowId(getPeripheralUpgradeData.getFlowId());
            sendPeripheralUpgradeData.setPeripheralId(getPeripheralUpgradeData.getPeripheralId());
            sendPeripheralUpgradeData.setOffset(getPeripheralUpgradeData.getOffset());

            int needSendFileDataLen = upgradeFileDataLen - getPeripheralUpgradeData.getOffset();
            if (needSendFileDataLen >= 252) {
                /* 剩下的数据够252  直接读252 */
                needSendFileDataLen = 252;
                sendPeripheralUpgradeData.setFileData(upgradeFileData + getPeripheralUpgradeData.getOffset(),
                                                      needSendFileDataLen);
            } else if (needSendFileDataLen <= 0) {
                /* 没剩下数据了  直接发送和校验 */
                needSendFileDataLen = 1;
                int sumCheck = 0;
                for (int i = 0; i < upgradeFileDataLen; i++) {
                    sumCheck += upgradeFileData[i];
                }
                sendPeripheralUpgradeData.setOffset(0xFFFFFFFF);
                uint8_t sumCheckCode[1] = {static_cast<uint8_t>((sumCheck & 0xFF))};
                sendPeripheralUpgradeData.setFileData(sumCheckCode, sizeof(sumCheckCode));
            } else {
                /* 剩下的数据 <252  && >0   就发实际剩下的  */
                sendPeripheralUpgradeData.setFileData(upgradeFileData + getPeripheralUpgradeData.getOffset(),
                                                      needSendFileDataLen);
            }
            int dataLen = encoder.generateSendPeripheralUpgradeData(uartUpgradePacketdata,
                                                                    sizeof(uartUpgradePacketdata),
                                                                    sendPeripheralUpgradeData);
            int senddatal = CodeUtils::getInstance().doEscape4SendFromMCUUart(uartUpgradePacketdata, dataLen, 1,
                                                                              dataLen - 2,
                                                                              uartUpgradedata);


//            printf("send to G3 and MCU uart %d bytes for send upgrade data ,content:\n", dataLen);
//            for (int i = 0; i < dataLen; i++) {
//                printf("%02x ", uartUpgradePacketdata[i]);
//            }
//            printf("\n");
            callback->onNeedSendDataToMCUUART(uartUpgradedata, senddatal);
        } else {
            printf("upgradeFileDataLen < 0   upgradeFileDataLen=%d \n", upgradeFileDataLen);

        }


    }

    void G3AndMCUUartManager::onGetPeripheralUpgradeResultFromMCUUART(
            G3AndMCUUartPeripheralUpgradeResult &peripheralUpgradeResult) {

        /* 当收到结果后  不论成功失败  直接退出更新 */
        upgradeing = false;
        /* 改成持续版本号，直到获取成功 */
        hasVersion = false;

        callback->onGetPeripheralUpgradeResultFromMCUUART(peripheralUpgradeResult);



    }

    std::string G3AndMCUUartManager::getSoundFilePath(int deviceMode, int soundId) {
        std::string filePath;
        switch (deviceMode) {
            case DEVICE_MODE_JFFZ_BYD: {
                filePath.append("/userdata/soundFile/");
            }
                break;
        }

        switch (soundId) {
            case 0x01: {
                filePath.append("pdw_b.pcm");
            }
                break;


            case 0x02: {
                filePath.append("pdw_r.pcm");
            }
                break;


            case 0x03: {
                filePath.append("pdw_l.pcm");
            }
                break;

            case 0x04: {
                filePath.append("adas_error.pcm");
            }
                break;
            case 0x05: {
                filePath.append("system_warring.pcm");
            }
                break;
            case 0x81: {
                filePath.append("stop_b.pcm");
            }
                break;


            case 0x82: {
                filePath.append("stop_l.pcm");
            }
                break;


            case 0x83: {
                filePath.append("stop_r.pcm");
            }
                break;

            case 0x84: {
                filePath.append("system_error.pcm");
            }
                break;
            case 0x85: {
                filePath.append("power_off.pcm");
            }
                break;


        };


        return filePath;
    }

    void G3AndMCUUartManager::setIOStatus(const int portNum, const char *value, const int valueLen) {
        switch (portNum) {
            case IO_IN_1_NUM: {
                in1Level = std::atoi(value) ? 0 : 1;
            }
                break;
            case IO_IN_2_NUM: {
                in2Level = std::atoi(value) ? 0 : 1;
            }
                break;
            case IO_IN_3_NUM: {
                in3Level = std::atoi(value) ? 0 : 1;
            }
                break;
        }
    }


    bool G3AndMCUUartManager::checkNeedUpgrade(int peripheralId, int softwareVersion, int protocolVersion) {
        bool ret = false;

        std::string upgradeFile = "/mnt/sdcard/upgrade/mcu/";
        /* 先看看TF卡里面有没有东西  没有东西直接换本地自带的 */
        if (XuFile::getInstance().fileExists(upgradeFile.c_str()) &&
            !XuFile::getInstance().getDirAllFile(upgradeFile.c_str(), "bin").empty()) { ; //not to do
        } else {
            upgradeFile.clear();
            upgradeFile.append("/userdata/upgradeFile/mcu/");
        }
        if (XuFile::getInstance().fileExists(upgradeFile.c_str())) {
            std::vector<std::string> fileList = XuFile::getInstance().getDirAllFile(upgradeFile.c_str(), "bin");
            if (fileList.size() > 0) {
                /* 只取第一个的版本号 */
                int fileVersion = 9999999;
                fileVersion = std::atoi(XuString::getInstance().split(fileList[0], "_")[1].c_str());

                /* 版本号大的话就需要升级 */
                if (fileVersion > softwareVersion) {
                    upgradeFile.append(fileList[0]);
                    FILE *fileFd = fopen(upgradeFile.c_str(), "rb+");
                    if (fileFd) {
                        /* 需要去掉前面30个字节  */
                        fseek(fileFd, 30, SEEK_SET);
                        /* 读出真正的数据 */
                        upgradeFileDataLen = fread(upgradeFileData, 1, 1024 * 1024 * 2, fileFd);
                        fclose(fileFd);
                        if (upgradeFileDataLen > 0) {
                            ret = true;
                        }
                    }
                }
            }
        }
        return ret;
    }

    void G3AndMCUUartManager::startUpgrade(int peripheralId) {
        upgradeing = true;
        uint8_t uartPacketdata[1024];
        memset(uartPacketdata, 0x00, sizeof(uartPacketdata));

        uint8_t uartdata[2048];
        memset(uartdata, 0x00, sizeof(uartdata));

        G3AndMCUUartStartPeripheralUpgrade startPeripheralUpgrade;
        startPeripheralUpgrade.setPeripheralId(peripheralId);

        int dataLen = encoder.generateStartPeripheralUpgrade(uartPacketdata, sizeof(uartPacketdata),
                                                             startPeripheralUpgrade);
        int senddatal = CodeUtils::getInstance().doEscape4SendFromMCUUart(uartPacketdata, dataLen, 1, dataLen - 2,
                                                                          uartdata);
        callback->onNeedSendDataToMCUUART(uartdata, senddatal);

//        printf("startUpgrade   send to G3 and MCU uart %d bytes,content:\n", senddatal);
//        for (int i = 0; i < senddatal; i++) {
//            printf("%02x ", uartdata[i]);
//        }
//        printf("\n");
    }

    void G3AndMCUUartManager::setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
        detectionInfoLock.lock();
        /* 在现有的列表里找下  如果有修改对应的值 没有就添加到列表里 */
        bool isInList = false;
        for (std::size_t i = 0; i < curDetectionResultList.size(); i++) {
            if (curDetectionResultList[i].curCameraType.cameraId == detectionResult.curCameraType.cameraId) {
                isInList = true;
                curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.left_level1 = detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1;
                curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.left_level2 = detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2;
                curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.right_level1 = detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1;
                curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.right_level2 = detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2;
                curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.backward_level1 = detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level1;
                curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.backward_level2 = detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level2;
                curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.forward_level1 = detectionResult.bsdDetectionInfo.pedestrianStatus.forward_level1;
                curDetectionResultList[i].bsdDetectionInfo.pedestrianStatus.forward_level2 = detectionResult.bsdDetectionInfo.pedestrianStatus.forward_level2;

                curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.left = detectionResult.bsdDetectionInfo.cameraCoverStatus.left;
                curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.right = detectionResult.bsdDetectionInfo.cameraCoverStatus.right;
                curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.forward = detectionResult.bsdDetectionInfo.cameraCoverStatus.forward;
                curDetectionResultList[i].bsdDetectionInfo.cameraCoverStatus.backward = detectionResult.bsdDetectionInfo.cameraCoverStatus.backward;

                curDetectionResultList[i].bsdDetectionInfo.bypassStatus.left = detectionResult.bsdDetectionInfo.bypassStatus.left;
                curDetectionResultList[i].bsdDetectionInfo.bypassStatus.right = detectionResult.bsdDetectionInfo.bypassStatus.right;
                curDetectionResultList[i].bsdDetectionInfo.bypassStatus.forward = detectionResult.bsdDetectionInfo.bypassStatus.forward;
                curDetectionResultList[i].bsdDetectionInfo.bypassStatus.backward = detectionResult.bsdDetectionInfo.bypassStatus.backward;
            }
        }

        if (!isInList) {
            curDetectionResultList.push_back(detectionResult);
        }

        detectionInfoLock.unlock();
    }

    void
    G3AndMCUUartManager::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
        uint8_t uartPacketdata[1024];
        memset(uartPacketdata, 0x00, sizeof(uartPacketdata));

        uint8_t uartdata[2048];
        memset(uartdata, 0x00, sizeof(uartdata));

        G3AndMCUUartAlarmInfo uartAlarmInfo;


        G3AndMCUUartAlarmInfo_BSD bsdEvent;


        switch (eventCode) {
            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventCode(G3AndMCUUartAlarmInfo_BSD::EVENT_TYPE_LEFT_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(G3AndMCUUartAlarmInfo_BSD::EVENT_LEVEL_LEVEL1);
                uartAlarmInfo.addBSDInfo(bsdEvent);
            }
                break;

            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventCode(G3AndMCUUartAlarmInfo_BSD::EVENT_TYPE_RIGHT_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(G3AndMCUUartAlarmInfo_BSD::EVENT_LEVEL_LEVEL1);
                uartAlarmInfo.addBSDInfo(bsdEvent);
            }
                break;


            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventCode(G3AndMCUUartAlarmInfo_BSD::EVENT_TYPE_LEFT_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(G3AndMCUUartAlarmInfo_BSD::EVENT_LEVEL_LEVEL2);
                uartAlarmInfo.addBSDInfo(bsdEvent);
            }
                break;

            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventCode(G3AndMCUUartAlarmInfo_BSD::EVENT_TYPE_RIGHT_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(G3AndMCUUartAlarmInfo_BSD::EVENT_LEVEL_LEVEL2);
                uartAlarmInfo.addBSDInfo(bsdEvent);
            }
                break;

            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventCode(G3AndMCUUartAlarmInfo_BSD::EVENT_TYPE_FORWARD_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(G3AndMCUUartAlarmInfo_BSD::EVENT_LEVEL_LEVEL1);
                uartAlarmInfo.addBSDInfo(bsdEvent);
            }
                break;

            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventCode(G3AndMCUUartAlarmInfo_BSD::EVENT_TYPE_FORWARD_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(G3AndMCUUartAlarmInfo_BSD::EVENT_LEVEL_LEVEL2);
                uartAlarmInfo.addBSDInfo(bsdEvent);
            }
                break;


            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventCode(G3AndMCUUartAlarmInfo_BSD::EVENT_TYPE_BACKWARD_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(G3AndMCUUartAlarmInfo_BSD::EVENT_LEVEL_LEVEL1);
                uartAlarmInfo.addBSDInfo(bsdEvent);
            }
                break;

            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
                bsdEvent.setEventStatus(0x00);
                bsdEvent.setEventCode(G3AndMCUUartAlarmInfo_BSD::EVENT_TYPE_BACKWARD_PEDESTRIAN);
                bsdEvent.setDistance(alarmEventInfo.bsdAlarmInfo.yDistance);
                bsdEvent.setEventLevel(G3AndMCUUartAlarmInfo_BSD::EVENT_LEVEL_LEVEL2);
                uartAlarmInfo.addBSDInfo(bsdEvent);
            }
                break;

        }


        int datal = encoder.generateAlarmEvent(uartPacketdata, sizeof(uartPacketdata), uartAlarmInfo);
        int senddatal = CodeUtils::getInstance().doEscape4SendFromRS485(uartPacketdata, datal, 1, datal - 2, uartdata);

        callback->onNeedSendDataToMCUUART(uartdata, senddatal);


//    printf("send to G3 and MCU uart %d bytes,content:\n", datal);
//    for (int i = 0; i < senddatal; i++) {
//        printf("%02x ", uartPacketdata[i]);
//    }
//    printf("\n");

    }

    int G3AndMCUUartManager::setUpgradeMCU(std::string &upgradeFilePath) {
        int ret = -1;
        if (XuFile::getInstance().fileExists(upgradeFilePath.c_str())) {
            FILE *fileFd = fopen(upgradeFilePath.c_str(), "rb+");
            if (fileFd) {
                /* 需要去掉前面30个字节  */
                fseek(fileFd, 30, SEEK_SET);
                /* 读出真正的数据 */
                upgradeFileDataLen = fread(upgradeFileData, 1, 1024 * 1024 * 2, fileFd);
                fclose(fileFd);
                if (upgradeFileDataLen > 0) {
                    ret = true;
                }
            }
            startUpgrade(G3AndMCUUartPacket::PERIPHERAL_ID_MCU);
            ret = 0;
        }
        return ret;


    }

    void G3AndMCUUartManager::onGetProductionTestResultFromMCUUART(G3AndMCUUartPTWrokResult &ptWrokResult) {
        needProductionTest = false;
        callback->onGetProductionTestResultFromMCUUART(ptWrokResult);
    }

    void G3AndMCUUartManager::setDetectInfo_BarCode(G3DetectionResult &detectionResult,
                                                    std::vector<BarCodeInfo> &barCodeInfoList) {
        /* 先看看是不是有识别到 */
        if(!barCodeInfoList.empty()){
            int indexOfValid = -1;
            /* 有识别信息，获取下可用信息的下标 */
            for(std::size_t i = 0; i < barCodeInfoList.size(); i ++){
                if(barCodeInfoList[i].isCodeInfoAvailable){
                    indexOfValid = i;
                    break;
                }
            }
            /* 看看是不是有可用信息 */
            if(indexOfValid >= 0){
                detectionInfoLock.lock();

                /* 有可用信息了，那么就开始取 条码类型 */
                switch (barCodeInfoList[indexOfValid].barcodeType) {
                    case BARCODE_TYPE_QRCODE:{
                        indoorLocation.setCodeType(G3AndMCUUartDetectionInfo_IndoorLocation::CODE_TYPE_QRCODE);
                    }
                        break;
                    case BARCODE_TYPE_CODE128:{
                        indoorLocation.setCodeType(G3AndMCUUartDetectionInfo_IndoorLocation::CODE_TYPE_CODE128);
                    }
                        break;
                }
                /* 分割一下信息 */
                std::vector<std::string> keyInfos = XuString::getInstance().split(barCodeInfoList[indexOfValid].code_key_info,"-");
                /* 看看分割出来的长度对不对 */
                if(keyInfos.size() >= 2){
                    /* 分割出来的长度对，那么先取 定位点编号 */
                    indoorLocation.setAnchorPointNum(std::atoi(keyInfos[1].c_str()));
                    /* 仓库编号 */
                    indoorLocation.setWarehouseNum(std::atoi(keyInfos[0].c_str()));
                    /* 重置发送次数 */
                    indoorLocationSendCount = 0;
                }

                detectionInfoLock.unlock();
            }
        }


    }

}