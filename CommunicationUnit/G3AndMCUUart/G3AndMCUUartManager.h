//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/15.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTMANAGER_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTMANAGER_H


#include "G3AndMCUUartDecoder.h"
#include "G3AndMCUUartEncoder.h"
#include <Poco/Runnable.h>

#include <mutex>
#include "tdShareDefine.h"
#include <mutex>
#include "G3DetectionDefind.h"
#include "G3_Configuration.h"
#include "XuFile.h"
#include "XuString.h"

namespace vis {

    class G3AndMCUUartManager : public Poco::Runnable, public CommunicationDataCallback {
    public:
        const static int DEVICE_MODE_JFFZ_BYD = 0;


        void init(CommunicationDataCallback &communicationDataCallback);

        void run() override;

        /**
          * 设置从识别模块拿到的识别信息
          *
          * @param objectInfo ： 识别信息的结构体
          * @param detectionResult : 整理过后的识别结果
          *
          * */
        void setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

        /**
         * 设置从识别模块拿到的报警事件
         *
         * @param cameraId : 相机ID
         * @param detectType : 识别算法类型
         * @param eventCode ： 事件码
         *
         * */
        void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

        /**
         * 设置跟MCU的Uart发过来的数据
         *
         * @param buf : 数据内存指针
         * @param len : 数据长度
         *
         * */
        void setDataFromMCUUart(const uint8_t *buf, int len);

        /**
         * 获取声音ID对应的声音文件的路径
         *
         * @param deviceMode ： 接入的设备的类型
         * @param soundId ： 声音ID
         *
         * */
        std::string getSoundFilePath(int deviceMode, int soundId);


        /**
         * 设置IO口状态
         *
         * @param portNum ： IO口编码
         * @param value ： 电平内容
         * @param valueLen ： 电平内容长度
         *
         * */
        void setIOStatus(const int portNum, const char *value, const int valueLen);

        /**
         * 检查是否需要升级
         *
         * @return 是否需要升级
         * */
        bool checkNeedUpgrade(int peripheralId, int softwareVersion, int srotocolVersion);

        /**
         * 开启升级
         *
         * @param peripheralId : 外设ID
         *
         * */
        void startUpgrade(int peripheralId);


        /**
         * 强制升级一下MCU软件
         * @param upgradeFilePath : 升级文件路径
         *
         * @return 结果  0：成功  其他：失败
         */
        int setUpgradeMCU(std::string &upgradeFilePath);

        /**
         * 设置从识别模块拿到的识别信息(条码)
         *
         * @param detectionInfo ： 封装好的识别结果
         * @param barCodeInfoList : 条码信息列表
         *
         * */
        void setDetectInfo_BarCode(G3DetectionResult &detectionResult, std::vector<BarCodeInfo> &barCodeInfoList);



        /* 获取到从跟MCU的UART来的通用应答包 */
        void onGetUniversalAnswerFromMCUUART(G3AndMCUUartUniversalAnswer &universalAnswer) override;

        /* 获取到从跟MCU的UART来的车况信息 */
        void onGetRealtimeVehicleStatusFromMCUUART(G3AndMCUUartVehicleRealTimeStatus &vehicleStatus) override;

        /* 获取到一个跟MCU的UART来的外设的版本号 */
        void onGetPeripheralVersionFromMCUUART(G3AndMCUUartRespondPeripheralVersion &peripheralVersion) override;

        /* 获取到一个跟MCU的UART来的获取外设升级数据的请求 */
        void
        onGetGetPeripheralUpgradeDataFromMCUUART(
                G3AndMCUUartGetPeripheralUpgradeData &getPeripheralUpgradeData) override;

        /* 获取到一个跟MCU的UART来的获取外设升级结果 */
        void
        onGetPeripheralUpgradeResultFromMCUUART(G3AndMCUUartPeripheralUpgradeResult &peripheralUpgradeResult) override;

        void onGetProductionTestResultFromMCUUART(G3AndMCUUartPTWrokResult &ptWrokResult) override;


    private:
        bool isNeedStop = true;
        CommunicationDataCallback *callback;
        G3AndMCUUartDecoder decoder;
        G3AndMCUUartEncoder encoder;
        G3AndMCUUartDetectionInfo detectionInfo;
        /* 室内定位的信息 */
        G3AndMCUUartDetectionInfo_IndoorLocation indoorLocation;
        /* 室内定位的信息的发送次数 */
        int indoorLocationSendCount = 0;
        /* 最大的室内定位的信息的发送次数 */
        const int MAX_INDOORLOCATION_SEND_COUNT = 3;
        /* */
        std::vector<G3DetectionResult> curDetectionResultList;
        std::mutex detectionInfoLock;


        /* 是否正在升级中 */
        bool upgradeing = false;

        /* 是否让MCU进行生产测试 */
        bool needProductionTest = true;


        uint8_t uartUpgradePacketdata[700] = {0x00};

        uint8_t uartUpgradedata[1400] = {0x00};

        uint8_t *upgradeFileData;

        int upgradeFileDataLen = -1;


        /* 是否查询到了版本号 */
        bool hasVersion = false;


        /* IO  IN1的电平 （IO115）*/
        int in1Level = 1;
        /* IO  IN2的电平 （IO111）*/
        int in2Level = 1;
        /* IO  IN3的电平 （IO110）*/
        int in3Level = 1;


    };

}

#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTMANAGER_H
