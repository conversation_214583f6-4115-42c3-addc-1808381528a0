//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#include "G3AndMCUUartStartPTWrok.h"

G3AndMCUUartStartPTWrok::G3AndMCUUartStartPTWrok() {
    setMsgId(MSGID_START_PRODUCTION_TEST_WORK);
}

G3AndMCUUartStartPTWrok::~G3AndMCUUartStartPTWrok() {

}

int G3AndMCUUartStartPTWrok::addCANtestList(G3AndMCUUartStartPTWrok_CANTest &canTest) {
    cantestList.push_back(canTest);
    return 0;
}

int G3AndMCUUartStartPTWrok::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 5;
    int msgContent = 0;
    /* 先随便塞个内容长度 */
    CodeUtils::getInstance().uint16ToBb(0, buf + index);
    index += 2;
    /* 数据项总数 */
    itemSum = cantestList.size();
    buf[index] = itemSum;
    index++;
    msgContent++;

    /* 先塞CAN接口测试数据项 */
    if (cantestList.size() > 0) {
        for (std::size_t i = 0; i < cantestList.size(); i++) {
            int itemLen = cantestList[i].toCode(buf + index, len);
            index += itemLen;
            msgContent += itemLen;
        }
    }

    /* 把真实的内容长度塞进去 */
    setContentLen(msgContent);
    CodeUtils::getInstance().uint16ToBb(getContentLen(), buf + 5);
    index += 2;
    /* 加上头尾那些东西 */
    ret = G3AndMCUUartPacket::toCode(buf, len);

    return ret;
}

uint8_t G3AndMCUUartStartPTWrok::getItemSum() const {
    return itemSum;
}
