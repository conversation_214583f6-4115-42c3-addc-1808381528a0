//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/20.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTALARMINFO_DSM_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTALARMINFO_DSM_H

#include <cstdint>
#include "CodeUtils.h"

class G3AndMCUUartAlarmInfo_DSM {
public:

    struct RS485DSMAlarmType1{
        /* Bit0	一级疲劳驾驶报警 */
        bool tired_level1 : 1;
        /* Bit1	接打电话报警 */
        bool phone : 1;
        /* Bit2	抽烟报警 */
        bool smoking : 1;
        /* Bit3	分神驾驶报警 */
        bool lookAround : 1;
        /* Bit4	驾驶员异常报警 */
        bool faceMiss : 1;
        /* Bit5	二级疲劳驾驶报警 */
        bool tired_level2 : 1;
        /* Bit6	打哈欠 */
        bool yawn : 1;
        /* Bit7	驾驶员变更事件 */
        bool driverChange : 1;
        /* Bit8	主动抓拍事件 */
        bool captureEvent : 1;
        /* bit9	闭眼报警 */
        bool eyeClost : 1;
        /* bit10	离岗报警 */
        bool faceMissing : 1;
        /* bit11	遮挡报警 */
        bool camCover : 1;
        /* bit12	安全带报警 */
        bool seatbelt : 1;
        /* bit13	看盲点报警 */
        bool blindSpot : 1;
        /* Bit14-Bit31	保留 */
        int reserve : 18;
    };

    int toCode(uint8_t *buf, int len);

    uint8_t getEventStatus() const;

    void setEventStatus(uint8_t eventStatus);

    const RS485DSMAlarmType1 &getEventCode() const;

    void setEventCode(const RS485DSMAlarmType1 &eventCode);

    const uint8_t *getReserved() const;

private:
    uint16_t itemId = 0x0065;
    uint8_t itemContentLen = 7;

    /* 标志状态	uint8_t	0x00：不可用  0x01：开始标志  0x02：结束标志  该字段仅适用于有开始和结束标志类型的报警或事件，报警类型或事件类型无开始和结束标志，则该位不可用，填入0x00即可 */
    uint8_t eventStatus;
    /* 报警/事件类型	*/
    RS485DSMAlarmType1 eventCode = {0};
    /* 预留 */
    uint8_t reserved[2] = {0x00};

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTALARMINFO_DSM_H
