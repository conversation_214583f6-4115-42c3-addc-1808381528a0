//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/18.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_H

#include "G3AndMCUUartPacket.h"
#include "G3AndMCUUartDetectionInfo_ADAS.h"
#include "G3AndMCUUartDetectionInfo_BSD.h"
#include "G3AndMCUUartDetectionInfo_SYSStatus.h"
#include "G3AndMCUUartDetectionInfo_IOStatus.h"
#include "G3AndMCUUartDetectionInfo_LampControl.h"
#include "G3AndMCUUartDetectionInfo_IndoorLocation.h"
#include <vector>

class G3AndMCUUartDetectionInfo : public G3AndMCUUartPacket {
public:
    G3AndMCUUartDetectionInfo();

    ~G3AndMCUUartDetectionInfo();

    int addADASInfo(G3AndMCUUartDetectionInfo_ADAS &adasInfo);

    int addBSDInfo(G3AndMCUUartDetectionInfo_BSD &bsdInfo);

    int addSysStatusInfo(G3AndMCUUartDetectionInfo_SYSStatus &sysStatusInfo);

    int addIOInfo(G3AndMCUUartDetectionInfo_IOStatus &ioStatus);

    int addLampControl(G3AndMCUUartDetectionInfo_LampControl &lampControl);

    int addIndoorLocation(G3AndMCUUartDetectionInfo_IndoorLocation &indoorLocation);

    int toCode(uint8_t *buf, int len) override;

private:
    uint8_t itemSum = 0;
    std::vector<G3AndMCUUartDetectionInfo_ADAS> adasInfoList;
    std::vector<G3AndMCUUartDetectionInfo_BSD> bsdInfoList;
    std::vector<G3AndMCUUartDetectionInfo_SYSStatus> sysStatusInfoList;
    std::vector<G3AndMCUUartDetectionInfo_IOStatus> ioInfoList;
    std::vector<G3AndMCUUartDetectionInfo_LampControl> lampControlList;
    std::vector<G3AndMCUUartDetectionInfo_IndoorLocation> indoorLocationList;
};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_H
