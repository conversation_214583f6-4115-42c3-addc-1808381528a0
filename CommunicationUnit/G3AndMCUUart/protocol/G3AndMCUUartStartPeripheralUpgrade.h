//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTSTARTPERIPHERALUPGRADE_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTSTARTPERIPHERALUPGRADE_H

#include "G3AndMCUUartPacket.h"

class G3AndMCUUartStartPeripheralUpgrade : public G3AndMCUUartPacket {
public:
    G3AndMCUUartStartPeripheralUpgrade();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTSTARTPERIPHERALUPGRADE_H
