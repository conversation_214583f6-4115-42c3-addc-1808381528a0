//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#include "G3AndMCUUartPeripheralUpgradeResult.h"

G3AndMCUUartPeripheralUpgradeResult::G3AndMCUUartPeripheralUpgradeResult() {
    setMsgId(MSGID_PERIPHERAL_UPGRADE_RESULT);

}

uint8_t G3AndMCUUartPeripheralUpgradeResult::getPeripheralId() const {
    return peripheralId;
}

void G3AndMCUUartPeripheralUpgradeResult::setPeripheralId(uint8_t peripheralId) {
    G3AndMCUUartPeripheralUpgradeResult::peripheralId = peripheralId;
}

uint8_t G3AndMCUUartPeripheralUpgradeResult::getUpgradeResult() const {
    return upgradeResult;
}

void G3AndMCUUartPeripheralUpgradeResult::setUpgradeResult(uint8_t upgradeResult) {
    G3AndMCUUartPeripheralUpgradeResult::upgradeResult = upgradeResult;
}

int G3AndMCUUartPeripheralUpgradeResult::decode(uint8_t *buf, int len) {
    int ret = -1;
    if (G3AndMCUUartPacket::decode(buf, len) == 0) {
        int index = 5;
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        setContentLen(CodeUtils::getInstance().BbToint16(buf + index));
        index += 2;
        /* 外设编号 */
        peripheralId = buf[index];
        index++;
        /* 升级结果 */
        upgradeResult = buf[index];
        index++;

        ret = 0;
    }
    return ret;
}
