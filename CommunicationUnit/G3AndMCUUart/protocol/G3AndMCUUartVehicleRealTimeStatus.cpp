//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/19.
//

#include <cstdio>
#include "G3AndMCUUartVehicleRealTimeStatus.h"
#include "XuString.h"

G3AndMCUUartVehicleRealTimeStatus::G3AndMCUUartVehicleRealTimeStatus() {
    setMsgId(MSGID_VEHICLE_REALTIME_STATUS);
}

int G3AndMCUUartVehicleRealTimeStatus::getBatteryVoltage() const {
    return batteryVoltage;
}

void G3AndMCUUartVehicleRealTimeStatus::setBatteryVoltage(int batteryVoltage) {
    G3AndMCUUartVehicleRealTimeStatus::batteryVoltage = batteryVoltage;
}

uint16_t G3AndMCUUartVehicleRealTimeStatus::getEngineSpeed() const {
    return engineSpeed;
}

void G3AndMCUUartVehicleRealTimeStatus::setEngineSpeed(uint16_t engineSpeed) {
    G3AndMCUUartVehicleRealTimeStatus::engineSpeed = engineSpeed;
}

float G3AndMCUUartVehicleRealTimeStatus::getSpeed() const {
    return speed;
}

void G3AndMCUUartVehicleRealTimeStatus::setSpeed(float speed) {
    G3AndMCUUartVehicleRealTimeStatus::speed = speed;
}

uint8_t G3AndMCUUartVehicleRealTimeStatus::getCoolantTemperature() const {
    return coolantTemperature;
}

void G3AndMCUUartVehicleRealTimeStatus::setCoolantTemperature(uint8_t coolantTemperature) {
    G3AndMCUUartVehicleRealTimeStatus::coolantTemperature = coolantTemperature;
}

float G3AndMCUUartVehicleRealTimeStatus::getInstantaneousFuelConsumption() const {
    return instantaneousFuelConsumption;
}

void G3AndMCUUartVehicleRealTimeStatus::setInstantaneousFuelConsumption(float instantaneousFuelConsumption) {
    G3AndMCUUartVehicleRealTimeStatus::instantaneousFuelConsumption = instantaneousFuelConsumption;
}

uint8_t G3AndMCUUartVehicleRealTimeStatus::getResidualOil() const {
    return residualOil;
}

void G3AndMCUUartVehicleRealTimeStatus::setResidualOil(uint8_t residualOil) {
    G3AndMCUUartVehicleRealTimeStatus::residualOil = residualOil;
}

const G3AndMCUUartVehicleStatus &G3AndMCUUartVehicleRealTimeStatus::getCarStatus() const {
    return carStatus;
}

void G3AndMCUUartVehicleRealTimeStatus::setCarStatus(const G3AndMCUUartVehicleStatus &carStatus) {
    G3AndMCUUartVehicleRealTimeStatus::carStatus = carStatus;
}

float G3AndMCUUartVehicleRealTimeStatus::getCurrentMileage() const {
    return currentMileage;
}

void G3AndMCUUartVehicleRealTimeStatus::setCurrentMileage(float currentMileage) {
    G3AndMCUUartVehicleRealTimeStatus::currentMileage = currentMileage;
}

uint16_t G3AndMCUUartVehicleRealTimeStatus::getTravelTime() const {
    return travelTime;
}

void G3AndMCUUartVehicleRealTimeStatus::setTravelTime(uint16_t travelTime) {
    G3AndMCUUartVehicleRealTimeStatus::travelTime = travelTime;
}

uint16_t G3AndMCUUartVehicleRealTimeStatus::getIdlingTime() const {
    return idlingTime;
}

void G3AndMCUUartVehicleRealTimeStatus::setIdlingTime(uint16_t idlingTime) {
    G3AndMCUUartVehicleRealTimeStatus::idlingTime = idlingTime;
}

uint32_t G3AndMCUUartVehicleRealTimeStatus::getAccumulativeOilConsumption() const {
    return AccumulativeOilConsumption;
}

void G3AndMCUUartVehicleRealTimeStatus::setAccumulativeOilConsumption(uint32_t accumulativeOilConsumption) {
    AccumulativeOilConsumption = accumulativeOilConsumption;
}

float G3AndMCUUartVehicleRealTimeStatus::getOdo() const {
    return ODO;
}

void G3AndMCUUartVehicleRealTimeStatus::setOdo(float odo) {
    ODO = odo;
}

uint32_t G3AndMCUUartVehicleRealTimeStatus::getCumulativeDrivingTime() const {
    return cumulativeDrivingTime;
}

void G3AndMCUUartVehicleRealTimeStatus::setCumulativeDrivingTime(uint32_t cumulativeDrivingTime) {
    G3AndMCUUartVehicleRealTimeStatus::cumulativeDrivingTime = cumulativeDrivingTime;
}

uint32_t G3AndMCUUartVehicleRealTimeStatus::getCumulativeIdlingTime() const {
    return cumulativeIdlingTime;
}

void G3AndMCUUartVehicleRealTimeStatus::setCumulativeIdlingTime(uint32_t cumulativeIdlingTime) {
    G3AndMCUUartVehicleRealTimeStatus::cumulativeIdlingTime = cumulativeIdlingTime;
}

const G3AndMCUUartGPSInfo &G3AndMCUUartVehicleRealTimeStatus::getGpsData() const {
    return gpsData;
}

void G3AndMCUUartVehicleRealTimeStatus::setGpsData(const G3AndMCUUartGPSInfo &gpsData) {
    G3AndMCUUartVehicleRealTimeStatus::gpsData = gpsData;
}

int G3AndMCUUartVehicleRealTimeStatus::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((G3AndMCUUartPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 5;
        setContentLen(CodeUtils::getInstance().BbToint16(buf + index));
        index += 2;

        // 数据总数
        int count = buf[index] & 0xff;
        index++;
        // 开始循环解析参数
        for (int i = 0; i < count; i++) {
            // 获取参数ID
            uint16_t paramId = CodeUtils::getInstance().BbToUint16(buf + index);
            index += 2;
            // 获取参数长度
            uint8_t paramLen = buf[index];
            index++;
            // 解析参数
            parseParam(paramId, buf + index);
            index += paramLen;
        }
        ret = 0;
    }
    return ret;
}

int G3AndMCUUartVehicleRealTimeStatus::parseParam(uint16_t paramId, uint8_t *data) {
    // 根据ID赋值给不同的变量
    switch (paramId) {
        case ID_BATTERTVOLTAGE: {
            // 电瓶电压
            batteryVoltage = (float) CodeUtils::getInstance().BbToUint16(data) / (float) 100;
        }
            break;
        case ID_ENGINESPEED: {
            // 发动机转速
            engineSpeed = CodeUtils::getInstance().BbToUint16(data);
        }
            break;
        case ID_SPEED: {
            // 车速
            int speedfm = CodeUtils::getInstance().BbToUint16(data);
            speed = (float) ((float) speedfm / (float) 10);
            if (speedfm == 65535 || speedfm == -1) {
                speed = -1;
            }
        }
            break;
        case ID_COOLANTTEMPERATURE: {
            // 冷却液温度
            coolantTemperature = data[0] & 0xff;
        }
            break;
        case ID_INSTANTANEOUSFUELCONSUMPTION: {
            // 瞬时油耗
            instantaneousFuelConsumption = (float) CodeUtils::getInstance().BbToUint16(data) / (float) 100;
        }
            break;
        case ID_RESIDUALOIL: {
            // 剩余油量
            residualOil = data[0] & 0xff;
        }
            break;
        case ID_CARSTATUS: {
            // 车辆状态
            uint16_t carStatusInt = CodeUtils::getInstance().BbToUint16(data);
            memcpy(&carStatus, &carStatusInt, 2);
        }
            break;

        case ID_CURRENTMILEAGE: {
            // 当前行驶里程
            currentMileage = (float) CodeUtils::getInstance().BbToUint16(data) / (float) 10;
        }
            break;
        case ID_TRAVELTIME: {
            // 本次行驶时间
            travelTime = CodeUtils::getInstance().BbToUint16(data);
        }
            break;
        case ID_IDLINGTIME: {
            // 本次怠速时间
            idlingTime = CodeUtils::getInstance().BbToUint16(data);
        }
            break;
        case ID_ACCUMULATIVEOILCONSUMPTION: {
            // 累计耗油量
            AccumulativeOilConsumption = CodeUtils::getInstance().BbToUint32(data);
        }
            break;
        case ID_ODO: {
            // 累计行驶里程
            ODO = (float) CodeUtils::getInstance().BbToUint32(data) / (float) 10;
        }
            break;
        case ID_CUMULATIVEDRIVINGTIME: {
            // 累计行驶时间
            cumulativeDrivingTime = CodeUtils::getInstance().BbToUint32(data);
        }
            break;
        case ID_CUMULATIVEIDLINGTIME: {
            // 累计怠速时间
            cumulativeIdlingTime = CodeUtils::getInstance().BbToUint32(data);
        }
            break;
        case ID_GPS: {
            // GPS 数据
            int index = 0;
            gpsData.gpsSpeed = (float) CodeUtils::getInstance().BbToUint16(data + index) / (float) 10;
            index += 2;
            gpsData.status = data[index];
            index++;
            gpsData.direction = CodeUtils::getInstance().BbToUint16(data + index);
            index += 2;
            gpsData.altitude = CodeUtils::getInstance().BbToUint16(data + index);
            index += 2;
            gpsData.latitude = (double) CodeUtils::getInstance().BbToUint32(data + index) / (double) 1000000;
            index += 4;
            gpsData.longitude = (double) CodeUtils::getInstance().BbToUint32(data + index) / (double) 1000000;
            index += 4;
            memcpy(gpsData.bcdTime, data + index, 6);
        }
            break;
        case ID_JKFZ_SOUND_CONTROL: {

            //printf("ID_JKFZ_SOUND_CONTROL:%02x %02x \n",data[0],data[1]);


            // 工程机械状态及语音控制
            soundControl.reset();
            int index = 0;
            memcpy(&soundControl.soundInfo, data + index, 1);
            index++;
            memcpy(&soundControl.constructionMachineryStatus, data + index, 1);
            index++;
            memcpy(&soundControl.reserved, data + index, 6);
            index += 6;


        }
            break;


        case ID_GJ_LAMP_CONTROL: {
            memcpy(&lampContrl.channelControl1,&data,1);
            memcpy(&lampContrl.channelControl2,&data[1],1);
            memcpy(&lampContrl.reserved,&data[2],2);

//            printf("ID_GJ_LAMP_CONTROL:%02x %02x %02x  %02x \n",data[0],data[1],data[2],data[3]);


        }
            break;





    }

    return 0;
}

const G3AndMCUUartJKFZSoundControl &G3AndMCUUartVehicleRealTimeStatus::getSoundControl() const {
    return soundControl;
}

void G3AndMCUUartVehicleRealTimeStatus::setSoundControl(const G3AndMCUUartJKFZSoundControl &soundControl) {
    G3AndMCUUartVehicleRealTimeStatus::soundControl = soundControl;
}
