//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/15.
//

#include "G3AndMCUUartUniversalAnswer.h"

G3AndMCUUartUniversalAnswer::G3AndMCUUartUniversalAnswer() {
    setMsgId(MSGID_UNIVERSALANSWER);
}

G3AndMCUUartUniversalAnswer::~G3AndMCUUartUniversalAnswer() {

}

int G3AndMCUUartUniversalAnswer::toCode(uint8_t *buf, int len) {
    int ret = -1;
    if (len >= (9 + 10)) {
        /* 塞内容长度 */
        int index = 5;
        setContentLen(2);
        CodeUtils::getInstance().int16ToBb(getContentLen(), buf + index);
        index += 2;
        /* 再塞应答的消息的命令字 */
        buf[index] = getQMsgId();
        index++;
        /* 再塞结果 */
        buf[index] = getResult();
        index++;
        /* 加上头尾那些东西 */
        ret = G3AndMCUUartPacket::toCode(buf, len);
    }
    return ret;
}

int G3AndMCUUartUniversalAnswer::decode(uint8_t *buf, int len) {
    int ret = -1;
    if (G3AndMCUUartPacket::decode(buf, len) == 0) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        setContentLen(CodeUtils::getInstance().BbToint32(buf + 6));
        setQMsgId(buf[10]);
        setResult(buf[11]);
        ret = 0;
    }
    return ret;
}

uint8_t G3AndMCUUartUniversalAnswer::getQMsgId() const {
    return qMsgId;
}

void G3AndMCUUartUniversalAnswer::setQMsgId(uint8_t qMsgId) {
    G3AndMCUUartUniversalAnswer::qMsgId = qMsgId;
}

uint8_t G3AndMCUUartUniversalAnswer::getResult() const {
    return result;
}

void G3AndMCUUartUniversalAnswer::setResult(uint8_t result) {
    G3AndMCUUartUniversalAnswer::result = result;
}


