//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/20.
//

#include "G3AndMCUUartAlarmInfo_BSD.h"

uint8_t G3AndMCUUartAlarmInfo_BSD::getEventStatus() const {
    return eventStatus;
}

void G3AndMCUUartAlarmInfo_BSD::setEventStatus(uint8_t eventStatus) {
    G3AndMCUUartAlarmInfo_BSD::eventStatus = eventStatus;
}

uint8_t G3AndMCUUartAlarmInfo_BSD::getEventCode() const {
    return eventCode;
}

void G3AndMCUUartAlarmInfo_BSD::setEventCode(uint8_t eventCode) {
    G3AndMCUUartAlarmInfo_BSD::eventCode = eventCode;
}

uint16_t G3AndMCUUartAlarmInfo_BSD::getDistance() const {
    return distance;
}

void G3AndMCUUartAlarmInfo_BSD::setDistance(uint16_t distance) {
    G3AndMCUUartAlarmInfo_BSD::distance = distance;
}

uint8_t G3AndMCUUartAlarmInfo_BSD::getEventLevel() const {
    return eventLevel;
}

void G3AndMCUUartAlarmInfo_BSD::setEventLevel(uint8_t eventLevel) {
    G3AndMCUUartAlarmInfo_BSD::eventLevel = eventLevel;
}

const uint8_t *G3AndMCUUartAlarmInfo_BSD::getReserved() const {
    return reserved;
}

int G3AndMCUUartAlarmInfo_BSD::toCode(uint8_t *buf, int len) {
    int ret = -1;

    int index = 0;
    /* 塞入ID */
    CodeUtils::getInstance().uint16ToBb(itemId, buf + index);
    index += 2;
    /* 塞入长度 */
    buf[index] = itemContentLen;
    index++;
    /* 塞入标志状态 */
    buf[index] = eventStatus;
    index++;
    /* 塞入报警/事件类型 */
    buf[index] = eventCode;
    index++;
    /* 塞入距离 */
    CodeUtils::getInstance().uint16ToBb(distance, buf + index);
    index += 2;
    /* 塞入报警级别 */
    buf[index] = eventLevel;
    index++;
    /* 塞入预留 */
    memcpy(buf + index, reserved, sizeof(reserved));
    index += sizeof(reserved);
    ret = index;

    return ret;
}
