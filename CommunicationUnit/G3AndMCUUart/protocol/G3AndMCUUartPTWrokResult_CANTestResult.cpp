//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#include "G3AndMCUUartPTWrokResult_CANTestResult.h"
#include "CodeUtils.h"

int G3AndMCUUartPTWrokResult_CANTestResult::deCode(uint8_t *buf, int len) {
    int ret = -1;
    if(len >= 1){
        int index = 0;

        result = buf[index];
        index  = index + 1;

        ret = 0;
    }


    return ret;
}

uint8_t G3AndMCUUartPTWrokResult_CANTestResult::getResult() const {
    return result;
}

uint16_t G3AndMCUUartPTWrokResult_CANTestResult::getItemId() const {
    return itemId;
}

uint8_t G3AndMCUUartPTWrokResult_CANTestResult::getItemContentLen() const {
    return itemContentLen;
}
