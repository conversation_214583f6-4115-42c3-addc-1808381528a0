//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/19.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTVEHICLEREALTIMESTATUS_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTVEHICLEREALTIMESTATUS_H

#include "G3AndMCUUartPacket.h"


struct G3AndMCUUartJKFZSoundControl_SoundInfo {
    int soundId: 7;/* 声音文件的ID */
    bool level: 1;  /* 0--普通级别   1--高优先级 */
};

struct G3AndMCUUartJKFZSoundControl_ConstructionMachineryStatus {
    bool turnL: 1;  /* 左转向 */
    bool turnR: 1;  /* 右转向 */
    bool forward: 1;  /* 前进档 */
    bool reverse: 1;  /* 倒档 */
    bool handBrake: 1;  /* 手刹 */
    bool footBrake: 1;  /* 脚刹 */
    bool gasPedal: 1;  /* 油门 */
    bool horn: 1;  /* 喇叭 */
};


/* 机科发展工程机械状态及语音控制 */
struct G3AndMCUUartJKFZSoundControl {
    G3AndMCUUartJKFZSoundControl_SoundInfo soundInfo = {0, false};
    G3AndMCUUartJKFZSoundControl_ConstructionMachineryStatus constructionMachineryStatus = {false, false, false, false, false, false, false, false};
    uint8_t reserved[6] = {0x00};

    void reset(){
        soundInfo.soundId = -1;
        soundInfo.level = false;
        constructionMachineryStatus.turnL = false;
        constructionMachineryStatus.turnR = false;
        constructionMachineryStatus.forward = false;
        constructionMachineryStatus.reverse = false;
        constructionMachineryStatus.handBrake = false;
        constructionMachineryStatus.footBrake = false;
        constructionMachineryStatus.gasPedal = false;
        constructionMachineryStatus.horn = false;
        memset(reserved,0x00, sizeof(reserved));
    };
};


struct G3AndMCUUartGPSInfo {
    float gpsSpeed;
    uint8_t status;
    uint16_t direction;
    uint16_t altitude;
    double latitude;
    double longitude;
    uint8_t bcdTime[6];
};

struct G3AndMCUUartVehicleStatus {
    bool acc: 1;
    bool reverse: 1;
    bool reserve1: 1;
    bool turnL: 1;
    bool turnR: 1;
    bool bigLight: 1;
    bool wiper: 1;
    bool reserve2: 1;
    bool brake: 1;
    bool frontDoor: 1;
    bool backDoor: 1;
    bool seatBelt: 1;
    int reserve3: 4;
};



/* GJ灯控状态_通道控制 */
struct G3AndMCUUartGJLampContrl_ChannelControl {
    bool lamp_open_norma1 : 1;
    bool  lamp_open_infrared1 : 1;
    bool  reserved1 : 1;
    bool  reserved2 : 1;
    bool lamp1_open_norma2 : 1;
    bool  lamp_open_infrared2 : 1;
    bool  reserved3 : 1;
    bool  reserved4 : 1;
};

/* GJ灯控状态 */
struct G3AndMCUUartGJLampContrl {
    /* 通道1，2控制 */
    G3AndMCUUartGJLampContrl_ChannelControl channelControl1;
    /* 通道3，4控制 */
    G3AndMCUUartGJLampContrl_ChannelControl channelControl2;
    /* 保留字节 */
    uint8_t reserved[2] = {0x00};
};

class G3AndMCUUartVehicleRealTimeStatus : public G3AndMCUUartPacket {
public:
    const static uint16_t ID_BATTERTVOLTAGE = 0x0001; // 电瓶电压
    const static uint16_t ID_ENGINESPEED = 0x0002; // 发动机转速
    const static uint16_t ID_SPEED = 0x0003; // 车速
    const static uint16_t ID_COOLANTTEMPERATURE = 0x0004; // 冷却液温度
    const static uint16_t ID_INSTANTANEOUSFUELCONSUMPTION = 0x0005;// 瞬时油耗
    const static uint16_t ID_RESIDUALOIL = 0x0006; // 剩余油量
    const static uint16_t ID_CARSTATUS = 0x0007; // 车辆状态
    const static uint16_t ID_CARERRORCODE = 0x0008; // 故障码
    const static uint16_t ID_CURRENTMILEAGE = 0x0009; // 当前行驶里程
    const static uint16_t ID_TRAVELTIME = 0x000A; // 本次行驶时间
    const static uint16_t ID_IDLINGTIME = 0x000B; // 本次怠速时间
    const static uint16_t ID_ACCUMULATIVEOILCONSUMPTION = 0x000C; // 累计耗油量
    const static uint16_t ID_ODO = 0x000D; // 累计行驶里程
    const static uint16_t ID_CUMULATIVEDRIVINGTIME = 0x000E; // 累计行驶时间
    const static uint16_t ID_CUMULATIVEIDLINGTIME = 0x000F; // 累计怠速时间
    const static uint16_t ID_GPS = 0xF001; // GPS 数据
    const static uint16_t ID_JKFZ_SOUND_CONTROL = 0xF005; // 工程机械状态及语音控制
    const static uint16_t ID_GJ_LAMP_CONTROL = 0xF006; // 灯控状态



    G3AndMCUUartVehicleRealTimeStatus();

    int decode(uint8_t *buf, int len) override;

    int parseParam(uint16_t paramId, uint8_t *data);

    int getBatteryVoltage() const;

    void setBatteryVoltage(int batteryVoltage);

    uint16_t getEngineSpeed() const;

    void setEngineSpeed(uint16_t engineSpeed);

    float getSpeed() const;

    void setSpeed(float speed);

    uint8_t getCoolantTemperature() const;

    void setCoolantTemperature(uint8_t coolantTemperature);

    float getInstantaneousFuelConsumption() const;

    void setInstantaneousFuelConsumption(float instantaneousFuelConsumption);

    uint8_t getResidualOil() const;

    void setResidualOil(uint8_t residualOil);

    const G3AndMCUUartVehicleStatus &getCarStatus() const;

    void setCarStatus(const G3AndMCUUartVehicleStatus &carStatus);

    float getCurrentMileage() const;

    void setCurrentMileage(float currentMileage);

    uint16_t getTravelTime() const;

    void setTravelTime(uint16_t travelTime);

    uint16_t getIdlingTime() const;

    void setIdlingTime(uint16_t idlingTime);

    uint32_t getAccumulativeOilConsumption() const;

    void setAccumulativeOilConsumption(uint32_t accumulativeOilConsumption);

    float getOdo() const;

    void setOdo(float odo);

    uint32_t getCumulativeDrivingTime() const;

    void setCumulativeDrivingTime(uint32_t cumulativeDrivingTime);

    uint32_t getCumulativeIdlingTime() const;

    void setCumulativeIdlingTime(uint32_t cumulativeIdlingTime);

    const G3AndMCUUartGPSInfo &getGpsData() const;

    void setGpsData(const G3AndMCUUartGPSInfo &gpsData);

    const G3AndMCUUartJKFZSoundControl &getSoundControl() const;

    void setSoundControl(const G3AndMCUUartJKFZSoundControl &soundControl);

private:
    // 电瓶电压
    int batteryVoltage;
    // 发动机转速
    uint16_t engineSpeed;
    // 车速
    float speed = -1;
    // 冷却液温度
    uint8_t coolantTemperature = -1;
    // 瞬时油耗
    float instantaneousFuelConsumption;
    // 剩余油量
    uint8_t residualOil;
    // 车辆状态
    G3AndMCUUartVehicleStatus carStatus = {};
    // 当前行驶里程
    float currentMileage;
    // 本次行驶时间
    uint16_t travelTime;
    // 本次怠速时间
    uint16_t idlingTime;
    // 累计耗油量
    uint32_t AccumulativeOilConsumption;
    // 累计行驶里程
    float ODO;
    // 累计行驶时间
    uint32_t cumulativeDrivingTime;
    // 累计怠速时间
    uint32_t cumulativeIdlingTime;
    // GPS 数据
    G3AndMCUUartGPSInfo gpsData;
    //工程机械状态及语音控制
    G3AndMCUUartJKFZSoundControl soundControl;
    /* GJ灯控状态 */
    G3AndMCUUartGJLampContrl lampContrl;
};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTVEHICLEREALTIMESTATUS_H
