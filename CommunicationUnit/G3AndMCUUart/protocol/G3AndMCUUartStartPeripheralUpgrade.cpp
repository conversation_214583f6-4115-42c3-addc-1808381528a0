//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#include "G3AndMCUUartStartPeripheralUpgrade.h"

G3AndMCUUartStartPeripheralUpgrade::G3AndMCUUartStartPeripheralUpgrade() {
    setMsgId(MSGID_START_PERIPHERAL_UPGRADE);
}

uint8_t G3AndMCUUartStartPeripheralUpgrade::getPeripheralId() const {
    return peripheralId;
}

void G3AndMCUUartStartPeripheralUpgrade::setPeripheralId(uint8_t peripheralId) {
    G3AndMCUUartStartPeripheralUpgrade::peripheralId = peripheralId;
}

int G3AndMCUUartStartPeripheralUpgrade::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 5;

    /* 先随便塞个内容长度 */
    setContentLen(1);
    CodeUtils::getInstance().uint16ToBb(getContent<PERSON><PERSON>(), buf + index);
    index += 2;
    buf[index] = peripheralId;
    index++;
    /* 加上头尾那些东西 */
    ret = G3AndMCUUartPacket::toCode(buf, len);

    return ret;
}
