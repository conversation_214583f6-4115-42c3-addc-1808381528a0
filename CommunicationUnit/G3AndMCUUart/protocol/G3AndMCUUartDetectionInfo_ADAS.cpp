//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/18.
//

#include "G3AndMCUUartDetectionInfo_ADAS.h"

uint16_t G3AndMCUUartDetectionInfo_ADAS::getItemId() const {
    return itemId;
}

void G3AndMCUUartDetectionInfo_ADAS::setItemId(uint16_t itemId) {
    G3AndMCUUartDetectionInfo_ADAS::itemId = itemId;
}

uint8_t G3AndMCUUartDetectionInfo_ADAS::getItemContentLen() const {
    return itemContentLen;
}

void G3AndMCUUartDetectionInfo_ADAS::setItemContentLen(uint8_t itemContentLen) {
    G3AndMCUUartDetectionInfo_ADAS::itemContentLen = itemContentLen;
}

uint8_t G3AndMCUUartDetectionInfo_ADAS::getHmw() const {
    return hmw;
}

void G3AndMCUUartDetectionInfo_ADAS::setHmw(uint8_t hmw) {
    G3AndMCUUartDetectionInfo_ADAS::hmw = hmw;
}

uint16_t G3AndMCUUartDetectionInfo_ADAS::getCarDistance() const {
    return carDistance;
}

void G3AndMCUUartDetectionInfo_ADAS::setCarDistance(uint16_t carDistance) {
    G3AndMCUUartDetectionInfo_ADAS::carDistance = carDistance;
}

uint16_t G3AndMCUUartDetectionInfo_ADAS::getLaneLineDistanceLeft() const {
    return laneLineDistance_left;
}

void G3AndMCUUartDetectionInfo_ADAS::setLaneLineDistanceLeft(uint16_t laneLineDistanceLeft) {
    laneLineDistance_left = laneLineDistanceLeft;
}

uint16_t G3AndMCUUartDetectionInfo_ADAS::getLaneLineDistanceRight() const {
    return laneLineDistance_right;
}

void G3AndMCUUartDetectionInfo_ADAS::setLaneLineDistanceRight(uint16_t laneLineDistanceRight) {
    laneLineDistance_right = laneLineDistanceRight;
}

uint8_t G3AndMCUUartDetectionInfo_ADAS::getPdw() const {
    return pdw;
}

void G3AndMCUUartDetectionInfo_ADAS::setPdw(uint8_t pdw) {
    G3AndMCUUartDetectionInfo_ADAS::pdw = pdw;
}

uint16_t G3AndMCUUartDetectionInfo_ADAS::getPedestrianDistance() const {
    return pedestrianDistance;
}

void G3AndMCUUartDetectionInfo_ADAS::setPedestrianDistance(uint16_t pedestrianDistance) {
    G3AndMCUUartDetectionInfo_ADAS::pedestrianDistance = pedestrianDistance;
}

uint8_t G3AndMCUUartDetectionInfo_ADAS::getObjectStatus() const {
    return objectStatus;
}

void G3AndMCUUartDetectionInfo_ADAS::setObjectStatus(uint8_t objectStatus) {
    G3AndMCUUartDetectionInfo_ADAS::objectStatus = objectStatus;
}

int G3AndMCUUartDetectionInfo_ADAS::toCode(uint8_t *buf, int len) {
    int ret = -1;

    int index = 0;
    /* 塞入ID */
    CodeUtils::getInstance().uint16ToBb(itemId, buf + index);
    index += 2;
    /* 塞入长度 */
    buf[index] = itemContentLen;
    index++;
    /* 塞入车距监控时间 */
    buf[index] = hmw;
    index++;
    /* 塞入车距监控距离 */
    CodeUtils::getInstance().uint16ToBb(carDistance, buf + index);
    index += 2;
    /* 塞入左车道距离 */
    CodeUtils::getInstance().uint16ToBb(laneLineDistance_left, buf + index);
    index += 2;
    /* 塞入右车道距离 */
    CodeUtils::getInstance().uint16ToBb(laneLineDistance_right, buf + index);
    index += 2;
    /* 塞入行人碰撞时间 */
    buf[index] = pdw;
    index++;
    /* 塞入行人距离 */
    CodeUtils::getInstance().uint16ToBb(pedestrianDistance, buf + index);
    index += 2;
    /* 塞入状态信息 */
    buf[index] = objectStatus;
    index++;

    ret = index;

    return ret;
}
