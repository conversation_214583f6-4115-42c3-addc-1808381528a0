//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTSTARTPTWROK_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTSTARTPTWROK_H

#include "G3AndMCUUartPacket.h"
#include "G3AndMCUUartStartPTWrok_CANTest.h"
#include "vector"

/* 发送生产测试命令让MCU开始一次生产测试 */
class G3AndMCUUartStartPTWrok : public G3AndMCUUartPacket{
public:
    G3AndMCUUartStartPTWrok();
    ~G3AndMCUUartStartPTWrok();

    int addCANtestList(G3AndMCUUartStartPTWrok_CANTest &canTest);

    int toCode(uint8_t *buf, int len) override;

    uint8_t getItemSum() const;

private:
    uint8_t itemSum = 0;
    std::vector<G3AndMCUUartStartPTWrok_CANTest> cantestList;


};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTSTARTPTWROK_H
