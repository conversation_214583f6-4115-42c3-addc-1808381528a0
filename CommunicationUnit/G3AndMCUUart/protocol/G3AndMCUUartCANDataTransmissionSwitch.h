//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTCANDATATRANSMISSIONSWITCH_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTCANDATATRANSMISSIONSWITCH_H

#include "G3AndMCUUartPacket.h"
class G3AndMCUUartCANDataTransmissionSwitch : public G3AndMCUUartPacket{
public:
    const static uint8_t SWITCH_OPEN = 0x01;
    const static uint8_t SWITCH_CLOSE = 0x02;


    G3AndMCUUartCANDataTransmissionSwitch();


    int toCode(uint8_t *buf, int len) override;

    uint8_t getIsOpen() const;

    void setIsOpen(uint8_t isOpen);

private:
    uint8_t isOpen = 0x00;
};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTCANDATATRANSMISSIONSWITCH_H
