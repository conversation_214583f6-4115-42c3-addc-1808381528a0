//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/20.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTALARMINFO_BSD_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTALARMINFO_BSD_H

#include <cstdint>
#include "CodeUtils.h"

class G3AndMCUUartAlarmInfo_BSD {
public:

    static const uint8_t EVENT_TYPE_LEFT_PEDESTRIAN = 0x01;
    static const uint8_t EVENT_TYPE_LEFT_VEHICLE = 0x02;
    static const uint8_t EVENT_TYPE_RIGHT_PEDESTRIAN = 0x03;
    static const uint8_t EVENT_TYPE_RIGHT_VEHICLE = 0x04;
    static const uint8_t EVENT_TYPE_FORWARD_PEDESTRIAN = 0x05;
    static const uint8_t EVENT_TYPE_FORWARD_VEHICLE = 0x06;
    static const uint8_t EVENT_TYPE_BACKWARD_PEDESTRIAN = 0x07;
    static const uint8_t EVENT_TYPE_BACKWARD_VEHICLE = 0x08;

    static const uint8_t EVENT_LEVEL_NOT = 0x00;
    static const uint8_t EVENT_LEVEL_LEVEL1 = 0x01;
    static const uint8_t EVENT_LEVEL_LEVEL2 = 0x02;

    int toCode(uint8_t *buf, int len);

    uint8_t getEventStatus() const;

    void setEventStatus(uint8_t eventStatus);

    uint8_t getEventCode() const;

    void setEventCode(uint8_t eventCode);

    uint16_t getDistance() const;

    void setDistance(uint16_t distance);

    uint8_t getEventLevel() const;

    void setEventLevel(uint8_t eventLevel);

    const uint8_t *getReserved() const;

private:
    uint16_t itemId = 0x0067;
    uint8_t itemContentLen = 8;

    /* 标志状态	uint8_t	0x00：不可用  0x01：开始标志  0x02：结束标志  该字段仅适用于有开始和结束标志类型的报警或事件，报警类型或事件类型无开始和结束标志，则该位不可用，填入0x00即可 */
    uint8_t eventStatus;
    /* 报警/事件类型	0x01：左侧行人接近报警  0x02：左侧车辆接近报警  0x03：右侧行人接近报警  0x04：右侧车辆接近报警  0x05：前方行人接近报警  0x06：前方车辆接近报警  0x07：后方行人接近报警  0x08：后方车辆接近报警 */
    uint8_t eventCode;
    /* 距离	uint16_t	单位0.01m; */
    uint16_t distance;
    /* 报警级别	uint8_t	0--无报警  1--1级报警  2--2级报警 */
    uint8_t eventLevel;
    /* 预留 */
    uint8_t reserved[3] = {0x00};

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTALARMINFO_BSD_H
