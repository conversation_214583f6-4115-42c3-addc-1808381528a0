//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/22.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTENCODER_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTENCODER_H

#include "G3AndMCUUartAlarmInfo.h"
#include "G3AndMCUUartDetectionInfo.h"
#include "G3AndMCUUartUniversalAnswer.h"
#include "G3AndMCUUartGetPeripheralVersion.h"
#include "G3AndMCUUartStartPeripheralUpgrade.h"
#include "G3AndMCUUartSendPeripheralUpgradeData.h"
#include "G3AndMCUUartStartPTWrok.h"
#include "G3AndMCUUartCANDataTransmissionSwitch.h"

class G3AndMCUUartEncoder {
public:
    int generateDetectionInfo(uint8_t *buf, int len, G3AndMCUUartDetectionInfo &detectionInfo);

    int generateAlarmEvent(uint8_t *buf, int len, G3AndMCUUartAlarmInfo &detectionAlarmEvent);

    int generateUniversalAnswer(uint8_t *buf, int len, G3AndMCUUartUniversalAnswer &universalAnswer);

    int generateGetPeripheralVersion(uint8_t *buf, int len, G3AndMCUUartGetPeripheralVersion &getPeripheralVersion);

    int
    generateStartPeripheralUpgrade(uint8_t *buf, int len, G3AndMCUUartStartPeripheralUpgrade &startPeripheralUpgrade);

    int generateSendPeripheralUpgradeData(uint8_t *buf, int len,
                                          G3AndMCUUartSendPeripheralUpgradeData &sendPeripheralUpgradeData);

    int generateStartPTWrok(uint8_t *buf, int len, G3AndMCUUartStartPTWrok &startPtWrok);

    int generateCANDataTransmissionSwitch(uint8_t *buf, int len, G3AndMCUUartCANDataTransmissionSwitch &canDataTransmissionSwitch);




    uint16_t getFlowId();

private:
    /* 流水号 */
    uint16_t flowId = 0;
};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTENCODER_H
