//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTGETPERIPHERALUPGRADEDATA_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTGETPERIPHERALUPGRADEDATA_H

#include "G3AndMCUUartPacket.h"

class G3AndMCUUartGetPeripheralUpgradeData : public G3AndMCUUartPacket {
public:
    G3AndMCUUartGetPeripheralUpgradeData();

    int decode(uint8_t *buf, int len) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

    uint32_t getOffset() const;

    void setOffset(uint32_t offset);

private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;
    /* 文件偏移地址 */
    uint32_t offset;


};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTGETPERIPHERALUPGRADEDATA_H
