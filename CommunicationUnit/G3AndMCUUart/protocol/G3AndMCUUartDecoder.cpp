//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/22.
//

#include "G3AndMCUUartDecoder.h"

namespace vis {

    G3AndMCUUartDecoder::G3AndMCUUartDecoder() {
        dataCache = static_cast<uint8_t *>(malloc(MAX_DATA_CACHE_SIZE));
        formalDataBuf = static_cast<uint8_t *>(malloc(MAX_DATA_CACHE_SIZE));
    }

    void G3AndMCUUartDecoder::setInterface(CommunicationDataCallback &communicationDataCallback) {
        callback = &communicationDataCallback;
    }

    void G3AndMCUUartDecoder::onDataReceived(const uint8_t *buf, int len) {
        for (int i = 0; i < len; i++) {
            switch (buf[i]) {
                case IDENTIFICATION_HEAD:
                    resetData();
                    addANewCharToCache(buf[i]);
                    break;
                case IDENTIFICATION_TAIL:
                    addANewCharToCache(buf[i]);
                    doDecode(dataCache, point);
                    resetData();
                    break;
                default:
                    addANewCharToCache(buf[i]);
                    break;
            }
        }
    }

    void G3AndMCUUartDecoder::resetData() {
        point = 0;
    }

    void G3AndMCUUartDecoder::addANewCharToCache(uint8_t nc) {
        dataCache[point] = nc;
        point++;
        if (point >= MAX_DATA_DATA_SIZE) {
            point = 0;
        }
    }

    void G3AndMCUUartDecoder::doDecode(uint8_t *buf, int len) {

        //先转义一下
        int packagetDataLen = CodeUtils::getInstance().doEscape4ReceiveFromMCUUart(buf, len, 1, len - 2, formalDataBuf);
        /* 判断长度是否达到了最小的数据包长度 */
        if (packagetDataLen < G3AndMCUUartPacket::MIN_DATA_LEN) {
            printf("error package len. %d  \n", packagetDataLen);
            return;
        }

//        printf("G3AndMCUUartDecoder::doDecode:%s \n",XuString::getInstance().byteArrayToString(formalDataBuf,packagetDataLen).c_str());

        //解析出校验码
        uint16_t checkCode = CodeUtils::getInstance().BbToUint16(formalDataBuf + (packagetDataLen - 3));
        //自己计算下校验码
        uint16_t mCheckCode = CodeUtils::getInstance().generateCrc16(formalDataBuf + 1, packagetDataLen - 4);
        // 判断校验码是否正确
        if (checkCode != mCheckCode) {
            printf("Uart checkCode error!  PacketCheckCode=%d   mCheckCode=%d  \n", checkCode, mCheckCode);
            return;
        }
        //根据命令字判断用什么类装
        switch (formalDataBuf[4]) {
            case G3AndMCUUartPacket::MSGID_UNIVERSALANSWER: {
//                printf("this msg is a universal answer!  \n");
                G3AndMCUUartUniversalAnswer universalAnswer;
                if (universalAnswer.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetUniversalAnswerFromMCUUART(universalAnswer);
                }
            }
                break;
            case G3AndMCUUartPacket::MSGID_VEHICLE_REALTIME_STATUS: {
//            printf("this msg is a vehicle realtime status !  \n");
                G3AndMCUUartVehicleRealTimeStatus vehicleRealTimeStatus;
                if (vehicleRealTimeStatus.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetRealtimeVehicleStatusFromMCUUART(vehicleRealTimeStatus);
                }

            }
                break;
            case G3AndMCUUartPacket::MSGID_RESPOND_PERIPHERAL_VERSION: {
                printf("this msg is a peripheral version!  \n");
                G3AndMCUUartRespondPeripheralVersion peripheralVersion;
                if (peripheralVersion.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetPeripheralVersionFromMCUUART(peripheralVersion);
                }

            }
                break;
            case G3AndMCUUartPacket::MSGID_GET_PERIPHERAL_UPGRADE_DATA: {
                printf("this msg is a get peripheral upgrade data!  \n");
                G3AndMCUUartGetPeripheralUpgradeData getPeripheralUpgradeData;
                if (getPeripheralUpgradeData.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetGetPeripheralUpgradeDataFromMCUUART(getPeripheralUpgradeData);
                }
            }
                break;
            case G3AndMCUUartPacket::MSGID_PERIPHERAL_UPGRADE_RESULT: {
                printf("this msg is a peripheral upgrade result!  \n");
                G3AndMCUUartPeripheralUpgradeResult peripheralUpgradeResult;
                if (peripheralUpgradeResult.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetPeripheralUpgradeResultFromMCUUART(peripheralUpgradeResult);
                }
            }
                break;

            case G3AndMCUUartPacket::MSGID_PRODUCTION_TEST_WORK_RESULT: {
                printf("this msg is a production test work result!  \n");
                G3AndMCUUartPTWrokResult ptWrokResult;
                if (ptWrokResult.decode(formalDataBuf, packagetDataLen) == 0) {
                    callback->onGetProductionTestResultFromMCUUART(ptWrokResult);
                }
            }
                break;
        }
    }

}