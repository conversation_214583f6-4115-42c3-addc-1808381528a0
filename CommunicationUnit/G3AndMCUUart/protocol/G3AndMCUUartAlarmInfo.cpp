//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/20.
//

#include "G3AndMCUUartAlarmInfo.h"

G3AndMCUUartAlarmInfo::G3AndMCUUartAlarmInfo() {
    setMsgId(MSGID_ALARM_INFO);
}

int G3AndMCUUartAlarmInfo::addADASInfo(G3AndMCUUartAlarmInfo_ADAS &adasInfo) {
    adasInfoList.push_back(adasInfo);
    return 0;
}

int G3AndMCUUartAlarmInfo::addBSDInfo(G3AndMCUUartAlarmInfo_BSD &bsdInfo) {
    bsdInfoList.push_back(bsdInfo);
    return 0;
}

int G3AndMCUUartAlarmInfo::addDSMInfo(G3AndMCUUartAlarmInfo_DSM &dsmInfo) {
    dsmInfoList.push_back(dsmInfo);
    return 0;
}

int G3AndMCUUartAlarmInfo::toCode(uint8_t *buf, int len) {
    int ret = -1;
    if (len >= (9 + 10)) {
        int index = 5;
        int msgContent = 0;
        /* 先随便塞个内容长度 */
        CodeUtils::getInstance().uint16ToBb(0, buf + index);
        index += 2;
        /* 数据项总数 */
        itemSum = adasInfoList.size() + bsdInfoList.size() + dsmInfoList.size();
        buf[index] = itemSum;
        index++;
        msgContent++;

        /* 先塞ADAS数据项 */
        if (adasInfoList.size() > 0) {
            for (std::size_t i = 0; i < adasInfoList.size(); i++) {
                int itemLen = adasInfoList[i].toCode(buf + index, len);
                index += itemLen;
                msgContent += itemLen;
            }
        }
        /* 再塞BSD数据项 */
        if (bsdInfoList.size() > 0) {
            for (std::size_t i = 0; i < bsdInfoList.size(); i++) {
                int itemLen = bsdInfoList[i].toCode(buf + index, len);
                index += itemLen;
                msgContent += itemLen;
            }
        }
        /* 再塞DSM数据项 */
        if (dsmInfoList.size() > 0) {
            for (std::size_t i = 0; i < dsmInfoList.size(); i++) {
                int itemLen = dsmInfoList[i].toCode(buf + index, len);
                index += itemLen;
                msgContent += itemLen;
            }
        }
        /* 把真实的内容长度塞进去 */
        setContentLen(msgContent);
        CodeUtils::getInstance().uint16ToBb(getContentLen(), buf + 5);
        index += 2;
        /* 加上头尾那些东西 */
        ret = G3AndMCUUartPacket::toCode(buf, len);
    }
    return ret;
}
