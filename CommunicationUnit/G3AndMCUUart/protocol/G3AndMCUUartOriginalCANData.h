//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTORIGINALCANDATA_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTORIGINALCANDATA_H

#include "G3AndMCUUartPacket.h"
class G3AndMCUUartOriginalCANData : public G3AndMCUUartPacket{
public:
    G3AndMCUUartOriginalCANData();

private:

    /* CAN数据总数 */
    uint8_t itemSum = 0;


};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTORIGINALCANDATA_H
