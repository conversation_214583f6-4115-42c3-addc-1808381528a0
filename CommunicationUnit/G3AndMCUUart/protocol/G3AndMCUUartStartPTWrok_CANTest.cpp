//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#include "G3AndMCUUartStartPTWrok_CANTest.h"
#include "CodeUtils.h"

int G3AndMCUUartStartPTWrok_CANTest::toCode(uint8_t *buf, int len) {
    int ret = -1;

    int index = 0;
    /* 塞入ID */
    CodeUtils::getInstance().uint16ToBb(itemId, buf + index);
    index += 2;
    /* 塞入长度 */
    buf[index] = itemContentLen;
    index++;
    /* 塞入波特率 */
    CodeUtils::getInstance().uint16ToBb(baudRate, buf + index);
    index += 2;

    ret = index;

    return ret;
}

uint16_t G3AndMCUUartStartPTWrok_CANTest::getBaudRate() const {
    return baudRate;
}

void G3AndMCUUartStartPTWrok_CANTest::setBaudRate(uint16_t baudRate) {
    G3AndMCUUartStartPTWrok_CANTest::baudRate = baudRate;
}
