//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/20.
//

#include "G3AndMCUUartAlarmInfo_ADAS.h"


uint8_t G3AndMCUUartAlarmInfo_ADAS::getEventStatus() const {
    return eventStatus;
}

void G3AndMCUUartAlarmInfo_ADAS::setEventStatus(uint8_t eventStatus) {
    G3AndMCUUartAlarmInfo_ADAS::eventStatus = eventStatus;
}

uint8_t G3AndMCUUartAlarmInfo_ADAS::getEventCode() const {
    return eventCode;
}

void G3AndMCUUartAlarmInfo_ADAS::setEventCode(uint8_t eventCode) {
    G3AndMCUUartAlarmInfo_ADAS::eventCode = eventCode;
}

uint8_t G3AndMCUUartAlarmInfo_ADAS::getCollisionTime() const {
    return collisionTime;
}

void G3AndMCUUartAlarmInfo_ADAS::setCollisionTime(uint8_t collisionTime) {
    G3AndMCUUartAlarmInfo_ADAS::collisionTime = collisionTime;
}

uint8_t G3AndMCUUartAlarmInfo_ADAS::getLdwType() const {
    return ldwType;
}

void G3AndMCUUartAlarmInfo_ADAS::setLdwType(uint8_t ldwType) {
    G3AndMCUUartAlarmInfo_ADAS::ldwType = ldwType;
}

uint8_t G3AndMCUUartAlarmInfo_ADAS::getRoadSignType() const {
    return roadSignType;
}

void G3AndMCUUartAlarmInfo_ADAS::setRoadSignType(uint8_t roadSignType) {
    G3AndMCUUartAlarmInfo_ADAS::roadSignType = roadSignType;
}

const uint8_t *G3AndMCUUartAlarmInfo_ADAS::getReserved() const {
    return reserved;
}

int G3AndMCUUartAlarmInfo_ADAS::toCode(uint8_t *buf, int len) {
    int ret = -1;

    int index = 0;
    /* 塞入ID */
    CodeUtils::getInstance().uint16ToBb(itemId, buf + index);
    index += 2;
    /* 塞入长度 */
    buf[index] = itemContentLen;
    index++;
    /* 塞入标志状态 */
    buf[index] = eventStatus;
    index++;
    /* 塞入报警/事件类型 */
    buf[index] = eventCode;
    index++;
    /* 塞入碰撞时间 */
    buf[index] = collisionTime;
    index++;
    /* 塞入偏离类型 */
    buf[index] = ldwType;
    index++;
    /* 塞入道路标志识别类型 */
    buf[index] = roadSignType;
    index++;
    /* 塞入预留 */
    memcpy(buf + index, reserved, sizeof(reserved));
    index += sizeof(reserved);


    ret = index;

    return ret;
}
