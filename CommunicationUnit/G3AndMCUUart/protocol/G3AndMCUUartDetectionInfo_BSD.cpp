//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/18.
//

#include "G3AndMCUUartDetectionInfo_BSD.h"

int G3AndMCUUartDetectionInfo_BSD::getLeftAlarmArea1Status() const {
    return leftAlarmArea1Status;
}

void G3AndMCUUartDetectionInfo_BSD::setLeftAlarmArea1Status(int leftAlarmArea1Status) {
    G3AndMCUUartDetectionInfo_BSD::leftAlarmArea1Status = leftAlarmArea1Status;
}

int G3AndMCUUartDetectionInfo_BSD::getLeftAlarmArea2Status() const {
    return leftAlarmArea2Status;
}

void G3AndMCUUartDetectionInfo_BSD::setLeftAlarmArea2Status(int leftAlarmArea2Status) {
    G3AndMCUUartDetectionInfo_BSD::leftAlarmArea2Status = leftAlarmArea2Status;
}

int G3AndMCUUartDetectionInfo_BSD::getRightAlarmArea1Status() const {
    return rightAlarmArea1Status;
}

void G3AndMCUUartDetectionInfo_BSD::setRightAlarmArea1Status(int rightAlarmArea1Status) {
    G3AndMCUUartDetectionInfo_BSD::rightAlarmArea1Status = rightAlarmArea1Status;
}

int G3AndMCUUartDetectionInfo_BSD::getRightAlarmArea2Status() const {
    return rightAlarmArea2Status;
}

void G3AndMCUUartDetectionInfo_BSD::setRightAlarmArea2Status(int rightAlarmArea2Status) {
    G3AndMCUUartDetectionInfo_BSD::rightAlarmArea2Status = rightAlarmArea2Status;
}

int G3AndMCUUartDetectionInfo_BSD::getForwardAlarmArea1Status() const {
    return forwardAlarmArea1Status;
}

void G3AndMCUUartDetectionInfo_BSD::setForwardAlarmArea1Status(int forwardAlarmArea1Status) {
    G3AndMCUUartDetectionInfo_BSD::forwardAlarmArea1Status = forwardAlarmArea1Status;
}

int G3AndMCUUartDetectionInfo_BSD::getForwardAlarmArea2Status() const {
    return forwardAlarmArea2Status;
}

void G3AndMCUUartDetectionInfo_BSD::setForwardAlarmArea2Status(int forwardAlarmArea2Status) {
    G3AndMCUUartDetectionInfo_BSD::forwardAlarmArea2Status = forwardAlarmArea2Status;
}

int G3AndMCUUartDetectionInfo_BSD::getBackwardAlarmArea1Status() const {
    return backwardAlarmArea1Status;
}

void G3AndMCUUartDetectionInfo_BSD::setBackwardAlarmArea1Status(int backwardAlarmArea1Status) {
    G3AndMCUUartDetectionInfo_BSD::backwardAlarmArea1Status = backwardAlarmArea1Status;
}

int G3AndMCUUartDetectionInfo_BSD::getBackwardAlarmArea2Status() const {
    return backwardAlarmArea2Status;
}

void G3AndMCUUartDetectionInfo_BSD::setBackwardAlarmArea2Status(int backwardAlarmArea2Status) {
    G3AndMCUUartDetectionInfo_BSD::backwardAlarmArea2Status = backwardAlarmArea2Status;
}

int G3AndMCUUartDetectionInfo_BSD::getLeftCoverStatus() const {
    return leftCoverStatus;
}

void G3AndMCUUartDetectionInfo_BSD::setLeftCoverStatus(int leftCoverStatus) {
    G3AndMCUUartDetectionInfo_BSD::leftCoverStatus = leftCoverStatus;
}

int G3AndMCUUartDetectionInfo_BSD::getRightCoverStatus() const {
    return rightCoverStatus;
}

void G3AndMCUUartDetectionInfo_BSD::setRightCoverStatus(int rightCoverStatus) {
    G3AndMCUUartDetectionInfo_BSD::rightCoverStatus = rightCoverStatus;
}

int G3AndMCUUartDetectionInfo_BSD::getForwardCoverStatus() const {
    return forwardCoverStatus;
}

void G3AndMCUUartDetectionInfo_BSD::setForwardCoverStatus(int forwardCoverStatus) {
    G3AndMCUUartDetectionInfo_BSD::forwardCoverStatus = forwardCoverStatus;
}

int G3AndMCUUartDetectionInfo_BSD::getBackwardCoverStatus() const {
    return backwardCoverStatus;
}

void G3AndMCUUartDetectionInfo_BSD::setBackwardCoverStatus(int backwardCoverStatus) {
    G3AndMCUUartDetectionInfo_BSD::backwardCoverStatus = backwardCoverStatus;
}

int G3AndMCUUartDetectionInfo_BSD::getLeftBypassStatus() const {
    return leftBypassStatus;
}

void G3AndMCUUartDetectionInfo_BSD::setLeftBypassStatus(int leftBypassStatus) {
    G3AndMCUUartDetectionInfo_BSD::leftBypassStatus = leftBypassStatus;
}

int G3AndMCUUartDetectionInfo_BSD::getRightBypassStatus() const {
    return rightBypassStatus;
}

void G3AndMCUUartDetectionInfo_BSD::setRightBypassStatus(int rightBypassStatus) {
    G3AndMCUUartDetectionInfo_BSD::rightBypassStatus = rightBypassStatus;
}

int G3AndMCUUartDetectionInfo_BSD::getForwardBypassStatus() const {
    return forwardBypassStatus;
}

void G3AndMCUUartDetectionInfo_BSD::setForwardBypassStatus(int forwardBypassStatus) {
    G3AndMCUUartDetectionInfo_BSD::forwardBypassStatus = forwardBypassStatus;
}

int G3AndMCUUartDetectionInfo_BSD::getBackwardBypassStatus() const {
    return backwardBypassStatus;
}

void G3AndMCUUartDetectionInfo_BSD::setBackwardBypassStatus(int backwardBypassStatus) {
    G3AndMCUUartDetectionInfo_BSD::backwardBypassStatus = backwardBypassStatus;
}

int G3AndMCUUartDetectionInfo_BSD::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 塞入ID */
    CodeUtils::getInstance().uint16ToBb(itemId, buf + index);
    index += 2;
    /* 塞入长度 */
    buf[index] = itemContentLen;
    index++;
    /* 整理行人状态 */
    pedestrianStatus = 0;
    if (leftAlarmArea2Status) {
        pedestrianStatus |= 0x01;
    }
    if (rightAlarmArea2Status) {
        pedestrianStatus |= 0x02;
    }
    if (forwardAlarmArea2Status) {
        pedestrianStatus |= 0x04;
    }
    if (backwardAlarmArea2Status) {
        pedestrianStatus |= 0x08;
    }
    if (leftAlarmArea1Status) {
        pedestrianStatus |= 0x10;
    }
    if (rightAlarmArea1Status) {
        pedestrianStatus |= 0x20;
    }
    if (forwardAlarmArea1Status) {
        pedestrianStatus |= 0x40;
    }
    if (backwardAlarmArea1Status) {
        pedestrianStatus |= 0x80;
    }
    /* 塞行人状态 */
    buf[index] = pedestrianStatus;
    index++;

    /* 整理镜头遮挡状态 */
    cameraCoverStatus = 0;
    if (leftCoverStatus) {
        cameraCoverStatus |= 0x01;
    }
    if (rightCoverStatus) {
        cameraCoverStatus |= 0x02;
    }
    if (forwardCoverStatus) {
        cameraCoverStatus |= 0x04;
    }
    if (backwardCoverStatus) {
        cameraCoverStatus |= 0x08;
    }
    /* 塞镜头遮挡状态 */
    buf[index] = cameraCoverStatus;
    index++;

    /* 整理镜头bypass状态 */
    cameraBypassStatus = 0;
    if (leftBypassStatus) {
        cameraBypassStatus |= 0x01;
    }
    if (rightBypassStatus) {
        cameraBypassStatus |= 0x02;
    }
    if (forwardBypassStatus) {
        cameraBypassStatus |= 0x04;
    }
    if (backwardBypassStatus) {
        cameraBypassStatus |= 0x08;
    }

    /* 塞镜头bypass状态 */
    buf[index] = cameraBypassStatus;
    index++;
    /* 塞预留字节 */
    memcpy(buf + index, reserveByte, sizeof(reserveByte));
    index += sizeof(reserveByte);

    ret = index;
    return ret;
}
