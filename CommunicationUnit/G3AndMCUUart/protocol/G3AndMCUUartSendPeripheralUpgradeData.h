//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTSENDPERIPHERALUPGRADEDATA_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTSENDPERIPHERALUPGRADEDATA_H

#include "G3AndMCUUartPacket.h"

class G3AndMCUUartSendPeripheralUpgradeData : public G3AndMCUUartPacket {
public:
    G3AndMCUUartSendPeripheralUpgradeData();

    int toCode(uint8_t *buf, int len) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

    uint32_t getOffset() const;

    void setOffset(uint32_t offset);

    const uint8_t *getFileData() const;

    uint8_t getFileDataLen() const;

    void setFileDataLen(uint8_t fileDataLen);

    void setFileData(uint8_t *data, uint8_t dataLen);


private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;
    /* 文件偏移地址 */
    uint32_t offset;
    /* 文件数据 */
    uint8_t fileData[252];

    /* 此包文件数据的长度 */
    uint8_t fileDataLen = 252;

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTSENDPERIPHERALUPGRADEDATA_H
