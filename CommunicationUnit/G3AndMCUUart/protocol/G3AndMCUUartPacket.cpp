//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/15.
//


#include <cstdio>
#include "G3AndMCUUartPacket.h"

int G3AndMCUUartPacket::toCode(uint8_t *buf, int len) {
    int ret = -1;
    if (len >= 10) {
        int index = 0;
        /* 先塞包头 */
        buf[index] = getHead();
        index++;
        /* 再塞流水号 */
        CodeUtils::getInstance().int16ToBb(getFlowId(), buf + index);
        index += 2;
        /* 再保留字 */
        buf[index] = getReserved();
        index++;
        /* 再通信命令 */
        buf[index] = getMsgId();
        index++;
        /* 直接跳过内容 */
        index += (getContentLen() + 2);
        /* 塞校验字 */
        uint16_t curCheckCode = CodeUtils::getInstance().generateCrc16(buf + 1, index - 1);
        CodeUtils::getInstance().int16ToBb(curCheckCode, buf + index);
        index += 2;

        /* 最后塞入包尾 */
        buf[index] = getTail();
        index++;
        ret = index;
    }
    return ret;
}

uint8_t G3AndMCUUartPacket::getHead() const {
    return head;
}

void G3AndMCUUartPacket::setHead(uint8_t head) {
    G3AndMCUUartPacket::head = head;
}

uint16_t G3AndMCUUartPacket::getFlowId() const {
    return flowId;
}

void G3AndMCUUartPacket::setFlowId(uint16_t flowId) {
    G3AndMCUUartPacket::flowId = flowId;
}

uint8_t G3AndMCUUartPacket::getReserved() const {
    return reserved;
}

void G3AndMCUUartPacket::setReserved(uint8_t reserved) {
    G3AndMCUUartPacket::reserved = reserved;
}

uint8_t G3AndMCUUartPacket::getMsgId() const {
    return msgId;
}

void G3AndMCUUartPacket::setMsgId(uint8_t msgId) {
    G3AndMCUUartPacket::msgId = msgId;
}

uint16_t G3AndMCUUartPacket::getContentLen() const {
    return contentLen;
}

void G3AndMCUUartPacket::setContentLen(uint16_t contentLen) {
    G3AndMCUUartPacket::contentLen = contentLen;
}

uint16_t G3AndMCUUartPacket::getCrcCheckCode() const {
    return crcCheckCode;
}

void G3AndMCUUartPacket::setCrcCheckCode(uint16_t crcCheckCode) {
    G3AndMCUUartPacket::crcCheckCode = crcCheckCode;
}

uint8_t G3AndMCUUartPacket::getTail() const {
    return tail;
}

void G3AndMCUUartPacket::setTail(uint8_t tail) {
    G3AndMCUUartPacket::tail = tail;
}

int G3AndMCUUartPacket::decode(uint8_t *buf, int len) {
    int ret = -1;
    /* 头是固定的，就不用解析了 */
//    setHead(buf[0]);
    /* 设置流水号 */
    setFlowId(CodeUtils::getInstance().BbToint16(buf + 1));
    /* 设置保留字节 */
    setReserved(buf[3]);
    /* 内容长度 */
    setContentLen(CodeUtils::getInstance().BbToint16(buf + 5));

//    /* 判断数据内容的长度对不对 */
//    if(len != MIN_DATA_LEN + getContentLen()){
//        return -1;
//    }

    /* 跳过内容  直接解析校验码 */
    setCrcCheckCode(CodeUtils::getInstance().BbToint16(buf + (len - 3)));
    /* 尾巴是固定的，就不用解析了 */
//    setTail(buf[12+getContentLen()]);

    ret = 0;
    return ret;
}
