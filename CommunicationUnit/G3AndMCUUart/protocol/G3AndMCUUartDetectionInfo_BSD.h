//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/18.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_BSD_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_BSD_H

#include <cstdint>
#include "CodeUtils.h"

class G3AndMCUUartDetectionInfo_BSD {
public:
    /* 报警区域有人 */
    const static int HAS_PEDESTRIAN = 1;
    /* 报警区域没人 */
    const static int NOT_PEDESTRIAN = 0;
    /* 镜头被遮挡 */
    const static int COVERED = 1;
    /* 镜头没被遮挡 */
    const static int NOT_COVER = 0;
    /* 镜头被bypass */
    const static int BYPASSED = 1;
    /* 镜头没被bypass */
    const static int NOT_BYPASS = 0;


    /* 左边报警区域1是否有人 */
    int leftAlarmArea1Status = 0;
    /* 左边报警区域2是否有人 */
    int leftAlarmArea2Status = 0;
    /* 右边报警区域1是否有人 */
    int rightAlarmArea1Status = 0;
    /* 右边报警区域2是否有人 */
    int rightAlarmArea2Status = 0;
    /* 前面报警区域1是否有人 */
    int forwardAlarmArea1Status = 0;
    /* 前面报警区域2是否有人 */
    int forwardAlarmArea2Status = 0;
    /* 后面报警区域1是否有人 */
    int backwardAlarmArea1Status = 0;
    /* 后面报警区域2是否有人 */
    int backwardAlarmArea2Status = 0;


    /* 左边镜头是否被遮挡 */
    int leftCoverStatus = 0;
    /* 左边镜头是否被遮挡 */
    int rightCoverStatus = 0;
    /* 前面镜头是否被遮挡 */
    int forwardCoverStatus = 0;
    /* 后面镜头是否被遮挡 */
    int backwardCoverStatus = 0;


    /* 左边镜头是否被Bypass */
    int leftBypassStatus = 0;
    /* 左边镜头是否被Bypass */
    int rightBypassStatus = 0;
    /* 前面镜头是否被Bypass */
    int forwardBypassStatus = 0;
    /* 后面镜头是否被Bypass */
    int backwardBypassStatus = 0;


    int toCode(uint8_t *buf, int len);

    int getLeftAlarmArea1Status() const;

    void setLeftAlarmArea1Status(int leftAlarmArea1Status);

    int getLeftAlarmArea2Status() const;

    void setLeftAlarmArea2Status(int leftAlarmArea2Status);

    int getRightAlarmArea1Status() const;

    void setRightAlarmArea1Status(int rightAlarmArea1Status);

    int getRightAlarmArea2Status() const;

    void setRightAlarmArea2Status(int rightAlarmArea2Status);

    int getForwardAlarmArea1Status() const;

    void setForwardAlarmArea1Status(int forwardAlarmArea1Status);

    int getForwardAlarmArea2Status() const;

    void setForwardAlarmArea2Status(int forwardAlarmArea2Status);

    int getBackwardAlarmArea1Status() const;

    void setBackwardAlarmArea1Status(int backwardAlarmArea1Status);

    int getBackwardAlarmArea2Status() const;

    void setBackwardAlarmArea2Status(int backwardAlarmArea2Status);

    int getLeftCoverStatus() const;

    void setLeftCoverStatus(int leftCoverStatus);

    int getRightCoverStatus() const;

    void setRightCoverStatus(int rightCoverStatus);

    int getForwardCoverStatus() const;

    void setForwardCoverStatus(int forwardCoverStatus);

    int getBackwardCoverStatus() const;

    void setBackwardCoverStatus(int backwardCoverStatus);

    int getLeftBypassStatus() const;

    void setLeftBypassStatus(int leftBypassStatus);

    int getRightBypassStatus() const;

    void setRightBypassStatus(int rightBypassStatus);

    int getForwardBypassStatus() const;

    void setForwardBypassStatus(int forwardBypassStatus);

    int getBackwardBypassStatus() const;

    void setBackwardBypassStatus(int backwardBypassStatus);

private:
    uint16_t itemId = 0x0003;
    uint8_t itemContentLen = 9;

    /* 行人标志   报警区域	bit	0表示报警区域内没人；1表示有人
       二级报警区域
           0	左侧
           1	右侧
	       2	前向
	       3	后向
       一级报警区域
           4	左侧
	       5	右侧
	       6	前向
	       7	后向
      */
    uint8_t pedestrianStatus;

    /* 摄像头遮挡状态
	 0表示正常；1表示被遮档
     镜头位置	0	左侧
	            1	右侧
	            2	前向
	            3	后向
                4~7	保留
      */
    uint8_t cameraCoverStatus;
    /* 镜头bypass状态
	 0表示正常；1表示bypass
     镜头位置	0	左侧
	            1	右侧
	            2	前向
	            3	后向
                4~7	保留
      */
    uint8_t cameraBypassStatus;

    /* 保留字节 */
    uint8_t reserveByte[6];


};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_BSD_H
