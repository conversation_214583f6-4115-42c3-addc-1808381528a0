//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/7.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_LAMPCONTROL_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_LAMPCONTROL_H


#include <cstdint>

class G3AndMCUUartDetectionInfo_LampControl {
public:
    struct G3AndMCUUartDetectionInfo_LampContro_ChannelControl{
        bool lamp1_open_norma1 : 1;
        bool  lamp_open_infrared1 : 1;
        bool  reserved1 : 1;
        bool  reserved2 : 1;
        bool lamp1_open_norma2 : 1;
        bool  lamp_open_infrared2 : 1;
        bool  reserved3 : 1;
        bool  reserved4 : 1;
    };

    int toCode(uint8_t *buf, int len);


    const G3AndMCUUartDetectionInfo_LampContro_ChannelControl &getChannelControl1() const;

    void setChannelControl1(const G3AndMCUUartDetectionInfo_LampContro_ChannelControl &channelControl1);

    const G3AndMCUUartDetectionInfo_LampContro_ChannelControl &getChannelControl2() const;

    void setChannelControl2(const G3AndMCUUartDetectionInfo_LampContro_ChannelControl &channelControl2);

private:
    uint16_t itemId = 0x0008;
    uint8_t itemContentLen = 4;

    G3AndMCUUartDetectionInfo_LampContro_ChannelControl channelControl1;
    G3AndMCUUartDetectionInfo_LampContro_ChannelControl channelControl2;
    uint8_t reserved[2] = {0x00};

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_LAMPCONTROL_H
