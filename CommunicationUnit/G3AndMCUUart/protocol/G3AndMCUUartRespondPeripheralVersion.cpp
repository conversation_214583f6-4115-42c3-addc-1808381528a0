//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#include <cstdio>
#include "G3AndMCUUartRespondPeripheralVersion.h"

G3AndMCUUartRespondPeripheralVersion::G3AndMCUUartRespondPeripheralVersion() {
    setMsgId(MSGID_RESPOND_PERIPHERAL_VERSION);
}

uint8_t G3AndMCUUartRespondPeripheralVersion::getPeripheralId() const {
    return peripheralId;
}

void G3AndMCUUartRespondPeripheralVersion::setPeripheralId(uint8_t peripheralId) {
    G3AndMCUUartRespondPeripheralVersion::peripheralId = peripheralId;
}

const char *G3AndMCUUartRespondPeripheralVersion::getSoftwareVersion() const {
    return softwareVersion;
}

const char *G3AndMCUUartRespondPeripheralVersion::getProtocolVersion() const {
    return protocolVersion;
}

int G3AndMCUUartRespondPeripheralVersion::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 5;

    /* 先随便塞个内容长度 */
    setContentLen(1);
    CodeUtils::getInstance().uint16ToBb(getContentLen(), buf + index);
    index += 2;
    /* 塞外设ID */
    buf[index] = peripheralId;
    index++;
    /* 塞软件版本号 */
    memcpy(buf + index, softwareVersion, sizeof(softwareVersion));
    index += 4;
    /* 塞通信协议版本号 */
    memcpy(buf + index, protocolVersion, sizeof(protocolVersion));
    index += 4;

    /* 加上头尾那些东西 */
    ret = G3AndMCUUartPacket::toCode(buf, len);

    return ret;
}

int G3AndMCUUartRespondPeripheralVersion::decode(uint8_t *buf, int len) {
    int ret = -1;
    if (G3AndMCUUartPacket::decode(buf, len) == 0) {
        int index = 5;
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        setContentLen(CodeUtils::getInstance().BbToint16(buf + index));
        index += 2;
        /* 外设编号 */
        peripheralId = buf[index];
        index++;
        /* 软件版本号 */
        memcpy(softwareVersion, buf + index, sizeof(softwareVersion));
        index += 4;
        /* 通信协议版本号 */
        memcpy(protocolVersion, buf + index, sizeof(protocolVersion));
        index += 4;
        /* 应用分支编号 */
        memcpy(branchNumber, buf + index, sizeof(branchNumber));
        index += 4;

        ret = 0;
    } else {
        printf("G3AndMCUUartPacket::decode(buf, len) != 0  \n");

    }

    return ret;
}

const char *G3AndMCUUartRespondPeripheralVersion::getBranchNumber() const {
    return branchNumber;
}

std::size_t G3AndMCUUartRespondPeripheralVersion::getSoftwareVersionLen() const {
    return sizeof(softwareVersion);
}

std::size_t G3AndMCUUartRespondPeripheralVersion::getProtocolVersionLen() const {
    return sizeof(protocolVersion);
}

std::size_t G3AndMCUUartRespondPeripheralVersion::getBranchNumberLen() const {
    return sizeof(branchNumber);
}
