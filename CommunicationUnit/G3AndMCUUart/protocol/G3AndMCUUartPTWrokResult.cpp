//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#include "G3AndMCUUartPTWrokResult.h"

G3AndMCUUartPTWrokResult::G3AndMCUUartPTWrokResult() {
    setMsgId(MSGID_PRODUCTION_TEST_WORK_RESULT);

}

int G3AndMCUUartPTWrokResult::decode(uint8_t *buf, int len) {
    int ret = -1;
    if ((G3AndMCUUartPacket::decode(buf, len) == 0)) {
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        int index = 5;
        setContentLen(CodeUtils::getInstance().BbToint16(buf + index));
        index += 2;

        // 数据总数
        int count = buf[index] & 0xff;
        index++;
        // 开始循环解析参数
        for (int i = 0; i < count; i++) {
            // 获取参数ID
            uint16_t paramId = CodeUtils::getInstance().BbToUint16(buf + index);
            index += 2;
            // 获取参数长度
            uint8_t paramLen = buf[index];
            index++;
            // 解析参数
            switch (paramId) {
                case ID_CAN_TEST_RESULT:{
                    G3AndMCUUartPTWrokResult_CANTestResult canTestResult;
                    if(canTestResult.deCode(buf+index,paramLen) == 0){
                        canTestResultList.push_back(canTestResult);
                    }
                }
                    break;
                
            }
            index += paramLen;
        }
        ret = 0;
    }
    return ret;
}

const std::vector<G3AndMCUUartPTWrokResult_CANTestResult> &G3AndMCUUartPTWrokResult::getCanTestResultList() const {
    return canTestResultList;
}
