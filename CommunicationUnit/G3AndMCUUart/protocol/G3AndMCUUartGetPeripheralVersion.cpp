//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#include <cstdio>
#include "G3AndMCUUartGetPeripheralVersion.h"

G3AndMCUUartGetPeripheralVersion::G3AndMCUUartGetPeripheralVersion() {
    setMsgId(MSGID_GET_PERIPHERAL_VERSION);

}

uint8_t G3AndMCUUartGetPeripheralVersion::getPeripheralId() const {
    return peripheralId;
}

void G3AndMCUUartGetPeripheralVersion::setPeripheralId(uint8_t peripheralId) {
    G3AndMCUUartGetPeripheralVersion::peripheralId = peripheralId;
}

int G3AndMCUUartGetPeripheralVersion::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 5;

    /* 先塞个内容长度 */
    setContentLen(1);
    CodeUtils::getInstance().uint16ToBb(getContentLen(), buf + index);
    index += 2;
    buf[index] = peripheralId;
    index++;
    /* 加上头尾那些东西 */
    ret = G3AndMCUUartPacket::toCode(buf, len);

    return ret;
}
