//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/18.
//

#include "G3AndMCUUartDetectionInfo.h"

G3AndMCUUartDetectionInfo::G3AndMCUUartDetectionInfo() {
    setMsgId(MSGID_DETECTION_INFO);

}

G3AndMCUUartDetectionInfo::~G3AndMCUUartDetectionInfo() {

}

int G3AndMCUUartDetectionInfo::toCode(uint8_t *buf, int len) {
    int ret = -1;
    if (len >= (9 + 10)) {
        int index = 5;
        int msgContent = 0;
        /* 先随便塞个内容长度 */
        CodeUtils::getInstance().uint16ToBb(0, buf + index);
        index += 2;
        /* 数据项总数 */
        itemSum = adasInfoList.size() + bsdInfoList.size() + sysStatusInfoList.size() + ioInfoList.size() + lampControlList.size() + indoorLocationList.size();
        buf[index] = itemSum;
        index++;
        msgContent++;

        /* 先塞ADAS数据项 */
        if (adasInfoList.size() > 0) {
            for (std::size_t i = 0; i < adasInfoList.size(); i++) {
                int itemLen = adasInfoList[i].toCode(buf + index, len);
                index += itemLen;
                msgContent += itemLen;
            }
        }
        /* 再塞BSD数据项 */
        if (bsdInfoList.size() > 0) {
            for (std::size_t i = 0; i < bsdInfoList.size(); i++) {
                int itemLen = bsdInfoList[i].toCode(buf + index, len);
                index += itemLen;
                msgContent += itemLen;
            }
        }
        /* 再塞系统错误信息数据项 */
        if (sysStatusInfoList.size() > 0) {
            for (std::size_t i = 0; i < sysStatusInfoList.size(); i++) {
                int itemLen = sysStatusInfoList[i].toCode(buf + index, len);
                index += itemLen;
                msgContent += itemLen;
            }
        }

        /* 再塞IO状态信息数据项 */
        if (ioInfoList.size() > 0) {
            for (std::size_t i = 0; i < ioInfoList.size(); i++) {
                int itemLen = ioInfoList[i].toCode(buf + index, len);
                index += itemLen;
                msgContent += itemLen;
            }
        }

        /* 再塞灯控状态信息数据项 */
        if (lampControlList.size() > 0) {
            for (std::size_t i = 0; i < lampControlList.size(); i++) {
                int itemLen = lampControlList[i].toCode(buf + index, len);
                index += itemLen;
                msgContent += itemLen;
            }
        }

        /* 再塞室内定位信息数据项 */
        if (indoorLocationList.size() > 0) {
            for (std::size_t i = 0; i < indoorLocationList.size(); i++) {
                int itemLen = indoorLocationList[i].toCode(buf + index, len);
                index += itemLen;
                msgContent += itemLen;
            }
        }

        /* 把真实的内容长度塞进去 */
        setContentLen(msgContent);
        CodeUtils::getInstance().uint16ToBb(getContentLen(), buf + 5);
        index += 2;
        /* 加上头尾那些东西 */
        ret = G3AndMCUUartPacket::toCode(buf, len);
    }
    return ret;
}

int G3AndMCUUartDetectionInfo::addADASInfo(G3AndMCUUartDetectionInfo_ADAS &adasInfo) {
    adasInfoList.push_back(adasInfo);
    return 0;
}

int G3AndMCUUartDetectionInfo::addBSDInfo(G3AndMCUUartDetectionInfo_BSD &bsdInfo) {
    bsdInfoList.push_back(bsdInfo);
    return 0;
}

int G3AndMCUUartDetectionInfo::addSysStatusInfo(G3AndMCUUartDetectionInfo_SYSStatus &sysStatusInfo) {
    sysStatusInfoList.push_back(sysStatusInfo);
    return 0;
}

int G3AndMCUUartDetectionInfo::addIOInfo(G3AndMCUUartDetectionInfo_IOStatus &ioStatus) {
    ioInfoList.push_back(ioStatus);
    return 0;
}

int G3AndMCUUartDetectionInfo::addLampControl(G3AndMCUUartDetectionInfo_LampControl &lampControl) {
    if(lampControlList.empty()){
        lampControlList.push_back(lampControl);
    }else{
        G3AndMCUUartDetectionInfo_LampControl::G3AndMCUUartDetectionInfo_LampContro_ChannelControl temp = lampControlList[0].getChannelControl1();
        temp.lamp1_open_norma1 |= lampControl.getChannelControl1().lamp1_open_norma1;
        temp.lamp_open_infrared1 |=lampControl.getChannelControl1().lamp_open_infrared1;
        temp.lamp1_open_norma2 |= lampControl.getChannelControl1().lamp1_open_norma2;
        temp.lamp_open_infrared2 |=lampControl.getChannelControl1().lamp_open_infrared2;
        lampControlList[0].setChannelControl1(temp);
    }

    return 0;
}

int G3AndMCUUartDetectionInfo::addIndoorLocation(G3AndMCUUartDetectionInfo_IndoorLocation &indoorLocation) {
    if(indoorLocationList.empty()){
        indoorLocationList.push_back(indoorLocation);
    }else{
        indoorLocationList.clear();
        indoorLocationList.push_back(indoorLocation);
    }
    return 0;
}
