//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTPERIPHERALUPGRADERESULT_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTPERIPHERALUPGRADERESULT_H

#include "G3AndMCUUartPacket.h"

class G3AndMCUUartPeripheralUpgradeResult : public G3AndMCUUartPacket {
public:
    G3AndMCUUartPeripheralUpgradeResult();

    int decode(uint8_t *buf, int len) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

    uint8_t getUpgradeResult() const;

    void setUpgradeResult(uint8_t upgradeResult);

private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;
    /* 升级结果 */
    uint8_t upgradeResult = 0x00;

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTPERIPHERALUPGRADERESULT_H
