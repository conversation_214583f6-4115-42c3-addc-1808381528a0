//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#include "G3AndMCUUartCANDataTransmissionSwitch.h"

G3AndMCUUartCANDataTransmissionSwitch::G3AndMCUUartCANDataTransmissionSwitch() {
    setMsgId(MSGID_CAN_DATA_TRANSMISSION_SWITCH);
}

int G3AndMCUUartCANDataTransmissionSwitch::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 5;

    /* 先塞个内容长度 */
    setContentLen(1);
    CodeUtils::getInstance().uint16ToBb(getContentLen(), buf + index);
    index += 2;
    buf[index] = isOpen;
    index++;
    /* 加上头尾那些东西 */
    ret = G3AndMCUUartPacket::toCode(buf, len);

    return ret;
}

uint8_t G3AndMCUUartCANDataTransmissionSwitch::getIsOpen() const {
    return isOpen;
}

void G3AndMCUUartCANDataTransmissionSwitch::setIsOpen(uint8_t isOpen) {
    G3AndMCUUartCANDataTransmissionSwitch::isOpen = isOpen;
}
