//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/20.
//

#include "G3AndMCUUartAlarmInfo_DSM.h"

uint8_t G3AndMCUUartAlarmInfo_DSM::getEventStatus() const {
    return eventStatus;
}

void G3AndMCUUartAlarmInfo_DSM::setEventStatus(uint8_t eventStatus) {
    G3AndMCUUartAlarmInfo_DSM::eventStatus = eventStatus;
}



const uint8_t *G3AndMCUUartAlarmInfo_DSM::getReserved() const {
    return reserved;
}

int G3AndMCUUartAlarmInfo_DSM::toCode(uint8_t *buf, int len) {
    int ret = -1;

    int index = 0;
    /* 塞入ID */
    CodeUtils::getInstance().uint16ToBb(itemId, buf + index);
    index += 2;
    /* 塞入长度 */
    buf[index] = itemContentLen;
    index++;
    /* 塞入标志状态 */
    buf[index] = eventStatus;
    index++;
    /* 塞入报警/事件类型 */
    memcpy(buf+index,&eventCode,4);
    index += 4;
    /* 塞入预留 */
    memcpy(buf + index, reserved, sizeof(reserved));
    index += sizeof(reserved);


    ret = index;

    return ret;
}

const G3AndMCUUartAlarmInfo_DSM::RS485DSMAlarmType1 &G3AndMCUUartAlarmInfo_DSM::getEventCode() const {
    return eventCode;
}

void G3AndMCUUartAlarmInfo_DSM::setEventCode(const G3AndMCUUartAlarmInfo_DSM::RS485DSMAlarmType1 &eventCode) {
    G3AndMCUUartAlarmInfo_DSM::eventCode = eventCode;
}
