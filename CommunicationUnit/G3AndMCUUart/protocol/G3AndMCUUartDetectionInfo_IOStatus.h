//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/2.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_IOSTATUS_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_IOSTATUS_H

#include <cstdint>
#include "CodeUtils.h"

struct G3AndMCUUart_IO_Level {
    int io_1: 1;
    int io_2: 1;
    int io_3: 1;
    int io_4: 1;
    int io_5: 1;
    int io_6: 1;
    int io_7: 1;
    int io_8: 1;
    int io_9: 1;
    int io_10: 1;
    int io_11: 1;
    int io_12: 1;
    int io_13: 1;
    int io_14: 1;
    int io_15: 1;
    int io_16: 1;
};


class G3AndMCUUartDetectionInfo_IOStatus {
public:
    /* IO有效标志-可用 */
    const static int IO_STATUS_ENABLE = 0;
    /* IO有效标志-不可用 */
    const static int IO_STATUS_DISENABLE = 1;

    /* IO电平--高 */
    const static int IO_LEVEL_HIGHT = 1;
    /* IO电平--低 */
    const static int IO_LEVEL_LOW = 0;

    int toCode(uint8_t *buf, int len);

    uint8_t getInputStatus() const;

    void setInputStatus(uint8_t inputStatus);

    uint8_t getOutputStatus() const;

    void setOutputStatus(uint8_t outputStatus);

    int getIoIn1Level() const;

    void setIoIn1Level(int ioIn1Level);

    int getIoIn2Level() const;

    void setIoIn2Level(int ioIn2Level);

    int getIoIn3Level() const;

    void setIoIn3Level(int ioIn3Level);

private:
    uint16_t itemId = 0x0007;
    uint8_t itemContentLen = 6;
    /* 输入端口有效标志	0x00无效；0x01为有效 */
    uint8_t inputStatus = 0;
    /* 输入端口值	bit0表示输入端口1，bit15表示输入端口16    值为0为低电平或接地，值为1为高电平或对地断开 */
    G3AndMCUUart_IO_Level inputLevel = {0};
    /* 输出端口有效标志	0x00无效；0x01为有效 */
    uint8_t outputStatus = 0;
    /* 输出端口值	bit0表示输出端口1，bit15表示输出端口16    值为0为低电平或接地，值为1为高电平或对地断开 */
    G3AndMCUUart_IO_Level outputLevel = {0};


};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_IOSTATUS_H
