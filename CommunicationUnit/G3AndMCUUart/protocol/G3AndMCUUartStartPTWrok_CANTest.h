//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTSTARTPTWROK_CANTEST_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTSTARTPTWROK_CANTEST_H


#include <cstdint>

/**
 * CAN接口测试
 */
class G3AndMCUUartStartPTWrok_CANTest {
public:
    int toCode(uint8_t *buf, int len);

    uint16_t getBaudRate() const;

    void setBaudRate(uint16_t baudRate);

private:
    uint16_t itemId = 0x0001;
    uint8_t itemContentLen = 2;

    /* CAN接口测试，数值为波特率，目前为500 */
    uint16_t baudRate = 500;



};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTSTARTPTWROK_CANTEST_H
