//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/15.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTUNIVERSALANSWER_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTUNIVERSALANSWER_H

#include "G3AndMCUUartPacket.h"

class G3AndMCUUartUniversalAnswer : public G3AndMCUUartPacket {
public:
    G3AndMCUUartUniversalAnswer();

    ~G3AndMCUUartUniversalAnswer();

    int toCode(uint8_t *buf, int len) override;

    int decode(uint8_t *buf, int len) override;

    uint8_t getQMsgId() const;

    void setQMsgId(uint8_t qMsgId);

    uint8_t getResult() const;

    void setResult(uint8_t result);

private:

    /* 被应答的消息的命令字 */
    uint8_t qMsgId;
    /* 结果 0：成功/确认；1：失败；2：消息有误；3：不支持*/
    uint8_t result;

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTUNIVERSALANSWER_H
