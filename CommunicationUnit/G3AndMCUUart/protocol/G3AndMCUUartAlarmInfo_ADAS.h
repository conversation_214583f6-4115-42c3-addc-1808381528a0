//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/20.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTALARMINFO_ADAS_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTALARMINFO_ADAS_H

#include <cstdint>
#include "CodeUtils.h"

/**
 * MCU的报警事件指令中的ADAS报警事件数据项
 */
class G3AndMCUUartAlarmInfo_ADAS {
public:

    /**
     * 封装
     *
     * @param buf ： 存放封装完成后的数据的指针
     * @param len ： 存放封装完成后的数据的指针的长度
     *
     * @return  封装完成后的数据长度
     */
    int toCode(uint8_t *buf, int len);



    uint8_t getEventStatus() const;

    void setEventStatus(uint8_t eventStatus);

    uint8_t getEventCode() const;

    void setEventCode(uint8_t eventCode);

    uint8_t getCollisionTime() const;

    void setCollisionTime(uint8_t collisionTime);

    uint8_t getLdwType() const;

    void setLdwType(uint8_t ldwType);

    uint8_t getRoadSignType() const;

    void setRoadSignType(uint8_t roadSignType);

    const uint8_t *getReserved() const;

private:
    /* 数据项的ID */
    uint16_t itemId = 0x0064;
    /* 数据线项的内容长度 */
    uint8_t itemContentLen = 9;


    /* 标志状态	uint8_t	0x00：不可用  0x01：开始标志  0x02：结束标志  该字段仅适用于有开始和结束标志类型的报警或事件，报警类型或事件类型无开始和结束标志，则该位不可用，填入0x00即可 */
    uint8_t eventStatus;
    /* 报警/事件类型	uint8_t	0x01：前向碰撞报警  0x02：车道偏离报警  0x03：车距过近报警  0x04：行人碰撞报警  0x05：频繁变道报警  0x06：道路标识超限报警  0x07：防溜车提醒  0x08：前车启动提醒  0x10：道路标志识别事件  0x11：主动抓拍事件  0x12~0x1F：用户自定义*/
    uint8_t eventCode;
    /* 碰撞时间	uint8_t	单位100ms，范围0~100，仅报警类型为0x01、0x02和0x04时有效。 */
    uint8_t collisionTime;
    /* 偏离类型	uint8_t	0x01：左侧虚线偏离  0x02：右侧虚线偏离  0x03：左侧实线偏离  0x04：右侧实线偏离  0x05：双侧偏离  仅报警类型为0x02时有效*/
    uint8_t ldwType;
    /* 道路标志识别类型	uint8_t	0x01：限速标志  0x02：限高标志  0x03：限重标志  仅报警类型为0x06和0x10时有效。*/
    uint8_t roadSignType;
    /* 预留 */
    uint8_t reserved[4] = {0x00};


};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTALARMINFO_ADAS_H
