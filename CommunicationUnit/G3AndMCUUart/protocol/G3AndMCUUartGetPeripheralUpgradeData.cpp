//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#include "G3AndMCUUartGetPeripheralUpgradeData.h"

G3AndMCUUartGetPeripheralUpgradeData::G3AndMCUUartGetPeripheralUpgradeData() {
    setMsgId(MSGID_GET_PERIPHERAL_UPGRADE_DATA);
}

uint8_t G3AndMCUUartGetPeripheralUpgradeData::getPeripheralId() const {
    return peripheralId;
}

void G3AndMCUUartGetPeripheralUpgradeData::setPeripheralId(uint8_t peripheralId) {
    G3AndMCUUartGetPeripheralUpgradeData::peripheralId = peripheralId;
}

uint32_t G3AndMCUUartGetPeripheralUpgradeData::getOffset() const {
    return offset;
}

void G3AndMCUUartGetPeripheralUpgradeData::setOffset(uint32_t offset) {
    G3AndMCUUartGetPeripheralUpgradeData::offset = offset;
}

int G3AndMCUUartGetPeripheralUpgradeData::decode(uint8_t *buf, int len) {
    int ret = -1;
    if (G3AndMCUUartPacket::decode(buf, len) == 0) {
        int index = 5;
        //直接从内容长度开始解析  头尾那些不管 因为已经校验完了  没作用了
        setContentLen(CodeUtils::getInstance().BbToint16(buf + index));
        index += 2;
        /* 外设编号 */
        peripheralId = buf[index];
        index++;
        /* 文件偏移地址 */
        offset = CodeUtils::getInstance().BbToUint32(buf + index);
        index += 4;

        ret = 0;
    }
    return ret;
}
