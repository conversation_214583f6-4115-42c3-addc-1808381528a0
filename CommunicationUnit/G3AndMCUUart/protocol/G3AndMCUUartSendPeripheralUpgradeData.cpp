//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#include "G3AndMCUUartSendPeripheralUpgradeData.h"

G3AndMCUUartSendPeripheralUpgradeData::G3AndMCUUartSendPeripheralUpgradeData() {
    setMsgId(MSGID_SEND_PERIPHERAL_UPGRADE_DATA);
}

uint8_t G3AndMCUUartSendPeripheralUpgradeData::getPeripheralId() const {
    return peripheralId;
}

void G3AndMCUUartSendPeripheralUpgradeData::setPeripheralId(uint8_t peripheralId) {
    G3AndMCUUartSendPeripheralUpgradeData::peripheralId = peripheralId;
}

uint32_t G3AndMCUUartSendPeripheralUpgradeData::getOffset() const {
    return offset;
}

void G3AndMCUUartSendPeripheralUpgradeData::setOffset(uint32_t offset) {
    G3AndMCUUartSendPeripheralUpgradeData::offset = offset;
}

const uint8_t *G3AndMCUUartSendPeripheralUpgradeData::getFileData() const {
    return fileData;
}

uint8_t G3AndMCUUartSendPeripheralUpgradeData::getFileDataLen() const {
    return fileDataLen;
}

void G3AndMCUUartSendPeripheralUpgradeData::setFileDataLen(uint8_t fileDataLen) {
    G3AndMCUUartSendPeripheralUpgradeData::fileDataLen = fileDataLen;
}

int G3AndMCUUartSendPeripheralUpgradeData::toCode(uint8_t *buf, int len) {
    int ret = -1;
    if (len >= (9 + 10)) {
        int index = 5;

        /* 塞内容长度 */
        setContentLen(5 + fileDataLen);
        CodeUtils::getInstance().uint16ToBb(getContentLen(), buf + index);
        index += 2;
        /* 塞外设ID */
        buf[index] = peripheralId;
        index++;
        /* 塞偏移地址 */
        CodeUtils::getInstance().uint32ToBb(offset, buf + index);
        index += 4;
        /* 塞文件数据 */
        if (fileDataLen > 0) {
            memcpy(buf + index, fileData, fileDataLen);
            index += fileDataLen;
        }


        /* 加上头尾那些东西 */
        ret = G3AndMCUUartPacket::toCode(buf, len);
    }
    return ret;
}

void G3AndMCUUartSendPeripheralUpgradeData::setFileData(uint8_t *data, uint8_t dataLen) {
    if (dataLen > 0) {
        fileDataLen = dataLen;
        memcpy(fileData, data, fileDataLen);
    }

}
