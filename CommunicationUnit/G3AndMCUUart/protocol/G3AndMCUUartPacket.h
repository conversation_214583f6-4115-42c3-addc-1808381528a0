//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/15.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTPACKET_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTPACKET_H


#include <cstdint>
#include <CodeUtils.h>

class G3AndMCUUartPacket {
public:

    /* 一包数据最小的长度因为是13个字节 */
    const static int MIN_DATA_LEN = 10;


    /* 通用应答 */
    const static int MSGID_UNIVERSALANSWER = 0x10;
    /* 周期数据命令（下行） */
    const static int MSGID_DETECTION_INFO = 0xC3;
    /* 实时数据命令（上行） */
    const static int MSGID_VEHICLE_REALTIME_STATUS = 0x43;
    /* 报警命令 */
    const static int MSGID_ALARM_INFO = 0x40;
    /* 读取外设版本信息 */
    const static int MSGID_GET_PERIPHERAL_VERSION = 0xA3;
    /* 外设版本信息 */
    const static int MSGID_RESPOND_PERIPHERAL_VERSION = 0x23;
    /* 要求外设升级 */
    const static int MSGID_START_PERIPHERAL_UPGRADE = 0xAB;
    /* 请求升级文件数据 */
    const static int MSGID_GET_PERIPHERAL_UPGRADE_DATA = 0x2C;
    /* 发送升级文件数据 */
    const static int MSGID_SEND_PERIPHERAL_UPGRADE_DATA = 0xAC;
    /* 升级结果 */
    const static int MSGID_PERIPHERAL_UPGRADE_RESULT = 0x2B;
    /* 开启/停止CAN数据直传 */
    const static int MSGID_CAN_DATA_TRANSMISSION_SWITCH = 0xD0;
    /* CAN数据直传 */
    const static int MSGID_ORIGINAL_CAN_DATA = 0x50;
    /* 发送调试命令 */
    const static int MSGID_SEDN_DEBUG_CMD = 0xD1;
    /* 调试信息上传 */
    const static int MSGID_DEBUG_MSG = 0x51;
    /* 发送生产测试命令 */
    const static int MSGID_START_PRODUCTION_TEST_WORK = 0xD2;
    /* 生产测试结果上传 */
    const static int MSGID_PRODUCTION_TEST_WORK_RESULT = 0x52;




    /* 通用应答的结果---成功 */
    const static uint8_t RESULT_SUCCESS = 0x00;
    /* 通用应答的结果---失败 */
    const static uint8_t RESULT_FAILED = 0x01;
    /* 通用应答的结果---消息有误 */
    const static uint8_t RESULT_MSG_ERROR = 0x02;
    /* 通用应答的结果---不支持 */
    const static uint8_t RESULT_UNSUPPORT = 0x03;


    /* 外设ID----MCU */
    const static uint8_t PERIPHERAL_ID_MCU = 0xF0;


    /**
     * 把对象转成一包16进制数据包
     *
     * @param buf ： 用来存放数据包的内存指针
     * @param len ： 内存指针的大小
     *
     * @return 16进制数据包的大小
     *
     * */
    virtual int toCode(uint8_t *buf, int len);

    /**
     * 解析一包数据包转成对象
     *
     * @param buf ： 用来存放数据包的内存指针
     * @param len ： 内存指针的大小
     *
     * @return 结果  0：成功   其他：失败
     *
     * */
    virtual int decode(uint8_t *buf, int len);

    uint8_t getHead() const;

    void setHead(uint8_t head);

    uint16_t getFlowId() const;

    void setFlowId(uint16_t flowId);

    uint8_t getReserved() const;

    void setReserved(uint8_t reserved);

    uint8_t getMsgId() const;

    void setMsgId(uint8_t msgId);

    uint16_t getContentLen() const;

    void setContentLen(uint16_t contentLen);

    uint16_t getCrcCheckCode() const;

    void setCrcCheckCode(uint16_t crcCheckCode);

    uint8_t getTail() const;

    void setTail(uint8_t tail);


private:
    uint8_t head = 0x5D;
    uint16_t flowId;
    uint8_t reserved = 0x00;
    uint8_t msgId;
    uint16_t contentLen;
    uint16_t crcCheckCode;
    uint8_t tail = 0x5E;

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTPACKET_H
