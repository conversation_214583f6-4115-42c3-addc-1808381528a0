//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTPTWROKRESULT_CANTESTRESULT_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTPTWROKRESULT_CANTESTRESULT_H

#include <cstdint>

/**
 * CAN接口测试的结果
 */
class G3AndMCUUartPTWrokResult_CANTestResult {
public:
    int deCode(uint8_t *buf, int len);

    uint8_t getResult() const;

    uint16_t getItemId() const;

    uint8_t getItemContentLen() const;

private:
    uint16_t itemId = 0x0001;
    uint8_t itemContentLen = 1;

    /* CAN接口测试，数值为测试结果，0为测试成功，其余为错误值 */
    uint8_t result = 0;

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTPTWROKRESULT_CANTESTRESULT_H
