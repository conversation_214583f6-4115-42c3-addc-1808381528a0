//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/25.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTPTWROKRESULT_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTPTWROKRESULT_H

#include "G3AndMCUUartPacket.h"
#include <vector>
#include "G3AndMCUUartPTWrokResult_CANTestResult.h"
class G3AndMCUUartPTWrokResult : public G3AndMCUUartPacket {
public:
    const static uint16_t ID_CAN_TEST_RESULT = 0x0001; // CAN接口测试结果

    G3AndMCUUartPTWrokResult();
    int decode(uint8_t *buf, int len) override;

    const std::vector<G3AndMCUUartPTWrokResult_CANTestResult> &getCanTestResultList() const;

private:
    uint8_t itemSum = 0;
    std::vector<G3AndMCUUartPTWrokResult_CANTestResult> canTestResultList;
};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTPTWROKRESULT_H
