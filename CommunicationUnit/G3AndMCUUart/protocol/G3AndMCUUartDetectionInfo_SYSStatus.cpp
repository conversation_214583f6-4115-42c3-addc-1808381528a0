//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/18.
//

#include "G3AndMCUUartDetectionInfo_SYSStatus.h"

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getTfCardStatus() const {
    return tfCardStatus;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setTfCardStatus(uint8_t tfCardStatus) {
    G3AndMCUUartDetectionInfo_SYSStatus::tfCardStatus = tfCardStatus;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getHighTemperature() const {
    return highTemperature;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setHighTemperature(uint8_t highTemperature) {
    G3AndMCUUartDetectionInfo_SYSStatus::highTemperature = highTemperature;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getAdasCameraStatus() const {
    return adasCameraStatus;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setAdasCameraStatus(uint8_t adasCameraStatus) {
    G3AndMCUUartDetectionInfo_SYSStatus::adasCameraStatus = adasCameraStatus;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getBsdLeftCameraStatus() const {
    return bsdLeftCameraStatus;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setBsdLeftCameraStatus(uint8_t bsdLeftCameraStatus) {
    G3AndMCUUartDetectionInfo_SYSStatus::bsdLeftCameraStatus = bsdLeftCameraStatus;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getBsdRightCameraStatus() const {
    return bsdRightCameraStatus;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setBsdRightCameraStatus(uint8_t bsdRightCameraStatus) {
    G3AndMCUUartDetectionInfo_SYSStatus::bsdRightCameraStatus = bsdRightCameraStatus;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getDsmCameraStatus() const {
    return dsmCameraStatus;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setDsmCameraStatus(uint8_t dsmCameraStatus) {
    G3AndMCUUartDetectionInfo_SYSStatus::dsmCameraStatus = dsmCameraStatus;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getAdasAlgorithmStatus() const {
    return adasAlgorithmStatus;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setAdasAlgorithmStatus(uint8_t adasAlgorithmStatus) {
    G3AndMCUUartDetectionInfo_SYSStatus::adasAlgorithmStatus = adasAlgorithmStatus;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getBsdLeftAlgorithmStatus() const {
    return bsdLeftAlgorithmStatus;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setBsdLeftAlgorithmStatus(uint8_t bsdLeftAlgorithmStatus) {
    G3AndMCUUartDetectionInfo_SYSStatus::bsdLeftAlgorithmStatus = bsdLeftAlgorithmStatus;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getBsdRightAlgorithmStatus() const {
    return bsdRightAlgorithmStatus;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setBsdRightAlgorithmStatus(uint8_t bsdRightAlgorithmStatus) {
    G3AndMCUUartDetectionInfo_SYSStatus::bsdRightAlgorithmStatus = bsdRightAlgorithmStatus;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getDsmAlgorithmStatus() const {
    return dsmAlgorithmStatus;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setDsmAlgorithmStatus(uint8_t dsmAlgorithmStatus) {
    G3AndMCUUartDetectionInfo_SYSStatus::dsmAlgorithmStatus = dsmAlgorithmStatus;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getDsmFindFace() const {
    return dsmFindFace;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setDsmFindFace(uint8_t dsmFindFace) {
    G3AndMCUUartDetectionInfo_SYSStatus::dsmFindFace = dsmFindFace;
}

int G3AndMCUUartDetectionInfo_SYSStatus::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 塞入ID */
    CodeUtils::getInstance().uint16ToBb(itemId, buf + index);
    index += 2;
    /* 塞入长度 */
    buf[index] = itemContentLen;
    index++;
    /* 整理行人状态 */
    systemStatus = 0;
    if (tfCardStatus) {
        systemStatus |= 0x01;
    }
    if (highTemperature) {
        systemStatus |= 0x02;
    }
    if (adasCameraStatus) {
        systemStatus |= 0x04;
    }
    if (bsdLeftCameraStatus) {
        systemStatus |= 0x08;
    }
    if (bsdRightCameraStatus) {
        systemStatus |= 0x10;
    }
    if (dsmCameraStatus) {
        systemStatus |= 0x20;
    }
    if (adasAlgorithmStatus) {
        systemStatus |= 0x40;
    }
    if (bsdLeftAlgorithmStatus) {
        systemStatus |= 0x80;
    }
    if (bsdRightAlgorithmStatus) {
        systemStatus |= 0x100;
    }
    if (dsmAlgorithmStatus) {
        systemStatus |= 0x200;
    }
    if (dsmFindFace) {
        systemStatus |= 0x8000;
    }
    if (isCameraCover_1) {
        systemStatus |= 0x10000;
    }
    if (isCameraCover_2) {
        systemStatus |= 0x20000;
    }
    if (isCameraCover_3) {
        systemStatus |= 0x40000;
    }
    if (isCameraCover_4) {
        systemStatus |= 0x80000;
    }
    if (isCameraCover_5) {
        systemStatus |= 0x100000;
    }
    if (isCameraCover_6) {
        systemStatus |= 0x200000;
    }
    if (isCameraCover_7) {
        systemStatus |= 0x400000;
    }
    if (isCameraCover_8) {
        systemStatus |= 0x800000;
    }


    CodeUtils::getInstance().uint32ToBb(systemStatus,buf+index);
    index+=4;

    ret = index;
    return ret;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getIsCameraCover1() const {
    return isCameraCover_1;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setIsCameraCover1(uint8_t isCameraCover1) {
    isCameraCover_1 = isCameraCover1;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getIsCameraCover2() const {
    return isCameraCover_2;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setIsCameraCover2(uint8_t isCameraCover2) {
    isCameraCover_2 = isCameraCover2;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getIsCameraCover3() const {
    return isCameraCover_3;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setIsCameraCover3(uint8_t isCameraCover3) {
    isCameraCover_3 = isCameraCover3;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getIsCameraCover4() const {
    return isCameraCover_4;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setIsCameraCover4(uint8_t isCameraCover4) {
    isCameraCover_4 = isCameraCover4;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getIsCameraCover5() const {
    return isCameraCover_5;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setIsCameraCover5(uint8_t isCameraCover5) {
    isCameraCover_5 = isCameraCover5;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getIsCameraCover6() const {
    return isCameraCover_6;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setIsCameraCover6(uint8_t isCameraCover6) {
    isCameraCover_6 = isCameraCover6;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getIsCameraCover7() const {
    return isCameraCover_7;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setIsCameraCover7(uint8_t isCameraCover7) {
    isCameraCover_7 = isCameraCover7;
}

uint8_t G3AndMCUUartDetectionInfo_SYSStatus::getIsCameraCover8() const {
    return isCameraCover_8;
}

void G3AndMCUUartDetectionInfo_SYSStatus::setIsCameraCover8(uint8_t isCameraCover8) {
    isCameraCover_8 = isCameraCover8;
}
