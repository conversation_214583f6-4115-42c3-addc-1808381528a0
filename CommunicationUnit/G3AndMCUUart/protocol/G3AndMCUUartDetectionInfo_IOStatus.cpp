//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/2.
//

#include "G3AndMCUUartDetectionInfo_IOStatus.h"

int G3AndMCUUartDetectionInfo_IOStatus::toCode(uint8_t *buf, int len) {
    int ret = -1;
    int index = 0;
    /* 塞入ID */
    CodeUtils::getInstance().uint16ToBb(itemId, buf + index);
    index += 2;
    /* 塞入长度 */
    buf[index] = itemContentLen;
    index++;
    /* 塞入输入端口是否可用的标识 */
    buf[index] = inputStatus;
    index++;
    /* 塞入输入端口的电平 */
    int ilevel = 0;
    memcpy(&ilevel, &inputLevel, 2);
    CodeUtils::getInstance().uint16ToBb(ilevel, buf + index);
    index += 2;

    /* 塞入输出端口是否可用的标识 */
    buf[index] = outputStatus;
    index++;
    /* 塞入输出端口的电平 */
    int olevel = 0;
    memcpy(&olevel, &outputLevel, 2);
    CodeUtils::getInstance().uint16ToBb(olevel, buf + index);
    index += 2;

    ret = index;

    return ret;
}

uint8_t G3AndMCUUartDetectionInfo_IOStatus::getInputStatus() const {
    return inputStatus;
}

void G3AndMCUUartDetectionInfo_IOStatus::setInputStatus(uint8_t inputStatus) {
    G3AndMCUUartDetectionInfo_IOStatus::inputStatus = inputStatus;
}

uint8_t G3AndMCUUartDetectionInfo_IOStatus::getOutputStatus() const {
    return outputStatus;
}

void G3AndMCUUartDetectionInfo_IOStatus::setOutputStatus(uint8_t outputStatus) {
    G3AndMCUUartDetectionInfo_IOStatus::outputStatus = outputStatus;
}

int G3AndMCUUartDetectionInfo_IOStatus::getIoIn1Level() const {
    return inputLevel.io_1;
}

void G3AndMCUUartDetectionInfo_IOStatus::setIoIn1Level(int ioIn1Level) {
    inputLevel.io_1 = ioIn1Level;
}

int G3AndMCUUartDetectionInfo_IOStatus::getIoIn2Level() const {
    return inputLevel.io_2;
}

void G3AndMCUUartDetectionInfo_IOStatus::setIoIn2Level(int ioIn2Level) {
    inputLevel.io_2 = ioIn2Level;
}

int G3AndMCUUartDetectionInfo_IOStatus::getIoIn3Level() const {
    return inputLevel.io_3;
}

void G3AndMCUUartDetectionInfo_IOStatus::setIoIn3Level(int ioIn3Level) {
    inputLevel.io_3 = ioIn3Level;
}
