//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/22.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTDECODER_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTDECODER_H

#include "CommunicationDataCallback.h"
#include "G3AndMCUUartDetectionInfo.h"
#include "G3AndMCUUartVehicleRealTimeStatus.h"
#include "G3AndMCUUartUniversalAnswer.h"
#include "G3AndMCUUartRespondPeripheralVersion.h"
#include "G3AndMCUUartGetPeripheralUpgradeData.h"
#include "G3AndMCUUartPeripheralUpgradeResult.h"


namespace vis {

    class G3AndMCUUartDecoder {
    public:

        const static uint8_t IDENTIFICATION_HEAD = 0x5D;
        const static uint8_t IDENTIFICATION_TAIL = 0x5E;
        const static int MAX_DATA_CACHE_SIZE = 1024 * 1024 * 1;
        const static int MAX_DATA_DATA_SIZE = 1024 * 1024;
        const static int MSG_MIN_SIZE = 10;


        G3AndMCUUartDecoder();

        void onDataReceived(const uint8_t *buf, int len);

        void resetData();

        void addANewCharToCache(uint8_t nc);

        void doDecode(uint8_t *buf, int len);

        void setInterface(CommunicationDataCallback &communicationDataCallback);

    private:
        /* 临时放数据的缓存 */
        uint8_t *dataCache;
        /* 临时放数据的缓存当前被读写到了哪里 */
        int point = 0;
        /* 转义后的正式的数据包的内存 */
        uint8_t *formalDataBuf;

        /* 返回解析结果用的callback */
        CommunicationDataCallback *callback;

    };

}
#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTDECODER_H
