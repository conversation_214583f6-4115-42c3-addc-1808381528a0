//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/18.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_ADAS_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_ADAS_H

#include <cstdint>
#include "CodeUtils.h"

class G3AndMCUUartDetectionInfo_ADAS {
public:

    int toCode(uint8_t *buf, int len);

    uint16_t getItemId() const;

    void setItemId(uint16_t itemId);

    uint8_t getItemContentLen() const;

    void setItemContentLen(uint8_t itemContentLen);

    uint8_t getHmw() const;

    void setHmw(uint8_t hmw);

    uint16_t getCarDistance() const;

    void setCarDistance(uint16_t carDistance);

    uint16_t getLaneLineDistanceLeft() const;

    void setLaneLineDistanceLeft(uint16_t laneLineDistanceLeft);

    uint16_t getLaneLineDistanceRight() const;

    void setLaneLineDistanceRight(uint16_t laneLineDistanceRight);

    uint8_t getPdw() const;

    void setPdw(uint8_t pdw);

    uint16_t getPedestrianDistance() const;

    void setPedestrianDistance(uint16_t pedestrianDistance);

    uint8_t getObjectStatus() const;

    void setObjectStatus(uint8_t objectStatus);

private:
    uint16_t itemId = 0x0001;
    uint8_t itemContentLen = 10;

    /* 车距监控时间 单位 0.1s。0-100,其它值表示没检测到车 */
    uint8_t hmw;
    /* 车距监控距离 单位 单位0.01m。0-30000,其它值表示没检测到车 */
    uint16_t carDistance;
    /* 左车道线距离 单位mm。-5000~5000,其它值表示没检测到线 */
    uint16_t laneLineDistance_left;
    /* 右车道线距离 单位mm。-5000~5000,其它值表示没检测到线 */
    uint16_t laneLineDistance_right;
    /* 行人碰撞时间 单位 0.1s。0-100,其它值表示没检测到人 */
    uint8_t pdw;
    /* 行人距离 单位0.01m。0-5000,其它值表示没检测到人 */
    uint16_t pedestrianDistance;
    /* 状态信息  Bit0----1红车，0为绿车;Bit1----1为红人，0为绿人 */
    uint8_t objectStatus;

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_ADAS_H
