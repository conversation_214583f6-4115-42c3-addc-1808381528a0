//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTRESPONDPERIPHERALVERSION_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTRESPONDPERIPHERALVERSION_H

#include "G3AndMCUUartPacket.h"

class G3AndMCUUartRespondPeripheralVersion : public G3AndMCUUartPacket {
public:
    G3AndMCUUartRespondPeripheralVersion();

    int toCode(uint8_t *buf, int len) override;

    int decode(uint8_t *buf, int len) override;

    uint8_t getPeripheralId() const;

    void setPeripheralId(uint8_t peripheralId);

    const char *getSoftwareVersion() const;

    const char *getProtocolVersion() const;

    const char *getBranchNumber() const;

    std::size_t getSoftwareVersionLen() const;

    std::size_t getProtocolVersionLen() const;

    std::size_t getBranchNumberLen() const;

private:
    /* 外设编号 */
    uint8_t peripheralId = 0x00;
    /* 软件版本号 */
    char softwareVersion[4] = {0x00, 0x00, 0x00, 0x00};
    /* 通信协议版本号 */
    char protocolVersion[4] = {0x00, 0x00, 0x00, 0x00};
    /* 应用分支编号 */
    char branchNumber[4] = {0x00, 0x00, 0x00, 0x00};

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTRESPONDPERIPHERALVERSION_H
