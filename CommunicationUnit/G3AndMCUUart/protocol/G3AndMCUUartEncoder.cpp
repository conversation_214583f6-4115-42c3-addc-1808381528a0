//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/22.
//

#include "G3AndMCUUartEncoder.h"

uint16_t G3AndMCUUartEncoder::getFlowId() {
    flowId++;
    return flowId;
}

int G3AndMCUUartEncoder::generateDetectionInfo(uint8_t *buf, int len, G3AndMCUUartDetectionInfo &detectionInfo) {
    int ret = -1;
    detectionInfo.setFlowId(getFlowId());
    ret = detectionInfo.toCode(buf, len);
    return ret;
}

int
G3AndMCUUartEncoder::generateAlarmEvent(uint8_t *buf, int len, G3AndMCUUartAlarmInfo &detectionAlarmEvent) {
    int ret = -1;
    detectionAlarmEvent.setFlowId(getFlowId());
    ret = detectionAlarmEvent.toCode(buf, len);
    return ret;
}

int G3AndMCUUartEncoder::generateUniversalAnswer(uint8_t *buf, int len,
                                                 G3AndMCUUartUniversalAnswer &universalAnswer) {
    int ret = -1;
    ret = universalAnswer.toCode(buf, len);
    return ret;
}

int G3AndMCUUartEncoder::generateGetPeripheralVersion(uint8_t *buf, int len,
                                                      G3AndMCUUartGetPeripheralVersion &getPeripheralVersion) {
    int ret = -1;
    getPeripheralVersion.setFlowId(getFlowId());
    ret = getPeripheralVersion.toCode(buf, len);
    return ret;
}

int G3AndMCUUartEncoder::generateStartPeripheralUpgrade(uint8_t *buf, int len,
                                                        G3AndMCUUartStartPeripheralUpgrade &startPeripheralUpgrade) {
    int ret = -1;
    startPeripheralUpgrade.setFlowId(getFlowId());
    ret = startPeripheralUpgrade.toCode(buf, len);
    return ret;
}

int G3AndMCUUartEncoder::generateSendPeripheralUpgradeData(uint8_t *buf, int len,
                                                           G3AndMCUUartSendPeripheralUpgradeData &sendPeripheralUpgradeData) {
    int ret = -1;
    ret = sendPeripheralUpgradeData.toCode(buf, len);
    return ret;
}

int G3AndMCUUartEncoder::generateStartPTWrok(uint8_t *buf, int len, G3AndMCUUartStartPTWrok &startPtWrok) {
    int ret = -1;
    startPtWrok.setFlowId(getFlowId());
    ret = startPtWrok.toCode(buf, len);
    return ret;
}

int G3AndMCUUartEncoder::generateCANDataTransmissionSwitch(uint8_t *buf, int len,
                                                           G3AndMCUUartCANDataTransmissionSwitch &canDataTransmissionSwitch) {
    int ret = -1;
    canDataTransmissionSwitch.setFlowId(getFlowId());
    ret = canDataTransmissionSwitch.toCode(buf, len);
    return ret;
}


