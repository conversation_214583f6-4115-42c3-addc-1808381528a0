//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/18.
//

#ifndef VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_SYSSTATUS_H
#define VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_SYSSTATUS_H

#include <cstdint>
#include "CodeUtils.h"

class G3AndMCUUartDetectionInfo_SYSStatus {
public:
    /* 正常 */
    const static int WORKING = 0;
    /* 损坏 */
    const static int NOT_WORKING = 1;

    /* DSM检测不到人脸 */
    const static int DSM_NOT_FIND_FACE = 0;
    /* DSM检测人脸正常 */
    const static int DSM_FIND_FACE = 1;

    /* TF卡 */
    uint8_t tfCardStatus;
    /* 高温报警 */
    uint8_t highTemperature;
    /* ADAS摄像头 */
    uint8_t adasCameraStatus;
    /* 左侧摄像头 */
    uint8_t bsdLeftCameraStat<PERSON>;
    /* 右侧摄像头 */
    uint8_t bsdRightCameraStatus;
    /* DSM摄像头 */
    uint8_t dsmCameraStatus;
    /* ADAS算法 */
    uint8_t adasAlgorithmStatus;
    /* 左侧算法 */
    uint8_t bsdLeftAlgorithmStatus;
    /* 右侧算法 */
    uint8_t bsdRightAlgorithmStatus;
    /* DSM算法 */
    uint8_t dsmAlgorithmStatus;
    /* DSM检测结果 */
    uint8_t dsmFindFace;
    /* 镜头1是否遮挡  0：未遮挡  1：已遮挡 */
    uint8_t isCameraCover_1 = 0;
    /* 镜头2是否遮挡  0：未遮挡  1：已遮挡 */
    uint8_t isCameraCover_2 = 0;
    /* 镜头3是否遮挡  0：未遮挡  1：已遮挡 */
    uint8_t isCameraCover_3 = 0;
    /* 镜头4是否遮挡  0：未遮挡  1：已遮挡 */
    uint8_t isCameraCover_4 = 0;
    /* 镜头5是否遮挡  0：未遮挡  1：已遮挡 */
    uint8_t isCameraCover_5 = 0;
    /* 镜头6是否遮挡  0：未遮挡  1：已遮挡 */
    uint8_t isCameraCover_6 = 0;
    /* 镜头7是否遮挡  0：未遮挡  1：已遮挡 */
    uint8_t isCameraCover_7 = 0;
    /* 镜头8是否遮挡  0：未遮挡  1：已遮挡 */
    uint8_t isCameraCover_8 = 0;



    int toCode(uint8_t *buf, int len);

    uint8_t getTfCardStatus() const;

    void setTfCardStatus(uint8_t tfCardStatus);

    uint8_t getHighTemperature() const;

    void setHighTemperature(uint8_t highTemperature);

    uint8_t getAdasCameraStatus() const;

    void setAdasCameraStatus(uint8_t adasCameraStatus);

    uint8_t getBsdLeftCameraStatus() const;

    void setBsdLeftCameraStatus(uint8_t bsdLeftCameraStatus);

    uint8_t getBsdRightCameraStatus() const;

    void setBsdRightCameraStatus(uint8_t bsdRightCameraStatus);

    uint8_t getDsmCameraStatus() const;

    void setDsmCameraStatus(uint8_t dsmCameraStatus);

    uint8_t getAdasAlgorithmStatus() const;

    void setAdasAlgorithmStatus(uint8_t adasAlgorithmStatus);

    uint8_t getBsdLeftAlgorithmStatus() const;

    void setBsdLeftAlgorithmStatus(uint8_t bsdLeftAlgorithmStatus);

    uint8_t getBsdRightAlgorithmStatus() const;

    void setBsdRightAlgorithmStatus(uint8_t bsdRightAlgorithmStatus);

    uint8_t getDsmAlgorithmStatus() const;

    void setDsmAlgorithmStatus(uint8_t dsmAlgorithmStatus);

    uint8_t getDsmFindFace() const;

    void setDsmFindFace(uint8_t dsmFindFace);

    uint8_t getIsCameraCover1() const;

    void setIsCameraCover1(uint8_t isCameraCover1);

    uint8_t getIsCameraCover2() const;

    void setIsCameraCover2(uint8_t isCameraCover2);

    uint8_t getIsCameraCover3() const;

    void setIsCameraCover3(uint8_t isCameraCover3);

    uint8_t getIsCameraCover4() const;

    void setIsCameraCover4(uint8_t isCameraCover4);

    uint8_t getIsCameraCover5() const;

    void setIsCameraCover5(uint8_t isCameraCover5);

    uint8_t getIsCameraCover6() const;

    void setIsCameraCover6(uint8_t isCameraCover6);

    uint8_t getIsCameraCover7() const;

    void setIsCameraCover7(uint8_t isCameraCover7);

    uint8_t getIsCameraCover8() const;

    void setIsCameraCover8(uint8_t isCameraCover8);

private:
    uint16_t itemId = 0x0003;
    uint8_t itemContentLen = 4;

    uint32_t systemStatus = 0;

};


#endif //VIS_G3_SOFTWARE_G3ANDMCUUARTDETECTIONINFO_SYSSTATUS_H
