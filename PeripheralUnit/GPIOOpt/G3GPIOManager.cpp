//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/18.
//

#include "G3GPIOManager.h"
#include "G3_Configuration.h"
#include "XuShell.h"

namespace vis {

    void G3GPIOManager::init(PeripheraDataCallback &peripheraDataCallback) {
        callback = &peripheraDataCallback;
        /* 打开 控制RS485的电源的GPIO，并持续输出高电平 高电平12V输出，低电平12V不输出  */
        XuShell::getInstance().runShellWithTimeout("echo 103 > /sys/class/gpio/export && echo out > /sys/class/gpio/gpio103/direction && echo 1 > /sys/class/gpio/gpio103/value");

        /* 打开 控制相机电源是否输出的GPIO，并持续输出高电平 高电平为输出  */
        XuShell::getInstance().runShellWithTimeout("echo 104 > /sys/class/gpio/export && echo out > /sys/class/gpio/gpio104/direction && echo 1 > /sys/class/gpio/gpio104/value");

        /* 打开 开关量输出1 的GPIO，并持续输出高电平  高电平常开管脚闭合，常闭管脚打开，默认给出去的是常开管脚  这个对应OUT1 */
        XuShell::getInstance().runShellWithTimeout("echo 105 > /sys/class/gpio/export && echo out > /sys/class/gpio/gpio105/direction && echo 0 > /sys/class/gpio/gpio105/value");

        /* 打开 开关量输出2 的GPIO，并持续输出高电平  高电平常开管脚闭合，常闭管脚打开，默认给出去的是常开管脚  这个对应OUT2 */
        XuShell::getInstance().runShellWithTimeout("echo 108 > /sys/class/gpio/export && echo out > /sys/class/gpio/gpio108/direction && echo 0 > /sys/class/gpio/gpio108/value");

        /* 打开 Turn L 的GPIO  设置成输入模式  这个对应INT3 */
        XuShell::getInstance().runShellWithTimeout("echo 110 > /sys/class/gpio/export && echo in > /sys/class/gpio/gpio110/direction");

        /* 打开 Turn R 的GPIO  设置成输入模式  这个对应INT2 */
        XuShell::getInstance().runShellWithTimeout("echo 111 > /sys/class/gpio/export && echo in > /sys/class/gpio/gpio111/direction");

//        /* 打开 ACC 的GPIO  设置成输入模式  */
//        system("echo 113 > /sys/class/gpio/export && echo in > /sys/class/gpio/gpio113/direction");

        /* 打开 控制RS232的电源的GPIO，并持续输出高电平 高电平12V输出，低电平12V不输出  */
        XuShell::getInstance().runShellWithTimeout("echo 113 > /sys/class/gpio/export && echo out > /sys/class/gpio/gpio113/direction && echo 1 > /sys/class/gpio/gpio113/value");

        /* 打开 SPEED 的GPIO  设置成输入模式  这个对应INT1 */
        XuShell::getInstance().runShellWithTimeout("echo 115 > /sys/class/gpio/export && echo in > /sys/class/gpio/gpio115/direction");

        /* 把LED灯点亮 */
        XuShell::getInstance().runShellWithTimeout("echo 1 > /sys/devices/platform/leds/leds/system_led/brightness");

        /* 刷新一下震动口 */
        refreshG3VibratorInfo();


        /* 给485供电 */
        setOutlevel(103, 1);
        /* 给232供电 */
        setOutlevel(113, 1);

        /* 监听110(IN3)  111(IN2)  115(IN1) 113(未知)这四个输入 */
        addGPIOListener(IO_IN_1_NUM);
        addGPIOListener(IO_IN_2_NUM);
        addGPIOListener(IO_IN_3_NUM);
        addGPIOListener(IO_IN_UNKNOW_NUM);

        needStopListener = false;
    }

    int G3GPIOManager::setInMode(int portNum) {
        int ret = -1;
        std::string ioFilePaht = "/sys/class/gpio/gpio";
        ioFilePaht.append(std::to_string(portNum));
        ioFilePaht.append("/direction");
        FILE *ioFile = fopen(ioFilePaht.c_str(), "w");
        if (ioFile) {
            const char *inStr = "in";
            ret = fwrite(inStr, 2, 1, ioFile);
            if (ret > 0) {
                ret = 0;
            }
        }
        fflush(ioFile);
        /* 操作GPIO的，重要性和及时性不高，故不用fsync降低性能 */
        fclose(ioFile);

        return ret;
    }

    int G3GPIOManager::setOutMode(int portNum) {
        int ret = -1;
        std::string ioFilePaht = "/sys/class/gpio/gpio";
        ioFilePaht.append(std::to_string(portNum));
        ioFilePaht.append("/direction");
        FILE *ioFile = fopen(ioFilePaht.c_str(), "w");
        if (ioFile) {
            const char *outStr = "out";
            ret = fwrite(outStr, 3, 1, ioFile);
            if (ret > 0) {
                ret = 0;
            }
        }
        fflush(ioFile);
        /* 操作GPIO的，重要性和及时性不高，故不用fsync降低性能 */
        fclose(ioFile);
        return ret;
    }

    void G3GPIOManager::setOutlevel(int portNum, int level) {
        int ret = -1;
        std::string ioFilePaht = "/sys/class/gpio/gpio";
        ioFilePaht.append(std::to_string(portNum));
        ioFilePaht.append("/value");
        FILE *ioFile = fopen(ioFilePaht.c_str(), "w");
        if (ioFile) {
            ret = fwrite(std::to_string(level).c_str(), 1, 1, ioFile);
            if (ret > 0) {
                ret = 0;
            }
        }
        fflush(ioFile);
        /* 操作GPIO的，重要性和及时性不高，故不用fsync降低性能 */
        fclose(ioFile);
        /* 记录一下输出口的当前电平 */
        switch (portNum) {
            case IO_OUT_1_NUM:{
                output1_value = std::to_string(level);
            }
                break;
            case IO_OUT_2_NUM:{
                output1_value = std::to_string(level);
            }
                break;
        }
    }

    int G3GPIOManager::readInputLvl(int portNum) {
        int ret = -1;
        std::string ioFilePaht = "/sys/class/gpio/gpio";
        ioFilePaht.append(std::to_string(portNum));
        ioFilePaht.append("/value");
        FILE *ioFile = fopen(ioFilePaht.c_str(), "w");
        if (ioFile) {
            char inputLvl[5];
            ret = fread(inputLvl, sizeof(inputLvl), 1, ioFile);
            if (ret > 0) {
                ret = atoi(inputLvl);
            }
        }
        fclose(ioFile);
        return ret;
    }


    int G3GPIOManager::addGPIOListener(int portNum) {
        bool isIn = false;
        for (std::size_t i = 0; i < gpioListenerList.size(); i++) {
            if (gpioListenerList[i].io_num == portNum) {
                isIn = true;
                break;
            }
        }

        if (!isIn) {
            G3GPIO_FD_Info fdInfo;
            fdInfo.io_num = portNum;
            std::string gpioFilePath = "/sys/class/gpio/gpio";
            gpioFilePath.append(std::to_string(portNum));
            gpioFilePath.append("/value");
            fdInfo.fd = open(gpioFilePath.c_str(), O_RDONLY);

            if (fdInfo.fd >= 0) {
                fdInfo.fds->fd = fdInfo.fd;
                fdInfo.fds->events = POLLPRI;
                if (poll(fdInfo.fds, 1, 0) == 1) {
                    gpioListenerList.push_back(fdInfo);
                } else {
                    perror("======================poll failed!=============================\n");
                }

            } else {
                printf("========================open %s failed!\n", gpioFilePath.c_str());
            }
        }

        return 0;

//    int ret = -1;
//    GPIOListener listener;
//    ret = listener.init(portNum,*this);
//    if(ret == 0){
//        if( gpioListenerThreadPool.available() > 0 ){
//            gpioListenerThreadPool.start(listener);
//            gpioListenerList.push_back(listener);
//        }else{
//            printf("threadPool.available() <= 0");
//            ret = -1;
//        }
//    }
//    return ret;
    }

    int G3GPIOManager::removeGPIOListener(int portNum) {
        int ret = -1;
        for (std::vector<G3GPIO_FD_Info>::iterator iter = gpioListenerList.begin();
             iter != gpioListenerList.end(); iter++) {
            //从vector中删除指定的某一个元素
            if (iter->io_num == portNum) {
                close(iter->fd);
                gpioListenerList.erase(iter);
                ret = 0;
            }
        }
        return ret;
    }

    void G3GPIOManager::run() {
        std::string pthreadName = "GPIOManager";
        pthread_setname_np(pthread_self(), pthreadName.c_str());


        char buffer[16];
        while (!needStopListener) {
            /* 读一下IN的值 */
            for (std::size_t i = 0; i < gpioListenerList.size(); i++) {
                if (gpioListenerList[i].fds[0].revents & POLLPRI) {
                    if (lseek(gpioListenerList[i].fd, 0, SEEK_SET) == -1) {
                        perror("=====================lseek failed!=========================\n");
                    } else {
                        int len;
                        if ((len = read(gpioListenerList[i].fd, buffer, sizeof(buffer))) == -1) {
                            perror("======================read failed!======================\n");
                        } else {
                            buffer[len] = 0;
//                        printf("======================%s======================\n", buffer);
                            callback->onGPIOValueChange(gpioListenerList[i].io_num, buffer, len);
                        }
                    }
                } else {
                    printf("======================fd=%d revents=%d  events=%d=============================\n",
                           gpioListenerList[i].fds[0].fd, gpioListenerList[i].fds[0].revents,
                           gpioListenerList[i].fds[0].events);
                }

            }


            /* 设置一下OUT的值 厂测模式特殊处理*/
            if (isPTM) {
                uint64_t currentTimeMillis = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                if (currentTimeMillis - lastOutTime_1 >= 1000) {
                    setOutlevel(IO_OUT_1_NUM, (lastOutHeight_1 ? 1 : 0));
                    lastOutHeight_1 = !lastOutHeight_1;
                    lastOutTime_1 = currentTimeMillis;
                }
                if (currentTimeMillis - lastOutTime_2 >= 1000) {
                    setOutlevel(IO_OUT_2_NUM, (lastOutHeight_2 ? 1 : 0));
                    lastOutHeight_2 = !lastOutHeight_2;
                    lastOutTime_2 = currentTimeMillis;
                }

            } else {
                vibratorIOListLock.lock();
                for (std::size_t i = 0; i < outputModeInfoList.size(); i++) {
                    uint64_t currentTimeMillis = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                    /* 判断输出实际是否够了  够了就不输出 */
                    if (currentTimeMillis - outputModeInfoList[i].startTime >= outputModeInfoList[i].keepTime) {
                        setOutlevel(outputModeInfoList[i].ioNum, (outputModeInfoList[i].onStatus ? 1 : 0));
                    } else {
                        setOutlevel(outputModeInfoList[i].ioNum, (outputModeInfoList[i].onStatus ? 0 : 1));
                    }
                }
                vibratorIOListLock.unlock();
            }
            /* 记录一下输出口1的电平 */
            callback->onGPIOValueChange(IO_OUT_1_NUM, output1_value.c_str(), 2);
            /* 记录一下输出口2的电平 */
            callback->onGPIOValueChange(IO_OUT_2_NUM, output2_value.c_str(), 2);
            usleep(100 * 1000);
        }
        pthread_setname_np(pthread_self(), "Finish");
    }

    void G3GPIOManager::refreshG3VibratorInfo() {
        outputModeInfoList.clear();
        /* 判断下IO口1是否有设置过输出类型 */
//    if(G3_Configuration::getInstance().getGpioOut1OuputType() != IO_TYPE_OUT_UNKNOW){
        IO_OUTPUT_MODE_Info vibratorInfoTemp_1;
        vibratorInfoTemp_1.ioNum = IO_OUT_1_NUM;
        vibratorInfoTemp_1.startTime = 0;
        /* 如果是相反输出的话  需要把输出状态改成false 然后持续跟间隔时间都改成500ms */
        if (G3_Configuration::getInstance().getGpioOut1OuputType() == IO_TYPE_OUT_CONTRARY) {
            vibratorInfoTemp_1.onStatus = true;
            vibratorInfoTemp_1.keepTime = 500;
            vibratorInfoTemp_1.interval = 500;
        } else {
            vibratorInfoTemp_1.onStatus = false;
            vibratorInfoTemp_1.keepTime = VIBRATED_KEEP_TIME;
            vibratorInfoTemp_1.interval = 0;
        };
        outputModeInfoList.push_back(vibratorInfoTemp_1);
//    }

//    if(G3_Configuration::getInstance().getGpioOut2OuputType() != IO_TYPE_OUT_UNKNOW){
        IO_OUTPUT_MODE_Info vibratorInfoTemp_2;
        vibratorInfoTemp_2.ioNum = IO_OUT_2_NUM;
        vibratorInfoTemp_2.startTime = 0;
        /* 如果是相反输出的话  需要把输出状态改成false 然后持续跟间隔时间都改成500ms */
        if (G3_Configuration::getInstance().getGpioOut2OuputType() == IO_TYPE_OUT_CONTRARY) {
            vibratorInfoTemp_2.onStatus = true;
            vibratorInfoTemp_2.keepTime = 500;
            vibratorInfoTemp_2.interval = 500;
        } else {
            vibratorInfoTemp_2.onStatus = false;
            vibratorInfoTemp_2.keepTime = VIBRATED_KEEP_TIME;
            vibratorInfoTemp_2.interval = 0;
        };

        outputModeInfoList.push_back(vibratorInfoTemp_2);
//    }
        vibratorIOListLock.unlock();
    }

    void G3GPIOManager::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
//        printf("***********G3GPIOManager::setDetectionEvent:      eventCode=%d  OutPut1AlarmType=%d   GpioOut1OuputType=%d \n",
//               eventCode, G3_Configuration::getInstance().getOutPut1AlarmType(),
//               G3_Configuration::getInstance().getGpioOut1OuputType());
        if (!needIgnoreAlarmOutput) {
            /* 先拿给IO口1判断一下 */
            startOutput(IO_OUT_1_NUM,curCameraType,eventCode,alarmEventInfo);
            /* 再拿给IO口2判断一下 */
            startOutput(IO_OUT_2_NUM,curCameraType,eventCode,alarmEventInfo);
        }


    }

    bool G3GPIOManager::isPtm() const {
        return isPTM;
    }

    void G3GPIOManager::setIsPtm(bool isPtm) {
        isPTM = isPtm;
    }

    void G3GPIOManager::setStopAlarmButtonStatus(const bool isButtonOn) {
        /* 被按下了就表示需要停止报警时候的IO输出 */
        needIgnoreAlarmOutput= isButtonOn;
    }

    void G3GPIOManager::startOutput(int ioNum,CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
        /* 找出IO口的封装信息在哪里 */
        int outoutInfoIndex = -1;
        for (std::size_t i = 0; i < outputModeInfoList.size(); i++) {
            if (outputModeInfoList[i].ioNum == ioNum){
                outoutInfoIndex = i;
            }
        }
        /* 如果找到了，才可以进行下一步 */
        if(outoutInfoIndex >= 0){
            /* 判断下这个报警是否来自输出口的关联相机 */
            bool isCameraEnable = false;
            IO_OUT_Camera_Enable cameraEnableInfo = (ioNum == IO_OUT_1_NUM) ? G3_Configuration::getInstance().getOutPut1CameraEnable() : G3_Configuration::getInstance().getOutPut2CameraEnable();
            switch (curCameraType.cameraId) {
                case CAMERA_ID_1:{
                    isCameraEnable = cameraEnableInfo.camerd_1;
                }
                    break;

                case CAMERA_ID_2:{
                    isCameraEnable = cameraEnableInfo.camerd_2;
                }
                    break;
                case CAMERA_ID_3:{
                    isCameraEnable = cameraEnableInfo.camerd_3;
                }
                    break;

                case CAMERA_ID_4:{
                    isCameraEnable = cameraEnableInfo.camerd_4;
                }
                    break;

            }

            /* 如果是来自关联相机，那么就进行类型判断 */
            if(isCameraEnable){
                /* 判断一下报警事件是否是关联的报警事件 */
                IO_OUT_Alarm_Type alarmType = (ioNum == IO_OUT_1_NUM) ? G3_Configuration::getInstance().getOutPut1AlarmType() : G3_Configuration::getInstance().getOutPut2AlarmType();
                /* 是否需要输出 */
                bool needOutput = false;
                /* 上次输出的这个报警的ID，只要在区域人和区域车的报警的时候才会不为空 */
                uint32_t *lastAlarmId = nullptr;
                /* 本次报警的行人或者车辆ID */
                uint32_t curAlarmId = 99999999;
                /* ADAS报警是否开始,只有在ADAS报警的时候才有可能为false */
                bool isADASAlarmStart = true;


                switch (eventCode) {
                    case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
                        needOutput = alarmType.bsd_pedestrian_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastBSDPedestrianLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
                        needOutput = alarmType.bsd_pedestrian_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastBSDPedestrianLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_FORWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_BACKWARD_VEHICLE_LEVEL1: {
                        needOutput = alarmType.bsd_vehicle_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastBSDVehicleLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.bsdAlarmInfo.alarmVehicleId;
                    }
                        break;

                    case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_FORWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_BACKWARD_VEHICLE_LEVEL2: {
                        needOutput = alarmType.bsd_vehicle_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastBSDVehicleLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.bsdAlarmInfo.alarmVehicleId;

                    }
                        break;

                    case EVENT_DSM: {
                        needOutput = alarmType.dsm;
                    }
                        break;

                    case EVENT_SBST_FORWARD_PDW_LEVEL1: {
                        needOutput = alarmType.sbst_forward_pedestrian_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastBSDPedestrianLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.sbstForwardAlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_SBST_FORWARD_PDW_LEVEL2: {
                        needOutput = alarmType.sbst_forward_pedestrian_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastBSDPedestrianLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.sbstForwardAlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_SBST_FORWARD_FCW_LEVEL1: {
                        needOutput = alarmType.sbst_forward_vehicle_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastBSDVehicleLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.sbstForwardAlarmInfo.alarmVehicleId;
                    }
                        break;

                    case EVENT_SBST_FORWARD_FCW_LEVEL2: {
                        needOutput = alarmType.sbst_forward_vehicle_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastBSDVehicleLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.sbstForwardAlarmInfo.alarmVehicleId;
                    }
                        break;

                    case EVENT_SBST_FORWARD_LDW_LEFT_DASHED: {
                        //TODO 车道线只响一次要靠报警只发一次
                        needOutput = alarmType.sbst_forward_ldw_left_dashed;
                    }
                        break;

                    case EVENT_SBST_FORWARD_LDW_LEFT_SOLID: {
                        //TODO 车道线只响一次要靠报警只发一次
                        needOutput = alarmType.sbst_forward_ldw_left_solid;
                    }
                        break;

                    case EVENT_SBST_FORWARD_LDW_RIGHT_DASHED: {
                        //TODO 车道线只响一次要靠报警只发一次
                        needOutput = alarmType.sbst_forward_ldw_right_dashed;
                    }
                        break;

                    case EVENT_SBST_FORWARD_LDW_RIGHT_SOLID: {
                        //TODO 车道线只响一次要靠报警只发一次
                        needOutput = alarmType.sbst_forward_ldw_right_solid;
                    }
                        break;

                    case EVENT_ADAS_VEHICLE_TTC: {
                        needOutput = alarmType.adas_ttc;
                        /* ADAS的报警状态为开始或者不可用时，都算这个报警是刚开始 */
                        isADASAlarmStart = (alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START || alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_UNAVAILABLE);
                    }
                        break;

                    case EVENT_ADAS_VEHICLE_HMW: {
                        needOutput = alarmType.adas_hmw;
                        /* ADAS的报警状态为开始或者不可用时，都算这个报警是刚开始 */
                        isADASAlarmStart = (alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START || alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_UNAVAILABLE);
                    }
                        break;

                    case EVENT_ADAS_PEDESTRIAN_HMW: {
                        needOutput = alarmType.adas_pdw;
                        /* ADAS的报警状态为开始或者不可用时，都算这个报警是刚开始 */
                        isADASAlarmStart = (alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START || alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_UNAVAILABLE);
                    }
                        break;

                    case EVENT_ADAS_LDW_LEFT_SOLID: {
                        needOutput = alarmType.adas_ldw_left_solid;
                        /* ADAS的报警状态为开始或者不可用时，都算这个报警是刚开始 */
                        isADASAlarmStart = (alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START || alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_UNAVAILABLE);
                    }
                        break;

                    case EVENT_ADAS_LDW_LEFT_DASH: {
                        needOutput = alarmType.adas_ldw_left_dashed;
                        /* ADAS的报警状态为开始或者不可用时，都算这个报警是刚开始 */
                        isADASAlarmStart = (alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START || alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_UNAVAILABLE);
                    }
                        break;

                    case EVENT_ADAS_LDW_RIGHT_SOLID: {
                        needOutput = alarmType.adas_ldw_right_solid;
                        /* ADAS的报警状态为开始或者不可用时，都算这个报警是刚开始 */
                        isADASAlarmStart = (alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START || alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_UNAVAILABLE);
                    }
                        break;

                    case EVENT_ADAS_LDW_RIGHT_DASH: {
                        needOutput = alarmType.adas_ldw_right_dashed;
                        /* ADAS的报警状态为开始或者不可用时，都算这个报警是刚开始 */
                        isADASAlarmStart = (alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START || alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_UNAVAILABLE);
                    }
                        break;

                    case EVENT_BSD_R151_AREA1_PEDESTRIAN: {
                        needOutput = alarmType.r151_pedestrian_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR151PedestrianLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r151AlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_R151_AREA2_PEDESTRIAN: {
                        needOutput = alarmType.r151_pedestrian_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR151PedestrianLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r151AlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_R151_AREA3_PEDESTRIAN: {
                        needOutput = alarmType.r151_pedestrian_level_3;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR151PedestrianLevel3AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r151AlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_R151_AREA4_PEDESTRIAN: {
                        needOutput = alarmType.r151_pedestrian_level_4;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR151PedestrianLevel4AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r151AlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_R151_AREA1_VEHICLE: {
                        needOutput = alarmType.r151_vehicle_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR151VehicleLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r151AlarmInfo.alarmVehicleId;
                    }
                        break;

                    case EVENT_BSD_R151_AREA2_VEHICLE: {
                        needOutput = alarmType.r151_vehicle_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR151VehicleLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r151AlarmInfo.alarmVehicleId;
                    }
                        break;

                    case EVENT_BSD_R151_CAMERA_COVER: {
                        needOutput = alarmType.r151_camera_cover;
                    }
                        break;

                    case EVENT_BSD_R158_AREA1_PEDESTRIAN: {
                        needOutput = alarmType.r158_pedestrian_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR158PedestrianLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r158AlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_R158_AREA2_PEDESTRIAN: {
                        needOutput = alarmType.r158_pedestrian_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR158PedestrianLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r158AlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_R158_AREA3_PEDESTRIAN: {
                        needOutput = alarmType.r158_pedestrian_level_3;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR158PedestrianLevel3AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r158AlarmInfo.alarmPedestrianId;
                    }
                        break;


                    case EVENT_BSD_R158_AREA1_VEHICLE: {
                        needOutput = alarmType.r158_vehicle_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR158VehicleLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r158AlarmInfo.alarmVehicleId;
                    }
                        break;

                    case EVENT_BSD_R158_AREA2_VEHICLE: {
                        needOutput = alarmType.r158_vehicle_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR158VehicleLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r158AlarmInfo.alarmVehicleId;
                    }
                        break;

                    case EVENT_BSD_R158_CAMERA_COVER: {
                        needOutput = alarmType.r158_camera_cover;
                    }
                        break;

                    case EVENT_BSD_R159_AREA1_PEDESTRIAN: {
                        needOutput = alarmType.r159_pedestrian_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR159PedestrianLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r159AlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_R159_AREA2_PEDESTRIAN: {
                        needOutput = alarmType.r159_pedestrian_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR159PedestrianLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r159AlarmInfo.alarmPedestrianId;
                    }
                        break;

                    case EVENT_BSD_R159_AREA3_PEDESTRIAN: {
                        needOutput = alarmType.r159_pedestrian_level_3;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR159PedestrianLevel3AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r159AlarmInfo.alarmPedestrianId;
                    }
                        break;


                    case EVENT_BSD_R159_AREA1_VEHICLE: {
                        needOutput = alarmType.r159_vehicle_level_1;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR159VehicleLevel1AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r159AlarmInfo.alarmVehicleId;
                    }
                        break;

                    case EVENT_BSD_R159_AREA2_VEHICLE: {
                        needOutput = alarmType.r159_vehicle_level_2;
                        /* 把这个报警对应的上次报警的ID拿出来,给同ID只报一次的模式用 */
                        lastAlarmId = &lastR159VehicleLevel2AlarmId;
                        /* 把这个报警的ID拿出来,给同ID只报一次的模式用 */
                        curAlarmId = alarmEventInfo.r159AlarmInfo.alarmVehicleId;
                    }
                        break;

                    case EVENT_BSD_R159_CAMERA_COVER: {
                        needOutput = alarmType.r159_camera_cover;
                    }
                        break;


                    case EVENT_ADAS_VEHICLE_VB: {
                        needOutput = alarmType.adas_vb;
                        /* ADAS的报警状态为开始或者不可用时，都算这个报警是刚开始 */
                        isADASAlarmStart = (alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START || alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_UNAVAILABLE);
                    }
                        break;
                    case EVENT_ADAS_VEHICLE_GO: {
                        needOutput = alarmType.adas_go;
                        /* ADAS的报警状态为开始或者不可用时，都算这个报警是刚开始 */
                        isADASAlarmStart = (alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_START || alarmEventInfo.adasAlarmInfo.eventStatus == EVENTSTATUS_UNAVAILABLE);
                    }
                        break;
                }

                /* 如果是关联的报警类型，那么看看怎么输出 */
                if(needOutput){
                    /* 先获取输出模式 */
                    switch ((ioNum == IO_OUT_1_NUM) ? G3_Configuration::getInstance().getGpioOut1OuputType() : G3_Configuration::getInstance().getGpioOut2OuputType()) {
                        case IO_TYPE_OUT_ONLY_ONE: {
                            /* 只输出一次 */

                            /*要先看看是不是ADAS的报警状态，如果是ADAS报警，那么状态会改变，如果不算ADAS报警，状态就一直是true，可以直接进去 */
                            if(isADASAlarmStart){
                                /* 最终是否可以输出 */
                                bool shouldOutput = false;
                                /* 先看看是不是BSD报警，通过存ID的那个指针可以判断是不是BSD报警，如果指针不为空，那么就是BSD报警 */
                                if(lastAlarmId != nullptr){
                                    /* 是BSD报警，需要判断一下是否同ID，不同ID才可以报警 */
                                    if((*lastAlarmId) != curAlarmId){
                                        /* 设置可以报警 */
                                        shouldOutput = true;
                                        /* 把当前的ID存起来 */
                                        *lastAlarmId = curAlarmId;
                                    }else{
                                        ; //是相同ID，直接不输出
                                    }
                                }else {
                                    /* 不是BSD报警，直接输出就行了，因为DSM是直接输出，ADAS已经通过前面判断完了是否开始 */
                                    shouldOutput = true;
                                }
                                /* 一系列条件判断完了，如果可以输出就输出 */
                                if(shouldOutput){
                                    vibratorIOListLock.lock();
                                    outputModeInfoList[outoutInfoIndex].onStatus = false;
                                    outputModeInfoList[outoutInfoIndex].keepTime = VIBRATED_KEEP_TIME;
                                    outputModeInfoList[outoutInfoIndex].interval = 0;
                                    outputModeInfoList[outoutInfoIndex].startTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                                    vibratorIOListLock.unlock();
                                 }else{
                                    ; //最终判断完了，不可以输出，直接不管了
                                }
                            }else{
                                ; //这个ADAS报警不是开始，所以直接不管
                            }

                        }
                            break;
                        
                        case IO_TYPE_OUT_CONTINUOUS: {
                            /* 持续输出模式 */

                            /* 先判断一下是不是ADAS，采用反证法，如果不是BSD也不是DSM ，那么就只能是ADAS了 */
                            if(lastAlarmId == nullptr && eventCode != EVENT_DSM){
                                /* 是ADAS报警，那么需要在开始报警的时候输出60秒，结束报警的时候停止输出 */
                                if(isADASAlarmStart){
                                    /* 是开始报警，那么就输出100秒，中间可以被结束报警打断 */
                                    vibratorIOListLock.lock();
                                    outputModeInfoList[outoutInfoIndex].onStatus = false;
                                    outputModeInfoList[outoutInfoIndex].keepTime = 100 * 1000;
                                    outputModeInfoList[outoutInfoIndex].interval = 0;
                                    outputModeInfoList[outoutInfoIndex].startTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                                    vibratorIOListLock.unlock();


                                }else{
                                    /* 是停止报警，那么就停止输出 */
                                    vibratorIOListLock.lock();
                                    outputModeInfoList[outoutInfoIndex].onStatus = false;
                                    outputModeInfoList[outoutInfoIndex].keepTime = 0;
                                    outputModeInfoList[outoutInfoIndex].interval = 0;
                                    outputModeInfoList[outoutInfoIndex].startTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                                    vibratorIOListLock.unlock();
                                }

                            }else{
                                /* 不是ADAS报警，直接输出 */
                                vibratorIOListLock.lock();
                                outputModeInfoList[outoutInfoIndex].onStatus = false;
                                outputModeInfoList[outoutInfoIndex].keepTime = VIBRATED_KEEP_TIME;
                                outputModeInfoList[outoutInfoIndex].interval = 0;
                                outputModeInfoList[outoutInfoIndex].startTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                                vibratorIOListLock.unlock();
                            }


                        }
                            break;


                        case IO_TYPE_OUT_CONTRARY: {
                            /* 是相反输出模式（持续500 然后断开500）*/

                            /* 相反输出现在只给BSD用，所以需要判断是否是BSD */
                            if(lastAlarmId != nullptr){
                                if (XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                    outputModeInfoList[outoutInfoIndex].startTime >=
                                    (outputModeInfoList[outoutInfoIndex].keepTime + outputModeInfoList[outoutInfoIndex].interval)) {
                                    vibratorIOListLock.lock();
                                    outputModeInfoList[outoutInfoIndex].onStatus = true;
                                    outputModeInfoList[outoutInfoIndex].keepTime = 500;
                                    outputModeInfoList[outoutInfoIndex].interval = 500;
                                    outputModeInfoList[outoutInfoIndex].startTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                                    vibratorIOListLock.unlock();
                                } else {
                                    ; // not to do
                                }
                            }else{
                                ; //不是BSD报警，直接丢掉
                            }
                        }
                            break;

                    }




                }else{
                    ; //不是关联的报警类型，直接丢掉
                }
            }else{
                ; //不是关联的相机，直接丢掉
            }

        }else{
            ; //没有找到对应的输出口封装信息
        }
    }


}





