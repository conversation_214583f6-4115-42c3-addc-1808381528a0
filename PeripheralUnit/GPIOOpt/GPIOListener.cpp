//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/21.
//

#include <fcntl.h>
#include <poll.h>
#include <unistd.h>
#include <cstring>
#include "GPIOListener.h"

namespace vis {

    int GPIOListener::init(int portNum, PeripheraDataCallback &peripheraDataCallback) {
        int ret = -1;
        std::string gpioFilePath = "/sys/class/gpio/gpio";
        gpioFilePath.append(std::to_string(portNum));
        gpioFilePath.append("/value");
        fd = open(gpioFilePath.c_str(), O_RDONLY);
        if (fd >= 0) {
            fds[0].fd = fd;
            fds[0].events = POLLPRI;
            curGPIONum = portNum;
            callback = &peripheraDataCallback;
            needStopListener = false;
            ret = 0;
        } else {
            printf("open %s failed!\n", gpioFilePath.c_str());
        }
        return ret;

    }

    void GPIOListener::run() {
        std::string pthreadName = "GPIOListener";
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        char buffer[16];
        memset(buffer, 0x00, sizeof(buffer));
        while (!needStopListener) {
            if (poll(fds, 1, 0) == -1) {
                perror("======================poll failed!=============================\n");
            } else {
                if (fds[0].revents & POLLPRI) {
                    if (lseek(fd, 0, SEEK_SET) == -1) {
                        perror("=====================lseek failed!=========================\n");

                    } else {
                        int len;
                        if ((len = read(fd, buffer, sizeof(buffer))) == -1) {
                            perror("======================read failed!======================\n");
                        } else {
                            buffer[len] = 0;
//                        printf("======================%s======================\n", buffer);
                            callback->onGPIOValueChange(curGPIONum, buffer, len);
                        }
                    }
                }

            }
            usleep(100 * 1000);
        }
        pthread_setname_np(pthread_self(), "Finish");
    }

    int GPIOListener::getCurGpioNum() const {
        return curGPIONum;
    }

}