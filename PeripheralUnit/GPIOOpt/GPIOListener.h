//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/21.
//

#ifndef VIS_G3_SOFTWARE_GPIOLISTENER_H
#define VIS_G3_SOFTWARE_GPIOLISTENER_H

#include <Poco/Runnable.h>
#include "PeripheraDataCallback.h"

namespace vis {

    class GPIOListener : public Poco::Runnable {
    public:
        int init(int portNum, PeripheraDataCallback &peripheraDataCallback);

        void run() override;

        int getCurGpioNum() const;


    private:
        bool needStopListener = true;
        /* 当前监听的GPIO号 */
        int curGPIONum = -1;
        /* GPIO对应的句柄 */
        int fd = -1;
        struct pollfd fds[1];
        /*  */
        PeripheraDataCallback *callback;


    };

}
#endif //VIS_G3_SOFTWARE_GPIOLISTENER_H
