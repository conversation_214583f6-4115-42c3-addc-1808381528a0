//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/18.
//

#ifndef VIS_G3_SOFTWARE_G3GPIOMANAGER_H
#define VIS_G3_SOFTWARE_G3GPIOMANAGER_H


#include <stdio.h>
#include <fcntl.h>
#include <poll.h>
#include <unistd.h>
#include <cstdlib>
#include <Poco/ThreadPool.h>
#include <string>
#include "GPIOListener.h"
#include <mutex>
#include "PeripheraDataCallback.h"
#include "XuTimeUtil.h"
#include "G3DetectionDefind.h"

namespace vis {

    struct G3GPIO_FD_Info {
        int io_num = -1;
        int fd = -1;
        struct pollfd fds[1];
    };

    class G3GPIOManager : public Poco::Runnable {

    public:
        /* 震动输出保持多长时间  单位s */
        const int VIBRATED_KEEP_TIME = 3 * 1000;

        G3GPIOManager() {};

        ~G3GPIOManager() {};


        void run() override;

        /**
         * 初始化GPIO
         *
         * */
        void init(PeripheraDataCallback &peripheraDataCallback);

        /**
         * 设置某个GPIO为输入模式
         *
         * @param portNum: GPIO号
         *
         * @return 结果    0：成功  其他：失败
         *
         * */
        int setInMode(int portNum);


        /**
         * 设置某个GPIO为输出模式
         *
         * @param portNum: GPIO号
         *
         * @return 结果    0：成功  其他：失败
         *
         * */
        int setOutMode(int portNum);


        /**
         * 设置GPIO的输出电平
         *
         * @param portNum: GPIO号
         * @param level: 电平  0：低电平   1：高电平
         *
         * */
        void setOutlevel(int portNum, int level);

        /**
         * 读取GPIO的输入电平
         *
         * @param portNum: GPIO号
         *
         * @return 电平 0：低电平   1：高电平
         *
         * */
        int readInputLvl(int portNum);

        /**
         * 添加一个GPIO状态变化的监听器
         *
         * @param portNum: GPIO号
         *
         * @return 结果  0：成功   其他：失败
         *
         * */
        int addGPIOListener(int portNum);

        /**
         * 移除一个GPIO状态变化的监听器
         *
         * @param portNum: GPIO号
         *
         * @return 结果  0：成功   其他：失败
         *
         * */
        int removeGPIOListener(int portNum);

        /**
         * 刷新一下震动口的设置，可能被新增或者删除
         * */
        void refreshG3VibratorInfo();


        /**
         * 设置从识别模块拿到的报警事件
         *
         * @param cameraId : 相机ID
         * @param detectType : 识别算法类型
         * @param eventCode ： 事件码
         *
         * */
        void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);


        /**
         * 设置停止报警按钮的状态
         *
         * @param isButtonOn ： 停止报警按钮是否被按下
         */
        void setStopAlarmButtonStatus(const bool isButtonOn);


        bool isPtm() const;

        void setIsPtm(bool isPtm);

    private:
        struct IO_OUTPUT_MODE_Info {
            /* 输出对应的IO口 */
            int ioNum;
            /* 开始输出的时间 */
            uint64_t startTime;
            /* 输出要保持输出多长时间 ms*/
            uint64_t keepTime;
            /* 输出开还是输出关  false：关   true：开 */
            bool onStatus;
            /* 间隔时间 ms*/
            uint64_t interval;
        };




        /* 输出口1当前的电平 */
        std::string output1_value = "0";
        /* 输出口2当前的电平 */
        std::string output2_value = "0";


        /* 简单GPIO状态的列表 */
        std::vector<G3GPIO_FD_Info> gpioListenerList;


        /* 震动的IO信息的存放队列 */
        std::vector<IO_OUTPUT_MODE_Info> outputModeInfoList;

        std::mutex vibratorIOListLock;

        PeripheraDataCallback *callback;

        bool needStopListener = true;


        /* 上一次BSD区域1行人报警的行人的ID（目标检测跟语义分割先共用） */
        uint32_t lastBSDPedestrianLevel1AlarmId = -1;
        /* 上一次BSD区域2行人报警的行人的ID（目标检测跟语义分割先共用） */
        uint32_t lastBSDPedestrianLevel2AlarmId = -1;
        /* 上一次BSD区域1车辆报警的车辆的ID（目标检测跟语义分割先共用） */
        uint32_t lastBSDVehicleLevel1AlarmId = -1;
        /* 上一次BSD区域2车辆报警的车辆的ID（目标检测跟语义分割先共用） */
        uint32_t lastBSDVehicleLevel2AlarmId = -1;


        /* 上一次R151区域1行人报警的行人的ID */
        uint32_t lastR151PedestrianLevel1AlarmId = -1;
        /* 上一次R151区域2行人报警的行人的ID */
        uint32_t lastR151PedestrianLevel2AlarmId = -1;
        /* 上一次R151区域3行人报警的行人的ID */
        uint32_t lastR151PedestrianLevel3AlarmId = -1;
        /* 上一次R151区域4行人报警的行人的ID */
        uint32_t lastR151PedestrianLevel4AlarmId = -1;
        /* 上一次R151区域1车辆报警的车辆的ID */
        uint32_t lastR151VehicleLevel1AlarmId = -1;
        /* 上一次R151区域2车辆报警的车辆的ID */
        uint32_t lastR151VehicleLevel2AlarmId = -1;


        /* 上一次R158区域1行人报警的行人的ID */
        uint32_t lastR158PedestrianLevel1AlarmId = -1;
        /* 上一次R158区域2行人报警的行人的ID */
        uint32_t lastR158PedestrianLevel2AlarmId = -1;
        /* 上一次R158区域3行人报警的行人的ID */
        uint32_t lastR158PedestrianLevel3AlarmId = -1;
        /* 上一次R158区域1车辆报警的车辆的ID */
        uint32_t lastR158VehicleLevel1AlarmId = -1;
        /* 上一次R158区域2车辆报警的车辆的ID */
        uint32_t lastR158VehicleLevel2AlarmId = -1;


        /* 上一次R159区域1行人报警的行人的ID */
        uint32_t lastR159PedestrianLevel1AlarmId = -1;
        /* 上一次R159区域2行人报警的行人的ID */
        uint32_t lastR159PedestrianLevel2AlarmId = -1;
        /* 上一次R159区域3行人报警的行人的ID */
        uint32_t lastR159PedestrianLevel3AlarmId = -1;
        /* 上一次R159区域1车辆报警的车辆的ID */
        uint32_t lastR159VehicleLevel1AlarmId = -1;
        /* 上一次R159区域2车辆报警的车辆的ID */
        uint32_t lastR159VehicleLevel2AlarmId = -1;














        /* 是否在厂测模式 */
        bool isPTM = false;
        /* 上一次是否输出高电平 */
        bool lastOutHeight_1 = false;
        bool lastOutHeight_2 = true;
        /* 上一次修改输出的时间 */
        uint64_t lastOutTime_1 = 0;
        uint64_t lastOutTime_2 = 0;

        /* 是否忽略掉报警声音  如果是的话，那么有报警来的时候，直接不输出 */
        bool needIgnoreAlarmOutput = false;


        void startOutput(int ioNum,CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);




    };

}
#endif //VIS_G3_SOFTWARE_G3GPIOMANAGER_H
