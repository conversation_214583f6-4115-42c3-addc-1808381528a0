//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/8.
//

#include <cstdio>
#include "PeripheraDataCallback.h"
namespace vis {

    void PeripheraDataCallback::onGetDataFromSocket(const char *ip, const int port, const uint8_t *buf, int len) {
        printf("PeripheraDataCallback::onGetDataFromSocket  \n");

    }

    void PeripheraDataCallback::onGetDataFromRS485(const uint8_t *buf, int len) {
        printf("PeripheraDataCallback::onGetDataFromRS485  \n");
    }

    void PeripheraDataCallback::onGetDataFromRS232(const uint8_t *buf, int len) {
        printf("PeripheraDataCallback::onGetDataFromRS232  \n");
    }

    void PeripheraDataCallback::onGetDataFromCANBus(const can_frame &canFrame) {
        printf("PeripheraDataCallback::onGetDataFromCANBus  \n");
    }

    void PeripheraDataCallback::onGPIOValueChange(const int portNum, const char *value, const int valueLen) {
        printf("PeripheraDataCallback::onGPIOValueChange  \n");
    }

    void PeripheraDataCallback::onGetDataFromUART_MCU(const uint8_t *buf, int len) {
        printf("PeripheraDataCallback::onGetDataFromUART_MCU  \n");
    }

    void PeripheraDataCallback::onGetGSensorData(float value_x, float value_y, float value_z) {
        printf("PeripheraDataCallback::onGetGSensorData  \n");
    }

    void PeripheraDataCallback::onGetADCVoltage(float voltage) {
        printf("PeripheraDataCallback::onGetADCVoltage  \n");
    }

    void PeripheraDataCallback::onGetDataFromMQTT(const int mqttType, const char *topicName, const int topicNameLen,
                                                  const char *payload, const int payloadLen) {
        printf("PeripheraDataCallback::onGetDataFromMQTT  \n");
    }

    void PeripheraDataCallback::onGetMQTTConnectStatusChange(const bool isConnected) {
        printf("PeripheraDataCallback::onGetMQTTConnectStatusChange  \n");
    }

}