//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/8.
//



#include <arpa/inet.h>
#include "H264UDPSocket.h"
#include "G3_Configuration.h"
#include "XuString.h"
#include "XuLog.h"
#include "CodeUtils.h"

H264UDPSocket::H264UDPSocket() {


}

H264UDPSocket::~H264UDPSocket() {

}

int H264UDPSocket::init(int port, int cameraId) {
    int ret = -1;
    curPort = port;
    curCameraId = cameraId;
    /*创建服务器端套接字--IPv4协议，面向无连接通信，UDP协议*/
    sockfd = socket(PF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (sockfd >= 0) {
        const int sock_flag = 1;
        setsockopt(sockfd, SOL_SOCKET, SO_BROADCAST, &sock_flag, sizeof(sock_flag));
        memset(&servaddr, 0, sizeof(servaddr)); //数据初始化--清零
        servaddr.sin_family = AF_INET; //设置为IP通信
        /* 如果是在G4-S的工作模式下，那么广播地址需要变化 */
        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_G4_UNIT){
            /* 先把IP分段 */
            std::vector<std::string> ipStrUnitList = XuString::getInstance().split(G3_Configuration::getInstance().getMrv220IpAdress(),".");
            /* 拼成广播地址 */
            std::string broadcastIpAddress = "";
            broadcastIpAddress.append(ipStrUnitList[0]+"."+ipStrUnitList[1]+"."+ipStrUnitList[2]+".255");
            servaddr.sin_addr.s_addr = inet_addr(broadcastIpAddress.c_str());//广播地址
        }else{
            servaddr.sin_addr.s_addr = inet_addr("*************");//广播地址
        }


        servaddr.sin_port = htons(port); //服务器端口号
        //2.开启广播
        const int on = 1;
        int ret = setsockopt(sockfd, SOL_SOCKET, SO_BROADCAST, &on, sizeof(on));
        if (ret >= 0) {
            ret = 0;
            printf("init UDP socket success! port = %d \n", port);

            int nRecvBuf = 700 * 1024;//设置为700K
            if (0 != setsockopt(sockfd, SOL_SOCKET, SO_RCVBUF, (const char *) &nRecvBuf, sizeof(int))) {
                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"H264UDPSocket") << "set UDP socket recvbuf failed!" << XU_LOG_END;

            }

            int nSendBuf = 700 * 1024;//设置为700K
            if (0 != setsockopt(sockfd, SOL_SOCKET, SO_SNDBUF, (const char *) &nSendBuf, sizeof(int))) {
                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"H264UDPSocket") << "set UDP socket sendbuf failed!" << XU_LOG_END;
            }

            /* 把端口存起来 */
            switch (cameraId) {
                case CAMERA_ID_1:{
                    G3_Configuration::getInstance().setCameraUdpPort1(port);
                }
                    break;

                case CAMERA_ID_2:{
                    G3_Configuration::getInstance().setCameraUdpPort2(port);
                }
                    break;
                case CAMERA_ID_3:{
                    G3_Configuration::getInstance().setCameraUdpPort3(port);
                }
                    break;

                case CAMERA_ID_4:{
                    G3_Configuration::getInstance().setCameraUdpPort4(port);
                }
                    break;
            }

        } else {
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"H264UDPSocket") << "set UDP socket broadcast failed!" << XU_LOG_END;
        }
    } else {
        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"H264UDPSocket") << "cread UDP socket failed!" << XU_LOG_END;
    }
    return ret;
}

int H264UDPSocket::sendH264Data(uint8_t *buf, int len) {
    int ret = -1;

    switch (G3_Configuration::getInstance().getRealviewDataFormat()) {
        case REALVIEW_H264_DATA_FORMAT_NOMAL:{
            ret = sendData_old(buf,len);
        }
            break;
        case REALVIEW_H264_DATA_FORMAT_V11:{
            /* 在这里拆分一下粘在一起的SPS+PPS+I帧 */
            uint8_t nalType = (buf[4] & 0x1F);
//    printf("nalType=%d \n",nalType);
            /* 看看是不是SPS */
            if(nalType == 7 ) {
                /* 是SPS，那么就会是粘在一起的。找出NAL标识头 */
                std::vector<int> indexList;
                for(int i = 0; i < len; i ++){
                    if ((i < (len - 4)) && buf[i] == 0x00 && buf[i + 1] == 0x00 && buf[i + 2] == 0x00 && buf[i + 3] == 0x01) {
                        indexList.push_back(i);
                    }
                }
                /* 分开一包包塞进去 */
                for(std::size_t i = 0; i < indexList.size(); i ++){
                    if(i == indexList.size()-1){
                        ret &= sendData_new(buf+indexList[i],(len-indexList[i]));
                    }else{
                        ret &= sendData_new(buf+indexList[i],(indexList[i+1]-indexList[i]));
                    }
                }
            }else{
                /* 不是SPS，直接塞过去 */
                ret = sendData_new(buf,len);
            }
        }
            break;
        
    }
    




    return ret;
}

int H264UDPSocket::closeUPDSocket() {
    /*关闭套接字*/
    close(sockfd);
    sockfd = -1;
    return 0;
}

void H264UDPSocket::reInit() {
    closeUPDSocket();
    init(curPort,curCameraId);
}

int H264UDPSocket::sendData_old(uint8_t *buf, int len) {
    int ret = -1;
    if (sockfd >= 0) {
        /* 由于超过的长度需要分包 */
        if (len > MAX_UDP_PACKAGER_SIZE) {
            /* 先计算下分几包 */
            int packageSum = len / MAX_UDP_PACKAGER_SIZE;
            if (len % MAX_UDP_PACKAGER_SIZE != 0) {
                packageSum++;
            }
            /* 开始分包发送 */
            for (int i = 0; i < packageSum; i++) {
                /* 得判断下是不是最后一包  最后一包要特殊处理 */
                if (i == packageSum - 1) {
                    /* 是最后一包了  则发送剩下的  因为可能不足最大值 */
                    int needSend = len - (i * MAX_UDP_PACKAGER_SIZE);
                    ret = sendto(sockfd, buf + (i * MAX_UDP_PACKAGER_SIZE), needSend, MSG_CONFIRM,
                                 reinterpret_cast<const sockaddr *>(&servaddr), sizeof(servaddr));
                } else {
                    /* 不是最后一包  则发送一包最大的包 */
                    ret = sendto(sockfd, buf + (i * MAX_UDP_PACKAGER_SIZE), MAX_UDP_PACKAGER_SIZE, MSG_CONFIRM,
                                 reinterpret_cast<const sockaddr *>(&servaddr), sizeof(servaddr));
                }
                if (ret < 0) {
                    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"H264UDPSocket") << "sendH264Data to port " << curPort << " failed.  len=" << len << " ret = " << ret << "   err=%s" << strerror(errno) << XU_LOG_END;

                    /* 发送失败的话重新初始化一下端口 */
                    reInit();
                } else {
//                    printf("sendH264Data to port %d success.   len=%d ret = %d \n",curPort,len,ret);
                }
            }


        } else {
            /* 长度没有超过65535-28 直接发送就行了 */
            ret = sendto(sockfd, buf, len, MSG_CONFIRM, reinterpret_cast<const sockaddr *>(&servaddr),
                         sizeof(servaddr));
            if (ret < 0) {
                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"H264UDPSocket") << "sendH264Data to port " << curPort << " failed.  len=" << len << " ret = " << ret << "   err=%s" << strerror(errno) << XU_LOG_END;

                /* 发送失败的话重新初始化一下端口 */
                reInit();
            } else {
//                printf("sendH264Data to port %d success.   len=%d ret = %d \n",curPort,len,ret);
            }
        }


    }
    return ret;
}

int H264UDPSocket::sendData_new(uint8_t *buf, int len) {
    int ret = -1;
    if (sockfd >= 0) {
        /* 长度要大于1，不然计算不合法的数据 */
        if(len > 1){
            /* 把NALU的长度填充到原来的NAL的位置(不包括NAL头的长度) */
            uint32_t nalLen = len-4;
            CodeUtils::getInstance().uint32ToBb(nalLen,packDataHead+8);
            /* 在包头填入流水号 */
            CodeUtils::getInstance().uint32ToBb(flowId,packDataHead+4);
            //累加一下流水号
            if(flowId >= 0xFFFFFFFF){
                flowId = 2;
            }else{
                flowId ++;
            }

            /* 先发送一下NAL标志头和流水号 */
            ret = sendto(sockfd, packDataHead, sizeof(packDataHead), MSG_CONFIRM,
                         reinterpret_cast<const sockaddr *>(&servaddr), sizeof(servaddr));
            if (ret < 0) {
                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"H264UDPSocket") << "sendH264Data to port " << curPort << " failed.  len=" << len << " ret = " << ret << "   err=%s" << strerror(errno) << XU_LOG_END;

                /* 发送失败的话重新初始化一下端口 */
                reInit();
            } else {
//                    printf("sendH264Data to port %d success.   len=%d ret = %d \n",curPort,len,ret);
            }

            /* 发送正式数据，先判断长度是否超过阈值 */
            if (len > MAX_UDP_PACKAGER_SIZE) {
                /* 先计算下分几包 */
                int packageSum = (len-4) / MAX_UDP_PACKAGER_SIZE;
                uint8_t *h264DataBuf = buf + 4;
                if (len % MAX_UDP_PACKAGER_SIZE != 0) {
                    packageSum++;
                }
                /* 开始分包发送 */
                for (int i = 0; i < packageSum; i++) {
                    /* 得判断下是不是最后一包  最后一包要特殊处理 */
                    if (i == packageSum - 1) {
                        /* 是最后一包了  则发送剩下的  因为可能不足最大值 */
                        int needSend = len - (i * MAX_UDP_PACKAGER_SIZE);
                        ret = sendto(sockfd, h264DataBuf + (i * MAX_UDP_PACKAGER_SIZE), needSend, MSG_CONFIRM,
                                     reinterpret_cast<const sockaddr *>(&servaddr), sizeof(servaddr));
                    } else {
                        /* 不是最后一包  则发送一包最大的包 */
                        ret = sendto(sockfd, h264DataBuf + (i * MAX_UDP_PACKAGER_SIZE), MAX_UDP_PACKAGER_SIZE, MSG_CONFIRM,
                                     reinterpret_cast<const sockaddr *>(&servaddr), sizeof(servaddr));
                    }
                    if (ret < 0) {
                        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"H264UDPSocket") << "sendH264Data to port " << curPort << " failed.  len=" << len << " ret = " << ret << "   err=%s" << strerror(errno) << XU_LOG_END;

                        /* 发送失败的话重新初始化一下端口 */
                        reInit();
                    } else {
//                    printf("sendH264Data to port %d success.   len=%d ret = %d \n",curPort,len,ret);
                    }
                }


            } else {
                /* 长度没有超过65535-28 直接发送就行了 */
                ret = sendto(sockfd, buf+4, len-4, MSG_CONFIRM, reinterpret_cast<const sockaddr *>(&servaddr),
                             sizeof(servaddr));
                if (ret < 0) {
                    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"H264UDPSocket") << "sendH264Data to port " << curPort << " failed.  len=" << len << " ret = " << ret << "   err=%s" << strerror(errno) << XU_LOG_END;

                    /* 发送失败的话重新初始化一下端口 */
                    reInit();
                } else {
//                printf("sendH264Data to port %d success.   len=%d ret = %d \n",curPort,len,ret);
                }
            }



        }













    }
    return ret;
}


