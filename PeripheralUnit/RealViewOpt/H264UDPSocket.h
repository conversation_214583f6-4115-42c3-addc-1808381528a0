//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/8.
//

#ifndef VIS_G3_SOFTWARE_H264UDPSOCKET_H
#define VIS_G3_SOFTWARE_H264UDPSOCKET_H


#include <cstdint>
#include <cstdio>
#include <cstring>
#include <netinet/in.h>
#include <cerrno>
#include <unistd.h>

class H264UDPSocket {
public:
    /* UDP协议规定了一包UDP数据内容不能超过65535个 但是UDP的包结构还得去掉28个字节是其他的内容  所以实际可用的字节是65535-28  */
    const static int MAX_UDP_PACKAGER_SIZE = 65000;

    H264UDPSocket();

    ~H264UDPSocket();

    int init(int port, int cameraId);

    int sendH264Data(uint8_t *buf, int len);

    int closeUPDSocket();

    void reInit();

private:
    int sockfd = -1;
    int curPort = -1;
    int curCameraId = -1;
    struct sockaddr_in servaddr;
    uint32_t flowId = 2;


    uint8_t packDataHead[12] = {0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00};

    int sendData_old(uint8_t *buf, int len);

    int sendData_new(uint8_t *buf, int len);


};


#endif //VIS_G3_SOFTWARE_H264UDPSOCKET_H
