//
// Created by Administrator on 2025/6/3.
//

#ifndef VIS_G3_SOFTWARE_H264TCPSENDER_H
#define VIS_G3_SOFTWARE_H264TCPSENDER_H

#include <Poco/Runnable.h>
#include <string>
#include <vector>
#include <array>
#include <mutex>
#include <memory>

class H264TCPSender : public Poco::Runnable{
public:
    void init(int fd, std::string ip, int port);

    void run() override;

    bool sendH264Data(uint8_t *buf, int len);

    bool sendH264Data(uint8_t *headBuf, int headLen,uint8_t *h264Buf, int h264Len);

    int closeTcpSocket();

    uint64_t getLastRecvDataTime() const;

    bool isRunning() const;

private:
    struct H264DataInfo{
        std::array<uint8_t,4194304> h264Buf;
        int h264BufLen = 0;
    };


    int MAX_DATA_LIST_SIZE = 10;
    int clientFd = -1;
    std::string clientIp;
    int clientPort = -1;

    bool needCloseClient = false;

    bool running = false;

    uint64_t lastRecvDataTime = 0xFFFFFFFFFFFFFFFFLU;

    std::vector<H264DataInfo> h264DataList;
    std::vector<H264DataInfo> sendBufList;
    std::mutex h264DataLock;

    std::array<uint8_t, 1024*512> recvBuf = {0x00};

    int lastIndex = -1;

    int sendDataToClient(uint8_t *sendBuf, int bufLen);

    int readDatFromClient();




};


#endif //VIS_G3_SOFTWARE_H264TCPSENDER_H
