//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/9.
//


#include "H264UDPSocketSeparate.h"
#include "XuString.h"
#include "CodeUtils.h"

H264UDPSocketSeparate::H264UDPSocketSeparate() {

}

H264UDPSocketSeparate::~H264UDPSocketSeparate() {

}

int H264UDPSocketSeparate::init(int cameraId) {
    curCameraId = cameraId;
    return 0;
}

int H264UDPSocketSeparate::addClient(int port, std::string ipAddr) {
    int ret = -1;
    if (updClientList.size() < 2) {
        H264UPDSocketClientInfo updSocketClient;
        /* 保存下IP地址 */
        (void) memcpy(updSocketClient.clientIp, ipAddr.c_str(), ipAddr.size());

        /*创建服务器端套接字--IPv4协议，面向无连接通信，UDP协议*/
        updSocketClient.clientFd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
        if (updSocketClient.clientFd >= 0) {
            /* 设置我方IP地址和端口 */
            updSocketClient.addr_recv.sin_family = AF_INET; //设置为IP通信
            updSocketClient.addr_recv.sin_addr.s_addr = INADDR_ANY;//绑定所有网卡
            updSocketClient.addr_recv.sin_port = htons(port); //设置我方端口号  由于直接用的输出端口号做输入端口号，所以不同客户端必须使用不同的端口

            /* 设置接收方IP地址和端口 */
            updSocketClient.addr_send.sin_family = AF_INET;//使用IPv4协议
            updSocketClient.addr_send.sin_port = htons(port);//设置接收方端口号
            updSocketClient.addr_send.sin_addr.s_addr = inet_addr(ipAddr.c_str()); //设置接收方IP


            /* 绑定我方接收用的端口号 */
            ret = bind(updSocketClient.clientFd, (struct sockaddr *) &updSocketClient.addr_recv,
                       sizeof(updSocketClient.addr_recv));//将socket和服务器地址绑定
            if (ret < 0) {
                printf("socket bind fail!\r\n");
                return -1;
            }
            /* 设置接收和发送得缓冲区大小 */
            int nRecvBuf = 700 * 1024;//设置为700K
            if (0 != setsockopt(updSocketClient.clientFd, SOL_SOCKET, SO_RCVBUF, (const char *) &nRecvBuf, sizeof(int))) {
                printf("set UDP socket recvbuf failed!\n");
            }

            int nSendBuf = 700 * 1024;//设置为700K
            if (0 !=
                setsockopt(updSocketClient.clientFd, SOL_SOCKET, SO_SNDBUF, (const char *) &nSendBuf, sizeof(int))) {
                printf("set UDP socket sendbuf failed!\n");
            }
            /* 添加到列表里 */
            updClientList.push_back(updSocketClient);

            ret = 0;

            printf("add udp separate Client success! \n");
        } else {
            printf("cread UDP socket failed! \n");
        }
    } else {
        printf("UDP socket list size >= 2! \n");
    }
    return ret;
}

int H264UDPSocketSeparate::sendH264Data(uint8_t *buf, int len) {
    int ret = -1;
    if (!updClientList.empty()) {
        /* 先定一个vector用来存储需要删除的客户端 */
        std::vector<H264UPDSocketClientInfo> closedClientInfo;
        /* 由于超过的长度需要分包 */
        if (len > MAX_UDP_PACKAGER_SIZE) {
            /* 先计算下分几包 */
            int packageSum = len / MAX_UDP_PACKAGER_SIZE;
            if (len % MAX_UDP_PACKAGER_SIZE != 0) {
                packageSum++;
            }
            /* 开始分包发送 */
            for (int i = 0; i < packageSum; i++) {
                /* 得判断下是不是最后一包  最后一包要特殊处理 */
                if (i == packageSum - 1) {
                    /* 是最后一包了  则发送剩下的  因为可能不足最大值 */
                    int needSend = len - (i * MAX_UDP_PACKAGER_SIZE);
                    int offset = (i * MAX_UDP_PACKAGER_SIZE);
                    /* 遍历一个个发送 */
                    for (std::size_t j = 0; j < updClientList.size(); j++) {
                        ret = sendto(updClientList[j].clientFd, buf + offset, needSend,
                                     MSG_CONFIRM,
                                     reinterpret_cast<const sockaddr *>(&updClientList[j].addr_send),
                                     sizeof(updClientList[j].addr_send));
                        if (ret <= 0) {
                            /* 如果发送失败  先保存下信息 */
                            printf("sendto %s : %d error!\n", updClientList[j].clientIp,
                                   updClientList[j].addr_send.sin_port);
                            closedClientInfo.push_back(updClientList[j]);
                        } else {
                            printf("sendto %s : %d success! datalen=%d \n", updClientList[j].clientIp,
                                   updClientList[j].addr_send.sin_port, ret);
                        }
                    }
                } else {
                    /* 不是最后一包  则发送一包最大的包 */
                    /* 遍历一个个发送 */
                    for (std::size_t j = 0; j < updClientList.size(); j++) {
                        ret = sendto(updClientList[j].clientFd, buf + (i * MAX_UDP_PACKAGER_SIZE),
                                     MAX_UDP_PACKAGER_SIZE, MSG_CONFIRM,
                                     reinterpret_cast<const sockaddr *>(&updClientList[j].addr_send),
                                     sizeof(updClientList[j].addr_send));
                        if (ret <= 0) {
                            /* 如果发送失败  先保存下信息 */
                            printf("sendto %s : %d error!\n", updClientList[j].clientIp,
                                   updClientList[j].addr_send.sin_port);
                            closedClientInfo.push_back(updClientList[j]);
                        } else {
                            printf("sendto %s : %d success! datalen=%d \n", updClientList[j].clientIp,
                                   updClientList[j].addr_send.sin_port, ret);
                        }
                    }


                }

            }


        } else {
            /* 长度没有超过65535-28 直接发送就行了 */
            for (std::size_t i = 0; i < updClientList.size(); i++) {
                ret = sendto(updClientList[i].clientFd, buf, len, MSG_CONFIRM,
                             (struct sockaddr *) &updClientList[i].addr_send,
                             sizeof(updClientList[i].addr_send));
                if (ret <= 0) {
                    /* 如果发送失败  先保存下信息 */
                    printf("sendto %s : %d error!\n", updClientList[i].clientIp, updClientList[i].addr_send.sin_port);
                    closedClientInfo.push_back(updClientList[i]);
                } else {
                    printf("sendto %s : %d success! datalen=%d \n", updClientList[i].clientIp,
                           updClientList[i].addr_send.sin_port, ret);
                }
            }
        }
        /* 发送完了  看看有没有需要发关闭的  有就开始关闭 */
        if (!closedClientInfo.empty()) {
            /* 遍历一个个关闭 */
            for (std::size_t i = 0; i < closedClientInfo.size(); i++) {
                closeUPDSocket(closedClientInfo[i].addr_send.sin_port, closedClientInfo[i].clientIp);
            }
        }

        /* 读一下数据，防止对方不在看了 */
        readUDPData();


    } else {
//        printf("UDP socket list size <= 0! \n");; //not to do
    }



    return ret;
}

int H264UDPSocketSeparate::closeUPDSocket(int port, std::string ipAddr) {
    if (!updClientList.empty()) {
        for (std::vector<H264UPDSocketClientInfo>::iterator iter = updClientList.begin();
             iter != updClientList.end();) {
            printf("ipAddr=%s   port=%d   iter->clientIp = %s ter->addr_send.sin_port=% d \n", ipAddr.c_str(),port,iter->clientIp, iter->addr_send.sin_port);
            if ((strcmp(iter->clientIp, ipAddr.c_str()) == 0) && iter->addr_send.sin_port == port) {
                /* 关掉 */
                close(iter->clientFd);
                /* 从列表里删除 */
                updClientList.erase(iter);

                printf("remove one %s %d \n", iter->clientIp, iter->addr_send.sin_port);
                break;
            } else {
                iter++;
            }
        }
    }
    printf("H264UDPSocketSeparate::closeUPDSocket   clientList.size=%d \n", updClientList.size());
    return 0;
}

int H264UDPSocketSeparate::readUDPData() {
    int ret = -1;
    char buffer_recv[100] = {0};
    if (!updClientList.empty()) {
        /* 先定一个vector用来存储需要删除的客户端 */
        std::vector<H264UPDSocketClientInfo> closedClientInfo;
        /* 遍历一个个接收 */
        for (std::size_t i = 0; i < updClientList.size(); i++) {
            fd_set readFds;
            struct timeval timeout = {0};
            // 初始化延迟时间
            timeout.tv_sec = 0;
            timeout.tv_usec = 1 * 1000;
            // 设置字符集
            FD_ZERO(&readFds);
            FD_SET(updClientList[i].clientFd, &readFds);

            // 阻塞millisecond毫秒检测可读
            ret = select(updClientList[i].clientFd + 1, &readFds, nullptr, nullptr, &timeout);
            if (ret < 0) {
                printf("select recv fail!   error=%s    want to close %s \n", strerror(errno),
                       updClientList[i].clientIp);
                if(updClientList[i].readFaileCount < MAX_UDP_READ_FAILED_COUNT){
                    updClientList[i].readFaileCount ++;
                }else{
                    closedClientInfo.push_back(updClientList[i]);
                }

            } else {
                // 判断_sockFd是否发生可读事件
                if (FD_ISSET(updClientList[i].clientFd, &readFds)) {

                    uint32_t addr_recvLen = sizeof(updClientList[i].addr_recv);
                    ret = recvfrom(updClientList[i].clientFd, buffer_recv, sizeof(buffer_recv), MSG_DONTWAIT,
                                   (struct sockaddr *) &updClientList[i].addr_recv, &addr_recvLen);
                    if (ret <= 0) {
                        /* 如果接收失败  先保存下信息 */
                        printf("recv %s : %d error!\n", updClientList[i].clientIp, updClientList[i].addr_send.sin_port);
                        if(updClientList[i].readFaileCount < MAX_UDP_READ_FAILED_COUNT){
                            updClientList[i].readFaileCount ++;
                        }else{
                            closedClientInfo.push_back(updClientList[i]);
                        }

                    } else {
                        printf("recv %s : %d success! datalen=%d  size=%d\n", updClientList[i].clientIp,
                               updClientList[i].addr_send.sin_port, ret,CodeUtils::getInstance().BbToUint32(
                                        reinterpret_cast<uint8_t *>(buffer_recv)));
                        updClientList[i].readFaileCount = 0;
                    }
                }else{
                    /* 如果接收失败  先保存下信息 */
                    printf("recv %s : %d error!\n", updClientList[i].clientIp, updClientList[i].addr_send.sin_port);
                    if(updClientList[i].readFaileCount < MAX_UDP_READ_FAILED_COUNT){
                        updClientList[i].readFaileCount ++;
                    }else{
                        closedClientInfo.push_back(updClientList[i]);
                    }

                }
            }
        }
        /* 发送完了  看看有没有需要发关闭的  有就开始关闭 */
        if (!closedClientInfo.empty()) {
            /* 遍历一个个关闭 */
            for (std::size_t i = 0; i < closedClientInfo.size(); i++) {
                closeUPDSocket(closedClientInfo[i].addr_send.sin_port, closedClientInfo[i].clientIp);
            }
        }

    } else {
        printf("readUDPData UDP socket list size <= 0! \n");; //not to do
    }
    return ret;
}
