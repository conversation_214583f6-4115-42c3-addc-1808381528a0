//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/9.
//

#ifndef VIS_G3_SOFTWARE_H264UDPSOCKETSEPARATE_H
#define VIS_G3_SOFTWARE_H264UDPSOCKETSEPARATE_H

#include <cstdint>
#include <cstdio>
#include <cstring>
#include <netinet/in.h>
#include <cerrno>
#include <unistd.h>
#include <string>
#include <vector>
#include <arpa/inet.h>

class H264UDPSocketSeparate {
public:
    /* UDP协议规定了一包UDP数据内容不能超过65535个 但是UDP的包结构还得去掉28个字节是其他的内容  所以实际可用的字节是65535-28  */
    const static int MAX_UDP_PACKAGER_SIZE = 65000;
    /* 连续接收消息失败的最大次数  大于这个次数就认为客户端断开了  */
    const static int MAX_UDP_READ_FAILED_COUNT = 120;

    H264UDPSocketSeparate();

    ~H264UDPSocketSeparate();

    int init(int cameraId);

    int addClient(int port,std::string ipAddr);

    int sendH264Data(uint8_t *buf, int len);

    int readUDPData();

    int closeUPDSocket(int port,std::string ipAddr);

private:
    /* 存放连接过来的配置客户端的信息 */
    struct H264UPDSocketClientInfo {
        /* socket描述符 */
        int clientFd;
        /*IP地址 */
        char clientIp[20] = {0x00};
        /* 接收地址和端口的信息 */
        struct sockaddr_in addr_recv = {0};
        /* 发送地址和端口的信息 */
        struct sockaddr_in addr_send = {0};
        /* 接收消息失败的次数 */
        int readFaileCount = 0;

    };


    std::vector<H264UPDSocketClientInfo> updClientList;

    int curCameraId;


};


#endif //VIS_G3_SOFTWARE_H264UDPSOCKETSEPARATE_H
