//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/21.
//

#ifndef VIS_G3_SOFTWARE_UART_MCU_MANAGER_H
#define VIS_G3_SOFTWARE_UART_MCU_MANAGER_H

#include <fcntl.h>
#include <termios.h>
#include <cstring>
#include <unistd.h>
#include "utils/term.h"
#include <Poco/Runnable.h>
#include "PeripheraDataCallback.h"

namespace vis {

    class Uart_MCU_Manager : public Poco::Runnable {
    public:
        /**
        * 初始化UART对应的串口
        *
        * @return 结果     0：成功  其他：失败
        *
        * */
        int initUart(PeripheraDataCallback &peripheraDataCallback);

        /**
         * 发送数据到UART总线上
         *
         * @param buf : 数据内存的指针
         * @param len ： 数据的长度
         *
         *@return 结果    0：成功   其他：失败
         *
         * */
        int sendDataToUart(const uint8_t *buf, const int len);


        /**
         * 关闭UART通信
         *
         * */
        void stopUart();

        bool isUartOpen() const;

        void run() override;


    private:
        const char *UART_TTY_PATH = "/dev/ttyS4";
        const int UART_DATA_CACHE_SIZE = 1024 * 1024;
        int uartTtyFd = -1;
        bool isNeedStopUart = true;
        PeripheraDataCallback *callback;
        bool uartOpen = false;

        uint8_t *uartDataCache;
    };

}
#endif //VIS_G3_SOFTWARE_UART_MCU_MANAGER_H
