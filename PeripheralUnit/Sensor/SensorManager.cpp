//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/31.
//


#include "SensorManager.h"
#include "G3_Configuration.h"
namespace vis {

    int SensorManager::init(PeripheraDataCallback &peripheraDataCallback) {
        int ret = -1;
        ctx = iio_create_local_context();
        if (nullptr == ctx) {
            ret = -1;
        }else{
            devCnt = iio_context_get_devices_count(ctx);

            // 遍历所有的iio设备  以此寻找加速度传感器
            for (unsigned int i = 0; i < devCnt; ++i) {
                dev_GSenor = iio_context_get_device(ctx, i);
                name = iio_device_get_name(dev_GSenor);
                if (strstr(name, "lsm6ds") != nullptr && strstr(name, "accel") != nullptr) {
                    // 找到了加速度传感器
                    fined = true;
                    break;
                }
            }

            // 没找到则直接退出
            if (!fined) {
                iio_context_destroy(ctx);
                ret = -1;
            }else{
                callback = &peripheraDataCallback;
                needStopSensorRead = false;
                ret = 0;
            }

        }
        return ret;
    }

    void SensorManager::destroy() {
        needStopSensorRead = true;
        // 程序结束理应释放ctx
        if (nullptr != ctx) {
            iio_context_destroy(ctx);
        }
    }

    void SensorManager::run() {

        std::string pthreadName = "SensorManager";
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        float gsensor_x = 0;
        float gsensor_y = 0;
        float gsensor_z = 0;
        float scale_value = 1;
        while (!needStopSensorRead) {
            // 找到x轴加速度通道
            chn_GSenor = iio_device_find_channel(dev_GSenor, "accel_x", false);       // x轴跟z轴分别是accel_x和accel_z
            if (nullptr != chn_GSenor) {
                /* 先读出缩放率 */
                ret = iio_channel_attr_read(chn_GSenor, "scale", buf, sizeof(buf));
                scale_value = std::stod(buf);
                // 读取通道的raw字段，读取到的是字符串表达的double
                ret = iio_channel_attr_read(chn_GSenor, "raw", buf, sizeof(buf));
                if (ret >= 0) {
                    //NOTE 这里不固定减去40的误差，因为这个需要实测，而我们没有实测
                    gsensor_x = (std::stod(buf)  * scale_value) ;
                }
            }

            // 找到y轴加速度通道
            chn_GSenor = iio_device_find_channel(dev_GSenor, "accel_y", false);       // x轴跟z轴分别是accel_x和accel_z
            if (nullptr != chn_GSenor) {
                /* 先读出缩放率 */
                ret = iio_channel_attr_read(chn_GSenor, "scale", buf, sizeof(buf));
                scale_value = std::stod(buf);
                // 读取通道的raw字段，读取到的是字符串表达的double
                ret = iio_channel_attr_read(chn_GSenor, "raw", buf, sizeof(buf));
                if (ret >= 0) {
                    //NOTE 这里不固定减去40的误差，因为这个需要实测，而我们没有实测
                    gsensor_y = (std::stod(buf)  * scale_value) ;
                }
            }

            // 找到z轴加速度通道
            chn_GSenor = iio_device_find_channel(dev_GSenor, "accel_z", false);       // x轴跟z轴分别是accel_x和accel_z
            if (nullptr != chn_GSenor) {
                /* 先读出缩放率 */
                ret = iio_channel_attr_read(chn_GSenor, "scale", buf, sizeof(buf));
                scale_value = std::stod(buf);
                // 读取通道的raw字段，读取到的是字符串表达的double
                ret = iio_channel_attr_read(chn_GSenor, "raw", buf, sizeof(buf));
                if (ret >= 0) {
                    //NOTE 这里不固定减去40的误差，因为这个需要实测，而我们没有实测
                    gsensor_z = (std::stod(buf)  * scale_value) ;
                }
            }
            callback->onGetGSensorData(gsensor_x, gsensor_y, gsensor_z);

            usleep(G3_Configuration::getInstance().getGsensorSamplingInterval() * 1000);
        }
        pthread_setname_np(pthread_self(), "Finish");
    }

}
