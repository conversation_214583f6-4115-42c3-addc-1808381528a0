//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/15.
//

#include "RS232OptManager.h"
#include <fcntl.h>
#include <termios.h>
#include <cstring>
#include <unistd.h>
#include "XuString.h"
namespace vis {

    int RS232OptManager::initRS232(PeripheraDataCallback &peripheraDataCallback) {
        int ret = -1;
        callback = &peripheraDataCallback;
        rs232TtyFd = open(RS232_TTY_PATH, O_RDWR | O_NOCTTY);
        if (rs232TtyFd >= 0) {
            ret = term_set(rs232TtyFd, 1, B9600, P_NONE, 8, 1, FC_NONE, 1, 0);
            if (ret >= 0) {
                isNeedStopRS232 = false;
                rs232DataCache = static_cast<uint8_t *>(malloc(RS232_DATA_CACHE_SIZE));

            } else {
                printf("term_set rs232 failed!   error=%s \n", strerror(errno));
            }
        } else {
            printf("open rs232 tty %s fail", RS232_TTY_PATH);
        }
        return ret;
    }

    int RS232OptManager::sendDataToRS232(const uint8_t *buf, const int len) {
        int ret = -1;
        if (rs232TtyFd != -1) {
            ret = write(rs232TtyFd, buf, len);
//            printf("send %d data to RS232,ret=%d   content:%s  \n", len, ret,XuString::getInstance().byteArrayToString(buf,len).c_str());
        }
        return ret;
    }

    void RS232OptManager::stopRS232() {
        if (rs232TtyFd != -1) {
            isNeedStopRS232 = true;
            close(rs232TtyFd);
            free(rs232DataCache);
        }
    }

    void RS232OptManager::run() {

        std::string pthreadName = "RS232IO";
        pthread_setname_np(pthread_self(), pthreadName.c_str());


        int rl = 0;
        while (!isNeedStopRS232) {
            rl = read(rs232TtyFd, rs232DataCache, RS232_DATA_CACHE_SIZE);
//            printf("read  len =%d \n  ", rl);
//            if(rl > 0){
//                printf("content:%s \n", XuString::getInstance().byteArrayToString(rs232DataCache,rl).c_str());
//            }
            if (rl > 0) {
                callback->onGetDataFromRS232(rs232DataCache, rl);
            } else {
                printf("RS232  read error rl=%d\n", rl);
            }
        }
        pthread_setname_np(pthread_self(), "Finish");
    }

}