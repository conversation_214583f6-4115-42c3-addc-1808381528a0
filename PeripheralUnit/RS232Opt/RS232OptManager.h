//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/15.
//

#ifndef VIS_G3_SOFTWARE_RS232OPTMANAGER_H
#define VIS_G3_SOFTWARE_RS232OPTMANAGER_H

#include "utils/term.h"
#include <Poco/Runnable.h>
#include "PeripheraDataCallback.h"

namespace vis {

    class RS232OptManager : public Poco::Runnable {
    public:

        /**
         * 初始化RS232对应的串口
         *
         * @return 结果     0：成功  其他：失败
         *
         * */
        int initRS232(PeripheraDataCallback &peripheraDataCallback);

        /**
         * 发送数据到232总线上
         *
         * @param buf : 数据内存的指针
         * @param len ： 数据的长度
         *
         *@return 结果    0：成功   其他：失败
         *
         * */
        int sendDataToRS232(const uint8_t *buf, const int len);


        /**
         * 关闭232通信
         *
         * */
        void stopRS232();

        void run() override;

    private:

        const char *RS232_TTY_PATH = "/dev/ttyS3";
        const int RS232_DATA_CACHE_SIZE = 1024 * 1024;
        int rs232TtyFd = -1;
        bool isNeedStopRS232 = true;
        PeripheraDataCallback *callback;

        uint8_t *rs232DataCache;

    };

}
#endif //VIS_G3_SOFTWARE_RS232OPTMANAGER_H
