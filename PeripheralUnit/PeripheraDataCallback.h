//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/8.
//

#ifndef VIS_G3_SOFTWARE_PERIPHERADATACALLBACK_H
#define VIS_G3_SOFTWARE_PERIPHERADATACALLBACK_H


#include <cstdint>
#include "XuCANOpt.h"

namespace vis {

    class PeripheraDataCallback {
    public:
        /**
         * 接收到网口Sokcet发来的数据
         *
         * @param ip ： 数据来源的IP地址
         * @param port ： 数据来源的端口
         * @param buf ： 数据内容指针
         * @param len ： 数据内容长度
         *
         * */
        virtual void onGetDataFromSocket(const char *ip, const int port, const uint8_t *buf, int len);

        /**
         * 接收到RS485发来的数据
         *
         * @param buf ： 数据内容指针
         * @param len ： 数据内容长度
         *
         * */
        virtual void onGetDataFromRS485(const uint8_t *buf, int len);

        /**
         * 接收到RS232发来的数据
         *
         * @param buf ： 数据内容指针
         * @param len ： 数据内容长度
         *
         * */
        virtual void onGetDataFromRS232(const uint8_t *buf, int len);

        /**
         * 接收到CANBus发来的数据
         *
         * @param canFrame ： 数据内容的结构器
         *
         * */
        virtual void onGetDataFromCANBus(const can_frame &canFrame);

        /**
         * 接收到GPIO的输入数值变动
         *
         * @param portNum ： GPIO号
         * @param value ： 存放内容的内存指针
         * @param valueLen ： 内容长度
         *
         * */
        virtual void onGPIOValueChange(const int portNum, const char *value, const int valueLen);

        /**
         * 接收到从MCU发来的UART数据
         *
         * @param buf ： 数据内容指针
         * @param len ： 数据内容长度
         *
         * */
        virtual void onGetDataFromUART_MCU(const uint8_t *buf, int len);


        /**
         * 接收到从G-Sensor读取出来的数值
         *
         * @param value_x ： X轴的加速度
         * @param value_y ： Y轴的加速度
         * @param value_z ： Z轴的加速度
         *
         * */
        virtual void onGetGSensorData(float value_x, float value_y, float value_z);

        /**
         * 接收到从传感器读出来的ADC电压值
         *
         * @param voltage ： ADC的电压
         *
         * */
        virtual void onGetADCVoltage(float voltage);

        /**
         * MQTT连接状态发生变化
         *
         * @param isConnected ： 是否连着
         */
        virtual void onGetMQTTConnectStatusChange(const bool isConnected);

        /**
         * 接收到从MQTT来的消息
         *
         * @param mqttType ： MQTT客户端的类型
         * @param topicName ： 该消息的订阅字的指针
         * @param topicNameLen ： 该消息的订阅字的指针的长度
         * @param payload ： 该消息内容的指针
         * @param payloadLen ： 该消息内容的指针的长度
         */
        virtual void onGetDataFromMQTT(const int mqttType, const char *topicName, const int topicNameLen, const char *payload, const int payloadLen);

    };

}
#endif //VIS_G3_SOFTWARE_PERIPHERADATACALLBACK_H
