//
// Created by <PERSON><PERSON>g<PERSON><PERSON><PERSON> on 2022/3/3.
//



#include <unistd.h>
#include "ConfigureServer.h"
#include <arpa/inet.h>
namespace vis {

    int ConfigureServer::init(int port, PeripheraDataCallback &peripheraDataCallback) {


        curTcpPort = port;
        callback = &peripheraDataCallback;
        curSocketIoRunnable.init(*this);
        if (threadPool.available() > 0) {
            threadPool.start(curSocketIoRunnable);
        }
        printf("init socket\n");
        return 0;
    }

    ConfigureServer::ConfigureServer() : threadPool(1, 1) {

    }

    ConfigureServer::~ConfigureServer() {

    }

    void ConfigureServer::run() {
        std::string pthreadName = "SocketListener";
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        int ret = -1;
        while (ret != 0) {
            if ((listenfd = socket(AF_INET, SOCK_STREAM, 0)) == -1) {
                printf("==================create socket error: %s(errno: %d)==========================\n",
                       strerror(errno),
                       errno);
                ret = -1;
            } else {
                memset(&servaddr, 0, sizeof(servaddr));
                servaddr.sin_family = AF_INET;
                servaddr.sin_addr.s_addr = htonl(INADDR_ANY);
                servaddr.sin_port = htons(curTcpPort);
                /* 设置一下端口复用 */
                int opt = 1;
                setsockopt(listenfd, SOL_SOCKET, SO_REUSEADDR, (const void *) &opt, sizeof(opt));
                /* 绑定端口 */
                if (bind(listenfd, (struct sockaddr *) &servaddr, sizeof(servaddr)) == -1) {
                    printf("=========================bind socket error: %s(errno: %d)========================\n",
                           strerror(errno), errno);
                    ret = -1;
                } else {
                    if (listen(listenfd, 10) == -1) {
                        printf("=========================listen socket error: %s(errno: %d)=========================\n",
                               strerror(errno), errno);
                        ret = -1;
                    } else {
                        ret = 0;
                        tcpOpen = true;
                    }
                }
            }
            sleep(1);
        }
        while (listenfd != -1) {
            int connfd = -1;
            if ((connfd = accept(listenfd, (struct sockaddr *) nullptr, 0)) == -1) {
//            printf("accept socket error: %s(errno: %d)   \n",strerror(errno),errno);
                usleep(1000 * 1000);
                continue;
            }
            struct sockaddr_in sa;
            int len;
            len = sizeof(sa);
            if (!getpeername(connfd, (struct sockaddr *) &sa, reinterpret_cast<socklen_t *>(&len))) {
                char *clientIp = inet_ntoa(sa.sin_addr);
                int clientPort = ntohs(sa.sin_port);
                printf("client login. ip: %s, port :%d \n", clientIp, clientPort);
                curSocketIoRunnable.addClient(connfd, clientIp, strlen(clientIp), clientPort);
                /* 新连接过来也重置下数据接收时间 */
                lastRecvDataTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
            } else {
                close(connfd);
            }
        }
        pthread_setname_np(pthread_self(), "Finish");
    }

    void ConfigureServer::stop() {
        close(listenfd);
        listenfd = -1;

    }

    void ConfigureServer::onClientConnect(const char *ip, const int port) {
        printf("ConfigureServer::onClientConnect %s : %d \n", ip, port);
    }

    void ConfigureServer::onClientDisconnect(const char *ip, const int port) {
        printf("ConfigureServer::onClientDisconnect %s : %d \n", ip, port);


    }

    void ConfigureServer::onRecvSocketData(const char *ip, const int port, const uint8_t *buf, const int len) {
        lastRecvDataTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
        callback->onGetDataFromSocket(ip, port, const_cast<uint8_t *>(buf), len);

    }

    void
    ConfigureServer::onSendSocketData(const char *ip, const int port, const uint8_t *buf, const int len,
                                      const int result) {
//    printf("onSendSocketData to %s:%d  result:%d  content:",ip,port,result);
//    for(int i = 0; i < len; i ++){
//        printf("%02x ",buf[i]);
//    }
//    printf("\n");
    }

    int ConfigureServer::sendDataToTCPSokcet(const char *ip, const int port, const uint8_t *buf, int len) {
        int ret = -1;
        ret = curSocketIoRunnable.sendDataToClient(ip, port, buf, len);
        return ret;
    }

    bool ConfigureServer::isWorking() const {
        bool result = tcpOpen;
        if ((G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4_UNIT) &&
            (curSocketIoRunnable.getClientCount() > 0)) {
            int intervalTime = (XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec() - lastRecvDataTime);
            result &= (intervalTime < MAX_RECV_DELAY_TIME);
        }

        return result;
    }

    bool ConfigureServer::isTcpOpen() const {
        return tcpOpen;
    }

}