//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/7.
//

#ifndef VIS_G3_SOFTWARE_SOCKETDATACALLBACK_H
#define VIS_G3_SOFTWARE_SOCKETDATACALLBACK_H


#include <cstdint>

namespace vis {

    class SocketDataCallback {
    public:
        virtual void onRecvSocketData(const char *ip, const int port, const uint8_t *buf, const int len);

        virtual void
        onSendSocketData(const char *ip, const int port, const uint8_t *buf, const int len, const int result);

        virtual void onClientConnect(const char *ip, const int port);

        virtual void onClientDisconnect(const char *ip, const int port);
    };

}
#endif //VIS_G3_SOFTWARE_SOCKETDATACALLBACK_H
