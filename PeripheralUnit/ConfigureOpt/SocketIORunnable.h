//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/3.
//

#ifndef VIS_G3_SOFTWARE_SOCKETIORUNNABLE_H
#define VIS_G3_SOFTWARE_SOCKETIORUNNABLE_H

#include <Poco/Runnable.h>
#include <sys/socket.h>
#include <cstring>
#include <vector>
#include "SocketDataCallback.h"

namespace vis {

    class SocketIORunnable : public Poco::Runnable {
    public:

        /* 存放连接过来的配置客户端的信息 */
        struct ConfigureSocketClient {
            int clientFd;
            char clientIp[20] = {0x00};
            int clientPort;
        };

        const static int RECV_BUF_MAX_SIZE = 1024 * 100;


        void run() override;

        void init(SocketDataCallback &socketDataCallback);

        int sendDataToClient(const char *ip, const int port, const uint8_t *const sendBuf, int bufLen);

        int disConnectClient(ConfigureSocketClient client);

        int addClient(const int fd, const char *ip, const int len, const int port);

        int stop();

        /**
         * 获取下当前的client数量
         *
         * @return 当前的client数量
         */
        const int getClientCount() const;

    private:
        bool needCloseClient;


        std::vector<ConfigureSocketClient> clientList;

        uint8_t *recvBuf;
        SocketDataCallback *socketCallback;


    };

}
#endif //VIS_G3_SOFTWARE_SOCKETIORUNNABLE_H
