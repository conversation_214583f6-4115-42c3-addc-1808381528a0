//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/3.
//



#include <unistd.h>
#include "SocketIORunnable.h"

namespace vis {

    void SocketIORunnable::run() {
        std::string pthreadName = "ClientSocketIO";
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        ssize_t revcLen = 0;
        while (!needCloseClient) {
            /* 读一下所有客户端的数据 */
            for (std::size_t i = 0; i < clientList.size(); i++) {
                //TODO 用双层for进行时效优化
                int ret;
                fd_set readFds;
                struct timeval timeout = {0};

                // 初始化延迟时间
                timeout.tv_sec = 0;
                timeout.tv_usec = 1 * 1000;

                // 设置字符集
                FD_ZERO(&readFds);
                FD_SET(clientList[i].clientFd, &readFds);


                // 阻塞millisecond毫秒检测可读
                ret = select(clientList[i].clientFd + 1, &readFds, nullptr, nullptr, &timeout);
                if (ret < 0) {
                    printf("select recv fail!   error=%s    want to close %s \n", strerror(errno),
                           clientList[i].clientIp);
//            needCloseClient = true;
//            socketCallback->onClientDisconnect(clientIp,clientPort);

                } else {

                    // 判断_sockFd是否发生可写事件

                    if (FD_ISSET(clientList[i].clientFd, &readFds)) {
                        /* revcLen为接收到的长度   如果连接已中止，返回0。如果发生错误，返回-1，应用程序可通过perror()获取相应错误信息 */
                        revcLen = recv(clientList[i].clientFd, recvBuf, RECV_BUF_MAX_SIZE, MSG_DONTWAIT);

                        if (revcLen > 0) {
//                            printf("from %s get %d bytes! \n", clientList[i].clientIp, revcLen);
                            /* 这里价格try  防止发过来的数据有问题 */
                            try {
                                socketCallback->onRecvSocketData(clientList[i].clientIp, clientList[i].clientPort,
                                                                 recvBuf,
                                                                 revcLen);
                            } catch (...) {
                                printf(" socketCallback->onRecvSocketData has Exception! %s \n", strerror(errno));
                            }

                        } else {
                            /* 如果返回0  那么说明连接被中断了  这个客户端就可以关闭了 */
                            if (revcLen == 0) {
                                printf("client %s is closed! \n", clientList[i].clientIp);
                                /* 关掉 */
                                disConnectClient(clientList[i]);
                            }
                        }
                    } else {
//                    printf("client %s:%d is can not to read! \n",clientList[i].clientIp,clientList[i].clientPort);
//                socketCallback->onSendSocketData(clientIp,clientPort,sendBuf, len,-22);
                    }

                }


            }
            /* 由于socket读取是有阻塞超时的，所以这里sleep 1ms就行了 */
            usleep(1 * 1000);
        }

        pthread_setname_np(pthread_self(), "Finish");
    }

    void SocketIORunnable::init(SocketDataCallback &socketDataCallback) {
        recvBuf = static_cast<uint8_t *>(malloc(RECV_BUF_MAX_SIZE));
        socketCallback = &socketDataCallback;
        needCloseClient = false;
    }


    int SocketIORunnable::stop() {
        needCloseClient = true;
        return 0;
    }

    int SocketIORunnable::sendDataToClient(const char *ip, const int port, const uint8_t *const sendBuf, int bufLen) {
        int ret = -1;

        for (std::size_t i = 0; i < clientList.size(); i++) {
            if ((strcmp("ALL", ip) == 0) ||
                ((strcmp(clientList[i].clientIp, ip) == 0) && (clientList[i].clientPort == port))) {
                if (clientList[i].clientFd > 0 && !needCloseClient && bufLen > 0) {
                    int ret;
                    fd_set sendFds;
                    struct timeval timeout = {0};

                    // 初始化延迟时间
                    timeout.tv_sec = 0;
                    timeout.tv_usec = 10 * 1000;

                    // 设置字符集
                    FD_ZERO(&sendFds);
                    FD_SET(clientList[i].clientFd, &sendFds);

                    // 阻塞millisecond毫秒检测可读
                    ret = select(clientList[i].clientFd + 1, nullptr, &sendFds, nullptr, &timeout);
                    if (ret < 0) {
                        printf("select send fail!   error=%s    want to close %s \n", strerror(errno),
                               clientList[i].clientIp);
                        disConnectClient(clientList[i]);
                    } else {
                        // 判断_sockFd是否发生可写事件
                        if (FD_ISSET(clientList[i].clientFd, &sendFds)) {
                            ret = send(clientList[i].clientFd, sendBuf, bufLen, 0);
                            socketCallback->onSendSocketData(clientList[i].clientIp, clientList[i].clientPort, sendBuf,
                                                             bufLen, ret);
                            /* 如果返回-1  那么说明连接被中断了  这个客户端就可以关闭了 */
                            if (ret < 0) {
                                if (ret == -1) {
                                    printf("client %s is closed! \n", clientList[i].clientIp);
                                    disConnectClient(clientList[i]);
                                } else {
                                    printf("send tp client %s failed! error=%s \n", clientList[i].clientIp,
                                           strerror(errno));
                                }
                            }


                        } else {
                            printf("client %s is can not to send! \n", clientList[i].clientIp);
                            socketCallback->onSendSocketData(clientList[i].clientIp, clientList[i].clientPort, sendBuf,
                                                             bufLen, -22);
                        }

                    }
                }
            }
        }


        return ret;
    }

    int SocketIORunnable::addClient(const int fd, const char *ip, const int len, const int port) {
        ConfigureSocketClient client;
        client.clientFd = fd;
        memset(client.clientIp, 0x00, sizeof(client.clientIp));
        memcpy(client.clientIp, ip, len);
        client.clientPort = port;
        if (clientList.size() < 10) {
            clientList.push_back(client);
            printf("SocketIORunnable::addClient   clientList.size=%d \n", clientList.size());
            socketCallback->onClientConnect(ip, port);
        } else {
            /* 关掉 */
            close(fd);
            printf("SocketIORunnable::addClient   clientList.size=%d  is more than 10! \n", clientList.size());
        }


        return 0;
    }

    int SocketIORunnable::disConnectClient(SocketIORunnable::ConfigureSocketClient client) {

        if (clientList.size() > 0) {
            for (std::vector<ConfigureSocketClient>::iterator iter = clientList.begin(); iter != clientList.end();) {
                if ((strcmp(iter->clientIp, client.clientIp) == 0) && iter->clientPort == client.clientPort) {
                    printf("remove one %s %d \n", iter->clientIp, iter->clientPort);
                    /* 关掉 */
                    close(client.clientFd);
                    /* 从列表里删除 */
                    clientList.erase(iter);
                    /* 报出去 */
                    socketCallback->onClientDisconnect(client.clientIp, client.clientPort);

                    break;
                } else {
                    iter++;
                }
            }
        }

        printf("SocketIORunnable::disConnectClient   clientList.size=%d \n", clientList.size());
        return 0;
    }

    const int SocketIORunnable::getClientCount() const {
        return clientList.size();
    }

}

