//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/7.
//

#include <cstdio>
#include "SocketDataCallback.h"
namespace vis {

    void SocketDataCallback::onRecvSocketData(const char *ip, const int port, const uint8_t *buf, const int len) {
        printf("SocketDataCallback::onRecvSocketData   \n");
    }

    void SocketDataCallback::onSendSocketData(const char *ip, const int port, const uint8_t *buf, const int len,
                                              const int result) {
        printf("SocketDataCallback::onSendSocketData   \n");
    }

    void SocketDataCallback::onClientDisconnect(const char *ip, const int port) {
        printf("SocketDataCallback::onClientDisconnect   \n");
    }

    void SocketDataCallback::onClientConnect(const char *ip, const int port) {
        printf("SocketDataCallback::onClientConnect   \n");
    }

}