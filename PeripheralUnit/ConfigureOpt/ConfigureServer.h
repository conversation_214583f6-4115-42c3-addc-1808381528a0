//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/3.
//

#ifndef VIS_G3_SOFTWARE_CONFIGURESERVER_H
#define VIS_G3_SOFTWARE_CONFIGURESERVER_H

#include <Poco/Runnable.h>
#include <Poco/ThreadPool.h>
#include <cstdint>
#include <netinet/in.h>
#include <cerrno>
#include <cstring>
#include <cstdio>
#include <vector>
#include "SocketIORunnable.h"
#include "SocketDataCallback.h"
#include "../PeripheraDataCallback.h"
#include "G3_Configuration.h"
#include "XuTimeUtil.h"

namespace vis {
/**
 * 跟客户端的TCP socket通信管理
 */
    class ConfigureServer : public Poco::Runnable, public SocketDataCallback {
    public:
        void run() override;

        ConfigureServer();

        ~ConfigureServer();

        int init(int port, PeripheraDataCallback &peripheraDataCallback);

        int sendDataToTCPSokcet(const char *ip, const int port, const uint8_t *buf, int len);

        void stop();

        void onClientConnect(const char *ip, const int port) override;

        void onClientDisconnect(const char *ip, const int port) override;

        void onRecvSocketData(const char *ip, const int port, const uint8_t *buf, const int len) override;

        void
        onSendSocketData(const char *ip, const int port, const uint8_t *buf, const int len, const int result) override;


        bool isWorking() const;

        bool isTcpOpen() const;

    private:
        /* 配置服务使用的线程池 */
        Poco::ThreadPool threadPool;

        int curTcpPort = -1;

        int listenfd;
        struct sockaddr_in servaddr;
        uint8_t buff[4096];
        /* 读写socket clinet的操作类 */
        SocketIORunnable curSocketIoRunnable;

        PeripheraDataCallback *callback;

        /* TCP断开是否打开成功 */
        bool tcpOpen = false;

        /* 上次从socket接收到 */
        uint64_t lastRecvDataTime = 0;
        /* 在工作模式为G4的一部分时，Socket接收数据允许的最长间隔，暂定10s */
        const int MAX_RECV_DELAY_TIME = 10;

    };

}
#endif //VIS_G3_SOFTWARE_CONFIGURESERVER_H
