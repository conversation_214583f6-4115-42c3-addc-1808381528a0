//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/17.
//

#ifndef VIS_G3_SOFTWARE_SPEAKERMANAGER_H
#define VIS_G3_SOFTWARE_SPEAKERMANAGER_H

#include "PeripheraDataCallback.h"
#include "rkmedia_api.h"
#include <Poco/Runnable.h>
#include <vector>
#include "G3_Configuration.h"
#include "G3DetectionDefind.h"

namespace vis {

    class SpeakerManager : public Poco::Runnable {
    public:
        int init();

        /**
         * 尝试播放语音文件
         *
         * @param soundFilePath ： 语音文件的路径
         * @param isForce ： 是否强制播放（如果为是，则立马终止现在的语言，再播放这条语音）
         *
         * */
        int tryPlaySound(const char *soundFilePath, bool isForce);

        /**
         * 尝试播放语音文件(播放机科发展的语音文件)
         *
         * @param soundFilePath ： 语音文件的路径
         * @param isForce ： 是否强制播放（如果为是，则立马终止现在的语言，再播放这条语音）
         *
         * */
        int tryPlaySound_JKFZ(const char *soundFilePath, bool isForce);

        /**
         * 设置从识别模块拿到的报警事件
         *
         * @param cameraId : 相机ID
         * @param detectType : 识别算法类型
         * @param eventCode ： 事件码
         *
         * */
        void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

        /**
         * 设置停止报警按钮的状态
         *
         * @param isButtonOn ： 停止报警按钮是否被按下
         */
        void setStopAlarmButtonStatus(const bool isButtonOn);


        int playSoundFile(const char *soundFilePath);

        void stopNowPlaying();

        int close();

        bool isSpeakerOpen() const;

        RK_S32 getS32Volume() const;

        void setS32Volume(RK_S32 s32Volume);

        void run() override;

    private:

        /**
         * 根据输出模式跟语言类型确定声音文件的根目录
         *
         * @param speakerMode ： 输出模式
         * @param languageType ： 语言类型
         * @param path ： 存放路径的地方
         */
        void getSoundFileRootPath(const int speakerMode,const int languageType,std::string &path);


        const char *ALARM_SOUND_FILE_PATH_SE = "alarmSoundFile/soundEffect/";
        const char *ALARM_SOUND_FILE_PATH_VOICE_ZH = "alarmSoundFile/voice_zh/";
        const char *ALARM_SOUND_FILE_PATH_VOICE_EN = "alarmSoundFile/voice_en/";

        PeripheraDataCallback *callback;

        bool needStopSpeakerManaget = true;
        /* 采样率 */
        RK_U32 u32SampleRate = 44100;
        /* 音频输出通道的通道号 */
        RK_S32 aoChn = 0;
        /* 几声道 */
        RK_U32 u32ChnCnt = 1;
        /* 读取一帧的数据大小 */
        RK_U32 u32FrameCnt = 1152;
        /* 喇叭音量 0~100 */
        RK_S32 s32Volume = 100;
        /* 音频输出的参数 */
        AO_CHN_ATTR_S ao_attr;
        /* 是否正在播放报警 */
        bool isplaying_alarm = false;
        /* 是否正在播放JKFZ的语音 */
        bool isplaying_speech = false;
        /* 是否需要停止现在的播放 */
        bool needStopNowPlay = false;
        /* 扬声器是否打开成功 */
        bool speakerOpen = false;
        /* 是否是播放普通的语音文件 */
        bool speechWaitToPlay = false;


        /* 当前正在播放的语言文件名字 */
        std::string curPlaySoudFilePath = "";


        /* 报警间隔时间  1.5s */
        const uint64_t PEDSESTRIAN_ALARM_INTERVAL_TIME = 2;
        /* 上次发送行人报警的时间 */
        uint64_t lastEventTime = 0;
        /* 上次发送的报警事件 */
        int lastEventCode = -1;

        uint32_t lastBSDLevel1AlarmId = -1;
        uint32_t lastBSDLevel2AlarmId = -1;

        /* 是否忽略掉报警声音  如果是的话，那么有报警来的时候，喇叭直接不响 */
        bool needIgnoreAlarmSound = false;


    };

}
#endif //VIS_G3_SOFTWARE_SPEAKERMANAGER_H
