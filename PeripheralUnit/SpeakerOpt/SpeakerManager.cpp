//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/17.
//



#include <cstdio>
#include <cstring>
#include <unistd.h>
#include "SpeakerManager.h"
#include "XuTimeUtil.h"

namespace vis {

    int SpeakerManager::init() {
        int ret = -1;
        // default:CARD=rockchiprk809co
        const RK_CHAR *pDeviceName = "default";
        SAMPLE_FORMAT_E enSampleFmt = RK_SAMPLE_FMT_U8;
        RK_MPI_SYS_Init();


        ao_attr.pcAudioNode = pDeviceName;
        ao_attr.enSampleFormat = enSampleFmt;
        ao_attr.u32NbSamples = u32FrameCnt;
        ao_attr.u32SampleRate = u32SampleRate;
        ao_attr.u32Channels = u32ChnCnt;
        ret = RK_MPI_AO_SetChnAttr(aoChn, &ao_attr);
        ret |= RK_MPI_AO_EnableChn(aoChn);
        if (ret == 0) {
//        RK_S32 s32CurrentVolmue = -1;
//        RK_MPI_AO_GetVolume(0, &s32CurrentVolmue);
//        if (ret) {
//            printf("Get Volume(before) failed! ret=%d\n", ret);
//        }
//        printf("#Before Volume set, volume=%d\n", s32CurrentVolmue);

            ret = RK_MPI_AO_SetVolume(aoChn, s32Volume);
            if (ret) {
                printf("Set Volume failed! ret=%d\n", ret);
            }

            speakerOpen = true;

            needStopSpeakerManaget = false;

        } else {
            printf("ERROR: create ao[0] failed! ret=%d\n", ret);
        }
        return ret;
    }


    int SpeakerManager::playSoundFile(const char *soundFilePath) {
        int ret = -1;
        needStopNowPlay = false;
        FILE *file = fopen(soundFilePath, "r");
        if (!file) {
            printf("ERROR: open %s failed!  error=%s\n", soundFilePath, strerror(errno));
            /* 清理掉，免得一直尝试播放 */
            curPlaySoudFilePath.clear();
        } else {
            /* 先每次都重新设置一下音量 */
            ret = RK_MPI_AO_SetVolume(aoChn, G3_Configuration::getInstance().getSpeakerVolume());
            if (ret) {
                printf("Set Volume failed! ret=%d\n", ret);
            }

            MEDIA_BUFFER mb = nullptr;
            RK_U32 u32Timeval = (u32FrameCnt / u32SampleRate) * 1000000;
            RK_U32 u32ReadSize;
            MB_AUDIO_INFO_S stSampleInfo = {ao_attr.u32Channels, ao_attr.u32SampleRate,
                                            ao_attr.u32NbSamples, ao_attr.enSampleFormat};
            while (!needStopNowPlay) {
                mb = RK_MPI_MB_CreateAudioBufferExt(&stSampleInfo, RK_FALSE, 0);
                if (!mb) {
                    printf("ERROR: create audio buffer\n");
                    break;
                }
                u32ReadSize = RK_MPI_MB_GetSize(mb);
//                printf("# TimeVal:%dus, ReadSize:%d\n", u32Timeval, u32ReadSize);
                ret = fread(RK_MPI_MB_GetPtr(mb), 1, u32ReadSize, file);
                if (!ret) {
                    printf("# Get end of file!\n");
                    break;
                }
                ret = RK_MPI_SYS_SendMediaBuffer(RK_ID_AO, 0, mb);
                if (ret) {
                    printf("ERROR: RK_MPI_SYS_SendMediaBuffer failed! ret = %d\n", ret);
                    break;
                }
                usleep(u32Timeval);
                RK_MPI_MB_ReleaseBuffer(mb);
                mb = nullptr;
                ret = 0;
            }

            if (mb) {
                RK_MPI_MB_ReleaseBuffer(mb);
                mb = nullptr;
            }



            /* 只有不是被强制打断的时候才需要清   强制打断的话早就已经清了 */
            if (!needStopNowPlay) {
                curPlaySoudFilePath.clear();
            }
            isplaying_alarm = false;
            isplaying_speech = false;

            fclose(file);
        }

        return ret;
    }

    int SpeakerManager::close() {
        RK_MPI_AO_DisableChn(aoChn);
        return 0;
    }

    void SpeakerManager::stopNowPlaying() {
        needStopNowPlay = true;
    }

    bool SpeakerManager::isSpeakerOpen() const {
        return speakerOpen;
    }

    RK_S32 SpeakerManager::getS32Volume() const {
        return s32Volume;
    }

    void SpeakerManager::setS32Volume(RK_S32 s32Volume) {
        SpeakerManager::s32Volume = s32Volume;
    }

    void SpeakerManager::run() {
        std::string pthreadName = "SpeakerManager";
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        while (!needStopSpeakerManaget) {
            /* 不为空则表示需要播放声音  那么开始播放 */
            if (!curPlaySoudFilePath.empty()) {
                if (!speechWaitToPlay) {
//                    printf("!speechWaitToPlay ==%d    %s    \n", !speechWaitToPlay, curPlaySoudFilePath.c_str());
                    isplaying_alarm = true;
                    isplaying_speech = false;
                } else {
                    isplaying_alarm = false;
                    isplaying_speech = true;
                }

                playSoundFile(curPlaySoudFilePath.c_str());
            }
            usleep(10 * 1000);
        }
        pthread_setname_np(pthread_self(), "Finish");

    }

    int SpeakerManager::tryPlaySound(const char *soundFilePath, bool isForce) {
//        printf("tryPlaySound  soundFilePath=%s   isForce=%d  \n", soundFilePath, isForce);
        if (isForce) {
            /* 需要强制中断则强制中断 */
            needStopNowPlay = true;
            curPlaySoudFilePath.clear();
            curPlaySoudFilePath.append(soundFilePath);
            speechWaitToPlay = false;
        } else {
            /* 看看是否正在播放报警语音 */
            if (!isplaying_alarm) {
                if (isplaying_speech) {
                    needStopNowPlay = true;
                }
                /* 没在播放报警语音则直接播放 */
                curPlaySoudFilePath.clear();
                curPlaySoudFilePath.append(soundFilePath);
                speechWaitToPlay = false;
            }
        };


        return 0;
    }

    int SpeakerManager::tryPlaySound_JKFZ(const char *soundFilePath, bool isForce) {
        printf("tryPlaySound_JKFZ  soundFilePath=%s isForce=%d  \n", soundFilePath, isForce);
        if (isForce) {
            /* 看看是否在播放报警语音 如果不是才可以播放 */
            if (!isplaying_alarm) {
                /* 看看正在播放的是不是跟现在要播放的相同  如果相同则直接不理 */
                if (curPlaySoudFilePath.compare(soundFilePath) == 0) {
                    printf("curPlaySoudFilePath:%s == soundFilePath:%s    \n", curPlaySoudFilePath.c_str(),
                           soundFilePath);
                } else {
                    /* ID不相同 直接打断 */
                    needStopNowPlay = true;
                    curPlaySoudFilePath.clear();
                    curPlaySoudFilePath.append(soundFilePath);
                    speechWaitToPlay = true;
                }
            }
        } else {
            /* 没在播放报警语音的时候才可以播放 */
            if (!isplaying_alarm) {
                curPlaySoudFilePath.clear();
                curPlaySoudFilePath.append(soundFilePath);
                speechWaitToPlay = true;
            }
        }

        return 0;
    }

    void SpeakerManager::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
        if(!needIgnoreAlarmSound){
                /* 根据声音类型看看需要从哪个文件夹里面读声音文件 */
                std::string filePath = "";
                uint32_t lastAlarmId = -1;
                int speakerRingingMode = 0;
                switch (eventCode) {
                    case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1:
                    case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1:{
                        lastAlarmId = lastBSDLevel1AlarmId;
                        speakerRingingMode = G3_Configuration::getInstance().getBsdAlarmLevel1SpeakerRingingModel();
                        getSoundFileRootPath(G3_Configuration::getInstance().getBsdPedestrianAlarmSpeakerModelArea1(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);

                    }
                        break;



                    case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_FORWARD_VEHICLE_LEVEL1:
                    case EVENT_BSD_BACKWARD_VEHICLE_LEVEL1:
                    case EVENT_SBST_FORWARD_FCW_LEVEL1:{
                        lastAlarmId = lastBSDLevel1AlarmId;
                        speakerRingingMode = G3_Configuration::getInstance().getBsdAlarmLevel1SpeakerRingingModel();
                        getSoundFileRootPath(G3_Configuration::getInstance().getBsdVehicleAlarmSpeakerModelArea1(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);

                    }
                        break;
                    case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2:
                    case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2:{
                        lastAlarmId = lastBSDLevel2AlarmId;
                        speakerRingingMode = G3_Configuration::getInstance().getBsdAlarmLevel2SpeakerRingingModel();
                        getSoundFileRootPath(G3_Configuration::getInstance().getBsdPedestrianAlarmSpeakerModelArea2(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);

                    }
                        break;

                    case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_FORWARD_VEHICLE_LEVEL2:
                    case EVENT_BSD_BACKWARD_VEHICLE_LEVEL2:
                    case EVENT_SBST_FORWARD_FCW_LEVEL2:{
                        lastAlarmId = lastBSDLevel2AlarmId;
                        speakerRingingMode = G3_Configuration::getInstance().getBsdAlarmLevel2SpeakerRingingModel();

                        getSoundFileRootPath(G3_Configuration::getInstance().getBsdVehicleAlarmSpeakerModelArea2(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);

                    }
                        break;


                    case EVENT_BSD_R151_AREA1_PEDESTRIAN:{
                        /* 原来R151的区域1、3、4是同一开关，州哥给忘了，记成分开控制且给出了配置表，故现在改成分开控制 */
                        getSoundFileRootPath(G3_Configuration::getInstance().getR151PedestrianAlarmSpeakerModelArea1(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;

                    case EVENT_BSD_R151_AREA2_PEDESTRIAN:{
                        /* R151的2区域可能特殊使用特殊的配置 */
                        getSoundFileRootPath(G3_Configuration::getInstance().getR151PedestrianAlarmSpeakerModelArea2(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;

                    case EVENT_BSD_R151_AREA3_PEDESTRIAN:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR151PedestrianAlarmSpeakerModelArea3(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;
                    case EVENT_BSD_R151_AREA4_PEDESTRIAN:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR151PedestrianAlarmSpeakerModelArea4(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;

                    case EVENT_BSD_R151_AREA1_VEHICLE:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR151VehicleAlarmSpeakerModelArea1(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;

                    case EVENT_BSD_R151_AREA2_VEHICLE:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR151VehicleAlarmSpeakerModelArea2(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;

                    case EVENT_BSD_R151_CAMERA_COVER:
                    case EVENT_BSD_R158_CAMERA_COVER:
                    case EVENT_BSD_R159_CAMERA_COVER:{
                        //TODO 镜头报警州哥确定只用语音
                        getSoundFileRootPath(SPEAKER_ENABLE_TYPE_VOICE,G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;

                    case EVENT_BSD_R158_AREA1_PEDESTRIAN:{

                        getSoundFileRootPath(G3_Configuration::getInstance().getR158PedestrianAlarmSpeakerModelArea1(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;
                    case EVENT_BSD_R158_AREA2_PEDESTRIAN:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR158PedestrianAlarmSpeakerModelArea2(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;
                    case EVENT_BSD_R158_AREA3_PEDESTRIAN:{
                        /* R158行人三级不响喇叭 */
                        getSoundFileRootPath(G3_Configuration::getInstance().getR158PedestrianAlarmSpeakerModelArea3(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;
                    case EVENT_BSD_R158_AREA1_VEHICLE:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR158VehicleAlarmSpeakerModelArea1(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                    case EVENT_BSD_R158_AREA2_VEHICLE:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR158VehicleAlarmSpeakerModelArea2(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;

                    case EVENT_BSD_R159_AREA1_PEDESTRIAN:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR159PedestrianAlarmSpeakerModelArea1(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;
                    case EVENT_BSD_R159_AREA2_PEDESTRIAN:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR159PedestrianAlarmSpeakerModelArea2(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;
                    case EVENT_BSD_R159_AREA3_PEDESTRIAN:{
                        /* R159行人三级不响喇叭 */
                        getSoundFileRootPath(G3_Configuration::getInstance().getR159PedestrianAlarmSpeakerModelArea3(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;
                    case EVENT_BSD_R159_AREA1_VEHICLE:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR159VehicleAlarmSpeakerModelArea1(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;
                    case EVENT_BSD_R159_AREA2_VEHICLE:{
                        getSoundFileRootPath(G3_Configuration::getInstance().getR159VehicleAlarmSpeakerModelArea2(),G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;

                    case EVENT_DSM:{
                        getSoundFileRootPath(SPEAKER_ENABLE_TYPE_VOICE,G3_Configuration::getInstance().getSpeakerSoundLanguage(),filePath);
                    }
                        break;


                    default:{
                        /* 如果是没有的报警类型，那么直接退出 */
                        return;
                    }
                        break;


                }


                if (!filePath.empty()){
                    uint64_t userTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec() - lastEventTime;
                    if (userTime >= PEDSESTRIAN_ALARM_INTERVAL_TIME) {
                        lastEventTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
                        lastEventCode = -1;
                    }

                    /* 看看喇叭是不是也需要同ID不持续报警  需要的话就判断下上一次的报警ID是不是跟这次相同  相同就不报了 */
                    if (speakerRingingMode == 1 && lastAlarmId == alarmEventInfo.bsdAlarmInfo.alarmPedestrianId) {
                        printf("speakerRingingMode is not same id!  lastAlarmId=%d   curAlarmId=%d  \n", lastAlarmId,
                               alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                        return;
                    } else {

//                printf("speakerRingingMode is need same id! speakerRingingMode=%d lastAlarmId=%d   curAlarmId=%d  \n",
//                       speakerRingingMode, lastAlarmId, alarmEventInfo.bsdAlarmInfo.alarmPedestrianId);
                    }


                    if (eventCode != lastEventCode) {
                        /* 收到事件了 先播放声音 */
                        switch (eventCode) {
                            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
                            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1: {
                                lastBSDLevel1AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel1SpeakerEnable()) {
                                    filePath.append("pdw.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;
                            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
                            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2: {
                                lastBSDLevel2AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel2SpeakerEnable()) {
                                    filePath.append("pdw_2.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;

                            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
                            case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1: {
                                lastBSDLevel1AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel1SpeakerEnable()) {
                                    filePath.append("pdw.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;
                            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
                            case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2: {
                                lastBSDLevel2AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel2SpeakerEnable()) {
                                    filePath.append("pdw_2.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;


                            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1: {
                                lastBSDLevel1AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel1SpeakerEnable()) {
                                    filePath.append("pdw.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;

                            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2: {
                                lastBSDLevel2AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel2SpeakerEnable()) {
                                    filePath.append("pdw_2.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;

                            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1: {
                                lastBSDLevel1AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel1SpeakerEnable()) {
                                    filePath.append("pdw.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;

                            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2: {
                                lastBSDLevel2AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel2SpeakerEnable()) {
                                    filePath.append("pdw_2.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;


                            case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1:
                            case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1:
                            case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1:
                            case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1:
                            case EVENT_BSD_FORWARD_VEHICLE_LEVEL1:
                            case EVENT_BSD_BACKWARD_VEHICLE_LEVEL1:
                            case EVENT_SBST_FORWARD_FCW_LEVEL1:{
                                lastBSDLevel2AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel1SpeakerEnable()) {
                                    filePath.append("fcw.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;

                            case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2:
                            case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2:
                            case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2:
                            case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2:
                            case EVENT_BSD_FORWARD_VEHICLE_LEVEL2:
                            case EVENT_BSD_BACKWARD_VEHICLE_LEVEL2:
                            case EVENT_SBST_FORWARD_FCW_LEVEL2:{
                                lastBSDLevel2AlarmId = alarmEventInfo.bsdAlarmInfo.alarmPedestrianId;
                                /* 看看是否能播放声音 */
//                                if (G3_Configuration::getInstance().getBsdAlarmLevel2SpeakerEnable()) {
                                    filePath.append("fcw_2.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                    lastEventCode = eventCode;
//                                }
                            }
                                break;


                            case EVENT_BSD_R151_AREA1_PEDESTRIAN:{
                                filePath.append("r151_pedestrian_zone1.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;

                            case EVENT_BSD_R151_AREA2_PEDESTRIAN:{
                                filePath.append("r151_pedestrian_zone2.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;

                            case EVENT_BSD_R151_AREA3_PEDESTRIAN:{
                                filePath.append("r151_pedestrian_zone3.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;

                            case EVENT_BSD_R151_AREA4_PEDESTRIAN:{
                                filePath.append("r151_pedestrian_zone4.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;


                            case EVENT_BSD_R151_AREA1_VEHICLE:{
                                filePath.append("r151_vehicle_zone1.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;

                            case EVENT_BSD_R151_AREA2_VEHICLE:{
                                filePath.append("r151_vehicle_zone2.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;

                            case EVENT_BSD_R151_CAMERA_COVER:{
                                //TODO 镜头遮挡的报警需要确定报警声音用什么样的
                                filePath.append("camcoverd.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;

                            case EVENT_BSD_R158_AREA1_PEDESTRIAN:{
                                filePath.append("r158_pedestrian_zone1.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;
                            case EVENT_BSD_R158_AREA2_PEDESTRIAN:{
                                filePath.append("r158_pedestrian_zone2.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;
                            case EVENT_BSD_R158_AREA3_PEDESTRIAN:{
                                filePath.append("r158_pedestrian_zone3.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;

                            }
                                break;
                            case EVENT_BSD_R158_AREA1_VEHICLE:{
                                filePath.append("r158_vehicle_zone1.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;
                            case EVENT_BSD_R158_AREA2_VEHICLE:{
                                filePath.append("r158_vehicle_zone2.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;
                            case EVENT_BSD_R158_CAMERA_COVER:{
                                //TODO 镜头遮挡的报警需要确定报警声音用什么样的
                                filePath.append("camcoverd.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;
                            case EVENT_BSD_R159_AREA1_PEDESTRIAN:{
                                filePath.append("r159_pedestrian_zone1.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;
                            case EVENT_BSD_R159_AREA2_PEDESTRIAN:{
                                filePath.append("r159_pedestrian_zone2.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;

                            case EVENT_BSD_R159_AREA3_PEDESTRIAN:{
                                filePath.append("r159_pedestrian_zone3.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;

                            }
                                break;
                            case EVENT_BSD_R159_AREA1_VEHICLE:{
                                filePath.append("r159_vehicle_zone1.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;
                            case EVENT_BSD_R159_AREA2_VEHICLE:{
                                filePath.append("r159_vehicle_zone1.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;
                            case EVENT_BSD_R159_CAMERA_COVER:{
                                //TODO 镜头遮挡的报警需要确定报警声音用什么样的
                                filePath.append("camcoverd.pcm");
                                tryPlaySound(filePath.c_str(), false);
                                lastEventCode = eventCode;
                            }
                                break;
                            case EVENT_DSM:{
                                //FIXME DSM要区分报警的内容,这里优先级Joe哥哥说定不了，张文祥先给个初步的，但是只有五个

                                if(alarmEventInfo.dsmAlarmInfo.eye_alarm){
                                    filePath.append("dsm_eyeclose.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                }else if(alarmEventInfo.dsmAlarmInfo.mouth_alarm){
                                    filePath.append("dsm_yawn.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                }else if(alarmEventInfo.dsmAlarmInfo.smoking_alarm){
                                    filePath.append("dsm_smoking.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                }else if(alarmEventInfo.dsmAlarmInfo.phone_alarm){
                                    filePath.append("dsm_phone.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                }else if(alarmEventInfo.dsmAlarmInfo.lookaround_alarm){
                                    filePath.append("dsm_lookaround.pcm");
                                    tryPlaySound(filePath.c_str(), false);
                                }
                                lastEventCode = eventCode;
                            }
                                break;
                        }

                    }

//            printf("TimeVal  PeripheralUnitManager::setDetectionEvent eventCode=%d  userTime=%u  \n", eventCode,
//                   userTime);
                }


        }


    }

    void SpeakerManager::getSoundFileRootPath(const int speakerMode, const int languageType, std::string &path) {
        /* 看看需要播放什么声音 */
        switch (speakerMode) {
            case SPEAKER_ENABLE_TYPE_SOUND_EFFECT: {
                path.append(ALARM_SOUND_FILE_PATH_SE);
            }
                break;
            case SPEAKER_ENABLE_TYPE_VOICE: {
                /* 需要报语音  那么看看是需要报什么类型的语音 */
                switch (languageType) {
                    case SPEAKER_SOUND_LANGUAGE_CHINESE: {
                        /* 报中文语音 */
                        path.append(ALARM_SOUND_FILE_PATH_VOICE_ZH);
                    }
                        break;
                    case SPEAKER_SOUND_LANGUAGE_ENGLISH: {
                        /* 报英文语音 */
                        path.append(ALARM_SOUND_FILE_PATH_VOICE_EN);
                    }
                        break;
                    default: {
                        /* 默认报中文语音 */
                        path.append(ALARM_SOUND_FILE_PATH_VOICE_ZH);
                    }
                        break;
                }
            }
                break;
        }
    }

    void SpeakerManager::setStopAlarmButtonStatus(const bool isButtonOn) {
        /* 被按下了就表示需要停止报警时候的喇叭声音 */
        needIgnoreAlarmSound = isButtonOn;

    }

}