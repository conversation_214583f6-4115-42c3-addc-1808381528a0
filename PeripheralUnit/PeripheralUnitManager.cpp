//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/3.
//


#include <XuTimeUtil.h>
#include "PeripheralUnitManager.h"
#include "G3_Configuration.h"
#include "XuLog.h"

namespace vis {

    void PeripheralUnitManager::start() {

        /* 启动单播的服务 */
        h264UdpSocketSeparate_camera1.init(0);
        h264UdpSocketSeparate_camera2.init(1);
        h264UdpSocketSeparate_camera3.init(2);
        h264UdpSocketSeparate_camera4.init(3);
        /* 启动TCP的服务 */
        h264TcpServerSocket_camera1.init(G3_TCP_CAMERA1_PORT);
        h264TcpServerSocket_camera2.init(G3_TCP_CAMERA2_PORT);
        h264TcpServerSocket_camera3.init(G3_TCP_CAMERA3_PORT);
        h264TcpServerSocket_camera4.init(G3_TCP_CAMERA4_PORT);

        configureServer.init(TCP_COM_PORT, *this);

        if (threadPool.available() > 0) {
            threadPool.start(configureServer);
        }

//        canBusManager.init(250000, *this);
//        if (threadPool.available() > 0) {
//            threadPool.start(canBusManager);
//        }

        speakerManager.init();
        if (threadPool.available() > 0) {
            threadPool.start(speakerManager);
        }


        /* 判断一下是不是V6模式 */
        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6){
            /* 不是V6模式，启动正常的GPIO处理 */
            gpioManager.init(*this);
            if (threadPool.available() > 0) {
                threadPool.start(gpioManager);
            }
        }else{
            /* 是V6模式，启动V6专用的的GPIO处理 */
            gpioManager_V6.init(*this);
            if (threadPool.available() > 0) {
                threadPool.start(gpioManager_V6);
            }
        }


        /* 启动RS485通信 */
        rs485OptManager.initRS485(*this);
        if (threadPool.available() > 0) {
            threadPool.start(rs485OptManager);
        }
        /* 启动uart通信 */
        uartMcuManager.initUart(*this);
        if (threadPool.available() > 0) {
            threadPool.start(uartMcuManager);
        }
        /* 启动sensor读取 */
        sensorManager.init(*this);
        if (threadPool.available() > 0) {
            threadPool.start(sensorManager);
        }

        /* 启动RS232通信 */
        rs232OptManager.initRS232(*this);
        if (threadPool.available() > 0) {
            threadPool.start(rs232OptManager);
        }


        adcVoltageManager.init(*this);
        if (threadPool.available() > 0) {
            threadPool.start(adcVoltageManager);
        }


        /* 如果需要连接NDS，那么就启动NDS通信和RTMP推流 */
        if (G3_Configuration::getInstance().getCloudPlatformTypes().nds) {
            ndsMqttManager.initNDSMqtt(VISMQTTCLIENTTYPE_NDS, *this);
            if (threadPool.available() > 0) {
                threadPool.start(ndsMqttManager);
            }
            rtmpPusher1.setCameraId(CAMERA_ID_1);

            rtmpPusher2.setCameraId(CAMERA_ID_2);

            rtmpPusher3.setCameraId(CAMERA_ID_3);

            rtmpPusher4.setCameraId(CAMERA_ID_4);
        }
    }

    PeripheralUnitManager::PeripheralUnitManager() : threadPool(7, 11) {

    }

    PeripheralUnitManager::~PeripheralUnitManager() {

    }

    void PeripheralUnitManager::onGetDataFromSocket(const char *ip, const int port, const uint8_t *buf, int len) {
        callback->onGetDataFromSocket(ip, port, buf, len);
    }

    void PeripheralUnitManager::setInterface(PeripheraDataCallback &peripheraDataCallback) {
        callback = &peripheraDataCallback;
    }

    int PeripheralUnitManager::sendDataToSokcet(const char *ip, const int port, const uint8_t *buf, int len) {
        configureServer.sendDataToTCPSokcet(ip, port, buf, len);
        return 0;
    }

    int PeripheralUnitManager::sendH264DataToUDP(const int cameraId, const uint8_t *buf, int len) {
        switch (cameraId) {
            case CAMERA_ID_1: {
                h264UdpSocketSeparate_camera1.sendH264Data(const_cast<uint8_t *>(buf), len);
                h264UdpSocket_camera1.sendH264Data(const_cast<uint8_t *>(buf), len);
            }
                break;
            case CAMERA_ID_2: {
                h264UdpSocketSeparate_camera2.sendH264Data(const_cast<uint8_t *>(buf), len);
                h264UdpSocket_camera2.sendH264Data(const_cast<uint8_t *>(buf), len);
            }
                break;
            case CAMERA_ID_3: {
                h264UdpSocketSeparate_camera3.sendH264Data(const_cast<uint8_t *>(buf), len);
                h264UdpSocket_camera3.sendH264Data(const_cast<uint8_t *>(buf), len);
            }
                break;
            case CAMERA_ID_4: {
                h264UdpSocketSeparate_camera4.sendH264Data(const_cast<uint8_t *>(buf), len);
                h264UdpSocket_camera4.sendH264Data(const_cast<uint8_t *>(buf), len);
            }
                break;

        }

        return 0;
    }

    void PeripheralUnitManager::onGetDataFromRS485(const uint8_t *buf, int len) {
        callback->onGetDataFromRS485(buf, len);
    }

    void PeripheralUnitManager::onGetDataFromRS232(const uint8_t *buf, int len) {
        callback->onGetDataFromRS232(buf, len);
    }

    void PeripheralUnitManager::onGetDataFromCANBus(const can_frame &canFrame) {
        callback->onGetDataFromCANBus(canFrame);
    }

    int PeripheralUnitManager::playSound(const char *soundFilePaht, bool isForce) {
        return speakerManager.tryPlaySound(soundFilePaht, isForce);
    }

    void PeripheralUnitManager::onGPIOValueChange(const int portNum, const char *value, const int valueLen) {
        callback->onGPIOValueChange(portNum, value, valueLen);
    }

    int PeripheralUnitManager::sendDataToRS485(const uint8_t *buf, int len) {
        return rs485OptManager.sendDataToRS485(buf, len);
    }

    int PeripheralUnitManager::getWorkStaus() {
        /* 先检查CAN */
        int ret = true;
        /* 再检查以太网的TCP通信 */
        ret &= configureServer.isWorking();

        /* 再检查RS485通信 */
        ret &= rs485OptManager.isRs485Open();
        /* 再检查speaker通信 */
        ret &= speakerManager.isSpeakerOpen();
        /* 再检查Uart通信 */
        ret &= uartMcuManager.isUartOpen();
        return (ret ? 0 : -1);
    }


    void PeripheralUnitManager::onGetDataFromUART_MCU(const uint8_t *buf, int len) {
        callback->onGetDataFromUART_MCU(buf, len);
    }

    int PeripheralUnitManager::sendDataToUART_MCU(const uint8_t *buf, int len) {
        return uartMcuManager.sendDataToUart(buf, len);
    }

    int PeripheralUnitManager::startUPDBroadcast(const int camera1_udp_port, const int camera2_udp_port,
                                                 const int camera3_udp_port, const int camera4_udp_port) {
        int cam1Ret = h264UdpSocket_camera1.init(camera1_udp_port, CAMERA_ID_1);
        int cam2Ret = h264UdpSocket_camera2.init(camera2_udp_port, CAMERA_ID_2);
        int cam3Ret = h264UdpSocket_camera3.init(camera3_udp_port, CAMERA_ID_3);
        int cam4Ret = h264UdpSocket_camera4.init(camera4_udp_port, CAMERA_ID_4);
        return (cam1Ret == 0 && cam2Ret == 0 && cam3Ret == 0 && cam4Ret == 0) ? 0 : -1;
    }

    int PeripheralUnitManager::playSound_JKFZ(const char *soundFilePaht, bool isForce) {
        return speakerManager.tryPlaySound_JKFZ(soundFilePaht, isForce);
    }

    const SpeakerManager &PeripheralUnitManager::getSpeakerManager() const {
        return speakerManager;
    }

    const G3GPIOManager &PeripheralUnitManager::getGpioManager() const {
        return gpioManager;
    }

    const RS485OptManager &PeripheralUnitManager::getRs485OptManager() const {
        return rs485OptManager;
    }

    const Uart_MCU_Manager &PeripheralUnitManager::getUartMcuManager() const {
        return uartMcuManager;
    }

    const ConfigureServer &PeripheralUnitManager::getConfigureServer() const {
        return configureServer;
    }

    void PeripheralUnitManager::onGetGSensorData(float value_x, float value_y, float value_z) {
        callback->onGetGSensorData(value_x, value_y, value_z);
    }

    void PeripheralUnitManager::onGetADCVoltage(float voltage) {
        callback->onGetADCVoltage(voltage);
    }

    void
    PeripheralUnitManager::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
        /* 判断一下是不是V6模式 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6) {
            /* 不是V6模式，通知正常的GPIO处理和喇叭 */
            /* 输出震动 */
            gpioManager.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            /* 输出喇叭 */
            speakerManager.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);
        } else {
            /* 是V6模式，只输出给V6专用的的GPIO处理 */
            gpioManager_V6.setDetectionEvent(curCameraType, eventCode, alarmEventInfo);

        }


    }

    void PeripheralUnitManager::setOutlevel(int portNum, int level) {
        /* 判断一下是不是V6模式 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6) {
            /* 不是V6模式，通知正常的GPIO处理和喇叭 */
            gpioManager.setOutlevel(portNum, level);
        } else {
            /* 是V6模式，只输出给V6专用的的GPIO处理 */
            gpioManager_V6.setOutlevel(portNum, level);

        }
    }

    int
    PeripheralUnitManager::addRealviewUDPSocketSeparate(int cameraId, int clienPort, std::string ipAddr, int selfPort) {
        int ret = -1;
        switch (cameraId) {
            case 0: {
                ret = h264UdpSocketSeparate_camera1.addClient(clienPort, ipAddr);

            }
                break;
            case 1: {
                ret = h264UdpSocketSeparate_camera2.addClient(clienPort, ipAddr);
            }
                break;
            case 2: {
                ret = h264UdpSocketSeparate_camera3.addClient(clienPort, ipAddr);
            }
                break;
            case 3: {
                ret = h264UdpSocketSeparate_camera4.addClient(clienPort, ipAddr);
            }
                break;

        }
        return ret;
    }

    int PeripheralUnitManager::closeRealviewUDPSocketSeparate(int cameraId, int clienPort, std::string ipAddr) {
        int ret = -1;
        switch (cameraId) {
            case 0: {
                ret = h264UdpSocketSeparate_camera1.closeUPDSocket(htons(clienPort), ipAddr);

            }
                break;
            case 1: {
                ret = h264UdpSocketSeparate_camera2.closeUPDSocket(htons(clienPort), ipAddr);
            }
                break;
            case 2: {
                ret = h264UdpSocketSeparate_camera3.closeUPDSocket(htons(clienPort), ipAddr);
            }
                break;
            case 3: {
                ret = h264UdpSocketSeparate_camera4.closeUPDSocket(htons(clienPort), ipAddr);
            }
                break;
        }
        return ret;
    }

    void PeripheralUnitManager::onGetDataFromMQTT(const int mqttType, const char *topicName, const int topicNameLen,
                                                  const char *payload, const int payloadLen) {
        callback->onGetDataFromMQTT(mqttType, topicName, topicNameLen, payload, payloadLen);
    }

    int PeripheralUnitManager::sendDataToNDSMqtt(const std::string &strData) {
        return ndsMqttManager.sendDataToNDSMqtt(strData);

    }

    void PeripheralUnitManager::onGetMQTTConnectStatusChange(const bool isConnected) {
        callback->onGetMQTTConnectStatusChange(isConnected);
    }

    int PeripheralUnitManager::rtmpControl(const std::string url, const int cameraId, const int opt) {
        int ret = -1;
        switch (cameraId) {
            case CAMERA_ID_1: {
                /* 根据操作类型进行对应的动作 */
                switch (opt) {
                    case 0: {
                        if (rtmpPusher1.isInited()) {
                            rtmpPusher1.stop();
                            ret = 0;
                        }
                    }
                        break;
                    case 1: {
                        if (!rtmpPusher1.isInited()) {
                            rtmpPusher1.init(url);
                            rtmpPusher1.start();
                            ret = 0;
                        }
                    }
                }
            }
                break;

            case CAMERA_ID_2: {
                /* 根据操作类型进行对应的动作 */
                switch (opt) {
                    case 0: {
                        if (rtmpPusher2.isInited()) {
                            rtmpPusher2.stop();
                            ret = 0;
                        }
                    }
                        break;
                    case 1: {
                        if (!rtmpPusher2.isInited()) {
                            rtmpPusher2.init(url);
                            rtmpPusher2.start();
                            ret = 0;
                        }
                    }
                }

            }
                break;

            case CAMERA_ID_3: {
                /* 根据操作类型进行对应的动作 */
                switch (opt) {
                    case 0: {
                        if (rtmpPusher3.isInited()) {
                            rtmpPusher3.stop();
                            ret = 0;
                        }
                    }
                        break;
                    case 1: {
                        if (!rtmpPusher3.isInited()) {
                            rtmpPusher3.init(url);
                            rtmpPusher3.start();
                            ret = 0;
                        }
                    }
                }

            }
                break;

            case CAMERA_ID_4: {
                /* 根据操作类型进行对应的动作 */
                switch (opt) {
                    case 0: {
                        if (rtmpPusher4.isInited()) {
                            rtmpPusher4.stop();
                            ret = 0;
                        }
                    }
                        break;
                    case 1: {
                        if (!rtmpPusher4.isInited()) {
                            rtmpPusher4.init(url);
                            rtmpPusher4.start();
                            ret = 0;
                        }
                    }
                }

            }
                break;
        }


        return ret;

    }

    void PeripheralUnitManager::sendH264DataToRTMP(const int cameraId, uint8_t *h264Data, const int dataLen) {
        switch (cameraId) {
            case CAMERA_ID_1: {
                if (rtmpPusher1.isInited()) {
                    rtmpPusher1.setH264Data(h264Data, dataLen);
                }

            }
                break;

            case CAMERA_ID_2: {
                if (rtmpPusher2.isInited()) {
                    rtmpPusher2.setH264Data(h264Data, dataLen);
                }
            }
                break;
            case CAMERA_ID_3: {
                if (rtmpPusher3.isInited()) {
                    rtmpPusher3.setH264Data(h264Data, dataLen);
                }

            }
                break;

            case CAMERA_ID_4: {
                if (rtmpPusher4.isInited()) {
                    rtmpPusher4.setH264Data(h264Data, dataLen);
                }
            }
                break;
        }
    }

    bool PeripheralUnitManager::isPtm() const {
        bool ret = false;

        /* 判断一下是不是V6模式 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6) {
            /* 不是V6模式，拿正常的GPIO处理的 */
            ret = gpioManager.isPtm();
        } else {
            /* 是V6模式，拿V6专用的GPIO处理的 */
            ret = gpioManager_V6.isPtm();

        }

        return ret;
    }

    void PeripheralUnitManager::setIsPtm(bool isPtm) {
        /* 判断一下是不是V6模式 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6) {
            /* 不是V6模式，设置给正常的GPIO处理 */
            gpioManager.setIsPtm(isPtm);
        } else {
            /* 是V6模式，设置给V6专用的GPIO处理 */
            gpioManager_V6.setIsPtm(isPtm);

        }
    }

    int PeripheralUnitManager::sendDataToRS232(const uint8_t *buf, int len) {
        return rs232OptManager.sendDataToRS232(buf, len);
    }

    void PeripheralUnitManager::swichCameraIRLamp(const bool lampSwich) {
        /* 判断一下是不是V6模式 */
        if (G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6) {
            /* 不是V6模式，那么没这功能 */
            ; //not to do

        } else {
            /* 是V6模式，设置给V6专用的GPIO处理 */
            gpioManager_V6.swichCameraIRLamp(lampSwich);
        }
    }

    void PeripheralUnitManager::setRS485Baudrate(const int baudrate) {
        rs485OptManager.setRS485Baudrate(baudrate);
    }

    int PeripheralUnitManager::sendH264DataToTCP(const int cameraId, const uint8_t *buf, int len) {
        switch (cameraId) {
            case CAMERA_ID_1: {
                h264TcpServerSocket_camera1.sendH264Data(const_cast<uint8_t *>(buf), len);
            }
                break;
            case CAMERA_ID_2: {
                h264TcpServerSocket_camera2.sendH264Data(const_cast<uint8_t *>(buf), len);
            }
                break;
            case CAMERA_ID_3: {
                h264TcpServerSocket_camera3.sendH264Data(const_cast<uint8_t *>(buf), len);
            }
                break;
            case CAMERA_ID_4: {
                h264TcpServerSocket_camera4.sendH264Data(const_cast<uint8_t *>(buf), len);
            }
                break;
        }
        return 0;
    }

//    void PeripheralUnitManager::setStopAlarmButtonStatus(const bool isButtonOn) {
//        /* 目前只有喇叭和IO输出关心它 */
//        speakerManager.setStopAlarmButtonStatus(isButtonOn);
//        gpioManager.setStopAlarmButtonStatus(isButtonOn);
//    }

}