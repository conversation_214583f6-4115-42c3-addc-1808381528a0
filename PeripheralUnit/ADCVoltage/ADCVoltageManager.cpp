//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/31.
//

#include "ADCVoltageManager.h"
#include "G3_Configuration.h"

namespace vis {

    int ADCVoltageManager::init(PeripheraDataCallback &peripheraDataCallback) {
        int ret = -1;
        ctx = iio_create_local_context();
        if (nullptr == ctx) {
            ret = -1;
        }else{
            devCnt = iio_context_get_devices_count(ctx);

            // 遍历所有的iio设备  以此寻找电压传传感器
            for (unsigned int i = 0; i < devCnt; ++i) {
                dev = iio_context_get_device(ctx, i);
                name = iio_device_get_name(dev);
                printf("ADCVoltageManager   name=%s \n",name);
                if (strcmp(name, "ff5e0000.saradc") == 0) {
                    // 找到了电压传传感器
                    fined = true;
                    printf("*************ADCVoltageManager   name=%s \n",name);
                    break;
                }
            }

            // 没找到则直接退出
            if (!fined) {
                iio_context_destroy(ctx);
                ret = -1;
            }else{
                callback = &peripheraDataCallback;
                needStopADCVoltageRead = false;
                ret = 0;
            }

        }

        return ret;
    }

    void ADCVoltageManager::destroy() {
        needStopADCVoltageRead = true;
        // 程序结束理应释放ctx
        if (nullptr != ctx) {
            iio_context_destroy(ctx);
        }
    }

    void ADCVoltageManager::run() {
        std::string pthreadName = "ADCVoltage";
        pthread_setname_np(pthread_self(), pthreadName.c_str());
        int count = 0;
        float sum = 0;


        float voltage = 0;
        while (!needStopADCVoltageRead) {
            // 找到对应的ADC电压通道
            chn = iio_device_find_channel(dev, "voltage3", false);       // ADC用的是IN3 所以使用voltage3
            if (nullptr != chn) {


                // 读取通道的raw字段，读取到的是字符串表达的double
                ret = iio_channel_attr_read(chn, "raw", buf, sizeof(buf));
                if (ret >= 0) {


                    /* 判断一下是不是V6模式 */
                    if(G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6){
                        /* 不是V6模式，那么计算一下真实的电压值 */
                        voltage = 1.8 / 1024 * atof(buf) * 21;
                        callback->onGetADCVoltage(voltage);
                        sleep(5);
                    }else{
                        /* 是V6模式，那么直接拿内容出来，作为光线传感器的值 */
                        voltage = atof(buf);
                        sum += voltage;
                        count ++;
                        if(count >= 50){
                            callback->onGetADCVoltage(sum/count);
                            count = 0;
                            sum = 0;
                        }
                        usleep(100*1000);
                    }
                }
            }

        }
        pthread_setname_np(pthread_self(), "Finish");
    }

}