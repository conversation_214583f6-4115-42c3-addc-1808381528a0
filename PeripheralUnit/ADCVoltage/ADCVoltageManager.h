//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/31.
//

#ifndef VIS_G3_SOFTWARE_ADCVOLTAGEMANAGER_H
#define VIS_G3_SOFTWARE_ADCVOLTAGEMANAGER_H

#include <iio.h>
#include <Poco/Runnable.h>
#include "PeripheraDataCallback.h"
#include <cstring>
#include <cstdio>

namespace vis {

    class ADCVoltageManager : public Poco::Runnable {
    public:
        int init(PeripheraDataCallback &peripheraDataCallback);

        void destroy();

        void run() override;


    private:
        PeripheraDataCallback *callback;
        bool needStopADCVoltageRead = true;
        struct iio_context *ctx = nullptr;
        struct iio_device *dev = nullptr;
        struct iio_channel *chn = nullptr;
        char buf[20] = {0};
        int ret = 0;
        unsigned int devCnt = 0;
        const char *name = nullptr;
        bool fined = false;
        double unit = 0x7fff / 2 / 1000;        // 15bit表示2G(最高bit表示正负)，精度到mG
        char *end = nullptr;
    };

}
#endif //VIS_G3_SOFTWARE_ADCVOLTAGEMANAGER_H
