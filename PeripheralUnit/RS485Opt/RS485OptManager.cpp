//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/15.
//

#include <fcntl.h>
#include <termios.h>
#include <cstring>
#include <unistd.h>
#include "RS485OptManager.h"
#include "XuTimeUtil.h"
#include "XuString.h"

namespace vis {

    int RS485OptManager::initRS485(PeripheraDataCallback &peripheraDataCallback) {
        int ret = -1;
        callback = &peripheraDataCallback;
        rs485TtyFd = open(RS485_TTY_PATH, O_RDWR | O_NOCTTY);
        if (rs485TtyFd >= 0) {
            ret = term_set(rs485TtyFd, 1, B38400, P_NONE, 8, 1, FC_NONE, 1, 0);
            if (ret >= 0) {
                isNeedStopRS485 = false;
                rs485DataCache = static_cast<uint8_t *>(malloc(RS485_DATA_CACHE_SIZE));
                rs485Open = true;

                printf("init RS485 success  rs485TtyFd=%d   ret=%d \n", rs485TtyFd, ret);
            } else {
                printf("term_set rs485 failed!   error=%s \n", strerror(errno));
            }
        } else {
            printf("open rs485 tty %s fail", RS485_TTY_PATH);
        }
        return ret;
    }

    void RS485OptManager::run() {
        std::string pthreadName = "RS485IO";
        pthread_setname_np(pthread_self(), pthreadName.c_str());


        int rl = 0;
//    printf("========================================RS485OptManager::run======================================== \n",rl);
        while (!isNeedStopRS485) {
//        printf("========================================RS485  start read======================================== \n",rl);
            rl = read(rs485TtyFd, rs485DataCache, RS485_DATA_CACHE_SIZE);
            if (rl > 0) {
//                printf("rs485 read = %s   time=%llu\n",XuString::getInstance().byteArrayToString(rs485DataCache,rl).c_str(),XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis());
                callback->onGetDataFromRS485(rs485DataCache, rl);
            } else {
                printf("RS485  read error rl=%d\n", rl);
            }
        }
        pthread_setname_np(pthread_self(), "Finish");
    }


    int RS485OptManager::sendDataToRS485(const uint8_t *buf, const int len) {
        int ret = -1;
        if (rs485TtyFd != -1) {
            ret = write(rs485TtyFd, buf, len);
//        printf("send %d data to RS485,ret=%d     time=%llu \n content: \n",len,ret,XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis());
//        for(int i = 0; i < len; i ++){
//            printf("%02x ",buf[i]);
//        }
//        printf("\n");
        }
        return ret;
    }

    void RS485OptManager::stopRS485() {
        if (rs485TtyFd != -1) {
            isNeedStopRS485 = true;
            close(rs485TtyFd);
            free(rs485DataCache);
        }
    }

    bool RS485OptManager::isRs485Open() const {
        return rs485Open;
    }

    void RS485OptManager::setRS485Baudrate(const int baudrate) {
        int decBaudrate = B38400;
        switch (baudrate) {
            case 38400:{
                decBaudrate = B38400;
            }
                break;
            case 57600:{
                decBaudrate = B57600;
            }
                break;
            case 115200:{
                decBaudrate = B115200;
            }
                break;
        }

            term_set(rs485TtyFd, 1, decBaudrate, P_NONE, 8, 1, FC_NONE, 1, 0);




    }

}