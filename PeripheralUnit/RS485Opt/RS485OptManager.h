//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/15.
//

#ifndef VIS_G3_SOFTWARE_RS485OPTMANAGER_H
#define VIS_G3_SOFTWARE_RS485OPTMANAGER_H

#include "utils/term.h"
#include <Poco/Runnable.h>
#include "PeripheraDataCallback.h"

namespace vis {

    class RS485OptManager : public Poco::Runnable {
    public:
        /**
         * 初始化RS485对应的串口
         *
         * @return 结果     0：成功  其他：失败
         *
         * */
        int initRS485(PeripheraDataCallback &peripheraDataCallback);

        /**
         * 发送数据到485总线上
         *
         * @param buf : 数据内存的指针
         * @param len ： 数据的长度
         *
         *@return 结果    0：成功   其他：失败
         *
         * */
        int sendDataToRS485(const uint8_t *buf, const int len);

        /**
         * 关闭485通信
         *
         * */
        void stopRS485();

        /**
         * 设置RS485的波特率
         *
         * @param baudrate ： 波特率
         */
        void setRS485Baudrate(const int baudrate);

        void run() override;

        bool isRs485Open() const;

    private:
        const char *RS485_TTY_PATH = "/dev/ttyS0";
        const int RS485_DATA_CACHE_SIZE = 1024 * 1024;
        int rs485TtyFd = -1;
        bool isNeedStopRS485 = true;
        PeripheraDataCallback *callback;
        uint8_t *rs485DataCache;

        /* rs485是否打开成功 */
        bool rs485Open = false;
    };

}
#endif //VIS_G3_SOFTWARE_RS485OPTMANAGER_H
