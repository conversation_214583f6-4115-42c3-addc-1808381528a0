//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/3.
//

#ifndef VIS_G3_SOFTWARE_PERIPHERALUNITMANAGER_H
#define VIS_G3_SOFTWARE_PERIPHERALUNITMANAGER_H

#include "ConfigureOpt/ConfigureServer.h"
#include "RealViewOpt/H264UDPSocket.h"

#include "SpeakerOpt/SpeakerManager.h"
#include "GPIOOpt/G3GPIOManager.h"
#include "GPIOOpt/G3GPIOManager_V6.h"
#include "RS485Opt/RS485OptManager.h"
#include "Uart_mcu/Uart_MCU_Manager.h"
#include "Sensor/SensorManager.h"
#include "ADCVoltage/ADCVoltageManager.h"

#include "G3_Configuration.h"
#include "H264UDPSocketSeparate.h"
#include "NDSMqttManager.h"

#include "RTMPPusher.h"

#include "RS232OptManager.h"

#include "H264TCPServerSocket.h"

namespace vis {

    class PeripheralUnitManager : public PeripheraDataCallback {
    public:
        const static int TCP_COM_PORT = 6666;

        PeripheralUnitManager();

        ~PeripheralUnitManager();

        void setInterface(PeripheraDataCallback &peripheraDataCallback);

        void start();

        int sendDataToSokcet(const char *ip, const int port, const uint8_t *buf, int len);

        int sendH264DataToUDP(const int cameraId, const uint8_t *buf, int len);

        int sendH264DataToTCP(const int cameraId, const uint8_t *buf, int len);

        int sendDataToRS485(const uint8_t *buf, int len);

        int playSound(const char *soundFilePaht, bool isForce);

        int playSound_JKFZ(const char *soundFilePaht, bool isForce);


        void onGetDataFromSocket(const char *ip, const int port, const uint8_t *buf, int len) override;

        void onGetDataFromRS485(const uint8_t *buf, int len) override;

        void onGetDataFromRS232(const uint8_t *buf, int len) override;

        void onGetDataFromCANBus(const can_frame &canFrame) override;

        void onGPIOValueChange(const int portNum, const char *value, const int valueLen) override;

        void onGetDataFromUART_MCU(const uint8_t *buf, int len) override;

        int sendDataToUART_MCU(const uint8_t *buf, int len);

        void onGetGSensorData(float value_x, float value_y, float value_z) override;

        void onGetADCVoltage(float voltage) override;

        void onGetDataFromMQTT(const int mqttType, const char *topicName, const int topicNameLen, const char *payload, const int payloadLen) override;

        void onGetMQTTConnectStatusChange(const bool isConnected) override;

        /**
         * 设置从识别模块拿到的报警事件
         *
         * @param cameraId : 相机ID
         * @param detectType : 识别算法类型
         * @param eventCode ： 事件码
         *
         * */
        void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

        /**
         * 获取当前的运行状态
         *
         * @return 0：一切正常   其他：有故障
         * */
        int getWorkStaus();


        /**
         * 初始化UDP广播
         * */
        int startUPDBroadcast(const int camera1_udp_port, const int camera2_udp_port, const int camera3_udp_port, const int camera4_udp_port);

        /**
         * 设置GPIO的输出电平
         *
         * @param portNum: GPIO号
         * @param level: 电平  0：低电平   1：高电平
         *
         * */
        void setOutlevel(int portNum, int level);


        /**
         * 增加一条实景UDPsocket（单播）
         *
         * @param cameraId ： 实景得相机ID
         * @param clienPort ： 客户端得端口号
         * @param ipAddr ： 客户端得IP地址
         * @param selfPort ： 自己用的端口号
         * @return  0：成功  其他:失败
         */
        int addRealviewUDPSocketSeparate(int cameraId, int clienPort, std::string ipAddr, int selfPort);

        /**
         * 关闭一条实景UDPsocket（单播）
         * @param cameraId ：
         * @param clienPort ：
         * @param ipAddr ：
         * @return
         */
        int closeRealviewUDPSocketSeparate(int cameraId, int clienPort, std::string ipAddr);

        /**
         * 发送消息到MQTT
         *
         * @param strData ： strData消息
         *
         * @return 结果    0：成功  其他：失败
         */
        int sendDataToNDSMqtt(const std::string &strData);

        /**
         * RTMP推送控制
         *
         * @param url ： 推流地址
         * @param cameraId ： 相机ID
         * @param opt ： 操作
         *
         * @return 0:成功  其他：失败
         */
        int rtmpControl(const std::string url,const int cameraId, const int opt);

        /**
         * 通过RTMP推送一帧H264数据帧
         *
         * @param h264Data ： 一帧H264数据的指针
         * @param dataLen ： 一帧H264数据的指针的长度
         */
        void sendH264DataToRTMP(const int cameraId, uint8_t *h264Data, const int dataLen);

        /**
         * 发送数据到RS232
         *
         * @param buf ：需要发送出去的数据的指针
         * @param len ：需要发送出去的数据的长度
         * @return 发送的字节数
         */
        int sendDataToRS232(const uint8_t *buf, int len);


//        /**
//         * 设置停止报警按钮的状态
//         *
//         * @param isButtonOn ： 停止报警按钮是否被按下
//         */
//        void setStopAlarmButtonStatus(const bool isButtonOn);

        bool isPtm() const;

        void setIsPtm(bool isPtm);

        /**
         * 开关镜头的红外灯
         *
         * @param lampSwich ： 红外灯的开关  ture：打开  false：关闭
         */
        void swichCameraIRLamp(const bool lampSwich);

        /**
         * 设置RS485的波特率
         *
         * @param baudrate ： 波特率
         */
        void setRS485Baudrate(const int baudrate);


        const SpeakerManager &getSpeakerManager() const;

        const G3GPIOManager &getGpioManager() const;

        const RS485OptManager &getRs485OptManager() const;

        const Uart_MCU_Manager &getUartMcuManager() const;

        const ConfigureServer &getConfigureServer() const;



    private:
        /* Media 模块所有线程使用的线程池 */
        Poco::ThreadPool threadPool;
        ConfigureServer configureServer;
        PeripheraDataCallback *callback;
        H264UDPSocket h264UdpSocket_camera1;
        H264UDPSocket h264UdpSocket_camera2;
        H264UDPSocket h264UdpSocket_camera3;
        H264UDPSocket h264UdpSocket_camera4;

        H264UDPSocketSeparate h264UdpSocketSeparate_camera1;
        H264UDPSocketSeparate h264UdpSocketSeparate_camera2;
        H264UDPSocketSeparate h264UdpSocketSeparate_camera3;
        H264UDPSocketSeparate h264UdpSocketSeparate_camera4;

        H264TCPServerSocket h264TcpServerSocket_camera1;
        H264TCPServerSocket h264TcpServerSocket_camera2;
        H264TCPServerSocket h264TcpServerSocket_camera3;
        H264TCPServerSocket h264TcpServerSocket_camera4;



        SpeakerManager speakerManager;
        G3GPIOManager gpioManager;
        G3GPIOManager_V6 gpioManager_V6;
        RS485OptManager rs485OptManager;
        Uart_MCU_Manager uartMcuManager;
        SensorManager sensorManager;
        ADCVoltageManager adcVoltageManager;
        NDSMqttManager ndsMqttManager;

        RS232OptManager rs232OptManager;

        RTMPPusher rtmpPusher1;
        RTMPPusher rtmpPusher2;
        RTMPPusher rtmpPusher3;
        RTMPPusher rtmpPusher4;


    };

}

#endif //VIS_G3_SOFTWARE_PERIPHERALUNITMANAGER_H
