#include <cstdio>
#include <string>
#include <sys/time.h>
#include <cstring>
#include <fstream>
#include <sys/stat.h>
#include <sys/types.h>
#include <cstdint>
#include "utils/XuRGAUtils.h"

// 简化的文件保存函数
bool saveConvertedImage(uint8_t* imageData, int dataSize, int width, int height, const std::string& format) {
    // 确保/userdata目录存在
    mkdir("/userdata", 0755);

    // 生成带时间戳的文件名
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    long timestamp = tv.tv_sec * 1000 + tv.tv_usec / 1000;

    std::string filename = "/userdata/converted_" + std::to_string(width) + "x" + std::to_string(height) +
                          "_" + std::to_string(timestamp) + "." + format;

    // 使用标准C++文件操作保存
    std::ofstream file(filename, std::ios::binary);
    if (file.is_open()) {
        file.write(reinterpret_cast<const char*>(imageData), dataSize);
        file.close();

        printf("图像转换并保存成功: %s, 尺寸: %dx%d, 大小: %d bytes\n",
               filename.c_str(), width, height, dataSize);
        return true;
    } else {
        printf("图像保存失败: %s\n", filename.c_str());
        return false;
    }
}

// 创建测试图案的函数
void createTestPattern(uint8_t* yuvBuffer, int width, int height) {
    int ySize = width * height;
    int uvSize = ySize / 2;
    
    // Y分量：创建渐变图案
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int index = y * width + x;
            // 创建水平渐变
            yuvBuffer[index] = (uint8_t)((x * 255) / width);
        }
    }
    
    // UV分量：设置为中性值（128）
    memset(yuvBuffer + ySize, 128, uvSize);
}

// 完善的图像转换函数
int performImageTransformation(int srcWidth, int srcHeight, int srcFormat, 
                              int dstWidth, int dstHeight, int dstFormat) {
    printf("=== 开始图像转换 ===\n");
    printf("源图像: %dx%d, 格式: %d\n", srcWidth, srcHeight, srcFormat);
    printf("目标图像: %dx%d, 格式: %d\n", dstWidth, dstHeight, dstFormat);
    
    // 创建XuRGAUtils实例
    XuRGAUtils rgaUtils;
    
    // 计算缓冲区大小
    int srcDataSize, dstDataSize;
    
    // 根据格式计算源图像数据大小
    if (srcFormat == XuRGAUtils::IMG_TYPE_NV12 || srcFormat == XuRGAUtils::IMG_TYPE_NV21) {
        srcDataSize = srcWidth * srcHeight * 3 / 2;  // YUV420格式
    } else if (srcFormat == XuRGAUtils::IMG_TYPE_RGB888 || srcFormat == XuRGAUtils::IMG_TYPE_BGR888) {
        srcDataSize = srcWidth * srcHeight * 3;      // RGB888格式
    } else if (srcFormat == XuRGAUtils::IMG_TYPE_RGBA8888 || srcFormat == XuRGAUtils::IMG_TYPE_BGRA8888) {
        srcDataSize = srcWidth * srcHeight * 4;      // RGBA8888格式
    } else {
        printf("不支持的源图像格式: %d\n", srcFormat);
        return -1;
    }
    
    // 根据格式计算目标图像数据大小
    if (dstFormat == XuRGAUtils::IMG_TYPE_NV12 || dstFormat == XuRGAUtils::IMG_TYPE_NV21) {
        dstDataSize = dstWidth * dstHeight * 3 / 2;  // YUV420格式
    } else if (dstFormat == XuRGAUtils::IMG_TYPE_RGB888 || dstFormat == XuRGAUtils::IMG_TYPE_BGR888) {
        dstDataSize = dstWidth * dstHeight * 3;      // RGB888格式
    } else if (dstFormat == XuRGAUtils::IMG_TYPE_RGBA8888 || dstFormat == XuRGAUtils::IMG_TYPE_BGRA8888) {
        dstDataSize = dstWidth * dstHeight * 4;      // RGBA8888格式
    } else {
        printf("不支持的目标图像格式: %d\n", dstFormat);
        return -1;
    }
    
    // 分配缓冲区
    uint8_t* srcBuffer = new uint8_t[srcDataSize];
    uint8_t* dstBuffer = new uint8_t[dstDataSize];
    
    if (!srcBuffer || !dstBuffer) {
        printf("内存分配失败\n");
        delete[] srcBuffer;
        delete[] dstBuffer;
        return -1;
    }
    
    // 创建测试图案
    if (srcFormat == XuRGAUtils::IMG_TYPE_NV21 || srcFormat == XuRGAUtils::IMG_TYPE_NV12) {
        createTestPattern(srcBuffer, srcWidth, srcHeight);
        printf("已创建YUV测试图案\n");
    } else {
        // 对于其他格式，填充简单的测试数据
        memset(srcBuffer, 128, srcDataSize);
        printf("已创建简单测试图案\n");
    }
    
    // 执行图像格式转换
    printf("正在执行RGA图像转换...\n");
    int convertRet = rgaUtils.imageTransformation(
        srcWidth, srcHeight, srcFormat, srcBuffer,    // 源图像
        dstWidth, dstHeight, dstFormat, dstBuffer     // 目标图像
    );
    
    if (convertRet > 0) {
        printf("图像转换成功！输出数据大小: %d bytes (预期: %d bytes)\n", convertRet, dstDataSize);
        
        // 确定文件扩展名
        std::string fileExt;
        if (dstFormat == XuRGAUtils::IMG_TYPE_NV12 || dstFormat == XuRGAUtils::IMG_TYPE_NV21) {
            fileExt = "yuv";
        } else if (dstFormat == XuRGAUtils::IMG_TYPE_RGB888) {
            fileExt = "rgb";
        } else if (dstFormat == XuRGAUtils::IMG_TYPE_BGR888) {
            fileExt = "bgr";
        } else if (dstFormat == XuRGAUtils::IMG_TYPE_RGBA8888) {
            fileExt = "rgba";
        } else if (dstFormat == XuRGAUtils::IMG_TYPE_BGRA8888) {
            fileExt = "bgra";
        } else {
            fileExt = "raw";
        }
        
        // 保存转换后的图像
        if (saveConvertedImage(dstBuffer, convertRet, dstWidth, dstHeight, fileExt)) {
            printf("图像已成功保存到 /userdata/ 目录\n");
        }
    } else {
        printf("图像转换失败，错误码: %d\n", convertRet);
    }
    
    // 释放内存
    delete[] srcBuffer;
    delete[] dstBuffer;
    
    printf("=== 图像转换完成 ===\n\n");
    return convertRet;
}

int main() {
    printf("图像转换示例程序\n");
    printf("功能：使用XuRGAUtils进行图像格式转换并保存到/userdata/目录\n\n");
    
    // 示例1：YUV420SP转RGBA8888 (1280x720)
    printf("示例1: YUV420SP -> RGBA8888\n");
    performImageTransformation(1280, 720, XuRGAUtils::IMG_TYPE_NV21, 
                              1280, 720, XuRGAUtils::IMG_TYPE_RGBA8888);
    
    // 示例2：YUV420SP转RGB888 (缩放到640x480)
    printf("示例2: YUV420SP -> RGB888 (缩放)\n");
    performImageTransformation(1280, 720, XuRGAUtils::IMG_TYPE_NV21, 
                              640, 480, XuRGAUtils::IMG_TYPE_RGB888);
    
    // 示例3：YUV420SP转RGBA8888 (缩放到720x576)
    printf("示例3: YUV420SP -> RGBA8888 (缩放到720x576)\n");
    performImageTransformation(1280, 720, XuRGAUtils::IMG_TYPE_NV21, 
                              720, 576, XuRGAUtils::IMG_TYPE_RGBA8888);
    
    printf("所有转换示例已完成！\n");
    printf("请检查 /userdata/ 目录中的转换结果文件。\n");
    
    return 0;
}
