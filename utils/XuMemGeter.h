//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/2/27.
//

#ifndef MRV440SOFTWARE_XUMEMGETER_H
#define MRV440SOFTWARE_XUMEMGETER_H

#include <cstdio>
#include <cstdint>
#include <memory>


/**
 * MRV440的内存开辟工具类，这个工具类可以自动在作用域外释放内存，避免泄露
 */
namespace VIS {

template <class T>
class XuMemGeter {
public:

    /**
     * 不开辟内存的构造函数
     */
    XuMemGeter(){

    };

    /**
     * 构造函数，在这里就开辟好内存了
     *
     * @param size ： 需要开辟多大的内存
     */
    XuMemGeter(std::size_t size){
        dataPtr = new T[size];
        std::shared_ptr<T> managedData(dataPtr, [](T* d) { delete[] d; });
        sharedPtr = managedData;
        ptrLen = size;
    };

    /**
     * 手动初始化这个工具类来开辟内存（如果不使用构造函数来开辟的话就可以选择这个函数来开辟）
     *
     * @param size ： 需要开辟多大的内存
     *
     * @return 是否初始化成功
     */
    bool init(std::size_t size){
        bool ret = false;
        if(dataPtr == nullptr){
            dataPtr = new T[size];
            std::shared_ptr<T> managedData(dataPtr, [](T* d) { delete[] d; });
            sharedPtr = managedData;
            ptrLen = size;
            ret = true;
        }else{
            ret = false;
        }

        return ret;
    };


    /**
     * 获取开辟好的内存的指针
     *
     * @return 开辟好的内存的指针
     */
    T* getPtr(){
        return sharedPtr.get();
    };

    /**
     * 获取指针的长度
     *
     * @return 指针长度
     */
    std::size_t getSize(){
        return ptrLen;
    }

    /**
     * 判断是否为空（因为可能会开辟失败）
     *
     * @return 是否为空
     */
    bool empty(){
        bool ret = true;
        if(sharedPtr == nullptr || ptrLen <= 0){
            ret = false;
        }
        return ret;
    };


private:
    /* 用来管理开辟的内存的智能指针 */
    std::shared_ptr<T> sharedPtr;
    /* 原始指针 */
    T *dataPtr = nullptr;
    /* 指针的长度 */
    std::size_t ptrLen = 0;

};

} // VIS

#endif //MRV440SOFTWARE_XUMEMGETER_H
