//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/2/28.
//

#include "Ping.h"

uint16_t Ping::cal_chksum(uint16_t *addr, int len) {
    int nleft=len;
    int sum=0;
    uint16_t *w=addr;
    uint16_t answer=0;

    while(nleft > 1)
    {
        sum += *w++;
        nleft -= 2;
    }

    if( nleft == 1)
    {
        *(uint8_t *)(&answer) = *(uint8_t *)w;
        sum += answer;
    }

    sum = (sum >> 16) + (sum & 0xffff);
    sum += (sum >> 16);
    answer = ~sum;

    return answer;
}

bool Ping::ping(char *address, int timeout, bool usedHostname) {
    try{
        struct timeval *tval;
        int maxfds = 0;
        fd_set readfds;

        struct sockaddr_in addr;
        struct sockaddr_in from;
        // 设定Ip信息
        bzero(&addr,sizeof(addr));
        addr.sin_family = AF_INET;
        if(usedHostname){
            struct hostent* h = gethostbyname(address);                         //存放服务器端IP的结构体
            if(!h){
                return false;
            }
            printf("*******h_name=%s   h_addrtype=%d   h_length=%d    h_addr=%s ******\n",h->h_name,h->h_addrtype,h->h_length,h->h_addr);
            addr.sin_addr = *((struct in_addr*)h->h_addr); // 复制IP地址
        }else{
            addr.sin_addr.s_addr = inet_addr(address);
        }
        int sockfd;
        // 取得socket  。  如果没加sudo 这里会报错
        sockfd = socket(AF_INET, SOCK_RAW, IPPROTO_ICMP);
        if (sockfd < 0)
        {
            printf("address:%s,socket error\n",address);
            return false;
        }
        struct timeval timeo;
        // 设定TimeOut时间
        timeo.tv_sec = timeout / 1000;
        timeo.tv_usec = timeout % 1000;
        if (setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &timeo, sizeof(timeo)) == -1)
        {
            printf("address:%s,setsockopt error\n",address

            );
            return false;
        }
        char sendpacket[PACKET_SIZE];
        char recvpacket[PACKET_SIZE];
        // 设定Ping包
        memset(sendpacket, 0, sizeof(sendpacket));
        pid_t pid;
        // 取得PID，作为Ping的Sequence ID
        pid=getpid();
        struct ip *iph;
        struct icmp *icmp;
        icmp=(struct icmp*)sendpacket;
        icmp->icmp_type=ICMP_ECHO;  //回显请求
        icmp->icmp_code=0;
        icmp->icmp_cksum=0;
        icmp->icmp_seq=0;
        icmp->icmp_id=pid;
        tval= (struct timeval *)icmp->icmp_data;
        gettimeofday(tval,NULL);
        icmp->icmp_cksum=cal_chksum((unsigned short *)icmp,sizeof(struct icmp));  //校验
        int n;
        // 发包 。可以把这个发包挪到循环里面去。
        n = sendto(sockfd, (char *)&sendpacket, sizeof(struct icmp), 0, (struct sockaddr *)&addr, sizeof(addr));
        if (n < 1)
        {
            printf("address:%s,sendto error\n",address);
            return false;
        }

        // 接受
        // 由于可能接受到其他Ping的应答消息，所以这里要用循环
        while(1)
        {
            // 设定TimeOut时间，这次才是真正起作用的
            FD_ZERO(&readfds);
            FD_SET(sockfd, &readfds);
            maxfds = sockfd + 1;
            n = select(maxfds, &readfds, NULL, NULL, &timeo);
            if (n <= 0)
            {
                printf("address:%s,Time out error\n",address);
                close(sockfd);
                return false;
            }
            // 接受
            memset(recvpacket, 0, sizeof(recvpacket));
            int fromlen = sizeof(from);
            n = recvfrom(sockfd, recvpacket, sizeof(recvpacket), 0, (struct sockaddr *)&from, (socklen_t *)&fromlen);
            printf("recvfrom Len:%d\n",n);
            if (n < 1)
            {
                return false;
            }
            char *from_ip = (char *)inet_ntoa(from.sin_addr);
            char *dec_ips = (char *)inet_ntoa(addr.sin_addr);
            // 判断是否是自己Ping的回复
            if (strcmp(from_ip,dec_ips) != 0)
            {
                printf("NowPingip:%s Fromip:%s NowPingip is not same to Fromip,so ping wrong!\n",dec_ips,from_ip);
                return false;
            }
            iph = (struct ip *)recvpacket;
            icmp=(struct icmp *)(recvpacket + (iph->ip_hl<<2));
            printf("address:%s,icmp->icmp_type:%d,icmp->icmp_id:%d\n",address,icmp->icmp_type,icmp->icmp_id);
            // 判断Ping回复包的状态
            if (icmp->icmp_type == ICMP_ECHOREPLY && icmp->icmp_id == pid)   //ICMP_ECHOREPLY回显应答
            {
                // 正常就退出循环
                printf("icmp succecss .............  \n");
                break;
            }
            else
            {
                // 否则继续等
                continue;
            }
        }
        close(sockfd);
    } catch (...) {
        printf("ping error! \n");
    }
    return true;
}
