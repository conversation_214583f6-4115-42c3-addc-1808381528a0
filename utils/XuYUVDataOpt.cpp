//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/28.
//

#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <iostream>
#include <valarray>
#include "XuYUVDataOpt.h"


int XuYUVDataOpt::drawToNV21(int p1x, int p1y, int p2x, int p2y, int p3x, int p3y, int p4x, int p4y, const char *str,
                             int width, int height, uint8_t *nv21data, int colorid, int type) {


    int result = -1;

    if (!drawpoints) {
        drawpoints = static_cast<YUVDataOpt_Point *>(malloc(width * height));
    }

    int drawPointSize = 0;
    switch (type) {
        case TYPE_ONLY_TEXT: {
            if (str != nullptr) {
                //直接画文字
                drawPointSize = _AddStr(width, height, p1x, p1y, str, FONT_WIDTH, FONT_HEIGHT, 0);
            }

        }
            break;
        case TYPE_SOLID_LINE: {
            if (str != nullptr) {
                //直接画文字
                drawPointSize = _AddStr(width, height, p1x, p1y, str, FONT_WIDTH, FONT_HEIGHT, 0);
            }
            //画实线
            YUVDataOpt_DrawLineInfo drawLineInfo;
            drawLineInfo.lineWidth = 5;
            drawLineInfo.startPoint.x = p1x;
            drawLineInfo.startPoint.y = p1y;
            drawLineInfo.endPoint.x = p2x;
            drawLineInfo.endPoint.y = p2y;
            drawLineInfo.isFull = 1;
            drawPointSize = yuv_drawline(width, height, &drawLineInfo,
                                         drawPointSize);

        }
            break;

        case TYPE_DASHED_LINE: {
            if (str != nullptr) {
                //直接画文字
                drawPointSize = _AddStr(width, height, p1x, p1y, str, FONT_WIDTH, FONT_HEIGHT, 0);
            }
            //画虚线
            YUVDataOpt_DrawLineInfo drawLineInfo;
            drawLineInfo.lineWidth = 5;
            drawLineInfo.startPoint.x = p1x;
            drawLineInfo.startPoint.y = p1y;
            drawLineInfo.endPoint.x = p2x;
            drawLineInfo.endPoint.y = p2y;
            drawLineInfo.isFull = 0;
            drawPointSize = yuv_drawline(width, height, &drawLineInfo,
                                         drawPointSize);

        }
            break;

        case TYPE_RECTANGLE: {
            if (str != nullptr) {
                //直接画文字
                drawPointSize = _AddStr(width, height, p1x, p1y, str, FONT_WIDTH, FONT_HEIGHT, 0);
            }
            //画框
            drawPointSize = draw_rect(p1x, p1y, p2x, p2y, width, height,
                                      drawPointSize,5);

        }
            break;

        case TYPE_ONLY_POINT: {
            //画点
            drawPointSize = yuv_drawPoint(p1x, p1y, p2x, p2y,
                                          drawPointSize);

        }
            break;

        case TYPE_QUADRILATERAL: {
            //画四边形
            drawPointSize = draw_quadrilateral(p1x, p1y, p2x, p2y, p3x, p3y, p4x, p4y,
                                               width, height, drawPointSize,5);

        }
        case TYPE_POLYGON: {
            //画多边形
            drawPointSize = draw_quadrilateral(p1x, p1y, p2x, p2y, p3x, p3y, p4x, p4y,
                                               width, height, drawPointSize,5);

        }
            break;
    }

    uint8_t *YBuff = nv21data;
    uint8_t *UVBuff = nv21data + (width * height);
    for (int i = 0; i < drawPointSize; i++) {
        yuv_setdata(YBuff, UVBuff, width, height, drawpoints[i], colorid);
    }
    return result;
}

unsigned int

XuYUVDataOpt::_AddChar(unsigned int width, unsigned int height, unsigned int x, unsigned int y, const unsigned char c,
                       unsigned int fontHeight, unsigned int fontWidth, unsigned int indexListoffset) {
    unsigned int point = y * width + x;
    unsigned char *cPoint;      //  = ascii + (fontHeight * fontWidth * c / 8);
    unsigned int widthByteCount;
    int i, j, k;

    // 如果指定位置剩余的长度或宽度不足字体的长宽，则退出
    if (x + fontWidth > width || y + fontHeight > height) {
        return indexListoffset;
    }

    // 计算参数c在字库的位置
    widthByteCount = fontWidth % 8 == 0 ? fontWidth / 8 : fontWidth / 8 + 1;
    cPoint = asciiPoint + fontHeight * widthByteCount * c;

    // 开始遍历字库中指定的字符
    for (i = 0; i < (int) fontHeight; ++i) {
        for (j = 0, k = 0; j < (int) fontWidth; ++j, k++) {
            if (k > 7) {
                k = 0;
                cPoint++;
            }
            if (1 == ((*cPoint >> k) & 1)) {
                // 下标 = point - img->addr;
                drawpoints[indexListoffset].y = point / width;
                drawpoints[indexListoffset].x = point
                                                - (drawpoints[indexListoffset].y * width);
                indexListoffset++;
//                LOGE("point:%d    indexListoffset:%d",point,indexListoffset);
            }
            point++;
        }
        cPoint++;
        point += width - fontWidth;
    }

    return indexListoffset;
}

int
XuYUVDataOpt::_AddStr(unsigned int width, unsigned int height, unsigned int x, unsigned int y, const char *str,
                      unsigned int fontHeight, unsigned int fontWidth, unsigned int indexListOffset) {
    unsigned int strLen = 0;
    unsigned int i;

    if (nullptr == str) {
        return -1;
    }

    strLen = strlen(str);

    for (i = 0; i < strLen; ++i) {
        indexListOffset = _AddChar(width, height, x, y, str[i], fontHeight,
                                   fontWidth, indexListOffset);
        x += fontWidth;
    }
    return indexListOffset;
}

int XuYUVDataOpt::yuv_drawPoint(int startX, int startY, int endX, int endY, int poinoffset) {
    drawpoints[poinoffset].x = startX;
    drawpoints[poinoffset].y = startY;
    poinoffset++;
    return poinoffset;
}

int XuYUVDataOpt::yuv_drawline(int width, int height, YUVDataOpt_DrawLineInfo *pDrawLineInfo, int poinoffset) {
    uint16_t x0 = pDrawLineInfo->startPoint.x, y0 = pDrawLineInfo->startPoint.y;
    uint16_t x1 = pDrawLineInfo->endPoint.x, y1 = pDrawLineInfo->endPoint.y;
    if(pDrawLineInfo->startPoint.x == 0 && pDrawLineInfo->startPoint.y == 0 && pDrawLineInfo->endPoint.x == 0 && pDrawLineInfo->endPoint.y == 0){
        return poinoffset;
    }
    uint8_t isFull = pDrawLineInfo->isFull;
    /* 先防止宽度没设置 */
    if(pDrawLineInfo->lineWidth <= 0){
        pDrawLineInfo->lineWidth = 1;
    }
    /* 画了多大的宽度 */
    int drawedWidth = 0;
    do{
        /* 如果已经画过真正的线，那么就需要来画扩展的线了 */
        if(drawedWidth > 0){
            /* 目前是以原始线段作为起点往一个方向加宽 */
                /* 得出start延长出来的点 */
                YUVDataOpt_Point newPonitEndLeft = getPointOfIntersectingLine_Vertical(pDrawLineInfo->startPoint,pDrawLineInfo->endPoint,pDrawLineInfo->startPoint,drawedWidth,1);
                x0 = newPonitEndLeft.x;
                y0 = newPonitEndLeft.y;
                /* 得出end延长出来的点 */
                YUVDataOpt_Point newPonitEndRight = getPointOfIntersectingLine_Vertical(pDrawLineInfo->startPoint,pDrawLineInfo->endPoint,pDrawLineInfo->endPoint,drawedWidth,1);
                x1 = newPonitEndRight.x;
                y1 = newPonitEndRight.y;
//            printf("start=(%d,%d)  end=(%d,%d)  Left=(%d,%d)   Right=(%d,%d)   \n",pDrawLineInfo->startPoint.x,pDrawLineInfo->startPoint.y,pDrawLineInfo->endPoint.x,pDrawLineInfo->endPoint.y,x0,y0,x1,y1);
        }
        x0 = (x0 > width) ? width : (x0 < 0 ? 0 : x0);
        x1 = (x1 > width) ? width : (x1 < 0 ? 0 : x1);
        y0 = (y0 > height) ? height : (y0 < 0 ? 0 : y0);
        y1 = (y1 > height) ? height : (y1 < 0 ? 0 : y1);
        uint16_t dx = (x0 > x1) ? (x0 - x1) : (x1 - x0);
        uint16_t dy = (y0 > y1) ? (y0 - y1) : (y1 - y0);
        int16_t xstep = (x0 < x1) ? 1 : -1;
        int16_t ystep = (y0 < y1) ? 1 : -1;
        int16_t nstep = 0, eps = 0;
        YUVDataOpt_Point draw_point;
        draw_point.x = x0;
        draw_point.y = y0;
        int needToJump = 0;
        int drawSum = 0;
        int notDrawSum = 0;
        // 布雷森汉姆算法画线
        if (dx > dy) {
            while (nstep <= dx) {
                eps += dy;
                if ((eps << 1) >= dx) {
                    draw_point.y += ystep;
                    eps -= dx;
                }
                draw_point.x += xstep;
                if (isFull == 0) {
                    if (needToJump == 0) {
                        drawpoints[poinoffset].y = draw_point.y;
                        drawpoints[poinoffset].x = draw_point.x;
                        poinoffset++;
                        if ((drawSum != 0) && (drawSum % 6) == 0) {
                            needToJump = 1;
                            drawSum = 0;
                        } else {
                            drawSum++;
                        }
                    } else {
                        if ((notDrawSum != 0) && (notDrawSum % 18) == 0) {
                            needToJump = 0;
                            notDrawSum = 0;
                        } else {
                            notDrawSum++;
                        }
                    }
                } else {
                    drawpoints[poinoffset].y = draw_point.y;
                    drawpoints[poinoffset].x = draw_point.x;
                    poinoffset++;
                }
                nstep++;
            }
        } else {
            while (nstep <= dy) {
                eps += dx;
                if ((eps << 1) >= dy) {
                    draw_point.x += xstep;
                    eps -= dy;
                }
                draw_point.y += ystep;
                if (isFull == 0) {
                    if (needToJump == 0) {
                        drawpoints[poinoffset].y = draw_point.y;
                        drawpoints[poinoffset].x = draw_point.x;
                        poinoffset++;
                        if ((drawSum != 0) && (drawSum % 6) == 0) {
                            needToJump = 1;
                            drawSum = 0;
                        } else {
                            drawSum++;
                        }
                    } else {
                        if ((notDrawSum != 0) && (notDrawSum % 18) == 0) {
                            needToJump = 0;
                            notDrawSum = 0;
                        } else {
                            notDrawSum++;
                        }
                    }
                } else {
                    drawpoints[poinoffset].y = draw_point.y;
                    drawpoints[poinoffset].x = draw_point.x;
                    poinoffset++;
                }
                nstep++;
            }
        }
        drawedWidth ++;
    } while (drawedWidth < pDrawLineInfo->lineWidth);

//    x0 = (x0 > width) ? width : (x0 < 0 ? 0 : x0);
//    x1 = (x1 > width) ? width : (x1 < 0 ? 0 : x1);
//    y0 = (y0 > height) ? height : (y0 < 0 ? 0 : y0);
//    y1 = (y1 > height) ? height : (y1 < 0 ? 0 : y1);
//    uint16_t dx = (x0 > x1) ? (x0 - x1) : (x1 - x0);
//    uint16_t dy = (y0 > y1) ? (y0 - y1) : (y1 - y0);
//    int16_t xstep = (x0 < x1) ? 1 : -1;
//    int16_t ystep = (y0 < y1) ? 1 : -1;
//    int16_t nstep = 0, eps = 0;
//    YUVDataOpt_Point draw_point;
//    draw_point.x = x0;
//    draw_point.y = y0;
//    int needToJump = 0;
//    int drawSum = 0;
//    int notDrawSum = 0;
//    // 布雷森汉姆算法画线
//    if (dx > dy) {
//        while (nstep <= dx) {
//            eps += dy;
//            if ((eps << 1) >= dx) {
//                draw_point.y += ystep;
//                eps -= dx;
//            }
//            draw_point.x += xstep;
//            if (isFull == 0) {
//                if (needToJump == 0) {
//                    drawpoints[poinoffset].y = draw_point.y;
//                    drawpoints[poinoffset].x = draw_point.x;
//                    poinoffset++;
//                    if ((drawSum != 0) && (drawSum % 4) == 0) {
//                        needToJump = 1;
//                        drawSum = 0;
//                    } else {
//                        drawSum++;
//                    }
//                } else {
//                    if ((notDrawSum != 0) && (notDrawSum % 4) == 0) {
//                        needToJump = 0;
//                        notDrawSum = 0;
//                    } else {
//                        notDrawSum++;
//                    }
//                }
//            } else {
//                drawpoints[poinoffset].y = draw_point.y;
//                drawpoints[poinoffset].x = draw_point.x;
//                poinoffset++;
//            }
//            nstep++;
//        }
//    } else {
//        while (nstep <= dy) {
//            eps += dx;
//            if ((eps << 1) >= dy) {
//                draw_point.x += xstep;
//                eps -= dy;
//            }
//            draw_point.y += ystep;
//            if (isFull == 0) {
//                if (needToJump == 0) {
//                    drawpoints[poinoffset].y = draw_point.y;
//                    drawpoints[poinoffset].x = draw_point.x;
//                    poinoffset++;
//                    if ((drawSum != 0) && (drawSum % 4) == 0) {
//                        needToJump = 1;
//                        drawSum = 0;
//                    } else {
//                        drawSum++;
//                    }
//                } else {
//                    if ((notDrawSum != 0) && (notDrawSum % 4) == 0) {
//                        needToJump = 0;
//                        notDrawSum = 0;
//                    } else {
//                        notDrawSum++;
//                    }
//                }
//            } else {
//                drawpoints[poinoffset].y = draw_point.y;
//                drawpoints[poinoffset].x = draw_point.x;
//                poinoffset++;
//            }
//            nstep++;
//        }
//    }




    return poinoffset;
}

int XuYUVDataOpt::draw_rect(int startX, int startY, int endX, int endY, int width, int height, int porintOffset, int lineWidth) {
    //矩形上面的横线
    YUVDataOpt_DrawLineInfo drawLineInfo;
    drawLineInfo.lineWidth = lineWidth;
    drawLineInfo.startPoint.x = startX;
    drawLineInfo.startPoint.y = startY;
    drawLineInfo.endPoint.x = endX;
    drawLineInfo.endPoint.y = startY;
    drawLineInfo.isFull = 1;
    porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);
    //矩形下面的横线
    drawLineInfo.lineWidth = lineWidth;
    drawLineInfo.startPoint.x = startX;
    drawLineInfo.startPoint.y = endY;
    drawLineInfo.endPoint.x = endX;
    drawLineInfo.endPoint.y = endY;
    drawLineInfo.isFull = 1;
    porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);
    //矩形左边的竖线
    drawLineInfo.lineWidth = lineWidth;
    drawLineInfo.startPoint.x = startX;
    drawLineInfo.startPoint.y = startY;
    drawLineInfo.endPoint.x = startX;
    drawLineInfo.endPoint.y = endY;
    drawLineInfo.isFull = 1;
    porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);
    //矩形右边的竖线
    drawLineInfo.lineWidth = lineWidth;
    drawLineInfo.startPoint.x = endX;
    drawLineInfo.startPoint.y = startY;
    drawLineInfo.endPoint.x = endX;
    drawLineInfo.endPoint.y = endY;
    drawLineInfo.isFull = 1;
    porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);
    return porintOffset;
}

int XuYUVDataOpt::draw_quadrilateral(int p1x, int p1y, int p2x, int p2y, int p3x, int p3y, int p4x, int p4y, int width,
                                     int height, int porintOffset, int lineWidth) {
    //四边形的第一条线
    YUVDataOpt_DrawLineInfo drawLineInfo;
    drawLineInfo.lineWidth = lineWidth;
    drawLineInfo.startPoint.x = p1x;
    drawLineInfo.startPoint.y = p1y;
    drawLineInfo.endPoint.x = p2x;
    drawLineInfo.endPoint.y = p2y;
    drawLineInfo.isFull = 1;
    porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);
    //四边形的第二条线
    drawLineInfo.lineWidth = lineWidth;
    drawLineInfo.startPoint.x = p2x;
    drawLineInfo.startPoint.y = p2y;
    drawLineInfo.endPoint.x = p3x;
    drawLineInfo.endPoint.y = p3y;
    drawLineInfo.isFull = 1;
    porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);
    //四边形的第三条线
    drawLineInfo.lineWidth = lineWidth;
    drawLineInfo.startPoint.x = p3x;
    drawLineInfo.startPoint.y = p3y;
    drawLineInfo.endPoint.x = p4x;
    drawLineInfo.endPoint.y = p4y;
    drawLineInfo.isFull = 1;
    porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);
    //四边形的第四条线
    drawLineInfo.lineWidth = lineWidth;
    drawLineInfo.startPoint.x = p4x;
    drawLineInfo.startPoint.y = p4y;
    drawLineInfo.endPoint.x = p1x;
    drawLineInfo.endPoint.y = p1y;
    drawLineInfo.isFull = 1;
    porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);

    return porintOffset;
}

int XuYUVDataOpt::displayRectangle(int resultLen, int startX, int startY, int endX, int endY, int width, int height,
                                   int *indexList) {
    if ((startX > width) || (startY > height) || (startX < 0) || (startX < 0)) {
        return -1;
    }

    if ((endX > width) || (endY > height) || (endX < 0) || (endY < 0)) {
        return -2;
    }
    int i;

    //长方形下面的横线
    int bottomStartIndexx = endY * width + startX; //长方形下面的横线第一个需要开始改变颜色的点
    for (i = 0; i < (endX - startX); i++) {
        int curIndex = bottomStartIndexx + i;
//			LOGE("DisplayRectangle curIndex:%d resultLen:%d", curIndex, resultLen);
        indexList[resultLen] = curIndex;
        resultLen++;
    }

    //长方形上面的横线
    int topStartIndex = startY * width + startX; //长方形上面的横线第一个需要开始改变颜色的点
    for (i = 0; i < (endX - startX); i++) {
        int curIndex = topStartIndex + i;
        //		LOGE("DisplayRectangle curIndex:%d resultLen:%d", curIndex, resultLen);
        indexList[resultLen] = curIndex;
        resultLen++;
    }

    //长方形左边的竖线
    for (i = startY; i <= endY; i++) {
        int curIndex = i * width + startX;
        //		LOGE("DisplayRectangle curIndex:%d resultLen:%d", curIndex, resultLen);
        indexList[resultLen] = curIndex;
        resultLen++;
    }

    //长方形右边的竖线
    for (i = startY; i <= endY; i++) {
        int curIndex = i * width + endX;
        //		LOGE("DisplayRectangle curIndex:%d resultLen:%d", curIndex, resultLen);
        indexList[resultLen] = curIndex;
        resultLen++;
    }

    return resultLen;
}

void XuYUVDataOpt::yuv_setdata(uint8_t *YBuff, uint8_t *UVBuff, uint16_t width,
                               uint16_t height, YUVDataOpt_Point &draw_point, int colorid) {
    {
        /*
         YY YY
         YY YY
         UV UV
         */
        uint32_t y_offset = draw_point.y * width + draw_point.x;
        uint32_t u_offset = 0, v_offset = 0;

#if 0
        Int32 x_flag = 1, y_flag = 1;
        if(draw_point.y % 2 == 0) {
            YBuff[y_offset + width] = s_color_table[colorid].Y;
            y_flag = 1;
        }
        else {
            YBuff[y_offset - width] = s_color_table[colorid].Y;
            y_flag = -1;
        }

        if(draw_point.x % 2 == 0) {
            YBuff[y_offset + 1] = s_color_table[colorid].Y;
            x_flag = 1;
        }
        else {
            YBuff[y_offset - 1] = s_color_table[colorid].Y;
            x_flag = -1;
        }
        YBuff[y_offset + width * y_flag + 1 * x_flag] = s_color_table[colorid].Y;
#endif

        u_offset = (draw_point.y / 2) * width + draw_point.x / 2 * 2;
        v_offset = u_offset + 1;


        if (y_offset <= width * height) {
            YBuff[y_offset] = s_color_table[colorid].Y;
        }

        if (u_offset <= (width * height * 3 / 2) && v_offset <= (width * height * 3 / 2)) {
            UVBuff[u_offset] = s_color_table[colorid].U;
            UVBuff[v_offset] = s_color_table[colorid].V;
        }


//        printf("[%d, %d]: y_offset = %d, u_offset = %d, v_offset = %d   colorid = %d \n",draw_point.x, draw_point.y, y_offset, u_offset, v_offset,colorid);
    }
}


int XuYUVDataOpt::drawToNV21(const YUVDataOpt_Point *pointList, int len, const char *str, int width, int height,
                             uint8_t *nv21data, int colorid, int type, int lineWidth) {
    int result = -1;

    if (len > 0) {
        if (drawpoints == nullptr) {
            drawpoints = static_cast<YUVDataOpt_Point *>(malloc(width * height));

            for(int i = 0; i < (width * height); i ++){
                /* 由于结构体里面没有裸指针，故直接用={}重置就好了 */
                drawpoints[i] = {};
            }
        }



        int drawPointSize = 0;
        switch (type) {
            case TYPE_ONLY_TEXT: {
                if (str != nullptr) {
                    //直接画文字
                    drawPointSize = _AddStr(width, height, pointList[0].x, pointList[0].y, str, FONT_WIDTH, FONT_HEIGHT,
                                            0);
                }
            }
                break;
            case TYPE_SOLID_LINE: {
                if (str != nullptr) {
                    //直接画文字
                    drawPointSize = _AddStr(width, height, pointList[0].x, pointList[0].y, str, FONT_WIDTH, FONT_HEIGHT,
                                            0);
                }
                //画实线
                YUVDataOpt_DrawLineInfo drawLineInfo;
                drawLineInfo.lineWidth = lineWidth;
                drawLineInfo.startPoint.x = pointList[0].x;
                drawLineInfo.startPoint.y = pointList[0].y;
                drawLineInfo.endPoint.x = pointList[1].x;
                drawLineInfo.endPoint.y = pointList[1].y;
                drawLineInfo.isFull = 1;
                drawPointSize = yuv_drawline(width, height, &drawLineInfo,
                                             drawPointSize);

            }
                break;

            case TYPE_DASHED_LINE: {
                if (str != nullptr) {
                    //直接画文字
                    drawPointSize = _AddStr(width, height, pointList[0].x, pointList[0].y, str, FONT_WIDTH, FONT_HEIGHT,
                                            0);
                }
                //画虚线
                YUVDataOpt_DrawLineInfo drawLineInfo;
                drawLineInfo.lineWidth = lineWidth;
                drawLineInfo.startPoint.x = pointList[0].x;
                drawLineInfo.startPoint.y = pointList[0].y;
                drawLineInfo.endPoint.x = pointList[1].x;
                drawLineInfo.endPoint.y = pointList[1].y;
                drawLineInfo.isFull = 0;
                drawPointSize = yuv_drawline(width, height, &drawLineInfo,
                                             drawPointSize);

            }
                break;

            case TYPE_RECTANGLE: {
                if (str != nullptr) {
                    //直接画文字
                    drawPointSize = _AddStr(width, height, pointList[0].x, pointList[0].y, str, FONT_WIDTH, FONT_HEIGHT,
                                            0);
                }
                //画框
                drawPointSize = draw_rect(pointList[0].x, pointList[0].y, pointList[1].x, pointList[1].y, width, height,
                                          drawPointSize,lineWidth);

            }
                break;

            case TYPE_ONLY_POINT: {
                //画点
                drawPointSize = yuv_drawPoint(pointList[0].x, pointList[0].y, pointList[0].x, pointList[0].y,
                                              drawPointSize);

            }
                break;

            case TYPE_QUADRILATERAL: {
                //画四边形
                drawPointSize = draw_quadrilateral(pointList[0].x, pointList[0].y, pointList[1].x, pointList[1].y,
                                                   pointList[2].x, pointList[2].y, pointList[3].x, pointList[3].y,
                                                   width, height, drawPointSize,lineWidth);
            }
                break;
            case TYPE_POLYGON: {
                if (str != nullptr) {
                    //直接画文字
                    drawPointSize = _AddStr(width, height, pointList[0].x, pointList[0].y, str, FONT_WIDTH, FONT_HEIGHT,
                                            0);
                }
                /* 画多边形(实线) */
                drawPointSize = draw_polygon_solidline(pointList, len, width, height, drawPointSize,lineWidth);

            }
                break;

            case TYPE_POLYGON_DASHEDLINE: {
                if (str != nullptr) {
                    //直接画文字
                    drawPointSize = _AddStr(width, height, pointList[0].x, pointList[0].y, str, FONT_WIDTH, FONT_HEIGHT,
                                            0);
                }
                /* 画多边形（虚线） */
                drawPointSize = draw_polygon_dashedline(pointList, len, width, height, drawPointSize,lineWidth);

            }
                break;


        }

//        printf("drawPointSize=%d    colorid=%d  \n",drawPointSize,colorid);
        for (int i = 0; i < drawPointSize && (drawPointSize <= width * height * 3 / 2); i++) {
            yuv_setdata(nv21data, nv21data + width * height, width, height, drawpoints[i], colorid);
        }
        drawPointSize = 0;
    }


    return result;
}

int XuYUVDataOpt::draw_polygon_solidline(const YUVDataOpt_Point *pointList, int pointListSize, int width, int height,
                                         int porintOffset, int lineWidth) {
    //四边形的第一条线
    for (int i = 0; i < pointListSize; i++) {
        YUVDataOpt_DrawLineInfo drawLineInfo;
        drawLineInfo.lineWidth = lineWidth;
        drawLineInfo.startPoint.x = pointList[i].x;
        drawLineInfo.startPoint.y = pointList[i].y;
        drawLineInfo.isFull = 1;
        if (i == pointListSize - 1) {
            drawLineInfo.endPoint.x = pointList[0].x;
            drawLineInfo.endPoint.y = pointList[0].y;
        } else {
            drawLineInfo.endPoint.x = pointList[i + 1].x;
            drawLineInfo.endPoint.y = pointList[i + 1].y;
        }
        drawLineInfo.isFull = 1;
        porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);

    }

    return porintOffset;
}

int XuYUVDataOpt::draw_polygon_dashedline(const YUVDataOpt_Point *pointList, int pointListSize, int width, int height,
                                          int porintOffset, int lineWidth) {
    //四边形的第一条线
    for (int i = 0; i < pointListSize; i++) {
        YUVDataOpt_DrawLineInfo drawLineInfo;
        drawLineInfo.lineWidth = lineWidth;
        drawLineInfo.startPoint.x = pointList[i].x;
        drawLineInfo.startPoint.y = pointList[i].y;
        drawLineInfo.isFull = 0;
        if (i == pointListSize - 1) {
            drawLineInfo.endPoint.x = pointList[0].x;
            drawLineInfo.endPoint.y = pointList[0].y;
        } else {
            drawLineInfo.endPoint.x = pointList[i + 1].x;
            drawLineInfo.endPoint.y = pointList[i + 1].y;
        }
        porintOffset = yuv_drawline(width, height, &drawLineInfo, porintOffset);

    }

    return porintOffset;
}

int XuYUVDataOpt::drawAdasLanelineToNV21(const YUVDataOpt_Point &p1, const YUVDataOpt_Point &p2, const double mA0,
                                         const double mA1, const double mA2, const double mA3, int width, int height,
                                         uint8_t *nv21data, const int colorId) {
    if (drawpoints == nullptr) {
        drawpoints = static_cast<YUVDataOpt_Point *>(malloc(width * height));
    }

    for(int i = 0; i < (width * height); i ++){
        /* 由于结构体里面没有裸指针，故直接用={}重置就好了 */
        drawpoints[i] = {};
    }
    int drawPointSize = 0;
    int maxY = (p1.y > p2.y) ? p1.y : p2.y;
    int minxY = (p1.y <= p2.y) ? p1.y : p2.y;

    for (int i = minxY; i <= maxY; i++) {
        int startX = 0;
        int startY = i;
        startX = mA0 + mA1 * startY + mA2 * (startY * startY) + mA3 * (startY * startY * startY);
        if (startX >= width) {
            startX = width;
        }

        if (startX <= 0) {
            startX = 0;
        }

        if (startY >= height) {
            startY = height;
        }

        if (startY <= 0) {
            startY = 0;
        }

        drawPointSize = yuv_drawPoint(startX, startY, startX, startY, drawPointSize);
    }

    uint8_t *YBuff = nv21data;
    uint8_t *UVBuff = nv21data + (width * height);
    for (int i = 0; i < drawPointSize; i++) {
        yuv_setdata(YBuff, UVBuff, width, height, drawpoints[i], colorId);
    }

    return 0;
}

double XuYUVDataOpt::getSlope(YUVDataOpt_Point pointA, YUVDataOpt_Point pointB) {
    if (pointB.x - pointA.x == 0) { // 看看直线是不是跟X轴垂直，如果是，就直接给0
        return 99999;
    }else if(pointB.y - pointA.y == 0){ // 看看直线是不是跟X轴平行，如果是，就直接给99999
        return 0;
    }
    double ret = (double )(pointB.y - pointA.y) / (double )(pointB.x - pointA.x);
    return  ret;
}

YUVDataOpt_Point XuYUVDataOpt::getPointOfIntersectingLine_Vertical(YUVDataOpt_Point pointA1, YUVDataOpt_Point pointA2,
                                                                   YUVDataOpt_Point pointB1, int lineLength,int direction) {
    YUVDataOpt_Point pointB2;
    // 计算直线A的斜率
    double kA = getSlope(pointA1, pointA2);
    if(kA == 0){
        //平行
//        printf("平行  kA=%f  \n",kA);
        pointB2.x = pointB1.x;
        pointB2.y = pointB1.y + lineLength;
    }else if(kA == 99999){
        //垂直
//        printf("垂直  kA=%f  \n",kA);
        pointB2.y = pointB1.y;
        pointB2.x = pointB1.x + lineLength;
    }else{

        /* 不是特殊的直线，那么就根据斜率算出另一个点 */
        double kB = -1 * (1 / kA);
        /* 跟根据斜率求出直线跟X轴的弧度 */
        double radian = 3.141592 - atan(kB);
        /* 利用正弦定理算出Y的差值 */
        double yDiff = (lineLength * sin(radian));
        /* 根据差值得出Y的坐标 */
        pointB2.y =  pointB1.y + yDiff;
        /* 利用勾股定理算出X的差值 */
        double xDiff = sqrt((lineLength * lineLength) - (yDiff * yDiff));
        /* 根据差值得出X的坐标 */
        pointB2.x =  pointB1.x + xDiff;
//        /* 套入斜率方程，得出X值 */
//        pointB2.x = (pointB2.y - pointB1.y + pointB1.x) / kB;
//        printf("kA=%f  kB=%f lineLength=%d pointB1.x=%d  pointB1.y=%d   endX=%d endY=%d\n",kA,kB,lineLength,pointB1.x,pointB1.y,pointB2.x,pointB2.y);

    }
    return pointB2;
}
