//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/5.
//

#ifndef VIS_G3_SOFTWARE_XUFILEDOWNLOADCALLBACK_H
#define VIS_G3_SOFTWARE_XUFILEDOWNLOADCALLBACK_H


#include <string>

class XuFileDownloadCallback {
public:
    /* 结果----成功 */
    static const int DOWNLOAD_RESULT_SUCCESS = 0;
    /* 结果----失败 */
    static const int DOWNLOAD_RESULT_FAILED = 1;

    /**
     * 获取到一包文件下载的结果
     *
     * @param filePath ： 文件路径
     * @param result ： 结果
     * @param ip ： 请求端的IP
     * @param port ： 请求端的端口
     */
    virtual void onGetDownloadResult(std::string filePath, const int result, const char *ip, const int port);

private:

};


#endif //VIS_G3_SOFTWARE_XUFILEDOWNLOADCALLBACK_H
