//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/5.
//

#include <cstring>
#include <unistd.h>
#include <arpa/inet.h>
#include "XuFileDownloadUtils.h"
#include "XuFile.h"

int
XuFileDownloadUtils::initDownloadTool(int port, std::string filePath, XuFileDownloadCallback &fileDownloadCallback) {
    int ret = -1;
    if (!XuFile::getInstance().fileExists(filePath.c_str())) {
        ret = -1;
        printf("file %s not find! \n", filePath.c_str());
    } else {
        curFilePath = filePath;
        curTcpPort = port;
        callback = &fileDownloadCallback;
        fileLen = XuFile::getInstance().getFileLength(filePath.c_str());
        if (fileLen > 0) {
            XuFile::getInstance().getMD5FromFile(filePath.c_str(), fileMD5);
            tcpOpen = true;
            ret = 0;
        } else {
            ret = -1;
        }
    }


    return ret;
}

void XuFileDownloadUtils::run() {
    std::string pthreadName = "XuFileDownload";
    pthread_setname_np(pthread_self(), pthreadName.c_str());

    int ret = -1;
    while (ret != 0) {
        if ((listenfd = socket(AF_INET, SOCK_STREAM, 0)) == -1) {
            printf("==================create download socket error: %s(errno: %d)==========================\n",
                   strerror(errno), errno);
            ret = -1;
        } else {
            memset(&servaddr, 0, sizeof(servaddr));
            servaddr.sin_family = AF_INET;
            servaddr.sin_addr.s_addr = htonl(INADDR_ANY);
            servaddr.sin_port = htons(curTcpPort);
            /* 设置一下端口复用 */
            int opt = 1;
            setsockopt(listenfd, SOL_SOCKET, SO_REUSEADDR, (const void *) &opt, sizeof(opt));
            /* 绑定端口 */
            if (bind(listenfd, (struct sockaddr *) &servaddr, sizeof(servaddr)) == -1) {
                printf("=========================bind upgrade socket error: %s(errno: %d)========================\n",
                       strerror(errno), errno);
                ret = -1;
            } else {
                if (listen(listenfd, 10) == -1) {
                    printf("=========================listen upgrade socket error: %s(errno: %d)=========================\n",
                           strerror(errno), errno);
                    ret = -1;
                } else {
                    ret = 0;
                    tcpOpen = true;
                }
            }
        }
        sleep(1);
    }

    fd_set readfds;//保存读文件描述符集合

    while (listenfd != -1) {
        //把readfds清空
        FD_ZERO(&readfds);
        //把要监听的sockfd添加到readfds中
        FD_SET(listenfd, &readfds);
        timeval tv;
        tv.tv_sec = 60;
        tv.tv_usec = 0;
        //用select类监听sockfd 阻塞状态
        printf("before select \n");
        int ret = select(listenfd + 1, &readfds, nullptr, nullptr, &tv);

        int connfd = -1;
        if (ret > 0 && FD_ISSET(listenfd, &readfds)) {
            printf("FD_ISSET(listenfd, &readfds) \n");
            if ((connfd = accept(listenfd, (struct sockaddr *) nullptr, 0)) == -1) {
                printf("accept socket error: %s(errno: %d)   \n", strerror(errno), errno);
                usleep(1000 * 1000);
                continue;
            }
            struct sockaddr_in sa;
            int len;
            len = sizeof(sa);
            if (!getpeername(connfd, (struct sockaddr *) &sa, reinterpret_cast<socklen_t *>(&len))) {
                char *ip = inet_ntoa(sa.sin_addr);
                memcpy(clientIp, ip, strlen(ip));
                clientPort = ntohs(sa.sin_port);
                isDownloading = true;
                printf("download client login. ip: %s, port :%d \n", clientIp, clientPort);
                /* 开始发送出去 */
                sendData(connfd);
                break;


            } else {
                close(connfd);
                close(listenfd);
                isDownloading = false;
                tcpOpen = false;
                callback->onGetDownloadResult(curFilePath, XuFileDownloadCallback::DOWNLOAD_RESULT_FAILED, clientIp,
                                              clientPort);
                printf("download client get ip failed! \n");
                break;
            }
        } else {
            //超时了  直接return掉把
            printf("accept timeout!  wait time >= 60    \n");
            callback->onGetDownloadResult(curFilePath, XuFileDownloadCallback::DOWNLOAD_RESULT_FAILED, clientIp,
                                          clientPort);
            close(listenfd);
            listenfd = -1;
            isDownloading = false;
            tcpOpen = false;
            return;
        }


    }

    pthread_setname_np(pthread_self(), "Finish");
}

void XuFileDownloadUtils::sendData(int clientFd) {
    int revcLen = 0;
    int sendLen = 0;
    uint8_t sendBuf[1024 * 100] = {0x00};
    fd_set sendfds;//保存读文件描述符集合
    FILE *fileFd = fopen(curFilePath.c_str(), "r");
    if (!fileFd) {
        isDownloading = false;
        tcpOpen = false;
        close(clientFd);
        close(listenfd);
        fclose(fileFd);
        callback->onGetDownloadResult(curFilePath, XuFileDownloadCallback::DOWNLOAD_RESULT_FAILED, clientIp,
                                      clientPort);
    } else {
        while (isDownloading) {
            //把readfds清空
            FD_ZERO(&sendfds);
            //把要监听的sockfd添加到readfds中
            FD_SET(clientFd, &sendfds);
            timeval tv;
            tv.tv_sec = 60;
            tv.tv_usec = 0;
            //用select类监听sockfd 阻塞状态
            int ret = select(clientFd + 1, nullptr, &sendfds, nullptr, &tv);
            if (ret > 0 && FD_ISSET(clientFd, &sendfds)) {
                /* 先从文件里读出数据 */
                revcLen = fread(sendBuf, 1, sizeof(sendBuf), fileFd);
                if (revcLen > 0) {
                    /* 读到数据了  那么直接写到socket里去 */
                    sendLen = write(clientFd, sendBuf, sizeof(sendBuf));
                    /* 判断下写出去多少  写不够就等写够  写出去的<= 0 说明连接断开了 */
                    if (sendLen > 0) {
                        allSendFileDataLen = allSendFileDataLen + sendLen;
                    } else {
                        isDownloading = false;
                        tcpOpen = false;
                        close(clientFd);
                        close(listenfd);
                        fclose(fileFd);
                        callback->onGetDownloadResult(curFilePath, XuFileDownloadCallback::DOWNLOAD_RESULT_FAILED,
                                                      clientIp, clientPort);
                        printf("can not send file data! download fialed  ip=%s  allSendFileDataLen=%u   fileLen=%u \n",
                               clientIp, allSendFileDataLen, fileLen);
                        break;
                    }

                } else {
                    /* 如果小于等于0  说明文件传输结束了  那么验证下发送的长度是不是对 */
                    if (allSendFileDataLen == fileLen) {
                        isDownloading = false;
                        tcpOpen = false;
                        close(clientFd);
                        close(listenfd);
                        fclose(fileFd);
                        callback->onGetDownloadResult(curFilePath, XuFileDownloadCallback::DOWNLOAD_RESULT_SUCCESS,
                                                      clientIp, clientPort);
                        printf("download success  ip=%s  allSendFileDataLen=%u   fileLen=%u \n", clientIp,
                               allSendFileDataLen, fileLen);
                        break;
                    } else {
                        isDownloading = false;
                        tcpOpen = false;
                        close(clientFd);
                        close(listenfd);
                        fclose(fileFd);
                        callback->onGetDownloadResult(curFilePath, XuFileDownloadCallback::DOWNLOAD_RESULT_FAILED,
                                                      clientIp, clientPort);
                        printf("can not read file data! download fialed  ip=%s  allSendFileDataLen=%u   fileLen=%u \n",
                               clientIp, allSendFileDataLen, fileLen);
                        break;
                    }
                    usleep(3 * 1000);
                }

            } else {
                isDownloading = false;
                tcpOpen = false;
                close(clientFd);
                close(listenfd);
                fclose(fileFd);
                callback->onGetDownloadResult(curFilePath, XuFileDownloadCallback::DOWNLOAD_RESULT_FAILED, clientIp,
                                              clientPort);
                printf("download client %s is not data > 60s!   allSendFileDataLen=%d curFileLen=%d \n", clientIp,
                       allSendFileDataLen,
                       fileLen);
                break;
            }
        }
    }
}

bool XuFileDownloadUtils::isTcpOpen() const {
    return tcpOpen;
}

bool XuFileDownloadUtils::isDownloading1() const {
    return isDownloading;
}

int XuFileDownloadUtils::getCurTcpPort() const {
    return curTcpPort;
}

void XuFileDownloadUtils::setCurTcpPort(int curTcpPort) {
    XuFileDownloadUtils::curTcpPort = curTcpPort;
}

const std::string &XuFileDownloadUtils::getCurFilePath() const {
    return curFilePath;
}

void XuFileDownloadUtils::setCurFilePath(const std::string &curFilePath) {
    XuFileDownloadUtils::curFilePath = curFilePath;
}

const uint8_t *XuFileDownloadUtils::getFileMd5() const {
    return fileMD5;
}

uint32_t XuFileDownloadUtils::getFileLen() const {
    return fileLen;
}

void XuFileDownloadUtils::setFileLen(uint32_t fileLen) {
    XuFileDownloadUtils::fileLen = fileLen;
}

int XuFileDownloadUtils::getListenfd() const {
    return listenfd;
}

void XuFileDownloadUtils::setListenfd(int listenfd) {
    XuFileDownloadUtils::listenfd = listenfd;
}

const sockaddr_in &XuFileDownloadUtils::getServaddr() const {
    return servaddr;
}

void XuFileDownloadUtils::setServaddr(const sockaddr_in &servaddr) {
    XuFileDownloadUtils::servaddr = servaddr;
}

void XuFileDownloadUtils::setTcpOpen(bool tcpOpen) {
    XuFileDownloadUtils::tcpOpen = tcpOpen;
}

bool XuFileDownloadUtils::isDownloading2() const {
    return isDownloading;
}

void XuFileDownloadUtils::setIsDownloading(bool isDownloading) {
    XuFileDownloadUtils::isDownloading = isDownloading;
}

const char *XuFileDownloadUtils::getClientIp() const {
    return clientIp;
}

int XuFileDownloadUtils::getClientPort() const {
    return clientPort;
}

void XuFileDownloadUtils::setClientPort(int clientPort) {
    XuFileDownloadUtils::clientPort = clientPort;
}

uint32_t XuFileDownloadUtils::getAllSendFileDataLen() const {
    return allSendFileDataLen;
}

void XuFileDownloadUtils::setAllSendFileDataLen(uint32_t allSendFileDataLen) {
    XuFileDownloadUtils::allSendFileDataLen = allSendFileDataLen;
}
