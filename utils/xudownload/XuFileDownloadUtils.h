//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/12/5.
//

#ifndef VIS_G3_SOFTWARE_XUFILEDOWNLOADUTILS_H
#define VIS_G3_SOFTWARE_XUFILEDOWNLOADUTILS_H

#include <Poco/Runnable.h>
#include <Poco/ThreadPool.h>
#include <cstdint>
#include <netinet/in.h>
#include <XuFileDownloadCallback.h>

class XuFileDownloadUtils : public Poco::Runnable {
public:
    int initDownloadTool(int port, std::string filePath, XuFileDownloadCallback &fileDownloadCallback);

    void run() override;

    void sendData(int clientFd);

    bool isTcpOpen() const;

    bool isDownloading1() const;

    int getCurTcpPort() const;

    void setCurTcpPort(int curTcpPort);

    const std::string &getCurFilePath() const;

    void setCurFilePath(const std::string &curFilePath);

    const uint8_t *getFileMd5() const;

    uint32_t getFileLen() const;

    void setFileLen(uint32_t fileLen);

    int getListenfd() const;

    void setListenfd(int listenfd);

    const sockaddr_in &getServaddr() const;

    void setServaddr(const sockaddr_in &servaddr);

    void setTcpOpen(bool tcpOpen);

    bool isDownloading2() const;

    void setIsDownloading(bool isDownloading);

    const char *getClientIp() const;

    int getClientPort() const;

    void setClientPort(int clientPort);

    uint32_t getAllSendFileDataLen() const;

    void setAllSendFileDataLen(uint32_t allSendFileDataLen);

private:
    XuFileDownloadCallback *callback;
    int curTcpPort = -1;
    std::string curFilePath;
    uint8_t fileMD5[16] = {0x00};
    uint32_t fileLen = -1;
    int listenfd;
    struct sockaddr_in servaddr;
    bool tcpOpen = false;
    bool isDownloading = false;
    char clientIp[20] = {0x00};
    int clientPort = -1;
    uint32_t allSendFileDataLen = 0;

};


#endif //VIS_G3_SOFTWARE_XUFILEDOWNLOADUTILS_H
