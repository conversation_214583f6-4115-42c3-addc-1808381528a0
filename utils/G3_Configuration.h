//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/27.
//

#ifndef VIS_G3_SOFTWARE_G3_CONFIGURATION_H
#define VIS_G3_SOFTWARE_G3_CONFIGURATION_H

#include <yaml-cpp/yaml.h>
#include <fstream>
#include "XuCalculationTool.h"

#include <iostream>


/* 加密功能锁所使用的AES-128-ECB的密钥 */
#define AES_128_ECB_KEY_FUNCTIONLOCK "VISPECT.NETKEYG4"

/* G4使用的IP和实景的UDP、TCP端口 */
#define G3_IP_SLOT_POSITION_2 "*************"
#define G3_IP_SLOT_POSITION_1 "*************"
#define G3_UDP_CAMERA1_PORT_SLOT_POSITION_2 8811
#define G3_UDP_CAMERA2_PORT_SLOT_POSITION_2 8812
#define G3_UDP_CAMERA3_PORT_SLOT_POSITION_2 8813
#define G3_UDP_CAMERA4_PORT_SLOT_POSITION_2 8814
#define G3_UDP_CAMERA1_PORT_SLOT_POSITION_1 8821
#define G3_UDP_CAMERA2_PORT_SLOT_POSITION_1 8822
#define G3_UDP_CAMERA3_PORT_SLOT_POSITION_1 8823
#define G3_UDP_CAMERA4_PORT_SLOT_POSITION_1 8824
#define G3_TCP_CAMERA1_PORT 8831
#define G3_TCP_CAMERA2_PORT 8832
#define G3_TCP_CAMERA3_PORT 8833
#define G3_TCP_CAMERA4_PORT 8834


/* TF卡根目录 */
#define TF_CARD_ROOT_PATH "/mnt/sdcard/"
/* V6使用的内部存储根目录 */
#define EMMC_ROOT_PATH "/userdata/media/"
/* 镜头1的DVR存放路径 */
#define DVR_FILE_PATH_OF_CAMERA1 "DVR/camera1/"
/* 镜头2的DVR存放路径 */
#define DVR_FILE_PATH_OF_CAMERA2 "DVR/camera2/"
/* 镜头3的DVR存放路径 */
#define DVR_FILE_PATH_OF_CAMERA3 "DVR/camera3/"
/* 镜头4的DVR存放路径 */
#define DVR_FILE_PATH_OF_CAMERA4 "DVR/camera4/"
/* 未知用途视频存放路径 */
#define UNKNOW_VIDEO_FILE_PATH "otherVideo/"
/* G3-S模式下一路镜头的DVR能使用的内存空间的比例 */
#define MAX_ONE_CAMERA_DVR_FILE_SIZE_IN_G3S 0.3
/* G3-S模式下一路镜头的报警视频能使用的内存空间的比例 */
#define MAX_ONE_CAMERA_WAR_VIDEO_FILE_SIZE_IN_G3S 0.075
/* G3-S模式下一路镜头的报警图片能使用的内存空间的比例 */
#define MAX_ONE_CAMERA_WAR_PHOTO_FILE_SIZE_IN_G3S 0.025
/* V6模式下一路镜头的DVR能使用的内存空间的比例 */
#define MAX_ONE_CAMERA_DVR_FILE_SIZE_IN_V6 0.6
/* V6模式下一路镜头的报警视频能使用的内存空间的比例 */
#define MAX_ONE_CAMERA_WAR_VIDEO_FILE_SIZE_IN_V6 0.15
/* V6模式下一路镜头的报警图片能使用的内存空间的比例 */
#define MAX_ONE_CAMERA_WAR_PHOTO_FILE_SIZE_IN_V6 0.05
/* G4-mini模式下一路镜头的DVR能使用的内存空间的比例 */
#define MAX_ONE_CAMERA_DVR_FILE_SIZE_IN_G4MINI 0.15
/* G4-mini模式下一路镜头的报警视频能使用的内存空间的比例 */
#define MAX_ONE_CAMERA_WAR_VIDEO_FILE_SIZE_IN_G4MINI 0.0375
/* G4-mini模式下一路镜头的报警图片能使用的内存空间的比例 */
#define MAX_ONE_CAMERA_WAR_PHOTO_FILE_SIZE_IN_G4MINI 0.0125
/* 功能锁内容文件的存放目录 */
#define FUNCTIONLOCKFILEPATH "/userdata/FunctionLock"
/* 多媒体文件加密所使用的密钥文件的存放目录 */
#define MULTIMEDIAFILEENCRYPTIONKEYPATH "/userdata/MutimediaEncryptionKey"
/* 报警声音文件配置参数存放的文件路径 */
#define ALARM_SOUND_FILE_CONFIG_PATH "/userdata/alarmSoundFile/alarmSoundConfig"
/* 镜头输入类型过滤器内容文件的存放目录 */
#define CAMERAINPUTTYPEFILTERINFOFILEPATH "/userdata/CameraInputTypeFilterInfo"
/* 镜头降效信息内容文件的存放目录 */
#define CAMERAREDUCEERRECTINFOFILEPATH "/userdata/CameraReduceEffectInfo"

/* 纯黑色的YUV图片的地址(这里使用相对路径，跟程序在同一目录) */
#define BLACK_YUV_FILE_PATHA "blackyuv"

/* MRV220所使用的相机图像的分辨率 */
#define MRV220_IMG_WIDTH 1280
#define MRV220_IMG_HEIGHT 720
/* 最新的启动脚本的MD5值 */
#define NEW_MRV220_LUCHER_FILE_MD5 "5ff7dc81095461e128ade1a630b5f069"



enum IO_TYPE_IN {
    /* 无定义  */
    IO_TYPE_IN_UNKNOW = -1,
    /* 左转信号 */
    IO_TYPE_IN_TURN_LEFT = 0,
    /* 右转信息 */
    IO_TYPE_IN_TURN_RIGHT = 1,
    /* 车速 */
    IO_TYPE_IN_TURN_SPEED = 2,
    /* 侧翻信号 */
    IO_TYPE_IN_TURN_ROLLOVER = 3,
    /* 车门信号 */
    IO_TYPE_IN_DOOR = 4,
    /* 倒车信号 */
    IO_TYPE_IN_REVERSE = 5,
    /* 工程机械操作杆 */
    IO_TYPE_IN_MACHINERY_ACTION_BARS = 6,
    /* 镜头报警开关1 */
    IO_TYPE_IN_CAMERA_ALARM_SWITCH_1 = 7,
    /* 镜头报警开关w */
    IO_TYPE_IN_CAMERA_ALARM_SWITCH_2 = 8,
    /* 镜头报警开关3 */
    IO_TYPE_IN_CAMERA_ALARM_SWITCH_3 = 9,
    /* 震动信号(目前只是给MRV330用以触发震动事件的) */
    IO_TYPE_IN_SHOCK_SIGNA = 10,
    /* 消警信号 */
    IO_TYPE_IN_SAFEPASS_SIGN = 11,
    /* SOS信号 */
    IO_TYPE_IN_SOS_SIGN = 12,
    /* RoadMode信号 */
    IO_TYPE_IN_ROADMODE_SIGN = 13,

};

enum IO_TYPE_OUT {
    /* 无定义  */
    IO_TYPE_OUT_UNKNOW = -1,
    /* 只输出一次（报警时闭合持续2s，间隔10s）  */
    IO_TYPE_OUT_ONLY_ONE = 0,
    /* 持续输出直到报警结束  */
    IO_TYPE_OUT_CONTINUOUS = 1,
    /* 相反输出（报警时断开持续500ms，间隔500s）  */
    IO_TYPE_OUT_CONTRARY = 2,

};


enum Speaker_Sound_LanguageType {
    /* 中文  */
    SPEAKER_SOUND_LANGUAGE_CHINESE = 0,
    /* 英文 */
    SPEAKER_SOUND_LANGUAGE_ENGLISH = 1,
};

enum Speaker_Enbale_Type {
    /* 0-不响  */
    SPEAKER_ENABLE_TYPE_NOT = 0,
    /* 1-响提示音 */
    SPEAKER_ENABLE_TYPE_SOUND_EFFECT = 1,
    /* 2-响语音 */
    SPEAKER_ENABLE_TYPE_VOICE = 2,
};

struct IO_OUT_Alarm_Type {
    /* 侧边行人1级区域报警 */
    bool bsd_pedestrian_level_1: 1;
    /* 侧边行人2级区域报警 */
    bool bsd_pedestrian_level_2: 1;
    /* 侧边车辆1级区域报警 */
    bool bsd_vehicle_level_1: 1;
    /* 侧边车辆2级区域报警 */
    bool bsd_vehicle_level_2: 1;
    /* DSM（暂定全部） */
    bool dsm: 1;
    /* SBST前向行人1级区域报警 */
    bool sbst_forward_pedestrian_level_1: 1;
    /* SBST前向行人2级区域报警 */
    bool sbst_forward_pedestrian_level_2: 1;
    /* SBST前向车辆1级区域报警 */
    bool sbst_forward_vehicle_level_1: 1;
    /* SBST前向车辆2级区域报警 */
    bool sbst_forward_vehicle_level_2: 1;
    /* SBST前车道偏离报警（左边虚线） */
    bool sbst_forward_ldw_left_dashed: 1;
    /* SBST前车道偏离报警（左边实线） */
    bool sbst_forward_ldw_left_solid: 1;
    /* SBST前车道偏离报警（右边虚线） */
    bool sbst_forward_ldw_right_dashed: 1;
    /* SBST前车道偏离报警（右边实线） */
    bool sbst_forward_ldw_right_solid: 1;
    /* ADAS前车防碰撞 */
    bool adas_ttc: 1;
    /* ADAS车距过近 */
    bool adas_hmw: 1;
    /* ADAS行人防碰撞 */
    bool adas_pdw: 1;
    /* ADAS车道偏离（左边实线） */
    bool adas_ldw_left_solid: 1;
    /* ADAS车道偏离（左边虚线） */
    bool adas_ldw_left_dashed: 1;
    /* ADAS车道偏离（右边实线） */
    bool adas_ldw_right_solid: 1;
    /* ADAS车道偏离（右边虚线） */
    bool adas_ldw_right_dashed: 1;
    /* R151行人区域1报警 */
    bool r151_pedestrian_level_1: 1;
    /* R151行人区域2报警 */
    bool r151_pedestrian_level_2: 1;
    /* R151行人区域3报警 */
    bool r151_pedestrian_level_3: 1;
    /* R151行人区域4报警 */
    bool r151_pedestrian_level_4: 1;
    /* R151车辆区域1报警 */
    bool r151_vehicle_level_1: 1;
    /* R151车辆区域2报警 */
    bool r151_vehicle_level_2: 1;
    /* R151镜头遮挡报警 */
    bool r151_camera_cover: 1;
    /* R158行人区域1报警 */
    bool r158_pedestrian_level_1: 1;
    /* R158行人区域2报警 */
    bool r158_pedestrian_level_2: 1;
    /* R158行人区域3报警 */
    bool r158_pedestrian_level_3: 1;
    /* R158车辆区域1报警 */
    bool r158_vehicle_level_1: 1;
    /* R158车辆区域2报警 */
    bool r158_vehicle_level_2: 1;
    /* R158镜头遮挡报警 */
    bool r158_camera_cover: 1;
    /* R159行人区域1报警 */
    bool r159_pedestrian_level_1: 1;
    /* R159行人区域2报警 */
    bool r159_pedestrian_level_2: 1;
    /* R159行人区域3报警 */
    bool r159_pedestrian_level_3: 1;
    /* R159车辆区域1报警 */
    bool r159_vehicle_level_1: 1;
    /* R159车辆区域2报警 */
    bool r159_vehicle_level_2: 1;
    /* R159镜头遮挡报警 */
    bool r159_camera_cover: 1;
    /* ADAS虚拟保险杆 */
    bool adas_vb: 1;
    /* ADAS前车启动 */
    bool adas_go: 1;
    /* 其他预留 */
    long long int ohter : 43;
};

enum ALGORITHM_TYPE {
    /* 不启用算法 */
    ALGORITHM_TYPE_NOT = 0,
    /* 英国工程机械-BSD相机-目标检测 */
    ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION = 1,
    /* 新加坡巴士-160度相机-语义分割 */
    ALGORITHM_TYPE_SBST_160_SEMANTIC_SEGMENTATION = 2,
    /* 地铁DSM */
    ALGORITHM_TYPE_DSM_METRO = 3,
    /* 天津地铁手势 */
    ALGORITHM_TYPE_GESTURE_TJ_METRO = 4,
    /* 新加坡巴士-160度相机-目标检测 */
    ALGORITHM_TYPE_SBST_160_OBJECT_DETECTION = 5,
    /* 英国工程机械-160度相机-目标检测 */
    ALGORITHM_TYPE_UK_BSD_160_OBJECT_DETECTION = 6,
    /* ADAS-160度镜头 */
    ALGORITHM_TYPE_ADAS_160_NORMAL = 12,
    /* 德国公交车-BSD相机-目标检测 */
    ALGORITHM_TYPE_DE_BSD_OBJECT_DETECTION = 13,
    /* ADAS-60度镜头 */
    ALGORITHM_TYPE_ADAS_60_NORMAL = 14,
    /* 新加坡MUV的DSM */
    ALGORITHM_TYPE_DSM_MUV = 15,
    /* DVR算法（目前暂时只识别镜头遮挡） */
    ALGORITHM_TYPE_DVR = 16,
    /* 小松-BSD相机-目标检测 */
    ALGORITHM_TYPE_KOM_BSD_OBJECT_DETECTION = 17,
    /* SBST-ADAS-160度镜头 */
    ALGORITHM_TYPE_ADAS_160_SBST = 18,
    /* 标准的DSM算法 */
    ALGORITHM_TYPE_DSM_NORMAL = 19,
    /* 英国DD的二维码识别 */
    ALGORITHM_TYPE_UK_DD_QRCODE = 20,
    /* DSM-识别安全带 */
    ALGORITHM_TYPE_DSM_FB = 21,
    /* 叉车-160度镜头-目标检测 */
   ALGORITHM_TYPE_FORKLIFT_160_OBJECT_DETECTION = 22,

   /* 以下的定义都是为了兼容徐工的旧版本客户端 */
    /* SBST的向前 */
    ALGORITHM_TYPE_SBST_FORWARD = 7,
    /* SBST的向后 */
    ALGORITHM_TYPE_SBST_BACKWARD = 8,
    /* SBST的侧边 */
    ALGORITHM_TYPE_SBST_SIDEWARD = 9,
    /* ADAS */
    ALGORITHM_TYPE_ADAS_NORMAL = 10,
    /* 侧边行人和车辆 */
    ALGORITHM_TYPE_SIDE_PEDESTRIAN_AND_VEHICLE = 11,

};

enum VEHICLE_MODEL {
    VEHICLE_MODEL_SEDAN_CAR = 1,
    VEHICLE_MODEL_TRUCK = 2,
    VEHICLE_MODEL_CONSTRUCTION_MACHINERY = 3,
    VEHICLE_MODEL_FORKLIFT = 4,
    VEHICLE_MODEL_METRO = 5,
    VEHICLE_MODEL_SGP_BUS = 6,
    VEHICLE_MODEL_R151158159 = 7,
    VEHICLE_MODEL_MOTORHOME = 8,
};

enum INSTALL_POSITION {
    INSTALL_POSITION_FRONT = 1,
    INSTALL_POSITION_BACK = 2,
    INSTALL_POSITION_LEFT = 3,
    INSTALL_POSITION_RIGHT = 4,
    INSTALL_POSITION_CAB = 5,
    INSTALL_POSITION_ADAS = 6,
};


enum CAMERA_ORIENTATION {
    CAMERA_ORIENTATION_FORWARD = 1,
    CAMERA_ORIENTATION_BACKWARD = 2,
    CAMERA_ORIENTATION_LEFT = 3,
    CAMERA_ORIENTATION_RIGHT = 4,
    CAMERA_ORIENTATION_DOWNWARD = 5,
    CAMERA_ORIENTATION_DRIVER = 6,
};


struct CameraType_DetectType {
    bool pedestrianEnable: 1;
    bool vehicleEnable: 1;
    bool faceEnable: 1;
    bool gestureEnable: 1;
    uint otherEnable: 28;
};

/* 报警决策类型 */
struct CameraType_AlarmDecisionType {
    /* 人（区域) */
    bool pedestrian_area: 1;
    /* 车（区域) */
    bool vehicle_area: 1;
    /* 人脸 */
    bool mt_dsm_face: 1;
    /* 地铁手势 */
    bool gesture_metro: 1;
    /* 人（TTC) */
    bool pedestrian_ttc: 1;
    /* 车（TTC） */
    bool vehicle_ttc: 1;
    /* 车道线 */
    bool laneline: 1;
    /* 交通标志 */
    bool trafficSign: 1;
    /* 车辆（变线辅助） */
    bool laneChangeAssistance: 1;
    /* 行人（R151) */
    bool pedestrian_r151: 1;
    /* 车辆（R151） */
    bool vehicle_r151: 1;
    /* 行人（R158) */
    bool pedestrian_r158: 1;
    /* 车辆（R158） */
    bool vehicle_r158: 1;
    /* 行人（R159) */
    bool pedestrian_r159: 1;
    /* 车辆（R159） */
    bool vehicle_r159: 1;
    /* 行人（SBST后向） */
    bool pedestrian_SBST_Backward: 1;
    /* 车辆（SBST后向） */
    bool vehicle_SBST_Backward: 1;
    /* 新加坡的MUV的人脸 */
    bool muv_dsm_face: 1;
    /* 镜头状态 */
    bool camera_status: 1;
    /* 新加坡专用的傻逼ADAS车辆报警 */
    bool spg_adas_vehicle: 1;
    /* 英国DD的定位二维码识别的报警 */
    bool uk_dd_qrcode: 1;
    /* 人（Security区域） */
    bool pedestrian_area_security_model: 1;
    /* 标准DSM的人脸 */
    bool std_dsm_face: 1;
    /* 预留 */
    uint otherEnable : 9;

};

/* 所有算法决策的类型 */
enum ALARM_DECISION_TYPE {
    /* 未定义 */
    ALARM_DECISION_TYPE_UNKNOW = 0,
    /* 人（区域) */
    ALARM_DECISION_TYPE_PEDESTRIAN_AREA,
    /* 车（区域) */
    ALARM_DECISION_TYPE_VEHICLE_AREA,
    /* 人脸 */
    ALARM_DECISION_TYPE_MT_DSM_FACE,
    /* 地铁手势 */
    ALARM_DECISION_TYPE_GESTURE_METRO,
    /* 人（TTC) */
    ALARM_DECISION_TYPE_PEDSTRIAN_TTC,
    /* 车（TTC） */
    ALARM_DECISION_TYPE_VEHICLE_TTC,
    /* 车道线 */
    ALARM_DECISION_TYPE_LANELINE,
    /* 交通标志 */
    ALARM_DECISION_TYPE_TRAFFIC_SIGN,
    /* 车辆（变线辅助） */
    ALARM_DECISION_TYPE_LANE_CHANGE_ASSISTANCE,
    /* 行人（R151） */
    ALARM_DECISION_TYPE_PEDESTRIAN_R151,
    /* 车辆（R151） */
    ALARM_DECISION_TYPE_VEHICLE_R151,
    /* 行人（R158） */
    ALARM_DECISION_TYPE_PEDESTRIAN_R158,
    /* 车辆（R158） */
    ALARM_DECISION_TYPE_VEHICLE_R158,
    /* 行人（R159） */
    ALARM_DECISION_TYPE_PEDESTRIAN_R159,
    /* 车辆（R159） */
    ALARM_DECISION_TYPE_VEHICLE_R159,
    /* 行人（SBST后向） */
    ALARM_DECISION_TYPE_PEDESTRIAN_SBST_BACKWARD,
    /* 车辆（SBST后向） */
    ALARM_DECISION_TYPE_VEHICLE_SBST_BACKWARD,
    /* 新加坡的MUV的人脸 */
    ALARM_DECISION_TYPE_MUV_DSM_FACE,
    /* 镜头状态 */
    ALARM_DECISION_TYPE_CAMERA_STATUS,
    /* 新加坡的特殊版ADAS车辆报警决策 */
    ALARM_DECISION_TYPE_SPG_ADAS_VEHICLE,
    /* 英国DD的定位二维码识别的报警决策 */
    ALARM_DECISION_TYPE_DD_QRCODE,
    /*  人（Security区域） */
    ALARM_DECISION_TYPE_PEDESTRIAN_AREA_SECURITY_MODE,
    /* 标准DSM的人脸 */
    ALARM_DECISION_TYPE_STD_DSM_FACE,
};

struct BSD_Alarm_Trend_Shielding {
    bool trend_static: 1;
    bool trend_far_to_near: 1;
    bool trend_near_to_far: 1;
    uint otherEnable: 29;
};

enum G3WORKMODE {
    /*G3的工作模式--作为G4的一部分工作 */
    G3WORKMODE_G4_UNIT = 0,
    /*G3的工作模式--作为独立G3工作 */
    G3WORKMODE_G3S = 1,
    /*G3的工作模式--作为G4-MT的一部分工作 */
    G3WORKMODE_G4MT_UNIT = 2,
    /*G3的工作模式--作为G4-GJ的G3部分工作 */
    G3WORKMODE_G4GJ_UNIT_G3 = 3,
    /*G3的工作模式--作为G4-GJ中的G4的一部分工作 */
    G3WORKMODE_G4GJ_G4_UNIT = 4,
    /*G3的工作模式--V6 */
    G3WORKMODE_V6 = 5,
    /*G3的工作模式--G4-MINI */
    G3WORKMODE_G4MINI = 6,
    /*G3的工作模式--作为G4-4+1的一部分工作 */
    G3WORKMODE_G45_UNIT = 7,
};

enum H264_BIT_RATE_LEVEL {
    /* 等级1   1M */
    H264_BIT_RATE_LEVEL_LEVEL1 = 1,
    /* 等级2   2M */
    H264_BIT_RATE_LEVEL_LEVEL2 = 2,
    /* 等级3   3M */
    H264_BIT_RATE_LEVEL_LEVEL3 = 3,
    /* 等级4   4M */
    H264_BIT_RATE_LEVEL_LEVEL4 = 4,
};

enum SpeedFrom {
    SPEEDFROM_DEFAULT = -1,
    SPEEDFROM_CAN = 0,
    SPEEDFROM_UART = 1,
    SPEEDFROM_GPS = 2,
    SPEEDFROM_ETH = 3,
};


/* 镜头的ID列表 */
enum CAMERAIDLIST {
    /* 未定义的ID */
    CAMERA_ID_UNKNOW = -1,
    /* 镜头1的ID */
    CAMERA_ID_1 = 0,
    /* 镜头2的ID */
    CAMERA_ID_2 = 1,
    /* 镜头3的ID */
    CAMERA_ID_3 = 2,
    /* 镜头4的ID */
    CAMERA_ID_4 = 3,
};

/* 功能模块编码 */
enum UNIT_NUM{
    /* 通信模块 */
    UNIT_NUM_COMMUNICATIONUNIT = 1,
    /* 识别模块 */
    UNIT_NUM_DETECTUNIT,
    /* 多媒体模块 */
    UNIT_NUM_MEDIAUNIT,
    /* 外设模块 */
    UNIT_NUM_PERIPHERALUNIT,
    /* UI模块 */
    UNIT_NUM_UIUNIT,
    /* 升级模块 */
    UNIT_NUM_UPGRADEUNIT,
};


/* 镜头输入类型 */
enum CAMERA_INPUT_TYPE{
    /* 无效 */
    CAMERA_INPUT_TYPE_NONE = 0,
    /* AHD信号 1280*720的分辨率 */
    CAMERA_INPUT_TYPE_AHD_720P = 1,
    /* CVBS信号(P制) 720*576的分辨率 */
    CAMERA_INPUT_TYPE_CVBS_D1_PAL = 2,
    /* CVBS信号(P制) 720*480的分辨率 */
    CAMERA_INPUT_TYPE_CVBS_D1_NTSC = 3,
    /* AHD信号 1080*1920的分辨率 */
    CAMERA_INPUT_TYPE_AHD_1080P = 4,
};

/* 镜头类型信息 */
struct CameraType {
    uint8_t cameraId = 0xFF;    /* 相机ID */
    uint8_t installVehicleModel = 0xFF;      /* 安装设备的车辆型号   1：小轿车  2：货车  3：工程机械  4：叉车  5：地铁 */
    uint8_t installPosition = 0xFF;      /* 设备的安装位置   1：车辆前面  2：车辆后面  3：车辆左侧  4：车辆右侧  5：驾驶室内  6：ADAS指定位置 */
    uint8_t cameraOrientation = 0xFF;      /* 镜头的朝向   1：朝前车前进的方向  2：朝向车前进的反方向  3：朝车的左侧  4：朝车的右侧  5：朝车地面  6：朝司机 */
    CameraType_DetectType detectType = {false, false, false, false, 0};    /* 识别目标 由于是多选的，所以使用4个字节的int来装，一个bit代表一种类型，该bit位为1表示开始该类型的识别   bit0：行人  bit1：汽车 */
    uint8_t algType = 0;       /* #算法类型   0：不启动算法  1：英国工程机械-BSD相机-目标检测  2：新加坡巴士-160度相机-语义分割  3：DSM  4：地铁手势  5：新加坡巴士-160度相机-目标检测  6：英国工程机械-160度相机-目标检测 */
    CameraType_AlarmDecisionType adType = {};    /* 报警决策类型   由于是多选的，所以使用4个字节的int来装，一个bit代表一种类型，该bit位为1表示开始该类型的识别   bit0：人（区域）  bit1：车（区域）   bit2：人脸  bit3：地铁手势  bit4：人（TTC）  bit5:车（TTC）  bit6:车道线  bit7:交通标志 */
    int cameraInputSignalType = 0;    /* 接入的镜头的输入类型（目前有：微牌的AHD的720P的镜头 和 小松的） */
    int cameraHeight = 0;    /* 相机安装高度   单位：cm */
    /* 复制函数 */
    void copy(CameraType &newCameraType){
        /* 由于这个结构体里面没有用到指针，所以直接用=就好了 */
        cameraId = newCameraType.cameraId;
        installVehicleModel = newCameraType.installVehicleModel;
        installPosition = newCameraType.installPosition;
        cameraOrientation = newCameraType.cameraOrientation;
        detectType = newCameraType.detectType;
        algType = newCameraType.algType;
        adType = newCameraType.adType;
        cameraInputSignalType = newCameraType.cameraInputSignalType;
    };

};


struct BSD_Alarm_EvidenceInfo {
    bool video: 1;
    bool image: 1;
    uint otherEnable: 30;
};

struct Locked_Function {
    bool alarmOut: 1;
    bool detectOut: 1;
    uint otherEnable: 30;
};


struct Vehicle_RealtimeStatus {
    // 电瓶电压
    int batteryVoltage = -1;
    // 发动机转速
    uint16_t engineSpeed = 0xFFFF;
    // 车速
    float speed = -1;
    // 冷却液温度
    uint8_t coolantTemperature = 0xFF;
    // 瞬时油耗
    float instantaneousFuelConsumption = -1;
    // 剩余油量
    uint8_t residualOil = 0xFF;
    // ACC状态
    bool acc = false;
    // 左转灯
    bool turnL = false;
    // 右转灯
    bool turnR = false;
    // 大灯
    bool bigLight = false;
    // 雨刮
    bool wiper = false;
    // 刹车
    bool brake = false;
    // 前车门
    bool frontDoor = false;
    // 后车门
    bool backDoor = false;
    // 倒车信号
    bool reverse = false;
    // G4-MT中镜头切换脚的状态  0：当前为DSM镜头  1：当前为交通灯镜头
    uint8_t MT_Camera_switch = 0;
    // SOS按钮
    bool sosSign = false;
    // 消警按钮
    bool safepassSign = false;
    // 从CAN中读到的安全带状态。0xFF：无效    0x00：安全带未扣上    0x01：安全带已扣上
    uint8_t safebeltStatus = 0xFF;
    // Security开关状态     0：关闭  1：开启      0xFF表示无效
    bool SecurityModeStatus = false;
    // C31开关状态
    bool c3Switch_c31 = false;
    // C32开关状态
    bool c3Switch_c32 = false;
    // C31开关状态
    bool c3Switch_c33 = false;
    // C32开关状态
    bool c3Switch_c34 = false;

};


struct GPSInfo {
    float gpsSpeed = -1.0;
    uint8_t status = 0;
    uint16_t direction = 0xFFFF;
    uint16_t altitude = 0xFFFF;
    double latitude = -1.0;
    double longitude = -1.0;
    uint8_t bcdTime[6] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
};

enum G3_DISPALY_MODE{
    /* 分屏显示两个镜头（切分） */
    G3_DISPALY_MODE_ALL_SLICING_2 = 0,
    /* 全屏显示镜头1 */
    G3_DISPALY_MODE_CAMERA1_FULL,
    /* 全屏显示镜头2 */
    G3_DISPALY_MODE_CAMERA2_FULL,
    /* 什么都不显示 */
    G3_DISPALY_MODE_NOT_SHOW,
    /* R151认证模式 */
    G3_DISPALY_MODE_R151,
    /* R158认证模式 */
    G3_DISPALY_MODE_R158,
    /* R159认证模式 */
    G3_DISPALY_MODE_R159,
    /* R158认证和R159认证同时运行 */
    G3_DISPALY_MODE_R158_AND_R159,
    /* 根据信号切换显示模式 */
    G3_DISPALY_MODE_SWITCH_FOR_SIGNAL,
    /* 根据报警信号切换显示模式 */
    G3_DISPALY_MODE_ALARM_EVENT_SIGNAL,
    /* 分屏显示四个镜头（缩放） */
    G3_DISPALY_MODE_ALL_SLICING_4,
    /* 全屏显示镜头1 */
    G3_DISPALY_MODE_CAMERA3_FULL,
    /* 全屏显示镜头2 */
    G3_DISPALY_MODE_CAMERA4_FULL,
};

/* 镜头报警开关的状态 */
struct Camera_Alarm_Switch_Status {
    /* 镜头报警开关1 */
    bool  cameraAlarmSwitch1 : 1;
    /* 镜头报警开关2 */
    bool  cameraAlarmSwitch2 : 1;
    /* 镜头报警开关3 */
    bool  cameraAlarmSwitch3 : 1;
    /* 预留 */
    uint reserve : 5;
};

/* 镜头报警开关的状态 */
struct Camera_Alarm_Switch_Info {
    /* 镜头1是否可以报警 */
    bool  camera1AlarmEnable : 1;
    /* 镜头2是否可以报警 */
    bool  camera2AlarmEnable : 1;
    /* 预留 */
    uint reserve : 6;
};



/* NDS的MQTT连接参数 */
struct MqttConnectParams{
    /* 是否使用SSL端口 */
    bool useSSL = true;
    /* 域名 */
//    std::string host = "portal.safetyshieldvue.com";
    std::string host = "**************";
    /* 端口 */
//    int port = 43332;
    int port = 10003;
    /* 连接MQTT时使用的用户名 */
    std::string userName;
    /* 连接MQTT时使用的密码 */
    std::string password;
    /* 连接MQTT时使用的clientId */
    std::string clientId;
    /* 连接MQTT的SSL端口时使用的证书路径（目前只做单向校验，校验服务器的证书） */
    std::string crtFilePath = "/userdata/__safetyshieldvue_com.crt";
    /* 订阅从服务器收到消息的订阅字 */
    std::string subTopic = "atmToG2_";
    /* 发送消息给服务器的订阅字 */
    std::string pubTopic = "g2ToAtm";
    /* 发送遗言给服务器的订阅字 */
    std::string willTopic = "g2ToAtm_will";
};

/* MQTT客户端的类型 */
enum VISMQTTCLIENTTYPE{
    /* 用来跟NDS通信的MQTT客户端 */
    VISMQTTCLIENTTYPE_NDS = 0,
};

/* R151的报警输出类型 */
enum R151_ALARM_OUTPUT_MODEL{
    /* 同ID只报一次 */
    SAME_ID_ALARM_ONCE = 0,
    /* 同ID持续报警 */
    SAME_ID_KEEP_ALARM = 1,
};
/* 通过IO口拿到的车辆状态信息 主要是一个信号状态  */
struct VehicleStatusFromIO{
    /* 倒车信号 */
    bool reverse = false;
    /* 左转信号 */
    bool turnL = false;
    /* 右转信号 */
    bool turnR = false;
    /* 侧翻 */
    bool rollover = false;
    /* 开门 */
    bool door = false;
    /* 消警信号 */
    bool safepass = false;
    /* sos信号 */
    bool sos = false;
};
/* 屏幕输出的信号类型 */
enum DISPLAY_SIGNAL_TYPE{
    /* CVBS输出 */
    DISPLAY_SIGNAL_TYPE_CVBS_PAL = 0,
    /* AHD输出 */
    DISPLAY_SIGNAL_TYPE_AHD = 1,
    /* CVBS输出 */
    DISPLAY_SIGNAL_TYPE_CVBS_NTSC = 2,
};

/* GPIO_OUT1 的输出关联的镜头 */
struct IO_OUT_Camera_Enable{
    /* 镜头1 */
    bool camerd_1: 1;
    /* 镜头2 */
    bool camerd_2: 1;
    /* 镜头3 */
    bool camerd_3: 1;
    /* 镜头4 */
    bool camerd_4: 1;
    /* 其他镜头 */
    int camerd_other : 4;
};

/* 接在RS232口上的走modbus协议的按钮的定义 */
enum RS232_MODBUS_BUTTON_TYPE {
    /* 无定义  */
    RS232_MODBUS_BUTTON_TYPE_UNKNOW = 0,
    /* 消警按钮  */
    RS232_MODBUS_BUTTON_TYPE_SAFEPASS = 1,
    /* sos按钮  */
    RS232_MODBUS_BUTTON_TYPE_SOS = 2,
};

struct ByPassInfo{
    /* 镜头1的报警是否需要被pass */
    bool camera1 : 1;
    /* 镜头2的报警是否需要被pass */
    bool camera2 : 1;
    /* 镜头3的报警是否需要被pass */
    bool camera3 : 1;
    /* 镜头4的报警是否需要被pass */
    bool camera4 : 1;
    /* 预留 */
    int other : 4;
};

struct TimeOSDAddEnable{
    /* 镜头1是否叠加时间水印 */
    bool camera1 : 1;
    /* 镜头2是否叠加时间水印 */
    bool camera2 : 1;
    /* 镜头3是否叠加时间水印 */
    bool camera3 : 1;
    /* 镜头4是否叠加时间水印 */
    bool camera4 : 1;
    /* 预留 */
    int other : 4;
};


/* 镜头输入类型的过滤器  （只有被打开的镜头类型允许使用） */
struct Camera_Input_Type_Filter {
    /* 是否允许接入CVBS-P制的镜头 */
    bool cvbs_p : 1;
    /* 是否允许接入CVBS-N制的镜头 */
    bool cvbs_n : 1;
    /* 是否允许接入AHD-720P的镜头 */
    bool ahd_720p : 1;
    /* 是否允许接入AHD-1080P的镜头 */
    bool ahd_1080p : 1;
    /* 预留 */
    uint other : 4;
};

/* 可以控制屏幕显示的信号类型 */
enum DISPALY_SWITCH_SIGNALE_TYPE{
    /* 可以控制屏幕显示的信号类型---未定义 */
    DISPALY_SWITCH_SIGNALE_TYPE_NUNKONW = -1,
    /* 可以控制屏幕显示的信号类型---左转信号 */
    DISPALY_SWITCH_SIGNALE_TYPE_TURN_LEFT = 0,
    /* 可以控制屏幕显示的信号类型---右转信号 */
    DISPALY_SWITCH_SIGNALE_TYPE_TURN_RIGHT = 1,
    /* 可以控制屏幕显示的信号类型---倒车信号 */
    DISPALY_SWITCH_SIGNALE_TYPE_REVERSE = 2,
};

/* 根据信号切换屏幕显示时可以选择的屏幕显示的类型 */
enum DISPALY_SWITCH_DISPLAY_TYPE{
    /* 根据信号切换屏幕显示时可以选择的屏幕显示的类型---分屏 */
    DISPALY_SWITCH_DISPLAY_TYPE_ALL_SLICING = 0,
    /* 根据信号切换屏幕显示时可以选择的屏幕显示的类型---镜头1全屏 */
    DISPALY_SWITCH_DISPLAY_TYPE_FULL_CAMERA1,
    /* 根据信号切换屏幕显示时可以选择的屏幕显示的类型---镜头2全屏 */
    DISPALY_SWITCH_DISPLAY_TYPE_FULL_CAMERA2,
    /* 根据信号切换屏幕显示时可以选择的屏幕显示的类型---镜头3全屏 */
    DISPALY_SWITCH_DISPLAY_TYPE_FULL_CAMERA3,
    /* 根据信号切换屏幕显示时可以选择的屏幕显示的类型---镜头4全屏 */
    DISPALY_SWITCH_DISPLAY_TYPE_FULL_CAMERA4,
};

struct DisplaySwitchForSignalRule{
    /* 信号类型      -1：未定义  0：左转信号   1：右转信息   2：侧翻信号  3：车门  4：倒车 */
    int signalType = -1;
    /* 优先级 范围1~5， 1最低，5最高 */
    int priority = 0;
    /* 显示类型    0：分屏  1：全屏显示镜头1  2：全屏显示镜头2  */
    int dispalyType = 0;
};

/* RS232口的工作模式 */
enum RS232_WORK_MODE{
    /* RS232口的工作模式----停止按钮模式 */
    RS232_WORK_MODE_STOP_BUTTON = 0,
    /* RS232口的工作模式----Q-Control-01模式 */
    RS232_WORK_MODE_Q_CONTROL_01,
};

/* 镜头画面的264数据封装格式 */
enum REALVIEW_H264_DATA_FORMAT{
    /* 镜头画面的264数据封装格式---0：H264裸数据 */
    REALVIEW_H264_DATA_FORMAT_NOMAL = 0,
    /* 镜头画面的264数据封装格式---1：UDP的视频传输协议格式v1.1（具体查看socket通信协议的附录4.6） */
    REALVIEW_H264_DATA_FORMAT_V11,
};

struct CameraAlgWorkRule_SignalSet {
    /* 左转信号 */
    bool turnL : 1;
    /* 右转信号 */
    bool turnR : 1;
    /* 车门 */
    bool door : 1;
    /* 倒车 */
    bool reverse : 1;
    /* 其他预留 */
    int other : 12;
};

/* 相机算法工作规则 */
struct CameraAlgWorkRule{
    /* 相机ID */
    CAMERAIDLIST cameraId = CAMERA_ID_UNKNOW;
    /* 需要收到什么信号才可以工作 */
    CameraAlgWorkRule_SignalSet workSignal = {};
    /* 当收到什么信号时需要暂停工作 */
    CameraAlgWorkRule_SignalSet pauseSignal = {};
};

/* 镜头算法需要降效的信息 */
struct CameraReduceEffectInfo {
    /* 相机1是否需要降低效率 */
    bool camera1 : 1;
    /* 相机2是否需要降低效率 */
    bool camera2 : 1;
    /* 相机3是否需要降低效率 */
    bool camera3 : 1;
    /* 相机4是否需要降低效率 */
    bool camera4 : 1;
    /* 预留 */
    int other : 4;
};
/* 核心板类型 */
enum COREBOARDTYPE{
    COREBOARDTYPE_MRV220 = 0,
    COREBOARDTYPE_MRV220K,
    COREBOARDTYPE_MRV240,
};
/* C3的ID */
enum C3ID{
    /* C3ID---C3_1（接在镜头1口的C3） */
    C3ID_1 = 0,
    /* C3ID---C3_2（接在镜头2口的C3） */
    C3ID_2,
    /* C3ID---C3_3（接在镜头3口的C3） */
    C3ID_3,
    /* C3ID---C3_4（接在镜头4口的C3） */
    C3ID_4,
};
/* C3的切换规则 */
struct C3SwitchRule{
    /* C3的ID      -1：未定义  0：C3_1（接在镜头1口的C3）  1：C3_2（接在镜头2口的C3）  2：C3_3（接在镜头3口的C3）  3：C3_4（接在镜头4口的C3） */
    int c3Id = -1;
    /* C3的状态为ON的时候使用的镜头是哪个？    0：镜头1  1：镜头2  2：镜头3  3：镜头4 */
    int cameraForEnable = 0;
};

/* g4-mini的算法串行喂图像规则 */
enum G4MINI_ALG_SERIAL_RULE{
    /* g4-mini的算法串行喂图像规则---规则1（1、2、1、3） */
    G4MINI_ALG_SERIAL_RULE_1 = 0,
    /* g4-mini的算法串行喂图像规则---规则2（1、2、3、1、2、3） */
    G4MINI_ALG_SERIAL_RULE_2,
};

/* 需要录制的报警证据类型 */
struct AlarmEvidenceTypeList{
    /* 需要录制的报警证据类型----是否需要录制视频 */
    bool video : 1;
    /* 需要录制的报警证据类型----是否需要录制图片 */
    bool photo : 1;
    /* 需要录制的报警证据类型----预留 */
    uint others : 6;
};

/* 需要连接的云平台类型 */
struct CloundPlatformTypeList{
    /* 需要连接的云平台类型----是否需要连接NDS */
    bool nds : 1;
    /* 需要连接的云平台类型----预留 */
    uint others : 7;
};

/* 需要添加识别信息的报警证据类型 */
struct AddDetectInfoAlarmEvidenceTypes{
    /* 需要添加识别信息的报警证据类型----是否添加到视频里 */
    bool video : 1;
    /* 需要添加识别信息的报警证据类型----是否添加到图片里 */
    bool photo : 1;
    /* 需要录制的报警证据类型----预留 */
    uint others : 6;
};

/* 镜头算法需要执行的信息 */
struct AlgSerialRuleItem {
    /* 镜头1是否需要执行 */
    bool camera1 : 1;
    /* 镜头2是否需要执行 */
    bool camera2 : 1;
    /* 镜头3是否需要执行 */
    bool camera3 : 1;
    /* 镜头4是否需要执行 */
    bool camera4 : 1;
    /* 预留 */
    int other : 4;
};

/* IO 输出口对应的IO号 */
enum IO_OUTPUT_NUM{
    /* IO 输出口1对应的IO号 */
    IO_OUT_1_NUM = 105,
    /* IO 输出口2对应的IO号 */
    IO_OUT_2_NUM = 108,
};

/* IO 输出口对应的IO号 */
enum IO_INPUT_NUM{
    /* IO 输入口1对应的IO号 */
    IO_IN_1_NUM = 115,
    /* IO 输入口2对应的IO号 */
    IO_IN_2_NUM = 111,
    /* IO 输入口3对应的IO号 */
    IO_IN_3_NUM = 110,
    /* IO 未知输入口对应的IO号 */
    IO_IN_UNKNOW_NUM = 113,
};

/* 镜头图像来源 */
enum CAMERA_IMAGE_SOURCE{
    /* 镜头图像来源---模拟信号镜头（AHD\CVBS） */
    CAMERA_IMAGE_SOURCE_ANALOG_SIGNAL_CAMERA = 0,
    /* 镜头图像来源---MP4文件 */
    CAMERA_IMAGE_SOURCE_MP4FILE,
    /* 镜头图像来源---德国小松的IPC */
    CAMERA_IMAGE_SOURCE_DE_KOM_AVTP_IPC,

};

class G3_Configuration {
public:

    /* 配置表的文件路径  （目前使用相对路径，就是跟程序在同个目录） */
    const char *CONFIG_FILE_PATH= "conf.yaml";
    /* MUV特定的配置表的路径 */
    const char *MUV_CONFIG_FILE_PATH = "/userdata/model/muv_dsm_deconfig.yml";
    /* 记录UUID的文件的路径 */
    const char *UUID_FILE_PATH = "/userdata/uuid";
    /* 记录功能锁的文件路径  （目前使用相对路径，就是跟程序在同个目录） */
    const char *FUNCTIONE_LOCK_FILE_PATH = "FunctionLock";



    G3_Configuration();

    ~G3_Configuration();

    /* 单例 */
    static G3_Configuration &getInstance();

    /* 从文件中读取配置表 */
    int loadFromFile();

    /* 从string中读取配置表 */
    int loadFromStr(const char *str, int len);

    /* 把配置项保存成文件 */
    int saveToFile();

    /* 保存MUV的DSM算法用的配置文件 */
    int saveMUVDSMConfig();

    /**
     * 获取镜头对应的算法类型
     *
     * @param cameraId : 相机ID
     *
     * @return 算法ID   -1：无法获取到算法ID
     * */
    int getAlgorithmTypeOfCamera(int cameraId);

    /**
     * 获取镜头对应的镜头类型信息
     *
     * @param cameraType ： 存放镜头类型的结构体
     * @param cameraId : 相机ID
     *
     * @return 是否获取到了
     * */
    bool getCameraTypeInfoOfCamera(CameraType &cameraType, int cameraId);


    /* 获取string格式的配置表 */
    std::string getConfigurationStr() const;


    int getGpioIn1Type() const;

    void setGpioIn1Type(int gpioIn1Type);

    int getGpioIn2Type() const;

    void setGpioIn2Type(int gpioIn2Type);

    int getGpioIn3Type() const;

    void setGpioIn3Type(int gpioIn3Type);

    int getSpeakerVolume() const;

    void setSpeakerVolume(int speakerVolume);

    int getRespiratorSpeed() const;

    void setRespiratorSpeed(int respiratorSpeed);

    int getRespiratorButton() const;

    void setRespiratorButton(int respiratorButton);

    int getRespiratorMode() const;

    void setRespiratorMode(int respiratorMode);

    int getRespiratorSensitivity() const;

    void setRespiratorSensitivity(int respiratorSensitivity);

    int getEventRespiratorTime() const;

    void setEventRespiratorTime(int eventRespiratorTime);

    int getFreezeRespiratorTime() const;

    void setFreezeRespiratorTime(int freezeRespiratorTime);

    int getEyeSpeed() const;

    void setEyeSpeed(int eyeSpeed);

    int getEyeButton() const;

    void setEyeButton(int eyeButton);

    int getEyeMode() const;

    void setEyeMode(int eyeMode);

    int getEyeSensitivity() const;

    void setEyeSensitivity(int eyeSensitivity);

    int getEventEyeTime() const;

    void setEventEyeTime(int eventEyeTime);

    int getFreezeEyeTime() const;

    void setFreezeEyeTime(int freezeEyeTime);

    int getMouthSpeed() const;

    void setMouthSpeed(int mouthSpeed);

    int getMouthButton() const;

    void setMouthButton(int mouthButton);

    int getMouthMode() const;

    void setMouthMode(int mouthMode);

    int getMouthSensitivity() const;

    void setMouthSensitivity(int mouthSensitivity);

    int getEventMouthTime() const;

    void setEventMouthTime(int eventMouthTime);

    int getFreezeMouthTime() const;

    void setFreezeMouthTime(int freezeMouthTime);

    int getLookaroundSpeed() const;

    void setLookaroundSpeed(int lookaroundSpeed);

    int getLookaroundButton() const;

    void setLookaroundButton(int lookaroundButton);

    int getLookaroundMode() const;

    void setLookaroundMode(int lookaroundMode);

    int getLookaroundSensitivity() const;

    void setLookaroundSensitivity(int lookaroundSensitivity);

    int getEventLookaroundTime() const;

    void setEventLookaroundTime(int eventLookaroundTime);

    int getFreezeLookaroundTime() const;

    void setFreezeLookaroundTime(int freezeLookaroundTime);

    int getFacemissingSpeed() const;

    void setFacemissingSpeed(int facemissingSpeed);

    int getFacemissingButton() const;

    void setFacemissingButton(int facemissingButton);

    int getFacemissingMode() const;

    void setFacemissingMode(int facemissingMode);

    int getFacemissingSensitivity() const;

    void setFacemissingSensitivity(int facemissingSensitivity);

    int getEventFacemissingTime() const;

    void setEventFacemissingTime(int eventFacemissingTime);

    int getFreezeFacemissingTime() const;

    void setFreezeFacemissingTime(int freezeFacemissingTime);

    int getCamcoverSpeed() const;

    void setCamcoverSpeed(int camcoverSpeed);

    int getCamcoverButton() const;

    void setCamcoverButton(int camcoverButton);

    int getCamcoverMode() const;

    void setCamcoverMode(int camcoverMode);

    int getCamcoverSensitivity() const;

    void setCamcoverSensitivity(int camcoverSensitivity);

    int getEventCamcoverTime() const;

    void setEventCamcoverTime(int eventCamcoverTime);

    int getFreezeCamcoverTime() const;

    void setFreezeCamcoverTime(int freezeCamcoverTime);

    int getSmokingSpeed() const;

    void setSmokingSpeed(int smokingSpeed);

    int getSmokingButton() const;

    void setSmokingButton(int smokingButton);

    int getSmokingMode() const;

    void setSmokingMode(int smokingMode);

    int getSmokingSensitivity() const;

    void setSmokingSensitivity(int smokingSensitivity);

    int getEventSmokingTime() const;

    void setEventSmokingTime(int eventSmokingTime);

    int getFreezeSmokingTime() const;

    void setFreezeSmokingTime(int freezeSmokingTime);

    int getPhoneSpeed() const;

    void setPhoneSpeed(int phoneSpeed);

    int getPhoneButton() const;

    void setPhoneButton(int phoneButton);

    int getPhoneMode() const;

    void setPhoneMode(int phoneMode);

    int getPhoneSensitivity() const;

    void setPhoneSensitivity(int phoneSensitivity);

    int getEventPhoneTime() const;

    void setEventPhoneTime(int eventPhoneTime);

    int getFreezePhoneTime() const;

    void setFreezePhoneTime(int freezePhoneTime);

    const std::vector<CameraType> &getCameraTypeList() const;

    void setCameraTypeList(const std::vector<CameraType> &cameraTypeList);

    const std::vector<VISPoint> &getCamera0PedestrianAlramAreaLevel1() const;

    void setCamera0PedestrianAlramAreaLevel1(
            const std::vector<VISPoint> &camera0PedestrianAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera0PedestrianAlramAreaLevel2() const;

    void setCamera0PedestrianAlramAreaLevel2(
            const std::vector<VISPoint> &camera0PedestrianAlramAreaLevel2);

    const std::vector<VISPoint> &getCamera1PedestrianAlramAreaLevel1() const;

    void setCamera1PedestrianAlramAreaLevel1(
            const std::vector<VISPoint> &camera1PedestrianAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera1PedestrianAlramAreaLevel2() const;

    void setCamera1PedestrianAlramAreaLevel2(
            const std::vector<VISPoint> &camera1PedestrianAlramAreaLevel2);

    const std::vector<VISPoint> &getCamera0VehicleAlramAreaLevel1() const;

    void setCamera0VehicleAlramAreaLevel1(
            const std::vector<VISPoint> &camera0VehicleAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera0VehicleAlramAreaLevel2() const;

    void setCamera0VehicleAlramAreaLevel2(
            const std::vector<VISPoint> &camera0VehicleAlramAreaLevel2);

    const std::vector<VISPoint> &getCamera1VehicleAlramAreaLevel1() const;

    void setCamera1VehicleAlramAreaLevel1(
            const std::vector<VISPoint> &camera1VehicleAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera1VehicleAlramAreaLevel2() const;

    void setCamera1VehicleAlramAreaLevel2(
            const std::vector<VISPoint> &camera1VehicleAlramAreaLevel2);

    int getBsdMinAlramSpeedLevel1() const;

    void setBsdMinAlramSpeedLevel1(int bsdMinAlramSpeedLevel1);

    int getBsdMaxAlramSpeedLevel1() const;

    void setBsdMaxAlramSpeedLevel1(int bsdMaxAlramSpeedLevel1);

    int getBsdMinAlramSpeedLevel2() const;

    void setBsdMinAlramSpeedLevel2(int bsdMinAlramSpeedLevel2);

    int getBsdMaxAlramSpeedLevel2() const;

    void setBsdMaxAlramSpeedLevel2(int bsdMaxAlramSpeedLevel2);

    int getBsdAddDetectionInfo() const;

    void setBsdAddDetectionInfo(int bsdAddDetectionInfo);

    int getBsdAlarmLevel1SpeakerEnable() const;

    void setBsdAlarmLevel1SpeakerEnable(int bsdAlarmLevel1SpeakerEnable);

    int getBsdAlarmLevel1SendtoRs485Enable() const;

    void setBsdAlarmLevel1SendtoRs485Enable(int bsdAlarmLevel1SendtoRs485Enable);

    int getBsdAlarmLevel2SpeakerEnable() const;

    void setBsdAlarmLevel2SpeakerEnable(int bsdAlarmLevel2SpeakerEnable);

    int getBsdAlarmLevel2SendtoRs485Enable() const;

    void setBsdAlarmLevel2SendtoRs485Enable(int bsdAlarmLevel2SendtoRs485Enable);

    ByPassInfo getBsdCameraBypassInfo();

    void setBsdCameraBypassInfo(int bsdCameraBypassInfo);

    int getG3DeviceWorkingMode() const;

    void setG3DeviceWorkingMode(int g3DeviceWorkingMode);

    const std::string &getLocalTimeZone() const;

    void setLocalTimeZone(const std::string &localTimeZone);

    int getGpioOut1OuputType() const;

    void setGpioOut1OuputType(int gpioOut1OuputType);

    int getGpioOut2OuputType() const;

    void setGpioOut2OuputType(int gpioOut2OuputType);

    int getSpeakerSoundLanguage() const;

    void setSpeakerSoundLanguage(int speakerSoundLanguage);

    BSD_Alarm_EvidenceInfo getBsdAlarmLevel1Evidence() const;

    void setBsdAlarmLevel1Evidence(int bsdAlarmLevel1Evidence);

    BSD_Alarm_EvidenceInfo getBsdAlarmLevel2Evidence() const;

    void setBsdAlarmLevel2Evidence(int bsdAlarmLevel2Evidence);

    int getSlotPosition() const;

    void setSlotPosition(int slotPosition);

    const std::string &getCpuSerialNumber() const;

    void setCpuSerialNumber(const std::string &cpuSerialNumber);

    const std::string &getDeviceUuid() const;

    void setDeviceUuid(const std::string &deviceUuid);

    IO_OUT_Alarm_Type getOutPut1AlarmType();

    IO_OUT_Alarm_Type getOutPut2AlarmType();

    int getBsdSameIdAlramTypeLevel1() const;

    void setBsdSameIdAlramTypeLevel1(int bsdSameIdAlramTypeLevel1);

    int getBsdSameIdAlramTypeLevel2() const;

    void setBsdSameIdAlramTypeLevel2(int bsdSameIdAlramTypeLevel2);

    BSD_Alarm_Trend_Shielding getBsdAlarmTrendShieldingLevel1() const;

    void setBsdAlarmTrendShieldingLevel1(int bsdAlarmTrendShieldingLevel1);

    BSD_Alarm_Trend_Shielding getBsdAlarmTrendShieldingLevel2() const;

    void setBsdAlarmTrendShieldingLevel2(int bsdAlarmTrendShieldingLevel2);

    const std::string &getMrv220IpAdress() const;

    void setMrv220IpAdress(const std::string &mrv220IpAdress);

    int getCam1RealviewPort() const;

    void setCam1RealviewPort(int cam1RealviewPort);

    int getCam2RealviewPort() const;

    void setCam2RealviewPort(int cam2RealviewPort);

    const Locked_Function &getLockFunction() const;

    void setLockFunction(const Locked_Function &lockFunction);

    int getBsdAlarmLevel1SpeakerRingingModel() const;

    void
    setBsdAlarmLevel1SpeakerRingingModel(int bsdAlarmLevel1SpeakerRingingModel);

    int getBsdAlarmLevel2SpeakerRingingModel() const;

    void
    setBsdAlarmLevel2SpeakerRingingModel(int bsdAlarmLevel2SpeakerRingingModel);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaLevel1Left() const;

    void setSbstVehicleAlramAreaLevel1Left(
            const std::vector<VISPoint> &sbstVehicleAlramAreaLevel1Left);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaLevel2Left() const;

    void setSbstVehicleAlramAreaLevel2Left(
            const std::vector<VISPoint> &sbstVehicleAlramAreaLevel2Left);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaLevel1Right() const;

    void setSbstVehicleAlramAreaLevel1Right(
            const std::vector<VISPoint> &sbstVehicleAlramAreaLevel1Right);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaLevel2Right() const;

    void setSbstVehicleAlramAreaLevel2Right(
            const std::vector<VISPoint> &sbstVehicleAlramAreaLevel2Right);

    int getSbstForwardPedestrianMinAlramSpeedLevel1() const;

    void setSbstForwardPedestrianMinAlramSpeedLevel1(
            int sbstForwardPedestrianMinAlramSpeedLevel1);

    int getSbstForwardPedestrianMaxAlramSpeedLevel1() const;

    void setSbstForwardPedestrianMaxAlramSpeedLevel1(
            int sbstForwardPedestrianMaxAlramSpeedLevel1);

    int getSbstForwardPedestrianMinAlramSpeedLevel2() const;

    void setSbstForwardPedestrianMinAlramSpeedLevel2(
            int sbstForwardPedestrianMinAlramSpeedLevel2);

    int getSbstForwardPedestrianMaxAlramSpeedLevel2() const;

    void setSbstForwardPedestrianMaxAlramSpeedLevel2(
            int sbstForwardPedestrianMaxAlramSpeedLevel2);

    int getSbstForwardVehicleMinAlramSpeedLevel1() const;

    void setSbstForwardVehicleMinAlramSpeedLevel1(
            int sbstForwardVehicleMinAlramSpeedLevel1);

    int getSbstForwardVehicleMaxAlramSpeedLevel1() const;

    void setSbstForwardVehicleMaxAlramSpeedLevel1(
            int sbstForwardVehicleMaxAlramSpeedLevel1);

    int getSbstForwardVehicleMinAlramSpeedLevel2() const;

    void setSbstForwardVehicleMinAlramSpeedLevel2(
            int sbstForwardVehicleMinAlramSpeedLevel2);

    int getSbstForwardVehicleMaxAlramSpeedLevel2() const;

    void setSbstForwardVehicleMaxAlramSpeedLevel2(
            int sbstForwardVehicleMaxAlramSpeedLevel2);

    int getSbstSidewardPedestrianMinAlramSpeedLevel1() const;

    void setSbstSidewardPedestrianMinAlramSpeedLevel1(
            int sbstSidewardPedestrianMinAlramSpeedLevel1);

    int getSbstSidewardPedestrianMaxAlramSpeedLevel1() const;

    void setSbstSidewardPedestrianMaxAlramSpeedLevel1(
            int sbstSidewardPedestrianMaxAlramSpeedLevel1);

    int getSbstSidewardPedestrianMinAlramSpeedLevel2() const;

    void setSbstSidewardPedestrianMinAlramSpeedLevel2(
            int sbstSidewardPedestrianMinAlramSpeedLevel2);

    int getSbstSidewardPedestrianMaxAlramSpeedLevel2() const;

    void setSbstSidewardPedestrianMaxAlramSpeedLevel2(
            int sbstSidewardPedestrianMaxAlramSpeedLevel2);

    int getSbstSidewardVehicleMinAlramSpeedLevel1() const;

    void setSbstSidewardVehicleMinAlramSpeedLevel1(
            int sbstSidewardVehicleMinAlramSpeedLevel1);

    int getSbstSidewardVehicleMaxAlramSpeedLevel1() const;

    void setSbstSidewardVehicleMaxAlramSpeedLevel1(
            int sbstSidewardVehicleMaxAlramSpeedLevel1);

    int getSbstSidewardVehicleMinAlramSpeedLevel2() const;

    void setSbstSidewardVehicleMinAlramSpeedLevel2(
            int sbstSidewardVehicleMinAlramSpeedLevel2);

    int getSbstSidewardVehicleMaxAlramSpeedLevel2() const;

    void setSbstSidewardVehicleMaxAlramSpeedLevel2(
            int sbstSidewardVehicleMaxAlramSpeedLevel2);

    int getDefaultSpeed() const;

    void setDefaultSpeed(int defaultSpeed);

    int getH264BitRateLevel() const;

    void setH264BitRateLevel(int h264BitRateLevel);

    int getSbstForwardLdwMinAlramSpeed() const;

    void setSbstForwardLdwMinAlramSpeed(int sbstForwardLdwMinAlramSpeed);

    int getSbstForwardLdwMaxAlramSpeed() const;

    void setSbstForwardLdwMaxAlramSpeed(int sbstForwardLdwMaxAlramSpeed);

    int getSbstSidewardAlarmNeedTurnLight() const;

    void setSbstSidewardAlarmNeedTurnLight(int sbstSidewardAlarmNeedTurnLight);

    static void yamlNodeToVisPoint(std::vector<VISPoint> &point,
                                   const YAML::Node &node);

    static void yamlNodeToVisPoint(std::vector<VISPoint> &point,
                                   const YAML::Node &node, bool &needReboot);

    static YAML::Node visPointToYamlNode(std::vector<VISPoint> &point);

    const std::vector<VISPoint> *getUndetectedAreaListCamera1() const;

    const std::vector<VISPoint> *getUndetectedAreaListCamera2() const;

    const std::vector<VISPoint> &
    getSbstVehicleAlramAreaCamera1TurnleftAngle1Level1() const;

    const std::vector<VISPoint> &
    getSbstVehicleAlramAreaCamera1TurnleftAngle1Level2() const;

    const std::vector<VISPoint> &
    getSbstVehicleAlramAreaCamera1TurnrightAngle1Level1() const;

    const std::vector<VISPoint> &
    getSbstVehicleAlramAreaCamera1TurnrightAngle1Level2() const;

    const std::vector<VISPoint> &
    getSbstPedestrianAlramAreaCamera1TurnleftAngle1Level1() const;

    const std::vector<VISPoint> &
    getSbstPedestrianAlramAreaCamera1TurnleftAngle1Level2() const;

    const std::vector<VISPoint> &
    getSbstPedestrianAlramAreaCamera1TurnrightAngle1Level1() const;

    const std::vector<VISPoint> &
    getSbstPedestrianAlramAreaCamera1TurnrightAngle1Level2() const;

    const std::vector<VISPoint> &
    getSbstVehicleAlramAreaCamera2TurnleftAngle1Level1() const;

    const std::vector<VISPoint> &
    getSbstVehicleAlramAreaCamera2TurnleftAngle1Level2() const;

    const std::vector<VISPoint> &
    getSbstVehicleAlramAreaCamera2TurnrightAngle1Level1() const;

    const std::vector<VISPoint> &
    getSbstVehicleAlramAreaCamera2TurnrightAngle1Level2() const;

    const std::vector<VISPoint> &
    getSbstPedestrianAlramAreaCamera2TurnleftAngle1Level1() const;

    const std::vector<VISPoint> &
    getSbstPedestrianAlramAreaCamera2TurnleftAngle1Level2() const;

    const std::vector<VISPoint> &
    getSbstPedestrianAlramAreaCamera2TurnrightAngle1Level1() const;

    const std::vector<VISPoint> &
    getSbstPedestrianAlramAreaCamera2TurnrightAngle1Level2() const;

    int getSbstChangePedestrianAlramAreaByTurnlightMinSpeed() const;

    int getSbstChangePedestrianAlramAreaByTurnlightMaxSpeed() const;

    int getConfigVersionCode() const;

    void setConfigVersionCode(int configVersionCode);

    int getPedestrianAlarmNeedTravelableArea() const;

    void setPedestrianAlarmNeedTravelableArea(int pedestrianAlarmNeedTravelableArea);

    int getVehicleAlarmNeedTravelableArea() const;

    void setVehicleAlarmNeedTravelableArea(int vehicleAlarmNeedTravelableArea);

    int getPedestrianAlarmNeedChangeByTurnSignCamera0() const;

    void setPedestrianAlarmNeedChangeByTurnSignCamera0(int pedestrianAlarmNeedChangeByTurnSignCamera0);

    int getPedestrianAlarmNeedChangeByTurnSignCamera1() const;

    void setPedestrianAlarmNeedChangeByTurnSignCamera1(int pedestrianAlarmNeedChangeByTurnSignCamera1);

    int getPedestrianAlarmNeedDoorSign() const;

    void setPedestrianAlarmNeedDoorSign(int pedestrianAlarmNeedDoorSign);

    int getVehicleAlarmNeedChangeByTurnSignCamera0() const;

    void setVehicleAlarmNeedChangeByTurnSignCamera0(int vehicleAlarmNeedChangeByTurnSignCamera0);

    int getVehicleAlarmNeedChangeByTurnSignCamera1() const;

    void setVehicleAlarmNeedChangeByTurnSignCamera1(int vehicleAlarmNeedChangeByTurnSignCamera1);

    int getSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera0() const;

    void
    setSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera0(int sbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera0);

    int getSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera0() const;

    void
    setSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera0(int sbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera0);

    int getSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera1() const;

    void
    setSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera1(int sbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera1);

    int getSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera1() const;

    void
    setSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera1(int sbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera1);

    int getAdasVehicleLdwAlarmMinSpeed() const;

    void setAdasVehicleLdwAlarmMinSpeed(int adasVehicleLdwAlarmMinSpeed);

    int getAdasVehicleLdwAlarmSensitivity() const;

    void setAdasVehicleLdwAlarmSensitivity(int adasVehicleLdwAlarmSensitivity);

    int getG3DisplayMode() const;

    void setG3DisplayMode(int g3DisplayMode);

    const Camera_Alarm_Switch_Status &getCameraAlarmSwitchStatus() const;

    void setCameraAlarmSwitchStatus(const Camera_Alarm_Switch_Status &cameraAlarmSwitchStatus);

    int getCameraAlarmNeedCameraAlarmSwitch() const;

    void setCameraAlarmNeedCameraAlarmSwitch(int cameraAlarmNeedCameraAlarmSwitch);

    const Camera_Alarm_Switch_Info &getCameraAlarmSwitch1() const;

    void setCameraAlarmSwitch1(const Camera_Alarm_Switch_Info &cameraAlarmSwitch1);

    const Camera_Alarm_Switch_Info &getCameraAlarmSwitch2() const;

    void setCameraAlarmSwitch2(const Camera_Alarm_Switch_Info &cameraAlarmSwitch2);

    const Camera_Alarm_Switch_Info &getCameraAlarmSwitch3() const;

    void setCameraAlarmSwitch3(const Camera_Alarm_Switch_Info &cameraAlarmSwitch3);

    uint8_t getMachineryActionBarsStatus() const;

    void setMachineryActionBarsStatus(uint8_t machineryActionBarsStatus);

    int getG3NotDisplaySpeed() const;

    void setG3NotDisplaySpeed(int g3NotDisplaySpeed);

    int getReverseDispalyCameraId() const;

    void setReverseDispalyCameraId(int reverseDispalyCameraId);

    int getAdasFcwStartSpeed() const;

    void setAdasFcwStartSpeed(int adasFcwStartSpeed);

    int getAdasLdwStartSpeed() const;

    void setAdasLdwStartSpeed(int adasLdwStartSpeed);

    int getAdasPdwStopSpeed() const;

    void setAdasPdwStopSpeed(int adasPdwStopSpeed);

    int getAdasCameraHeight() const;

    void setAdasCameraHeight(int adasCameraHeight);

    int getAdasVehicleWidth() const;

    void setAdasVehicleWidth(int adasVehicleWidth);

    int getAdasCameraDepth() const;

    void setAdasCameraDepth(int adasCameraDepth);

    int getAdasCameraCenterlineOffset() const;

    void setAdasCameraCenterlineOffset(int adasCameraCenterlineOffset);

    int getAdasCameraFrontWheelAxleDistance() const;

    void setAdasCameraFrontWheelAxleDistance(int adasCameraFrontWheelAxleDistance);

    int getAdasHorizonY() const;

    void setAdasHorizonY(int adasHorizonY);

    int getAdasTtcThresholdLevel1() const;

    void setAdasTtcThresholdLevel1(int adasTtcThresholdLevel1);

    int getAdasTtcThresholdLevel2() const;

    void setAdasTtcThresholdLevel2(int adasTtcThresholdLevel2);

    int getAdasTtcThresholdLevel3() const;

    void setAdasTtcThresholdLevel3(int adasTtcThresholdLevel3);

    int getAdasTtcThresholdLevel4() const;

    void setAdasTtcThresholdLevel4(int adasTtcThresholdLevel4);

    int getAdasHeadwayThresholdLevel1() const;

    void setAdasHeadwayThresholdLevel1(int adasHeadwayThresholdLevel1);

    int getAdasHeadwayThresholdLevel2() const;

    void setAdasHeadwayThresholdLevel2(int adasHeadwayThresholdLevel2);

    int getAdasHeadwayThresholdLevel3() const;

    void setAdasHeadwayThresholdLevel3(int adasHeadwayThresholdLevel3);

    int getAdasHeadwayThresholdLevel4() const;

    void setAdasHeadwayThresholdLevel4(int adasHeadwayThresholdLevel4);

    int getAdasPdCollisonThresholdLevel1() const;

    void setAdasPdCollisonThresholdLevel1(int adasPdCollisonThresholdLevel1);

    int getAdasPdCollisonThresholdLevel2() const;

    void setAdasPdCollisonThresholdLevel2(int adasPdCollisonThresholdLevel2);

    int getAdasPdCollisonThresholdLevel3() const;

    void setAdasPdCollisonThresholdLevel3(int adasPdCollisonThresholdLevel3);

    int getAdasPdCollisonThresholdLevel4() const;

    void setAdasPdCollisonThresholdLevel4(int adasPdCollisonThresholdLevel4);

    int getAdasLdwDistanceThresholdLevel1() const;

    void setAdasLdwDistanceThresholdLevel1(int adasLdwDistanceThresholdLevel1);

    int getAdasLdwDistanceThresholdLevel2() const;

    void setAdasLdwDistanceThresholdLevel2(int adasLdwDistanceThresholdLevel2);

    int getAdasLdwDistanceThresholdLevel3() const;

    void setAdasLdwDistanceThresholdLevel3(int adasLdwDistanceThresholdLevel3);

    int getAdasLdwDistanceThresholdLevel4() const;

    void setAdasLdwDistanceThresholdLevel4(int adasLdwDistanceThresholdLevel4);

    int getAdasVbDistanceThreshold() const;

    void setAdasVbDistanceThreshold(int adasVbDistanceThreshold);

    int getAdasGoThreshold() const;

    void setAdasGoThreshold(int adasGoThreshold);

    float getTdClassThreshold() const;

    void setTdClassThreshold(float tdClassThreshold);

    float getTdClassThresholdPerson() const;

    void setTdClassThresholdPerson(float tdClassThresholdPerson);

    bool isVisPtmModel() const;

    void setVisPtmModel(bool visPtmModel);

    int getAdasOriWidth() const;

    void setAdasOriWidth(int adasAdasOriWidth);

    int getAdasOriHeight() const;

    void setAdasOriHeight(int adasAdasOriHeight);

    double getAdasFx() const;

    void setAdasFx(double adasFx);

    double getAdasFy() const;

    void setAdasFy(double adasFy);

    double getAdasU0() const;

    void setAdasU0(double adasU0);

    double getAdasV0() const;

    void setAdasV0(double adasV0);

    double getAdasK1() const;

    void setAdasK1(double adasK1);

    double getAdasK2() const;

    void setAdasK2(double adasK2);

    double getAdasP1() const;

    void setAdasP1(double adasP1);

    double getAdasP2() const;

    void setAdasP2(double adasP2);

    double getAdasK3() const;

    void setAdasK3(double adasK3);

    double getAdasM00() const;

    void setAdasM00(double adasM00);

    double getAdasM01() const;

    void setAdasM01(double adasM01);

    double getAdasM02() const;

    void setAdasM02(double adasM02);

    double getAdasM03() const;

    void setAdasM03(double adasM03);

    double getAdasM10() const;

    void setAdasM10(double adasM10);

    double getAdasM11() const;

    void setAdasM11(double adasM11);

    double getAdasM12() const;

    void setAdasM12(double adasM12);

    double getAdasM13() const;

    void setAdasM13(double adasM13);

    double getAdasM20() const;

    void setAdasM20(double adasM20);

    double getAdasM21() const;

    void setAdasM21(double adasM21);

    double getAdasM22() const;

    void setAdasM22(double adasM22);

    double getAdasM23() const;

    void setAdasM23(double adasM23);

    double getAdasM30() const;

    void setAdasM30(double adasM30);

    double getAdasM31() const;

    void setAdasM31(double adasM31);

    double getAdasM32() const;

    void setAdasM32(double adasM32);

    double getAdasM33() const;

    void setAdasM33(double adasM33);

    int getAdasVerticalLine() const;

    void setAdasVerticalLine(int adasVerticalLine);

    int getCameraUdpPort1() const;

    void setCameraUdpPort1(int cameraUdpPort1);

    int getCameraUdpPort2() const;

    void setCameraUdpPort2(int cameraUdpPort2);

    const MqttConnectParams &getNdsMqttConnectParams() const;

    void setNdsMqttConnectParams(const MqttConnectParams &ndsMqttConnectParams);

    const std::string &getLockstr() const;

    void setLockstr(const std::string &lockstr);

    const GPSInfo &getCurGpsInfo() const;

    void setCurGpsInfo(const GPSInfo &curGpsInfo);

    const std::string &getNdsMqttHost() const;

    void setNdsMqttHost(const std::string &ndsMqttHost);

    int getNdsMqttPort() const;

    void setNdsMqttPort(int ndsMqttPort);

    const std::string &getNdsHttpHost() const;

    void setNdsHttpHost(const std::string &ndsHttpHost);

    int getNdsHttpPort() const;

    void setNdsHttpPort(int ndsHttpPort);

    bool isHasTfCard() const;

    void setHasTfCard(bool hasTfCard);

    bool isHasHightTemperature() const;

    void setHasHightTemperature(bool hasHightTemperature);

    const std::vector<VISPoint> &getCamera1R151PedestrianAlramArea1() const;

    void setCamera1R151PedestrianAlramArea1(const std::vector<VISPoint> &camera1R151PedestrianAlramArea1);

    const std::vector<VISPoint> &getCamera1R151PedestrianAlramArea2() const;

    void setCamera1R151PedestrianAlramArea2(const std::vector<VISPoint> &camera1R151PedestrianAlramArea2);

    const std::vector<VISPoint> &getCamera1R151PedestrianAlramArea3() const;

    void setCamera1R151PedestrianAlramArea3(const std::vector<VISPoint> &camera1R151PedestrianAlramArea3);

    const std::vector<VISPoint> &getCamera1R151PedestrianAlramArea4() const;

    void setCamera1R151PedestrianAlramArea4(const std::vector<VISPoint> &camera1R151PedestrianAlramArea4);

    const std::vector<VISPoint> &getCamera1R151VehicleAlramArea1() const;

    void setCamera1R151VehicleAlramArea1(const std::vector<VISPoint> &camera1R151VehicleAlramArea1);

    const std::vector<VISPoint> &getCamera1R151VehicleAlramArea2() const;

    void setCamera1R151VehicleAlramArea2(const std::vector<VISPoint> &camera1R151VehicleAlramArea2);

    const std::vector<VISPoint> &getCamera2R151PedestrianAlramArea1() const;

    void setCamera2R151PedestrianAlramArea1(const std::vector<VISPoint> &camera2R151PedestrianAlramArea1);

    const std::vector<VISPoint> &getCamera2R151PedestrianAlramArea2() const;

    void setCamera2R151PedestrianAlramArea2(const std::vector<VISPoint> &camera2R151PedestrianAlramArea2);

    const std::vector<VISPoint> &getCamera2R151PedestrianAlramArea3() const;

    void setCamera2R151PedestrianAlramArea3(const std::vector<VISPoint> &camera2R151PedestrianAlramArea3);

    const std::vector<VISPoint> &getCamera2R151PedestrianAlramArea4() const;

    void setCamera2R151PedestrianAlramArea4(const std::vector<VISPoint> &camera2R151PedestrianAlramArea4);

    const std::vector<VISPoint> &getCamera2R151VehicleAlramArea1() const;

    void setCamera2R151VehicleAlramArea1(const std::vector<VISPoint> &camera2R151VehicleAlramArea1);

    const std::vector<VISPoint> &getCamera2R151VehicleAlramArea2() const;

    void setCamera2R151VehicleAlramArea2(const std::vector<VISPoint> &camera2R151VehicleAlramArea2);

    int getSteeringwheelPosition() const;

    void setSteeringwheelPosition(int steeringwheelPosition);

    int getR151PedestrianAlarmSpeakerModelArea1() const;

    void setR151PedestrianAlarmSpeakerModelArea1(int r151PedestrianAlarmSpeakerModelArea1);

    int getR151PedestrianAlarmSpeakerModelArea2() const;

    void setR151PedestrianAlarmSpeakerModelArea2(int r151PedestrianAlarmSpeakerModelArea2);

    int getR151PedestrianAlarmSpeakerModelArea3() const;

    void setR151PedestrianAlarmSpeakerModelArea3(int r151PedestrianAlarmSpeakerModelArea3);

    int getR151PedestrianAlarmSpeakerModelArea4() const;

    void setR151PedestrianAlarmSpeakerModelArea4(int r151PedestrianAlarmSpeakerModelArea4);

    int getR151VehicleAlarmSpeakerModelArea1() const;

    void setR151VehicleAlarmSpeakerModelArea1(int r151VehicleAlarmSpeakerModelArea1);

    int getR151VehicleAlarmSpeakerModelArea2() const;

    void setR151VehicleAlarmSpeakerModelArea2(int r151VehicleAlarmSpeakerModelArea2);

    int getR151DefaultDisplayScreen() const;

    void setR151DefaultDisplayScreen(int r151DefaultDisplayScreen);

    int getR151CameraCoverTimeInterval() const;

    void setR151CameraCoverTimeInterval(int r151CameraCoverTimeInterval);

    const std::vector<VISPoint> &getR158PedestrianAlramArea1() const;

    void setR158PedestrianAlramArea1(const std::vector<VISPoint> &r158PedestrianAlramArea1);

    const std::vector<VISPoint> &getR158PedestrianAlramArea2() const;

    void setR158PedestrianAlramArea2(const std::vector<VISPoint> &r158PedestrianAlramArea2);

    const std::vector<VISPoint> &getR158PedestrianAlramArea3() const;

    void setR158PedestrianAlramArea3(const std::vector<VISPoint> &r158PedestrianAlramArea3);

    int getR151PedestrianDetectStopSpeed() const;

    void setR151PedestrianDetectStopSpeed(int r151PedestrianDetectStopSpeed);

    int getR151VehicleDetectStartSpeed() const;

    void setR151VehicleDetectStartSpeed(int r151VehicleDetectStartSpeed);

    int getR151PedestrianAlarmOutputModelArea1() const;

    void setR151PedestrianAlarmOutputModelArea1(int r151PedestrianAlarmOutputModelArea1);

    int getR151PedestrianAlarmOutputModelArea2() const;

    void setR151PedestrianAlarmOutputModelArea2(int r151PedestrianAlarmOutputModelArea2);

    int getR151PedestrianAlarmOutputModelArea3() const;

    void setR151PedestrianAlarmOutputModelArea3(int r151PedestrianAlarmOutputModelArea3);

    int getR151PedestrianAlarmOutputModelArea4() const;

    void setR151PedestrianAlarmOutputModelArea4(int r151PedestrianAlarmOutputModelArea4);

    int getR151PedestrianAlarmNeedTrend() const;

    void setR151PedestrianAlarmNeedTrend(int r151PedestrianAlarmNeedTrend);

    int getR158DetectStopSpeed() const;

    void setR158DetectStopSpeed(int r158DetectStopSpeed);

    const std::vector<VISPoint> &getR158VehicleAlramArea1() const;

    void setR158VehicleAlramArea1(const std::vector<VISPoint> &r158VehicleAlramArea1);

    const std::vector<VISPoint> &getR158VehicleAlramArea2() const;

    void setR158VehicleAlramArea2(const std::vector<VISPoint> &r158VehicleAlramArea2);

    int getR158DispalyDetectInfoAdd() const;

    void setR158DispalyDetectInfoAdd(int r158DispalyDetectInfoAdd);

    int getR158PedestrianAlarmTrendShielding() const;

    void setR158PedestrianAlarmTrendShielding(int r158PedestrianAlarmTrendShielding);

    const std::vector<VISPoint> &getR159PedestrianAlramArea1() const;

    void setR159PedestrianAlramArea1(const std::vector<VISPoint> &r159PedestrianAlramArea1);

    const std::vector<VISPoint> &getR159PedestrianAlramArea2() const;

    void setR159PedestrianAlramArea2(const std::vector<VISPoint> &r159PedestrianAlramArea2);

    const std::vector<VISPoint> &getR159PedestrianAlramArea3() const;

    void setR159PedestrianAlramArea3(const std::vector<VISPoint> &r159PedestrianAlramArea3);

    const std::vector<VISPoint> &getR159VehicleAlramArea1() const;

    void setR159VehicleAlramArea1(const std::vector<VISPoint> &r159VehicleAlramArea1);

    const std::vector<VISPoint> &getR159VehicleAlramArea2() const;

    void setR159VehicleAlramArea2(const std::vector<VISPoint> &r159VehicleAlramArea2);

    int getR159DetectStopSpeed() const;

    void setR159DetectStopSpeed(int r159DetectStopSpeed);

    int getR159DispalyDetectInfoAdd() const;

    void setR159DispalyDetectInfoAdd(int r159DispalyDetectInfoAdd);

    int getR159PedestrianAlarmTrendShielding() const;

    void setR159PedestrianAlarmTrendShielding(int r159PedestrianAlarmTrendShielding);

    int getR159DetectStartSpeed() const;

    void setR159DetectStartSpeed(int r159DetectStartSpeed);

    int getR159SameIdAlramType() const;

    void setR159SameIdAlramType(int r159SameIdAlramType);

    int getR151PedestrianAlarmArea1StartSpeed() const;

    void setR151PedestrianAlarmArea1StartSpeed(int r151PedestrianAlarmArea1StartSpeed);

    int getR151PedestrianAlarmArea1StopSpeed() const;

    void setR151PedestrianAlarmArea1StopSpeed(int r151PedestrianAlarmArea1StopSpeed);

    int getR151PedestrianAlarmArea2StartSpeed() const;

    void setR151PedestrianAlarmArea2StartSpeed(int r151PedestrianAlarmArea2StartSpeed);

    int getR151PedestrianAlarmArea2StopSpeed() const;

    void setR151PedestrianAlarmArea2StopSpeed(int r151PedestrianAlarmArea2StopSpeed);

    int getR151PedestrianAlarmArea3StartSpeed() const;

    void setR151PedestrianAlarmArea3StartSpeed(int r151PedestrianAlarmArea3StartSpeed);

    int getR151PedestrianAlarmArea3StopSpeed() const;

    void setR151PedestrianAlarmArea3StopSpeed(int r151PedestrianAlarmArea3StopSpeed);

    int getR151PedestrianAlarmArea4StartSpeed() const;

    void setR151PedestrianAlarmArea4StartSpeed(int r151PedestrianAlarmArea4StartSpeed);

    int getR151PedestrianAlarmArea4StopSpeed() const;

    void setR151PedestrianAlarmArea4StopSpeed(int r151PedestrianAlarmArea4StopSpeed);

    int getR151PedestrianAlarmArea4TrendShielding() const;

    void setR151PedestrianAlarmArea4TrendShielding(int r151PedestrianAlarmArea4TrendShielding);

    int getR151DispalyDetectInfoAdd() const;

    void setR151DispalyDetectInfoAdd(int r151DispalyDetectInfoAdd);

    int getDetectMoveButton() const;

    void setDetectMoveButton(int detectMoveButton);

    int getMovePixel() const;

    void setMovePixel(int movePixel);

    int getFrameEarly() const;

    void setFrameEarly(int frameEarly);

    const VehicleStatusFromIO &getVehicleStatusFromIo() const;

    void setVehicleStatusFromIo(const VehicleStatusFromIO &vehicleStatusFromIo);

    int getTdImproveTestEnable() const;

    void setTdImproveTestEnable(int tdImproveTestEnable);

    float getTdClassThresholdImproveTest() const;

    void setTdClassThresholdImproveTest(float tdClassThresholdImproveTest);

    int getSbst160OdImproveTestEnable() const;

    void setSbst160OdImproveTestEnable(int sbst160OdImproveTestEnable);

    float getSbst160OdClassThreshold() const;

    void setSbst160OdClassThreshold(float sbst160OdClassThreshold);

    float getSbst160OdClassThresholdImproveTest() const;

    void setSbst160OdClassThresholdImproveTest(float sbst160OdClassThresholdImproveTest);

    int getDeBsdOdImproveTestEnable() const;

    void setDeBsdOdImproveTestEnable(int deBsdOdImproveTestEnable);

    float getDeBsdOdClassThreshold() const;

    void setDeBsdOdClassThreshold(float deBsdOdClassThreshold);

    float getDeBsdOdClassThresholdImproveTest() const;

    void setDeBsdOdClassThresholdImproveTest(float deBsdOdClassThresholdImproveTest);

    bool isCamera1Opened() const;

    void setCamera1Opened(bool camera1Opened);

    bool isCamera2Opened() const;

    void setCamera2Opened(bool camera2Opened);

    int getR158PedestrianAlarmSpeakerModelArea1() const;

    void setR158PedestrianAlarmSpeakerModelArea1(int r158PedestrianAlarmSpeakerModelArea1);

    int getR158PedestrianAlarmSpeakerModelArea2() const;

    void setR158PedestrianAlarmSpeakerModelArea2(int r158PedestrianAlarmSpeakerModelArea2);

    int getR158VehicleAlarmSpeakerModelArea1() const;

    void setR158VehicleAlarmSpeakerModelArea1(int r158VehicleAlarmSpeakerModelArea1);

    int getR158VehicleAlarmSpeakerModelArea2() const;

    void setR158VehicleAlarmSpeakerModelArea2(int r158VehicleAlarmSpeakerModelArea2);

    int getR159PedestrianAlarmSpeakerModelArea1() const;

    void setR159PedestrianAlarmSpeakerModelArea1(int r159PedestrianAlarmSpeakerModelArea1);

    int getR159PedestrianAlarmSpeakerModelArea2() const;

    void setR159PedestrianAlarmSpeakerModelArea2(int r159PedestrianAlarmSpeakerModelArea2);

    int getR159VehicleAlarmSpeakerModelArea1() const;

    void setR159VehicleAlarmSpeakerModelArea1(int r159VehicleAlarmSpeakerModelArea1);

    int getR159VehicleAlarmSpeakerModelArea2() const;

    void setR159VehicleAlarmSpeakerModelArea2(int r159VehicleAlarmSpeakerModelArea2);

    int getG3DisplayOutputSignalType() const;

    void setG3DisplayOutputSignalType(int g3DisplayOutputSignalType);

    int getMultimediaFilesEncryptEnable() const;

    void setMultimediaFilesEncryptEnable(int multimediaFilesEncryptEnable);

    const uint8_t *getMultimediaFilesEncryptKey() const;

    void setMultimediaFilesEncryptKey(const uint8_t *buf,const int len);

    const uint8_t *getMultimediaFilesEncryptIv() const;

    void setMultimediaFilesEncryptIv(const uint8_t *buf,const int len);

    void setCameraInputSignalType(const int cameraId,int inputType);

    int getCameraInputSignalType(const int cameraId);

    IO_OUT_Camera_Enable getOutPut1CameraEnable();

    IO_OUT_Camera_Enable getOutPut2CameraEnable();

    int getBsdCamcoverCountThreshold() const;

    void setBsdCamcoverCountThreshold(int bsdCamcoverCountThreshold);

    int getBsdCamcoverAlarmOutputInterval() const;

    void setBsdCamcoverAlarmOutputInterval(int bsdCamcoverAlarmOutputInterval);

    int getR158PedestrianAlarmSpeakerModelArea3() const;

    void setR158PedestrianAlarmSpeakerModelArea3(int r158PedestrianAlarmSpeakerModelArea3);

    int getR159PedestrianAlarmSpeakerModelArea3() const;

    void setR159PedestrianAlarmSpeakerModelArea3(int r159PedestrianAlarmSpeakerModelArea3);

    int getBsdPedestrianAlarmSpeakerModelArea1() const;

    void setBsdPedestrianAlarmSpeakerModelArea1(int bsdPedestrianAlarmSpeakerModelArea1);

    int getBsdPedestrianAlarmSpeakerModelArea2() const;

    void setBsdPedestrianAlarmSpeakerModelArea2(int bsdPedestrianAlarmSpeakerModelArea2);

    int getBsdVehicleAlarmSpeakerModelArea1() const;

    void setBsdVehicleAlarmSpeakerModelArea1(int bsdVehicleAlarmSpeakerModelArea1);

    int getBsdVehicleAlarmSpeakerModelArea2() const;

    void setBsdVehicleAlarmSpeakerModelArea2(int bsdVehicleAlarmSpeakerModelArea2);

    int getR232ModbusButtonMainType() const;

    void setR232ModbusButtonMainType(int r232ModbusButtonMainType);

    int getR232ModbusButtonExtensionType() const;

    void setR232ModbusButtonExtensionType(int r232ModbusButtonExtensionType);

    int getR151VehicleAlarmOutputModelArea1() const;

    void setR151VehicleAlarmOutputModelArea1(int r151VehicleAlarmOutputModelArea1);

    int getR151VehicleAlarmOutputModelArea2() const;

    void setR151VehicleAlarmOutputModelArea2(int r151VehicleAlarmOutputModelArea2);

    int getR158PedestrianAlarmOutputModelArea1() const;

    void setR158PedestrianAlarmOutputModelArea1(int r158PedestrianAlarmOutputModelArea1);

    int getR158PedestrianAlarmOutputModelArea2() const;

    void setR158PedestrianAlarmOutputModelArea2(int r158PedestrianAlarmOutputModelArea2);

    int getR158PedestrianAlarmOutputModelArea3() const;

    void setR158PedestrianAlarmOutputModelArea3(int r158PedestrianAlarmOutputModelArea3);

    int getR158VehicleAlarmOutputModelArea1() const;

    void setR158VehicleAlarmOutputModelArea1(int r158VehicleAlarmOutputModelArea1);

    int getR158VehicleAlarmOutputModelArea2() const;

    void setR158VehicleAlarmOutputModelArea2(int r158VehicleAlarmOutputModelArea2);

    int getR159PedestrianAlarmOutputModelArea1() const;

    void setR159PedestrianAlarmOutputModelArea1(int r159PedestrianAlarmOutputModelArea1);

    int getR159PedestrianAlarmOutputModelArea2() const;

    void setR159PedestrianAlarmOutputModelArea2(int r159PedestrianAlarmOutputModelArea2);

    int getR159PedestrianAlarmOutputModelArea3() const;

    void setR159PedestrianAlarmOutputModelArea3(int r159PedestrianAlarmOutputModelArea3);

    int getR159VehicleAlarmOutputModelArea1() const;

    void setR159VehicleAlarmOutputModelArea1(int r159VehicleAlarmOutputModelArea1);

    int getR159VehicleAlarmOutputModelArea2() const;

    void setR159VehicleAlarmOutputModelArea2(int r159VehicleAlarmOutputModelArea2);

    TimeOSDAddEnable getCameraTimeOsdAdd();

    int getBlindspotSpeed() const;

    void setBlindspotSpeed(int blindspotSpeed);

    int getBlindspotButton() const;

    void setBlindspotButton(int blindspotButton);

    int getBlindspotMode() const;

    void setBlindspotMode(int blindspotMode);

    int getBlindspotSensitivity() const;

    void setBlindspotSensitivity(int blindspotSensitivity);

    int getEventBlindspotTime() const;

    void setEventBlindspotTime(int eventBlindspotTime);

    int getFreezeBlindspotTime() const;

    void setFreezeBlindspotTime(int freezeBlindspotTime);

    int getSeatbeltSpeed() const;

    void setSeatbeltSpeed(int seatbeltSpeed);

    int getSeatbeltButton() const;

    void setSeatbeltButton(int seatbeltButton);

    int getSeatbeltMode() const;

    void setSeatbeltMode(int seatbeltMode);

    int getSeatbeltSensitivity() const;

    void setSeatbeltSensitivity(int seatbeltSensitivity);

    int getEventSeatbeltTime() const;

    void setEventSeatbeltTime(int eventSeatbeltTime);

    int getFreezeSeatbeltTime() const;

    void setFreezeSeatbeltTime(int freezeSeatbeltTime);

    int getAdasLaneIntersectionCalc() const;

    void setAdasLaneIntersectionCalc(int adasLaneIntersectionCalc);

    const Camera_Input_Type_Filter &getCameraInputTypeFilter() const;

    void setCameraInputTypeFilter(const Camera_Input_Type_Filter &cameraInputTypeFilter);

    int getHighKeepTimeForHighMode() const;

    void setHighKeepTimeForHighMode(int highKeepTimeForHighMode);

    int getLowKeepTimeForHighMode() const;

    void setLowKeepTimeForHighMode(int lowKeepTimeForHighMode);

    int getOutputCountOneTimeForHighMode() const;

    void setOutputCountOneTimeForHighMode(int outputCountOneTimeForHighMode);

    int getHighKeepTimeForLowMode() const;

    void setHighKeepTimeForLowMode(int highKeepTimeForLowMode);

    int getLowKeepTimeForLowMode() const;

    void setLowKeepTimeForLowMode(int lowKeepTimeForLowMode);

    int getOutputCountOneTimeForLowMode() const;

    void setOutputCountOneTimeForLowMode(int outputCountOneTimeForLowMode);

    int getStartDimLighModetValue() const;

    void setStartDimLighModetValue(int startDimLighModetValue);

    int getStopDimLightModeValue() const;

    void setStopDimLightModeValue(int stopDimLightModeValue);

    int getKomBsdOdImproveTestEnable() const;

    void setKomBsdOdImproveTestEnable(int komBsdOdImproveTestEnable);

    float getKomBsdOdClassThreshold() const;

    void setKomBsdOdClassThreshold(float komBsdOdClassThreshold);

    float getKomBsdOdClassThresholdImproveTest() const;

    void setKomBsdOdClassThresholdImproveTest(float komBsdOdClassThresholdImproveTest);

    int getVehicleTtcSameIdCooldownThreshold() const;

    void setVehicleTtcSameIdCooldownThreshold(int vehicleTtcSameIdCooldownThreshold);

    const std::vector<DisplaySwitchForSignalRule> &getDisplaySwitchForSignalRuleList() const;

    void
    setDisplaySwitchForSignalRuleList(const std::vector<DisplaySwitchForSignalRule> &displaySwitchForSignalRuleList);

    int getDisplaySwitchForSignalDefaultDisplayType() const;

    void setDisplaySwitchForSignalDefaultDisplayType(int displaySwitchForSignalDefaultDisplayType);

    float getL4ProtocoVersion() const;

    void setL4ProtocoVersion(float l4ProtocoVersion);

    int getRs232WorkMode() const;

    void setRs232WorkMode(int rs232WorkMode);

    int getAdasCameraF() const;

    void setAdasCameraF(int adasCameraF);

    int getRealviewDataFormat() const;

    void setRealviewDataFormat(int realviewDataFormat);

    int getAdasLdwDashAlarmSwitch() const;

    void setAdasLdwDashAlarmSwitch(int adasLdwDashAlarmSwitch);

    const std::vector<CameraAlgWorkRule> &getCameraAlgWorkRuleList() const;

    void setCameraAlgWorkRuleList(const std::vector<CameraAlgWorkRule> &cameraAlgWorkRuleList);

    const CameraReduceEffectInfo &getCurCameraReduceEffectInfo() const;

    void setCurCameraReduceEffectInfo(const CameraReduceEffectInfo &curCameraReduceEffectInfo);

    int getForklift160OdImproveTestEnable() const;

    void setForklift160OdImproveTestEnable(int forklift160OdImproveTestEnable);

    float getForklift160OdClassThreshold() const;

    void setForklift160OdClassThreshold(float forklift160OdClassThreshold);

    float getForklift160OdClassThresholdImproveTest() const;

    void setForklift160OdClassThresholdImproveTest(float forklift160OdClassThresholdImproveTest);

    float getReduceEffectImgZoomRate() const;

    void setReduceEffectImgZoomRate(float reduceEffectImgZoomRate);

    bool changeCameraListToTwo();

    bool isCamera3Opened() const;

    void setCamera3Opened(bool camera3Opened);

    bool isCamera4Opened() const;

    void setCamera4Opened(bool camera4Opened);

    const std::string &getCurFileStorageRoot() const;

    void setCurFileStorageRoot(const std::string &curFileStorageRoot);

    const std::vector<VISPoint> &getCamera2PedestrianAlramAreaLevel1() const;

    void setCamera2PedestrianAlramAreaLevel1(const std::vector<VISPoint> &camera2PedestrianAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera2PedestrianAlramAreaLevel2() const;

    void setCamera2PedestrianAlramAreaLevel2(const std::vector<VISPoint> &camera2PedestrianAlramAreaLevel2);

    const std::vector<VISPoint> &getCamera3PedestrianAlramAreaLevel1() const;

    void setCamera3PedestrianAlramAreaLevel1(const std::vector<VISPoint> &camera3PedestrianAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera3PedestrianAlramAreaLevel2() const;

    void setCamera3PedestrianAlramAreaLevel2(const std::vector<VISPoint> &camera3PedestrianAlramAreaLevel2);

    const std::vector<VISPoint> &getCamera2VehicleAlramAreaLevel1() const;

    void setCamera2VehicleAlramAreaLevel1(const std::vector<VISPoint> &camera2VehicleAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera2VehicleAlramAreaLevel2() const;

    void setCamera2VehicleAlramAreaLevel2(const std::vector<VISPoint> &camera2VehicleAlramAreaLevel2);

    const std::vector<VISPoint> &getCamera3VehicleAlramAreaLevel1() const;

    void setCamera3VehicleAlramAreaLevel1(const std::vector<VISPoint> &camera3VehicleAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera3VehicleAlramAreaLevel2() const;

    void setCamera3VehicleAlramAreaLevel2(const std::vector<VISPoint> &camera3VehicleAlramAreaLevel2);

    const std::vector<VISPoint> *getUndetectedAreaListCamera3() const;

    const std::vector<VISPoint> *getUndetectedAreaListCamera4() const;

    int getCameraUdpPort3() const;

    void setCameraUdpPort3(int cameraUdpPort3);

    int getCameraUdpPort4() const;

    void setCameraUdpPort4(int cameraUdpPort4);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaCamera3TurnleftAngle1Level1() const;

    void setSbstVehicleAlramAreaCamera3TurnleftAngle1Level1(
            const std::vector<VISPoint> &sbstVehicleAlramAreaCamera3TurnleftAngle1Level1);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaCamera3TurnleftAngle1Level2() const;

    void setSbstVehicleAlramAreaCamera3TurnleftAngle1Level2(
            const std::vector<VISPoint> &sbstVehicleAlramAreaCamera3TurnleftAngle1Level2);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaCamera3TurnrightAngle1Level1() const;

    void setSbstVehicleAlramAreaCamera3TurnrightAngle1Level1(
            const std::vector<VISPoint> &sbstVehicleAlramAreaCamera3TurnrightAngle1Level1);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaCamera3TurnrightAngle1Level2() const;

    void setSbstVehicleAlramAreaCamera3TurnrightAngle1Level2(
            const std::vector<VISPoint> &sbstVehicleAlramAreaCamera3TurnrightAngle1Level2);

    const std::vector<VISPoint> &getSbstPedestrianAlramAreaCamera3TurnleftAngle1Level1() const;

    void setSbstPedestrianAlramAreaCamera3TurnleftAngle1Level1(
            const std::vector<VISPoint> &sbstPedestrianAlramAreaCamera3TurnleftAngle1Level1);

    const std::vector<VISPoint> &getSbstPedestrianAlramAreaCamera3TurnleftAngle1Level2() const;

    void setSbstPedestrianAlramAreaCamera3TurnleftAngle1Level2(
            const std::vector<VISPoint> &sbstPedestrianAlramAreaCamera3TurnleftAngle1Level2);

    const std::vector<VISPoint> &getSbstPedestrianAlramAreaCamera3TurnrightAngle1Level1() const;

    void setSbstPedestrianAlramAreaCamera3TurnrightAngle1Level1(
            const std::vector<VISPoint> &sbstPedestrianAlramAreaCamera3TurnrightAngle1Level1);

    const std::vector<VISPoint> &getSbstPedestrianAlramAreaCamera3TurnrightAngle1Level2() const;

    void setSbstPedestrianAlramAreaCamera3TurnrightAngle1Level2(
            const std::vector<VISPoint> &sbstPedestrianAlramAreaCamera3TurnrightAngle1Level2);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaCamera4TurnleftAngle1Level1() const;

    void setSbstVehicleAlramAreaCamera4TurnleftAngle1Level1(
            const std::vector<VISPoint> &sbstVehicleAlramAreaCamera4TurnleftAngle1Level1);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaCamera4TurnleftAngle1Level2() const;

    void setSbstVehicleAlramAreaCamera4TurnleftAngle1Level2(
            const std::vector<VISPoint> &sbstVehicleAlramAreaCamera4TurnleftAngle1Level2);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaCamera4TurnrightAngle1Level1() const;

    void setSbstVehicleAlramAreaCamera4TurnrightAngle1Level1(
            const std::vector<VISPoint> &sbstVehicleAlramAreaCamera4TurnrightAngle1Level1);

    const std::vector<VISPoint> &getSbstVehicleAlramAreaCamera4TurnrightAngle1Level2() const;

    void setSbstVehicleAlramAreaCamera4TurnrightAngle1Level2(
            const std::vector<VISPoint> &sbstVehicleAlramAreaCamera4TurnrightAngle1Level2);

    const std::vector<VISPoint> &getSbstPedestrianAlramAreaCamera4TurnleftAngle1Level1() const;

    void setSbstPedestrianAlramAreaCamera4TurnleftAngle1Level1(
            const std::vector<VISPoint> &sbstPedestrianAlramAreaCamera4TurnleftAngle1Level1);

    const std::vector<VISPoint> &getSbstPedestrianAlramAreaCamera4TurnleftAngle1Level2() const;

    void setSbstPedestrianAlramAreaCamera4TurnleftAngle1Level2(
            const std::vector<VISPoint> &sbstPedestrianAlramAreaCamera4TurnleftAngle1Level2);

    const std::vector<VISPoint> &getSbstPedestrianAlramAreaCamera4TurnrightAngle1Level1() const;

    void setSbstPedestrianAlramAreaCamera4TurnrightAngle1Level1(
            const std::vector<VISPoint> &sbstPedestrianAlramAreaCamera4TurnrightAngle1Level1);

    const std::vector<VISPoint> &getSbstPedestrianAlramAreaCamera4TurnrightAngle1Level2() const;

    void setSbstPedestrianAlramAreaCamera4TurnrightAngle1Level2(
            const std::vector<VISPoint> &sbstPedestrianAlramAreaCamera4TurnrightAngle1Level2);

    int getPedestrianAlarmNeedChangeByTurnSignCamera2() const;

    void setPedestrianAlarmNeedChangeByTurnSignCamera2(int pedestrianAlarmNeedChangeByTurnSignCamera2);

    int getPedestrianAlarmNeedChangeByTurnSignCamera3() const;

    void setPedestrianAlarmNeedChangeByTurnSignCamera3(int pedestrianAlarmNeedChangeByTurnSignCamera3);

    int getVehicleAlarmNeedChangeByTurnSignCamera2() const;

    void setVehicleAlarmNeedChangeByTurnSignCamera2(int vehicleAlarmNeedChangeByTurnSignCamera2);

    int getVehicleAlarmNeedChangeByTurnSignCamera3() const;

    void setVehicleAlarmNeedChangeByTurnSignCamera3(int vehicleAlarmNeedChangeByTurnSignCamera3);

    int getSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera2() const;

    void
    setSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera2(int sbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera2);

    int getSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera2() const;

    void
    setSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera2(int sbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera2);

    int getSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera3() const;

    void
    setSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera3(int sbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera3);

    int getSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera3() const;

    void
    setSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera3(int sbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera3);

    const std::vector<VISPoint> &getCamera3R151PedestrianAlramArea1() const;

    void setCamera3R151PedestrianAlramArea1(const std::vector<VISPoint> &camera3R151PedestrianAlramArea1);

    const std::vector<VISPoint> &getCamera3R151PedestrianAlramArea2() const;

    void setCamera3R151PedestrianAlramArea2(const std::vector<VISPoint> &camera3R151PedestrianAlramArea2);

    const std::vector<VISPoint> &getCamera3R151PedestrianAlramArea3() const;

    void setCamera3R151PedestrianAlramArea3(const std::vector<VISPoint> &camera3R151PedestrianAlramArea3);

    const std::vector<VISPoint> &getCamera3R151PedestrianAlramArea4() const;

    void setCamera3R151PedestrianAlramArea4(const std::vector<VISPoint> &camera3R151PedestrianAlramArea4);

    const std::vector<VISPoint> &getCamera3R151VehicleAlramArea1() const;

    void setCamera3R151VehicleAlramArea1(const std::vector<VISPoint> &camera3R151VehicleAlramArea1);

    const std::vector<VISPoint> &getCamera3R151VehicleAlramArea2() const;

    void setCamera3R151VehicleAlramArea2(const std::vector<VISPoint> &camera3R151VehicleAlramArea2);

    const std::vector<VISPoint> &getCamera4R151PedestrianAlramArea1() const;

    void setCamera4R151PedestrianAlramArea1(const std::vector<VISPoint> &camera4R151PedestrianAlramArea1);

    const std::vector<VISPoint> &getCamera4R151PedestrianAlramArea2() const;

    void setCamera4R151PedestrianAlramArea2(const std::vector<VISPoint> &camera4R151PedestrianAlramArea2);

    const std::vector<VISPoint> &getCamera4R151PedestrianAlramArea3() const;

    void setCamera4R151PedestrianAlramArea3(const std::vector<VISPoint> &camera4R151PedestrianAlramArea3);

    const std::vector<VISPoint> &getCamera4R151PedestrianAlramArea4() const;

    void setCamera4R151PedestrianAlramArea4(const std::vector<VISPoint> &camera4R151PedestrianAlramArea4);

    const std::vector<VISPoint> &getCamera4R151VehicleAlramArea1() const;

    void setCamera4R151VehicleAlramArea1(const std::vector<VISPoint> &camera4R151VehicleAlramArea1);

    const std::vector<VISPoint> &getCamera4R151VehicleAlramArea2() const;

    void setCamera4R151VehicleAlramArea2(const std::vector<VISPoint> &camera4R151VehicleAlramArea2);

    int getCam3RealviewPort() const;

    void setCam3RealviewPort(int cam3RealviewPort);

    int getCam4RealviewPort() const;

    void setCam4RealviewPort(int cam4RealviewPort);

    COREBOARDTYPE getCoreBoardType() const;

    void setCoreBoardType(COREBOARDTYPE coreBoardType);

    const std::vector<VISPoint> &getCamera0SecurityModelPedestrianAlramAreaLevel1() const;

    void setCamera0SecurityModelPedestrianAlramAreaLevel1(
            const std::vector<VISPoint> &camera0SecurityModelPedestrianAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera0SecurityModelPedestrianAlramAreaLevel2() const;

    void setCamera0SecurityModelPedestrianAlramAreaLevel2(
            const std::vector<VISPoint> &camera0SecurityModelPedestrianAlramAreaLevel2);

    const std::vector<VISPoint> &getCamera1SecurityModelPedestrianAlramAreaLevel1() const;

    void setCamera1SecurityModelPedestrianAlramAreaLevel1(
            const std::vector<VISPoint> &camera1SecurityModelPedestrianAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera1SecurityModelPedestrianAlramAreaLevel2() const;

    void setCamera1SecurityModelPedestrianAlramAreaLevel2(
            const std::vector<VISPoint> &camera1SecurityModelPedestrianAlramAreaLevel2);

    const std::vector<VISPoint> &getCamera2SecurityModelPedestrianAlramAreaLevel1() const;

    void setCamera2SecurityModelPedestrianAlramAreaLevel1(
            const std::vector<VISPoint> &camera2SecurityModelPedestrianAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera2SecurityModelPedestrianAlramAreaLevel2() const;

    void setCamera2SecurityModelPedestrianAlramAreaLevel2(
            const std::vector<VISPoint> &camera2SecurityModelPedestrianAlramAreaLevel2);

    const std::vector<VISPoint> &getCamera3SecurityModelPedestrianAlramAreaLevel1() const;

    void setCamera3SecurityModelPedestrianAlramAreaLevel1(
            const std::vector<VISPoint> &camera3SecurityModelPedestrianAlramAreaLevel1);

    const std::vector<VISPoint> &getCamera3SecurityModelPedestrianAlramAreaLevel2() const;

    void setCamera3SecurityModelPedestrianAlramAreaLevel2(
            const std::vector<VISPoint> &camera3SecurityModelPedestrianAlramAreaLevel2);

    const std::vector<C3SwitchRule> &getC3SwicthRuleList() const;

    void setC3SwicthRuleList(const std::vector<C3SwitchRule> &c3SwicthRuleList);

    C3SwitchRule getC3SwitchRuleByC3Id(const C3ID c3Id);

    int getR151PedestrianAlarmArea3TrendShielding() const;

    void setR151PedestrianAlarmArea3TrendShielding(int r151PedestrianAlarmArea3TrendShielding);

    int getGsensorSamplingInterval() const;

    void setGsensorSamplingInterval(int gsensorSamplingInterval);

    int getCameraFrameRate() const;

    void setCameraFrameRate(int cameraFrameRate);

    int getHatSpeed() const;

    void setHatSpeed(int hatSpeed);

    int getHatButton() const;

    void setHatButton(int hatButton);

    int getHatMode() const;

    void setHatMode(int hatMode);

    int getHatSensitivity() const;

    void setHatSensitivity(int hatSensitivity);

    int getHatEventime() const;

    void setHatEventime(int hatEventime);

    int getHatFreetime() const;

    void setHatFreetime(int hatFreetime);

    const AlarmEvidenceTypeList &getAlarmEvidenceTypes() const;

    void setAlarmEvidenceTypes(const AlarmEvidenceTypeList &alarmEvidenceTypes);

    const CloundPlatformTypeList &getCloudPlatformTypes() const;

    void setCloudPlatformTypes(const CloundPlatformTypeList &cloudPlatformTypes);

    const AddDetectInfoAlarmEvidenceTypes &getAddDetectinfoAlarmEvidence() const;

    void setAddDetectinfoAlarmEvidence(const AddDetectInfoAlarmEvidenceTypes &addDetectinfoAlarmEvidence);

    const std::vector<AlgSerialRuleItem> &getAlgSerialRule() const;

    void setAlgSerialRule(const std::vector<AlgSerialRuleItem> &algSerialRule);

    bool isInternetConnected() const;

    void setInternetConnected(bool internetConnected);

    bool isNdsRegistered() const;

    void setNdsRegistered(bool ndsRegistered);

    bool isNdsConnected() const;

    void setNdsConnected(bool ndsConnected);

    float getAdasCancelHeadwayAlarm() const;

    void setAdasCancelHeadwayAlarm(float adasCancelHeadwayAlarm);

    int getAdasPdwSwitch() const;

    void setAdasPdwSwitch(int adasPdwSwitch);

    int getAdasFcwSwitch() const;

    void setAdasFcwSwitch(int adasFcwSwitch);

    int getAdasLdwSwitch() const;

    void setAdasLdwSwitch(int adasLdwSwitch);

    int getAdasVbSwitch() const;

    void setAdasVbSwitch(int adasVbSwitch);

    int getAdasGoSwitch() const;

    void setAdasGoSwitch(int adasGoSwitch);

    int getCameraImageSource() const;

    void setCameraImageSource(int cameraImageSource);

private:
    void initItemFromString(std::vector<VISPoint> &point, const std::string name, YAML::Node &configFromPC,
                            bool &hasMissParams);

    /* 从文件读出来的配置 */
    YAML::Node curConfig;

    /* 相机类型的列表 */
    std::vector<CameraType> cameraTypeList;
    /* 镜头1的行人报警区域1的六个点 */
    std::vector<VISPoint> camera0_pedestrian_alram_area_level1;
    /* 镜头1的行人报警区域2的六个点 */
    std::vector<VISPoint> camera0_pedestrian_alram_area_level2;
    /* 镜头2的行人报警区域1的六个点 */
    std::vector<VISPoint> camera1_pedestrian_alram_area_level1;
    /* 镜头2的行人报警区域2的六个点 */
    std::vector<VISPoint> camera1_pedestrian_alram_area_level2;
    /* 镜头1的车辆报警区域1的六个点 */
    std::vector<VISPoint> camera0_vehicle_alram_area_level1;
    /* 镜头1的车辆报警区域2的六个点 */
    std::vector<VISPoint> camera0_vehicle_alram_area_level2;
    /* 镜头2的车辆报警区域1的六个点 */
    std::vector<VISPoint> camera1_vehicle_alram_area_level1;
    /* 镜头2的车辆报警区域2的六个点 */
    std::vector<VISPoint> camera1_vehicle_alram_area_level2;

    // sbst的镜头1当在特定车速区间内打了左转（打了左转向灯）后使用的车辆一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera1_turnleft_angle1_level1;
    // sbst的镜头1当在特定车速区间内打了左转（打了左转向灯）后使用的车辆二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera1_turnleft_angle1_level2;
    // sbst的镜头1当在特定车速区间内打了右转（打了右转向灯）后使用的车辆一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera1_turnright_angle1_level1;
    // sbst的镜头1当在特定车速区间内打了右转（打了右转向灯）后使用的车辆二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera1_turnright_angle1_level2;
    // sbst的镜头1当在特定车速区间内打了左转（打了左转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera1_turnleft_angle1_level1;
    // sbst的镜头1当在特定车速区间内打了左转（打了左转向灯）后使用的行人二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera1_turnleft_angle1_level2;
    // sbst的镜头1当在特定车速区间内打了右转（打了右转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera1_turnright_angle1_level1;
    // sbst的镜头1当在特定车速区间内打了右转（打了右转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera1_turnright_angle1_level2;

    // sbst的镜头2当在特定车速区间内打了左转（打了左转向灯）后使用的车辆一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera2_turnleft_angle1_level1;
    // sbst的镜头2当在特定车速区间内打了左转（打了左转向灯）后使用的车辆二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera2_turnleft_angle1_level2;
    // sbst的镜头2当在特定车速区间内打了右转（打了右转向灯）后使用的车辆一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera2_turnright_angle1_level1;
    // sbst的镜头2当在特定车速区间内打了右转（打了右转向灯）后使用的车辆二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera2_turnright_angle1_level2;
    // sbst的镜头2当在特定车速区间内打了左转（打了左转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera2_turnleft_angle1_level1;
    // sbst的镜头2当在特定车速区间内打了左转（打了左转向灯）后使用的行人二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera2_turnleft_angle1_level2;
    // sbst的镜头2当在特定车速区间内打了右转（打了右转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera2_turnright_angle1_level1;
    // sbst的镜头2当在特定车速区间内打了右转（打了右转向灯）后使用的行人二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera2_turnright_angle1_level2;

    int sbst_change_pedestrian_alram_area_by_turnlight_min_speed = 1;
    int sbst_change_pedestrian_alram_area_by_turnlight_max_speed = 30;

    int config_version_code = 18;
    /*镜头1的车辆不报警区域*/
    std::vector<VISPoint> undetected_area_list_camera1[2];
    /*镜头2的车辆不报警区域*/
    std::vector<VISPoint> undetected_area_list_camera2[2];

    /* 侧边行人报警级别1  速度区间的最低车速 */
    int bsd_min_alram_speed_level1 = 0;
    /* 侧边行人报警级别1  速度区间的最高车速 */
    int bsd_max_alram_speed_level1 = 50;
    /* 侧边行人报警级别2  速度区间的最低车速 */
    int bsd_min_alram_speed_level2 = 0;
    /* 侧边行人报警级别2  速度区间的最高车速 */
    int bsd_max_alram_speed_level2 = 50;

    /* 侧边行人同ID报警类型，目前只有：0-同ID只报一次   1-同ID持续报警  */
    int bsd_same_id_alram_type;

    /* 是否在图像中叠加识别信息   0-不叠加   1-叠加 */
    int bsd_add_detection_info;

    /* 侧边行人报警趋势过滤规则  长度为1个字节  用bit位表示开关  0-否   1-是     bit0-静止的人是否不需要报警   bit1-靠近镜头的人是否不需要报警  bit2-远离镜头的人是否不需要报警 */
    int bsd_alarm_trend_shielding;

    /* 侧边行人侧边报警报警级别1是否能响喇叭    0-不响  1-响提示音  2-响语音 */
    int bsd_alarm_level1_speaker_enable;

    /* 侧边行人侧边报警报警级别1是否要启动震动器    0-不震动   1-要震动   （此字段后续不使用）*/
    int bsd_alarm_level1_vibrator_enable;

    /* 侧边行人侧边报警报警级别1是否要发送到485总线    0-不发送   1-要发送 */
    int bsd_alarm_level1_sendto_rs485_enable;

    /* 侧边行人侧边报警报警级别2是否能响喇叭    0-不响  1-响提示音  2-响语音 */
    int bsd_alarm_level2_speaker_enable;

    /* 侧边行人侧边报警报警级别2是否要启动震动器    0-不震动   1-要震动 （此字段后续不使用）*/
    int bsd_alarm_level2_vibrator_enable;

    /* 侧边行人侧边报警报警级别2是否要发送到485总线    0-不发送   1-要发送 */
    int bsd_alarm_level2_sendto_rs485_enable;

    /* 镜头是否bypass， 用bit位表示开关  0-否   1-是        bit0-镜头1是否需要pass      bit1-镜头2是否需要pass      bit2-镜头3是否需要pass      bit3-镜头4是否需要pass */
    int bsd_camera_bypass_info = 0;

    /* G3的工作模式   0：作为独立G3工作   1：作为G4的一部分工作  2：作为G4-MT的一部分工作 */
    int g3_device_working_mode;

    /* G3使用的时区 */
    std::string localTimeZone = ":Asia/Shanghai";

    /* GPIO_IN1(GPIO_115) 的作用类型  -1：无定义   0：左转信号   1：右转信息   2：车速   3：侧翻信号  4：车门  5：倒车  6:工程机械操作杆  7:镜头报警开关1    8:镜头报警开关2    9:镜头报警开关3     */
    int gpioIn1_type = -1;

    /* GPIO_IN2(GPIO_111) 的作用类型  -1：无定义   0：左转信号   1：右转信息   2：车速   3：侧翻信号  4：车门  5：倒车  6:工程机械操作杆  7:镜头报警开关1    8:镜头报警开关2    9:镜头报警开关3     */
    int gpioIn2_type = -1;

    /* GPIO_IN3(GPIO_110) 的作用类型  -1：无定义   0：左转信号   1：右转信息   2：车速   3：侧翻信号  4：车门  5：倒车  6:工程机械操作杆  7:镜头报警开关1    8:镜头报警开关2    9:镜头报警开关3     */
    int gpioIn3_type = -1;

    /* GPIO_OUT1(GPIO_105) 的作用类型  -1：无定义   0：震动 （此字段后续不使用） */
    int gpioOut1_type = -1;

    /* GPIO_OUT1(GPIO_108) 的作用类型  -1：无定义   0：震动 （此字段后续不使用） */
    int gpioOut2_type = -1;

    /* 喇叭音量   范围：0~100 */
    int speakerVolume = 100;

    /* 戴口罩识别的启动车速 */
    int respirator_speed = 1;

    /* 戴口罩识别的的开关 */
    int respirator_button = 0;

    /* 戴口罩识别的的模式 */
    int respirator_mode = 0;

    /* 戴口罩识别的的灵敏度 */
    int respirator_sensitivity = 30;

    /* 戴口罩识别的的识别时间 */
    int event_respirator_time = 4000;

    /* 戴口罩识别的的冷却时间 */
    int freeze_respirator_time = 2000;

    /* 闭眼识别的启动车速 */
    int eye_speed = 2;

    /* 闭眼识别的的开关 */
    int eye_button = 1;

    /* 闭眼识别的的模式 */
    int eye_mode = 0;

    /* 闭眼识别的的灵敏度 */
    int eye_sensitivity = 15;

    /* 闭眼识别的的识别时间 */
    int event_eye_time = 2000;

    /* 闭眼识别的的冷却时间 */
    int freeze_eye_time = 1000;

    /* 打哈欠识别的启动车速 */
    int mouth_speed = 3;

    /* 打哈欠识别的的开关 */
    int mouth_button = 1;

    /* 打哈欠识别的的模式 */
    int mouth_mode = 0;

    /* 打哈欠识别的的灵敏度 */
    int mouth_sensitivity = 30;

    /* 打哈欠识别的的识别时间 */
    int event_mouth_time = 3600;

    /* 打哈欠识别的的冷却时间 */
    int freeze_mouth_time = 1200;

    /* 左顾右盼识别的启动车速 */
    int lookaround_speed = 4;

    /* 左顾右盼识别的的开关 */
    int lookaround_button = 1;

    /* 左顾右盼识别的的模式 */
    int lookaround_mode = 0;

    /* 左顾右盼识别的的灵敏度 */
    int lookaround_sensitivity = 30;

    /* 左顾右盼识别的的识别时间 */
    int event_lookaround_time = 4000;

    /* 左顾右盼识别的的冷却时间 */
    int freeze_lookaround_time = 2000;

    /* 离岗识别的启动车速 */
    int facemissing_speed = 5;

    /* 离岗识别的的开关 */
    int facemissing_button = 1;

    /* 离岗识别的的模式 */
    int facemissing_mode = 0;

    /* 离岗识别的的灵敏度 */
    int facemissing_sensitivity = 30;

    /* 离岗识别的的识别时间 */
    int event_facemissing_time = 4000;

    /* 离岗识别的的冷却时间 */
    int freeze_facemissing_time = 2000;

    /* 相机遮挡识别的启动车速 */
    int camcover_speed = 6;

    /* 相机遮挡识别的的开关 */
    int camcover_button = 0;

    /* 相机遮挡识别的的模式 */
    int camcover_mode = 0;

    /* 相机遮挡识别的的灵敏度 */
    int camcover_sensitivity = 30;

    /* 相机遮挡识别的的识别时间 */
    int event_camcover_time = 4000;

    /* 相机遮挡识别的的冷却时间 */
    int freeze_camcover_time = 2000;

    /* 抽烟识别的启动车速 */
    int smoking_speed = 5;

    /* 抽烟识别的的开关 */
    int smoking_button = 0;

    /* 抽烟识别的的模式 */
    int smoking_mode = 0;

    /* 抽烟识别的的灵敏度 */
    int smoking_sensitivity = 30;

    /* 抽烟识别的的识别时间 */
    int event_smoking_time = 4000;

    /* 抽烟识别的的冷却时间 */
    int freeze_smoking_time = 2000;

    /* 打电话识别的启动车速 */
    int phone_speed = 6;

    /* 打电话识别的的开关 */
    int phone_button = 0;

    /* 打电话识别的的模式 */
    int phone_mode = 0;

    /* 打电话识别的的灵敏度 */
    int phone_sensitivity = 30;

    /* 打电话识别的的识别时间 */
    int event_phone_time = 4000;

    /* 打电话识别的的冷却时间 */
    int freeze_phone_time = 2000;

    /* GPIO_OUT1 的输出报警类型  用bit位表示开关  0-否   1-是     bit0-侧边行人1级区域报警   bit1-侧边行人2级区域报警 */
    uint64_t gpioOut1_AlarmType = 0;

    /* GPIO_OUT1 的输出模式  -1：无定义   0：只输出一次（报警时拉高持续2s，间隔10s）  1：持续输出  2：相反输出（报警时拉低持续500ms，间隔500s） */
    int gpioOut1_OuputType = -1;

    /* GPIO_OUT2 的输出报警类型  用bit位表示开关  0-否   1-是     bit0-侧边行人1级区域报警   bit1-侧边行人2级区域报警 */
    uint64_t gpioOut2_AlarmType = 0;

    /* GPIO_OUT2 的输出模式  -1：无定义   0：只输出一次（报警时拉高持续2s，间隔10s）  1：持续输出  2：相反输出（报警时拉低持续500ms，间隔500s） */
    int gpioOut2_OuputType = -1;

    /* 当喇叭输出的声音为语音时，语音的语言类型  0：中文  1：英文 */
    int speaker_sound_language = 0;

    /* 侧边报警1级报警需要保存的证据    用bit位表示开关  0-否   1-是     bit0-视频   bit1-图片     */
    int bsd_alarm_level1_evidence = 0;

    /* 侧边报警2级报警需要保存的证据    用bit位表示开关  0-否   1-是     bit0-视频   bit1-图片     */
    int bsd_alarm_level2_evidence = 0;

    /* 侧边行人1级报警同ID报警类型，目前只有：0-同ID只报一次   1-同ID持续报警 */
    int bsd_same_id_alram_type_level1 = 1;
    /* 侧边行人1级报警趋势过滤规则  长度为1个字节  用bit位表示开关  0-否   1-是     bit0-静止的人是否不需要报警   bit1-靠近镜头的人是否不需要报警  bit2-远离镜头的人是否不需要报警 */
    int bsd_alarm_trend_shielding_level1 = 0;
    /* 侧边行人1级报警同ID报警类型，目前只有：0-同ID只报一次   1-同ID持续报警 */
    int bsd_same_id_alram_type_level2 = 1;
    /* 侧边行人1级报警趋势过滤规则  长度为1个字节  用bit位表示开关  0-否   1-是     bit0-静止的人是否不需要报警   bit1-靠近镜头的人是否不需要报警  bit2-远离镜头的人是否不需要报警 */
    int bsd_alarm_trend_shielding_level2 = 0;
    /* 当G3的工作模式为1时，将会使用此IP作为MRV220的IP地址 */
    std::string MRV220_ip_adress = G3_IP_SLOT_POSITION_2;
    /* 相机1使用的实景UDP端口 */
    int cam1_realview_port = G3_UDP_CAMERA1_PORT_SLOT_POSITION_2;
    /* 相机2使用的实景UDP端口 */
    int cam2_realview_port = G3_UDP_CAMERA2_PORT_SLOT_POSITION_2;
    /* 相机3使用的实景UDP端口 */
    int cam3_realview_port = G3_UDP_CAMERA3_PORT_SLOT_POSITION_2;
    /* 相机4使用的实景UDP端口 */
    int cam4_realview_port = G3_UDP_CAMERA4_PORT_SLOT_POSITION_2;
    /* 侧边行人侧边报警报警级别1的喇嘛鸣响模式  0：一收到报警就响   1：同ID只响一次 */
    int bsd_alarm_level1_speaker_ringing_model = 0;
    /* 侧边行人侧边报警报警级别2的喇嘛鸣响模式  0：一收到报警就响   1：同ID只响一次 */
    int bsd_alarm_level2_speaker_ringing_model = 0;


    /* sbst的后向镜头的后向镜头的变线辅助的左边车辆报警区域1的六个点 */
    std::vector<VISPoint> sbst_vehicle_alram_area_level1_left;
    /* sbst的后向镜头的后向镜头的变线辅助的左边车辆报警区域2的六个点 */
    std::vector<VISPoint> sbst_vehicle_alram_area_level2_left;
    /* sbst的后向镜头的后向镜头的变线辅助的右边车辆报警区域1的六个点 */
    std::vector<VISPoint> sbst_vehicle_alram_area_level1_right;
    /* sbst的后向镜头的后向镜头的变线辅助的右边车辆报警区域2的六个点 */
    std::vector<VISPoint> sbst_vehicle_alram_area_level2_right;

    /* sbst前向ADAS行人报警级别1  速度区间的最低车速 */
    int sbst_forward_pedestrian_min_alram_speed_level1 = 0;
    /* sbst前向ADAS行人报警级别1  速度区间的最高车速 */
    int sbst_forward_pedestrian_max_alram_speed_level1 = 60;
    /* sbst前向ADAS行人报警级别2  速度区间的最低车速 */
    int sbst_forward_pedestrian_min_alram_speed_level2 = 0;
    /* sbst前向ADAS行人报警级别2  速度区间的最高车速 */
    int sbst_forward_pedestrian_max_alram_speed_level2 = 60;

    /* sbst前向ADAS车辆报警级别1  速度区间的最低车速 */
    int sbst_forward_vehicle_min_alram_speed_level1 = 30;
    /* sbst前向ADAS车辆报警级别1  速度区间的最高车速 */
    int sbst_forward_vehicle_max_alram_speed_level1 = 90;
    /* sbst前向ADAS车辆报警级别2  速度区间的最低车速 */
    int sbst_forward_vehicle_min_alram_speed_level2 = 30;
    /* sbst前向ADAS车辆报警级别2  速度区间的最高车速 */
    int sbst_forward_vehicle_max_alram_speed_level2 = 90;

    /* sbst侧边算法行人报警级别1  速度区间的最低车速 */
    int sbst_sideward_pedestrian_min_alram_speed_level1 = 0;
    /* sbst侧边算法行人报警级别1  速度区间的最高车速 */
    int sbst_sideward_pedestrian_max_alram_speed_level1 = 60;
    /* sbst侧边算法行人报警级别2  速度区间的最低车速 */
    int sbst_sideward_pedestrian_min_alram_speed_level2 = 0;
    /* sbst侧边算法行人报警级别2  速度区间的最高车速  */
    int sbst_sideward_pedestrian_max_alram_speed_level2 = 60;

    /* sbst侧边算法车辆报警级别1  速度区间的最低车速 */
    int sbst_sideward_vehicle_min_alram_speed_level1 = 30;
    /* sbst侧边算法车辆报警级别1  速度区间的最高车速 */
    int sbst_sideward_vehicle_max_alram_speed_level1 = 90;
    /* sbst侧边算法车辆报警级别2  速度区间的最低车速 */
    int sbst_sideward_vehicle_min_alram_speed_level2 = 30;
    /* sbst侧边算法车辆报警级别2  速度区间的最高车速 */
    int sbst_sideward_vehicle_max_alram_speed_level2 = 90;

    /* 默认车速 */
    int default_speed = 10;
    /* H264码流等级 1:1M  2：2M  3:3M  4:4M */
    int h264_bit_rate_level = 4;

    /* sbst前向ADAS压线报警  速度区间的最低车速 */
    int sbst_forward_ldw_min_alram_speed = 10;
    /* sbst前向ADAS压线报警  速度区间的最高车速 */
    int sbst_forward_ldw_max_alram_speed = 1;
    /* 侧边报警是否需要依据转向灯  如果需要依据 那么只有打了灯才报警    0：不需要  1：需要 */
    int sbst_sideward_alarm_need_turn_light = 0;
    /* 行人报警是否需要判断可行使区域   0：不需要  1：需要 */
    int pedestrianAlarmNeedTravelableArea = 0;
    /* 车辆报警是否需要判断可行使区域   0：不需要  1：需要 */
    int vehicleAlarmNeedTravelableArea = 0;
    /* 镜头1的行人报警区域是否需要根据转向灯变化   0：不需要  1：需要 */
    int pedestrianAlarmNeedChangeByTurnSign_camera0 = 0;
    /* 镜头2的行人报警区域是否需要根据转向灯变化   0：不需要  1：需要 */
    int pedestrianAlarmNeedChangeByTurnSign_camera1 = 0;
    /* 行人的报警是否需要根据开门信号过滤   0：不需要  1：打开车门后不报警 */
    int pedestrianAlarmNeedDoorSign = 0;
    /* 镜头1的车辆报警区域是否需要根据转向灯变化   0：不需要  1：需要 */
    int vehicleAlarmNeedChangeByTurnSign_camera0 = 0;
    /* 镜头2的车辆报警区域是否需要根据转向灯变化   0：不需要  1：需要 */
    int vehicleAlarmNeedChangeByTurnSign_camera1 = 0;
    /* sbst镜头1打转向灯时需要变化车辆报警区域的最低车速要求 */
    int sbst_change_vehicle_alram_area_by_turnlight_min_speed_camera0 = 0;
    /* sbst镜头1打转向灯时需要变化车辆报警区域的最高车速要求 */
    int sbst_change_vehicle_alram_area_by_turnlight_max_speed_camera0 = 30;
    /* sbst镜头2打转向灯时需要变化车辆报警区域的最低车速要求 */
    int sbst_change_vehicle_alram_area_by_turnlight_min_speed_camera1 = 0;
    /* sbst镜头2打转向灯时需要变化车辆报警区域的最高车速要求 */
    int sbst_change_vehicle_alram_area_by_turnlight_max_speed_camera1 = 30;
    /* ADAS车道线车道线报警的启动车速 */
    int adas_vehicle_ldw_alarm_min_speed = 30;
    /* ADAS车道线报警的灵敏度（车辆边缘距离车道线多少才报）  单位：cm */
    int adas_vehicle_ldw_alarm_sensitivity = -15;
    /* 报警开关1的值   用bit位表示镜头报警是否打开  bit0:表示镜头1可以输出报警  bit1:表示镜头2可以输出报警 */
    Camera_Alarm_Switch_Info camera_alarm_switch_1 = {true,true};
    /* 报警开关2的值   用bit位表示镜头报警是否打开  bit0:表示镜头1可以输出报警  bit1:表示镜头2可以输出报警 */
    Camera_Alarm_Switch_Info camera_alarm_switch_2 = {true,true};
    /* 报警开关3的值   用bit位表示镜头报警是否打开  bit0:表示镜头1可以输出报警  bit1:表示镜头2可以输出报警 */
    Camera_Alarm_Switch_Info camera_alarm_switch_3 = {true,true};
    /* G3的显示屏显示模式   0：两个镜头画面分屏（显示部分）  1：只显示镜头1  2：只显示镜头2 */
    int g3_display_mode = 0;
    /* 镜头报警是否需要依据镜头报警开关 0：不需要依据   1：需要依据 */
    int cameraAlarmNeedCameraAlarmSwitch = 0;
    /* G3的显示屏不输出镜头画面的速度阈值，当车速大于等于这个阈值时，显示屏就不显示镜头画面  单位：KM/H */
    int g3_not_display_speed = 400;
    /* 当收到倒车信号后，需要在显示屏上全屏显示的镜头ID   0：不需要全屏显示某个镜头照原定的模式显示  1：全屏显示镜头1的画面  2：全屏显示镜头2的画面 */
    int reverse_dispaly_cameraId = 0;
    /* FCW启动车速（KM/H） */
    int adas_fcw_start_speed = 40;
    /* LDW启动车速（KM/H） */
    int adas_ldw_start_speed = 60;
    /* PDW停止车速（KM/H） */
    int adas_pdw_stop_speed = 60;
    /* ADAS相机高度（毫米） */
    int adas_camera_height = 1200;
    /* 车宽（毫米） */
    int adas_vehicle_width = 1800;
    /* 摄像头到车头距离（毫米） */
    int adas_camera_depth = 1000;
    /* 相机相对中线偏移（毫米） */
    int adas_camera_centerline_offset = 0;
    /* 相机到前轮车轴距离（毫米） */
    int adas_camera_front_wheel_axle_distance = 900;
    /* 天地线位置（像素） */
    int adas_horizon_y = 360;
    /* ttc报警级别1的阈值。（单位0.1s） */
    int adas_ttc_threshold_level1 = 27;
    /* ttc报警级别2的阈值。（单位0.1s） */
    int adas_ttc_threshold_level2 = 27;
    /* ttc报警级别3的阈值。（单位0.1s） */
    int adas_ttc_threshold_level3 = 27;
    /* ttc报警级别4的阈值。（单位0.1s） */
    int adas_ttc_threshold_level4 = 27;
    /* headway报警级别1的阈值。（单位0.1s） */
    int adas_headway_threshold_level1 = 12;
    /* headway报警级别2的阈值。（单位0.1s） */
    int adas_headway_threshold_level2 = 12;
    /* headway报警级别3的阈值。（单位0.1s） */
    int adas_headway_threshold_level3 = 12;
    /* headway报警级别4的阈值。（单位0.1s） */
    int adas_headway_threshold_level4 = 12;
    /* 行人碰撞报警级别1的阈值。（单位0.1s） */
    int adas_pd_collison_threshold_level1 = 12;
    /* 行人碰撞报警级别2的阈值。（单位0.1s） */
    int adas_pd_collison_threshold_level2 = 12;
    /* 行人碰撞报警级别3的阈值。（单位0.1s） */
    int adas_pd_collison_threshold_level3 = 12;
    /* 行人碰撞报警级别4的阈值。（单位0.1s） */
    int adas_pd_collison_threshold_level4 = 12;
    /* 车道偏移报警级别1的阈值。（毫米） */
    int adas_ldw_distance_threshold_level1 = 0;
    /* 车道偏移报警级别2的阈值。（毫米） */
    int adas_ldw_distance_threshold_level2 = 0;
    /* 车道偏移报警级别3的阈值。（毫米） */
    int adas_ldw_distance_threshold_level3 = 0;
    /* 车道偏移报警级别4的阈值。（毫米） */
    int adas_ldw_distance_threshold_level4 = 0;
    /* 前车溜车报警的阈值。（毫米） */
    int adas_vb_distance_threshold = 100;
    /* 前车启动报警阈值（单位0.1s） */
    int adas_go_threshold = 10;
    /* 英国工程机械-BSD镜头-目标检测的类阈值 */
    float td_classThreshold = 0.85f;
    /* 英国工程机械-BSD镜头-目标检测的行人类阈值 */
    float td_classThreshold_person = 0.85f;



    /* 图像的宽 */
    int adas_ori_width = 1280;
    /* 图像的高 */
    int adas_ori_height = 720;
    /* 相机内参矩阵 */
    double adas_fx = 473.9016027375025;
    /* 相机内参矩阵 */
    double adas_fy = 474.0320429514123;
    /* 相机内参矩阵 */
    double adas_u0 = 604.1656922272235;
    /* 相机内参矩阵 */
    double adas_v0 = 352.9479711737991;
    /* 相机畸变 */
    double adas_k1 = -0.2525584251468569;
    /* 相机畸变 */
    double adas_k2 = 0.05326890193801768;
    /* 相机畸变 */
    double adas_p1 = 0.001327575593440069;
    /* 相机畸变 */
    double adas_p2 = 0.0009155310698896944;
    /* 相机畸变 */
    double adas_k3 = -0.004354828414744483;
    /* 外参矩阵 */
    double adas_m0_0 = 0.998342;
    /* 外参矩阵 */
    double adas_m0_1 = 0.000712241;
    /* 外参矩阵 */
    double adas_m0_2 = 0.0575603;
    /* 外参矩阵 */
    double adas_m0_3 = 0;
    /* 外参矩阵 */
    double adas_m1_0 = 0.0299839;
    /* 外参矩阵 */
    double adas_m1_1 = 0.847135;
    /* 外参矩阵 */
    double adas_m1_2 = -0.530532;
    /* 外参矩阵 */
    double adas_m1_3 = 0;
    /* 外参矩阵 */
    double adas_m2_0 = -0.0491392;
    /* 外参矩阵 */
    double adas_m2_1 = 0.531378;
    /* 外参矩阵 */
    double adas_m2_2 = 0.845708;
    /* 外参矩阵 */
    double adas_m2_3 = 0;
    /* 外参矩阵 */
    double adas_m3_0 = 0;
    /* 外参矩阵 */
    double adas_m3_1 = 0;
    /* 外参矩阵 */
    double adas_m3_2 = 0;
    /* 外参矩阵 */
    double adas_m3_3 = 0;
    /* 垂直线坐标 */
    int adas_vertical_line = 640;

    /* NDS的MQTT的域名 */
    std::string ndsMqttHost = "portal.safetyshieldvue.com";
    /* NDSMQTT的端口 */
    int ndsMqttPort = 43332;
    /* NDS的HTTP的域名 */
    std::string ndsHttpHost = "portal.safetyshieldvue.com";
    /* NDS的HTTP的端口 */
    int ndsHttpPort = 443;

    // 德国R151认证中，镜头的1中的行人报警区域1的坐标点
    std::vector<VISPoint> camera1_r151_pedestrian_alram_area1;
    // 德国R151认证中，镜头的1中的行人报警区域2的坐标点
    std::vector<VISPoint> camera1_r151_pedestrian_alram_area2;
    // 德国R151认证中，镜头的1中的行人报警区域3的坐标点
    std::vector<VISPoint> camera1_r151_pedestrian_alram_area3;
    // 德国R151认证中，镜头的1中的行人报警区域4的坐标点
    std::vector<VISPoint> camera1_r151_pedestrian_alram_area4;
    // 德国R151认证中，镜头的1中的车辆报警区域1的坐标点
    std::vector<VISPoint> camera1_r151_vehicle_alram_area1;
    // 德国R151认证中，镜头的1中的车辆报警区域2的坐标点
    std::vector<VISPoint> camera1_r151_vehicle_alram_area2;






    /* 德国R151认证中，镜头的2中的行人报警区域1的坐标点 */
    std::vector<VISPoint> camera2_r151_pedestrian_alram_area1;
    /* 德国R151认证中，镜头的2中的行人报警区域2的坐标点 */
    std::vector<VISPoint> camera2_r151_pedestrian_alram_area2;
    /* 德国R151认证中，镜头的2中的行人报警区域3的坐标点 */
    std::vector<VISPoint> camera2_r151_pedestrian_alram_area3;
    /* 德国R151认证中，镜头的2中的行人报警区域4的坐标点 */
    std::vector<VISPoint> camera2_r151_pedestrian_alram_area4;
    /* 德国R151认证中，镜头的2中的车辆报警区域1的坐标点 */
    std::vector<VISPoint> camera2_r151_vehicle_alram_area1;
    /* 德国R151认证中，镜头的2中的车辆报警区域2的坐标点 */
    std::vector<VISPoint> camera2_r151_vehicle_alram_area2;

    /* 左/右置方向盘标志   0为左置方向盘（如中国大陆）   1为右置方向盘（如中国香港） */
    int steeringwheelPosition = 0;

    /* R151区域1行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r151_pedestrian_alarm_speaker_model_area1 = 0;
    /* R151区域2行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r151_pedestrian_alarm_speaker_model_area2 = 0;
    /* R151区域3行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r151_pedestrian_alarm_speaker_model_area3 = 0;
    /* R151区域4行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r151_pedestrian_alarm_speaker_model_area4 = 0;

    /* R151区域1车辆报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r151_vehicle_alarm_speaker_model_area1 = 0;
    /* R151区域2车辆报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r151_vehicle_alarm_speaker_model_area2 = 0;
    /* R151的显示屏默认显示画面   0：两个镜头画面分屏（显示部分）  1：只显示镜头1  2：只显示镜头2 */
    int r151_default_display_screen = 0;
    /* R151的镜头遮挡报警的间隔时间  单位:s */
    int r151_camera_cover_time_interval = 1800;
    /* R151的不识别行人的车速阈值，单位km/h （车速大于等于这个阈值就不识别行人了） */
    int r151_pedestrian_detect_stop_speed = 35;
    /* R151的开始识别车辆的车速阈值，单位km/h （车速大于等于这个阈值才识别车辆） */
    int r151_vehicle_detect_start_speed = 37;
    /* R151区域1行人报警的输出模式   0：同ID的行人只报一次  1：同ID的行人持续报警 */
    int r151_pedestrian_alarm_output_model_area1 = 1;
    /* R151区域2行人报警的输出模式   0：同ID的行人只报一次  1：同ID的行人持续报警 */
    int r151_pedestrian_alarm_output_model_area2 = 1;
    /* R151区域3行人报警的输出模式   0：同ID的行人只报一次  1：同ID的行人持续报警 */
    int r151_pedestrian_alarm_output_model_area3 = 1;
    /* R151区域4行人报警的输出模式   0：同ID的行人只报一次  1：同ID的行人持续报警 */
    int r151_pedestrian_alarm_output_model_area4 = 1;
    /* R151行人报警是否需要判断趋势 */
    int r151_pedestrian_alarm_need_trend = 0;





    /* 德国R158认证中，行人报警区域1的坐标点 */
    std::vector<VISPoint> r158_pedestrian_alram_area1;
    /* 德国R158认证中，行人报警区域2的坐标点 */
    std::vector<VISPoint> r158_pedestrian_alram_area2;
    /* 德国R158认证中，行人报警区域3的坐标点 */
    std::vector<VISPoint> r158_pedestrian_alram_area3;
    /* 德国R158认证中，车辆报警区域1的坐标点 */
    std::vector<VISPoint> r158_vehicle_alram_area1;
    /* 德国R158认证中，车辆报警区域2的坐标点 */
    std::vector<VISPoint> r158_vehicle_alram_area2;
    /* R158的停止识别的车速阈值，单位km/h （车速大于等于这个阈值就不进行任何识别了） */
    int r158_detect_stop_speed = 10;
    /* R158的屏幕显示是否叠加识别信息   (0:不叠加  1：叠加) */
    int r158_dispaly_detect_info_add = 0;
    /* R158行人报警趋势过滤规则  长度为1个字节  用bit位表示开关  0-否   1-是     bit0-静止的人是否不需要报警   bit1-靠近镜头的人是否不需要报警  bit2-远离镜头的人是否不需要报警 */
    int r158_pedestrian_alarm_trend_shielding = 0;

    /* 德国R159认证中，行人报警区域1的坐标点 */
    std::vector<VISPoint> r159_pedestrian_alram_area1;
    /* 德国R159认证中，行人报警区域2的坐标点 */
    std::vector<VISPoint> r159_pedestrian_alram_area2;
    /* 德国R159认证中，行人报警区域3的坐标点 */
    std::vector<VISPoint> r159_pedestrian_alram_area3;
    /* 德国R159认证中，车辆报警区域1的坐标点 */
    std::vector<VISPoint> r159_vehicle_alram_area1;
    /* 德国R159认证中，车辆报警区域2的坐标点 */
    std::vector<VISPoint> r159_vehicle_alram_area2;
    /* R159的停止识别的车速阈值，单位km/h （车速大于等于这个阈值就不进行任何识别了） */
    int r159_detect_stop_speed = 10;
    /* R159的屏幕显示是否叠加识别信息   (0:不叠加  1：叠加) */
    int r159_dispaly_detect_info_add = 0;
    /* R159行人报警趋势过滤规则  长度为1个字节  用bit位表示开关  0-否   1-是     bit0-静止的人是否不需要报警   bit1-靠近镜头的人是否不需要报警  bit2-远离镜头的人是否不需要报警 */
    int r159_pedestrian_alarm_trend_shielding = 0;
    /* R159的启动识别的车速阈值，单位km/h （车速大于等于这个阈值小于等于停止阈值才可以进行识别报警） */
    int r159_detect_start_speed = 0;
    /* R159报警同ID报警类型，目前只有：0-同ID只报一次   1-同ID持续报警 */
    int r159_same_id_alram_type = 1;


    /* R151区域1行人报警起始车速，单位km/h */
    int r151_pedestrian_alarm_area1_start_speed = 1;
    /* R151区域1行人报警停止车速，单位km/h */
    int r151_pedestrian_alarm_area1_stop_speed = 30;
    /* R151区域2行人报警起始车速，单位km/h */
    int r151_pedestrian_alarm_area2_start_speed = 0;
    /* R151区域2行人报警停止车速，单位km/h */
    int r151_pedestrian_alarm_area2_stop_speed = 30;
    /* R151区域3行人报警起始车速，单位km/h */
    int r151_pedestrian_alarm_area3_start_speed = 1;
    /* R151区域3行人报警停止车速，单位km/h */
    int r151_pedestrian_alarm_area3_stop_speed = 30;
    /* R151区域4行人报警起始车速，单位km/h */
    int r151_pedestrian_alarm_area4_start_speed = 1;
    /* R151区域4行人报警停止车速，单位km/h */
    int r151_pedestrian_alarm_area4_stop_speed = 30;
    /* R151行人报警区域4趋势过滤规则  长度为1个字节  用bit位表示开关  0-否   1-是     bit0-识别不出趋势的人不报警   bit1-靠近镜头的人不报警  bit2-远离镜头的人不报警  bit3-画面中从上到下的人不报警  bit4-画面中从下到上的人不报警  bit5-画面中从左到右的人不报警  bit6-画面中从右到左的人不报警  bit7-静止的人不报警 */
    int r151_pedestrian_alarm_area4_trend_shielding = 0;
    /* R151的屏幕显示是否叠加识别信息   (0:不叠加  1：叠加) */
    int r151_dispaly_detect_info_add = 0;
    /* 动静检测开关   0：关  1：开 */
    int detect_move_button = 0;
    /* 动静检测的灵敏度  范围：0~100 */
    int move_pixel = 80;
    /* 动静检测的提前结束帧数 */
    int frame_early = 6;


    /* 英国工程机械-BSD镜头-目标检测算法 是否开启影子报警   0：关闭  1：开启 */
    int td_improve_test_enable = 0;
    /* 英国工程机械-BSD镜头-目标检测算法 影子报警的阈值 */
    float td_classThreshold_improve_test = 0.65f;

    /* 新加坡巴士-160度相机-目标检测算法 是否开启影子报警   0：关闭  1：开启 */
    int SBST_160_OD_improve_test_enable = 0;
    /* 新加坡巴士-160度相机-目标检测算法 正式报警的阈值 */
    float SBST_160_OD_classThreshold = 0.85f;
    /* 新加坡巴士-160度相机-目标检测算法 影子报警的阈值（这个值一定要低于正式阈值） */
    float SBST_160_OD_classThreshold_improve_test = 0.65f;

    /* 德国公交车-BSD相机-目标检测算法 是否开启影子报警   0：关闭  1：开启 */
    int DE_BSD_OD_improve_test_enable = 0;
    /* 德国公交车-BSD相机-目标检测算法 正式报警的阈值 */
    float DE_BSD_OD_classThreshold = 0.85f;
    /* 德国公交车-BSD相机-目标检测算法 影子报警的阈值（这个值一定要低于正式阈值） */
    float DE_BSD_OD_classThreshold_improve_test = 0.65f;


    /* R158区域1行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r158_pedestrian_alarm_speaker_model_area1 = 1;
    /* R158区域2行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r158_pedestrian_alarm_speaker_model_area2 = 1;
    /* R158区域1车辆报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r158_vehicle_alarm_speaker_model_area1 = 1;
    /* R158区域2车辆报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r158_vehicle_alarm_speaker_model_area2 = 0;
    /* R159区域1行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r159_pedestrian_alarm_speaker_model_area1 = 1;
    /* R159区域2行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r159_pedestrian_alarm_speaker_model_area2 = 1;
    /* R159区域1车辆报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r159_vehicle_alarm_speaker_model_area1 = 1;
    /* R159区域2车辆报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r159_vehicle_alarm_speaker_model_area2 = 0;
    /* G3的显示屏输出信号类型：   0：CVBS（720*576）  1：AHD */
    int g3_display_output_signal_type = 0;
    /* 录制的多媒体文件是否需要加密   0：不需要  1：需要(AES-CBC-256) */
    int multimedia_files_encrypt_enable = 0;
    /* GPIO_OUT1 的输出关联的镜头有哪几个？  用bit位表示  bit0-：镜头1  bit1-：镜头2  bit2-：镜头3  bit3-：镜头4   */
    int gpioOut1_CameraEnbale = 3;
    /* GPIO_OUT2 的输出关联的镜头有哪几个？  用bit位表示  bit0-：镜头1  bit1-：镜头2  bit2-：镜头3  bit3-：镜头4   */
    int gpioOut2_CameraEnbale = 3;
    /* 侧边镜头的镜头遮挡报警的触发阈值（持续次数）   识别出被遮挡之后要持续这个阈值的帧数之后才能算真的被遮挡了 */
    int bsd_camcover_count_threshold = 2;
    /* 侧边镜头的镜头遮挡报警的输出间隔时间（单位：s），目前只控制喇叭 */
    int bsd_camcover_alarm_output_interval = 1800;
    /* R158区域3行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r158_pedestrian_alarm_speaker_model_area3 = 1;
    /* R159区域3行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int r159_pedestrian_alarm_speaker_model_area3 = 1;
    /* BSD侧边算法的区域1行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int bsd_pedestrian_alarm_speaker_model_area1 = 1;
    /* BSD侧边算法的区域2行人报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int bsd_pedestrian_alarm_speaker_model_area2 = 1;
    /* BSD侧边算法的区域1车辆报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int bsd_vehicle_alarm_speaker_model_area1 = 1;
    /* BSD侧边算法的区域2车辆报警时喇叭的工作模式   0：不响  1：响提示音（嘀嘀嘀）  2：响语音 */
    int bsd_vehicle_alarm_speaker_model_area2 = 1;
    /* 接在RS232口上的走modbus协议的按钮中，主按钮的定义   0：无d定义  1：消警按钮  2：sos按钮 */
    int r232_modbus_button_main_type = 1;
    /* 接在RS232口上的走modbus协议的按钮中，扩展按钮的定义   0：无d定义  1：消警按钮  2：sos按钮 */
    int r232_modbus_button_extension_type = 0;

    /* R151区域1车辆报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r151_vehicle_alarm_output_model_area1 = 1;
    /* R151区域2车辆报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r151_vehicle_alarm_output_model_area2 = 1;
    /* R158区域1行人报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r158_pedestrian_alarm_output_model_area1 = 1;
    /* R158区域2行人报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r158_pedestrian_alarm_output_model_area2 = 1;
    /* R158区域3行人报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r158_pedestrian_alarm_output_model_area3 = 1;
    /* R158区域1车辆报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r158_vehicle_alarm_output_model_area1 = 1;
    /* R158区域2车辆报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r158_vehicle_alarm_output_model_area2 = 1;
    /* R159区域1行人报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r159_pedestrian_alarm_output_model_area1 = 1;
    /* R159区域2行人报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r159_pedestrian_alarm_output_model_area2 = 1;
    /* R159区域3行人报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r159_pedestrian_alarm_output_model_area3 = 1;
    /* R159区域1车辆报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r159_vehicle_alarm_output_model_area1 = 1;
    /* R159区域2车辆报警的输出模式   0：同ID只报一次  1：同ID的行人持续报警 */
    int r159_vehicle_alarm_output_model_area2 = 1;

    /* MUV的相关参数 */
    int muv_dsm_debug_button = 0;
    int muv_dsm_adjusted_angle_button = 0;
    int muv_dsm_yaw_angle = 0;
    int muv_dsm_DebugBelt_button = 0;
    int muv_dsm_left_right = 0;
    VISPoint muv_dsm_seatbelt_point1{};
    VISPoint muv_dsm_seatbelt_point2{};
    VISPoint muv_dsm_seatbelt_point3{};
    VISPoint muv_dsm_seatbelt_point4{};
    VISPoint muv_dsm_seatbelt_point5{};
    /* 相机画面是否叠加时间水印  长度为1个字节  用bit位表示开关  0-否   1-是     bit0-镜头1是否叠加时间水印   bit1-镜头2是否叠加时间水印 */
    int camera_time_osd_add = 3;
    /* MVU_DSM盲点报警参数 */
    int blindspot_speed = 4;
    int blindspot_button = 1;
    int blindspot_mode = 0;
    int blindspot_sensitivity = 1;
    int event_blindspot_time = 2000;
    int freeze_blindspot_time = 1000;
    /* MVU_DSM安全带报警参数 */
    int seatbelt_speed = 3;
    int seatbelt_button = 1;
    int seatbelt_mode = 0;
    int seatbelt_sensitivity = 30;
    int event_seatbelt_time = 3000;
    int freeze_seatbelt_time = 1000;
    /* 是否需要进行车道线交点标定   0：不需要  1：需要 */
    int adas_lane_intersection_calc = 0;

    /* 高频模式下一个波形中拉高的时间  单位ms */
    int highKeepTimeForHighMode = 50;
    /* 高频模式下一个波形中拉低的时间  单位ms */
    int lowKeepTimeForHighMode = 50;
    /* 高频模式下输出一次报警需要输出的波形数 */
    int outputCountOneTimeForHighMode = 15;
    /* 低频模式下一个波形中拉高的时间  单位ms */
    int highKeepTimeForLowMode = 100;
    /* 低频模式下一个波形中拉低的时间  单位ms */
    int lowKeepTimeForLowMode = 100;
    /* 低频模式下输出一次报警需要输出的波形数 */
    int outputCountOneTimeForLowMode = 7;
    /* V6模式下，进入暗光模式的阈值，大于这个阈值就进入暗光模式，打开红外灯  （V6的光敏电阻读数范围为0-1024，读数越大说明环境越暗） */
    int startDimLighModetValue = 850;
    /* V6模式下，退出暗光模式的阈值，小于这个阈值就退出暗光模式，关闭红外灯  （V6的光敏电阻读数范围为0-1024，读数越大说明环境越暗） */
    int stopDimLightModeValue = 750;

    /* 小松-BSD镜头-目标检测算法 是否开启影子报警   0：关闭  1：开启 */
    int KOM_BSD_OD_improve_test_enable = 0;
    /* 小松-BSD镜头-目标检测的类阈值 */
    float KOM_BSD_OD_classThreshold = 0.65f;
    /* 小松-BSD镜头-目标检测算法 影子报警的阈值（这个值一定要低于正式阈值） */
    float KOM_BSD_OD_classThreshold_improve_test = 0.55f;
    /* 车辆TTC同ID过滤的冷却时间阈值  单位ms（在这个阈值时间内，同个车辆ID只能连续触发一次报警） */
    int vehicle_ttc_same_id_cooldown_threshold = 0;
    /* 根据车辆信号切换屏幕显示的规则列表 */
    std::vector<DisplaySwitchForSignalRule> displaySwitchForSignalRuleList;
    /* 根据信号切换屏幕显示模式时默认的显示画面   0：分屏  1：全屏显示镜头1  2：全屏显示镜头 */
    int displaySwitchForSignalDefaultDisplayType = 0;
    /* RS232口工作模式   0：停止按钮模式  1：Q-Control-01模式 */
    int rs232_work_mode = 0;
    /* ADAS镜头的焦距  单位：mm */
    int adas_camera_f = 6;
    /* 镜头画面的264数据封装格式   0：H264裸数据  1：UDP的视频传输协议格式v1.1（具体查看socket通信协议的附录4.6） */
    int realview_data_format = 0;
    /* ADAS算法的LDW是否需要虚线报警   0：不需要  1：需要 */
    int adas_ldw_dash_alarm_switch = 1;
    /* 相机算法工作限制的规则列表 */
    std::vector<CameraAlgWorkRule> camera_alg_work_rule_list;
    /* 叉车-160度镜头-目标检测算法 是否开启影子报警   0：关闭  1：开启 */
    int Forklift_160_OD_improve_test_enable = 0;
    /* 叉车-160度镜头-目标检测算法 正式报警的阈值 */
    float Forklift_160_OD_classThreshold = 0.85f;
    /* 叉车-160度镜头-目标检测算法 影子报警的阈值（这个值一定要低于正式阈值） */
    float Forklift_160_OD_classThreshold_improve_test = 0.65f;




    /* 镜头3的行人报警区域1的六个点 */
    std::vector<VISPoint> camera2_pedestrian_alram_area_level1;
    /* 镜头3的行人报警区域2的六个点 */
    std::vector<VISPoint> camera2_pedestrian_alram_area_level2;
    /* 镜头4的行人报警区域1的六个点 */
    std::vector<VISPoint> camera3_pedestrian_alram_area_level1;
    /* 镜头4的行人报警区域2的六个点 */
    std::vector<VISPoint> camera3_pedestrian_alram_area_level2;
    /* 镜头3的车辆报警区域1的六个点 */
    std::vector<VISPoint> camera2_vehicle_alram_area_level1;
    /* 镜头3的车辆报警区域2的六个点 */
    std::vector<VISPoint> camera2_vehicle_alram_area_level2;
    /* 镜头4的车辆报警区域1的六个点 */
    std::vector<VISPoint> camera3_vehicle_alram_area_level1;
    /* 镜头4的车辆报警区域2的六个点 */
    std::vector<VISPoint> camera3_vehicle_alram_area_level2;

    /*镜头3的车辆不报警区域*/
    std::vector<VISPoint> undetected_area_list_camera3[2];
    /*镜头4的车辆不报警区域*/
    std::vector<VISPoint> undetected_area_list_camera4[2];



    // sbst的镜头3当在特定车速区间内打了左转（打了左转向灯）后使用的车辆一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera3_turnleft_angle1_level1;
    // sbst的镜头3当在特定车速区间内打了左转（打了左转向灯）后使用的车辆二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera3_turnleft_angle1_level2;
    // sbst的镜头3当在特定车速区间内打了右转（打了右转向灯）后使用的车辆一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera3_turnright_angle1_level1;
    // sbst的镜头3当在特定车速区间内打了右转（打了右转向灯）后使用的车辆二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera3_turnright_angle1_level2;
    // sbst的镜头3当在特定车速区间内打了左转（打了左转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera3_turnleft_angle1_level1;
    // sbst的镜头3当在特定车速区间内打了左转（打了左转向灯）后使用的行人二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera3_turnleft_angle1_level2;
    // sbst的镜头3当在特定车速区间内打了右转（打了右转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera3_turnright_angle1_level1;
    // sbst的镜头3当在特定车速区间内打了右转（打了右转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera3_turnright_angle1_level2;

    // sbst的镜头4当在特定车速区间内打了左转（打了左转向灯）后使用的车辆一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera4_turnleft_angle1_level1;
    // sbst的镜头4当在特定车速区间内打了左转（打了左转向灯）后使用的车辆二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera4_turnleft_angle1_level2;
    // sbst的镜头4当在特定车速区间内打了右转（打了右转向灯）后使用的车辆一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera4_turnright_angle1_level1;
    // sbst的镜头4当在特定车速区间内打了右转（打了右转向灯）后使用的车辆二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_vehicle_alram_area_camera4_turnright_angle1_level2;
    // sbst的镜头4当在特定车速区间内打了左转（打了左转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera4_turnleft_angle1_level1;
    // sbst的镜头4当在特定车速区间内打了左转（打了左转向灯）后使用的行人二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera4_turnleft_angle1_level2;
    // sbst的镜头4当在特定车速区间内打了右转（打了右转向灯）后使用的行人一级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera4_turnright_angle1_level1;
    // sbst的镜头4当在特定车速区间内打了右转（打了右转向灯）后使用的行人二级报警区域的六个点（舵角级别1）
    std::vector<VISPoint> sbst_pedestrian_alram_area_camera4_turnright_angle1_level2;
    /* 镜头3的行人报警区域是否需要根据转向灯变化   0：不需要  1：需要 */
    int pedestrianAlarmNeedChangeByTurnSign_camera2 = 0;
    /* 镜头4的行人报警区域是否需要根据转向灯变化   0：不需要  1：需要 */
    int pedestrianAlarmNeedChangeByTurnSign_camera3 = 0;
    /* 镜头3的车辆报警区域是否需要根据转向灯变化   0：不需要  1：需要 */
    int vehicleAlarmNeedChangeByTurnSign_camera2 = 0;
    /* 镜头4的车辆报警区域是否需要根据转向灯变化   0：不需要  1：需要 */
    int vehicleAlarmNeedChangeByTurnSign_camera3 = 0;
    /* sbst镜头3打转向灯时需要变化车辆报警区域的最低车速要求 */
    int sbst_change_vehicle_alram_area_by_turnlight_min_speed_camera2 = 0;
    /* sbst镜头3打转向灯时需要变化车辆报警区域的最高车速要求 */
    int sbst_change_vehicle_alram_area_by_turnlight_max_speed_camera2 = 30;
    /* sbst镜头4打转向灯时需要变化车辆报警区域的最低车速要求 */
    int sbst_change_vehicle_alram_area_by_turnlight_min_speed_camera3 = 0;
    /* sbst镜头4打转向灯时需要变化车辆报警区域的最高车速要求 */
    int sbst_change_vehicle_alram_area_by_turnlight_max_speed_camera3 = 30;
    // 德国R151认证中，镜头的3中的行人报警区域1的坐标点
    std::vector<VISPoint> camera3_r151_pedestrian_alram_area1;
    // 德国R151认证中，镜头的3中的行人报警区域2的坐标点
    std::vector<VISPoint> camera3_r151_pedestrian_alram_area2;
    // 德国R151认证中，镜头的3中的行人报警区域3的坐标点
    std::vector<VISPoint> camera3_r151_pedestrian_alram_area3;
    // 德国R151认证中，镜头的3中的行人报警区域4的坐标点
    std::vector<VISPoint> camera3_r151_pedestrian_alram_area4;
    // 德国R151认证中，镜头的3中的车辆报警区域1的坐标点
    std::vector<VISPoint> camera3_r151_vehicle_alram_area1;
    // 德国R151认证中，镜头的3中的车辆报警区域2的坐标点
    std::vector<VISPoint> camera3_r151_vehicle_alram_area2;
    /* 德国R151认证中，镜头的4中的行人报警区域1的坐标点 */
    std::vector<VISPoint> camera4_r151_pedestrian_alram_area1;
    /* 德国R151认证中，镜头的4中的行人报警区域2的坐标点 */
    std::vector<VISPoint> camera4_r151_pedestrian_alram_area2;
    /* 德国R151认证中，镜头的4中的行人报警区域3的坐标点 */
    std::vector<VISPoint> camera4_r151_pedestrian_alram_area3;
    /* 德国R151认证中，镜头的4中的行人报警区域4的坐标点 */
    std::vector<VISPoint> camera4_r151_pedestrian_alram_area4;
    /* 德国R151认证中，镜头的4中的车辆报警区域1的坐标点 */
    std::vector<VISPoint> camera4_r151_vehicle_alram_area1;
    /* 德国R151认证中，镜头的4中的车辆报警区域2的坐标点 */
    std::vector<VISPoint> camera4_r151_vehicle_alram_area2;

    /* 镜头0的security模式行人报警区域1的六个点 */
    std::vector<VISPoint> camera0_security_model_pedestrian_alram_area_level1;
    /* 镜头0的security模式行人报警区域2的六个点 */
    std::vector<VISPoint> camera0_security_model_pedestrian_alram_area_level2;
    /* 镜头1的security模式行人报警区域1的六个点 */
    std::vector<VISPoint> camera1_security_model_pedestrian_alram_area_level1;
    /* 镜头1的security模式行人报警区域2的六个点 */
    std::vector<VISPoint> camera1_security_model_pedestrian_alram_area_level2;
    /* 镜头2的security模式行人报警区域1的六个点 */
    std::vector<VISPoint> camera2_security_model_pedestrian_alram_area_level1;
    /* 镜头2的security模式行人报警区域2的六个点 */
    std::vector<VISPoint> camera2_security_model_pedestrian_alram_area_level2;
    /* 镜头3的security模式行人报警区域1的六个点 */
    std::vector<VISPoint> camera3_security_model_pedestrian_alram_area_level1;
    /* 镜头3的security模式行人报警区域2的六个点 */
    std::vector<VISPoint> camera3_security_model_pedestrian_alram_area_level2;

    /* R151行人报警区域3趋势过滤规则  长度为1个字节  用bit位表示开关  0-否   1-是     bit0-识别不出趋势的人不报警   bit1-靠近镜头的人不报警  bit2-远离镜头的人不报警  bit3-画面中从上到下的人不报警  bit4-画面中从下到上的人不报警  bit5-画面中从左到右的人不报警  bit6-画面中从右到左的人不报警  bit7-静止的人不报警 */
    int r151_pedestrian_alarm_area3_trend_shielding = 0;
    /* C3的切换规则列表 */
    std::vector<C3SwitchRule> c3_swicth_rule_list;
    /* GSensor的采样间隔，单位：ms */
    int gsensor_sampling_interval = 100;
    /* 相机帧率（相机图像获取、编码、显示屏显示都使用这个帧率） */
    int camera_frame_rate = 25;
    /* DSM帽子报警的车速 */
    int hat_speed = 5;
    /* DSM帽子报警的开关 */
    int hat_button = 1;
    /* DSM帽子报警模式 */
    int hat_mode = 0;
    /* DSM帽子报警灵敏度（得分）   单位：%  范围0~100 */
    int hat_sensitivity = 25;
    /* DSM帽子报警识别时间   单位：ms */
    int hat_eventime = 4000;
    /* SM帽子报警冷却时间   单位：ms */
    int hat_freetime = 4000;
    /* 需要录制的报警证据类型 */
    AlarmEvidenceTypeList alarm_evidence_types = {true, false, 0};
    /* 需要连接云平台类型 */
    CloundPlatformTypeList cloud_platform_types = {false, 0};
    /* 是否需要在报警证据里添加识别信息 */
    AddDetectInfoAlarmEvidenceTypes add_detectinfo_alarm_evidence = {false, false, 0};
    /* 算法串行执行的规则 */
    std::vector<AlgSerialRuleItem> alg_serial_rule;
    /* 取消headway报警的增加时间  单位：s（例：原本1.0的报警时间，取消报警状态就+0.2s（Cancel_headway_alarm））   单位：s */
    float adas_cancel_headway_alarm = 0.2;
    /* 是否开启adas的行人报警功能    0：关闭  1：开启 */
    int adas_pdw_switch = 1;
    /* 是否开启adas的车距过近报警和前车防止碰撞报警功能    0：关闭  1：开启 */
    int adas_fcw_switch = 1;
    /* 是否开启adas的车道偏离报警功能    0：关闭  1：开启 */
    int adas_ldw_switch = 1;
    /* 是否开启adas的虚拟保险杠报警功能    0：关闭  1：开启 */
    int adas_vb_switch = 1;
    /* 是否开启adas的前车启动报警的功能    0：关闭  1：开启 */
    int adas_go_switch = 1;
    /* 当前的镜头图像来源 */
    int cameraImageSource = CAMERA_IMAGE_SOURCE_DE_KOM_AVTP_IPC;


    /***********************************************以下不属于配置表的内容*******************************************************/
    /* 在G4上的槽位 */
    int slotPosition = 0;
    /* CPU序列号 */
    std::string cpuSerialNumber;
    /* 设备UUID */
    std::string deviceUUID;
    /* 被锁功能 */
    Locked_Function lockFunction = {false, false, 0};
    /* 工程机械操作的状态 */
    uint8_t machineryActionBarsStatus = -1;
    /* 镜头报警开关的状态 */
    Camera_Alarm_Switch_Status cameraAlarmSwitchStatus = {true,true,true};
    /* 现在是否处于厂测模式 */
    bool visPTMModel = false;
    /* 相机1的UDP端口 */
    int camera_udp_port_1 = cam1_realview_port;
    /* 相机2的UDP端口 */
    int camera_udp_port_2 = cam2_realview_port;
    /* 相机3的UDP端口 */
    int camera_udp_port_3 = cam3_realview_port;
    /* 相机4的UDP端口 */
    int camera_udp_port_4 = cam4_realview_port;
    /* 跟NDS通信的mqtt的连接参数 */
    MqttConnectParams ndsMqttConnectParams;
    /* 功能锁文本内容 */
    std::string lockstr;
    /* 当前的GPS信息 */
    GPSInfo curGPSInfo;
    /* TF卡是否插入 */
    bool hasTFCard = false;
    /* 是否处于高温状态 */
    bool hasHightTemperature = false;
    /* 通过IO口拿到的车辆状态信息 */
    VehicleStatusFromIO vehicleStatusFromIo;
    /* 镜头1是否打开 */
    bool camera1opened = false;
    /* 镜头2是否打开 */
    bool camera2opened = false;
    /* 镜头3是否打开 */
    bool camera3opened = false;
    /* 镜头4是否打开 */
    bool camera4opened = false;
    /* AES-256-CBC加密使用的KEY */
    uint8_t multimedia_files_encrypt_key[32] = {0x00};
    /* AES-256-CBC加密使用的IV */
    uint8_t multimedia_files_encrypt_iv[16] = {0x00};
    /* 镜头输入过滤器 */
    Camera_Input_Type_Filter cameraInputTypeFilter = {true, true, true, true, 0};
    /* L4的协议版本号 */
    float l4protocoVersion = -1;
    /* 当前的镜头降低效率信息 */
    CameraReduceEffectInfo curCameraReduceEffectInfo = {false, false, false, false, 0};
    /* 降效图片的缩放比 */
    float reduceEffectImgZoomRate = 0.5;
    /* 当前使用的文件存储器的路径 */
    std::string curFileStorageRoot = TF_CARD_ROOT_PATH;
    /* 当前的核心板类型 */
    COREBOARDTYPE coreBoardType = COREBOARDTYPE_MRV220;
    /* 当前设备是否能连接互联网 */
    bool internetConnected = false;
    /* 当前NDS的注册状态 */
    bool ndsRegistered = false;
    /* 当前NDS的连接状态 */
    bool ndsConnected = false;



};


#endif //VIS_G3_SOFTWARE_G3_CONFIGURATION_H
