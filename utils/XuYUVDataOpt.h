//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/28.
//

#ifndef VIS_G3_SOFTWARE_XUYUVDATAOPT_H
#define VIS_G3_SOFTWARE_XUYUVDATAOPT_H

#include <cstdint>
#include "ascii1632.h"


struct YUVDataOpt_YuvColor {
    uint8_t Y;
    uint8_t U;
    uint8_t V;
};

struct YUVDataOpt_Point {
    uint16_t x = 0;
    uint16_t y = 0;
};

struct YUVDataOpt_DrawLineInfo {
    YUVDataOpt_Point startPoint;
    YUVDataOpt_Point endPoint;
    uint16_t lineWidth;
    uint8_t isFull;
};

class XuYUVDataOpt {

private:
    unsigned char *asciiPoint = ascii;  // 全局指针，指向字码库的数组

    const YUVDataOpt_YuvColor s_color_table[11] = {
            {0x90, 0x35, 0x22}, // green
            {0x4E, 0x53, 0xFE}, // red
            {0x00, 0xff, 0x00},    // blue
            {0x00, 0xff, 0xff},    // purple
            {0xff, 0x00, 0x00}, // dark green
            {0xD9, 0x07, 0x9B}, // yellow
            {0xff, 0xff, 0x00}, // light blue
            {0xff, 0xff, 0xff}, // light purple
            {0x00, 0x80, 0x80}, // dark black
            {0x80, 0x80, 0x80}, // gray
            {0xff, 0x80, 0x80}, // white
    };
    //需要绘画的坐标点的数组
    YUVDataOpt_Point *drawpoints = nullptr;

public:

    typedef enum {
        /* 只画文字 */
        TYPE_ONLY_TEXT = 0,
        /* 画实线 */
        TYPE_SOLID_LINE,
        /* 画虚线线 */
        TYPE_DASHED_LINE,
        /* 画矩形 */
        TYPE_RECTANGLE,
        /* 画点 */
        TYPE_ONLY_POINT,
        /* 画四边形 */
        TYPE_QUADRILATERAL,
        /* 画多边形（实线） */
        TYPE_POLYGON,
        /* 画多边形（虚线） */
        TYPE_POLYGON_DASHEDLINE,

    } drawType;


    static const int COLOR_GREEN = 0;
    static const int COLOR_RED = 1;
    static const int COLOR_BLUE = 2;
    static const int COLOR_PURPLE = 3;
    static const int COLOR_DARK_GREEN = 4;
    static const int COLOR_YELLOW = 5;
    static const int COLOR_LIGHT_BLUE = 6;
    static const int COLOR_LIGHT_PURPLE = 7;
    static const int COLOR_DARK_BLACK = 8;
    static const int COLOR_GRAY = 9;
    static const int COLOR_WHITE = 10;


    /**
     * 在NV21的数据上面画文字或者框
     *
     * @param p1x : 点1的X坐标
     *
     * */
    int drawToNV21(int p1x, int p1y, int p2x, int p2y, int p3x, int p3y, int p4x, int p4y, const char *str, int width,
                   int height, uint8_t *nv21data, int colorid, int type);


    /**
    * 在NV21的数据上面画文字或者框
    *
    * @param p1x : 点1的X坐标
    *
    * */
    int
    drawToNV21(const YUVDataOpt_Point *pointList, int len, const char *str, int width, int height, uint8_t *nv21data,
               int colorid, int type,int lineWidth = 3);

    /**
    * 在NV21的数据上面画文字或者框
    *
    * @param p1x : 点1的X坐标
    *
    * */
    int
    drawAdasLanelineToNV21(const YUVDataOpt_Point &p1, const YUVDataOpt_Point &p2 ,const double mA0,const double mA1,const double mA2,const double mA3,int width, int height, uint8_t *nv21data,const int colorId);

    /**
     *  叠加一个字
     *
     * img  一帧图像
     * x    x坐标
     * y    y坐标
     * c    指定要叠加的一个字
     * fontHeight   使用的字体的高度
     * fontWidth    使用的字体的宽度
     */
    unsigned int
    _AddChar(unsigned int width, unsigned int height, unsigned int x, unsigned int y, const unsigned char c,
             unsigned int fontHeight, unsigned int fontWidth, unsigned int indexListoffset);


    /**
     *  叠加一条字符串
     *
     * width   图像的宽
     * height  图像的高
     * x    x坐标
     * y    y坐标
     * c    指定要叠加的字符串的指针
     * fontHeight   使用的字体的高度
     * fontWidth    使用的字体的宽度
     * indexList    下标数组
     * indexListOffset  下标数组的偏移
     */
    int _AddStr(unsigned int width, unsigned int height, unsigned int x, unsigned int y, const char *str,
                unsigned int fontHeight, unsigned int fontWidth, unsigned int indexListOffset);

    /******************************************************************************
     * 获取需要如果要点，需要改变颜色的像素点 (只认开始的点)
     *******************************************************************************/
    int yuv_drawPoint(int startX, int startY, int endX, int endY, int poinoffset);

    /******************************************************************************
     * 获取需要如果要画线，需要改变颜色的像素点的index
     *******************************************************************************/
    int yuv_drawline(int width, int height, YUVDataOpt_DrawLineInfo *pDrawLineInfo, int poinoffset);

    /******************************************************************************
     * 获取需要长方形，需要改变颜色的像素点的
     *******************************************************************************/
    int draw_rect(int startX, int startY, int endX, int endY, int width, int height, int porintOffset, int lineWidth);

    /******************************************************************************
     * 获取需要画不规则四边形，需要改变颜色的像素点的
     *******************************************************************************/
    int
    draw_quadrilateral(int p1x, int p1y, int p2x, int p2y, int p3x, int p3y, int p4x, int p4y, int width, int height,
                       int porintOffset, int lineWidth);

    /******************************************************************************
     * 获取需要长方形，需要改变颜色的像素点的index
     *******************************************************************************/
    int
    displayRectangle(int resultLen, int startX, int startY, int endX, int endY, int width, int height, int *indexList);

    /******************************************************************************
     * 在NV21上改变对应的像素点的颜色
     *******************************************************************************/
    void yuv_setdata(uint8_t *YBuff, uint8_t *UVBuff, uint16_t width, uint16_t height, YUVDataOpt_Point &draw_point,
                     int colorid);

    /******************************************************************************
    * 画多边形(实线)
    *******************************************************************************/
    int draw_polygon_solidline(const YUVDataOpt_Point *pointList, int pointListSize, int width, int height, int porintOffset, int lineWidth);
    /******************************************************************************
    * 画多边形(虚线)
    *******************************************************************************/
    int draw_polygon_dashedline(const YUVDataOpt_Point *pointList, int pointListSize, int width, int height, int porintOffset, int lineWidth);

    /**
     * 算出一条直线（点A跟点B的连线）的斜率
     * @param pointA ： 直线上的一个点
     * @param pointB ： 直线上的另一个点
     * @return 斜率  0：该之前平行于X轴  99999：该直线垂直于X轴  其他：直线的斜率
     */
    double getSlope(YUVDataOpt_Point pointA, YUVDataOpt_Point pointB);

    YUVDataOpt_Point getPointOfIntersectingLine_Vertical(YUVDataOpt_Point pointA1, YUVDataOpt_Point pointA2, YUVDataOpt_Point pointB1, int lineLength,int direction);


};


#endif //VIS_G3_SOFTWARE_XUYUVDATAOPT_H
