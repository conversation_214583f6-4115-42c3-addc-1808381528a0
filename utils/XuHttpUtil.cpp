//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/25.
//


#include <fstream>
#include <unistd.h>
#include "XuHttpUtil.h"


XuHttpUtil::XuHttpUtil() {
    curl_global_init(CURL_GLOBAL_DEFAULT);
}

XuHttpUtil::~XuHttpUtil() {
    // 清理全局curl资源
    curl_global_cleanup();
}

XuHttpUtil &XuHttpUtil::getInstance() {
    static XuHttpUtil _instance;
    return _instance;
}


int XuHttpUtil::loginToNDS(const std::string &userName, const std::string &password, std::string &strResponse) {
    /* action */
    std::string actionPath = "/api/v1/device/Login";
    /* 设置参数列表 */
    std::vector<XuHttpParam> paramList(2);
    paramList[0].keyName = "password";
    paramList[0].value = password;
    paramList[1].keyName = "username";
    paramList[1].value = userName;
    /* 发起post请求 */
    return post_https(ndsRootRul + actionPath, paramList, strResponse);
}

int XuHttpUtil::registerToNDS(const std::string &uuid, const std::string &tag, const std::string &key,
                              std::string &strResponse) {
    /* action */
    std::string actionPath = "/api/v1/device/Register";
    /* 设置参数列表 */
    std::vector<XuHttpParam> paramList(3);
    paramList[1].keyName = "tag";
    paramList[1].value = tag;
    paramList[0].keyName = "uuid";
    paramList[0].value = uuid;
    paramList[2].keyName = "key";
    paramList[2].value = key;

    /* 发起post请求 */
    return post_https(ndsRootRul + actionPath, paramList, strResponse);
}

int XuHttpUtil::getNDSMqttCertificate(const std::string &uuid, const std::string &deviceType,
                                      const std::string &mqttCertificate, std::string &strResponse) {
    /* action */
    std::string actionPath = "/api/v1/device/getMqttCertificate";
    /* 设置参数列表 */
    std::vector<XuHttpParam> paramList(3);
    paramList[1].keyName = "deviceUuid";
    paramList[1].value = uuid;
    paramList[0].keyName = "deviceType";
    paramList[0].value = deviceType;
    paramList[2].keyName = "mqttCertificate";
    paramList[2].value = mqttCertificate;

    /* 发起post请求 */
    return post_https(ndsRootRul + actionPath, paramList, strResponse);
}


/**
 * HTTP写数据时的回掉
 *
 * @param ptr ： 需要新增的文字内容指针
 * @param size ： 需要新增的文字内容指针的长度
 * @param nmemb ： 现有的文字内容的长度页数
 * @param data ： 存放请求的内容的指针
 *
 * @return 新的长度
 */
std::size_t XuHttpUtilWriteCallback(char *ptr, std::size_t size, std::size_t nmemb, std::string *data) {
    data->append(ptr, size * nmemb);
    return size * nmemb;
}

/**
 * HTTP写数据时的回掉
 *
 * @param ptr ： 需要新增的文字内容指针
 * @param size ： 需要新增的文字内容指针的长度
 * @param nmemb ： 现有的文字内容的长度页数
 * @param data ： 存放请求的内容的指针
 *
 * @return 新的长度
 */
std::size_t XuHttpUtilWriteCallback_FileUpload(char *ptr, std::size_t size, std::size_t nmemb, std::string *data) {
    data->append(ptr, size * nmemb);
    printf("************write=%d  \n",(size * nmemb));
    return size * nmemb;
}

int XuHttpUtil::post_http(const std::string &url, std::vector<XuHttpParam> paramList, std::string &strResponse) {

    int ret = -1;
    // 初始化curl
    CURL *curl = curl_easy_init();
    if (curl) {
        std::string postData;
        if (!paramList.empty()) {
            for (std::size_t i = 0; i < paramList.size(); i++) {
                postData.append(paramList[i].keyName);
                postData.append("=");
                postData.append(paramList[i].value);
                if (i != (paramList.size() - 1)) {
                    postData.append("&");
                }
            }
        }

        // 设置URL
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());

        // 设置POST数据
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, postData.c_str());

        // 设置写入回调函数和缓冲区
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, XuHttpUtilWriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &strResponse);

        // 设置SSL选项，允许使用HTTPS
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        // 执行请求
        CURLcode res = curl_easy_perform(curl);

        printf("Post request url:%s  params:%s \n", url.c_str(), postData.c_str());


        // 检查执行结果
        if (res == CURLE_OK) {
//            std::cout << "Request succeeded. Response:\n" << strResponse << std::endl;
            ret = 0;
        } else {
            std::cerr << "Request failed. Error code: " << res << std::endl;
        }
        // 清理curl资源
        curl_easy_cleanup(curl);
    } else {
        printf("curl_easy_init failed!\n");
    }

    return ret;
}

int XuHttpUtil::get_https(const std::string &url, std::string &strResponse) {

    int ret = -1;
    CURL *curl = curl_easy_init();

    if (curl) {
        // 设置URL
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());

        // 设置写入回调函数和缓冲区

        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, XuHttpUtilWriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &strResponse);

        // 设置SSL选项，允许使用HTTPS
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        // 执行请求
        CURLcode res = curl_easy_perform(curl);

        // 检查执行结果
        if (res == CURLE_OK) {
//            std::cout << "Request succeeded. Response:\n" << strResponse << std::endl;
            ret = 0;
        } else {
//            std::cerr << "Request failed. Error code: " << res << std::endl;
        }

        // 清理curl资源
        curl_easy_cleanup(curl);
    }

    return ret;
}

int XuHttpUtil::postAttchmentFileToNDS(const std::string &url, const std::string &path, std::string &strResponse) {
    int ret = -1;
    // 初始化CURL
    CURL *curl = curl_easy_init();
    if (curl) {
        // 设置URL
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());

        // 设置POST请求
        curl_easy_setopt(curl, CURLOPT_POST, 1L);

        // 设置要发送的文件和参数
        curl_httppost* post = nullptr;
        curl_httppost* last = nullptr;
        // 添加文件
        curl_formadd(&post, &last, CURLFORM_COPYNAME, "alarmAttchment", CURLFORM_FILE, path.c_str(), CURLFORM_END);


        // 设置请求的body
        curl_easy_setopt(curl, CURLOPT_HTTPPOST, post);

        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, XuHttpUtilWriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &strResponse);

        // 设置SSL选项，允许使用HTTPS
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        // 执行请求
        int res = curl_easy_perform(curl);

        printf("Post request url:%s  path:%s \n", url.c_str(), path.c_str());

        // 检查执行结果
        if (res == CURLE_OK) {
            std::cout << "**********************Request succeeded. Response:\n" << strResponse << std::endl;
            ret = 0;
        } else {
            std::cerr << "***************************Request failed. Error code: " << res << std::endl;
        }

        // 清理curl资源
        curl_easy_cleanup(curl);


    }

    return ret;
}

int XuHttpUtil::postDVRFileToNDS(const std::string &url, const std::string &path, std::string &strResponse) {
    int ret = -1;
    // 初始化CURL
    CURL *curl = curl_easy_init();
    if (curl) {
        // 设置URL
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());

        // 设置POST请求
        curl_easy_setopt(curl, CURLOPT_POST, 1L);

        // 设置要发送的文件和参数
        curl_httppost* post = nullptr;
        curl_httppost* last = nullptr;

        // 添加文件
        curl_formadd(&post, &last, CURLFORM_COPYNAME, "cameraDvrFile", CURLFORM_FILE, path.c_str(), CURLFORM_END);


        // 设置请求的body
        curl_easy_setopt(curl, CURLOPT_HTTPPOST, post);

        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, XuHttpUtilWriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &strResponse);

        // 设置SSL选项，允许使用HTTPS
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        // 执行请求
        int res = curl_easy_perform(curl);

        printf("Post request url:%s  path:%s \n", url.c_str(), path.c_str());

        // 检查执行结果
        if (res == CURLE_OK) {
            std::cout << "**********************Request succeeded. Response:\n" << strResponse << std::endl;
            ret = 0;
        } else {
            std::cerr << "***************************Request failed. Error code: " << res << std::endl;
        }

        // 清理curl资源
        curl_easy_cleanup(curl);


    }

    return ret;
}

int XuHttpUtil::post_https(const std::string &url, std::vector<XuHttpParam> paramList, std::string &strResponse) {
    int ret = -1;
    // 初始化curl
    CURL *curl = curl_easy_init();
    if (curl) {
        std::string postData;
        if (!paramList.empty()) {
            for (std::size_t i = 0; i < paramList.size(); i++) {
                postData.append(paramList[i].keyName);
                postData.append("=");
                postData.append(paramList[i].value);
                if (i != (paramList.size() - 1)) {
                    postData.append("&");
                }
            }
        }

        // 设置URL
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());

        // 设置POST数据
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, postData.c_str());

        // 设置写入回调函数和缓冲区
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, XuHttpUtilWriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &strResponse);

        //NOTE 由于HTTPS的证书的不定，所以这里就不校验服务器的证书了
        //设置不校验服务器证书
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, false);


        // 执行请求
        CURLcode res = curl_easy_perform(curl);

        printf("Post(https) request url:%s  params:%s \n", url.c_str(), postData.c_str());


        // 检查执行结果
        if (res == CURLE_OK) {
//            std::cout << "Request succeeded. Response:\n" << strResponse << std::endl;
            ret = 0;
        } else {
            std::cerr << "Request failed. Error code: " << res << std::endl;
        }
        // 清理curl资源
        curl_easy_cleanup(curl);
    } else {
        printf("curl_easy_init failed!\n");
    }

    return ret;
}

void XuHttpUtil::setNDSHttpUrlBase(const std::string host, const int port) {
    ndsRootRul.clear();
    ndsRootRul.append("https://");
    ndsRootRul.append(host);
    ndsRootRul.append(":");
    ndsRootRul.append(std::to_string(port));
}
/* 下载文件的长度 */
int downloadLen = 0;
// 数据写入文件的回调函数
static size_t write_data(void *buffer, size_t size, size_t nmemb, void *stream) {
    std::ofstream* out = static_cast<std::ofstream*>(stream);
    if (!out->is_open()) return 0;
    out->write(static_cast<char*>(buffer), size * nmemb);
//    out->flush();
    downloadLen += (size * nmemb);
    return size * nmemb;  // 返回实际处理的数据长度‌:ml-citation{ref="2,4" data="citationList"}
}

int XuHttpUtil::downlaodFile(const std::string &url, const std::string &path) {
    int ret = -1;
    downloadLock.lock();
    CURL* curl = curl_easy_init();
    if (curl) {
        // 打开本地文件
        std::ofstream file(path.c_str(), std::ios::binary);
        if (file) {
            // 配置CURL参数
            curl_easy_setopt(curl, CURLOPT_URL, url.c_str());  // 设置下载URL‌:ml-citation{ref="2,7" data="citationList"}
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_data);  // 绑定回调函数‌:ml-citation{ref="4" data="citationList"}
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &file);  // 传递文件流指针‌:ml-citation{ref="2" data="citationList"}
            curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);  // 跟随重定向‌:ml-citation{ref="7,8" data="citationList"}
            curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);  // 超时30秒‌:ml-citation{ref="7" data="citationList"}
            //NOTE 由于HTTPS的证书的不定，所以这里就不校验服务器的证书了
            //设置不校验服务器证书
            curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, false);

            downloadLen = 0;
            // 执行下载
            CURLcode res = curl_easy_perform(curl);
            if (res == CURLE_OK) {
                ret = downloadLen;
                std::cerr << "download success!" << "  \nurl="<< url.c_str() << "\n filePath=" << path.c_str() << std::endl;
            }else{
                std::cerr << "download failed! error=" << res << "  \nurl="<< url.c_str() << "\n filePath=" << path.c_str() << std::endl;
            }
            file.close();
        }else{
            std::cerr << "can not creat file " << path.c_str() << std::endl;
        }
        // 清理资源
        curl_easy_cleanup(curl);
    }else{
        std::cerr << "curl_easy_init failed!" << std::endl;
    }
    downloadLock.unlock();

    return ret;
}












