//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/10.
//




#include "XuCalculationTool.h"

XuCalculationTool::XuCalculationTool() {

}

XuCalculationTool::~XuCalculationTool() {

}

XuCalculationTool &XuCalculationTool::getInstance() {
    static XuCalculationTool instance;
    return instance;
}

bool XuCalculationTool::isPointInPolygon(VISPoint point, VISPoint *polygonPonits, int polygonPonitCount) {
    int nCross = 0;
    for (int i = 0; i < polygonPonitCount; i++) {
        VISPoint p1 = polygonPonits[i];
        VISPoint p2 = polygonPonits[(i + 1) % polygonPonitCount];
        // p1.y = p2.y  平行于X轴  我们的射线也是平行于X轴  所以不计算
        if (p1.y == p2.y) {
//            printf("p1(%d,%d) 跟 p2(%d,%d) 的Y相同  是一条平行于X轴的线  不计算\n",p1.x,p1.y,p2.x,p2.y);
            continue;
        }
        // 交点在p1p2延长线上
        if (point.y < std::min(p1.y, p2.y)) {
//            printf("p1(%d,%d) 跟 p2(%d,%d) 交点在p1p2延长线上  不计算\n",p1.x,p1.y,p2.x,p2.y);
            continue;
        }
        // 交点在p1p2延长线上
        if (point.y >= std::max(p1.y, p2.y)) {
//            printf("p1(%d,%d) 跟 p2(%d,%d) 交点在p1p2延长线上  不计算\n",p1.x,p1.y,p2.x,p2.y);
            continue;
        }
        // 求交点的 X 坐标 --------------------------------------------------------------
        float x = (float) (point.y - p1.y) * (float) ((float) (p2.x - p1.x) / (float) (p2.y - p1.y)) + p1.x;
        if (x > point.x) {
//            printf("存在交点 pj(%f,%d) \n",x,p.y);
            nCross++; // 只统计单边交点
        }
    }
    // 单边交点为偶数，点在多边形之外 ---
    return (nCross % 2 == 1);
}


bool XuCalculationTool::isPolygon1AndPolygon2HaveOverlappingArea(VISPoint *polygon1Ponits, int polygon1PonitCount,
                                                                 VISPoint *polygon2Ponits, int polygon2PonitCount) {

    /* 判断多边形1是否有顶点在多边形2内  */
    bool polygon1HavePointInPolygon = false;
    for (int i = 0; i < polygon1PonitCount; i++) {
        polygon1HavePointInPolygon |= isPointInPolygon(polygon1Ponits[i], polygon2Ponits, polygon2PonitCount);
    }
    /* 判断多边形2是否有顶点在多边形1内 */
    bool polygon2HavePointInPolygon = false;
    for (int i = 0; i < polygon2PonitCount; i++) {
        polygon2HavePointInPolygon |= isPointInPolygon(polygon2Ponits[i], polygon1Ponits, polygon1PonitCount);
    }

    /* 如果通过点没判断出来  那么再判断两个多边形的边是否有相交的  有一条相交就算 */
    bool hasLineIntersected = false;
    if (!polygon1HavePointInPolygon && !polygon2HavePointInPolygon) {
        VISLine line1;
        VISLine line2;
        for (int i = 0; i < polygon1PonitCount; i++) {
            line1.startPoint = polygon1Ponits[i];
            if (i == polygon1PonitCount - 1) {
                line1.endPoint = polygon1Ponits[0];
            } else {
                line1.endPoint = polygon1Ponits[i + 1];
            }
            for (int j = 0; j < polygon2PonitCount; j++) {
                line2.startPoint = polygon2Ponits[j];
                if (j == polygon2PonitCount - 1) {
                    line2.endPoint = polygon2Ponits[0];
                } else {
                    line2.endPoint = polygon2Ponits[j + 1];
                }
                hasLineIntersected |= isLineIntersecting(line1, line2);
                /* 找到一条就够用了 */
                if (hasLineIntersected) {
                    break;
                }
            }

        }
    }


    return polygon1HavePointInPolygon | polygon2HavePointInPolygon | hasLineIntersected;
}

bool XuCalculationTool::isLineIntersecting(VISLine line1, VISLine line2) {
    /* 先进行快速排斥 判断两线段在x轴和y轴的投影是否有交，有任何一条轴没有交点就不可能相交  */
    if ((std::max(line1.startPoint.x, line1.endPoint.x) >= std::min(line2.startPoint.x, line2.endPoint.x) &&
         std::min(line1.startPoint.x, line1.endPoint.x) <= std::max(line2.startPoint.x, line2.endPoint.x))  //判断x轴投影
        && (std::max(line1.startPoint.y, line1.endPoint.y) >= std::min(line2.startPoint.y, line2.endPoint.y) &&
            std::min(line1.startPoint.y, line1.endPoint.y) <=
            std::max(line2.startPoint.y, line2.endPoint.y)))    //判断y轴投影
    {
        /* 判断是否跨立 */
        if (
                (((double) line2.startPoint.x - (double) line1.startPoint.x) *
                 ((double) line1.endPoint.y - (double) line1.startPoint.y) -
                 ((double) line2.startPoint.y - (double) line1.startPoint.y) *
                 ((double) line1.endPoint.x - (double) line1.startPoint.x)) *          //判断B是否跨过A
                (((double) line2.endPoint.x - (double) line1.startPoint.x) *
                 ((double) line1.endPoint.y - (double) line1.startPoint.y) -
                 ((double) line2.endPoint.y - (double) line1.startPoint.y) *
                 ((double) line1.endPoint.x - (double) line1.startPoint.x)) <= 0 &&
                (((double) line1.startPoint.x - (double) line2.startPoint.x) *
                 ((double) line2.endPoint.y - (double) line2.startPoint.y) -
                 ((double) line1.startPoint.y - (double) line2.startPoint.y) *
                 ((double) line2.endPoint.x - (double) line2.startPoint.x)) *          //判断A是否跨过B
                (((double) line1.endPoint.x - (double) line2.startPoint.x) *
                 ((double) line2.endPoint.y - (double) line2.startPoint.y) -
                 ((double) line1.endPoint.y - (double) line2.startPoint.y) *
                 ((double) line2.endPoint.x - (double) line2.startPoint.x)) <= 0
                ) {
            return 1;
        } else {
            return 0;
        }

    } else {
//        printf("快速排斥 失败！\n");
        return 0;
    }
}


int XuCalculationTool::mult(VISPoint point1, VISPoint point2, VISPoint point3) {
    return (point1.x - point3.x) * (point2.y - point3.y) - (point2.x - point3.x) * (point1.y - point3.y);
}

uint8_t XuCalculationTool::getPeripheralUpgradeFileSumCheck(const char *filePath) {
    uint8_t ret = -1;
    FILE *fileFd = fopen(filePath, "rb");
    if (fileFd) {
        uint8_t readCache[30];
        int sumCheck = 0;
        /* 先读掉前面三十个字节 */
        int readLen = fread(readCache, 1, 30, fileFd);
        /* 读完30个字节之后  把后面的数据一个个进行相加 */
        if (readLen == 30) {
            while (readLen > 0) {
                readLen = fread(readCache, 1, 1, fileFd);
                sumCheck += readCache[0];
            }
            /* 得到的和只取低8位 */
            ret = sumCheck & 0xFF;
        }
        fclose(fileFd);
    }
    return ret;
}

uint8_t
XuCalculationTool::polygonIsometricScaling(YUVDataOpt_Point *srcPoints, YUVDataOpt_Point *decPoints, float dist) {

    /* 先求出质心C */
    vis::Point2D point_centor;
    point_centor.x = (srcPoints[0].x + srcPoints[1].x + srcPoints[2].x + srcPoints[3].x + srcPoints[4].x + srcPoints[5].x) / 6.0f;
    point_centor.y = (srcPoints[0].y + srcPoints[1].y + srcPoints[2].y + srcPoints[3].y + srcPoints[4].y + srcPoints[5].y) / 6.0f;

    /* 通过计算新的多边形的点 */
    for(int i = 0; i < 6; i ++){
        /* 先算出原来的点距离质心的距离（x跟y的距离单独算） */
        float dist_x = srcPoints[i].x - point_centor.x;
        float dist_y = srcPoints[i].y - point_centor.y;
        /* 把这个距离加上需要缩放的值形成新的距离 */
        float newDist_x = dist_x + dist;
        float newDist_y = dist_y + dist;
        /* 把新的距离转成新的点坐标(这里有精度损失，但是可以忽略不计) */
        decPoints[i].x = newDist_x + point_centor.x;
        decPoints[i].y = newDist_y + point_centor.y;
    }


    /* 检查一下结果 */
    for (int i = 0; i < 6; i ++) {
        /* 先判断以下算出来的值对不对，不合法的直接不使用退出区域 */
        if(decPoints[i].x < 0 || decPoints[i].x > 1280 || decPoints[i].y < 0 || decPoints[i].y > 720){
            for(int j = 0; j < 6; j++){
                decPoints[j].x = srcPoints[j].x;
                decPoints[j].y = srcPoints[j].y;
            }
            break;
        }
    }


    return 0;
}

uint8_t XuCalculationTool::polygonIsometricScaling2(VISPoint *srcPoints, VISPoint *decPoints, float dist) {
    int ret = 0;
    /* 先求出质心C */
    vis::Point2D point_centor;
    point_centor.x = (srcPoints[0].x + srcPoints[1].x + srcPoints[2].x + srcPoints[3].x + srcPoints[4].x + srcPoints[5].x) / 6.0f;
    point_centor.y = (srcPoints[0].y + srcPoints[1].y + srcPoints[2].y + srcPoints[3].y + srcPoints[4].y + srcPoints[5].y) / 6.0f;

    /* 通过计算新的多边形的点 */
    for(int i = 0; i < 6; i ++){
        /* 先算出原来的点距离质心的距离（x跟y的距离单独算） */
        float dist_x = srcPoints[i].x - point_centor.x;
        float dist_y = srcPoints[i].y - point_centor.y;
        /* 把这个距离加上需要缩放的值形成新的距离 */
        float newDist_x = dist_x * dist;
        float newDist_y = dist_y * dist;
        /* 把新的距离转成新的点坐标(这里有精度损失，但是可以忽略不计) */
        decPoints[i].x = newDist_x + point_centor.x;
        decPoints[i].y = newDist_y + point_centor.y;
    }
    /* 检查一下结果 */
    for (int i = 0; i < 6; i ++) {
        /* 先判断以下算出来的值对不对，不合法的直接不使用退出区域 */
        if(decPoints[i].x < 0 || decPoints[i].x > 1280 || decPoints[i].y < 0 || decPoints[i].y > 720){
            ret = -1;
            for(int j = 0; j < 6; j++){
                decPoints[j].x = srcPoints[j].x;
                decPoints[j].y = srcPoints[j].y;
            }
            break;
        }
    }

    return ret;
}

bool
XuCalculationTool::isLineAndPolygonHaveOverlappingArea(VISLine line, VISPoint *polygonPonits, int polygonPonitCount) {
    /* 判断多边形1是否有顶点在多边形2内  */
    bool polygon1HavePointInPolygon = false;
    polygon1HavePointInPolygon |= isPointInPolygon(line.startPoint, polygonPonits, polygonPonitCount);
    polygon1HavePointInPolygon |= isPointInPolygon(line.endPoint, polygonPonits, polygonPonitCount);


    /* 如果通过点没判断出来  那么再判断两个多边形的边是否有相交的  有一条相交就算 */
    bool hasLineIntersected = false;
    if (!polygon1HavePointInPolygon) {
        VISLine line1 = line;
        VISLine line2;
        for (int j = 0; j < polygonPonitCount; j++) {
            line2.startPoint = polygonPonits[j];
            if (j == polygonPonitCount - 1) {
                line2.endPoint = polygonPonits[0];
            } else {
                line2.endPoint = polygonPonits[j + 1];
            }
            hasLineIntersected |= isLineIntersecting(line1, line2);
            /* 找到一条就够用了 */
            if (hasLineIntersected) {
                break;
            }
        }
    }

    return (polygon1HavePointInPolygon | hasLineIntersected);
}

