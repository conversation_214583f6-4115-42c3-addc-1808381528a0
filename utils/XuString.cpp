//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/8.
//


#include "XuString.h"
#include "XuFile.h"

#include <Poco/Crypto/Cipher.h>
#include <Poco/Crypto/CipherKey.h>
#include <Poco/Crypto/CipherFactory.h>
#include <memory>
#include <sstream>
#include <openssl/evp.h>
#include <openssl/err.h>

/**
 * 将str首尾的特殊字符去掉
 * @param str:需要进行处理的字符串
 *
 **/
void XuString::trim(std::string &str) {
    std::string blanks("\f\v\r\t\n ");
    str.erase(0, str.find_first_not_of(blanks));
    str.erase(str.find_last_not_of(blanks) + 1);
}

/**
 * 将str以pattern为分割符分割出来
 * @param str:需要进行分割的字符串
 * @param pattern:分割符
 *
 * @return 分割好的字符串数组
 **/
std::vector<std::string> XuString::split(std::string str, std::string pattern) {
    std::string::size_type pos;
    std::vector<std::string> result;
    str += pattern;//扩展字符串以方便操作
    std::size_t size = str.size();
    for (std::size_t i = 0; i < size; i++) {
        pos = str.find(pattern, i);
        if (pos < size) {
            std::string s = str.substr(i, pos - i);
            result.push_back(s);
            i = pos + pattern.size() - 1;
        }
    }
    return result;
}

/**
 * 将str转成longlong
 * @param digit:longlong的str形式
 * @param minus:是正或者负
 * @param len：需要转换的str的长度
 *
 * @return 转换好的结果
 **/
long long XuString::Strtointcode(const char *digit, bool minus, int len) {
    long long num = 0;

    /* 逐个字符转换然后再拼在一起 */
    for (int i = 0; i < len; i++) {
        /* 限制一下str的内容必须是个数字 */
        if (*digit >= '0' && *digit <= '9') {
            int flag = minus ? -1 : 1;
            num = num * 10 + flag * (*digit - '0');
            if ((!minus && num > 0x7FFFFFFF) || (minus && num < (signed int) 0x80000000)) {
                num = 0;
                break;
            } else { ;
            }
            digit++;
        } else {
            num = 0;
            break;
        }
    }
    return num;
}

/**
 * 将str转成int
 * @param str:int的str形式
 * @param len：需要转换的str的长度
 *
 * @return 转换好的结果
 **/
int XuString::Strtoint(const char *str, int len) {
    long long num = 0;
    /* 判断下不能为空 */
    if (str != nullptr && *str != '\0') {
        /* 判断下正负 */
        bool minus = false;
        if (*str == '+') {
            str++;
        } else if (*str == '-') {
            str++;
            minus = true;
        } else { ;
        }

        /* 判断下如果不是只有一个正负号就直接进去转了 */
        if (*str != '\0') {
            num = Strtointcode(str, minus, len);
        }
    } else { ;
    }
    /* 把结果返回出去 */
    return (int) num;
}

XuString &XuString::getInstance() {
    static XuString _instance;
    return _instance;
}


int XuString::getStrFormTime(char *sz, std::size_t nsize, const char *format, bool bgmt) {
    std::size_t strLen = 0;
    time_t t = time(0);
    if (bgmt) { // 标准时间
        strLen = strftime(sz, nsize, format, gmtime(&t));
    } else { // 北京时间
        strLen = strftime(sz, nsize, format, localtime(&t));
    }
    /* str的长度不会太长，所以直接从std::size_t转成int也没问题 */
    return static_cast<int>(strLen);

    //	%a 星期几的缩写。Eg:Tue
    // 	%A 星期几的全名。 Eg: Tuesday
    // 	%b 月份名称的缩写。
    // 	%B 月份名称的全名。
    // 	%c 本地端日期时间较佳表示字符串。
    // 	%d 用数字表示本月的第几天 (范围为 00 至 31)。日期
    // 	%H 用 24 小时制数字表示小时数 (范围为 00 至 23)。
    // 	%I 用 12 小时制数字表示小时数 (范围为 01 至 12)。
    // 	%j 以数字表示当年度的第几天 (范围为 001 至 366)。
    // 	%m 月份的数字 (范围由 1 至 12)。
    // 	%M 分钟。
    // 	%p 以 ''AM'' 或 ''PM'' 表示本地端时间。
    // 	%S 秒数。
    // 	%U 数字表示为本年度的第几周，第一个星期由第一个周日开始。
    // 	%W 数字表示为本年度的第几周，第一个星期由第一个周一开始。
    // 	%w 用数字表示本周的第几天 ( 0 为周日)。
    // 	%x 不含时间的日期表示法。
    // 	%X 不含日期的时间表示法。 Eg: 15:26:30
    // 	%y 二位数字表示年份 (范围由 00 至 99)。
    // 	%Y 完整的年份数字表示，即四位数。 Eg:2008
    // 	%Z(%z) 时区或名称缩写。Eg:中国标准时间
    // 	%% % 字符。
}

std::string XuString::bcd2String(const uint8_t *buf, const int len) {
    std::string temp;
    /* 先把所有的转成string */
    for (int i = 0; i < len; i++) {
        // 高四位
        temp.append(std::to_string((buf[i] & 0xF0) >> 4));
        // 低四位
        temp.append(std::to_string(buf[i] & 0x0F));
    }
    return temp;
}

int XuString::string2Bcd(std::string str, uint8_t *buf) {
    // 奇数,前补零
    if ((str.length() % 2) != 0) {
        str.insert(0, "0");
    }
    int ret = 0;

    for (std::size_t i = 0; i < str.size() / static_cast<std::size_t>(2); i++) {
        uint8_t high = ascII2Bcd(str.c_str()[2 * i]);
        uint8_t low = ascII2Bcd(str.c_str()[2 * i + 1]);
        /* 只遮罩BCD低四位是否会有问题这点存疑 */
        buf[i] = ((high << 4) | low);
        ret++;
    }
    return ret;
}

uint8_t XuString::ascII2Bcd(uint8_t asc) {
    if ((asc >= '0') && (asc <= '9'))
        return (asc - '0');
    else if ((asc >= 'A') && (asc <= 'F'))
        return (asc - 'A' + 10);
    else if ((asc >= 'a') && (asc <= 'f'))
        return (asc - 'a' + 10);
    else
        return (asc - 48);
}

int XuString::stringToByteArray(std::string str, uint8_t *buf, const int bufLen) {
    /* 先判断下str是否有内容 */
    if (str.empty()) {
        return -1;
    }
    /* 先判断下str的长度是否是能被2整除，因为byte是%02x格式的 */
    if ((str.size() % 2) != 0) {
        return -1;
    }
    /* 先检查下转换出来后的byte数组长度四多少 */
    int resultByteArrayLen = str.size() / 2;
    /* 判断下内存的大小是否满足数组长度 */
    if (bufLen < resultByteArrayLen) {
        return -1;
    }
    /* 每两个字符转成一个int */
    for (int i = 0; i < resultByteArrayLen; i++) {
        buf[i] = std::stoi(str.substr(i * 2, 2).c_str(), nullptr, 16) & 0xFF;
    }
    return resultByteArrayLen;
}

std::string XuString::byteArrayToString(const uint8_t * const buf, const int bufLen) {
    /* 先检查下需要转换的数组的长度 */
    if (bufLen <= 0) {
        return nullptr;
    }
    /* 定义一个string，把str添加进去 */
    std::string resultStr;
    /* 先开辟一个内容用来存16进制的数组内容用%02x的格式 */
    char str[3] = {0x00};
    /* 通过sprintf把一个个byte存进去str */
    for (int i = 0; i < bufLen; i++) {
        sprintf(str, "%02x", buf[i]);
        resultStr.append(str);
    }

    return resultStr;
}

bool XuString::cmpTwoByteArray(const uint8_t *array1, const int arrayLen1, const uint8_t *array2, const int arrayLen2) {
    bool ret = false;
    if (arrayLen1 == arrayLen2) {
        bool cmpResult = true;
        for (int i = 0; i < arrayLen1; i++) {
            cmpResult &= (array1[i] == array2[i]);
        }
        ret = cmpResult;
    }
    return ret;
}

std::string XuString::AES_128_ECB_AND_BASE64_Decrypt(uint8_t *myKey, std::string src) {
    std::string result;
    try {
        Poco::Crypto::Cipher::ByteVec b(16);
        for (int i = 0; i < 16; ++i) {
            b[i] = myKey[i];
        }
        Poco::Crypto::CipherKey key("aes-128-ecb");
        key.setKey(b);

        Poco::Crypto::Cipher::Ptr pCipher = Poco::Crypto::CipherFactory::defaultFactory().createCipher(key);
        result = pCipher->decryptString(src, Poco::Crypto::Cipher::ENC_BASE64);

    } catch (...) {
        printf("XuString::AES_ECB_Decrypt has catch!\n");
    }
    return result;

}

std::string XuString::AES_ECB_128_AND_BASE64_Encrypt(uint8_t *myKey, std::string src) {
    std::string result;
    try {
        Poco::Crypto::Cipher::ByteVec b(16);
        for (int i = 0; i < 16; ++i) {
            b[i] = myKey[i];
        }
        Poco::Crypto::CipherKey key("aes-128-ecb");
        key.setKey(b);

        Poco::Crypto::Cipher::Ptr pCipher = Poco::Crypto::CipherFactory::defaultFactory().createCipher(key);
        result = pCipher->encryptString(src, Poco::Crypto::Cipher::ENC_BASE64);
    } catch (...) {
        printf("XuString::AES_ECB_128_AND_BASE64_Encrypt has catch!\n");
    }
    return result;
}

std::string XuString::formatFloatValue(float val, int fixed) {
    std::string str = std::to_string(val);
    return str.substr(0, str.find(".") + fixed + 1);
}

int XuString::getStrFormTimestamp(uint64_t timestamp, char *sz, std::size_t nsize, const char *format, bool bgmt) {
    std::size_t strLen = 0;
    time_t t = timestamp;
    if (bgmt) { // 标准时间
        strLen = strftime(sz, nsize, format, gmtime(&t));
    } else { // 北京时间
        strLen = strftime(sz, nsize, format, localtime(&t));
    }
    /* str的长度不会太长，所以直接从std::size_t转成int也没问题 */
    return static_cast<int>(strLen);

    //	%a 星期几的缩写。Eg:Tue
    // 	%A 星期几的全名。 Eg: Tuesday
    // 	%b 月份名称的缩写。
    // 	%B 月份名称的全名。
    // 	%c 本地端日期时间较佳表示字符串。
    // 	%d 用数字表示本月的第几天 (范围为 00 至 31)。日期
    // 	%H 用 24 小时制数字表示小时数 (范围为 00 至 23)。
    // 	%I 用 12 小时制数字表示小时数 (范围为 01 至 12)。
    // 	%j 以数字表示当年度的第几天 (范围为 001 至 366)。
    // 	%m 月份的数字 (范围由 1 至 12)。
    // 	%M 分钟。
    // 	%p 以 ''AM'' 或 ''PM'' 表示本地端时间。
    // 	%S 秒数。
    // 	%U 数字表示为本年度的第几周，第一个星期由第一个周日开始。
    // 	%W 数字表示为本年度的第几周，第一个星期由第一个周一开始。
    // 	%w 用数字表示本周的第几天 ( 0 为周日)。
    // 	%x 不含时间的日期表示法。
    // 	%X 不含日期的时间表示法。 Eg: 15:26:30
    // 	%y 二位数字表示年份 (范围由 00 至 99)。
    // 	%Y 完整的年份数字表示，即四位数。 Eg:2008
    // 	%Z(%z) 时区或名称缩写。Eg:中国标准时间
    // 	%% % 字符。
}

std::string XuString::replaceString(std::string resource_str, std::string sub_str, std::string new_str) {
    std::string dst_str = resource_str;
    std::string::size_type pos = 0;
    while((pos = dst_str.find(sub_str)) != std::string::npos)   //替换所有指定子串
    {
        dst_str.replace(pos, sub_str.length(), new_str);
    }
    return dst_str;
}

std::string XuString::getCPUSerialNumber() {
    std::string ret;
    /* 读一下CPU的序列号 */
    uint8_t *buf = new uint8_t[1024 * 10];
    std::shared_ptr<uint8_t> managedData(buf, [](uint8_t* d) { delete[] d; });

    memset(managedData.get(), 0x00, 1024 * 10);
    int strLen = XuFile::getInstance().readFile("/proc/cpuinfo", managedData.get(), 1024 * 10);
    if (strLen > 0) {
        std::string cpuinfoStr;
        cpuinfoStr.append(reinterpret_cast<const char *>(managedData.get()));
        if (XuString::getInstance().split(cpuinfoStr, "Serial").size() == 2) {
            if (XuString::getInstance().split(XuString::getInstance().split(cpuinfoStr, "Serial")[1],":").size() == 2) {
                ret.append(XuString::getInstance().split(cpuinfoStr, "Serial")[1].c_str()+1);
            }
        }
    }
    return ret;
}

std::string XuString::AES_ECB_128_Encrypt(uint8_t *myKey, std::string src) {
    std::string result;
    try {
        Poco::Crypto::Cipher::ByteVec b(16);
        for (int i = 0; i < 16; ++i) {
            b[i] = myKey[i];
        }
        Poco::Crypto::CipherKey key("aes-128-ecb");
        key.setKey(b);
        Poco::Crypto::Cipher::Ptr pCipher = Poco::Crypto::CipherFactory::defaultFactory().createCipher(key);


        result = pCipher->encryptString(src, Poco::Crypto::Cipher::ENC_NONE);
        printf("result=%s  \n",XuString::getInstance().byteArrayToString(
                reinterpret_cast<const uint8_t *>(result.c_str()), result.size()).c_str());
    }  catch (const Poco::Exception& e) {
        printf("Encrypt failed: %s\n", e.displayText().c_str());
    }   catch (...) {
        printf("XuString::AES_ECB_128_Encrypt has catch!\n");
    }
    return result;

}

std::string XuString::AES_ECB_128_Decrypt(uint8_t *myKey, std::string src) {
    std::string result;
    try {
        Poco::Crypto::Cipher::ByteVec b(16);
        for (int i = 0; i < 16; ++i) {
            b[i] = myKey[i];
        }
        Poco::Crypto::CipherKey key("aes-128-ecb");
        key.setKey(b);
        Poco::Crypto::Cipher::Ptr pCipher = Poco::Crypto::CipherFactory::defaultFactory().createCipher(key);
        result = pCipher->decryptString(src, Poco::Crypto::Cipher::ENC_NONE);

    } catch (...) {
        printf("XuString::AES_ECB_128_Decrypt has catch!\n");
    }
    return result;

}
