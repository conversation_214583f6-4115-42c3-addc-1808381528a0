//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/2/28.
//

#ifndef MRV440SOFTWARE_PING_H
#define MRV440SOFTWARE_PING_H


#pragma once


#include <stdio.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip_icmp.h>
#include <arpa/inet.h>
#include <sys/types.h>
#include <sys/time.h>
#include <unistd.h>
#include <netdb.h>
#include <string.h>

struct IPHeader
{
    uint8_t m_byVerHLen; //4位版本+4位首部长度
    uint8_t m_byTOS; //服务类型
    uint16_t m_usTotalLen; //总长度
    uint16_t m_usID; //标识
    uint16_t m_usFlagFragOffset; //3位标志+13位片偏移
    uint8_t m_byTTL; //TTL
    uint8_t m_byProtocol; //协议
    uint16_t m_usHChecksum; //首部检验和
    uint32_t m_ulSrcIP; //源IP地址
    uint32_t m_ulDestIP; //目的IP地址
};

struct ICMPHeader
{
    uint8_t m_byType; //类型
    uint8_t m_byCode; //代码
    uint16_t m_usChecksum; //检验和
    uint16_t m_usID; //标识符
    uint16_t m_usSeq; //序号
    uint32_t m_ulTimeStamp; //时间戳（非标准ICMP头部）
};

struct PingReply
{
    uint16_t m_usSeq;
    uint32_t m_dwRoundTripTime;
    uint32_t m_dwBytes;
    uint32_t m_dwTTL;
};


#define PACKET_SIZE     4096
#define SUCCESS         true

class Ping
{
public:
    //效验算法
    uint16_t cal_chksum(unsigned short *addr, int len);
    // Ping函数
    bool ping(char *address, int timeout = 3000, bool usedHostname = false);



private:


};


#endif //MRV440SOFTWARE_PING_H
