//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/15.
//

#include <dirent.h>
#include <cstring>
#include <sys/stat.h>
#include <algorithm>
#include <fstream>
#include "XuFile.h"
#include <sys/statfs.h>
#include <Poco/MD5Engine.h>
#include <Poco/DigestStream.h>
#include <Poco/StreamCopier.h>
#include <sstream>
#include "XuString.h"
#include "CodeUtils.h"
#include "XuShell.h"
#include <iomanip>
#include <openssl/evp.h>
#include <openssl/aes.h>
#include <openssl/rand.h>
#include <iostream>
#include <openssl/err.h>
#include <thread>
#include <unistd.h>

#include <fcntl.h>
#include <sys/mman.h>

/**
 * 获取目录下的文件（只限获取表层目录的文件，子目录不算，不是标准文件也算）
 *
 * @param path：目录路径
 * @param filter:文件名需要包含的关键字（传入nullptr时代表所有文件名都行）
 *
 * @return 文件名列表
 * */
std::vector<std::string> XuFile::getDirAllFile(const char *path, const char *filter) {
    DIR *dir;
    std::vector<std::string> result;
    dir = opendir(path);
    if (dir != nullptr) {
        struct dirent *ptr;
        while ((ptr = readdir(dir)) != nullptr) {
            /* . 和 .. 和目录 都不算 */
            if(strcmp(ptr->d_name,".") != 0 && strcmp(ptr->d_name,"..") != 0 && ptr->d_type != DT_DIR){
                if (filter != nullptr) {
                    if (strstr(ptr->d_name, filter) != nullptr) {
                        result.push_back(ptr->d_name);
                    } else { ;
                    }
                } else {
                    result.push_back(ptr->d_name);
                }
            }
        }
    } else {
    }
    closedir(dir);
    return result;
}


/**
 * 获取目录下的文件（只限获取表层目录的文件，子目录不算，而且必须是标准文件才行）
 *
 * @param path：目录路径
 * @param filter:文件名需要包含的关键字（传入nullptr时代表所有文件名都行）
 *
 * @return 文件名列表
 * */
std::vector<std::string> XuFile::getDirAllFile_Harsh(const char *path, const char *filter) {
    DIR *dir;
    std::vector<std::string> result;
    dir = opendir(path);
    if (dir != nullptr) {
        struct dirent *ptr;
        while ((ptr = readdir(dir)) != nullptr) {
            if (ptr->d_type == DT_REG) {
                if (filter != nullptr) {
                    if (strstr(ptr->d_name, filter) != nullptr) {
                        result.push_back(ptr->d_name);
                    } else { ;
                    }
                } else {
                    result.push_back(ptr->d_name);
                }
            } else {
            }
        }
    } else {
    }
    closedir(dir);
    return result;
}

/**
 * 获取目录下所有文件的的总长度(包括子目录的文件)
 *
 * @param path ：目录路径
 *
 * @return 长度
 * */
uint64_t XuFile::getAllFileLength(const char *path) {
    DIR *dir;
    uint64_t allLength = 0;
    dir = opendir(path);
    if (dir != nullptr) {
        struct dirent *ptr;
        while ((ptr = readdir(dir)) != nullptr) {
            if(strcmp(ptr->d_name,".") != 0 && strcmp(ptr->d_name,"..") != 0){
                if(ptr->d_type == DT_DIR){
                    std::string subdirectoryPath = "";
                    subdirectoryPath.append(path);
                    subdirectoryPath.append(ptr->d_name);
                    subdirectoryPath.append("/");
//                    printf("file:%s  \n",subdirectoryPath.c_str());
                    uint64_t subdirectorySize = getAllFileLength(subdirectoryPath.c_str());
                    allLength = allLength + subdirectorySize;
                }else{
                    if (ptr->d_type == DT_REG) {
                        struct stat mystat;
                        std::string filePath = path;
                        filePath.append(ptr->d_name);
                        stat(filePath.c_str(), &mystat);
                        allLength += mystat.st_size;
                    }
                }
            }



        }
    } else {
        allLength = -1;
    }
    closedir(dir);

    return allLength;
}


/* 根据文件名对两个文件进行排序（升序）  使用的是单纯的文件名 */
bool doMP4FileAscending(const std::string fileNmaeA, const std::string fileNmaeB) {
    bool ret = false;
    /* 取出时间这个字段 */
    std::string timeStrA = XuString::getInstance().split(fileNmaeA, "_")[0];
    std::string timeStrB = XuString::getInstance().split(fileNmaeB, "_")[0];
    /* 比较一下时间  旧的在前面 */
    long long timeA = std::atoll(timeStrA.c_str());
    long long timeB = std::atoll(timeStrB.c_str());
    ret =  timeA < timeB;
    return ret;


}

/* 根据文件名对两个文件进行排序（升序）  使用的是文件的完整路径 */
bool doMP4FileAscending_FullPath(const std::string filePathA, const std::string filePathB) {
    bool ret = false;
    try {
        /* 取出文件名 */
        std::vector<std::string> filePathAItems = XuString::getInstance().split(filePathA, "/");
        std::string fileNmaeA = filePathAItems[(filePathAItems.size() - 1)];
        std::vector<std::string> filePathBItems = XuString::getInstance().split(filePathB, "/");
        std::string fileNmaeB = filePathBItems[(filePathBItems.size() - 1)];

        /* 取出时间这个字段 */
        std::string timeStrA = XuString::getInstance().split(fileNmaeA, "_")[0];
        std::string timeStrB = XuString::getInstance().split(fileNmaeB, "_")[0];

        /* 比较一下时间  旧的在前面 */
        long long timeA = std::atoll(timeStrA.c_str());
        long long timeB = std::atoll(timeStrB.c_str());
        ret =  timeA < timeB;

    }catch (...){
        printf("doMP4FileAscending_FullPath has error!  %s \n", strerror(errno));
    }

    return ret;


}

/**
 * 删除指定目录下的文件
 *
 * @param path：目录
 * @param count：删除多少个？
 * */
void XuFile::removeOldMp4File(const char *path, int count, const bool noZero) {
    std::vector<std::string> files = getDirAllFile_Harsh(path, ".mp4");
    std::sort(files.begin(), files.end(), doMP4FileAscending);
    int curDeleteCount = 0;
    int filesSize = static_cast<std::size_t>(files.size());
    int deleteFileCount = filesSize > count ? count : filesSize;
    for(int i = 0; (i < filesSize && curDeleteCount < deleteFileCount); i ++ ){
        std::string filePath = path;
        filePath.append(files[i]);
        /* 看看是否必须是要有长度的才算 */
        if(noZero){
            if(getFileLength(filePath.c_str()) > 0){
                curDeleteCount ++;
            }
        }else{
            curDeleteCount ++;
        }
        /* 在这里实际删除 */
        remove(filePath.c_str());
        printf("to remove file %s \n",filePath.c_str());
    }
}

/**
 * 创建目录（包含子目录）
 *
 * @param path：目录
 * @param mode：权限  默认0755
 * */
int XuFile::mkpath(std::string path, mode_t mode) {
    std::size_t pre = 0, pos;
    std::string dir;
    int mdret;
    if (path[path.size() - 1] != '/') {
        // force trailing / so we can handle everything in loop
        path += '/';
    }

    while ((pos = path.find_first_of('/', pre)) != std::string::npos) {
        dir = path.substr(0, pos++);
        pre = pos;
        if (dir.size() == 0) {
            continue; // if leading / first time is 0 length
        }
        if ((mdret = ::mkdir(dir.c_str(), mode)) && errno != EEXIST) {
            return mdret;
        }
    }
    return mdret;
}

XuFile &XuFile::getInstance() {
    static XuFile _instance;
    return _instance;
}

bool XuFile::fileExists(const char *path) {
//    struct stat buffer;
//    bool ret = (stat(path, &buffer) == 0);
    return (access(path, F_OK) == 0);
}

int XuFile::saveStrToFile(const char *filePath, std::string strContent, bool isReplace) {
    int ret = -1;
    std::ofstream file;
    if (isReplace) {
        file.open(filePath, std::ios::trunc);
    } else {
        file.open(filePath, std::ios::app);
    }

    if (file.is_open())  //判断文件是否存在。
    {
        file << strContent.c_str();
        file.close();
        ret = strContent.size();
    } else {
    }
    return ret;
}

bool XuFile::hasMount(const char *path) {
    bool ret = false;
    /* 最大只读1M的内容 */
    int max_file_content_size = 1024 * 1024;
    char *mountsFileData = static_cast<char *>(malloc(max_file_content_size));
    int readLen = readFile("/proc/mounts", reinterpret_cast<uint8_t *>(mountsFileData), max_file_content_size);
    if (readLen > 0) {
        if (strstr(reinterpret_cast<char *>(mountsFileData), path)) {
            ret =  true;
        }
    }
    free(mountsFileData);
    return ret;
}

int XuFile::readFile(const char *filePath, uint8_t *buf, const int bufLen) {
    int ret = -1;
    /* 先判断下传进的路径内容是否有问题 */
    if (strlen(filePath) > 1) {
        /* 先拼出备份文件的文件名 */
        std::string bakPath = filePath;
        bakPath.append(".bak");
        /* 看看备份文件是否已经存在了 */
        if(fileExists(bakPath.c_str())){
            /* 备份文件已存在，那么就说明上次修改没完全走完，使用备份文件覆盖未完成的文件 */
            rename(bakPath.c_str(),filePath);
        }
        /* 看下文件是否存在 */
        if(fileExists(filePath)){
            /* 文件存在就可以尝试去读了 */
            FILE *fd = fopen(filePath, "rb");
            /* 如果文件打开成功才进行读写 */
            if (fd) {
                ret = 0;
                int readLen = 0;
                while ((readLen = fread(buf + ret, 1, bufLen, fd)) > 0) {
                    ret += readLen;
                }
                fclose(fd);
            }
        }
    }
    return ret;
}

int XuFile::writeFile(const char *filePath, uint8_t *buf, const int bufLen) {
    int ret = -1;
    /* 先判断下传进的路径内容是否有问题 */
    if (strlen(filePath) > 2) {
        /* 判断下数据长度是否有问题 */
        if (bufLen > 0) {
            std::string bakPath;
            /* 看看这个文件是不是已经存在了 */
            if(fileExists(filePath)){
                /* 这个文件已经存在了，那么就把旧的文件存成备份文件 */
                bakPath.append(filePath);
                bakPath.append(".bak");
                rename(filePath,bakPath.c_str());
            }
            /* 打开文件准备写 */
            FILE *fd = fopen(filePath, "wb");
            /* 如果文件打开成功才进行读写 */
            if (fd) {
                ret = 0;
                int writeLen = 0;
                /* 循环写，直到写完 */
                while (1) {
                    writeLen = fwrite(buf + writeLen, 1, bufLen - writeLen, fd);
                    if (writeLen > 0) {
                        ret += writeLen;
                        /* 看看写完了吗  写完了才可以退出 */
                        if (ret >= bufLen) {
                            break;
                        }
                    }else{
                        break;
                    }
                }
                /* 推到缓存层 */
                fflush(fd);
                /* 同步到物理内存 */
                fsync(fileno(fd));
                /* 这个时候才可以关闭文件 */
                fclose(fd);
                /* 同步到物理内存了，那么就删除掉备份文件 */
                if(!bakPath.empty()){
                    deleteFile(bakPath);
                }
            }
        }
    }
    return ret;
}

int XuFile::doDecryptForUpgrade(const char *srcPath, const char *decPath, uint8_t *fileHead) {

    int ret = -1;
    /* 先直接复制一份过去 */
    std::string cpcmd = "cp ";
    cpcmd.append(srcPath);
    cpcmd.append(" ");
    cpcmd.append(decPath);
    cpcmd.append(" && sync");
    system(cpcmd.c_str());


    if (nullptr != srcPath && nullptr != decPath) {
        // 检查下加密类型
        switch (fileHead[26]) {
            case UPGRADE_FILE_ENCRYPTION_TYPE_NORMAL: {
                // 只在前面加了三十个字节 所以直接复制过去就行了
                ret = 0;
            }
                break;

            case UPGRADE_FILE_ENCRYPTION_TYPE_INTERVAL_INVERT: {
                /* 经过了间隔取反加密 所以要重新取反解密 */
                /* 先看看要间隔几个字节取反 */
                uint8_t intervalSize = fileHead[27];
                /* 打开文件 */
                int fd = open(decPath, O_RDWR);
                if (fd == -1) {
                    perror("open");
                }else{
                    /* 获取文件大小 */
                    struct stat sb;
                    if (fstat(fd, &sb) == -1) {
                        perror("fstat");
                        close(fd);
                    }else{
                        /* 映射文件到内存 */
                        void* map = mmap(nullptr, sb.st_size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
                        if (map == MAP_FAILED) {
                            perror("mmap");
                            close(fd);
                        }else{
                            /* 修改文件中的特定字节 */
                            for(std::size_t i = 0; i < static_cast<std::size_t>(sb.st_size); i += intervalSize){
                                uint8_t temp = ~static_cast<uint8_t *>(map)[i];
                                static_cast<uint8_t *>(map)[i] = temp;
                            }
                            /* 同步修改到磁盘 */
                            if (msync(map, sb.st_size, MS_SYNC) == -1) {
                                perror("msync");
                            }
                            /* 解映射文件并关闭文件描述符 */
                            if (munmap(map, sb.st_size) == -1) {
                                perror("munmap");
                            }
                            close(fd);
                            ret = 0;
                        }
                    }
                }
            }
                break;
            default: {
                printf("unknow encryption type: %d \n", fileHead[26]);
            }
                break;
        }
    } else {
        printf("%s and %s open failed! \n", srcPath, decPath);
    }
    return ret;
}

int XuFile::moveFille(const char *srcFilePath, const char *desFilePath, const int offset) {
    int ret = -1;
    FILE *srcFile = fopen(srcFilePath, "rb");
    FILE *decFile = fopen(desFilePath, "wb");
    if (nullptr != srcFile && nullptr != decFile) {
        int len = -1;
        /* 移动掉偏移的 */
        if (offset > 0) {
            fseek(srcFile, offset, SEEK_SET);
        }

        // 只在前面加了三十个字节 所以直接解密就行了
        uint8_t readData[4096] = {0x00};
        len = fread(readData, 1, sizeof(readData), srcFile);
        while (len > 0) {
            fwrite(readData, 1, len, decFile);
            len = fread(readData, 1, sizeof(readData), srcFile);
        }
        fclose(srcFile);
        fflush(decFile);
        fsync(fileno(decFile));
        fclose(decFile);
        /* 删除掉原文件 */
        deleteFile(srcFilePath);

    } else {
        printf("%s and %s open failed! \n", srcFilePath, desFilePath);
    }


    return ret;
}

uint64_t XuFile::getDiskTotalSize(const char *path) {
    uint64_t ret = -1;

    struct statfs diskInfo;
    if (statfs(path, &diskInfo) == 0) {
        unsigned long long blocksize = diskInfo.f_bsize;    //每个block里包含的字节数
        unsigned long long totalsize = blocksize * diskInfo.f_blocks;    //总的字节数
        ret = totalsize;
        printf("Total_size = %llu B= %llu KB = %llu MB = %llu GB\n", ret, totalsize >> 10, totalsize >> 20,
               totalsize >> 30);

    }

    return ret;
}

uint64_t XuFile::getDiskFreeSize(const char *path) {
    uint64_t ret = -1;
    struct statfs diskInfo;
    if (statfs(path, &diskInfo) == 0) {
        unsigned long long blocksize = diskInfo.f_bsize;    //每个block里包含的字节数
        unsigned long long freeDisk = diskInfo.f_bfree * blocksize;    //剩余空间的大小
        unsigned long long availableDisk = diskInfo.f_bavail * blocksize;    //可用空间大小
        ret = freeDisk;
        printf("Disk_free = %llu MB= %llu GB\nDisk_available = %llu MB = %llu GB\n",
               freeDisk >> 20, freeDisk >> 30, availableDisk >> 20, availableDisk >> 30);
    }

    return ret;
}

bool XuFile::checkMp4File(const char *mp4Path) {
    bool ret = false;
    struct stat mystat;
    /* 先通过stat的方法拿到文件的总长度 */
    if (stat(mp4Path, &mystat) == 0) {
        uint64_t realSize = mystat.st_size;
        /* 通过BOX读出来的所有box的总和 */
        uint64_t total = 0;
        /* 是否读到一个Large */
        bool readLarge = false;
        /* 当前的读到的这个BOX的长度 */
        uint32_t curBoxSize = 0;
        /* 用来存读出来的BOX头的内存  0~3：box size    4~7：box type */
        uint8_t boxHeader[8] = {0x00};
        /* 打开文件 用来一读数据 */
        FILE *fd = fopen(mp4Path, "r");
        if (!fd) {
            ret = false;
        } else {
            /* 不停得读box header */
            int readLen = 0;
            while ((readLen = fread(boxHeader, 1, sizeof(boxHeader), fd)) > 0) {
                /* 从读出来的box header取出box的size */
                curBoxSize = CodeUtils::getInstance().BbToint32(boxHeader);
//                printf("boxHeader=%s  curBoxSize=%d \n",XuString::getInstance().byteArrayToString(boxHeader,8).c_str(),curBoxSize);
                if (curBoxSize == 0) {
                    /* box的size等于0特殊处理 */
                    break;
                }
                if (curBoxSize == 1) {
                    /* box的size等于0特殊处理 表示跳过的时候还需要跳过largesize*/
                    readLarge = true;
                } else {
                    /* 把通过BOX读出来的所有box的总和加一下当前这个box的长度 */
                    total += curBoxSize;
                    /* 直接跳过当前这个BOX内容  直接取小一个box header */
                    long skip;
                    if (readLarge) {
                        skip = curBoxSize - 16;//跳过size + type + largeSize
                    } else {
                        skip = curBoxSize - 8;
                    }
                    if (skip > 0) {
                        fseek(fd, skip, SEEK_CUR);
                    }
                    /* 把readLarge恢复一下  因为下一个BOX可能不是large */
                    readLarge = false;
                }
            }
            /* 判断下文件真实大小是否跟通过box读出来的大小一致  一致就说明文件是正常的   不一致说明文件损坏 */
            ret = (realSize == total);
            fclose(fd);
        }

    } else {
        ret = false;
    }


    return ret;


}

int XuFile::removeAFileByShell(const char *filePath) {
    std::string cmd = "rm -rf ";
    cmd.append(filePath);
    XuShell::getInstance().runShellWithTimeout(cmd.c_str());
    return 0;
}

long long XuFile::getFileLength(const char *path) {
    int ret = -1;
    struct stat mystat;
    std::string filePath = path;
    if (stat(filePath.c_str(), &mystat) == 0) {
        ret = mystat.st_size;
    }
    return ret;
}

int XuFile::getMD5FromFile(const char *filePath, uint8_t *md5Buf) {
    Poco::MD5Engine md5_engine;
    Poco::DigestOutputStream output_stream(md5_engine);
    std::ifstream file_stream(filePath);
    Poco::StreamCopier::copyStream(file_stream, output_stream);
    output_stream.close();
    auto md5_val = md5_engine.digest();
    (void) memcpy(md5Buf, md5_val.data(), md5_val.size());
    return md5_val.size();
}

int XuFile::getMD5OfFromtes(uint8_t *bytes, int bytesLen, uint8_t *md5Buf) {
    Poco::MD5Engine md5_engine;
    Poco::DigestOutputStream output_stream(md5_engine);
    std::string bytesTemp = "";
    bytesTemp.append(reinterpret_cast<const char *>(bytes), bytesLen);
    std::istringstream istr(bytesTemp);
    Poco::StreamCopier::copyStream(istr, output_stream);
    output_stream.close();
    auto md5_val = md5_engine.digest();
    printf("getMD5OfBytes  md5_val.size()=%d \n", md5_val.size());
    (void) memcpy(md5Buf, md5_val.data(), md5_val.size());
    return md5_val.size();
}

std::vector<std::string> XuFile::getDirAllFile_Full(const char *path, const char *filter) {
    DIR *dir;
    std::vector<std::string> result;
    dir = opendir(path);
    if (dir != nullptr) {
        struct dirent *ptr;
        while ((ptr = readdir(dir)) != nullptr) {
            if(strcmp(ptr->d_name,".") != 0 && strcmp(ptr->d_name,"..") != 0){
                /* 读到了文件，那么需要看看是不是一个目录如果是就需要打开目录 */
                if(ptr->d_type == DT_DIR){
                    std::string subdirectoryPath = "";
                    subdirectoryPath.append(path);
                    subdirectoryPath.append(ptr->d_name);
                    subdirectoryPath.append("/");
                    printf("file:%s  \n",subdirectoryPath.c_str());
                    std::vector<std::string> fileList = getDirAllFile_Full(subdirectoryPath.c_str(),filter);
                    if(!fileList.empty()){
                        for(std::size_t i = 0; i < fileList.size(); i ++){
                            result.push_back(fileList[i]);
                        }
                    }
                }else{
                    if (filter != nullptr) {
                        if (strstr(ptr->d_name, filter) != nullptr) {
                            std::string fullFilePath = "";
                            fullFilePath.append(path);
                            fullFilePath.append(ptr->d_name);
                            result.push_back(fullFilePath);
                        } else { ;
                        }
                    } else {
                        std::string fullFilePath = "";
                        fullFilePath.append(path);
                        fullFilePath.append(ptr->d_name);
                        result.push_back(fullFilePath);
                    }
                }
            }


        }
    } else {
        printf("open %s failed! \n",path);
    }
    closedir(dir);
    return result;
}


void XuFile::removeOldMp4File_Full(const char *path, int count, const bool noZero) {
    /* 先拿 */
    std::vector<std::string> files = getDirAllFile_Full(path, ".mp4");
    /* 再排序 */
    std::sort(files.begin(), files.end(), doMP4FileAscending_FullPath);
    /* 再删 */
    int curDeleteCount = 0;
    int filesSize = static_cast<int>(files.size());
    int deleteFileCount = filesSize > count ? count : filesSize;
    for(int i = 0; (i < filesSize && curDeleteCount < deleteFileCount); i ++ ){
        /* 看看是否必须是要有长度的才算 */
        if(noZero){
            if(getFileLength(files[i].c_str()) > 0){
                curDeleteCount ++;
            }
        }else{
            curDeleteCount ++;
        }
        /* 在这里实际删除 */
        remove(files[i].c_str());
        printf("to remove file %s \n",files[i].c_str());
    }
}

int XuFile::encryptFile_AES_256_CBC(const std::string &inputFile, const std::string &outputFile, const uint8_t *key,
                                    const uint8_t *iv) {
    int ret = -1;

    try {
            /* 源文件的输入流 */
            std::ifstream srcIn(inputFile.c_str(),std::ios_base::in | std::ios_base::binary | std::ios_base::ate);
            /* 加密后的文件的输出流 */
            std::ofstream decOut(outputFile.c_str(),std::ios_base::out | std::ios_base::binary);
            /* 如果都打开了才能进行解密 */
            if(srcIn.is_open() && decOut.is_open()){
            /* 初始化OpenSSL库 */
            OPENSSL_init_crypto(OPENSSL_INIT_ADD_ALL_CIPHERS \
             | OPENSSL_INIT_ADD_ALL_DIGESTS, NULL);
            OPENSSL_init_crypto(OPENSSL_INIT_LOAD_CRYPTO_STRINGS, NULL);
                /* 初始化下加密工具 */
                EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
                EVP_CIPHER_CTX_init(ctx);
                EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key, iv);
                EVP_CIPHER_CTX_set_padding(ctx,EVP_PADDING_PKCS7);
                /* 确定文件的大小 */
                uint64_t inputFileLen = srcIn.tellg();
                /* 重新将文件流指针置于文件开始的位置 */
                srcIn.seekg(0, std::ios_base::beg);
                /* 用来读数据的缓冲区 */
                char readBuf[8192] = {0x00};
                /* 用来进行加密然后写数据的缓冲区（比读取大32个字节是为了放填充数据） */
                uint8_t writeBuf[8192+32] = {0x00};
                /* 记录读了多少长度的变量 */
                uint64_t allReadLen = 0;
                /* 循环从流里读出文件 */
                while (allReadLen < inputFileLen){
                    /* 当前应该读多长的数据 */
                    std::size_t curRead = sizeof(readBuf);
                    if(inputFileLen - allReadLen < sizeof(readBuf)){
                        curRead = inputFileLen - allReadLen;
                    }
                    /* 读出原文 */
                    srcIn.read(readBuf, curRead);
                    /* 记录一下长度 */
                    allReadLen = allReadLen + curRead;

                    /* 对原文一包包读出来进行加密 */
                    int toEnBufLen = 0;
                    int curEnLen = 0;
                    if(!EVP_EncryptUpdate(ctx, writeBuf, &toEnBufLen,reinterpret_cast<const unsigned char *>(readBuf), curRead)){
                        printf("EVP_EncryptUpdate failed!  err:%s  \n", ERR_error_string(ERR_get_error(),NULL));
                        break;
                    }
                    /* 如果是最后一包了，那么就调用一下结束，同时拿到填充出来的数据，只有调用了结束，才会加填充，这样加密一整个文件的时候就只是尾部有填充字节 */
                    if(curRead < sizeof(readBuf)){
                        if(!EVP_EncryptFinal_ex(ctx, writeBuf + toEnBufLen, &curEnLen)){
                            printf("EVP_EncryptFinal_ex failed!  err:%s  \n", ERR_error_string(ERR_get_error(),NULL));
                            break;
                        }
                        toEnBufLen += curEnLen;
                    }
//                    /* 将密文写入文件 */
                    decOut.write(reinterpret_cast<const char *>(writeBuf), toEnBufLen);

//                    printf("allReadLen=%llu   inputFileLen=%llu  toEnBufLen=%d curEnLen=%d curRead=%d\n",allReadLen,inputFileLen,toEnBufLen,curEnLen,curRead);
                }
                /* 关闭流 */
                srcIn.close();
                decOut.close();
                /* 关闭加密工具 */
                EVP_CIPHER_CTX_reset(ctx);
                EVP_CIPHER_CTX_free(ctx);

                ret = 0;
            }else{
                printf("%s or %s can not open! \n", inputFile.c_str(),outputFile.c_str());
            }
    } catch (...) {
        printf("encryptFile_AES_256_CBC failed! err:%s  \n", strerror(errno));
    }
    return ret;

}

/**
 * 用AES-256-CBC的方式解密一个文件
 * @param inputFile ： 输入文件（源文件）地址
 * @param outputFile ： 输出文件（解密后的文件）地址
 * @param key ： 密钥
 * @param iv ： 初始向量
 * @return  0：成功  其他：失败
 */
int XuFile::decryptFile_AES_256_CBC(const std::string &inputFile, const std::string &outputFile, const uint8_t *key, const uint8_t *iv) {
    int ret = -1;
    try {
            /* 源文件的输入流 */
            std::ifstream srcIn(inputFile.c_str(),std::ios_base::in | std::ios_base::binary | std::ios_base::ate);
            /* 解密后的文件的输出流 */
            std::ofstream decOut(outputFile.c_str(),std::ios_base::out | std::ios_base::binary | std::ios_base::trunc);
            /* 如果都打开了才能进行解密 */
            if(srcIn.is_open() && decOut.is_open()){
                /* 初始化OpenSSL库 */
                OPENSSL_init_crypto(OPENSSL_INIT_ADD_ALL_CIPHERS \
             | OPENSSL_INIT_ADD_ALL_DIGESTS, NULL);
                OPENSSL_init_crypto(OPENSSL_INIT_LOAD_CRYPTO_STRINGS, NULL);
                /* 初始化下解密工具 */
                EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
                EVP_CIPHER_CTX_init(ctx);
                EVP_DecryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key, iv);

                EVP_CIPHER_CTX_set_padding(ctx,EVP_PADDING_PKCS7);
                /* 确定文件的大小 */
                uint64_t inputFileLen = srcIn.tellg();
                /* 重新将文件流指针置于文件开始的位置 */
                srcIn.seekg(0, std::ios_base::beg);
                /* 用来读数据的缓冲区 */
                char readBuf[8192] = {0x00};
                /* 用来进行解密然后写数据的缓冲区（比读取大32个字节是为了放填充数据） */
                uint8_t writeBuf[8192+32] = {0x00};
                /* 记录读了多少长度的变量 */
                uint64_t allReadLen = 0;
                /* 循环从流里读出文件 */
                while (allReadLen < inputFileLen){
                    /* 当前应该读多长的数据 */
                    std::size_t curRead = sizeof(readBuf);
                    if(inputFileLen - allReadLen < sizeof(readBuf)){
                        curRead = inputFileLen - allReadLen;
                    }
                    /* 读出密文 */
                    srcIn.read(readBuf, curRead);
                    /* 记录一下长度 */
                    allReadLen = allReadLen + curRead;
                    /* 对密文一包包读取数据的数据进行解密 */
                    int toEnBufLen = 0;
                    int curEnLen = 0;
                    if(!EVP_DecryptUpdate(ctx, writeBuf, &toEnBufLen,reinterpret_cast<const unsigned char *>(readBuf), curRead)){
                        printf("EVP_DecryptUpdate failed!  err:%s  \n", ERR_error_string(ERR_get_error(),NULL));
                        break;
                    }
                    /* 如果是最后一包了，那么就调用一下结束 */
                    if(curRead < sizeof(readBuf)){
                        if(!EVP_DecryptFinal(ctx, writeBuf + toEnBufLen, &curEnLen)){
                            printf("EVP_DecryptFinal failed!  err:%s  \n", ERR_error_string(ERR_get_error(),NULL));
                            break;
                        }
                        toEnBufLen += curEnLen;
                    }

//                    /* 将解密后的数据写入文件 */
                    decOut.write(reinterpret_cast<const char *>(writeBuf), toEnBufLen);

                    printf("decryptFile_AES_256_CBC allReadLen=%llu   inputFileLen=%llu  toEnBufLen=%d curEnLen=%d curRead=%d\n",allReadLen,inputFileLen,toEnBufLen,curEnLen,curRead);
                }
                /* 关闭流 */
                srcIn.close();
                decOut.close();
                /* 关闭解密工具 */
                EVP_CIPHER_CTX_reset(ctx);
                EVP_CIPHER_CTX_free(ctx);

                ret = 0;
            }else{
                printf("%s or %s can not open! \n", inputFile.c_str(),outputFile.c_str());
            }

    } catch (...) {
        printf("decryptFile_AES_256_CBC failed! errstr:%s  \n",  ERR_error_string(ERR_get_error(),NULL));

    }
    return ret;

}

int XuFile::PKCS7_PAD(uint8_t *buf, int len, int blockSize) {
    int paddingLength = blockSize - (len % blockSize);
    char paddingChar = (char)paddingLength;
    if(paddingLength == 0){
        paddingLength = blockSize;
        paddingChar = (char)blockSize;
    }
    for (int i = 0; i < paddingLength; i++) {
        buf[len + i] = paddingChar;
    }
    return paddingLength;
}

void XuFile::deleteFile(const std::string &filePath) {
    std::string cmdStr = "rm -rf ";
    cmdStr.append(filePath.c_str());
    cmdStr.append(" && sync");
    XuShell::getInstance().runShellWithTimeout(cmdStr.c_str());
}

void XuFile::removeOldJPGFile_Full(const char *path, const int count, const bool noZero) {
    /* 先拿 */
    std::vector<std::string> files = getDirAllFile_Full(path, ".jpg");
    /* 再排序 */
    std::sort(files.begin(), files.end(), doMP4FileAscending_FullPath);
    /* 再删 */
    int curDeleteCount = 0;
    int filesSize = static_cast<int>(files.size());
    int deleteFileCount = filesSize > count ? count : filesSize;
    for(int i = 0; (i < filesSize && curDeleteCount < deleteFileCount); i ++ ){
        /* 看看是否必须是要有长度的才算 */
        if(noZero){
            if(getFileLength(files[i].c_str()) > 0){
                curDeleteCount ++;
            }
        }else{
            curDeleteCount ++;
        }
        /* 在这里实际删除 */
        remove(files[i].c_str());
        printf("to remove file %s \n",files[i].c_str());
    }
}







