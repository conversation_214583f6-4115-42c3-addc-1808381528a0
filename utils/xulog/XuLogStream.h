//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/15.
//

#ifndef MRV440SOFTWARE_XULOGSTREAM_H
#define MRV440SOFTWARE_XULOGSTREAM_H


#include <sstream>
#include <iostream>
#include <cstring>

#include "XuLogChanneCallback.h"
#define XU_LOG_END "XuLogStreamEnd"


class XuLogStream{

public:


    /**
     * 增加<<运算符，主要用来检测结束值
     */
    XuLogStream& operator<<(int content) ;

    XuLogStream& operator<<(const char *content);

    XuLogStream& operator<<(char *content);

    XuLogStream& operator<<(long content);

    XuLogStream& operator<<(unsigned long content);

    XuLogStream& operator<<(bool content);

    XuLogStream& operator<<(short content);

    XuLogStream& operator<<(unsigned short content);

    XuLogStream& operator<<(unsigned int content);

    XuLogStream& operator<<(long long content);

    XuLogStream& operator<<(unsigned long long content);

    XuLogStream& operator<<(double content);

    XuLogStream& operator<<(float content);

    XuLogStream& operator<<(long double content);

    XuLogStream& operator<<(std::string content);






//    XuLog2& operator<<(const void *content);



    void setCallback(XuLogChanneCallback &channeCallback);

    void setLogLevel(int logLevel);


    void clear();

    bool getIsUsing() const;

    void toBeUse();
private:
    /* 日志的级别 */
    int logLevel = 9999;
    /* 返回给上层的回调 */
    XuLogChanneCallback *callback;
    /* 用来存放单条日志内容的流 */
    std::stringstream  ss;
    /* 是否正在被使用 */
    bool isUsing = false;

};




#endif //MRV440SOFTWARE_XULOGSTREAM_H
