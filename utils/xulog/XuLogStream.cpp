//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/15.
//

#include "XuLogStream.h"


XuLogStream &XuLogStream::operator<<(int content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(const char *content) {
    /* 看看是不是要结束了，如果是的话，那么就告诉上面，并清空自己 */
    if(strcmp(content,XU_LOG_END) == 0){
        /* 加个换行 */
        ss << "\n";
        /* 回调出去 */
        callback->onLogMakeFinish(logLevel, ss.str().c_str());
        /* 打印到控制台 */
        printf("%s",ss.str().c_str());
        /* 清理一下 */
        clear();
    }else{
        ss << content;
    }

    return *this;
}

XuLogStream &XuLogStream::operator<<(char *content) {
    /* 看看是不是要结束了，如果是的话，那么就告诉上面，并清空自己 */
    if(strcmp(content,XU_LOG_END) == 0){
        /* 加个换行 */
        ss << "\n";
        /* 回调出去 */
        callback->onLogMakeFinish(logLevel, ss.str().c_str());
        /* 打印到控制台 */
        printf("%s",ss.str().c_str());
        /* 清理一下 */
        clear();
    }else{
        ss << content;
    }

    return *this;
}

XuLogStream &XuLogStream::operator<<(long content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(unsigned long content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(bool content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(short content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(unsigned short content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(unsigned int content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(long long int content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(unsigned long long int content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(double content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(float content) {
    ss << content;
    return *this;
}

XuLogStream &XuLogStream::operator<<(long double content) {
    ss << content;
    return *this;
}
XuLogStream &XuLogStream::operator<<(std::string content) {
    ss << content;
    return *this;
}


void XuLogStream::clear() {
    ss.str("");
    isUsing = false;
}

bool XuLogStream::getIsUsing() const {
    return isUsing;
}

void XuLogStream::setCallback(XuLogChanneCallback &channeCallback) {
    callback = &channeCallback;
}

void XuLogStream::toBeUse() {
    isUsing = true;
}

void XuLogStream::setLogLevel(int logLevel) {
    XuLogStream::logLevel = logLevel;
}






