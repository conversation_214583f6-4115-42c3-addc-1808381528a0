//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/10.
//

#ifndef VIS_G3_SOFTWARE_XUCALCULATIONTOOL_H
#define VIS_G3_SOFTWARE_XUCALCULATIONTOOL_H

#include <algorithm>
#include <cstdint>
#include "XuYUVDataOpt.h"
#include "Point2D.h"
#include "vector"
#include <cstdio>
#include <valarray>

struct VISPoint {
    uint16_t x = 0;
    uint16_t y = 0;
};

struct VISLine {
    VISPoint startPoint;
    VISPoint endPoint;
};

class XuCalculationTool {
public:

    XuCalculationTool();

    ~XuCalculationTool();

    /* 单例 */
    static XuCalculationTool &getInstance();

    /***
     * 判断点是否在多边形内
     *
     * @param point : 需要判断是否在多边形内的点坐标
     * @param polygonPonits ： 多边形的顶点坐标（按顺时针的顺序）
     * @param polygonPonitCount ： 多边形的顶点数量
     *
     * @return 该点是否在多边形内
     * */
    bool isPointInPolygon(VISPoint point, VISPoint *polygonPonits, int polygonPonitCount);

    /***
     * 判断线段是否有与多边形的某条边相交
     *
     * @param line : 需要判断是否相交的点
     * @param polygonPonits ： 多边形的顶点坐标（按顺时针的顺序）
     * @param polygonPonitCount ： 多边形的顶点数量
     *
     * @return 是否有与多边形的某条边相交
     * */
    bool isLineIntersecting(VISLine line1, VISLine line2);

    int mult(VISPoint point1, VISPoint point2, VISPoint point3);


    /***
     * 判断两个多边形是否有重叠的地方（只要有任意一个顶点在另一个多边形内容  则认为有重叠）
     *
     * @param polygon1Ponits : 多边形1的顶点坐标（按顺时针的顺序）
     * @param polygon1PonitCount ： 多边形1的顶点数量
     * @param polygon2Ponits : 多边形2的顶点坐标（按顺时针的顺序）
     * @param polygon2PonitCount ： 多边形2的顶点数量
     *
     * @return 两个多边形是否有重叠的区域
     * */
    bool
    isPolygon1AndPolygon2HaveOverlappingArea(VISPoint *polygon1Ponits, int polygon1PonitCount, VISPoint *polygon2Ponits,
                                             int polygon2PonitCount);


    /**
     *
     * 判断某条线是否跟某个多边形是否有交集（只要有任意一个顶点在另一个多边形内容  则认为有重叠）
     *
     * @param line ： 需要判断的线段
     * @param polygonPonits ：多边形的顶点坐标（按顺时针的顺序）
     * @param polygonPonitCount ： 多边形的顶点数量
     *
     * @return 是否有交集
     */
    bool isLineAndPolygonHaveOverlappingArea(VISLine line, VISPoint *polygonPonits, int polygonPonitCount);


    /**
     * 获取外设升级文件的校验和
     *
     * @param filePath ： 外设升级文件的路径
     *
     * @return 校验和
     * */
    uint8_t getPeripheralUpgradeFileSumCheck(const char *filePath);

    /**
     * 对一个多边形进行等距缩放 (https://blog.csdn.net/leon_zeng0/article/details/73500174   根据这篇文章写的，具体原理看这里)
     *
     *
     * @param srcPoints ： 需要进行缩放的多边形的顶点数组
     * @param decPoints ： 缩放过后的多边形的顶点数组
     * @param dist ： 缩放的距离  负是向外扩张 为正是向内收缩
     * @return 结果  0：成功   其他：失败
     */
    uint8_t polygonIsometricScaling(YUVDataOpt_Point *srcPoints, YUVDataOpt_Point *decPoints, float dist);

    /**
     * 对一个多边形进行等比例缩放 (只能保证形状不边，无法保证每条边都等距膨胀，特别是凹多边形)
     *
     *
     * @param srcPoints ： 需要进行缩放的多边形的顶点数组
     * @param decPoints ： 缩放过后的多边形的顶点数组
     * @param dist ： 缩放的比例
     * @return 结果  0：成功   其他：失败
     */
    uint8_t polygonIsometricScaling2(VISPoint *srcPoints, VISPoint *decPoints, float dist);


private:

};


#endif //VIS_G3_SOFTWARE_XUCALCULATIONTOOL_H
