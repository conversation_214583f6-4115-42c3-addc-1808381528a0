//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/30.
//





#include "XuCANOpt.h"


int XuCANOpt::initCANBus(int bitRate, const std::string ifName) {
    int ret = -1;
    canfd_revc = init(ifName);
    canfd_send = init(ifName);
    if (canfd_revc >= 0 && canfd_send >= 0) {
        ret = 0;
    }
    return ret;

}

int XuCANOpt::recvCANData(can_frame &frame, timeval tv) {
    int ret = -1;
    fd_set rset;
    if (tv.tv_usec > 0 || tv.tv_sec > 0) {
        FD_ZERO(&rset);
        FD_SET(canfd_revc, &rset);
        ret = select(canfd_revc + 1, &rset, nullptr, nullptr, &tv);
        if (ret == -1) {
            perror("select ret = -1");
            return ret;
        } else if (ret > 0) {
            if (!FD_ISSET(canfd_revc, &rset)) {
                return ret;
            }
        } else {
            // timeout
//            printf("select timeout  \n");
            return ret;
        }
    }


    ret = read(canfd_revc, &frame, sizeof(frame));
//        printf("read one can data,ret=%d    id = %u  value=%02x %02x %02x %02x %02x %02x %02x %02x \n",ret,frame.can_id,
//               frame.data[0],frame.data[1],frame.data[2],frame.data[3],frame.data[4],frame.data[5],frame.data[6],frame.data[7]);
    return ret;
}

int XuCANOpt::sendCanFrame(can_frame &frame, const uint &timeout) {
    struct timeval tv{0};

    tv.tv_usec = timeout % 1000 * 1000;
    tv.tv_sec = timeout / 1000;

    return sendCanFrame(frame, tv);
}

int XuCANOpt::sendCanFrame(can_frame &frame, timeval &tv) {
    int ret = 0;
    ret = write(canfd_send, &frame, sizeof(frame));
    if (ret < 0 || ret != sizeof(frame)) {
        if (strcmp("No buffer space available", strerror(errno)) == 0) {
        }


        return -1;
    }

    if (!isSendFinish(frame, tv)) {
        ret = -2;
    }

    return ret;
}

bool XuCANOpt::isSendFinish(can_frame &frame, timeval &tv) {
    int iRet{0};
    bool bRet{false};
    struct can_frame recvFrame = {0};
    const __suseconds_t usec = 1000;

    do {
        // 如果接收到的can id超过7个位，则很有可能是驱动上传的错误信息
        if (recvFrame.can_id > MAX_CAN_ID) {
            printCanFrame(recvFrame);
            // 此处还应该要判断can的错误类型之类的，目前暂时不做，在发布版本前做
            if (isFatalError(recvFrame)) {
                break;
            }
            usleep(usec);
            if (usec > tv.tv_usec && 0 == tv.tv_sec) {
                tv.tv_sec = 0;
                tv.tv_usec = 0;
            } else {
                if (usec > tv.tv_usec) {
                    tv.tv_usec = tv.tv_usec + 1000000 - usec;
                    --tv.tv_sec;
                } else {
                    tv.tv_usec -= usec;
                }
            }

            // 重发一次
            resend();
        }

        /* 发出去后直接看看能不能读回来一样的操作这个先不错 */
        memset(&recvFrame, 0, sizeof(recvFrame));
        iRet = recvCANData(recvFrame, tv);
        // 超时或者出错都返回
        if (iRet <= 0) {
            break;
        }

        if (equal(recvFrame, frame)) {
            bRet = true;
            break;
        }
    } while (0 != tv.tv_sec || 0 != tv.tv_usec);

    if (recvFrame.can_id > MAX_CAN_ID) {
        copyFrame(frame, recvFrame);
    }

    // 清空所有数据
    do {
        tv.tv_sec = 0;
        tv.tv_usec = 1;
    } while (recvCANData(recvFrame, tv) > 0);

    return bRet;
}

void XuCANOpt::printCanFrame(const can_frame &frame) {
    char str[100] = {0};
    char tmp[12] = {0};
    sprintf(str, "id=0x%04x,\tdata: ", frame.can_id);

    for (int i = 0; i < frame.can_dlc; ++i) {
        memset(tmp, 0, sizeof(tmp));
        sprintf(tmp, "%02x ", frame.data[i]);
        strcat(str, tmp);
    }

}

bool XuCANOpt::isFatalError(const can_frame &frame) {
    return isBusOff(frame);
}

bool XuCANOpt::isBusOff(const can_frame &frame) {
    if (frame.can_id > MAX_CAN_ID && frame.can_id & CAN_ERR_BUSOFF) {
        return true;
    }

    return false;
}

void XuCANOpt::resend() {
    struct ifreq ifr = {0};
    strncpy(ifr.ifr_name, curIfName.c_str(), IFNAMSIZ - 1);
    ioctl(canfd_send, SIOC_DO_RESEND, &ifr);
}

bool XuCANOpt::equal(const can_frame &frameA, const can_frame &frameB) {
    if (frameA.can_id != frameB.can_id) {
        return false;
    }

    if (frameA.can_dlc != frameB.can_dlc) {
        return false;
    }

    if (memcmp(frameA.data, frameB.data, frameA.can_dlc) != 0) {
        return false;
    }

    return true;
}

void XuCANOpt::copyFrame(can_frame &to, const can_frame &from) {
    memcpy(&to, &from, sizeof(struct can_frame));
}

int XuCANOpt::setIdMask(const XuCANOpt::can_idmask_s &mask) {
    struct ifreq ifr = {0};

    ifr.ifr_data = (__caddr_t) &mask;

    strncpy(ifr.ifr_name, curIfName.c_str(), IFNAMSIZ - 1);

    return ioctl(canfd_send, SIOC_SET_IDMASK, &ifr);
}

void XuCANOpt::closeCAN() {
    if (canfd_revc != -1) {
        close(canfd_revc);
        canfd_revc = -1;
    }

    if (canfd_send != -1) {
        close(canfd_send);
        canfd_send = -1;
    }
}

int XuCANOpt::init(const std::string ifName) {
    int ret = 0;
    struct ifreq ifr = {0};
    int canfd_on = 1;
    can_err_mask_t err_mask = 0xffffffff;
    struct can_filter rfilter = {0, 0};
    int flags = 0;
    int fd = 0;

    // 创建socket套接字
    fd = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (fd < 0) {
        return fd;
    }

    // 初始化addr结构体
    memset(&addr, 0, sizeof(addr));
    addr.can_family = AF_CAN;
    strncpy(ifr.ifr_name, ifName.c_str(), ifName.size());
    ret = ioctl(fd, SIOCGIFINDEX, &ifr);
    if (ret < 0) {
        perror("ioctl SIOCGIFINDEX");
        return ret;
    }
    addr.can_ifindex = ifr.ifr_ifindex;
    if (0 >= addr.can_ifindex) {
        return -1;
    }

    // 禁用过滤器
    setsockopt(fd, SOL_CAN_RAW, CAN_RAW_ERR_FILTER, &err_mask, sizeof(err_mask));
    setsockopt(fd, SOL_CAN_RAW, CAN_RAW_FILTER, &rfilter, sizeof(rfilter));
    setsockopt(fd, SOL_CAN_RAW, CAN_RAW_FD_FRAMES, &canfd_on, sizeof(canfd_on));

    // 不阻塞读写
    flags = fcntl(fd, F_GETFL);
    flags |= O_NONBLOCK;
    fcntl(fd, F_SETFL, flags);

    // 绑定网络接口
    ret = bind(fd, (struct sockaddr *) &addr, sizeof(addr));
    if (ret < 0) {
        perror("bind");
        return ret;
    }

    return fd;






//    int ret = 0;
//    struct ifreq ifr = {0};
//    int canfd_on = 1;
//    can_err_mask_t err_mask = 0xffffffff;
//    struct can_filter rfilter = {0, 0};
//    int flags = 0;
//    int canfd = -1;
//
//    // 创建socket套接字
//    canfd_revc = socket(PF_CAN, SOCK_RAW, CAN_RAW);
//    if (canfd < 0) {
//        return canfd;
//    }
//    // 初始化addr结构体
//    memset(&addr, 0, sizeof(addr));
//    addr.can_family = AF_CAN;
//    strncpy(ifr.ifr_name, ifName.c_str(), ifName.length());
//    curIfName.append(ifName);
//    ret = ioctl(canfd, SIOCGIFINDEX, &ifr);
//    if (ret < 0) {
//        perror("ioctl SIOCGIFINDEX");
//        return ret;
//    }
//
//    // 不阻塞读写
//    flags = fcntl(canfd, F_GETFL);
//    flags |= O_NONBLOCK;
//    fcntl(canfd, F_SETFL, flags);
//
//    addr.can_ifindex = ifr.ifr_ifindex;
//    if (0 >= addr.can_ifindex) {
//        printf("invalid bridge interface");
//        return -1;
//    }
//    addr.can_family = AF_CAN;
//    addr.can_ifindex = ifr.ifr_ifindex;
//    bind(canfd, (struct sockaddr *) &addr, sizeof(addr));
//
//    // 禁用过滤器
//    setsockopt(canfd, SOL_CAN_RAW, CAN_RAW_ERR_FILTER, &err_mask, sizeof(err_mask));
//    setsockopt(canfd, SOL_CAN_RAW, CAN_RAW_FILTER, &rfilter, sizeof(rfilter));
//    setsockopt(canfd, SOL_CAN_RAW, CAN_RAW_FD_FRAMES, &canfd_on, sizeof(canfd_on));
//
//    setsockopt(canfd, SOL_CAN_RAW, CAN_RAW_FILTER, &rfilter, sizeof(rfilter));
//
//    return canfd;
}




