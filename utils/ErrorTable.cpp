//
// Created by xwf on 2021/3/26.
//

#include "ErrorTable.h"

namespace vis {
    const char *et_string[] = {
            [et_success] = "success",
            [et_openfile] = "open file error",
            [et_createGpioIndex] = "create gpio index fail",
            [et_gpioUsed] = "gpio pin have be used",
            [et_param] = "param list error",
            [et_createFile] = "create file fial",
            [et_gpioAttrib] = "The GPIO attribute is incorrect",
            [et_setGpioAttrib] = "set gpio attrib fail",
            [et_pthreadRunning] = "Pthread is already running",
            [et_cmdlineParam] = "Command line argument passing error",
            [et_writeFile] = "write file fail",
            [et_pthreadCreate] = "pthread_create fail",
            [et_fileOpened] = "The file has been opened",
            [et_getUartAttr] = "Failed to get the UART property",
            [et_tcflush] = "tcflush fail",
            [et_tcsetattr] = "tcsetattr fail",
            [et_getBaudrate] = "Failed to get uart baud rate",
            [et_select] = "select() fail",
            [et_timeout] = "timeout",
            [et_fileMiss] = "File does not exist",
            [et_socketOpened] = "The socket has been opened",
            [et_socket] = "Failed to execute the socket function",
            [et_bind] = "Failed to execute the bind function",
            [et_setsockopt] = "Failed to execute the setsockopt function",
            [et_connect] = "Failed to execute the connect function",
            [et_getsockopt] = "Failed to execute the getsockopt function",
            [et_configFile] = "fail to Parse config file",
            [et_loadYamlFile] = "loadRawData yaml file fail",
            [et_yamlParam] = "Error parsing YAML parameters",
            [et_createDirectories] = "Failed to create directory",
            [et_yamlthrow] = "yaml lib throw an abnormal",
            [et_pocothrow] = "poco lib throw an abnormal",
            [et_recvfrom] = "Error in recvfrom function execution",
            [et_sendto] = "Error in sendto function execution",
            [et_loggerInit] = "init logger fail",
            [et_initGpio] = "init gpio fail",
            [et_tcgetattr] = "tcgetattr fail",
            [et_busy] = "busy",
            [et_unknow] = "unknow error code", // 在此行的上一行中加入含义
    };

    /**
     * @param errNo 错误码
     * @return 返回错误码对应的错误信息
     */
    std::string et_getErrStr(int errNo) {
        std::string errStr = "This error code was not found. code is: " + std::to_string(errNo);

        if (errNo < 0) {
            errNo = -errNo;
        }

        if (errNo <= et_unknow) {
            errStr = et_string[errNo];
        } else {
            errStr += std::to_string(errNo);
        }

        errStr += "\n";

        return errStr;
    }

} // namespace vis