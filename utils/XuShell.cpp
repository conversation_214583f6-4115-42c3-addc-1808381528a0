//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/22.
//

#include <cstring>
#include "XuShell.h"
#include "XuString.h"
#include "XuFile.h"
#include "G3_Configuration.h"
#include "XuLog.h"


XuShell &XuShell::getInstance() {
    static XuShell _instance;
    return _instance;
}

std::string XuShell::runShellWithTimeout(std::string cmd, int msec) {
    std::string ret;
    timeval tv;
    fd_set fds;

    try {
        FILE *pf = popen(cmd.c_str(), "r");
        if (nullptr == pf) {
            return std::string("");
        }

        // 获取文件描述符
        int fd = fileno(pf);

        while (true) {
            FD_ZERO(&fds);
            FD_SET(fd, &fds);

            tv.tv_sec = msec / 1000;
            tv.tv_usec = (msec % 1000) * 1000;

            // 设置超时时间
            int ready_fds = select(fd + 1, &fds, NULL, NULL, &tv);
            if (ready_fds == -1) {
                if (errno == EINTR) {
                    // 处理中断，可以选择重试或直接退出
                    continue;
                } else {
                    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,typeid(*this).name()) << "run shell " << cmd << "failed! err=" << std::strerror(errno) << XU_LOG_END;
                }
            } else if (ready_fds == 0) {
                // 超时
                break;
            }

            char buffer[1024] = {'\0'};
            if (fgets(buffer, sizeof(buffer), pf)) {
                ret += buffer;
            } else {
                // 可能是读取结束，也可能是读取错误，此处可以根据需要处理
                break;
            }
        }

        pclose(pf);
    } catch (...) {

    }

    return ret;
}


void XuShell::killallG3sofeward(std::string tag) {
    /* 设置一下时区，免得到时候时间不对 */
    setenv("TZ", G3_Configuration::getInstance().getLocalTimeZone().c_str(), 1);

    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,typeid(*this).name()) << "kill G3sofeward by " << tag << XU_LOG_END;

    system("sync");
//    sleep(2);
     system("echo -en \"sleep 6 \\n killall -9 vis_g3_software\" | bash &");
//    _exit(0);

}

void XuShell::rebootG3sofeward(std::string tag) {
    /* 设置一下时区，免得到时候时间不对 */
    setenv("TZ", G3_Configuration::getInstance().getLocalTimeZone().c_str(), 1);

    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,typeid(*this).name()) << "reboot MRV220 by " << tag << XU_LOG_END;

    system("sync");
    system("echo -en \"sleep 2 \\n reboot\" | bash &");
}

void XuShell::startResetApp() {
    /* 先判断下是不是已经启动了 */
    std::string psStr = runShellWithTimeout("ps -ef | grep 'vis_g3' ",100);
    printf("psStr=%s  \n",psStr.c_str());
    if(!strstr(psStr.c_str(),"vis_g3_reset")){
        system("echo -en \"cd /userdata/ \\n chmod 777 vis_g3_reset \\n ./vis_g3_reset \\n \" | bash &");
    }else{
        printf("vis_g3_reset is running! \n");
    }
}

void XuShell::stopResetApp() {
    /* 先判断下是不是已经启动了 */
    std::string psStr = runShellWithTimeout("ps -ef | grep 'vis_g3' ",2000);
    printf("psStr=%s  \n",psStr.c_str());
    if(!strstr(psStr.c_str(),"vis_g3_reset")){
        printf("vis_g3_reset is not run! \n");

    }else{
        runShellWithTimeout("killall -9 vis_g3_reset",2000);
    }

}
