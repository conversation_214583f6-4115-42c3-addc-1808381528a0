#include <opencv2/opencv.hpp>
#include <string>
#include "get_alarm.h"
#include "run.h"
#include "resnet18_muv.h"

extern dsm_muv::globals globalmuvs;



//���ֱ���

//����ʶ��
//bool Talarm::recognition_gesture(int far_info, Mat frame)
//{
//	string img_path = "zidane.jpg";
//	string model_path = "epoch1210.onnx";
//
//	//handnet
//	Net net;
//	if (handnet.readModel(net, model_path, false)) {
//		cout << "read net ok!" << endl;
//	}
//	vector<Scalar> color;
//	srand(time(0));
//	for (int i = 0; i < 10; i++) {
//		int b = rand() % 256;
//		int g = rand() % 256;
//		int r = rand() % 256;
//		color.push_back(Scalar(b, g, r));
//	}
//	vector<hand_output> result;
//	Mat img = imread(img_path);
//	if (handnet.detect_hand(img, net, result))
//	{
//		handnet.drawPred_hand(img, result, color);
//	}
//	else
//	{
//		cout << "Detect Failed!" << endl;
//	}
//
//
//	if (result.at<float>(0, 0) > result.at<float>(0, 1))
//	{
//		//putText(face_info.frame, ratetxt, Point(10, 70), FONT_HERSHEY_COMPLEX, 0.6, Scalar(0, 0, 255), 1, 8, 0);
//		//putTextZH(frame, "[6]����ʶ����Ϊfalse", Point(10, 290), Scalar(0, 0, 255), 18, "����");
//	}
//	else
//	{
//		return true;
//	}
//}


int dsm_muv::Talarm::early_termination(std::vector<int> input_vec, int frame_early)
{
	float sum_alarm = 0;
	for (size_t i = 0; i < input_vec.size(); i++)
	{
		if (input_vec[i] == 1)
		{
			sum_alarm++;
		}
	}
	if (sum_alarm >= frame_early)
	{
		return 1;
	}
	return 0;
}

//Ƶ�κ�λ��ê��
int dsm_muv::Talarm::get_event(std::vector<int> input_vec)
{
	float result_rate = 0;
	float rate_event = 0;
	float sum_alarm = 0;

	for (size_t i = 0; i < input_vec.size(); i++)
	{
		if (input_vec[i] == 1)
		{
			sum_alarm++;
		}
	}
	//�˴������¼�λ�õ��ж�
	rate_event = sum_alarm / float(input_vec.size());

	result_rate = rate_event * 100;
	return result_rate; //����Ƶ��    ������Ҫ����λ���жϵ�ģʽ�����Բ������ֲ�ͬģʽ  ������101��111��100ģʽ��  
}

//Ƶ�κ�λ��ê���ʹ���ģʽ
int dsm_muv::Talarm::get_event_mode(std::vector<int> input_vec, bool frame)
{
	float result_rate = 0;
	float rate_event = 0;
	float sum_alarm = 0;

	for (size_t i = 0; i < input_vec.size(); i++)
	{
		if (input_vec[i] == 1)
		{
			sum_alarm++;
		}
	}
	//�˴������¼�λ�õ��ж�
	rate_event = sum_alarm / float(input_vec.size());

	result_rate = rate_event * 100;
	return result_rate; //����Ƶ��    ������Ҫ����λ���жϵ�ģʽ�����Բ������ֲ�ͬģʽ  ������101��111��100ģʽ��  
}
