#include "Calibration.h"


int dsm_muv::camera_calibration(cv::Mat frame, cv::Mat map1, cv::Mat map2, cv::Mat &back_frame)
{
	cv::Mat reframe;
	if (frame.empty()) return -6;
	resize(frame, reframe, cv::Size(1280, 720), 0, 0, cv::INTER_LINEAR);
	//resize(frame, reframe, Size(1920, 1080), 0, 0, INTER_LINEAR);
	remap(reframe, back_frame, map1, map2, cv::INTER_LINEAR);
	return 0;
}
 
int dsm_muv::init_calibration(cv::Mat &map1, cv::Mat &map2)
{
	//720P
	cv::Mat cameraMatrix = cv::Mat::eye(3, 3, CV_64F);
	cameraMatrix.at<double>(0, 0) = 776.98264537906834;
	cameraMatrix.at<double>(0, 1) = 0;
	cameraMatrix.at<double>(0, 2) = 615.48702545700746;
	cameraMatrix.at<double>(1, 1) = 754.32961104401636;
	cameraMatrix.at<double>(1, 2) = 352.65919608583550;
	cv::Mat distCoeffs = cv::Mat::zeros(5, 1, CV_64F); 
	distCoeffs.at<double>(0, 0) = -0.62807320589721882;
	distCoeffs.at<double>(1, 0) = 0.36681398886788141;
	distCoeffs.at<double>(2, 0) = 0.0032331524568823362;
	distCoeffs.at<double>(3, 0) = 0.0033622569807113573;
	distCoeffs.at<double>(4, 0) = -0.082920210764092617;

	cv::Size image_Size = cv::Size(1280, 720);
	initUndistortRectifyMap(cameraMatrix, distCoeffs, cv::Mat(), cameraMatrix, image_Size, CV_16SC2, map1, map2);
	return 0;
}