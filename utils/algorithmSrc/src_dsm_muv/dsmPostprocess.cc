// Copyright (c) 2021 by Rockchip Electronics Co., Ltd. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
 
#include <iostream>
#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include <string>
#include <sys/time.h>
#include <vector>
#include <set>
#include "dsmPostprocess.h"
#include <stdint.h>

#include "dsm_ini.h"
// std::string namei = dsm_muv::ini::Inputpth::getSharedValue2();
// const char* charstransit = namei.c_str();
#define LABEL_NALE_TXT_PATH "./model/mvu_resnet18c6.txt"



static char *labels[OBJ_CLASS_NUM];
 
//这是之前yolov5的anchor
// const int anchor0[6] = {10, 13, 16, 30, 33, 23};
// const int anchor1[6] = {30, 61, 62, 45, 59, 119};
// const int anchor2[6] = {116, 90, 156, 198, 373, 326};
 
//这是yolov5-face的anchor.
const int anchor0[6] = {4, 5, 8, 10, 13, 16};
const int anchor1[6] = {23, 29, 43, 55, 73, 105};
const int anchor2[6] = {146, 217, 231, 300, 335, 433};
 
inline static int clamp(float val, int min, int max)
{
    return val > min ? (val < max ? val : max) : min;
}
 
char *readLine(FILE *fp, char *buffer, int *len)
{
    int ch;
    int i = 0;
    size_t buff_len = 0;
 
    buffer = (char *)malloc(buff_len + 1);
    if (!buffer)
        return NULL; // Out of memory
 
    while ((ch = fgetc(fp)) != '\n' && ch != EOF)
    {
        buff_len++;
        void *tmp = realloc(buffer, buff_len + 1);
        if (tmp == NULL)
        {
            free(buffer);
            return NULL; // Out of memory
        }
        buffer = (char *)tmp;
 
        buffer[i] = (char)ch;
        i++;
    }
    buffer[i] = '\0';
 
    *len = buff_len;
 
    // Detect end
    if (ch == EOF && (i == 0 || ferror(fp)))
    {
        free(buffer);
        return NULL;
    }
    return buffer;
}
 
int readLines(const char *fileName, char *lines[], int max_line)
{
    FILE *file = fopen(fileName, "r");
    char *s;
    int i = 0;
    int n = 0;
    while ((s = readLine(file, s, &n)) != NULL)
    {
        lines[i++] = s;
        if (i >= max_line)
            break;
    }
    return i;
}
 
int loadLabelName(const char *locationFilename, char *label[], int classNum)
{
    printf("loadLabelName %s\n", locationFilename);
    readLines(locationFilename, label, classNum);
    return 0;
}
 
static float CalculateOverlap(float xmin0, float ymin0, float xmax0, float ymax0, float xmin1, float ymin1, float xmax1, float ymax1)
{
    float w = fmax(0.f, fmin(xmax0, xmax1) - fmax(xmin0, xmin1) + 1.0);
    float h = fmax(0.f, fmin(ymax0, ymax1) - fmax(ymin0, ymin1) + 1.0);
    float i = w * h;
    float u = (xmax0 - xmin0 + 1.0) * (ymax0 - ymin0 + 1.0) + (xmax1 - xmin1 + 1.0) * (ymax1 - ymin1 + 1.0) - i;
    return u <= 0.f ? 0.f : (i / u);
}
 
static int nms(int validCount, std::vector<float> &outputLocations, std::vector<int> classIds, std::vector<int> &order, int filterId, float threshold)
{
    for (int i = 0; i < validCount; ++i)
    {
        if (order[i] == -1 || classIds[i] != filterId)
        {
            continue;
        }
        int n = order[i];
        for (int j = i + 1; j < validCount; ++j)
        {
            int m = order[j];
            if (m == -1 || classIds[i] != filterId)
            {
                continue;
            }
            float xmin0 = outputLocations[n * 4 + 0];
            float ymin0 = outputLocations[n * 4 + 1];
            float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
            float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];
 
            float xmin1 = outputLocations[m * 4 + 0];
            float ymin1 = outputLocations[m * 4 + 1];
            float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
            float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];
 
            float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);
 
            if (iou > threshold)
            {
                order[j] = -1;
            }
        }
    }
    return 0;
}
 
static int quick_sort_indice_inverse(
    std::vector<float> &input,
    int left,
    int right,
    std::vector<int> &indices)
{
    float key;
    int key_index;
    int low = left;
    int high = right;
    if (left < right)
    {
        key_index = indices[left];
        key = input[left];
        while (low < high)
        {
            while (low < high && input[high] <= key)
            {
                high--;
            }
            input[low] = input[high];
            indices[low] = indices[high];
            while (low < high && input[low] >= key)
            {
                low++;
            }
            input[high] = input[low];
            indices[high] = indices[low];
        }
        input[low] = key;
        indices[low] = key_index;
        quick_sort_indice_inverse(input, left, low - 1, indices);
        quick_sort_indice_inverse(input, low + 1, right, indices);
    }
    return low;
}
 
static float sigmoid(float x)
{
    return 1.0 / (1.0 + expf(-x));
}
 
static float unsigmoid(float y)
{
    return -1.0 * logf((1.0 / y) - 1.0);
}
 
inline static int32_t __clip(float val, float min, float max)
{
    float f = val <= min ? min : (val >= max ? max : val);
    return f;
}
 
static uint8_t qnt_f32_to_affine(float f32, uint32_t zp, float scale)
{
    float dst_val = (f32 / scale) + zp;
    uint8_t res = (uint8_t)__clip(dst_val, 0, 255);
    return res;
}
 
static float deqnt_affine_to_f32(uint8_t qnt, uint32_t zp, float scale)
{
    return ((float)qnt - (float)zp) * scale;
}
 
 
static int process(uint8_t *input, int *anchor, int grid_h, int grid_w, int height, int width, int stride,
                   std::vector<float> &boxes, std::vector<float> &landMarks, std::vector<float> &objProbs, std::vector<int> &classId, 
                   float threshold, uint32_t zp, float scale)
{
 
    int validCount = 0;
    int grid_len = grid_h * grid_w;//80*80, 40*40, 20*20.
    float thres = unsigmoid(threshold);
    uint8_t thres_u8 = qnt_f32_to_affine(thres, zp, scale);//量化
    for (int a = 0; a < 3; a++)
    {
        for (int i = 0; i < grid_h; i++)
        {
            for (int j = 0; j < grid_w; j++)
            {
                /**********************************************************************************************
                排列顺序是x y w h boxScore x1 y1 x2 y2 x3 y3 x4 y4 x5 y5 idScore，然后保存的时候是先存
                grid_h * grid_w个x，再存grid_h * grid_w个y,....,这样保存的。
                而不是先存第一个x y w h boxScore x1 y1  x2 y2 x3 y3 x4 y4 x5 y5 idScore，
                再存第二个x y w h boxScore x1 y1  x2 y2 x3 y3 x4 y4 x5 y5 idScore，
                **********************************************************************************************/
                uint8_t box_confidence = input[(PROP_BOX_SIZE * a + 4) * grid_len + i * grid_w + j];
                if (box_confidence >= thres_u8)//thres_u8经过反sigmoid和反量化操作了。
                {
                    int offset = (PROP_BOX_SIZE * a) * grid_len + i * grid_w + j;//
                    uint8_t *in_ptr = input + offset; 
                    /*这边乘以2，减掉0.5是因为内部运算的时候做了这个处理，所以这里要反操作一下,
                    deqnt_affine_to_f32是反量化，zp是量化时的zero point中心点，scale是量化时的尺度。*/
                    float box_x = sigmoid(deqnt_affine_to_f32(*in_ptr, zp, scale)) * 2.0 - 0.5;
                    float box_y = sigmoid(deqnt_affine_to_f32(in_ptr[grid_len], zp, scale)) * 2.0 - 0.5;
                    float box_w = sigmoid(deqnt_affine_to_f32(in_ptr[2 * grid_len], zp, scale)) * 2.0;
                    float box_h = sigmoid(deqnt_affine_to_f32(in_ptr[3 * grid_len], zp, scale)) * 2.0;
                    
                    float x1 = (deqnt_affine_to_f32(in_ptr[5 * grid_len], zp, scale));
                    float y1 = (deqnt_affine_to_f32(in_ptr[6 * grid_len], zp, scale));
                    float x2 = (deqnt_affine_to_f32(in_ptr[7 * grid_len], zp, scale));
                    float y2 = (deqnt_affine_to_f32(in_ptr[8 * grid_len], zp, scale));
                    float x3 = (deqnt_affine_to_f32(in_ptr[9 * grid_len], zp, scale));
                    float y3 = (deqnt_affine_to_f32(in_ptr[10 * grid_len], zp, scale));
                    float x4 = (deqnt_affine_to_f32(in_ptr[11 * grid_len], zp, scale));
                    float y4 = (deqnt_affine_to_f32(in_ptr[12 * grid_len], zp, scale));
                    float x5 = (deqnt_affine_to_f32(in_ptr[13 * grid_len], zp, scale));
                    float y5 = (deqnt_affine_to_f32(in_ptr[14 * grid_len], zp, scale));
                    //std::cout<<"landmark after deqnt_affine_to_f32:"<<std::endl;
                    //std::cout<<"x1:"<<x1<<"  y1:"<<y1<<std::endl;
                    //std::cout<<"x2:"<<x2<<"  y2:"<<y2<<std::endl;
                    //std::cout<<"x3:"<<x3<<"  y3:"<<y3<<std::endl;
                    //std::cout<<"x4:"<<x4<<"  y4:"<<y4<<std::endl;
                    //std::cout<<"x5:"<<x5<<"  y5:"<<y5<<std::endl;
 
                    box_x = (box_x + j) * (float)stride;//这边的box_x是0-1的，相当于是偏移值，所以要加上j。乘以stride是映射回原图640.
                    box_y = (box_y + i) * (float)stride;//这边的box_y是0-1的，相当于是偏移值，所以要加上i。乘以stride是映射回原图640.
                    box_w = box_w * box_w * (float)anchor[a * 2];
                    box_h = box_h * box_h * (float)anchor[a * 2 + 1];
                    box_x -= (box_w / 2.0);
                    box_y -= (box_h / 2.0);
                    boxes.push_back(box_x);
                    boxes.push_back(box_y);
                    boxes.push_back(box_w);
                    boxes.push_back(box_h);
               
                   x1 = x1 * (float)anchor[a * 2]     + j*(float)stride;
                   y1 = y1 * (float)anchor[a * 2 + 1] + i*(float)stride;
                   x2 = x2 * (float)anchor[a * 2]     + j*(float)stride;
                   y2 = y2 * (float)anchor[a * 2 + 1] + i*(float)stride;
                   x3 = x3 * (float)anchor[a * 2]     + j*(float)stride;
                   y3 = y3 * (float)anchor[a * 2 + 1] + i*(float)stride;
                   x4 = x4 * (float)anchor[a * 2]     + j*(float)stride;
                   y4 = y4 * (float)anchor[a * 2 + 1] + i*(float)stride;
                   x5 = x5 * (float)anchor[a * 2]     + j*(float)stride;
                   y5 = y5 * (float)anchor[a * 2 + 1] + i*(float)stride;
 
                   landMarks.push_back(x1);
                   landMarks.push_back(y1);
                   landMarks.push_back(x2);
                   landMarks.push_back(y2);
                   landMarks.push_back(x3);
                   landMarks.push_back(y3);
                   landMarks.push_back(x4);
                   landMarks.push_back(y4);
                   landMarks.push_back(x5);
                   landMarks.push_back(y5);
                   
 
                    //  printf("box_x=%.03f, box_y=%.03f, box_w=%.03f, box_h=%.03f\n"
                    //  , box_x, box_y, box_w, box_h);
 
                    uint8_t maxClassProbs = in_ptr[15 * grid_len];
                    int maxClassId = 0;
                    
                    for (int k = 0; k < OBJ_CLASS_NUM; ++k)
                    {
                        uint8_t prob = in_ptr[(15 + k) * grid_len];//这里是通过比较找到一个最高的得分和他的id。
                        if (prob > maxClassProbs)
                        {
                            maxClassId = k;
                            maxClassProbs = prob;
                        }
                    }
                    
                    objProbs.push_back(sigmoid(deqnt_affine_to_f32(box_confidence, zp, scale)));  // maxClassProbs
                    classId.push_back(maxClassId);
                    
 
                    //boxes   objProbs  classId这三个变量往外返回的时候应该是每四个box对应一个objProb和一个classId。
                    //std::cout<<"maxClassProbs in post_process:::"<<sigmoid(deqnt_affine_to_f32(maxClassProbs, zp, scale))<<std::endl;
                    //std::cout<<"maxClassId in post_process::"<<maxClassId<<std::endl;
                    validCount++;
 
                }
            }
        }
    }
    return validCount;
}
 
int post_process(uint8_t *input0, uint8_t *input1, uint8_t *input2, int model_in_h, int model_in_w,
                 float conf_threshold, float nms_threshold, float scale_w, float scale_h,
                 std::vector<uint32_t> &qnt_zps, std::vector<float> &qnt_scales,
                 detect_result_group_t *group)
{
    static int init = -1;
    if (init == -1)
    {
        int ret = 0;
        // std::cout<<"_____________________LABEL_NALE_TXT_PATH:  "<<charstransit<<std::endl;
        ret = loadLabelName(LABEL_NALE_TXT_PATH, labels, OBJ_CLASS_NUM);
        if (ret < 0)
        {
            return -1;
        }
 
        init = 0;
    }
    memset(group, 0, sizeof(detect_result_group_t));
 
    std::vector<float> filterBoxes;
    std::vector<float> landMarks;
    std::vector<float> objProbs;
    std::vector<int> classId;

/*
    printf("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");
    for (int t = 0; t < 10; t++)
    {
        float cc = deqnt_affine_to_f32(input0[t],  qnt_zps[0], qnt_scales[0]);
        printf("%f\n", cc);
    }
    printf("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$\n");
*/
printf("1\n");
    // stride 8
    int stride0 = 8;
    int grid_h0 = (float)model_in_h / stride0;//80*80
    int grid_w0 = (float)model_in_w / stride0;
    int validCount0 = 0;
    validCount0 = process(input0, (int *)anchor0, grid_h0, grid_w0, model_in_h, model_in_w,
                          stride0, filterBoxes, landMarks, objProbs, classId, conf_threshold, qnt_zps[0], qnt_scales[0]);
  printf("2\n");  
    // stride 16
    int stride1 = 16;
    int grid_h1 = (float)model_in_h / stride1;//40*40
    int grid_w1 = (float)model_in_w / stride1;
    int validCount1 = 0;
    validCount1 = process(input1, (int *)anchor1, grid_h1, grid_w1, model_in_h, model_in_w,
                          stride1, filterBoxes, landMarks, objProbs, classId, conf_threshold, qnt_zps[1], qnt_scales[1]);
 printf("3\n");
    // stride 32
    int stride2 = 32;
    int grid_h2 = (float)model_in_h / stride2;//20*20
    int grid_w2 = (float)model_in_w / stride2;
    int validCount2 = 0;
    validCount2 = process(input2, (int *)anchor2, grid_h2, grid_w2, model_in_h, model_in_w,
                          stride2, filterBoxes, landMarks, objProbs, classId, conf_threshold, qnt_zps[2], qnt_scales[2]);
 printf("4\n");
    int validCount = validCount0 + validCount1 + validCount2;
    printf("validCount=%d\n", validCount);
    // no object detect
    if (validCount <= 0)
    {
        return 0;
    }
 
    std::vector<int> indexArray;
    for (int i = 0; i < validCount; ++i)
    {
        indexArray.push_back(i);//第i个是i，这是做什么用的。
    }
 printf("5\n");
    quick_sort_indice_inverse(objProbs, 0, validCount - 1, indexArray);//猜测这个函数是按照得分把index进行了排序。
 printf("6\n");
    std::set<int> class_set(std::begin(classId), std::end(classId));
 
    for (auto c : class_set)//c是int类型的。
    { 
        std::cout<<"c:::"<<c<<std::endl;
        //static int nms(int validCount, std::vector<float> &outputLocations, std::vector<int> classIds, std::vector<int> &order, int filterId, float threshold)
        nms(validCount, filterBoxes, classId, indexArray, c, nms_threshold);
    }
 printf("7\n");
    int last_count = 0;
    group->count = 0;
    /* box valid detect target */
    for (int i = 0; i < validCount; ++i)
    {
        if (indexArray[i] == -1 || i >= OBJ_NUMB_MAX_SIZE)
        {
            continue;
        }
        int n = indexArray[i];
 
        float x1 = filterBoxes[n * 4 + 0];
        float y1 = filterBoxes[n * 4 + 1];
        float x2 = x1 + filterBoxes[n * 4 + 2];
        float y2 = y1 + filterBoxes[n * 4 + 3];
        int id = classId[n];
        float obj_conf = objProbs[i];
 
        float landmark_x1 = landMarks[n * 10 + 0];
        float landmark_y1 = landMarks[n * 10 + 1];
        float landmark_x2 = landMarks[n * 10 + 2];
        float landmark_y2 = landMarks[n * 10 + 3];
        float landmark_x3 = landMarks[n * 10 + 4];
        float landmark_y3 = landMarks[n * 10 + 5];
        float landmark_x4 = landMarks[n * 10 + 6];
        float landmark_y4 = landMarks[n * 10 + 7];
        float landmark_x5 = landMarks[n * 10 + 8];
        float landmark_y5 = landMarks[n * 10 + 9];
 
 
        // group->results[last_count].box.left = (int)(clamp(x1, 0, model_in_w) / scale_w);
        // group->results[last_count].box.top = (int)(clamp(y1, 0, model_in_h) / scale_h);
        // group->results[last_count].box.right = (int)(clamp(x2, 0, model_in_w) / scale_w);
        // group->results[last_count].box.bottom = (int)(clamp(y2, 0, model_in_h) / scale_h);
 
        group->results[last_count].box.left = (int)(clamp(x1, 0, model_in_w) );//clamp处理是否越界的。
        group->results[last_count].box.top = (int)(clamp(y1, 0, model_in_h) );
        group->results[last_count].box.right = (int)(clamp(x2, 0, model_in_w) );
        group->results[last_count].box.bottom = (int)(clamp(y2, 0, model_in_h) );
        
        group->results[last_count].landmark.push_back((int)(clamp(landmark_x1, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y1, 0, model_in_h)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_x2, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y2, 0, model_in_h)));       
        group->results[last_count].landmark.push_back((int)(clamp(landmark_x3, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y3, 0, model_in_h)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_x4, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y4, 0, model_in_h)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_x5, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y5, 0, model_in_h)));
 
        group->results[last_count].prop = obj_conf;
        char *label = labels[id];
        strncpy(group->results[last_count].name, label, OBJ_NAME_MAX_SIZE);
 
        // printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left, group->results[last_count].box.top,
        //        group->results[last_count].box.right, group->results[last_count].box.bottom, label);
        last_count++;
    }
    group->count = last_count;
 printf("8\n");
    return 0;
}


static int nms_fp(int validCount, std::vector<float>& outputLocations, std::vector<int>& order, float threshold)
{
    for (int i = 0; i < validCount; ++i)
    {
        if (order[i] == -1)
        {
            continue;
        }
        int n = order[i];
        for (int j = i + 1; j < validCount; ++j)
        {
            int m = order[j];
            if (m == -1)
            {
                continue;
            }
            float xmin0 = outputLocations[n * 4 + 0];
            float ymin0 = outputLocations[n * 4 + 1];
            float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
            float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

            float xmin1 = outputLocations[m * 4 + 0];
            float ymin1 = outputLocations[m * 4 + 1];
            float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
            float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

            float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);

            if (iou > threshold)
            {
                order[j] = -1;
            }
        }
    }
    return 0;
}



static int process_fp(float* input, int* anchor, int grid_h, int grid_w, int height, int width, int stride,
    std::vector<float>& boxes, std::vector<float> &landMarks, std::vector<float>& boxScores, std::vector<int>& classId,
    float threshold)
{

    int validCount = 0;
    int grid_len = grid_h * grid_w;
    float thres_sigmoid = unsigmoid(threshold);
    for (int a = 0; a < 3; a++)
    {
        for (int i = 0; i < grid_h; i++)
        {
            for (int j = 0; j < grid_w; j++)
            {
                /**********************************************************************************************
                排列顺序是x y w h boxScore x1 y1 x2 y2 x3 y3 x4 y4 x5 y5 idScore，然后保存的时候是先存
                grid_h * grid_w个x，再存grid_h * grid_w个y,....,这样保存的。
                而不是先存第一个x y w h boxScore x1 y1  x2 y2 x3 y3 x4 y4 x5 y5 idScore，
                再存第二个x y w h boxScore x1 y1  x2 y2 x3 y3 x4 y4 x5 y5 idScore，
                **********************************************************************************************/
                float box_confidence = input[(PROP_BOX_SIZE * a + 4) * grid_len + i * grid_w + j];
                if (box_confidence >= thres_sigmoid)
                {
                    int offset = (PROP_BOX_SIZE * a) * grid_len + i * grid_w + j;
                    float* in_ptr = input + offset;
                    float box_x = sigmoid(*in_ptr) * 2.0 - 0.5;
                    float box_y = sigmoid(in_ptr[grid_len]) * 2.0 - 0.5;
                    float box_w = sigmoid(in_ptr[2 * grid_len]) * 2.0;
                    float box_h = sigmoid(in_ptr[3 * grid_len]) * 2.0;

                    float x1 = in_ptr[5 * grid_len];
                    float y1 = in_ptr[6 * grid_len];
                    float x2 = in_ptr[7 * grid_len];
                    float y2 = in_ptr[8 * grid_len];
                    float x3 = in_ptr[9 * grid_len];
                    float y3 = in_ptr[10 * grid_len];
                    float x4 = in_ptr[11 * grid_len];
                    float y4 = in_ptr[12 * grid_len];
                    float x5 = in_ptr[13 * grid_len];
                    float y5 = in_ptr[14 * grid_len];


                    box_x = (box_x + j) * (float)stride;
                    box_y = (box_y + i) * (float)stride;
                    box_w = box_w * box_w * (float)anchor[a * 2];
                    box_h = box_h * box_h * (float)anchor[a * 2 + 1];
                    box_x -= (box_w / 2.0);
                    box_y -= (box_h / 2.0);
                    boxes.push_back(box_x);
                    boxes.push_back(box_y);
                    boxes.push_back(box_w);
                    boxes.push_back(box_h);


                   x1 = x1 * (float)anchor[a * 2]     + j*(float)stride;
                   y1 = y1 * (float)anchor[a * 2 + 1] + i*(float)stride;
                   x2 = x2 * (float)anchor[a * 2]     + j*(float)stride;
                   y2 = y2 * (float)anchor[a * 2 + 1] + i*(float)stride;
                   x3 = x3 * (float)anchor[a * 2]     + j*(float)stride;
                   y3 = y3 * (float)anchor[a * 2 + 1] + i*(float)stride;
                   x4 = x4 * (float)anchor[a * 2]     + j*(float)stride;
                   y4 = y4 * (float)anchor[a * 2 + 1] + i*(float)stride;
                   x5 = x5 * (float)anchor[a * 2]     + j*(float)stride;
                   y5 = y5 * (float)anchor[a * 2 + 1] + i*(float)stride;
 
                   landMarks.push_back(x1);
                   landMarks.push_back(y1);
                   landMarks.push_back(x2);
                   landMarks.push_back(y2);
                   landMarks.push_back(x3);
                   landMarks.push_back(y3);
                   landMarks.push_back(x4);
                   landMarks.push_back(y4);
                   landMarks.push_back(x5);
                   landMarks.push_back(y5);


                    float maxClassProbs = in_ptr[15 * grid_len];
                    int maxClassId = 0;
                    for (int k = 0; k < OBJ_CLASS_NUM; ++k)
                    {
                        float prob = in_ptr[(15 + k) * grid_len];
                        if (prob > maxClassProbs)
                        {
                            maxClassId = k;
                            maxClassProbs = prob;
                        }
                    }
                    float box_conf_f32 = sigmoid(box_confidence);
                    float class_prob_f32 = sigmoid(maxClassProbs);

                    //printf("box_conf_f32 = %f, class_prob_f32 = %f\n", box_conf_f32, class_prob_f32);
                    //boxScores.push_back(box_conf_f32 * class_prob_f32);
                    //boxScores.push_back(class_prob_f32);
                    boxScores.push_back(box_conf_f32);

                    classId.push_back(maxClassId);
                    validCount++;
                }
            }
        }
    }
    return validCount;
}

int post_process_fp(float *input0, float *input1, float *input2, int model_in_h, int model_in_w,
                 float conf_threshold, float nms_threshold,
                 detect_result_group_t *group)
{
    static int init = -1;
    if (init == -1)
    {
        int ret = 0;
        ret = loadLabelName(LABEL_NALE_TXT_PATH, labels, OBJ_CLASS_NUM);
        if (ret < 0)
        {
            return -1;
        }

        init = 0;
    }
    memset(group, 0, sizeof(detect_result_group_t));

    std::vector<float> filterBoxes;
    std::vector<float> landMarks;
    std::vector<float> boxesScore;
    std::vector<int> classId;

    //stride 8
    int stride0 = 8;
    int grid_h0 = (float)model_in_h / stride0; //80*80
    int grid_w0 = (float)model_in_w / stride0;
    int validCount0 = 0;
    validCount0 = process_fp(input0, (int*)anchor0, grid_h0, grid_w0, model_in_h, model_in_w,
        stride0, filterBoxes, landMarks, boxesScore, classId, conf_threshold);

    //stride 16
    int stride1 = 16;
    int grid_h1 = (float)model_in_h / stride1; //40*40
    int grid_w1 = (float)model_in_w / stride1;
    int validCount1 = 0;
    validCount1 = process_fp(input1, (int*)anchor1, grid_h1, grid_w1, model_in_h, model_in_w,
        stride1, filterBoxes, landMarks, boxesScore, classId, conf_threshold);

    //stride 32
    int stride2 = 32;
    int grid_h2 = (float)model_in_h / stride2; //20*20
    int grid_w2 = (float)model_in_w / stride2;
    int validCount2 = 0;
    validCount2 = process_fp(input2, (int*)anchor2, grid_h2, grid_w2, model_in_h, model_in_w,
        stride2, filterBoxes, landMarks, boxesScore, classId, conf_threshold);

    int validCount = validCount0 + validCount1 + validCount2;
    // no object detect
    if (validCount <= 0)
    {
        return 0;
    }

    std::vector<int> indexArray;
    for (int i = 0; i < validCount; ++i)
    {
        indexArray.push_back(i);
    }

    quick_sort_indice_inverse(boxesScore, 0, validCount - 1, indexArray);

    nms_fp(validCount, filterBoxes, indexArray, nms_threshold);

    int last_count = 0;
    group->count = 0;
    /* box valid detect target */
    for (int i = 0; i < validCount; ++i)
    {

        if (indexArray[i] == -1 || boxesScore[i] < conf_threshold || last_count >= OBJ_NUMB_MAX_SIZE)
        {
            continue;
        }
        int n = indexArray[i];

        float x1 = filterBoxes[n * 4 + 0];
        float y1 = filterBoxes[n * 4 + 1];
        float x2 = x1 + filterBoxes[n * 4 + 2];
        float y2 = y1 + filterBoxes[n * 4 + 3];
        int id = classId[n];


        float landmark_x1 = landMarks[n * 10 + 0];
        float landmark_y1 = landMarks[n * 10 + 1];
        float landmark_x2 = landMarks[n * 10 + 2];
        float landmark_y2 = landMarks[n * 10 + 3];
        float landmark_x3 = landMarks[n * 10 + 4];
        float landmark_y3 = landMarks[n * 10 + 5];
        float landmark_x4 = landMarks[n * 10 + 6];
        float landmark_y4 = landMarks[n * 10 + 7];
        float landmark_x5 = landMarks[n * 10 + 8];
        float landmark_y5 = landMarks[n * 10 + 9];

        group->results[last_count].box.left = (int)(clamp(x1, 0, model_in_w) );//clamp处理是否越界的。
        group->results[last_count].box.top = (int)(clamp(y1, 0, model_in_h) );
        group->results[last_count].box.right = (int)(clamp(x2, 0, model_in_w) );
        group->results[last_count].box.bottom = (int)(clamp(y2, 0, model_in_h) );


        group->results[last_count].landmark.push_back((int)(clamp(landmark_x1, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y1, 0, model_in_h)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_x2, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y2, 0, model_in_h)));       
        group->results[last_count].landmark.push_back((int)(clamp(landmark_x3, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y3, 0, model_in_h)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_x4, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y4, 0, model_in_h)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_x5, 0, model_in_w)));
        group->results[last_count].landmark.push_back((int)(clamp(landmark_y5, 0, model_in_h)));

        group->results[last_count].prop = boxesScore[i];
        //group->results[last_count].id = id;
        char* label = labels[id];
        strncpy(group->results[last_count].name, label, OBJ_NAME_MAX_SIZE);

        // printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left, group->results[last_count].box.top,
        //        group->results[last_count].box.right, group->results[last_count].box.bottom, label);
        last_count++;
    }
    group->count = last_count;

    return 0;
}
 
 
int rknn_GetTop(
    float *pfProb,
    float *pfMaxProb,
    uint32_t *pMaxClass,
    uint32_t outputCount,
    uint32_t topNum)
{
    uint32_t i, j;
 
#define MAX_TOP_NUM 20
    if (topNum > MAX_TOP_NUM)
        return 0;
 
    memset(pfMaxProb, 0, sizeof(float) * topNum);
    memset(pMaxClass, 0xff, sizeof(float) * topNum);
 
    for (j = 0; j < topNum; j++)
    {
        for (i = 0; i < outputCount; i++)
        {
            if ((i == *(pMaxClass + 0)) || (i == *(pMaxClass + 1)) || (i == *(pMaxClass + 2)) ||
                (i == *(pMaxClass + 3)) || (i == *(pMaxClass + 4)))
            {
                continue;
            }
 
            if (pfProb[i] > *(pfMaxProb + j))
            {
                *(pfMaxProb + j) = pfProb[i];
                *(pMaxClass + j) = i;
            }
        }
    }
 
    return 1;
}
