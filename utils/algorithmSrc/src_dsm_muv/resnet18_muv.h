#ifndef RESNET18_MUV_H_
#define RESNET18_MUV_H_

#include "run.h"
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include "dsmShareDefine.h"
// #include "resnet18_muv.h"


namespace dsm_muv {
 
 


    class Resnet {
    public:
        static std::vector<std::string> readClassNames();

//	static std::vector<cv::Mat> pro_resize(std::vector<cv::Mat> input_vec);
        static void PreProcess(const cv::Mat &image, cv::Mat &image_blob);
        static int glasses_detect(cv::Mat eye, std::vector<float> angle_vec);
        static std::vector<float> softmaxmuv(const cv::Mat& output);
#ifdef DSM_USING_RKNN
        static int resnet_recog(dsm_muv::face_message face_info, std::vector<int>& result_map, int Muv_speed, dsm_muv::ExternalParam exterIOparam);
#else
        static int resnet_eye(dnn::Net net, std::vector<cv::Mat> input_img, cv::Mat img_face, std::vector<int> landmarks);
        static int resnet_mouth(dnn::Net net, cv::Mat input_img, cv::Mat img_face, std::vector<int> landmarks);
        static int resnet_respirator(dnn::Net net, cv::Mat input_img, cv::Mat img_face, std::vector<int> landmarks);
#endif


    private:
        cv::Size input_size;
#ifdef USING_RKNN
#else
        cv::dnn::Net net;
#endif
        cv::Scalar default_mean;
        cv::Scalar default_std;
        std::vector<cv::String> labels;


    };


    class Recong {
    public:
        static int check_cream(cv::Mat image, bool &state_camover);
        static bool isImageBlurry(cv::Mat &img, double threshold = 42000);
    };

}
#endif // RESNET18_MUV_H_
