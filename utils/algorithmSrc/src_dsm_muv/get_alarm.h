#ifndef GET_ALARM_H
#define GET_ALARM_H
#include <string>
#include <opencv2/opencv.hpp>
#include "run.h"

namespace dsm_muv {
	class Talarm
	{
	public:
		static int get_event(std::vector<int> input_vec);
		static int get_event_mode(std::vector<int> input_vec, bool frame);
		static int early_termination(std::vector<int> input_vec, int frame_sum);	
		//const Rect mouth_roi ;
		/*static void alarm_Monitor(int printLimit, int state_alarm, FunctionTimer& functionTimer);
		static int alarm_box(std::vector<int> indata,std::vector<int> cooldown, std::vector<FunctionTimer>& functioTimer, std::vector<std::thread>& threads, std::vector<Nodeflags>& outhrev, std::vector<int>& starttime);*/

	};
}
#endif  //GET_ALARM_H