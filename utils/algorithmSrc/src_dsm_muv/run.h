#ifndef RUN_H
#define RUN_H
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include <string>
#include <vector>
#include <future>
#include <numeric>
#include <unordered_map>
#include "transit.h"

// 配置参数
// 识别开关  1：眼睛识别开关   2：嘴巴识别开关    3：左顾右盼   4：离岗   5：遮挡摄像头  6：抽烟   7：打电话
// 事件时间  1：闭眼事件的固定时间  2：哈欠事件的固定事件
// 灵敏度    1：闭眼频次占比 2：哈欠频次占比
// 冷却事件  1：闭眼报警后冷却时间  2：哈欠报警后冷却时间

// extern cv::dnn::Net net;
namespace dsm_muv
{
    uint64_t getTimesmuv();

    //列出安全带所有可能的状态
    typedef enum
    {
            SAFE_BELT_OFF = 0, // 安全带未系
            SAFE_BELT_ON = 1,      // 安全带已系
            SAFE_BELT_ERROR= 2    // 安全带状态错误
    } SafeBeltStatus;

    struct ExternalParam
    {
            bool turnL;
            bool turnR;
            bool back_gear;
            SafeBeltStatus safe_belt;

            // 构造函数
            ExternalParam(bool left, bool right, bool backgear, SafeBeltStatus belt)
                : turnL(left), turnR(right),back_gear(backgear), safe_belt(belt) {}
    };

    struct globals
    {
        // ���۲���
        int eye_speed;
        int eye_button;
        int eye_mode;
        int eye_sensitivity;
        int event_eye_time;
        int freeze_eye_time;

        // ������β���
        int lookaround_speed;
        int lookaround_button;
        int lookaround_mode;
        int lookaround_sensitivity;
        int event_lookaround_time;
        int freeze_lookaround_time;

        // �ڵ�����
        int camcover_speed;
        int camcover_button;
        int camcover_mode;
        int camcover_sensitivity;
        int event_camcover_time;
        int freeze_camcover_time;

        // ���̲���
        int smoking_speed;
        int smoking_button;
        int smoking_mode;
        int smoking_sensitivity;
        int event_smoking_time;
        int freeze_smoking_time;

        // ��绰����
        int phone_speed;
        int phone_button;
        int phone_mode;
        int phone_sensitivity;
        int event_phone_time;
        int freeze_phone_time;

        // ͷ������
        int hat_speed;
        int hat_button;
        int hat_mode;
        int hat_sensitivity;
        int event_hat_time;
        int freeze_hat_time;

        // ��ȫ������
        int seatbelt_speed;
        int seatbelt_button;
        int seatbelt_mode;
        int seatbelt_sensitivity;
        int event_seatbelt_time;
        int freeze_seatbelt_time;

        // ä�������
        int Blindspot_speed;
        int Blindspot_button;
        int Blindspot_mode;
        int Blindspot_sensitivity;
        int event_Blindspot_time;
        int freeze_Blindspot_time;
    
        // ����������
        int detect_move_button;
        int move_pixel;
        int frame_early;
    };

    struct regions
    {
        // �ٶȲ���
        int speed;

        // ���ز���
        int button;

        // ģʽ����
        int mode;

        // �¼�����
        int sensitivity;

        // �¼�����
        int event_time;

        // ��ȴ����
        int freeze_time;
    };

    struct Nodeflags
    {
        int value;
        std::string notes; // start  run  end  bad
    };

    enum class Status
    {
        Begin,
        Running,
        End,
        Freeze,
        Worthless
    };

    class printLog
    {
    public:
        static void Write(std::string log)
        {
            std::ofstream ofs;
            //            time_t t = time(0);
            //            char tmp[64];
            // strftime(tmp, sizeof(tmp), "[%Y-%m-%d %X]", localtime(&t));
            std::string path = "/userdata/media/AlarmLog.log";
            ofs.open(path, std::ofstream::app);

            // ��ӡ��¼ʱ��
            // ofs << tmp << " - ";
            // ����ӡ�����ַ���  ����ӡʱ��
            ofs.write(log.c_str(), log.size());
            ofs << std::endl;
            ofs.close();
        }
    };
    // 定义一个基类，包含一个处理输入的成员函数
    class Alarm
    {
    public:
        virtual Status check(int input, cv::Point3i &timep3d) = 0;
    };

    // 定义四个派生类，每个类都有自己的处理逻辑
    class AlarmA : public Alarm
    {
    private:
        uint64_t recog_start = 0;
        uint64_t freeze_start = 0;

    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            // int timer1 = 0; //初始值 用来触发start状态
            // 开启冷却状态
            if (freeze_start > 0)
            {
                uint64_t freeze_t = getTimesmuv() - freeze_start;
                // std::cout << "  已经冷却时间:  " << freeze_t << std::endl;
                if (freeze_t < timep3d.z)
                {
                    // std::cout << "  冷却中:  " << timep3d.z << std::endl;
                    return Status::Freeze;
                }
                else
                {
                    freeze_start = 0;
                    // std::cout << ">>  >> >>冷却结束:  " << timep3d.z << std::endl;
                    return Status::Worthless;
                }
            }
            if (recog_start > 0)
            {
                uint64_t recog_t = getTimesmuv() - recog_start;

                if (recog_t > timep3d.y)
                {

                    // 识别时间到
                    recog_start = 0;
                    // 赋值给冷却状态开始
                    freeze_start = getTimesmuv();

                    // 异常超时需要清理地图
                    int timeout = recog_t - timep3d.y;
                    if (timeout > 3000) // 超时3秒 认为是异常超时，这里可能是车速变化切换的导致的等待超时
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_start == 0))
            {
                recog_start = getTimesmuv();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmB : public Alarm
    {
    private:
        uint64_t recog_start = 0;
        uint64_t freeze_start = 0;

    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            if (freeze_start > 0)
            {
                uint64_t freeze_t = getTimesmuv() - freeze_start;
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    freeze_start = 0;
                    return Status::Worthless;
                }
            }
            if (recog_start > 0)
            {
                uint64_t recog_t = getTimesmuv() - recog_start;
                if (recog_t > timep3d.y)
                {
                    recog_start = 0;
                    freeze_start = int(getTimesmuv());
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000)
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_start == 0))
            {
                recog_start = getTimesmuv();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmC : public Alarm
    {
    private:
        uint64_t recog_startc = 0;
        uint64_t freeze_startc = 0;

    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            if (freeze_startc > 0)
            {
                uint64_t freeze_t = getTimesmuv() - freeze_startc;
                if (freeze_t < timep3d.z)
                {
                    //  std::cout << "muv:=====================================================  ====== >> Testing  Freeze  " << getTimesmuv() << std::endl;
                    return Status::Freeze;
                }
                else
                {
                    freeze_startc = 0;
                    // std::cout << "muv:=====================================================  ====== >> Testing 冷却后和触发前位置 Worthless  " << getTimesmuv() << std::endl;
                    return Status::Worthless;
                }
            }
            if (recog_startc > 0)
            {
                uint64_t recog_t = getTimesmuv() - recog_startc;
                if (recog_t > timep3d.y)
                {
                    // ʶ��ʱ�䵽
                    recog_startc = 0;
                    //  std::cout << "muv:=====================================================  ====== >> Testing glb识别时间timep3d.y  " << timep3d.y << std::endl;
                    freeze_startc = getTimesmuv();
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000)
                    {
                        timep3d.x = 404;
                        std::cout << "muv:=====================================================  ====== >> Testing 超时位置 Worthless  " << timeout << std::endl;
                        return Status::Worthless;
                    }
                    else
                    {
                        // std::cout << "muv:=====================================================  ====== >> Testing 识别过程位置 recog_t  " << recog_t << std::endl;
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    // std::cout << "muv:=====================================================  ====== >> Testing 识别过程位置 Running  " << getTimesmuv() << std::endl;
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_startc == 0))
            {
                recog_startc = getTimesmuv();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmD : public Alarm
    {
    private:
        uint64_t recog_startd = 0;
        uint64_t freeze_startd = 0;

    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            if (freeze_startd > 0)
            {
                uint64_t freeze_t = getTimesmuv() - freeze_startd;
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    freeze_startd = 0;
                    return Status::Worthless;
                }
            }
            if (recog_startd > 0)
            {
                uint64_t recog_t = getTimesmuv() - recog_startd;
                if (recog_t > timep3d.y)
                {
                    // ʶ��ʱ�䵽
                    recog_startd = 0;
                    // ��ֵ����ȴ״̬��ʼ
                    freeze_startd = int(getTimesmuv());

                    // �쳣��ʱ��Ҫ������ͼ
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000) // ��ʱ3�� ��Ϊ���쳣��ʱ����������ǳ��ٱ仯�л��ĵ��µĵȴ���ʱ
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_startd == 0))
            {
                recog_startd = getTimesmuv();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmE : public Alarm
    {
    private:
        uint64_t recog_starte = 0;
        uint64_t freeze_starte = 0;

    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            if (freeze_starte > 0)
            {
                uint64_t freeze_t = getTimesmuv() - freeze_starte;
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    freeze_starte = 0;
                    return Status::Worthless;
                }
            }
            if (recog_starte > 0)
            {
                uint64_t recog_t = getTimesmuv() - recog_starte;
                if (recog_t > timep3d.y)
                {
                    // ʶ��ʱ�䵽
                    recog_starte = 0;
                    // ��ֵ����ȴ״̬��ʼ
                    freeze_starte = int(getTimesmuv());

                    // �쳣��ʱ��Ҫ������ͼ
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000) // ��ʱ3�� ��Ϊ���쳣��ʱ����������ǳ��ٱ仯�л��ĵ��µĵȴ���ʱ
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_starte == 0))
            {
                recog_starte = getTimesmuv();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmBox
    {
    public:
        AlarmBox(Alarm *a, Alarm *b, Alarm *c, Alarm *d, Alarm *e) : alarms_{a, b, c, d, e} {}

        std::vector<Status> alarm_box(std::vector<int> input, std::vector<cv::Point3i> &timep3d)
        {
            std::vector<std::thread> threads;
            std::vector<Status> statuses(5, Status::Running);

            for (std::size_t i = 0; i < alarms_.size(); ++i)
            {
                threads.push_back(std::thread([this, input, i, &statuses, &timep3d]()
                                              {
                                                  statuses[i] = alarms_[i]->check(input[i], timep3d[i]); // ����input��time
                                              }));
            }

            for (auto &thread : threads)
            {
                thread.join();
            }
            threads.clear();
            // for (size_t i = 0; i < 5; i++)
            // {
            //     std::cout <<std::endl<< "muv:==================================多线程输出结果[i]" << input[i]<< std::endl;
            // }
            return statuses;
            // ������...
        }

    private:
        std::vector<Alarm *> alarms_; // �洢Alarm�����ָ��
    };

    int risk_level(DSMInfo alarm_infos);
    void init_DSMInfo_parmas(DSMInfo almuv_info);
    bool check_params(regions &region);
    void get_global_params(std::string yaml_path, globals globalmuvs); // ǰ�ڵ���ʹ��
    bool LoadConfigFile(const std::string &file_name, globals &globalmuvs);
    std::string readConfig(std::string file_path, const std::string &key, std::string def);
    int readConfigInt(std::string file_path, const std::string &key, int def);
    bool readConfigBool(std::string file_path, const std::string &key, bool def);
    float readConfigFloat(std::string file_path, const std::string &key, float def);
    int getDistance(cv::Point point1, cv::Point point2);
    bool cmpy(cv::Point const &a, cv::Point const &b);
    // subfunction

    std::string getStatusName(dsm_muv::Status status);
    extern cv::Mat convertTo3Channels(const cv::Mat &binImg);

    class Kernel
    {
    public:
        static std::vector<dsm_muv::Nodeflags> execute_alarm_box(std::vector<int> input, dsm_muv::AlarmBox &box, std::vector<int> &timeout);
        // 公有成员变量，可以在类的外部直接访问和修改
        std::vector<std::vector<int>> superposition;
        // 构造函数
        Kernel() : superposition(5, std::vector<int>(0)) {}

        int run_detector(cv::Mat back_frame, dsm_muv::YOLO yolo_model, DSMInfo &almuv_info, AlarmBox &box, int muv_speed, dsm_muv::ExternalParam exterIOparam);
    };

    struct ad_angle
    {
        bool adjusted_button = false;
        float yaw_angle;
    };
    struct BELT_INFO
    {
        // 0:未标定 1:标定待确认 2:标定完成
        int belt_demarcate_state = 0;
        cv::Mat belt_frame;
        std::vector<cv::Point2i> five_point;
        int left_right_button;
    };

    struct ParamsToCheck
    {
        int yaw_angle;

        std::vector<cv::Point2i> five_points;
        int left_right_button;

        bool debug_button;
    };

    class SkewParam
    {
    public:
        static bool debug_button;
        static BELT_INFO belt_info;
        static ad_angle adjusted_angle;

        static void SetAngle(ad_angle ad_angle);

        static void SetBeltInfo(BELT_INFO belt_info);

        static BELT_INFO GetBeltInfo();

        static ad_angle GetAngle();
    };

    // bool Beltcalibration(cv::Mat frame);
    bool LoadDebugConfigFile(const std::string &file_name);
    bool check_debug_param(ParamsToCheck &params);
    void LoadDefaultParam(ParamsToCheck &params);
    void belt_point_deal(std::vector<cv::Point> &points, int left_right_button);
    static inline bool point_x_sort(cv::Point const &a, cv::Point const &b);
    static inline bool point_y_sort(cv::Point const &a, cv::Point const &b);

}
#endif