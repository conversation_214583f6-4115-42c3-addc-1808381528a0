
#ifndef CUT_FACEROI_H_
#define CUT_FACEROI_H_
 
#include <string>
#include <opencv2/opencv.hpp>
namespace dsm_muv {
	class Tcut
	{
	public:
		static cv::Mat cut_eye_L(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks);
		static cv::Mat cut_eye_R(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks);
		static cv::Mat cut_rotate_roi(cv::Mat input_img, cv::Rect center, float angle_vec);  //test
		static cv::Mat cut_smoke(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, std::vector<int> landmarks);
		static cv::Mat cut_belt(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec);
		static double compareMats(cv::Mat mat1, cv::Mat mat2);
	};
}


#endif // CUT_FACEROI_H_