﻿#include <iostream>
#include <string>
#include <opencv2/opencv.hpp>
#include "face_angle.h"
#include "cut_faceroi.h"
#include "run.h"

// 人脸朝向角度
std::vector<float> dsm_muv::Tface::face_angle(cv::Mat input_img, cv::Rect input_box, std::vector<int> landmarks)
{
	// 限定roi区域
	cv::Rect face_box = roi_invade(input_img, input_box);
	cv::Point origin;
	std::vector<float> result_vec;
	origin.x = face_box.x;
	origin.y = face_box.y + face_box.height;

	float angle_roll = Tface::get_roll_arctan(landmarks);					 // 横滚角
	float angle_pitch = Tface::get_pitch_arctan(landmarks);					 // 俯仰角
	float angle_yaw = Tface::get_yaw_arctan(landmarks, angle_pitch);		 // 偏航角

	float ratio = Tface::get_face_distance(input_img, input_box, landmarks); // 人脸距离

	ad_angle adjusted_angle = SkewParam::GetAngle();
	if (adjusted_angle.adjusted_button == true)
	{
		angle_yaw -= adjusted_angle.yaw_angle;
	}

	result_vec.clear();
	result_vec.push_back(angle_yaw);
	result_vec.push_back(angle_pitch);
	result_vec.push_back(angle_roll);
	result_vec.push_back(ratio);

	return result_vec;
}

// 求取斜率 水平直线的角度，为弧度 偏航角
float dsm_muv::Tface::get_yaw_arctan(std::vector<int> landmark, float angle_pitch)
{
	float k = 0; // 直线斜率
	float angle = 0;
	// vector <float> lines_arctan;//直线斜率的反正切值
	// for (unsigned int i = 0; i < lines.size(); i++)
	//{

	//	k = (double)(lines[i][3] - lines[i][1]) / (double)(lines[i][2] - lines[i][0]); //求出直线的斜率
	//	lines_arctan.push_back(atan(k));
	//}
	// return lines_arctan;

	float tandb =0;
	float tanlb =0;
	//眼睛和鼻子靠近的异常处理
	float Newtandb = float((landmark[7] + landmark[9]) * 0.5 - landmark[5]);

	int limit_piexl = landmark[5] - ((landmark[1] + landmark[3]) * 0.5);
	// std::cout<<"muv:-------------------------------------------------------  ``````````````````````````````` limit_piexl:::::     " <<limit_piexl << std::endl;
	if (limit_piexl < 15)
	{
		tandb = (float)(landmark[7]  - ((landmark[1] + landmark[3]) * 0.5));
		tanlb = (float)(landmark[4]  - ((landmark[0] + landmark[2]) * 0.5));
	}
	else
	{
		tandb = (float)(landmark[5] - ((landmark[1] + landmark[3]) * 0.5));
		tanlb = (float)(landmark[4] - ((landmark[0] + landmark[2]) * 0.5));
	}
	
	if (tanlb == 0)
	{
		return angle;
	}
	k = Newtandb / tanlb; //求出直线的斜率
	// k = tandb / tanlb; // 求出直线的斜率
	if (angle_pitch < -20)
	{
		k = tandb / tanlb;
	}
	// cout << "tandb为： " << tandb << endl;
	// cout << "tanlb为： " << tanlb << endl;
	// cout << "斜率为： " << k << endl;
	angle = 180 - ((atan(k) * 180) / 3.1415926);
	if (k < 0)
	{
		angle = (float)(-1 * (90 + (atan(k) * 57.29577)));
	}
	else
	{
		angle = (float)(90 - (atan(k) * 57.29577));
	}

	//当鼻子和眼睛的水平线接近
	// cout << "检测到的直线角度为：" << angle << endl;
	/*int 😀 = 0;*/
	// 抑制抖动变化过快
	if ((angle > -8) && (angle < 8))
	{
		angle = angle * 0.4;
	}
	else
	{
		angle = angle * 0.82;
	}
	std::cout << "muv:_______________角度______________角度______________角度________________角度____________检测到的角度为：" << angle << std::endl;
	return angle;
}

// 头部上下旋转：俯仰角
float dsm_muv::Tface::get_pitch_arctan(std::vector<int> landmark)
{
	// 检测获取人脸距离摄像头的距离，得到一个加权系数  用于人脸特征缩放

	float angle = 0;
	// 根据中庭和下庭的大小关系  判断俯仰角
	float mouth = (float(landmark[7]) + float(landmark[9])) / 2;
	float eye = (float(landmark[1]) + float(landmark[1])) / 2;

	// 三庭只用了两厅之比
	// float shangt = float(mouth - landmark[5]) / float(eye - face_box.y);
	// float zhonot = (float(landmark[5]) - eye) /  float(eye - face_box.y) ;
	// string tet = to_string(shangt);
	// string text = to_string(zhonot);
	// putText(input_img, tet, Point(landmark[2], landmark[3]), FONT_HERSHEY_COMPLEX, 0.6, Scalar(0, 0, 255), 1, 8, 0);
	// putText(input_img, text, Point(landmark[4], landmark[5]), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 0, 255), 1, 8, 0);

	/*如果鼻子高于眼睛y，让鼻子永远低于眼睛一个像素*/
	if (float(landmark[5] - eye < 0))
	{
		eye = landmark[5] - 2;
	}
	float xiat = float(mouth - landmark[5]) / float(landmark[5] - eye);
	angle = (float)((atan(xiat) * 57.29577) - 45);
	if (true)
	{
		angle = angle * 0.96;
	}
	// vector <float> lines_arctan;//直线斜率的反正切值
	// for (unsigned int i = 0; i < lines.size(); i++)
	//{
	//	k = (double)(lines[i][3] - lines[i][1]) / (double)(lines[i][2] - lines[i][0]); //求出直线的斜率
	//	lines_arctan.push_back(atan(k));
	// }
	// return lines_arctan;
	// cout << "检测到的直线角度为：" << txt << endl;
	//  取值范围
	// 1.0~1.2  俯仰角0
	//<1.0  俯角
	//>1.4  仰角
	return angle;
}

// 头部左右倾斜：横滚角  正常角度不大于10°
float dsm_muv::Tface::get_roll_arctan(std::vector<int> landmark)
{
	float k = 0; // 直线斜率
	float angle = 0;

	float tandb = (float)(landmark[3] - landmark[1]);
	float tanlb = (float)(landmark[2] - landmark[0]);
	if (tandb == 0)
	{
		return angle;
	}
	k = tandb / tanlb; // 求出直线的斜率
	// cout << "tandb为： " << tandb << endl;
	// cout << "tanlb为： " << tanlb << endl;
	// cout << "斜率为： " << k << endl;
	// angle = 180 - ((atan(k) * 180) / 3.1415926);
	angle = (float)(atan(k) * 57.29577);
	// cout << "检测到的左右倾斜的角度为：" << angle << endl;
	return angle;
}

// 识别人脸距离
float dsm_muv::Tface::get_face_distance(cv::Mat input_img, cv::Rect face_box, std::vector<int> landmark)
{
	// 检测获取人脸距离摄像头的距离，得到一个加权系数  用于人脸特征缩放

	float ratio = 0;
	// 根据中庭和下庭的大小关系  判断俯仰角
	float mouth = (float(landmark[7]) + float(landmark[9])) / 2;
	float eye = (float(landmark[1]) + float(landmark[1])) / 2;

	// 三庭只用了两厅之比
	// float shangt = float(mouth - landmark[5]) / float(eye - face_box.y);
	// float zhonot = (float(landmark[5]) - eye) /  float(eye - face_box.y) ;

	// float shangt = float(eye - face_box.y)/ float(face_box.height);//恒定不变

	// 人脸距离系数
	// float zhonot = float(input_img.cols) / float(eye - face_box.y);
	// string text =  to_string(zhonot);
	// putText(input_img, text, Point(landmark[4], landmark[5]), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 0, 255), 1, 8, 0);

	float shangt = float(face_box.height) / float(input_img.cols);
	std::string tet = "distance: " + std::to_string(int(shangt * 100));
	// putText(input_img, tet, Point(face_box.x, face_box.y + face_box.height + 10), FONT_HERSHEY_COMPLEX, 0.6, Scalar(0, 0, 255), 1, 8, 0);

	return shangt;

	// float xiat = float(mouth - landmark[5]) / float(landmark[5] - eye);
	// ratio = (double)((atan(xiat) * 57.29577) - 45);

	// vector <float> lines_arctan;//直线斜率的反正切值
	// for (unsigned int i = 0; i < lines.size(); i++)
	//{
	//	k = (double)(lines[i][3] - lines[i][1]) / (double)(lines[i][2] - lines[i][0]); //求出直线的斜率
	//	lines_arctan.push_back(atan(k));
	// }
	// return lines_arctan;
	// cout << "检测到的直线角度为：" << txt << endl;
	//  取值范围
	// 1.0~1.2  俯仰角0
	//<1.0  俯角
	//>1.4  仰角
}

void dsm_muv::Tface::draw_face(cv::Mat input_img, cv::Rect roi, cv::Rect face_box, std::vector<float> angle_vec)
{
	// 左右转向的判定前提是人脸水平，如果人脸不水平 则只认为是倾斜而不是左右转向
	cv::Mat eyer_roi;
	cv::Rect center;
	if ((angle_vec[2] < 22) && (angle_vec[2] > -22)) // 12
	{
		if (angle_vec[0] > 18)
		{
			center.x = roi.x - 18;
			center.y = roi.y;
			center.width = roi.width;
			center.height = roi.height;
			// eyer_roi = Tcut::cut_rotate_roi(input_img, center, angle_vec[2]);
		}
		else if (angle_vec[0] < -18)
		{
			center.x = roi.x + 18;
			center.y = roi.y;
			center.width = roi.width;
			center.height = roi.height;
			// eyer_roi = Tcut::cut_rotate_roi(input_img, center, angle_vec[2]);
		}
		else if ((angle_vec[0] < 6.8) && (angle_vec[0] > -6.8))
		{
			// 倾斜角度不大时  用yolo检测框代替
			// rectangle(input_img, face_box, Scalar(0, 0, 255), 2);
		}
		else
		{
			// eyer_roi = Tcut::cut_rotate_roi(input_img, roi, angle_vec[2]);
		}
		cv::rectangle(input_img, face_box, cv::Scalar(255, 255, 255), 1); // 此处显示功能是为了提示人脸转动的效果
	}
	else
	{
		// 倾斜角度过大时  用yolo检测框代替
		cv::rectangle(input_img, face_box, cv::Scalar(255, 0, 0), 1);
	}

	// namedWindow("左眼ROI", WINDOW_NORMAL);
	// imshow("左眼ROI", input_img);
}

cv::Rect dsm_muv::Tface::roi_invade(cv::Mat input_img, cv::Rect roi_box)
{
	// ROI越界限制
	if ((roi_box.x >= input_img.cols) || (roi_box.x <= 0))
	{
		if (roi_box.x <= 0)
		{
			roi_box.x = 0;
		}
		else
		{
			if (roi_box.x <= 0)
			{
				roi_box.x = 0; // 此处添加了越界限制
			}
			else
			{
				roi_box.x = input_img.cols - 2;
			}
		}
	}
	if ((roi_box.y >= input_img.rows) || (roi_box.y <= 0))
	{
		if (roi_box.y <= 0)
		{
			roi_box.y = 0;
		}
		else
		{
			roi_box.y = input_img.rows - 2;
		}
	}

	if (roi_box.width <= 0)
	{
		roi_box.width = 0;
	}
	if (roi_box.height <= 0)
	{
		roi_box.height = 0;
	}

	if ((roi_box.x + roi_box.width) >= input_img.cols)
	{
		roi_box.width = input_img.cols - roi_box.x;
	}
	if ((roi_box.y + roi_box.height) >= input_img.rows)
	{
		roi_box.height = input_img.rows - roi_box.y;
	}
	return roi_box;
}