#include <iostream>
#include <string>
#include <opencv2/opencv.hpp>
#include "cut_faceroi.h"
#include "face_angle.h"
#include "run.h"

extern dsm_muv::DSMInfo almuv_info;
// 切割旋转的roi图片
cv::Mat dsm_muv::Tcut::cut_rotate_roi(cv::Mat input_img, cv::Rect center, float angle_vec)
{
	center = Tface::roi_invade(input_img, center);
	// cout << "center.height " << center.height << endl;
	// cout << "center.width " << center.width << endl;
	// cout << "center.x " << center.x << endl;
	cv::Point zxin;
	zxin.x = center.x;
	zxin.y = center.y;
	cv::Mat make = cv::Mat::zeros(input_img.size(), CV_8UC1);
	cv::RotatedRect rRect(zxin, cv::Size(center.width, center.height), angle_vec);
	cv::Point2f rRectPoint[4];
	rRect.points(rRectPoint);

	for (int i = 0; i < 4; i++)
	{
		cv::Point exit1, exit2;
		exit1.x = 0;
		exit1.y = 0;
		exit2.x = 0;
		exit2.y = 0;
		if (rRectPoint[(i + 1) % 4].x > input_img.cols)
		{
			exit1.x = input_img.cols;
		}
		else
		{
			exit1.x = rRectPoint[(i + 1) % 4].x;
		}
		if (rRectPoint[(i + 1) % 4].y > input_img.rows)
		{
			exit1.y = input_img.rows;
		}
		else
		{
			exit1.y = rRectPoint[(i + 1) % 4].y;
		}

		if (rRectPoint[i].x > input_img.cols)
		{
			exit2.x = input_img.cols;
		}
		else
		{
			exit2.x = rRectPoint[i].x;
		}
		if (rRectPoint[i].y > input_img.rows)
		{
			exit2.y = input_img.rows;
		}
		else
		{
			exit2.y = rRectPoint[i].y;
		}

		// line(make, exit2, exit1, Scalar(255, 0, 255));
		// line(input_img, rRectPoint[i], rRectPoint[(i + 1) % 4], Scalar(0, 255, 255));
	}
	// ��ɫ���
	// cout << "ROI����:  x " << zxin.x <<"  y: " << zxin.y << endl;
	floodFill(make, zxin, 255, nullptr); //  255
	cv::Mat makeImg;
	cv::Mat grayimg;
	cv::cvtColor(input_img, grayimg, cv::COLOR_BGR2GRAY);
	cv::bitwise_and(grayimg, make, makeImg);

	cv::Rect boundRect; // ������Ӿ���
	boundRect = rRect.boundingRect();
	boundRect = Tface::roi_invade(makeImg, boundRect);
	// ���Ŀ��
	cv::rectangle(makeImg, boundRect.tl(), boundRect.br(), cv::Scalar(255, 0, 0), 3, 8);
	cv::Mat result_roi = makeImg(boundRect).clone();
	return result_roi;
}

cv::Mat dsm_muv::Tcut::cut_eye_L(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks)
{
	int temp_leftright = angle_vec[0];
	int temp_updown = angle_vec[1];
	// ���ݱ�׼������ͥ���ۡ��ı������֡������и�
	int ratio_wight = angle_vec[3] * 80;
	int ratio_height = angle_vec[3] * 60;
	cv::Rect roi;
	roi.x = landmarks[0] - (landmarks[2] - landmarks[0]) * 0.36; // landmarks[2] - landmarks[0] Ϊ1ͥ
	roi.y = landmarks[1] - (landmarks[2] - landmarks[0]) * 0.2;
	roi.width = (landmarks[2] - landmarks[0]) * 0.72;
	roi.height = (landmarks[2] - landmarks[0]) * 0.4;

	// �޶�roi����  �޽Ƕ�ʶ�����ʹ��
	// Rect face_box = Tface::roi_invade(input_img, roi);
	// Mat eyel_roi = input_img(face_box).clone();
	cv::Mat eyel_roi;
	cv::Rect center;
	center.x = landmarks[0];
	center.y = landmarks[1];
	center.width = roi.width;
	center.height = roi.height;
	// ��֤���ַ�����ʶ��Ч����Ӱ��
	// �Ƕ�����  �������Ը���ϸ������
	if (temp_leftright < -17)
	{
		roi.x -= 5;
		roi.y += 5;
	}

	roi = Tface::roi_invade(input_img, roi);
	eyel_roi = input_img(roi).clone();
	// rectangle(input_img, roi.tl(), roi.br(), Scalar(0, 255, 0), 1, 8);
	return eyel_roi;
}

cv::Mat dsm_muv::Tcut::cut_eye_R(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks)
{
	//人眼的宽高比
	int temp_leftright = angle_vec[0];
	int ratio_wight = angle_vec[3] * 50;
	int ratio_height = angle_vec[3] * 40;
	cv::Rect roi;
	roi.x = landmarks[2] - (landmarks[2] - landmarks[0]) * 0.36; // landmarks[2] - landmarks[0] Ϊ1ͥ
	roi.y = landmarks[3] - (landmarks[2] - landmarks[0]) * 0.2;
	roi.width = (landmarks[2] - landmarks[0]) * 0.72;
	roi.height = (landmarks[2] - landmarks[0]) * 0.4;

	//限定roi区域  无角度识别情况使用
	//Rect face_box = Tface::roi_invade(input_img, roi);
	//Mat eyer_roi = input_img(face_box).clone();
	cv::Mat eyer_roi;
	cv::Rect center;
	center.x = landmarks[2];
	center.y = landmarks[3];
	center.width = roi.width;
	center.height = roi.height;
	//验证两种方案对识别效果的影响
	//角度限制  后续可以更精细化管理

	if (temp_leftright > 17)
	{
		roi.x += 5;
		roi.y += 5;
	}
	roi = Tface::roi_invade(input_img, roi);
	eyer_roi = input_img(roi).clone();
	// rectangle(input_img, roi.tl(), roi.br(), Scalar(0, 255, 0), 1, 8);

	return eyer_roi;
}

cv::Mat dsm_muv::Tcut::cut_belt(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec)
{
	cv::Mat belt_img(150, 150, CV_8UC3, cv::Scalar::all(0));
	std::vector<cv::Point> belt_roi;
	if (!input_img.empty())
	{
		std::vector<cv::Point> points;
		points.clear();
		int face_height = face_box.height + 25;//+50像素
		int face_y = face_box.y;
		//根据人脸远近进行微调,暂时以 50 作为标准距离
		int distance = 90 - int(angle_vec[3] * 100);
		int stand_dis = 50;
		int dis_bias = stand_dis - distance;
		if (dis_bias > 0)
		{
			dis_bias = dis_bias * 1;
		}
		else
		{
			dis_bias = 0;
		}
		// std::cout << "beltroi.y+distance = " << dis_bias << std::endl;
		face_height = face_height + dis_bias;
		BELT_INFO belt_info_ = SkewParam::GetBeltInfo();
		belt_info_.five_point[0] = cv::Point(belt_info_.five_point[0].x, face_y + face_height);
		belt_info_.five_point[1] = cv::Point(belt_info_.five_point[1].x, face_y + face_height + 50);  //50*0.5625=26
		belt_info_.five_point[2] = cv::Point(belt_info_.five_point[1].x, face_y + face_height + 150); //150*0.5625=84
		belt_info_.five_point[3] = cv::Point(belt_info_.five_point[3].x, face_y + face_height + 150);
		belt_info_.five_point[4] = cv::Point(belt_info_.five_point[4].x, face_y + face_height);
		for (size_t i = 0; i < 5; i++)  //150*85
		{
			points.push_back(belt_info_.five_point[i]);
			almuv_info.belt_point[i] = cv::Point(1280-points[i].y*2,  belt_info_.five_point[i].x*2-280);
			// std::cout<<"=======================================================================7777belt_info_.five_point[i]:  "<< belt_info_.five_point[i] << std::endl;
		}

		// almuv_info.belt_point[0] = cv::Point(1280-belt_info_.five_point[0].y*2,  belt_info_.five_point[0].x*2-280);
		// almuv_info.belt_point[1] = cv::Point(1280-belt_info_.five_point[1].y*2,  belt_info_.five_point[1].x*2-280);
		// almuv_info.belt_point[2] = cv::Point(1280-belt_info_.five_point[2].y*2,  belt_info_.five_point[2].x*2-280);
		// almuv_info.belt_point[3] = cv::Point(1280-belt_info_.five_point[3].y*2,  belt_info_.five_point[3].x*2-280);
		// almuv_info.belt_point[4] = cv::Point(1280-belt_info_.five_point[4].y*2,  belt_info_.five_point[4].x*2-280);

		// points.push_back(cv::Point(602, 191));
		// points.push_back(cv::Point(77, 194));
		// points.push_back(cv::Point(82, 610));
		// points.push_back(cv::Point(265, 626));
		// points.push_back(cv::Point(518, 495));

		// ����maskͼ��
		cv::Mat mask(input_img.size(), CV_8UC3, cv::Scalar::all(0));
		cv::fillConvexPoly(mask, points, cv::Scalar::all(255));

		cv::Mat result;
		cv::bitwise_and(input_img, mask, result);

		// Rect r1(Point(points[3].x, points[0].y), Point(points[1].x, points[2].y));
		//左舵安全带位置
		int left_x, right_x, top_y, bottom_y;
		if (belt_info_.left_right_button == 0)
		{
			left_x = MIN(points[1].x, points[2].x);
			right_x = MAX(points[3].x, points[4].x);
			top_y = MIN(points[0].y, points[4].y);
			bottom_y = MAX(points[2].y, points[3].y);
		}
		//右舵安全带位置
		else
		{
			left_x = MIN(points[4].x, points[3].x);
			right_x = MAX(points[1].x, points[2].x);
			top_y = MIN(points[0].y, points[4].y);
			bottom_y = MIN(points[2].y, points[3].y);
		}
		cv::Rect r1(cv::Point(left_x, top_y), cv::Point(right_x, bottom_y));
		r1 = Tface::roi_invade(result, r1);
		// std::cout<<"=======================================================================r1.heigt:  "<< r1.height<< std::endl;
		// std::cout<<"=======================================================================result:  "<< result.size() << std::endl;
		belt_img = result(r1).clone();
	
		// std::cout<<"=======================================================================belt_img:  "<< belt_img.size() << std::endl;
		if (belt_info_.left_right_button == 1) //翻转成左舵，适应分类模型
		{
			cv::flip(belt_img, belt_img, 1);
		}

		// cv::Scalar color = cv::Scalar(0, 255, 0);
		// cv::polylines(input_img, belt_info.five_point, true, color, 2, cv::LINE_AA);
		return belt_img;
	}
	else
	{
		printf("muv: **************************************************** cut_belt is empty *in line %d  \n", __LINE__);
		return input_img;
	}
}




cv::Mat dsm_muv::Tcut::cut_smoke(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, std::vector<int> landmarks)
{
	int center_x = (landmarks[6] + landmarks[8]) / 2;
	int center_y = landmarks[7] > landmarks[9] ? landmarks[7] : landmarks[9];
	int ratio_wight = 2;
	int ratio_height = 0;

	ratio_wight = angle_vec[3] * 100;
	ratio_height = angle_vec[3] * 40;

	int mouth_x = landmarks[6] - ratio_wight;
	int mouth_y = landmarks[5] - ratio_height;
	cv::Rect roi;
	roi.x = mouth_x; // 乘以系数
	roi.y = mouth_y;
	roi.width = (landmarks[8] - landmarks[6]) + (ratio_wight + ratio_wight);
	roi.height = (center_y - landmarks[5]) * 2.75; //+  ratio_height

	//添加角度控制
	//[0]左右朝向
	roi.x += angle_vec[0];
	//[1]俯仰朝向
	if (angle_vec[1] < -5)
	{
		roi.y -= angle_vec[1] * 0.8;
		roi.height -= angle_vec[1];
	}

	roi = Tface::roi_invade(input_img, roi);
	cv::Mat smoke_img = input_img(roi).clone();
	return smoke_img;
}

double dsm_muv::Tcut::compareMats(cv::Mat mat1, cv::Mat mat2)
{
	int face_ares = mat1.rows * mat1.cols;
	// cout << "face_ares ======= " << face_ares << endl;
	int frame_ares = mat2.rows * mat2.cols;
	// cout << "frame_ares ====== " << frame_ares << endl;

	double result = ((double)face_ares / (double)frame_ares) * 10 + 0.56;
	// cout << "result ===== " << result << endl;

	return result;
}