/*
 *parameter: file_path �ļ��ľ���·������: /user/home/<USER>
 *           key         �ı��еı�����   �� num
 *           def       	��Ӧ������Ĭ��ֵ
 *	     readConfig ����string����   readConfigFloat ����float����  readConfigInt ����Int����
 *
 */
#include <string>
#include <fstream>
#include "myparams.h"
#include "G3_Configuration.h"

//���յ��ںŶ�ȡ�����ļ��в���  ����һ���ṹ��
void get_global_params(std::string yaml_path, globals globalparams) {
    //���ֲ���
    globalparams.respirator_speed = readConfigInt(yaml_path, "respirator_speed", 10);
    globalparams.respirator_button = readConfigBool(yaml_path, "respirator_button", true);
    globalparams.event_respirator_time = readConfigInt(yaml_path, "event_respirator_time", 4000);
    globalparams.freeze_respirator_time = readConfigInt(yaml_path, "freeze_respirator_time", 2000);

    //���۲���
    globalparams.eye_speed = readConfigInt(yaml_path, "eye_speed", 10);
    globalparams.eye_button = readConfigBool(yaml_path, "eye_button", true);
    globalparams.event_eye_time = readConfigInt(yaml_path, "event_eye_time", 4000);
    globalparams.freeze_eye_time = readConfigInt(yaml_path, "freeze_eye_time", 2000);

    //��Ƿ����
    globalparams.mouth_speed = readConfigInt(yaml_path, "mouth_speed", 10);
    globalparams.mouth_button = readConfigBool(yaml_path, "mouth_button", true);
    globalparams.event_mouth_time = readConfigInt(yaml_path, "event_mouth_time", 4000);
    globalparams.freeze_mouth_time = readConfigInt(yaml_path, "freeze_mouth_time", 2000);

    //������β��� 
    globalparams.lookaround_speed = readConfigInt(yaml_path, "lookaround_speed", 10);
    globalparams.lookaround_button = readConfigBool(yaml_path, "lookaround_button", true);
    globalparams.event_lookaround_time = readConfigInt(yaml_path, "event_lookaround_time", 4000);
    globalparams.freeze_lookaround_time = readConfigInt(yaml_path, "freeze_lookaround_time", 2000);

    //��ڲ���
    globalparams.facemissing_speed = readConfigInt(yaml_path, "facemissing_speed", 10);
    globalparams.facemissing_button = readConfigBool(yaml_path, "facemissing_button", true);
    globalparams.event_facemissing_time = readConfigInt(yaml_path, "event_facemissing_time", 4000);
    globalparams.freeze_facemissing_time = readConfigInt(yaml_path, "freeze_facemissing_time", 2000);

    //�ڵ�����
    globalparams.camcover_speed = readConfigInt(yaml_path, "camcover_speed", 10);
    globalparams.camcover_button = readConfigBool(yaml_path, "camcover_button", true);
    globalparams.event_camcover_time = readConfigInt(yaml_path, "event_camcover_time", 4000);
    globalparams.freeze_camcover_time = readConfigInt(yaml_path, "freeze_camcover_time", 2000);

    //���̲���
    globalparams.smoking_speed = readConfigInt(yaml_path, "smoking_speed", 10);
    globalparams.smoking_button = readConfigBool(yaml_path, "smoking_button", true);
    globalparams.event_smoking_time = readConfigInt(yaml_path, "event_smoking_time", 4000);
    globalparams.freeze_smoking_time = readConfigInt(yaml_path, "freeze_smoking_time", 2000);

    //��绰����
    globalparams.phone_speed = readConfigInt(yaml_path, "phone_speed", 10);
    globalparams.phone_button = readConfigBool(yaml_path, "phone_button", true);
    globalparams.event_phone_time = readConfigInt(yaml_path, "event_phone_time", 4000);
    globalparams.freeze_phone_time = readConfigInt(yaml_path, "freeze_phone_time", 2000);

    //���Ʋ���  ��ʼ��ֹͣ�ź�  
    globalparams.gesture_button = readConfigBool(yaml_path, "gesture_button", true);
    globalparams.gesture_sign = readConfigInt(yaml_path, "event_phone_time", 0);



//    std::cout << "respirator_button is " << (bool) globalparams.respirator_button << std::endl;

}

//��ȡ���ò���
// �����Ĳ�����yaml�ļ���·��
bool LoadConfigFile(const std::string &file_name, globals &globalparams) {


    regions respirator;

    respirator.speed = G3_Configuration::getInstance().getRespiratorSpeed();
    respirator.button = G3_Configuration::getInstance().getRespiratorButton();
    respirator.mode = G3_Configuration::getInstance().getRespiratorMode();
    respirator.sensitivity = G3_Configuration::getInstance().getRespiratorSensitivity();
    respirator.event_time = G3_Configuration::getInstance().getEventRespiratorTime();
    respirator.freeze_time = G3_Configuration::getInstance().getFreezeRespiratorTime();
    //У�麯�� ������Ҫд�������־���Ա���ѯ
    bool stat_respirator = check_params(respirator);
    globalparams.respirator_speed = respirator.speed;
    globalparams.respirator_button = respirator.button;
    globalparams.respirator_mode = respirator.mode;
    globalparams.respirator_sensitivity = respirator.sensitivity;
    globalparams.event_respirator_time = respirator.event_time;
    globalparams.freeze_respirator_time = respirator.freeze_time;

    //���۲���
    regions eye;
    eye.speed = G3_Configuration::getInstance().getEyeSpeed();
    eye.button = G3_Configuration::getInstance().getEyeButton();
    eye.mode = G3_Configuration::getInstance().getEyeMode();
    eye.sensitivity = G3_Configuration::getInstance().getEyeSensitivity();
    eye.event_time = G3_Configuration::getInstance().getEventEyeTime();
    eye.freeze_time = G3_Configuration::getInstance().getFreezeEyeTime();
    bool stat_eye = check_params(eye);
    globalparams.eye_speed = eye.speed;
    globalparams.eye_button = eye.button;
    globalparams.eye_mode = eye.mode;
    globalparams.eye_sensitivity = eye.sensitivity;
    globalparams.event_eye_time = eye.event_time;
    globalparams.freeze_eye_time = eye.freeze_time;

    //��Ƿ����
    regions mouth;
    mouth.speed = G3_Configuration::getInstance().getMouthSpeed();
    mouth.button = G3_Configuration::getInstance().getMouthButton();
    mouth.mode = G3_Configuration::getInstance().getMouthMode();
    mouth.sensitivity = G3_Configuration::getInstance().getMouthSensitivity();
    mouth.event_time = G3_Configuration::getInstance().getEventMouthTime();
    mouth.freeze_time = G3_Configuration::getInstance().getFreezeMouthTime();
    bool stat_mouth = check_params(mouth);
    globalparams.mouth_speed = mouth.speed;
    globalparams.mouth_button = mouth.button;
    globalparams.mouth_mode = mouth.mode;
    globalparams.mouth_sensitivity = mouth.sensitivity;
    globalparams.event_mouth_time = mouth.event_time;
    globalparams.freeze_mouth_time = mouth.freeze_time;

    //������β��� lookaround
    regions lookaround;
    lookaround.speed = G3_Configuration::getInstance().getLookaroundSpeed();
    lookaround.button = G3_Configuration::getInstance().getLookaroundButton();
    lookaround.mode = G3_Configuration::getInstance().getLookaroundMode();
    lookaround.sensitivity = G3_Configuration::getInstance().getLookaroundSensitivity();
    lookaround.event_time = G3_Configuration::getInstance().getEventLookaroundTime();
    lookaround.freeze_time = G3_Configuration::getInstance().getFreezeLookaroundTime();
    bool stat_lookaround = check_params(lookaround);
    globalparams.lookaround_speed = lookaround.speed;
    globalparams.lookaround_button = lookaround.button;
    globalparams.lookaround_mode = lookaround.mode;
    globalparams.lookaround_sensitivity = lookaround.sensitivity;
    globalparams.event_lookaround_time = lookaround.event_time;
    globalparams.freeze_lookaround_time = lookaround.freeze_time;

    //��ڲ��� facemissing
    regions facemissing;
    facemissing.speed = G3_Configuration::getInstance().getFacemissingSpeed();
    facemissing.button = G3_Configuration::getInstance().getFacemissingButton();
    facemissing.mode = G3_Configuration::getInstance().getFacemissingMode();
    facemissing.sensitivity = G3_Configuration::getInstance().getFacemissingSensitivity();
    facemissing.event_time = G3_Configuration::getInstance().getEventFacemissingTime();
    facemissing.freeze_time = G3_Configuration::getInstance().getFreezeFacemissingTime();
    bool stat_facemissing = check_params(facemissing);
    globalparams.facemissing_speed = facemissing.speed;
    globalparams.facemissing_button = facemissing.button;
    globalparams.facemissing_mode = facemissing.mode;
    globalparams.facemissing_sensitivity = facemissing.sensitivity;
    globalparams.event_facemissing_time = facemissing.event_time;
    globalparams.freeze_facemissing_time = facemissing.freeze_time;
    //�ڵ����� camcover
    regions camcover;
    camcover.speed = G3_Configuration::getInstance().getCamcoverSpeed();
    camcover.button = G3_Configuration::getInstance().getCamcoverButton();
    camcover.mode = G3_Configuration::getInstance().getCamcoverMode();
    camcover.sensitivity = G3_Configuration::getInstance().getCamcoverSensitivity();
    camcover.event_time = G3_Configuration::getInstance().getEventCamcoverTime();
    camcover.freeze_time = G3_Configuration::getInstance().getFreezeCamcoverTime();
    bool stat_camcover = check_params(camcover);
    globalparams.camcover_speed = camcover.speed;
    globalparams.camcover_button = camcover.button;
    globalparams.camcover_mode = camcover.mode;
    globalparams.camcover_sensitivity = camcover.sensitivity;
    globalparams.event_camcover_time = camcover.event_time;
    globalparams.freeze_camcover_time = camcover.freeze_time;

    //���̲���
    regions smoking;
    smoking.speed = G3_Configuration::getInstance().getSmokingSpeed();
    smoking.button = G3_Configuration::getInstance().getSmokingButton();
    smoking.mode = G3_Configuration::getInstance().getSmokingMode();
    smoking.sensitivity = G3_Configuration::getInstance().getSmokingSensitivity();
    smoking.event_time = G3_Configuration::getInstance().getEventSmokingTime();
    smoking.freeze_time = G3_Configuration::getInstance().getFreezeSmokingTime();
    bool stat_smoking = check_params(smoking);
    globalparams.smoking_speed = smoking.speed;
    globalparams.smoking_button = smoking.button;
    globalparams.smoking_mode = smoking.mode;
    globalparams.smoking_sensitivity = smoking.sensitivity;
    globalparams.event_smoking_time = smoking.event_time;
    globalparams.freeze_smoking_time = smoking.freeze_time;

    //��绰����
    regions phone;
    phone.speed = G3_Configuration::getInstance().getPhoneSpeed();
    phone.button = G3_Configuration::getInstance().getPhoneButton();
    phone.mode = G3_Configuration::getInstance().getPhoneMode();
    phone.sensitivity = G3_Configuration::getInstance().getPhoneSensitivity();
    phone.event_time = G3_Configuration::getInstance().getEventPhoneTime();
    phone.freeze_time = G3_Configuration::getInstance().getFreezePhoneTime();
    bool stat_phone = check_params(phone);
    globalparams.phone_speed = phone.speed;
    globalparams.phone_button = phone.button;
    globalparams.phone_mode = phone.mode;
    globalparams.phone_sensitivity = phone.sensitivity;
    globalparams.event_phone_time = phone.event_time;
    globalparams.freeze_phone_time = phone.freeze_time;

    //����ģ����� ��������
    //config["gesture_button"] >> globalparams.gesture_button;
    //config["gesture_mode"] >> globalparams.gesture_mode;
    //config["gesture_sensitivity"] >> globalparams.gesture_sensitivity;
    //config["gesture_sign"] >> globalparams.gesture_sign;

    // 动静参数
    globalparams.detect_move_button = G3_Configuration::getInstance().getDetectMoveButton();
    globalparams.move_pixel = G3_Configuration::getInstance().getMovePixel();
    globalparams.frame_early = G3_Configuration::getInstance().getFrameEarly();

    return true;
}

std::string readConfig(std::string file_path, const std::string &key, std::string def) {
    const char *cfgfilepath = file_path.c_str();
    std::fstream cfgFile;
    std::string value;
    std::string no_value = "error";
    cfgFile.open(cfgfilepath);//���ļ�
    if (!cfgFile.is_open()) {
        std::cout << "can not open cfg file!" << std::endl;
        return no_value;
    }
    char tmp[100];
    while (!cfgFile.eof())//ѭ����ȡÿһ��
    {
        cfgFile.getline(tmp, 100);//ÿ�ж�ȡǰ1000���ַ���1000��Ӧ���㹻��
        std::string line(tmp);
        std::size_t pos = line.find('=');//�ҵ�ÿ�еġ�=����λ�ã�֮ǰ��key֮����value
        if (pos == std::string::npos) return def;
        std::string tmpKey = line.substr(0, pos);//ȡ=��֮ǰ
        if (key == tmpKey) {
            value = line.substr(pos + 1);//ȡ=��֮��
            return value;
        }
    }
    return def;
}


float readConfigFloat(std::string file_path, const std::string &key, float def) {
    const char *cfgfilepath = file_path.c_str();
    std::fstream cfgFile;
    std::string value;
    float no_value = -1;
    cfgFile.open(cfgfilepath);//���ļ�	
    if (!cfgFile.is_open()) {
        std::cout << "can not open cfg file!" << std::endl;
        return no_value;
    }
    char tmp[100];
    while (!cfgFile.eof())//ѭ����ȡÿһ��
    {
        cfgFile.getline(tmp, 100);//ÿ�ж�ȡǰ1000���ַ���1000��Ӧ���㹻��
        std::string line(tmp);
        std::size_t pos = line.find('=');//�ҵ�ÿ�еġ�=����λ�ã�֮ǰ��key֮����value
        if (pos == std::string::npos) return def;
        std::string tmpKey = line.substr(0, pos);//ȡ=��֮ǰ
        if (key == tmpKey) {
            value = line.substr(pos + 1);//ȡ=��֮��
            return atof(value.c_str());
        }
    }
    return def;
}

int readConfigInt(std::string file_path, const std::string &key, int def) {
    const char *cfgfilepath = file_path.c_str();
    std::fstream cfgFile;
    std::string value;
    int no_value = -1;
    cfgFile.open(cfgfilepath);//���ļ�	
    if (!cfgFile.is_open()) {
        std::cout << "can not open cfg file!" << std::endl;
        return no_value;
    }
    char tmp[100];
    while (!cfgFile.eof())//ѭ����ȡÿһ��
    {
        cfgFile.getline(tmp, 100);//ÿ�ж�ȡǰ100���ַ���100��Ӧ���㹻��
        std::string line(tmp);
        std::size_t pos = line.find('=');//�ҵ�ÿ�еġ�=����λ�ã�֮ǰ��key֮����value
        if (pos == std::string::npos) return def;
        std::string tmpKey = line.substr(0, pos);//ȡ=��֮ǰ
        if (key == tmpKey) {
            value = line.substr(pos + 1);//ȡ=��֮��
            return atoi(value.c_str());
        }
    }
    return def;

}

bool readConfigBool(std::string file_path, const std::string &key, bool def) {
    const char *cfgfilepath = file_path.c_str();
    std::fstream cfgFile;
    std::string value;
    int no_value = -1;
    cfgFile.open(cfgfilepath);//���ļ�	
    if (!cfgFile.is_open()) {
//        std::cout << "can not open cfg file!" << std::endl;


        return no_value;
    }
    char tmp[100];
    while (!cfgFile.eof())
    {
        cfgFile.getline(tmp, 100);
        std::string line(tmp);
        std::size_t pos = line.find('=');
        if (pos == std::string::npos) return def;
        std::string tmpKey = line.substr(0, pos);//ȡ=��֮ǰ
        if (key == tmpKey) {
            value = line.substr(pos + 1);//ȡ=��֮��
            return atoi(value.c_str());
        }
    }
    return def;
}

bool check_params(regions &region) {
    int stat_false = 0;
    if ((region.speed >= 0) && (region.speed < 100)) { ;
    } else {

        region.speed = 5;
        stat_false = 1;
    }

    if ((region.button == 0) || (region.button == 1)) { ;
    } else {

        region.button = 1;
        stat_false = 1;
    }

    if ((region.sensitivity >= 0) && (region.sensitivity <= 100)) { ;
    } else {

        region.sensitivity = 30;
        stat_false = 1;
    }

    if ((region.event_time >= 0) && (region.event_time <= 3600000)) { ;
    } else {

        region.event_time = 4000;
        stat_false = 1;
    }

    if ((region.freeze_time >= 0) && (region.freeze_time <= 3600000)) { ;
    } else {
        //�������ʹ��Ĭ��ֵ
        region.freeze_time = 2000;
        stat_false = 1;
    }
    if (stat_false == 1) {
        return false;
    } else {
        return true;
    }
}

int getDistance(cv::Point point1, cv::Point point2)
{
    int distance = sqrtf(powf((point1.x - point2.x), 2) + powf((point1.y - point2.y), 2));
    return distance;
}

