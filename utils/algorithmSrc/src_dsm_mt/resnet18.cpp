﻿#include <fstream>
#include <string>
#include <opencv2/opencv.hpp>
#include "resnet18.h"
#include "myparams.h"


#define RESNET_CLASS_NUM 5
using namespace std;
using namespace cv;

//初始化项目   resnet18_0908_best 识别眼睛效果较好；  resnet18_best 黑白摄像头 识别嘴巴较好，
//String Resnet_model = "E:\\Deeplearning\\Classifying_Cancer_ResNet18_Pytorch\\onnx\\resnet18_0908_1_best.onnx";
	
	//已经在main中定义全局变量，在这里只需要声明即可。
    extern String Resnet_model;
    extern String labels_txt_file;

//String Resnet_model = "./resnet18_best.onnx";
//String labels_txt_file = "./resnet18_best.txt";




#ifdef DSM_USING_RKNN

#include "rknn_api.h"



/*-------------------------------------------
                  Functions
-------------------------------------------*/

static void printRKNNTensor(rknn_tensor_attr *attr)
{
    printf("index=%d name=%s n_dims=%d dims=[%d %d %d %d] n_elems=%d size=%d fmt=%d type=%d qnt_type=%d fl=%d zp=%d scale=%f\n",
           attr->index, attr->name, attr->n_dims, attr->dims[3], attr->dims[2], attr->dims[1], attr->dims[0],
           attr->n_elems, attr->size, 0, attr->type, attr->qnt_type, attr->fl, attr->zp, attr->scale);
}

static unsigned char *load_model(const char *filename, int *model_size)
{
    FILE *fp = fopen(filename, "rb");
    if (fp == nullptr)
    {
        printf("fopen %s fail!\n", filename);
        return NULL;
    }
    fseek(fp, 0, SEEK_END);
    int model_len = ftell(fp);
    unsigned char *model = (unsigned char *)malloc(model_len);
    fseek(fp, 0, SEEK_SET);
    if (model_len != fread(model, 1, model_len, fp))
    {
        printf("fread %s fail!\n", filename);
        free(model);
        return NULL;
    }
    *model_size = model_len;
    if (fp)
    {
        fclose(fp);
    }
    return model;
}

static int rknn_GetTop_mt(
    float *pfProb,
    float *pfMaxProb,
    uint32_t *pMaxClass,
    uint32_t outputCount,
    uint32_t topNum)
{
    uint32_t i, j;

#define MAX_TOP_NUM 20
    if (topNum > MAX_TOP_NUM)
        return 0;

    memset(pfMaxProb, 0, sizeof(float) * topNum);
    memset(pMaxClass, 0xff, sizeof(float) * topNum);

    for (i = 0; i < 5; i++)
        printf("$$$$$$$$$$$$$$$$$$$ prob = %f\n", pfProb[i]);

    for (j = 0; j < topNum; j++)
    {
        for (i = 0; i < outputCount; i++)
        {
            if ((i == *(pMaxClass + 0)) || (i == *(pMaxClass + 1)) || (i == *(pMaxClass + 2)) ||
                (i == *(pMaxClass + 3)) || (i == *(pMaxClass + 4)))
            {
                continue;
            }

            if (pfProb[i] > *(pfMaxProb + j))
            {
                *(pfMaxProb + j) = pfProb[i];
                *(pMaxClass + j) = i;
            }
        }
    }

    return 1;
}

void softmax_mt(float *buf, int num)
{
    float total = 0.0;
    for (int i = 0; i < num; i++)
        total += exp(buf[i]);
    for (int i = 0; i < num; i++)
        buf[i] = exp(buf[i]) / total;
    return;
}

/*-------------------------------------------
                  Main Function
-------------------------------------------*/
static bool isInit = false;
static rknn_context ctx;

Mat rknn_infer_mt(Mat img)
{
    int ret;

//imwrite("./img.jpg", img);

if (isInit == false)
{
    int model_len = 0;
    unsigned char *model;

    //const char *model_path = "./model/resnet18.rknn";

    // Load RKNN Model
    model = load_model(Resnet_model.c_str(), &model_len);

    ret = rknn_init(&ctx, model, model_len, 0);
    if (ret < 0)
        printf("rknn_init fail! ret=%d\n", ret);

    isInit = true;
}

    rknn_input_output_num io_num_mt;

    // Get Model Input Output Info
    ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num_mt, sizeof(io_num_mt));
    if (ret != RKNN_SUCC)
        printf("rknn_query fail! ret=%d\n", ret);
        
    // printf("model input num: %d, output num: %d\n", io_num_mt.n_input, io_num_mt.n_output);

    // printf("input tensors:\n");
    rknn_tensor_attr input_attrs[io_num_mt.n_input];
    memset(input_attrs, 0, sizeof(input_attrs));
    for (int i = 0; i < io_num_mt.n_input; i++)
    {
        input_attrs[i].index = i;
        ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC)
            printf("rknn_query fail! ret=%d\n", ret);  
        // printRKNNTensor(&(input_attrs[i]));
    }

    // printf("output tensors:\n");
    rknn_tensor_attr output_attrs[io_num_mt.n_output];
    memset(output_attrs, 0, sizeof(output_attrs));
    for (int i = 0; i < io_num_mt.n_output; i++)
    {
        output_attrs[i].index = i;
        ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC)
            printf("rknn_query fail! ret=%d\n", ret);   
        // printRKNNTensor(&(output_attrs[i]));
    }

    // Set Input Data
    rknn_input inputs[1];
    memset(inputs, 0, sizeof(inputs));
    inputs[0].index = 0;
    inputs[0].type = RKNN_TENSOR_UINT8;
    inputs[0].size = input_attrs[0].size;
    inputs[0].fmt = RKNN_TENSOR_NHWC;
    inputs[0].buf = img.data;

    ret = rknn_inputs_set(ctx, io_num_mt.n_input, inputs);
    if (ret < 0)
        printf("rknn_input_set fail! ret=%d\n", ret);

    // Run
    // printf("rknn_run\n");
    ret = rknn_run(ctx, nullptr);
    if (ret < 0)
        printf("rknn_run fail! ret=%d\n", ret);

    // Get Output
    rknn_output outputs[1];
    memset(outputs, 0, sizeof(outputs));
    outputs[0].want_float = 1;
    ret = rknn_outputs_get(ctx, 1, outputs, NULL);
    if (ret < 0)
        printf("rknn_outputs_get fail! ret=%d\n", ret);

    int class_num = outputs[0].size / 4;
    
    float *buffer = (float *)outputs[0].buf;
    softmax_mt(buffer, class_num);

/*
for (int i = 0; i < 5; i++)
    printf("score = %f\n", buffer[i]);
*/

    Mat output_mat = Mat(Size(class_num, 1), CV_32FC1, buffer);

float *ptr = output_mat.ptr<float>(0);

  /*      
printf("***********************************************\n");
        for (int n = 0; n < output_mat.cols; n++) 
{
printf("[%d] = %f\n", n, *ptr);
            ptr++;
}
printf("***********************************************\n");
*/

    return output_mat;

    /*
    // Post Process
    int max_class_index = -1;
    for (int i = 0; i < io_num_mt.n_output; i++)
    {
        uint32_t MaxClass[5];
        float fMaxProb[5];
        float *buffer = (float *)outputs[i].buf;
        uint32_t sz = outputs[i].size / 4;
        //printf("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ sz = %d\n", sz);

        rknn_GetTop_mt(buffer, fMaxProb, MaxClass, sz, 5);

        printf(" --- Top5 ---\n");
        for (int i = 0; i < 5; i++)
        {
            printf("%3d: %8.6f\n", MaxClass[i], fMaxProb[i]);
        }
        max_class_index = MaxClass[0];
    }
    */
}
#endif


vector<String> Resnet::readClassNames()
{
	std::vector<String> classNames;

	std::ifstream fp(labels_txt_file);
	if (!fp.is_open())
	{
		printf("could not open file...\n");
		exit(-1);
	}
	std::string name;
	while (!fp.eof())
	{
		std::getline(fp, name);
		if (name.length())
			classNames.push_back(name);
	}
	fp.close();
	return classNames;
}

//综合识别算法
cv::Point Resnet::resnet_result(vector<Mat> input_img, Mat img_face, vector<int> landmarks)
{
#ifdef DSM_USING_RKNN
#else
	Mat img_eyer, img_eyel, img_mouth;
	input_img[0].copyTo(img_eyer);
	input_img[1].copyTo(img_eyel);
	input_img[2].copyTo(img_mouth);
	img_eyer = convertTo3Channels(img_eyer);
	img_eyel = convertTo3Channels(img_eyel);
	img_mouth = convertTo3Channels(img_mouth);

	//处理img_eyer
	resize(img_eyer, img_eyer, Size(56, 56), INTER_AREA);
	img_eyer.convertTo(img_eyer, CV_32FC3);
	PreProcess(img_eyer, img_eyer);         //标准化处理
	//处理img_eyel
	resize(img_eyel, img_eyel, Size(56, 56), INTER_AREA);
	img_eyel.convertTo(img_eyel, CV_32FC3);
	PreProcess(img_eyel, img_eyel);         //标准化处理
	//处理img_eyel
	resize(img_mouth, img_mouth, Size(56, 56), INTER_AREA);
	img_mouth.convertTo(img_mouth, CV_32FC3);
	PreProcess(img_mouth, img_mouth);         //标准化处理

	//将image1和image2合并到images
	vector<Mat> images;
	images.push_back(img_eyer);
	images.push_back(img_eyel);
	images.push_back(img_mouth);
	vector<String> labels = readClassNames();

	int res_weight = 56;
	int res_height = 56;

	// 加载网络
	cv::dnn::Net net = cv::dnn::readNetFromONNX(Resnet_model);  // 加载训练好的识别模型

	if (net.empty()) {
		printf("read onnx model data failure...\n");
		system("pause");
		exit(0);
	}
	Mat inputBlob = blobFromImages(images, 1.0, Size(res_weight, res_height), Scalar(0, 0, 0), false, true);

	// 执行图像分类
	net.setInput(inputBlob);
	cv::Mat prob = net.forward();     // 推理出结果
	//print(prob);
	vector<double> times;
	double time = net.getPerfProfile(times);
	float ms = (time * 1000) / getTickFrequency();
	printf("current inference time : %.2f ms \n", ms);
	// 得到最可能分类输出
	int classids = -1;
	vector<Point> face_mask;
	Point eyel = Point(landmarks[0], landmarks[1]);
	face_mask.push_back(eyel);
	Point eyer = Point(landmarks[2], landmarks[3]);
	face_mask.push_back(eyer);
	Point mouth = Point(landmarks[8], landmarks[9]);
	face_mask.push_back(mouth);

	Point return_idx;
	//Point3i rec;
	//rec_idx.clear();
	//增加输出类别限定
	for (int n = 0; n < prob.rows; n++) {
		Point classNumber;
		double classProb;
		Mat probMat = prob(Rect(0, n, RESNET_CLASS_NUM, 1)).clone();
		Mat result = probMat.reshape(1, 1);
		//minMaxLoc  获取最大最小值的位置 得到理想IDS  弃用
		//minMaxLoc(result, NULL, &classProb, NULL, &classNumber);
		// int classidx = classNumber.x;
		// n =0、1时 表示眼睛开闭输入  n=2、3属于嘴巴开闭  后续会增加更多类别
		if (n < 2)
		{
			int classidx = 1; //the eye is open 眼部状态
			if (result.at<float>(0, 0) > result.at<float>(0, 1))
			{
				classProb = result.at<float>(0, 0);
				classidx = 0;
				printf("\n current image classification : %s, possible : %.2f\n", labels.at(classidx).c_str(), classProb);
				//putText(img_face, labels.at(classidx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(255, 0, 0), 1, 1);
			}
			else
			{
				classProb = result.at<float>(0, 1);
				classidx = 1;
				printf("\n current image classification : %s, possible : %.2f\n", labels.at(classidx).c_str(), classProb);
				//putText(img_face, labels.at(classidx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 255, 0), 1, 1);
			}
			return_idx.x = classidx;
			// 显示文本
			/*putText(img_face, labels.at(classidx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 0, 255), 1, 1);*/
		}
		else
		{
			int mclassidx = 2; //the mouth is normal
			if (result.at<float>(0, 2) > result.at<float>(0, 3))
			{
				classProb = result.at<float>(0, 2);
				mclassidx = 2;
				printf("\n current image classification : %s, possible : %.2f\n", labels.at(mclassidx).c_str(), classProb);
				//putText(img_face, labels.at(mclassidx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(255, 0, 255), 1, 1);
			}
			else
			{
				classProb = result.at<float>(0, 3);
				mclassidx = 3;
				printf("\n current image classification : %s, possible : %.2f\n", labels.at(mclassidx).c_str(), classProb);
				//putText(img_face, labels.at(mclassidx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 255, 255), 1, 1);
			}
			return_idx.y = mclassidx;
		}
		cout << "result1： " << format(result, cv::Formatter::FMT_C) << endl;

		//imshow("Image Classification", Showimages[n]);
		//waitKey(0);
	}
	//返回值  0.1闭眼  2.3哈欠  4口罩
	cout << "return_idx： " << return_idx.x << endl;
	return return_idx;
#endif
}

int resbet18EyeCount = 0;
int resbet18mouthCount = 0;
#ifdef DSM_USING_RKNN
int Resnet::resnet_eye(vector<Mat> input_img, Mat img_face, vector<int> landmarks)
#else
int Resnet::resnet_eye(dnn::Net net, vector<Mat> input_img, Mat img_face, vector<int> landmarks)
#endif
{
	Mat img_eyer, img_eyel;
	input_img[0].copyTo(img_eyer);
	input_img[1].copyTo(img_eyel);
//	img_eyer = convertTo3Channels(img_eyer);
//	img_eyel = convertTo3Channels(img_eyel);

	//处理img_eyer
	resize(img_eyer, img_eyer, Size(56, 56), INTER_AREA);
	img_eyer.convertTo(img_eyer, CV_32FC3);
	PreProcess(img_eyer, img_eyer);         //标准化处理
	//处理img_eyel
	resize(img_eyel, img_eyel, Size(56, 56), INTER_AREA);
	img_eyel.convertTo(img_eyel, CV_32FC3);
	PreProcess(img_eyel, img_eyel);         //标准化处理


	//将image1和image2合并到images
	vector<Mat> images;
	images.push_back(img_eyer);
	images.push_back(img_eyel);
	vector<String> labels = readClassNames();

	int res_weight = 56;
	int res_height = 56;

#ifdef DSM_USING_RKNN
        Mat img_eyer_rknn, img_eyel_rknn;
	input_img[0].copyTo(img_eyer_rknn);
	input_img[1].copyTo(img_eyel_rknn);
	//img_eyer_rknn = convertTo3Channels(img_eyer_rknn);
	//img_eyel_rknn = convertTo3Channels(img_eyel_rknn);
        resize(img_eyer_rknn, img_eyer_rknn, Size(56, 56), INTER_AREA);
        resize(img_eyel_rknn, img_eyel_rknn, Size(56, 56), INTER_AREA);

        //imwrite("./img_eyer_rknn.jpg", img_eyer_rknn);
        //imwrite("./img_eyel_rknn.jpg", img_eyel_rknn);

        cv::Mat prob = Mat(Size(RESNET_CLASS_NUM, 2), CV_32FC1);  // resnet的分类数为5
        float *prob_ptr = prob.ptr<float>(0);

    cv::cvtColor(img_eyer_rknn, img_eyer_rknn, COLOR_BGR2RGB);  //#include <opencv2/opencv.hpp>
        cv::Mat prob_r = rknn_infer_mt(img_eyer_rknn);
        float *prob_r_ptr = prob_r.ptr<float>(0);
        for (int n = 0; n < prob_r.cols; n++) 
            *prob_ptr++ = *prob_r_ptr++;

    cv::cvtColor(img_eyel_rknn, img_eyel_rknn, COLOR_BGR2RGB);  //#include <opencv2/opencv.hpp>
        cv::Mat prob_l = rknn_infer_mt(img_eyel_rknn);        
        float *prob_l_ptr = prob_l.ptr<float>(0);
        for (int n = 0; n < prob_l.cols; n++) 
            *prob_ptr++ = *prob_l_ptr++;
#else
	// 加载网络
	//cv::dnn::Net net = cv::dnn::readNetFromONNX(Resnet_model);  // 加载训练好的识别模型

	if (net.empty()) {
		printf("read onnx model data failure...\n");
		system("pause");
		exit(0);
	}
	Mat inputBlob = blobFromImages(images, 1.0, Size(res_weight, res_height), Scalar(0, 0, 0), false, true);

	// 执行图像分类
	net.setInput(inputBlob);
	cv::Mat prob = net.forward();     // 推理出结果
        //print(prob);
	vector<double> times;
	double time = net.getPerfProfile(times);
	float ms = (time * 1000) / getTickFrequency();
	printf("current inference time : %.2f ms \n", ms);
#endif

	// 得到最可能分类输出
	int classids = -1;
	vector<Point> face_mask;
	Point eyel = Point(landmarks[0], landmarks[1]);
	face_mask.push_back(eyel);
	Point eyer = Point(landmarks[2], landmarks[3]);
	face_mask.push_back(eyer);

	int return_idx = 0;
    Scalar mean_l, mean_r;
    mean_l = cv::mean(images[0]);
    mean_r = cv::mean(images[1]);
    if ((mean_r[0] > 120)||(mean_r[0] > 120))
    {
        return_idx = 0;
        return return_idx;
    }

	//Point3i rec;
	//rec_idx.clear();
	//增加输出类别限定  n = 0 左眼   n = 1 右眼
	int eyel_idx = 0; //the eye is open 眼部状态
	int eyer_idx = 0; //the eye is open 眼部状态
    string top1,top2,top3,top4,top5;
    string topeye_L;
    string topeye_R;
    for (int n = 0; n < prob.rows; n++) {
		Point classNumber;
		double classProb;
		Mat probMat = prob(Rect(0, n, RESNET_CLASS_NUM, 1)).clone();
		Mat result = probMat.reshape(1, 1);
		//minMaxLoc  获取最大最小值的位置 得到理想IDS  弃用
		//minMaxLoc(result, NULL, &classProb, NULL, &classNumber);
		// int classidx = classNumber.x;
		// n =0、1时 表示眼睛开闭输入  n=2、3属于嘴巴开闭  后续会增加更多类别

        top1 = to_string(result.at<float>(0, 0)) ;
        top2 = to_string(result.at<float>(0, 1)) ;
        top3 = to_string(result.at<float>(0, 2)) ;
        top4 = to_string(result.at<float>(0, 3)) ;
        top5 = to_string(result.at<float>(0, 4)) ;

		if (n == 0)
		{
            if ((result.at<float>(0, 0) > result.at<float>(0, 1))&&(result.at<float>(0, 0)>=0.991))  //&&( anyles <0.3)  0.991
            {
//				classProb = result.at<float>(0, 0);
				eyel_idx = 1; //闭眼结果
//				printf("\n current image classification : %s, possible : %.2f\n", labels.at(eyel_idx).c_str(), classProb);
				//putText(img_face, labels.at(eyel_idx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(255, 0, 0), 1, 1);
			}
			else
			{
//				classProb = result.at<float>(0, 1);
				eyel_idx = 0;
//				printf("\n current image classification : %s, possible : %.2f\n", labels.at(eyel_idx).c_str(), classProb);
				//putText(img_face, labels.at(eyel_idx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 255, 0), 1, 1);
			}
			cout << "result左眼L： " << format(result, cv::Formatter::FMT_C) << endl;
            topeye_L = top1 +"_"+ top2 +"_"+ top3 +"_"+ top4+"_"+ top5;
		}
		if (n == 1)
		{
            if ((result.at<float>(0, 0) > result.at<float>(0, 1))&&(result.at<float>(0, 0)>=0.991))  //&&( anyles <0.3)  0.991
            {
//                classProb = result.at<float>(0, 0);
                eyer_idx = 1; //闭眼结果
//				printf("\n current image classification : %s, possible : %.2f\n", labels.at(eyer_idx).c_str(), classProb);
				//putText(img_face, labels.at(eyer_idx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(255, 0, 0), 1, 1);
			}
			else
			{
//				classProb = result.at<float>(0, 1);
				eyer_idx = 0;
//				printf("\n current image classification : %s, possible : %.2f\n", labels.at(eyer_idx).c_str(), classProb);
				//putText(img_face, labels.at(eyer_idx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 255, 0), 1, 1);
			}
			cout << "result右眼R： " << format(result, cv::Formatter::FMT_C) << endl;
            topeye_R = top1 +"_"+ top2 +"_"+ top3 +"_"+ top4+"_"+ top5;
		}

		// 显示文本
		/*putText(img_face, labels.at(classidx), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 0, 255), 1, 1);*/
		// cout << "result111： " << format(result, cv::Formatter::FMT_C) << endl;
		//imshow("Image Classification", Showimages[n]);
		//waitKey(0);
	}

	//二者合一   和脸部的角度有关 暂时以“或”的关系定义
	if ((eyel_idx == 1) && (eyer_idx == 1))
	{
		
		return_idx = 1; //存在闭眼
		
		cout << "——————————************************************************************************* 识别出一次闭眼: " << return_idx << endl;
//        if (resbet18EyeCount < 8000){
//
//            int etime = getTimestamp();
//            string save_eye0_pth= "/userdata/media/eyeclose/" +   to_string(resbet18EyeCount) + "_" + to_string(abs(etime)) +"_"+ topeye_L + ".jpg";
//            string save_eye1_pth= "/userdata/media/eyeclose/" +   to_string(resbet18EyeCount) + "_" + to_string(abs(etime)) +"_"+ topeye_R + ".jpg";
//            imwrite(save_eye0_pth.c_str(), input_img[0]);
//            imwrite(save_eye1_pth.c_str(), input_img[1]);
//            resbet18EyeCount++;
//        }
//        else   {
//            exit(0);
//        }
	}
	else
	{
		return_idx = 0; //正常或睁开状态
//        if (resbet18EyeCount < 8000){
//
//            int etime = getTimestamp();
//            string save_eye0_pth= "/userdata/media/eyeopen/" +   to_string(resbet18EyeCount) + "_" + to_string(abs(etime)) +"_"+ topeye_L + ".jpg";
//            string save_eye1_pth= "/userdata/media/eyeopen/" +   to_string(resbet18EyeCount) + "_" + to_string(abs(etime)) +"_"+ topeye_R + ".jpg";
//            imwrite(save_eye0_pth.c_str(), input_img[0]);
//            imwrite(save_eye1_pth.c_str(), input_img[1]);
//            resbet18EyeCount++;
//        }
//        else   {
//            exit(0);
//        }

	}
	return return_idx;
}
int resbet18MouthCount = 0;

//识别哈欠 二分类
#ifdef DSM_USING_RKNN
int Resnet::resnet_mouth(Mat input_img, Mat img_face, vector<int> landmarks)
#else
int Resnet::resnet_mouth(dnn::Net net, Mat input_img, Mat img_face, vector<int> landmarks)
#endif
{
	Mat img_mouth;
	input_img.copyTo(img_mouth);
    if ((input_img.rows >1280)||(input_img.cols >1280))
    {
        return -1;
    }
//	img_mouth = convertTo3Channels(img_mouth);

	//处理img_eyel
	resize(img_mouth, img_mouth, Size(56, 56), INTER_AREA);
	img_mouth.convertTo(img_mouth, CV_32FC3);   // 越界报错
	PreProcess(img_mouth, img_mouth);         //标准化处理

	//将image1和image2合并到images
	vector<Mat> images;
	images.push_back(img_mouth);
	vector<String> labels = readClassNames();

	int res_weight = 56;
	int res_height = 56;

#ifdef DSM_USING_RKNN
    Mat img_mouth_rknn;
	input_img.copyTo(img_mouth_rknn);
	//img_mouth_rknn = convertTo3Channels(img_mouth_rknn);
	resize(img_mouth_rknn, img_mouth_rknn, Size(56, 56), INTER_AREA);

        //imwrite("./img_mouth_rknn.jpg", img_mouth_rknn);

    cv::cvtColor(img_mouth_rknn, img_mouth_rknn, COLOR_BGR2RGB);  //#include <opencv2/opencv.hpp>
        cv::Mat prob = rknn_infer_mt(img_mouth_rknn);
#else
	// 加载网络
	//cv::dnn::Net net = cv::dnn::readNetFromONNX(Resnet_model);  // 加载训练好的识别模型

	if (net.empty()) {
		printf("read onnx model data failure...\n");
		system("pause");
		exit(0);
	}
	Mat inputBlob = blobFromImage(img_mouth, 1.0, Size(res_weight, res_height), Scalar(0, 0, 0), false, true);

	// 执行图像分类
	net.setInput(inputBlob);
	cv::Mat prob = net.forward();     // 推理出结果
        print(prob);
	vector<double> times;
	double time = net.getPerfProfile(times);
	float ms = (time * 1000) / getTickFrequency();
	printf("current inference time : %.2f ms \n", ms);
#endif
	// 得到最可能分类输出
	int classids = -1;
	Point mouth_pos = Point(landmarks[8], landmarks[9]);

	int return_idx = 0;
	//Point3i rec;
	//rec_idx.clear();
	//增加输出类别限定
	for (int n = 0; n < prob.rows; n++) {
		Point classNumber;
		double classProb;
		Mat probMat = prob(Rect(0, n, RESNET_CLASS_NUM, 1)).clone();
		Mat result = probMat.reshape(1, 1);
		//minMaxLoc  获取最大最小值的位置 得到理想IDS  弃用
		//minMaxLoc(result, NULL, &classProb, NULL, &classNumber);
		// int classidx = classNumber.x;
		// n =0、1时 表示眼睛开闭输入  n=2、3属于嘴巴开闭  后续会增加更多类别
        float anyles = abs(result.at<float>(0, 2) - result.at<float>(0, 3));
		int mclassidx = 2; //the mouth is normal 4 is kouzjap 5 s
		if ((result.at<float>(0, 2) < result.at<float>(0, 3))&&(result.at<float>(0, 3)>0.96))  //&&( anyles <0.3)   // 0815之前  0.92
		 {
			classProb = result.at<float>(0, 3);
			mclassidx = 3;
		}
		else 
		{
            classProb = result.at<float>(0, 2);
            mclassidx = 2;
			//printf("\n current image classification : %s, possible : %.2f\n", labels.at(mclassidx).c_str(), classProb);
			//putText(img_face, labels.at(mclassidx), mouth_pos, FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 255, 255), 1, 1);
		}

		if (mclassidx == 3)
		{
			return_idx = 1; //存在打哈切
//            putText(img_face, "Gape", cvPoint(260,50), FONT_HERSHEY_SIMPLEX, 0.6, Scalar(255, 255, 255), 1, 1);
        }
		else
		{
			return_idx = 0;
		}
		cout << "result1哈切： " << format(result, cv::Formatter::FMT_C) << endl;
        string top1 = to_string(result.at<float>(0, 0)) ;
        string top2 = to_string(result.at<float>(0, 1)) ;
        string top3 = to_string(result.at<float>(0, 2)) ;
        string top4 = to_string(result.at<float>(0, 3)) ;
        string top5 = to_string(result.at<float>(0, 4)) ;
        string topcon = top1 +"_"+ top2 +"_"+ top3 +"_"+ top4+"_"+ top5;
        double min = 0,max = 0;
        minMaxLoc(result, &min, &max);

//        if (return_idx == 1){
//
//            if (resbet18MouthCount < 8000){
//                string save_mouth_pth;
//                int stime = getTimestamp();
//                save_mouth_pth = "/userdata/media/yawn/" + to_string(resbet18MouthCount) + "_" + to_string(abs(stime)) + "_" + topcon + ".jpg";
//                imwrite(save_mouth_pth.c_str(), input_img);
//                resbet18MouthCount++;
//            }
//        }
//        else
//        {
//            if (resbet18MouthCount < 8000){
//                string save_mouth_pth;
//                int stime = getTimestamp();
//                save_mouth_pth = "/userdata/media/mouth/" + to_string(resbet18MouthCount) + "_" + to_string(abs(stime)) + "_" + topcon + ".jpg";
//                imwrite(save_mouth_pth.c_str(), input_img);
//                resbet18MouthCount++;
//            }
//        }
		//imshow("Image Classification", Showimages[n]);
		//waitKey(0);
	}
	//返回值  0.1闭眼  2.3哈欠  4口罩
//	cout << "return_idx： " << return_idx << endl;
	return return_idx;
}

//识别口罩 高优先级
#ifdef DSM_USING_RKNN
int Resnet::resnet_respirator(Mat input_img, Mat img_face, vector<int> landmarks)
#else
int Resnet::resnet_respirator(cv::dnn::Net net, Mat input_img, Mat img_face, vector<int> landmarks)
#endif
{
	Mat img_mouth;
	input_img.copyTo(img_mouth);
//	img_mouth = convertTo3Channels(img_mouth);

	//处理img_eyel
	resize(img_mouth, img_mouth, Size(56, 56), INTER_AREA);
	img_mouth.convertTo(img_mouth, CV_32FC3);   // 越界报错
	PreProcess(img_mouth, img_mouth);         //标准化处理

	//将image1和image2合并到images
	vector<Mat> images;
	images.push_back(img_mouth);
	vector<String> labels = readClassNames();

	int res_weight = 56;
	int res_height = 56;

#ifdef DSM_USING_RKNN
        Mat img_mouth_rknn;
	input_img.copyTo(img_mouth_rknn);
	//img_mouth_rknn = convertTo3Channels(img_mouth_rknn);
	resize(img_mouth_rknn, img_mouth_rknn, Size(56, 56), INTER_AREA);

    cv::cvtColor(img_mouth_rknn, img_mouth_rknn, COLOR_BGR2RGB);  //#include <opencv2/opencv.hpp>
        cv::Mat prob = rknn_infer_mt(img_mouth_rknn);
#else
	// 加载网络
	//cv::dnn::Net net = cv::dnn::readNetFromONNX(Resnet_model);  // 加载训练好的识别模型

	if (net.empty()) {
		printf("read onnx model data failure...\n");
		system("pause");
		exit(0);
	}
	Mat inputBlob = blobFromImage(img_mouth, 1.0, Size(res_weight, res_height), Scalar(0, 0, 0), false, true);

	// 执行图像分类
	net.setInput(inputBlob);
	cv::Mat prob = net.forward();     // 推理出结果
        print(prob);
	vector<double> times;
	double time = net.getPerfProfile(times);
	float ms = (time * 1000) / getTickFrequency();
	printf("current inference time : %.2f ms \n", ms);
#endif

	// 得到最可能分类输出
	int classids = -1;
	Point mouth_pos = Point(landmarks[8], landmarks[9]);

	int return_idx = 0;
	//Point3i rec;
	//rec_idx.clear();
	//增加输出类别限定
	for (int n = 0; n < prob.rows; n++) {
		Point classNumber;
		double classProb;
		Mat probMat = prob(Rect(0, n, RESNET_CLASS_NUM, 1)).clone();
		Mat result = probMat.reshape(1, 1);

        //
		int mclassidx = 2; //the mouth is normal 4 is kouzjap 5 s
		if ((result.at<float>(0, 4) > result.at<float>(0, 2))&&(result.at<float>(0, 4) > result.at<float>(0, 3))&&(result.at<float>(0, 4)>=0.991))
		{
			mclassidx = 4;
		}
		else
		{
			mclassidx = 2;
		}
        string  topeye_s,top1,top2,top3,top4,top5;
        top1 = to_string(result.at<float>(0, 0)) ;
        top2 = to_string(result.at<float>(0, 1)) ;
        top3 = to_string(result.at<float>(0, 2)) ;
        top4 = to_string(result.at<float>(0, 3)) ;
        top5 = to_string(result.at<float>(0, 4)) ;
        topeye_s = top1 +"_"+ top2 +"_"+ top3 +"_"+ top4+"_"+ top5;
		if (mclassidx == 4)
		{
			return_idx = 1; //存在口罩
//            if (resbet18mouthCount < 8000){
//
//                int etime = getTimestamp();
//                string save_res_pth= "/userdata/media/resp/" +   to_string(resbet18mouthCount) + "_" + to_string(abs(etime)) +"_"+ topeye_s + ".jpg";
//
//                imwrite(save_res_pth.c_str(), input_img);
//
//                resbet18mouthCount++;
//            }
//            else   {
//                exit(0);
//            }
		}
		else
		{
			return_idx = 0;
		}
		cout << "result1口罩： " << format(result, cv::Formatter::FMT_C) << endl;

		//imshow("Image Classification", Showimages[n]);
		//waitKey(0);
	}
	return return_idx;
}

//{
//	resize(input_img, input_img, Size(256, 256), INTER_AREA);
//	input_img.convertTo(input_img, CV_8UC1);
//	Mat vec_img = convertTo3Channels(input_img);
//
//
//	vector<String> labels = readClassNames();
//	// 加载网络
//	cv::dnn::Net net = cv::dnn::readNetFromONNX(Resnet_model);  // 加载训练好的识别模型
//	if (net.empty()) {
//		printf("read onnx model data failure...\n");
//		return -1;
//	}
//	Mat inputBlob = blobFromImage(vec_img, 1.0, Size(res_weight, res_height), Scalar(0, 0, 0), false, true);
//	// 执行图像分类
//	net.setInput(inputBlob);
//	cv::Mat prob = net.forward();     // 推理出结果
//	cout <<"总类别： "<< prob.cols << endl;
//	//计算耗时
//	vector<double> times;
//	double time = net.getPerfProfile(times);
//	float ms = (time * 1000) / getTickFrequency();
//	printf("current inference time : %.2f ms \n", ms);
//	// 得到最可能分类输出
//	//以下内容可以封装成一个函数 Resnet::
//	int classids = -1;
//	vector<Point> face_mask;
//	Point eyel = Point(landmarks[0], landmarks[1]);
//	face_mask.push_back(eyel);
//	Point eyer = Point(landmarks[2], landmarks[3]);
//	face_mask.push_back(eyer);
//	Point mouth = Point(landmarks[8], landmarks[9]);
//	face_mask.push_back(mouth);
//
//	Point classNumber;
//	double classProb;
//	Mat probMat = prob(Rect(0, 0, 4, 1)).clone();
//	Mat result = probMat.reshape(1, 1);
//	minMaxLoc(result, NULL, &classProb, NULL, &classNumber);
//	int classidx = classNumber.x;
//	cout << "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%   " << classidx << endl;
//	printf("\n current image classification : %s, possible : %.2f\n", labels.at(classidx).c_str(), classProb);
//
//	// 显示文本
//	putText(img_face, labels.at(classidx), face_mask[1], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 0, 255), 1, 1);
//	imshow("Image Classification", vec_img);
//		//waitKey(0);
//	
//	return classids;
//}

////标准化处理
//vector<Mat> Resnet::pro_resize(vector<Mat> input_vec)
//{
//	vector<Mat> result;
//	for (size_t i = 0; i < input_vec.size(); i++)
//	{
//		Mat temp_img;
//		resize(input_vec[i], input_vec[i], Size(56, 56), INTER_AREA);
//		//input_vec[i].convertTo(input_vec[i], CV_32FC3);
//		//input_vec[i].convertTo(input_vec[i], CV_8UC1);
//
//		// 确定是否是三通道  需要转换
//		if (input_vec[i].channels() == 3)
//		{
//			Resnet::PreProcess(input_vec[i], temp_img);
//			result.push_back(temp_img);
//		}
//		else
//		{
//			temp_img = convertTo3Channels(input_vec[i]);
//			result.push_back(temp_img);
//		}
//
//	}
//	return result;
//}
// 图像处理  标准化处理
void Resnet::PreProcess(const Mat& image, Mat& image_blob)
{
	Mat input;
	image.copyTo(input);


	//数据处理 标准化
	std::vector<Mat> channels, channel_p;
	split(input, channels);
	Mat R, G, B;
	B = channels.at(0);
	G = channels.at(1);
	R = channels.at(2);

	B = (B / 255. - 0.406) / 0.225;
	G = (G / 255. - 0.456) / 0.224;
	R = (R / 255. - 0.485) / 0.229;

	channel_p.push_back(R);
	channel_p.push_back(G);
	channel_p.push_back(B);

	Mat outt;
	merge(channel_p, outt);
	image_blob = outt;
}

int Recong::check_cream(Mat input_img, bool &state_camover)
{
    Mat thr, image;
    cvtColor(input_img, thr, COLOR_BGR2GRAY); //Convert to gray
    /*GaussianBlur(thr, thr, Size(5, 5),0);*/
    blur(thr, thr, Size(3, 3));
    //threshold(thr, thr, 18, 255, THRESH_BINARY); //Threshold the gray
    Canny(thr, image, 18, 54, 3, true);
    //bitwise_not(thr, thr); //这里先变反转颜色

    vector<vector<Point>> contours;
    vector<Vec4i> hierarchy;
    findContours(image, contours, hierarchy, RETR_TREE, CHAIN_APPROX_SIMPLE, Point());//寻找最外层轮廓
    //Mat imageContours0 = Mat::zeros(thr.size(), CV_8UC1);	//最小外接正矩形画布
    //输出最大面积即索引
    cout << "----轮廓计数： " << contours.size() << endl;
    //cout << "最大面积max=" << g_dConArea[max] << endl;

    //绘制轮廓
    //drawContours(input_img, contours, -1, Scalar(0, 255, 0), 1, 8, hierarchy);
    bool ftstate = isImageBlurry(image);

    if ((contours.size() < 140)&&(ftstate == false))
    {
        state_camover = true;
        return 1;
    }
    else
    {
        return 0;
    }
}

bool Recong::isImageBlurry(cv::Mat& img, double threshold)
{
    cv::Mat matImageGray;
    cv::Mat dst, abs_dst;
    cv::Laplacian(img, dst, CV_16S, 3);
    cv::convertScaleAbs(dst, abs_dst);
    cv::Mat tmp_m, tmp_sd;
    double sd = 0;
    cv::meanStdDev(dst, tmp_m, tmp_sd);
    sd = tmp_sd.at<double>(0, 0); // 方差
    int value=  sd* sd ;
    if (value > threshold)
    {
        return true;
    }
    else
    {
        return false;
    }
}

