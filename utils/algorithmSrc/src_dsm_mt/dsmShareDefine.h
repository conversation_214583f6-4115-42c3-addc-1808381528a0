#ifndef _SHARE_DEFINE_H_
#define _SHARE_DEFINE_H_



#include <fstream>
#include <sstream>
#include <iostream>


#include <stdint.h>



#if (defined WIN32 || defined _WIN32 )
#else
    /*下面两个定义二选一*/
    #define DSM_G3_APP_IN_OUT
    //#define USING_ONE_IMG_TEST
    

   
    #define DSM_USING_RKNN
  #ifdef DSM_USING_RKNN
    #define DSM_USING_ENCRYPT_CHIP_XS508    /*使用加密芯片XS508*/
    //#define DSM_USING_RGA
  #endif
#endif




#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>


#define MAX(x,y) (x > y ? x : y) 
#define MIN(x,y) (x < y ? x : y)


#endif
