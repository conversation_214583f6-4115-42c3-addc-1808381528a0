//����ʽ����1 �궨��  ע�����������������
#ifndef RESNET18_H_
#define RESNET18_H_

#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>

#include "dsmShareDefine.h"




class Recong
{
public:
    static int check_cream(cv::Mat image, bool &state_camover);
    static bool isImageBlurry(cv::Mat& img, double threshold = 42000);
};


class Resnet
{
public:
	static std::vector<cv::String> readClassNames();
//	static std::vector<cv::Mat> pro_resize(std::vector<cv::Mat> input_vec);
	static void PreProcess(const cv::Mat& image, cv::Mat& image_blob);
	static cv::Point resnet_result(std::vector<cv::Mat> input_img, cv::Mat img_face, std::vector<int> landmarks);
#ifdef DSM_USING_RKNN
    static int resnet_eye(std::vector<cv::Mat> input_img, cv::Mat img_face, std::vector<int> landmarks);
	static int resnet_mouth(cv::Mat input_img, cv::Mat img_face, std::vector<int> landmarks);
	static int resnet_respirator(cv::Mat input_img, cv::Mat img_face, std::vector<int> landmarks);
#else
	static int resnet_eye(dnn::Net net, std::vector<cv::Mat> input_img, cv::Mat img_face, std::vector<int> landmarks);
	static int resnet_mouth(dnn::Net net, cv::Mat input_img, cv::Mat img_face, std::vector<int> landmarks);
	static int resnet_respirator(dnn::Net net, cv::Mat input_img, cv::Mat img_face, std::vector<int> landmarks);
#endif
	
	//������๦��
private:
	cv::Size input_size;
#ifdef USING_RKNN
#else
	cv::dnn::Net net;
#endif
	cv::Scalar default_mean;
	cv::Scalar default_std;
	std::vector<cv::String> labels;



};

#endif // RESNET18_H_
