#include <opencv2/opencv.hpp>
#include <string>
#include "get_alarm.h"
#include "resnet18.h"
//#include "yolov5s.h"
#include "putcnText.h"
#include "myparams.h"
#include <unistd.h>

using namespace std;
using namespace cv;

extern globals globalparams;
extern String hand_model;
alarm_timer respirator_timer, mouth_timer, eye_timer, yaw_timer, camcover_timer, leave_timer;



//����ʶ��0215
int Talarm::alarm_respirator(face_message face_info) {
    int out_idx = 0;
    if (respirator_timer.freeze_state == false) {

        out_idx = Resnet::resnet_respirator(face_info.face_roi[2], face_info.frame, face_info.landmarks);

    }

    //ʹ�ö��д洢������Ϣ
    respirator_timer.ltime_idx.push_back(out_idx);
    if ((out_idx < 0) || (respirator_timer.ltime_idx.size() <= 0)) {
        return -1;
    }

    //��2�� ��������ʵ�� ###############################################

    //�̶�ʱ���� ֡��   3s����֡�ʺͱ�ʶ�𱨾�����
    string waringtxt;
    //int myparams_time = 6000;  //�¼�����ʱ��
    //int degree_params = 20;  //ʱ�����ʼ��
    int timestamp  = 0;  //ʱ�����ʼ��
    //star����֬
    if ((respirator_timer.recog_star_time) && (respirator_timer.freeze_state == false)) {
        //��ʶ�����趨ʱ�䱻���ʱ����Ҫ���»ع鵽δʶ��״̬
        if (face_info.state == 2) {
            respirator_timer.ltime_idx.clear();
            respirator_timer.recog_star_time = 0;
            respirator_timer.freeze_state = false;
            return 2;
        }
        timestamp = getTimestamp() - respirator_timer.recog_star_time;
//		cout << "*************** ���롾ʶ��һ�ι�Ƿ�¼���״̬: " << getTimestamp() << endl;
//		cout << "*************** ʶ����ʱ��: " << timestamp << endl;
        if ((timestamp - globalparams.event_respirator_time)>1000) {
            respirator_timer.ltime_idx.clear();
            respirator_timer.recog_star_time = 0;
            respirator_timer.freeze_state = false;
            return 2;
        }

        if (timestamp >= globalparams.event_respirator_time) {
            //��ȡ�¼�״̬  ������Ƶ��ռ�ȣ���λ�ã�λ��ê����
            int rate_alarm = Talarm::get_event(respirator_timer.ltime_idx);
//			cout << "%%%%%%%%%%%%%% ��֡�ʣ� " << respirator_timer.ltime_idx.size() << endl;
//			cout << "%%%%%%%%%%%%%% ƫ��ռ�ȣ� " << rate_alarm << endl;
            if (rate_alarm > globalparams.respirator_sensitivity) {
                //���������Ϣ 2���� Ƶ��ռ�ȴ�30%  ��Ϊ�ô�ʱ�䱻����Ϊ��Ƿ��Ϊ
                waringtxt = "Waring: Yaw" + to_string(rate_alarm) + "%";
                respirator_timer.alarm_state = true;
            }
            respirator_timer.recog_star_time = 0;
            respirator_timer.ltime_idx.clear();
            respirator_timer.freeze_star_time = getTimestamp();
            respirator_timer.freeze_state = true;
            return 1;
        }
#if (defined WIN32 || defined _WIN32 )
        //string ratetxt = "Recog_Yaw_State ";
        //putText(face_info.frame, ratetxt, Point(10, 110), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 205, 155), 1, 8, 0);
        putTextZH(face_info.frame, "[4]�������ʶ��", Point(10, 110), Scalar(255, 205, 155), 18, "����");
#endif
    }
    //�����һ������ ����ʱ�����
    if (respirator_timer.ltime_idx.size() == 1) {
        //ͳ��һ������ʶ�𱨾�����ʼʱ��
        if (respirator_timer.ltime_idx[0] == 1)
            //if ((ltime_idx[0].x == 0) || (ltime_idx[0].y == 3))
        {
            //��ӡ��һ�α������ֵ�ʱ���
            respirator_timer.recog_star_time = getTimestamp();
//			cout << "###ʶ�𵽵�һ�δ��Ƿ����¼�µ�ǰ��ʼʱ���: " << getTimestamp() << endl;
        } else {
            respirator_timer.ltime_idx.clear();
            respirator_timer.recog_star_time = 0;
        }
    }

    //��ȴʱ��,������ʶ��һ���¼���ɺ󣬿�ʼ��ȴ��
    //cout << "#############��ȴ��ʼʱ�䣺" << respirator_timer.freeze_star_time << endl;
    //int freeze_mouth_time = 4000; //ms  mydefine_params
    int resp_cool_end = getTimestamp();
    int temp_timm = resp_cool_end - respirator_timer.freeze_star_time;
    //cout << "#############�ۼ�ʱ�䣺" << temp_timm << endl;
    if ((temp_timm >= globalparams.freeze_respirator_time) && (respirator_timer.freeze_star_time)) {
        //��ȴʱ�������ʼ��һ�ֵ�ʶ��
        respirator_timer.freeze_state = false;
        respirator_timer.alarm_state = false;
        respirator_timer.freeze_star_time = 0;
        // return 1;
    }
    if (respirator_timer.freeze_state == true) {
        if (respirator_timer.alarm_state == true)  // &&(mouth_timer.recog_star_time == 0)
        {
#if (defined WIN32 || defined _WIN32 )
            putTextZH(face_info.frame, "������α���", Point(180, 110), Scalar(2, 0, 255), 18, "����");
#endif
        } else {
#if (defined WIN32 || defined _WIN32 )
            putTextZH(face_info.frame, "�ޱ���", Point(180, 110), Scalar(2, 0, 255), 18, "����");
#endif
        }
#if (defined WIN32 || defined _WIN32 )
        //string freezetxt = "Yaw_Freeze_Time!!! " + waringtxt;
        //putText(face_info.frame, freezetxt, Point(10, 130), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 70, 55), 1, 8, 0);
        putTextZH(face_info.frame, "�������-��ȴʱ��", Point(10, 110), Scalar(255, 70, 55), 18, "����");
        //break;
#endif
    }
}

////���ֱ���
//#ifdef DSM_USING_RKNN
//int Talarm::alarm_respirator(face_message face_info)
//#else
//int Talarm::alarm_respirator(dnn::Net resnet18,face_message face_info)
//#endif
//{
//    int out_idx = 0;
//
//    if (respirator_timer.freeze_state == false) {
//#ifdef DSM_USING_RKNN
//        out_idx = Resnet::resnet_respirator(face_info.face_roi[2], face_info.frame, face_info.landmarks);
//#else
//        out_idx = Resnet::resnet_respirator(resnet18, face_info.face_roi[2], face_info.frame, face_info.landmarks);
//#endif
//        if (out_idx == 1) {
//            return 1;
//#if (defined WIN32 || defined _WIN32 )
//            putTextZH(face_info.frame, "[1]����״̬", Point(10, 210), Scalar(20, 245, 225), 18, "����");
//#endif
//            respirator_timer.alarm_state = true;
//        } else {
//#if (defined WIN32 || defined _WIN32 )
//            //putTextZH(face_info.frame, "����״̬", Point(10, 210), Scalar(20, 245, 225), 18, "����");
//#endif
//        }
//
//    }
//    return 0;
//
//}

#ifdef DSM_USING_RKNN

int Talarm::alarm_eye(face_message face_info)
#else
int Talarm::alarm_eye(dnn::Net net, face_message face_info)
#endif
{
    int out_idx = 0;
        if (eye_timer.freeze_state == false) {
#ifdef DSM_USING_RKNN
            out_idx = Resnet::resnet_eye(face_info.face_roi, face_info.frame, face_info.landmarks);
#else
            out_idx = Resnet::resnet_eye(net, face_info.face_roi, face_info.frame, face_info.landmarks);
#endif
        }


    //ʹ�ö��д洢������Ϣ
    eye_timer.ltime_idx.push_back(out_idx);
    if ((out_idx < 0) || (eye_timer.ltime_idx.size() <= 0)) {
        return -1;
    }
    cout << "MT:_____________________________*************** resnet_eye = out_idx: " << out_idx << endl;
    // cout << "*************** eye_timer.ltime_idx.size() : " << eye_timer.ltime_idx.size()  << endl;
    //��2�� ��������ʵ�� ###############################################



    //�̶�ʱ���� ֡��   3s����֡�ʺͱ�ʶ�𱨾�����
    //int myparams_time = 5000;  //�¼�����ʱ��
    int timestamp  = 0;  //ʱ�����ʼ��
    //int eye_degree = 40;  //գ��Ƶ��ռ��
    string waringtxt;
    //star����֬
    if ((eye_timer.recog_star_time) && (eye_timer.freeze_state == false)) {    //��ʶ�����趨ʱ�䱻���ʱ����Ҫ���»ع鵽δʶ��״̬
       printf("**********XU in line %d  \n", __LINE__);
        if (face_info.state == 2) {
            eye_timer.ltime_idx.clear();
            eye_timer.recog_star_time = 0;
            eye_timer.freeze_state = false;
            printf("**********XU in line %d  \n", __LINE__);
            return 2;
        }
        timestamp = getTimestamp() - eye_timer.recog_star_time;
		cout << "*************** EYE进入【识别一次事件】状态: " << getTimestamp() << endl;
		cout << "*************** 识别历时长: " << timestamp << endl;
        if ((timestamp - globalparams.event_eye_time)>6000) {
            eye_timer.ltime_idx.clear();
            eye_timer.recog_star_time = 0;
            eye_timer.freeze_state = false;
            printf("**********XU in line %d  \n", __LINE__);
            return 2;
        }

        if (timestamp >= globalparams.event_eye_time) {
            //��ȡ�¼�״̬  ������Ƶ��ռ�ȣ���λ�ã�λ��ê����
            printf("**********XU in line %d  \n", __LINE__);
            bool stat_befirst = false;
            int rate_alarm = Talarm::get_event_mode(eye_timer.ltime_idx,stat_befirst);
			cout << "MT__________________%%%%%%%%%%%%%% 总帧率： " << eye_timer.ltime_idx.size() << endl;
			cout << "MT__________________%%%%%%%%%%%%%% 闭眼占比： " << rate_alarm << endl;
            if ((rate_alarm > globalparams.eye_sensitivity)&&(stat_befirst == true)) {
                ////输出报警信息 2秒内 频次占比达30%  认为该此时间被定义为哈欠行为
                waringtxt = "Waring: Close" + to_string(rate_alarm) + "%";
                eye_timer.alarm_state = true;

                int etime = getTimestamp();
                string eye_log = to_string(abs(etime)) + "_"+ to_string(eye_timer.ltime_idx.size())+ "_"+to_string(rate_alarm) + "_"+ to_string(globalparams.eye_sensitivity);
                printLog::Write(eye_log);
                printf("**********XU in line %d  \n", __LINE__);
            }
            eye_timer.recog_star_time = 0;
            eye_timer.ltime_idx.clear();
            eye_timer.freeze_star_time = getTimestamp();
            eye_timer.freeze_state = true;
            printf("**********XU in line %d  \n", __LINE__);
            // usleep(5000000);
            // return 1;   // 0814
        }
#if (defined WIN32 || defined _WIN32 )
        //string ratetxt = "Recog_Eye_State";
        //putText(face_info.frame, ratetxt, Point(10, 30), FONT_HERSHEY_COMPLEX, 0.6, Scalar(20, 245, 125), 1, 8, 0);
        putTextZH(face_info.frame, "[2]����ʶ��", Point(10, 30), Scalar(20, 245, 125), 18, "����");
#endif

    }
    //�����һ������ ����ʱ�����
    if (eye_timer.ltime_idx.size() == 1) {
        //ͳ��һ������ʶ�𱨾�����ʼʱ��
        if (eye_timer.ltime_idx[0] == 1)
            //if ((ltime_idx[0].x == 0) || (ltime_idx[0].y == 3))
        {
            //��ӡ��һ�α������ֵ�ʱ���
            eye_timer.recog_star_time = getTimestamp();
//			cout << "###ʶ�𵽵�һ�α��ۣ���¼�µ�ǰ��ʼʱ���: " << getTimestamp() << endl;
        } else {
            eye_timer.ltime_idx.clear();
            eye_timer.recog_star_time = 0;
        }
    }

    //��ȴʱ��,������ʶ��һ���¼���ɺ󣬿�ʼ��ȴ��
    //cout << "#############��ȴ��ʼʱ�䣺" << eye_timer.freeze_star_time << endl;
    //int freeze_eye_time = 1000; //ms  mydefine_params
    int eye_cool_end = getTimestamp();
    int temp_timm = eye_cool_end - eye_timer.freeze_star_time;
    //cout << "#############�ۼ�ʱ�䣺" << temp_timm << endl;
    if ((temp_timm >= globalparams.freeze_eye_time) && (eye_timer.freeze_star_time)) {
        //��ȴʱ�������ʼ��һ�ֵ�ʶ��
        if (eye_timer.alarm_state == true) {
            int etime = getTimestamp();
            string eye_log = to_string(abs(etime)) + "_" + to_string(eye_timer.alarm_state);
            printLog::Write(eye_log);

            eye_timer.freeze_state = false;
            eye_timer.alarm_state = false;
            eye_timer.freeze_star_time = 0;
            printf("**********XU in line %d  \n", __LINE__);
            return 1;  //0814
        }
        eye_timer.freeze_state = false;
        eye_timer.alarm_state = false;
        eye_timer.freeze_star_time = 0;

//        int etime = getTimestamp();
//        string eye_log = to_string(abs(etime)) + "_yyy_" + to_string(eye_timer.alarm_state);
//        printLog::Write(eye_log);
printf("**********XU in line %d  \n", __LINE__);
        return 0;
    }
    printf("**********XU in line %d  \n", __LINE__);
    if (eye_timer.freeze_state == true) {
        if (eye_timer.alarm_state == true)  // &&(mouth_timer.recog_star_time == 0)
        {
#if (defined WIN32 || defined _WIN32 )
            putTextZH(face_info.frame, "���۱���", Point(150, 30), Scalar(2, 0, 255), 18, "����");
#endif
        } else {
#if (defined WIN32 || defined _WIN32 )
            putTextZH(face_info.frame, "�ޱ���", Point(150, 30), Scalar(2, 0, 255), 18, "����");
#endif
        }
#if (defined WIN32 || defined _WIN32 )
        //string freezetxt = "Eye_Freeze_Time!!! ";
        //putText(face_info.frame, freezetxt, Point(10, 50), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 70, 55), 1, 8, 0);
        putTextZH(face_info.frame, "����-��ȴʱ��", Point(10, 30), Scalar(255, 70, 55), 18, "����");
        //break;
#endif
    }
    return 0;
}

#ifdef DSM_USING_RKNN

int Talarm::alarm_mouth(face_message face_info)
#else
int Talarm::alarm_mouth(dnn::Net net, face_message face_info)
#endif
{
    int out_idx = 0;
    if (mouth_timer.freeze_state == false) {
#ifdef DSM_USING_RKNN
        out_idx = Resnet::resnet_mouth(face_info.face_roi[2], face_info.frame, face_info.landmarks);
#else
        out_idx = Resnet::resnet_mouth(net, face_info.face_roi[2], face_info.frame, face_info.landmarks);
#endif
    }
//    if (face_info.face_roi.empty() || (face_info.state == 2)) {
//        //ͼƬΪ�գ����������ʱ
//    } else {
//        if (mouth_timer.freeze_state == false) {
//#ifdef DSM_USING_RKNN
//            out_idx = Resnet::resnet_mouth(face_info.face_roi[2], face_info.frame, face_info.landmarks);
//#else
//            out_idx = Resnet::resnet_mouth(net, face_info.face_roi[2], face_info.frame, face_info.landmarks);
//#endif
//        }
//    }
    //ʹ�ö��д洢������Ϣ
    mouth_timer.ltime_idx.push_back(out_idx);
    if ((out_idx < 0) || (mouth_timer.ltime_idx.size() <= 0)) {
        return -1;
    }

    //��2�� ��������ʵ�� ###############################################


    //�̶�ʱ���� ֡��   3s����֡�ʺͱ�ʶ�𱨾�����
    string waringtxt;
    //int myparams_time = 6000;  //�¼�����ʱ��
    //int degree_params = 40;  //ʱ�����ʼ��
    int timestamp  = 0;  //ʱ�����ʼ��
    //star����֬
    if ((mouth_timer.recog_star_time) && (mouth_timer.freeze_state == false)) {
        //��ʶ�����趨ʱ�䱻���ʱ����Ҫ���»ع鵽δʶ��״̬
        if (face_info.state == 2) {
            mouth_timer.ltime_idx.clear();
            mouth_timer.recog_star_time = 0;
            mouth_timer.freeze_state = false;
            return 2;
        }
        timestamp = getTimestamp() - mouth_timer.recog_star_time;
//		cout << "*************** ���롾ʶ��һ�ι�Ƿ�¼���״̬: " << getTimestamp() << endl;
//		cout << "*************** ʶ����ʱ��: " << timestamp << endl;
        if ((timestamp - globalparams.event_mouth_time)>1000) {
            mouth_timer.ltime_idx.clear();
            mouth_timer.recog_star_time = 0;
            mouth_timer.freeze_state = false;
            return 2;
        }
        if (timestamp >= globalparams.event_mouth_time) {
            //��ȡ�¼�״̬  ������Ƶ��ռ�ȣ���λ�ã�λ��ê����
            int rate_alarm = Talarm::get_event(mouth_timer.ltime_idx);
//			cout << "%%%%%%%%%%%%%% ��֡�ʣ� " << mouth_timer.ltime_idx.size() << endl;
//			cout << "%%%%%%%%%%%%%% ��Ƿռ�ȣ� " << rate_alarm << endl;
            if (rate_alarm > globalparams.mouth_sensitivity) {
                //���������Ϣ 2���� Ƶ��ռ�ȴ�30%  ��Ϊ�ô�ʱ�䱻����Ϊ��Ƿ��Ϊ
                waringtxt = "Waring: Yawn" + to_string(rate_alarm) + "%";
                mouth_timer.alarm_state = true;
            }
            mouth_timer.recog_star_time = 0;
            mouth_timer.ltime_idx.clear();
            mouth_timer.freeze_star_time = getTimestamp();
            mouth_timer.freeze_state = true;
            return 1;
        }
#if (defined WIN32 || defined _WIN32 )
        //string ratetxt = "Recog_Mouth_State ";
        //putText(face_info.frame, ratetxt, Point(10, 70), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 205, 155), 1, 8, 0);
        putTextZH(face_info.frame, "[3]��Ƿ״̬ʶ��", Point(10, 70), Scalar(255, 205, 155), 18, "����");
#endif
    }
    //�����һ������ ����ʱ�����
    if (mouth_timer.ltime_idx.size() == 1) {
        //ͳ��һ������ʶ�𱨾�����ʼʱ��
        if (mouth_timer.ltime_idx[0] == 1)
            //if ((ltime_idx[0].x == 0) || (ltime_idx[0].y == 3))
        {
            //��ӡ��һ�α������ֵ�ʱ���
            mouth_timer.recog_star_time = getTimestamp();
//			cout << "###ʶ�𵽵�һ�δ��Ƿ����¼�µ�ǰ��ʼʱ���: " << getTimestamp() << endl;
        } else {
            mouth_timer.ltime_idx.clear();
            mouth_timer.recog_star_time = 0;
        }
    }

    //��ȴʱ��,������ʶ��һ���¼���ɺ󣬿�ʼ��ȴ��
    //cout << "#############��ȴ��ʼʱ�䣺" << mouth_timer.freeze_star_time << endl;
    //int freeze_mouth_time = 1000; //ms  mydefine_params
    int eye_cool_end = getTimestamp();
    int temp_timm = eye_cool_end - mouth_timer.freeze_star_time;
    //cout << "#############�ۼ�ʱ�䣺" << temp_timm << endl;
    if ((temp_timm >= globalparams.freeze_mouth_time) && (mouth_timer.freeze_star_time)) {
        //��ȴʱ�������ʼ��һ�ֵ�ʶ��
        mouth_timer.freeze_state = false;
        mouth_timer.alarm_state = false;
        mouth_timer.freeze_star_time = 0;
        // return 1;
    }

    if (mouth_timer.freeze_state == true) {
        //string freezetxt = "Mouth_Freeze_Time!!! " + waringtxt;
        //putText(face_info.frame, waringtxt, Point(10, 110), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 20, 10), 1, 8, 0);
        if (mouth_timer.alarm_state == true)  // &&(mouth_timer.recog_star_time == 0)
        {
#if (defined WIN32 || defined _WIN32 )
            putTextZH(face_info.frame, "��Ƿ����", Point(150, 70), Scalar(2, 0, 255), 18, "����");
#endif
        } else {
#if (defined WIN32 || defined _WIN32 )
            putTextZH(face_info.frame, "�ޱ���", Point(150, 70), Scalar(2, 0, 255), 18, "����");
#endif
        }
#if (defined WIN32 || defined _WIN32 )
        putTextZH(face_info.frame, "��Ƿ-��ȴʱ��", Point(10, 70), Scalar(255, 25, 25), 18, "����");
#endif
        //break;
    }

    //return 0;
}

//左顾右盼识别和冷却时间
int Talarm::alarm_lookaround(face_message face_info) {
    int out_idx = 0;
    // cout << "MT:   *************** 人脸角度face_info.face_angle[0]): " << face_info.face_angle[0] << endl;
    int reduce = face_info.landmarks[5] - (face_info.landmarks[1]+face_info.landmarks[3])*0.5; //�޳�̧ͷ��ͷ����
    // std::cout<<"mt----------------------------------------------------------------reduceת��Ƕȹ��� reduce "<< reduce <<std::endl;
    if (yaw_timer.freeze_state == false) {
        if ((abs(face_info.face_angle[0]) > 47)&&(reduce > 29)) //18 ��������Ϊ������ε������Ȳ���
        {
            out_idx = 1; //ת��Ƕȹ��� �������
        }
        //ת����󣬳���ʶ�� 0618
        // if (((face_info.landmarks[4]+90)<face_info.landmarks[0])||((face_info.landmarks[4]-90)>face_info.landmarks[2]))
        // {
        //     // std::cout<<"mt------------------------------------------------------------ת��Ƕȹ��� "<< face_info.landmarks[4] <<std::endl;
        //     out_idx = 1; //ת��Ƕȹ��� �������
        // }
        //��ͷ����
//        int hand_up = face_info.landmarks[1] > face_info.landmarks[3]?face_info.landmarks[1] : face_info.landmarks[3] ;
//        if (abs(face_info.landmarks[5]-hand_up)<10)
//        {
//            out_idx = 1; //ת��Ƕȹ��� �������
//        }
        //��ͷʶ����ʱ��ʹ��
//        int hand_down = face_info.landmarks[7] > face_info.landmarks[9]?face_info.landmarks[7] : face_info.landmarks[9] ;
//        if (abs(hand_down - face_info.landmarks[5])<10)
//        {
//            out_idx = 1; //ת��Ƕȹ��� �������
//        }

    }

    //使用队列存储报警信息
    yaw_timer.ltime_idx.push_back(out_idx);
    cout << "MT:   *************** 人脸角度out_idx " << out_idx<< endl;
    if ((out_idx < 0) || (yaw_timer.ltime_idx.size() <= 0)) {
        return -1;
    }

    //��2�� ��������ʵ�� ###############################################

    //�̶�ʱ���� ֡��   3s����֡�ʺͱ�ʶ�𱨾�����
    string waringtxt;
    //int myparams_time = 6000;  //�¼�����ʱ��
    //int degree_params = 20;  //ʱ�����ʼ��
    int timestamp  = 0;  //ʱ�����ʼ��
    //star����֬
    if ((yaw_timer.recog_star_time) && (yaw_timer.freeze_state == false)) {
        //��ʶ�����趨ʱ�䱻���ʱ����Ҫ���»ع鵽δʶ��״̬
        if (face_info.state == 2) {
            yaw_timer.ltime_idx.clear();
            yaw_timer.recog_star_time = 0;
            yaw_timer.freeze_state = false;
            return 2;
        }
        timestamp = getTimestamp() - yaw_timer.recog_star_time;
		cout << "*************** 进入【识别一次哈欠事件】状态: " << getTimestamp() << endl;
		cout << "*************** 识别历时长: " << timestamp << endl;
        if ((timestamp - globalparams.event_lookaround_time)>1000) {
            yaw_timer.ltime_idx.clear();
            yaw_timer.recog_star_time = 0;
            yaw_timer.freeze_state = false;
            return 2;
        }

        if (timestamp >= globalparams.event_lookaround_time) {
            //��ȡ�¼�״̬  ������Ƶ��ռ�ȣ���λ�ã�λ��ê����
            int rate_alarm = Talarm::get_event(yaw_timer.ltime_idx);
//			cout << "%%%%%%%%%%%%%% ��֡�ʣ� " << yaw_timer.ltime_idx.size() << endl;
//			cout << "%%%%%%%%%%%%%% ƫ��ռ�ȣ� " << rate_alarm << endl;
            if (rate_alarm > globalparams.lookaround_sensitivity) {
                //���������Ϣ 2���� Ƶ��ռ�ȴ�30%  ��Ϊ�ô�ʱ�䱻����Ϊ��Ƿ��Ϊ
                waringtxt = "Waring: Yaw" + to_string(rate_alarm) + "%";
                yaw_timer.alarm_state = true;
            }
            yaw_timer.recog_star_time = 0;
            yaw_timer.ltime_idx.clear();
            yaw_timer.freeze_star_time = getTimestamp();
            yaw_timer.freeze_state = true;
            return 1;
        }
#if (defined WIN32 || defined _WIN32 )
        //string ratetxt = "Recog_Yaw_State ";
        //putText(face_info.frame, ratetxt, Point(10, 110), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 205, 155), 1, 8, 0);
        putTextZH(face_info.frame, "[4]�������ʶ��", Point(10, 110), Scalar(255, 205, 155), 18, "����");
#endif
    }
    //�����һ������ ����ʱ�����
    if (yaw_timer.ltime_idx.size() == 1) {
        //统计一个哈切识别报警的起始时间  
        if (yaw_timer.ltime_idx[0] == 1)
            //if ((ltime_idx[0].x == 0) || (ltime_idx[0].y == 3))
        {
            //��ӡ��һ�α������ֵ�ʱ���
            yaw_timer.recog_star_time = getTimestamp();
			cout << "###识别到第一次分神：记录下当前起始时间戳: " << getTimestamp() << endl;
        } else {
            yaw_timer.ltime_idx.clear();
            yaw_timer.recog_star_time = 0;
        }
    }

    //��ȴʱ��,������ʶ��һ���¼���ɺ󣬿�ʼ��ȴ��
    //cout << "#############��ȴ��ʼʱ�䣺" << yaw_timer.freeze_star_time << endl;
    //int freeze_mouth_time = 4000; //ms  mydefine_params
    int eye_cool_end = getTimestamp();
    int temp_timm = eye_cool_end - yaw_timer.freeze_star_time;
    //cout << "#############�ۼ�ʱ�䣺" << temp_timm << endl;
    if ((temp_timm >= globalparams.freeze_lookaround_time) && (yaw_timer.freeze_star_time)) {
        //��ȴʱ�������ʼ��һ�ֵ�ʶ��
        yaw_timer.freeze_state = false;
        yaw_timer.alarm_state = false;
        yaw_timer.freeze_star_time = 0;
        // return 1;
    }
    if (yaw_timer.freeze_state == true) {
        if (yaw_timer.alarm_state == true)  // &&(mouth_timer.recog_star_time == 0)
        {
#if (defined WIN32 || defined _WIN32 )
            putTextZH(face_info.frame, "������α���", Point(180, 110), Scalar(2, 0, 255), 18, "����");
#endif
        } else {
#if (defined WIN32 || defined _WIN32 )
            putTextZH(face_info.frame, "�ޱ���", Point(180, 110), Scalar(2, 0, 255), 18, "����");
#endif
        }
#if (defined WIN32 || defined _WIN32 )
        //string freezetxt = "Yaw_Freeze_Time!!! " + waringtxt;
        //putText(face_info.frame, freezetxt, Point(10, 130), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 70, 55), 1, 8, 0);
        putTextZH(face_info.frame, "�������-��ȴʱ��", Point(10, 110), Scalar(255, 70, 55), 18, "����");
        //break;
#endif
    }
}

//���ʶ��  ������һ�������ſ����������ʶ��
int Talarm::alarm_facemissing(face_message face_info, Mat frame) {
    int out_idx = 0;
    bool draw_init = false;
    if (face_info.face_roi.empty() || (face_info.state == 2)) {
        //ͼƬΪ�գ����������ʱ
        out_idx = 1;
    } else {
        if (leave_timer.freeze_state == false) {
            out_idx = 1; //ת��Ƕȹ��� �������
        }
    }

    if(face_info.state == 3)
    {
        leave_timer.freeze_state = false;
        leave_timer.freeze_star_time = 0;
        leave_timer.recog_star_time = 0;
        leave_timer.ltime_idx.clear();
        leave_timer.alarm_state = false;
        leave_timer.freeze_star_time = 0;
        out_idx = 1;
        std::cout << "________MT:*************** face_info.state == 3   "  << endl;
//        return 1;
    }
    //ʹ�ö��д洢������Ϣ
    leave_timer.ltime_idx.push_back(out_idx);
    if ((out_idx < 0) || (leave_timer.ltime_idx.size() <= 0)) {
        return -1;
    }
    // std::cout << "________MT:*************** leave_timer.ltime_idx  " << leave_timer.ltime_idx.size() << endl;
    // std::cout << "________MT:*************** out_idx " << out_idx << endl;
	//【2】 报警策略实现 ###############################################
	//固定时间内 帧率   3s内总帧率和被识别报警次数
    string waringtxt;
    //int myparams_time = 15000;  //����¼�����ʱ��
    int timestamp  = 0;  //ʱ�����ʼ��
    //star����֬
    if ((leave_timer.recog_star_time) && (leave_timer.freeze_state == false)) {
        //��ʶ�����趨ʱ�䱻���ʱ����Ҫ���»ع鵽δʶ��״̬
        if (face_info.state == 1) {
            leave_timer.ltime_idx.clear();
            leave_timer.recog_star_time = 0;
            leave_timer.freeze_state = false;
            return 2;
        }
        timestamp = getTimestamp() - leave_timer.recog_star_time;
		 std::cout << "________MT:*************** getTimestamp():   " << getTimestamp() << endl;
         std::cout << "________MT:*************** leave_timer.recog_star_time: " << leave_timer.recog_star_time << endl;
		std::cout << "________MT:*************** timestamp: " << timestamp << endl;
        if ((timestamp - globalparams.event_facemissing_time)>1000) {
            leave_timer.ltime_idx.clear();
            leave_timer.recog_star_time = 0;
            leave_timer.freeze_state = false;
            int ds = timestamp - globalparams.event_facemissing_time;
            std::cout << "________++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++MT:*************** timestamp超时时间: " << ds << endl;
            return 2;
        }

        if (timestamp >= globalparams.event_facemissing_time) {
            //��ȡ�¼�״̬  ������Ƶ��ռ�ȣ���λ�ã�λ��ê����
            int rate_alarm = Talarm::get_event(leave_timer.ltime_idx);
//			cout << "%%%%%%%%%%%%%% ��֡�ʣ� " << leave_timer.ltime_idx.size() << endl;
//			cout << "%%%%%%%%%%%%%% ���ռ�ȣ� " << rate_alarm << endl;
            if (!face_info.frame.empty()) {
                //���ٴ���һ������ʶ��󷽿ɴ��ݱ�����Ϣ
                waringtxt = "Waring: Yawn" + to_string(rate_alarm) + "%";
                leave_timer.alarm_state = true;
                //������Ҫ���ɱ����¼�������ʱ��  Ƶ��  �ֲ�����Ϣ
            }
            
            std::cout << "________<_<_<_<<<<<<<<<<<<<<<<<<<------>>>>>>>>>>>>>>>>>MT发送一次报警: *************** timestamp: " << timestamp << endl;
           

            leave_timer.recog_star_time = 0;
            leave_timer.ltime_idx.clear();
            leave_timer.freeze_star_time = getTimestamp();
            leave_timer.freeze_state = true;
            // usleep(3000000);
            return 1;
            //return 2;
        }

        string ratetxt = "Recog_Leave_State ";
#if (defined WIN32 || defined _WIN32 )
        //putText(face_info.frame, ratetxt, Point(10, 70), FONT_HERSHEY_COMPLEX, 0.6, Scalar(0, 0, 255), 1, 8, 0);
        putTextZH(face_info.frame, "[5]���״̬ʶ��", Point(10, 90), Scalar(0, 0, 255), 18, "����");
#endif

        //��ʼ״̬������ʱ���������ʶ���޷���ͼ����������Ǽ���һ���ظ���ͼͼƬ
        //putText(frame, ratetxt, Point(10, 70), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 205, 155), 1, 8, 0);
        //namedWindow("kWinName", WINDOW_NORMAL);
        //imshow("kWinName", frame);
    }

    //存入第一个数据 当作时间起点
    if (leave_timer.ltime_idx.size() == 1) {
        //统计一个离岗识别报警的起始时间 
        if (leave_timer.ltime_idx[0] == 1) {
            //打印第一次报警出现的时间戳
			leave_timer.recog_star_time = getTimestamp();
			cout << "###识别到第一次离岗：记录下当前起始时间戳: " << getTimestamp() << endl;
            
        } else {
            leave_timer.ltime_idx.clear();
            leave_timer.recog_star_time = 0;
        }
    }

    //冷却时间,必须是识别一次事件完成后，开始冷却，

    //int freeze_facemissing_time = 5000; //ms  mydefine_params
    if (leave_timer.freeze_star_time) {
        cout << "#############冷却开始时间：" << leave_timer.freeze_star_time << endl;
		int eye_cool_end = getTimestamp();
		int temp_timm = eye_cool_end - leave_timer.freeze_star_time;
        cout << "#############离岗累计时间：" << temp_timm << endl;

        if (temp_timm >= globalparams.freeze_facemissing_time) {
            //��ȴʱ�������ʼ��һ�ֵ�ʶ��
            leave_timer.freeze_state = false;
            leave_timer.freeze_star_time = 0;
            leave_timer.recog_star_time = 0;
            leave_timer.ltime_idx.clear();
            leave_timer.alarm_state = false;
            leave_timer.freeze_star_time = 0;
            // usleep(3000000);
            // return 1;
        }
        if (leave_timer.freeze_state == true) {
            if (leave_timer.alarm_state == true)  // &&(mouth_timer.recog_star_time == 0)
            {
#if (defined WIN32 || defined _WIN32 )
                putTextZH(face_info.frame, "��ڱ���", Point(150, 90), Scalar(2, 0, 255), 18, "����");
#endif
            } else {
#if (defined WIN32 || defined _WIN32 )
                putTextZH(face_info.frame, "�ޱ���", Point(150, 90), Scalar(2, 0, 255), 18, "����");
#endif
            }
#if (defined WIN32 || defined _WIN32 )
            //string freezetxt = "Leave_Freeze_Time!!! " + waringtxt;
            //putText(face_info.frame, freezetxt, Point(10, 110), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 70, 55), 1, 8, 0);
            putTextZH(face_info.frame, "���-��ȴʱ��", Point(10, 90), Scalar(255, 70, 55), 18, "����");
            //break;
#endif
        }
    }
}

//����ͷ�ڵ�ʶ��
int Talarm::alarm_camcover(face_message face_info, Mat frame)
{
    int out_idx = 0;
    bool draw_init = false;
    bool state_camover = false;
    if (camcover_timer.freeze_state == false)
    {
        out_idx = Recong::check_cream(frame, state_camover);

    }
    //ʹ�ö��д洢������Ϣ
    camcover_timer.ltime_idx.push_back(out_idx);
    if ((out_idx < 0) || (camcover_timer.ltime_idx.size() <= 0))
    {
        return -1;
    }

    string waringtxt;
    int timestamp  = 0;  //ʱ�����ʼ��
    //star����֬
    if ((camcover_timer.recog_star_time) && (camcover_timer.freeze_state == false))
    {
        //��ʶ�����趨ʱ�䱻���ʱ����Ҫ���»ع鵽δʶ��״̬
        if (face_info.state == 1)
        {
            camcover_timer.ltime_idx.clear();
            camcover_timer.recog_star_time = 0;
            camcover_timer.freeze_state = false;
            return 2;
        }
        timestamp = getTimestamp() - camcover_timer.recog_star_time;
        if ((timestamp - globalparams.event_camcover_time)>1000)
        {
            camcover_timer.ltime_idx.clear();
            camcover_timer.recog_star_time = 0;
            camcover_timer.freeze_state = false;
            return 2;
        }

        if (timestamp >= globalparams.event_camcover_time)
        {
            //��ȡ�¼�״̬  ������Ƶ��ռ�ȣ���λ�ã�λ��ê����
            int rate_alarm = Talarm::get_event(camcover_timer.ltime_idx);
            if (rate_alarm > globalparams.camcover_sensitivity)
            {
                waringtxt = "Waring: Yawn" + to_string(rate_alarm) + "%";
                camcover_timer.alarm_state = true;
            }
            camcover_timer.recog_star_time = 0;
            camcover_timer.ltime_idx.clear();
            camcover_timer.freeze_star_time = getTimestamp();
            camcover_timer.freeze_state = true;
            return 1;
        }
    }

    //�����һ������ ����ʱ�����
    if (camcover_timer.ltime_idx.size() == 1)
    {
        //ͳ��һ������ʶ�𱨾�����ʼʱ��
        if (camcover_timer.ltime_idx[0] == 1)
        {
            //��ӡ��һ�α������ֵ�ʱ���
            camcover_timer.recog_star_time = getTimestamp();
        }
        else
        {
            camcover_timer.ltime_idx.clear();
            camcover_timer.recog_star_time = 0;
        }
    }

    if (camcover_timer.freeze_star_time)
    {
        //cout << "#############��ȴ��ʼʱ�䣺" << leave_timer.freeze_star_time << endl;
        int eye_cool_end = getTimestamp();
        int temp_timm = eye_cool_end - camcover_timer.freeze_star_time;
        if (temp_timm >= globalparams.freeze_camcover_time)
        {
            //��ȴʱ�������ʼ��һ�ֵ�ʶ��
            camcover_timer.freeze_state = false;
            camcover_timer.freeze_star_time = 0;
            camcover_timer.recog_star_time = 0;
            camcover_timer.ltime_idx.clear();
            camcover_timer.alarm_state = false;
            // return 2;
        }
        if (camcover_timer.freeze_state == true)
        {
            if (camcover_timer.alarm_state == true)  // &&(mouth_timer.recog_star_time == 0)
            {
                //putTextZH(face_info.frame, "�ڵ�����", Point(150, 90), Scalar(2, 0, 255), 18, "����");
                return 1;
            }
            else
            {
                //putTextZH(face_info.frame, "�ޱ���", Point(150, 90), Scalar(2, 0, 255), 18, "����");
            }
        }
    }
}

//Ƶ�κ�λ��ê��
int Talarm::get_event(vector<int> input_vec ) {
    float result_rate = 0;
    float rate_event = 0;
    float sum_alarm = 0;
    for (size_t i = 0; i < input_vec.size(); i++) {
        if (input_vec[i] == 1) {
            sum_alarm++;
        }
    }

    rate_event = sum_alarm / float(input_vec.size());

    result_rate = rate_event * 100;
    return result_rate;
}

//Ƶ�κ�λ��ê���Լ�ǰ��֡
int Talarm::get_event_mode(vector<int> input_vec,bool &first_stat) {
    float result_rate = 0;
    float rate_event = 0;
    float sum_alarm = 0;

    for (size_t i = 0; i < input_vec.size(); i++) {
        if (input_vec[i] == 1) {
            sum_alarm++;
        }
    }

    if(input_vec.size()>=3)
    {
        if ((input_vec[0] == 1)&&(input_vec[1] == 1)&&(input_vec[2] == 1))
//            if ((input_vec[0] == 1)&&(input_vec[1] == 1)&&(input_vec[2] == 1)&&(input_vec[3] == 1)&&(input_vec[4] == 1))
        {
            first_stat = true;
        }
        else
        {
            first_stat = false;
        }
    }
    else
    {
        first_stat = false;
    }
    rate_event = sum_alarm / float(input_vec.size());
    result_rate = rate_event * 100;
    return result_rate;




}

