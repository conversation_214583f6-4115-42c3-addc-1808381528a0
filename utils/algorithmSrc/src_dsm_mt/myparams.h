#ifndef MYPARAMS_H
#define MYPARAMS_H
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include<vector>

//���ò���
//ʶ�𿪹�  1���۾�ʶ�𿪹�   2�����ʶ�𿪹�    3���������   4�����   5���ڵ�����ͷ  6������   7����绰
//�¼�ʱ��  1�������¼��Ĺ̶�ʱ��  2����Ƿ�¼��Ĺ̶��¼�
//������    1������Ƶ��ռ�� 2����ǷƵ��ռ��
//��ȴ�¼�  1�����۱�������ȴʱ��  2����Ƿ��������ȴʱ��

class printLog
{
public:
    static void Write(std::string log)
    {
            std::ofstream ofs;
//            time_t t = time(0);
//            char tmp[64];
            //strftime(tmp, sizeof(tmp), "[%Y-%m-%d %X]", localtime(&t));
            std::string path = "/userdata/media/AlarmLog.log";
            ofs.open(path, std::ofstream::app);

            //��ӡ��¼ʱ��
            //ofs << tmp << " - ";
            //����ӡ�����ַ���  ����ӡʱ��
            ofs.write(log.c_str(), log.size());
            ofs << std::endl;
            ofs.close();
    }

};

//extern cv::dnn::Net net;
struct globals
{
    //���ֲ��� 
    int respirator_speed;
    int respirator_button;
    int respirator_mode;
    int respirator_sensitivity;
    int event_respirator_time;
    int freeze_respirator_time;

    //���۲���
    int eye_speed;
    int eye_button;
    int eye_mode;
    int eye_sensitivity;
    int event_eye_time;
    int freeze_eye_time;

    //��Ƿ����
    int mouth_speed;
    int mouth_button;
    int mouth_mode;
    int mouth_sensitivity;
    int event_mouth_time;
    int freeze_mouth_time;

    //������β��� 
    int lookaround_speed;
    int lookaround_button;
    int lookaround_mode;
    int lookaround_sensitivity;
    int event_lookaround_time;
    int freeze_lookaround_time;

    //��ڲ���
    int facemissing_speed;
    int facemissing_button;
    int facemissing_mode;
    int facemissing_sensitivity;
    int event_facemissing_time;
    int freeze_facemissing_time;

    //�ڵ�����
    int camcover_speed;
    int camcover_button;
    int camcover_mode;
    int camcover_sensitivity;
    int event_camcover_time;
    int freeze_camcover_time;

    //���̲���
    int smoking_speed;
    int smoking_button;
    int smoking_mode;
    int smoking_sensitivity;
    int event_smoking_time;
    int freeze_smoking_time;

    //��绰����
    int phone_speed;
    int phone_button;
    int phone_mode;
    int phone_sensitivity;
    int event_phone_time;
    int freeze_phone_time;

    //���Ʋ���  ��ʼ��ֹͣ�ź�  
    int gesture_button;
    int gesture_mode;
    int gesture_sensitivity;
    int gesture_sign;

    //����������
    int detect_move_button;
    int move_pixel;
    int frame_early;

};

struct regions
{
    //�ٶȲ��� 
    int
    speed;

    //���ز���
    int button;

    //ģʽ����
    int mode;

    //�¼�����
    int sensitivity;

    //�¼�����
    int event_time;

    //��ȴ���� 
    int freeze_time;

    ////���Ʋ���  ��ʼ��ֹͣ�ź�  
    //int gesture;
};

#if (defined WIN32 || defined _WIN32 )
extern struct face_message 
#else
struct face_message 
#endif
{

	int state;

	cv::Mat frame;

	std::vector<cv::Mat> face_roi;

    std::vector<int> landmarks;

    std::vector<float> face_angle; //����ת������

};

struct Net_config
{
	float confThreshold; // Confidence threshold
	float nmsThreshold;  // Non-maximum suppression threshold
	float objThreshold;  //Object Confidence threshold
    std::string netname;
};

struct alarm_timer {

	uint64_t recog_star_time; //ʶ������ʱ
	uint64_t freeze_star_time; //��ȴ����ʱ

	bool alarm_state; //��ȴ״̬�ж�
	bool freeze_state; //��ȴ״̬�ж�

    std::vector<int> ltime_idx; //һ���¼���������
	
};


struct DSMInfo
{
    //�����Ƿ�����ͬ��ʻԱ
    int isSameDriver;

    //�����÷�
    float face_score;
    //�������� ���Ϻ����������� 
    std::vector<cv::Point2i> face_point;

    //������������㣬�����XY
    std::vector<cv::Point2i> five_point;

    //���ֱ���״̬ 
    bool respirator_alarm;

    //���۱���״̬
    bool eye_alarm;

    //��Ƿ����״̬
    bool mouth_alarm;

    //������α���״̬ 
    bool lookaround_alarm;

    //��ڱ���״̬
    bool facemissing_alarm;

    //�ڵ�����״̬
    bool camcover_alarm;

    //���̱���״̬
    bool smoking_alarm;

    //��绰����״̬
    bool phone_alarm;

    //ƣ�ͳ̶�
    int fatigue_rank;

};

int risk_level(DSMInfo alarm_infos);
void init_DSMInfo_parmas(DSMInfo alarm_info);
bool check_params(regions& region);
void get_global_params(std::string yaml_path, globals globalparams); //ǰ�ڵ���ʹ��
bool LoadConfigFile(const std::string& file_name, globals &globalparams);
std::string readConfig(std::string file_path, const std::string& key, std::string def);
int readConfigInt(std::string file_path, const std::string& key, int def);
bool readConfigBool(std::string file_path, const std::string& key, bool def);
float readConfigFloat(std::string file_path, const std::string& key, float def);
int getDistance(cv::Point point1, cv::Point point2);

extern cv::Mat convertTo3Channels(const cv::Mat& binImg);
uint64_t getTimestamp();
// unsigned long getTimestamp();
extern void init_global_params(alarm_timer input_struct);

#endif
