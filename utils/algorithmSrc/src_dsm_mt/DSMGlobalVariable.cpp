//
// Created by zheng<PERSON><PERSON><PERSON> on 2023/4/14.
// 20240815 by zwx

#include "DSMGlobalVariable.h"

using namespace std;
using namespace cv;

#if (defined WIN32 || defined _WIN32 )
String labels_txt_file = "E:\\Deeplearning\\Classifying_Cancer_ResNet18_Pytorch\\onnx\\resnet18_best.txt";
    String Resnet_model = "E:\\Deeplearning\\Classifying_Cancer_ResNet18_Pytorch\\onnx\\resnet18_0908_1_best.onnx";
#else
String labels_txt_file = "./model/face_5_class_label.txt";
String Resnet_model = "./model/face_5_class.rknn";
#endif

globals globalparams;

DSMInfo alarm_info;

//给旭哥的函数
int send_DSMinfo(DSMInfo &alarm_info) {
    int ret = -1;
    alarm_info.facemissing_alarm = 0;
    return ret;
}


/*************************************************
//  Method:    convertTo3Channels
//  Description: 将单通道图像转为三通道图像
//  Returns:   cv::Mat
//  Parameter: binImg 单通道图像对象
*************************************************/
Mat convertTo3Channels(const Mat &binImg) {
    Mat three_channel = Mat::zeros(binImg.rows, binImg.cols, CV_8UC3);
    vector<Mat> channels;
    for (int i = 0; i < 3; i++) {
        channels.push_back(binImg);
    }
    merge(channels, three_channel);
    return three_channel;
}

#include "XuTimeUtil.h"

// 获取时间戳  获取毫秒级 0812记录
uint64_t getTimestamp()
{
	uint64_t atime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
	return atime;
}

// //获取时间戳  获取毫秒级
// unsigned long getTimestamp() {
//     ////计算函数耗时
//     //std::chrono::steady_clock::time_point t1 = std::chrono::steady_clock::now();
//     ////测试程序
//     //std::chrono::steady_clock::time_point t2 = std::chrono::steady_clock::now();
//     //std::chrono::duration<double> time_used = std::chrono::duration_cast<std::chrono::duration<double>>(t2 - t1);
//     //std::cout << ".******************solve time cost = " << time_used.count() << " seconds. " << std::endl;

//     // NO1:时间戳 毫秒
//     //return chrono::system_clock::now().time_since_epoch().count() / chrono::system_clock::period::den;

//     // NO2:时间戳 毫秒
//     std::chrono::time_point<std::chrono::system_clock, std::chrono::milliseconds> tp = std::chrono::time_point_cast<std::chrono::milliseconds>(
//             std::chrono::system_clock::now());
//     return tp.time_since_epoch().count();

//     // NO3:时间戳 毫秒
//     //std::chrono::steady_clock::time_point t2 = std::chrono::steady_clock::now();
//     //return t2;
// }

//危险程度，包含0：不存在报警信息，1~3疲劳程度 4~6危险驾驶信息
int risk_level(DSMInfo alarm_infos) {
    //后续会增加遮挡、抽烟、打电话的识别判断需要修改此处
    if ((alarm_infos.respirator_alarm == 0) && (alarm_infos.eye_alarm == 0) && (alarm_infos.mouth_alarm == 0) &&
        (alarm_infos.lookaround_alarm == 0) && (alarm_infos.facemissing_alarm == 0)) {
        alarm_infos.fatigue_rank = 0;
        return 0;
    }
    int sum_alarm;
    sum_alarm = alarm_infos.respirator_alarm + alarm_infos.eye_alarm + alarm_infos.mouth_alarm +
                alarm_infos.lookaround_alarm + alarm_infos.facemissing_alarm;
    if (sum_alarm > 0) {
        alarm_infos.fatigue_rank = sum_alarm;
        return sum_alarm;
    } else {
        return 0;
    }
}

//初始化参数
void init_global_params(alarm_timer input_struct) {
    input_struct.recog_star_time = 0;
    input_struct.freeze_star_time = 0;

    input_struct.alarm_state = false;
    input_struct.freeze_state = false; //冷却状态判断

    input_struct.ltime_idx; //一次事件分析数组
}

void init_DSMInfo_parmas(DSMInfo alarm_info) {
    alarm_info.isSameDriver = 0;
    alarm_info.face_score = 0.6666;
    alarm_info.respirator_alarm = false;
    alarm_info.eye_alarm = false;
    alarm_info.mouth_alarm = false;
    alarm_info.eye_alarm = false;
    alarm_info.lookaround_alarm = false;
    alarm_info.facemissing_alarm = false;
    alarm_info.camcover_alarm = false;
    alarm_info.smoking_alarm = false;
    alarm_info.phone_alarm = false;
    alarm_info.fatigue_rank = 0;
}