#ifndef GET_ALARM_H
#define GET_ALARM_H
#include <string>
#include <opencv2/opencv.hpp>
#include"myparams.h"
#include "dsmShareDefine.h"



class Talarm
{
public:
	static int get_event(std::vector<int> input_vec);
    static int get_event_mode(std::vector<int> input_vec,bool &first_stat);
#ifdef DSM_USING_RKNN
        static int alarm_respirator(face_message face_info);
	static int alarm_eye(face_message face_info);
	static int alarm_mouth(face_message face_info);
#else
	static int alarm_respirator(dnn::Net net, face_message face_info);
	static int alarm_eye(dnn::Net net, face_message face_info);
	static int alarm_mouth(dnn::Net net, face_message face_info);
#endif
	static int alarm_lookaround(face_message face_info);
	static int alarm_facemissing(face_message face_info, cv::Mat frame);
    static int alarm_camcover(face_message face_info, cv::Mat frame);
	static bool recognition_gesture(int far_info, cv::Mat frame);

	//const Rect mouth_roi ;
	//DSM��������
private:

};

#endif  //GET_ALARM_H
