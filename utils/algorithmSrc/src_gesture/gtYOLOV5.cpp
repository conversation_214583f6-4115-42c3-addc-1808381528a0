﻿
#include "gtYOLOV5.h"

#if (defined WIN32 || defined _WIN32 )
#pragma warning(disable:4996)
#endif


#ifdef GT_USING_RKNN
/*-------------------------------------------
Includes
-------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dlfcn.h>
#include <vector>
#include <sys/time.h>

//#define _BASETSD_H




//#include "drm_func.h"
//#include "rga_func.h"

//#include "postprocess.h"

#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>

#ifdef GT_USING_RGA

#include "XuRGAUtils.h"

#endif

//#define PERF_WITH_POST 1

#endif

using namespace cv;
#ifdef GT_USING_RKNN
#else
using namespace dnn;
#endif
using namespace std;


#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include <vector>
#include <set>
#include <stdint.h>


#ifdef GT_USING_RGA
extern XuRGAUtils rga_utils;
#endif

namespace gt {

    const int anchor0[6] = {10, 13, 16, 30, 33, 23};
    const int anchor1[6] = {30, 61, 62, 45, 59, 119};
    const int anchor2[6] = {116, 90, 156, 198, 373, 326};


    inline static int clamp(float val, int min, int max) {
        return val > min ? (val < max ? val : max) : min;
    }

    char *readLine_Gt(FILE *fp, char *buffer, int *len) {
        int ch;
        int i = 0;
        size_t buff_len = 0;

        buffer = (char *) malloc(buff_len + 1);
        if (!buffer)
            return NULL; // Out of memory

        while ((ch = fgetc(fp)) != '\n' && ch != EOF) {
            buff_len++;
            void *tmp = realloc(buffer, buff_len + 1);
            if (tmp == NULL) {
                free(buffer);
                return NULL; // Out of memory
            }
            buffer = (char *) tmp;

            buffer[i] = (char) ch;
            i++;
        }
        buffer[i] = '\0';

        *len = buff_len;

        // Detect end
        if (ch == EOF && (i == 0 || ferror(fp))) {
            free(buffer);
            return NULL;
        }
        return buffer;
    }

    int readLines_Gt(const char *fileName, char *lines[], int max_line) {
        FILE *file = fopen(fileName, "r");
        char *s = NULL;
        int i = 0;
        int n = 0;
        while ((s = readLine_Gt(file, s, &n)) != NULL) {
            lines[i++] = s;
            if (i >= max_line)
                break;
        }
        return i;
    }

    int loadLabelName(const char *locationFilename, char *label[], int objNum) {
        printf("loadLabelName %s\n", locationFilename);
        readLines_Gt(locationFilename, label, objNum);
        return 0;
    }

    static float
    CalculateOverlap(float xmin0, float ymin0, float xmax0, float ymax0, float xmin1, float ymin1, float xmax1,
                     float ymax1) {
        float w = fmax(0.f, fmin(xmax0, xmax1) - fmax(xmin0, xmin1) + 1.0);
        float h = fmax(0.f, fmin(ymax0, ymax1) - fmax(ymin0, ymin1) + 1.0);
        float i = w * h;
        float u = (xmax0 - xmin0 + 1.0) * (ymax0 - ymin0 + 1.0) + (xmax1 - xmin1 + 1.0) * (ymax1 - ymin1 + 1.0) - i;
        return u <= 0.f ? 0.f : (i / u);
    }

    static int
    nms_u8(int validCount, std::vector<float> &outputLocations, std::vector<int> classIds, std::vector<int> &order,
           int filterId, float threshold) {
        for (int i = 0; i < validCount; ++i) {
            if (order[i] == -1 || classIds[i] != filterId) {
                continue;
            }
            int n = order[i];
            for (int j = i + 1; j < validCount; ++j) {
                int m = order[j];
                if (m == -1 || classIds[i] != filterId) {
                    continue;
                }
                float xmin0 = outputLocations[n * 4 + 0];
                float ymin0 = outputLocations[n * 4 + 1];
                float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
                float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

                float xmin1 = outputLocations[m * 4 + 0];
                float ymin1 = outputLocations[m * 4 + 1];
                float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
                float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

                float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);

                if (iou > threshold) {
                    order[j] = -1;
                }
            }
        }
        return 0;
    }

    static int nms_fp(int validCount, std::vector<float> &outputLocations, std::vector<int> &order, float threshold) {
        for (int i = 0; i < validCount; ++i) {
            if (order[i] == -1) {
                continue;
            }
            int n = order[i];
            for (int j = i + 1; j < validCount; ++j) {
                int m = order[j];
                if (m == -1) {
                    continue;
                }
                float xmin0 = outputLocations[n * 4 + 0];
                float ymin0 = outputLocations[n * 4 + 1];
                float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
                float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

                float xmin1 = outputLocations[m * 4 + 0];
                float ymin1 = outputLocations[m * 4 + 1];
                float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
                float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

                float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);

                if (iou > threshold) {
                    order[j] = -1;
                }
            }
        }
        return 0;
    }

    static int quick_sort_indice_inverse(
            std::vector<float> &input,
            int left,
            int right,
            std::vector<int> &indices) {
        float key;
        int key_index;
        int low = left;
        int high = right;
        if (left < right) {
            key_index = indices[left];
            key = input[left];
            while (low < high) {
                while (low < high && input[high] <= key) {
                    high--;
                }
                input[low] = input[high];
                indices[low] = indices[high];
                while (low < high && input[low] >= key) {
                    low++;
                }
                input[high] = input[low];
                indices[high] = indices[low];
            }
            input[low] = key;
            indices[low] = key_index;
            quick_sort_indice_inverse(input, left, low - 1, indices);
            quick_sort_indice_inverse(input, low + 1, right, indices);
        }
        return low;
    }

    static float sigmoid(float x) {
        return 1.0 / (1.0 + expf(-x));
    }

    static float unsigmoid(float y) {
        return -1.0 * logf((1.0 / y) - 1.0);
    }

    inline static int32_t __clip(float val, float min, float max) {
        float f = val <= min ? min : (val >= max ? max : val);
        return f;
    }

    static uint8_t qnt_f32_to_affine(float f32, uint32_t zp, float scale) {
        float dst_val = (f32 / scale) + zp;
        uint8_t res = (uint8_t) __clip(dst_val, 0, 255);
        return res;
    }

    static float deqnt_affine_to_f32(uint8_t qnt, uint32_t zp, float scale) {
        return ((float) qnt - (float) zp) * scale;
    }

    static int
    process_u8(uint8_t *input, int *anchor, int objNum, int prop_box_size, int grid_h, int grid_w, int height,
               int width, int stride,
               std::vector<float> &boxes, std::vector<float> &objProbs, std::vector<int> &classId,
               float threshold, uint32_t zp, float scale) {

        int validCount = 0;
        int grid_len = grid_h * grid_w;
        float thres = unsigmoid(threshold);
        uint8_t thres_u8 = qnt_f32_to_affine(thres, zp, scale);
        for (int a = 0; a < 3; a++) {
            for (int i = 0; i < grid_h; i++) {
                for (int j = 0; j < grid_w; j++) {
                    uint8_t box_confidence = input[(prop_box_size * a + 4) * grid_len + i * grid_w + j];
                    if (box_confidence >= thres_u8) {
                        int offset = (prop_box_size * a) * grid_len + i * grid_w + j;
                        uint8_t *in_ptr = input + offset;
                        float box_x = sigmoid(deqnt_affine_to_f32(*in_ptr, zp, scale)) * 2.0 - 0.5;
                        float box_y = sigmoid(deqnt_affine_to_f32(in_ptr[grid_len], zp, scale)) * 2.0 - 0.5;
                        float box_w = sigmoid(deqnt_affine_to_f32(in_ptr[2 * grid_len], zp, scale)) * 2.0;
                        float box_h = sigmoid(deqnt_affine_to_f32(in_ptr[3 * grid_len], zp, scale)) * 2.0;
                        box_x = (box_x + j) * (float) stride;
                        box_y = (box_y + i) * (float) stride;
                        box_w = box_w * box_w * (float) anchor[a * 2];
                        box_h = box_h * box_h * (float) anchor[a * 2 + 1];
                        box_x -= (box_w / 2.0);
                        box_y -= (box_h / 2.0);
                        boxes.push_back(box_x);
                        boxes.push_back(box_y);
                        boxes.push_back(box_w);
                        boxes.push_back(box_h);

                        uint8_t maxClassProbs = in_ptr[5 * grid_len];
                        int maxClassId = 0;
                        for (int k = 1; k < objNum; ++k) {
                            uint8_t prob = in_ptr[(5 + k) * grid_len];
                            if (prob > maxClassProbs) {
                                maxClassId = k;
                                maxClassProbs = prob;
                            }
                        }
                        //objProbs.push_back(sigmoid(deqnt_affine_to_f32(maxClassProbs, zp, scale)));
                        objProbs.push_back(sigmoid(deqnt_affine_to_f32(box_confidence, zp, scale)));

                        classId.push_back(maxClassId);
                        validCount++;
                    }
                }
            }
        }
        return validCount;
    }

    int post_process_u8(uint8_t *input0, uint8_t *input1, uint8_t *input2, char **labels, int objNum, int prop_box_size,
                        int model_in_h, int model_in_w,
                        float conf_threshold, float nms_threshold,
                        std::vector<uint32_t> &qnt_zps, std::vector<float> &qnt_scales,
                        detect_result_group_t *group) {
        memset(group, 0, sizeof(detect_result_group_t));

        std::vector<float> filterBoxes;
        std::vector<float> objProbs;
        std::vector<int> classId;

        // stride 8
        int stride0 = 8;
        int grid_h0 = model_in_h / stride0;
        int grid_w0 = model_in_w / stride0;
        int validCount0 = 0;
        validCount0 = process_u8(input0, (int *) anchor0, objNum, prop_box_size, grid_h0, grid_w0, model_in_h,
                                 model_in_w,
                                 stride0, filterBoxes, objProbs, classId, conf_threshold, qnt_zps[0], qnt_scales[0]);

        // stride 16
        int stride1 = 16;
        int grid_h1 = model_in_h / stride1;
        int grid_w1 = model_in_w / stride1;
        int validCount1 = 0;
        validCount1 = process_u8(input1, (int *) anchor1, objNum, prop_box_size, grid_h1, grid_w1, model_in_h,
                                 model_in_w,
                                 stride1, filterBoxes, objProbs, classId, conf_threshold, qnt_zps[1], qnt_scales[1]);

        // stride 32
        int stride2 = 32;
        int grid_h2 = model_in_h / stride2;
        int grid_w2 = model_in_w / stride2;
        int validCount2 = 0;
        validCount2 = process_u8(input2, (int *) anchor2, objNum, prop_box_size, grid_h2, grid_w2, model_in_h,
                                 model_in_w,
                                 stride2, filterBoxes, objProbs, classId, conf_threshold, qnt_zps[2], qnt_scales[2]);

        int validCount = validCount0 + validCount1 + validCount2;
        // no object detect
        if (validCount <= 0) {
            return 0;
        }

        std::vector<int> indexArray;
        for (int i = 0; i < validCount; ++i) {
            indexArray.push_back(i);
        }

        quick_sort_indice_inverse(objProbs, 0, validCount - 1, indexArray);

        std::set<int> class_set(std::begin(classId), std::end(classId));

        for (auto c: class_set) {
            nms_u8(validCount, filterBoxes, classId, indexArray, c, nms_threshold);
        }

        int last_count = 0;
        group->count = 0;
        /* box valid detect target */
        for (int i = 0; i < validCount; ++i) {

            if (indexArray[i] == -1 || i >= GT_OBJ_NUMB_MAX_SIZE) {
                continue;
            }
            int n = indexArray[i];

            float x1 = filterBoxes[n * 4 + 0];
            float y1 = filterBoxes[n * 4 + 1];
            float x2 = x1 + filterBoxes[n * 4 + 2];
            float y2 = y1 + filterBoxes[n * 4 + 3];
            int id = classId[n];
            float obj_conf = objProbs[i];

            group->results[last_count].box.left = (int) (clamp(x1, 0, model_in_w));
            group->results[last_count].box.top = (int) (clamp(y1, 0, model_in_h));
            group->results[last_count].box.right = (int) (clamp(x2, 0, model_in_w));
            group->results[last_count].box.bottom = (int) (clamp(y2, 0, model_in_h));
            group->results[last_count].prop = obj_conf;
            char *label = labels[id];
            strncpy(group->results[last_count].name, label, GT_OBJ_NAME_MAX_SIZE);

            // printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left, group->results[last_count].box.top,
            //        group->results[last_count].box.right, group->results[last_count].box.bottom, label);
            last_count++;
        }
        group->count = last_count;

        return 0;
    }

    static int
    process_fp(float *input, int *anchor, int objNum, int prop_box_size, int grid_h, int grid_w, int height, int width,
               int stride,
               std::vector<float> &boxes, std::vector<float> &boxScores, std::vector<int> &classId,
               float threshold) {

        int validCount = 0;
        int grid_len = grid_h * grid_w;
        float thres_sigmoid = unsigmoid(threshold);
        for (int a = 0; a < 3; a++) {
            for (int i = 0; i < grid_h; i++) {
                for (int j = 0; j < grid_w; j++) {
                    float box_confidence = input[(prop_box_size * a + 4) * grid_len + i * grid_w + j];
                    if (box_confidence >= thres_sigmoid) {
                        int offset = (prop_box_size * a) * grid_len + i * grid_w + j;
                        float *in_ptr = input + offset;
                        float box_x = sigmoid(*in_ptr) * 2.0 - 0.5;
                        float box_y = sigmoid(in_ptr[grid_len]) * 2.0 - 0.5;
                        float box_w = sigmoid(in_ptr[2 * grid_len]) * 2.0;
                        float box_h = sigmoid(in_ptr[3 * grid_len]) * 2.0;
                        box_x = (box_x + j) * (float) stride;
                        box_y = (box_y + i) * (float) stride;
                        box_w = box_w * box_w * (float) anchor[a * 2];
                        box_h = box_h * box_h * (float) anchor[a * 2 + 1];
                        box_x -= (box_w / 2.0);
                        box_y -= (box_h / 2.0);
                        boxes.push_back(box_x);
                        boxes.push_back(box_y);
                        boxes.push_back(box_w);
                        boxes.push_back(box_h);

                        float maxClassProbs = in_ptr[5 * grid_len];
                        int maxClassId = 0;
                        for (int k = 1; k < objNum; ++k) {
                            float prob = in_ptr[(5 + k) * grid_len];
                            if (prob > maxClassProbs) {
                                maxClassId = k;
                                maxClassProbs = prob;
                            }
                        }
                        float box_conf_f32 = sigmoid(box_confidence);
                        float class_prob_f32 = sigmoid(maxClassProbs);
                        boxScores.push_back(box_conf_f32 * class_prob_f32);
                        classId.push_back(maxClassId);
                        validCount++;
                    }
                }
            }
        }
        return validCount;
    }

    int post_process_fp(float *input0, float *input1, float *input2, char **labels, int objNum, int prop_box_size,
                        int model_in_h, int model_in_w,
                        int h_offset, int w_offset, float resize_scale, float conf_threshold, float nms_threshold,
                        detect_result_group_t *group) {
        memset(group, 0, sizeof(detect_result_group_t));

        std::vector<float> filterBoxes;
        std::vector<float> boxesScore;
        std::vector<int> classId;
        int stride0 = 8;
        int grid_h0 = model_in_h / stride0;
        int grid_w0 = model_in_w / stride0;
        int validCount0 = 0;
        validCount0 = process_fp(input0, (int *) anchor0, objNum, prop_box_size, grid_h0, grid_w0, model_in_h,
                                 model_in_w,
                                 stride0, filterBoxes, boxesScore, classId, conf_threshold);

        int stride1 = 16;
        int grid_h1 = model_in_h / stride1;
        int grid_w1 = model_in_w / stride1;
        int validCount1 = 0;
        validCount1 = process_fp(input1, (int *) anchor1, objNum, prop_box_size, grid_h1, grid_w1, model_in_h,
                                 model_in_w,
                                 stride1, filterBoxes, boxesScore, classId, conf_threshold);

        int stride2 = 32;
        int grid_h2 = model_in_h / stride2;
        int grid_w2 = model_in_w / stride2;
        int validCount2 = 0;
        validCount2 = process_fp(input2, (int *) anchor2, objNum, prop_box_size, grid_h2, grid_w2, model_in_h,
                                 model_in_w,
                                 stride2, filterBoxes, boxesScore, classId, conf_threshold);

        int validCount = validCount0 + validCount1 + validCount2;
        // no object detect
        if (validCount <= 0) {
            return 0;
        }

        std::vector<int> indexArray;
        for (int i = 0; i < validCount; ++i) {
            indexArray.push_back(i);
        }

        quick_sort_indice_inverse(boxesScore, 0, validCount - 1, indexArray);

        nms_fp(validCount, filterBoxes, indexArray, nms_threshold);

        int last_count = 0;
        group->count = 0;
        /* box valid detect target */
        for (int i = 0; i < validCount; ++i) {

            if (indexArray[i] == -1 || boxesScore[i] < conf_threshold || last_count >= GT_OBJ_NUMB_MAX_SIZE) {
                continue;
            }
            int n = indexArray[i];

            float x1 = filterBoxes[n * 4 + 0];
            float y1 = filterBoxes[n * 4 + 1];
            float x2 = x1 + filterBoxes[n * 4 + 2];
            float y2 = y1 + filterBoxes[n * 4 + 3];
            int id = classId[n];

            group->results[last_count].box.left = (int) ((clamp(x1, 0, model_in_w) - w_offset) / resize_scale);
            group->results[last_count].box.top = (int) ((clamp(y1, 0, model_in_h) - h_offset) / resize_scale);
            group->results[last_count].box.right = (int) ((clamp(x2, 0, model_in_w) - w_offset) / resize_scale);
            group->results[last_count].box.bottom = (int) ((clamp(y2, 0, model_in_h) - h_offset) / resize_scale);
            group->results[last_count].prop = boxesScore[i];
            group->results[last_count].class_index = id;
            char *label = labels[id];
            strncpy(group->results[last_count].name, label, GT_OBJ_NAME_MAX_SIZE);

            // printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left, group->results[last_count].box.top,
            //        group->results[last_count].box.right, group->results[last_count].box.bottom, label);
            last_count++;
        }
        group->count = last_count;

        return 0;
    }


#ifdef GT_USING_RKNN


    /*-------------------------------------------
    Functions
    -------------------------------------------*/

    inline const char *get_type_string(rknn_tensor_type type) {
        switch (type) {
            case RKNN_TENSOR_FLOAT32:
                return "FP32";
            case RKNN_TENSOR_FLOAT16:
                return "FP16";
            case RKNN_TENSOR_INT8:
                return "INT8";
            case RKNN_TENSOR_UINT8:
                return "UINT8";
            case RKNN_TENSOR_INT16:
                return "INT16";
            default:
                return "UNKNOW";
        }
    }

    inline const char *get_qnt_type_string(rknn_tensor_qnt_type type) {
        switch (type) {
            case RKNN_TENSOR_QNT_NONE:
                return "NONE";
            case RKNN_TENSOR_QNT_DFP:
                return "DFP";
            case RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC:
                return "AFFINE";
            default:
                return "UNKNOW";
        }
    }

    inline const char *get_format_string(rknn_tensor_format fmt) {
        switch (fmt) {
            case RKNN_TENSOR_NCHW:
                return "NCHW";
            case RKNN_TENSOR_NHWC:
                return "NHWC";
            default:
                return "UNKNOW";
        }
    }

    static void dump_tensor_attr(rknn_tensor_attr *attr) {
        printf("  index=%d, name=%s, n_dims=%d, dims=[%d, %d, %d, %d], n_elems=%d, size=%d, fmt=%s, type=%s, qnt_type=%s, "
               "zp=%d, scale=%f\n",
               attr->index, attr->name, attr->n_dims, attr->dims[3], attr->dims[2], attr->dims[1], attr->dims[0],
               attr->n_elems, attr->size, get_format_string(attr->fmt), get_type_string(attr->type),
               get_qnt_type_string(attr->qnt_type), attr->zp, attr->scale);
    }

    double __get_us_Gt_yolo(struct timeval t) { return (t.tv_sec * 1000000 + t.tv_usec); }

    static unsigned char *load_data(FILE *fp, size_t ofst, size_t sz) {
        unsigned char *data;
        int ret;

        data = NULL;

        if (NULL == fp) {
            return NULL;
        }

        ret = fseek(fp, ofst, SEEK_SET);
        if (ret != 0) {
            printf("blob seek failure.\n");
            return NULL;
        }

        data = (unsigned char *) malloc(sz);
        if (data == NULL) {
            printf("buffer malloc failure.\n");
            return NULL;
        }
        ret = fread(data, 1, sz, fp);
        return data;
    }

    static unsigned char *load_model(const char *filename, int *model_size) {

        FILE *fp;
        unsigned char *data;

        fp = fopen(filename, "rb");
        if (NULL == fp) {
            printf("Open file %s failed.\n", filename);
            return NULL;
        }

        fseek(fp, 0, SEEK_END);
        int size = ftell(fp);

        data = load_data(fp, 0, size);

        fclose(fp);

        *model_size = size;
        return data;
    }

    void decode(unsigned char *input, unsigned char *output, unsigned char *dict, int len) {
        for (int i = 0; i < len; i++)
            output[i] = input[i] ^ dict[i];
    }

    static int saveFloat(const char *file_name, float *output, int element_size) {
        FILE *fp;
        fp = fopen(file_name, "w");
        for (int i = 0; i < element_size; i++) {
            fprintf(fp, "%.6f\n", output[i]);
        }
        fclose(fp);
        return 0;
    }

#endif


    YOLOV5::YOLOV5() {}

    YOLOV5::~YOLOV5() {
#ifdef GT_USING_RKNN
        // release
        /*
        ret = rknn_destroy(ctx);
        drm_buf_destroy(&drm_ctx, drm_fd, buf_fd, handle, drm_buf, actual_size);

        drm_deinit(&drm_ctx, drm_fd);
        RGA_deinit(&rga_ctx);
        if (model_data)
            free(model_data);

        if (resize_buf)
            free(resize_buf);
        */
#endif
    }

    void YOLOV5::init(char *iniPath) {
        int ret;
        const bool keep_ratio = true;


        int netWidth;
        int netHeight;
        int objNum;
        float boxThreshold;
        float classThreshold;
        float nmsThreshold;
        float mean[3];
        float std[3];

        char modelpath[200];
        char labelPath[200];
        char imgPath[200];

        iniStart(iniPath);

        iniGetInt("net", "netWidth", &netWidth);
        iniGetInt("net", "netHeight", &netHeight);
        iniGetInt("net", "objNum", &objNum);
        iniGetFloat("net", "boxThreshold", &boxThreshold);
        iniGetFloat("net", "classThreshold", &classThreshold);
        iniGetFloat("net", "nmsThreshold", &nmsThreshold);
        iniGetFloat("net", "mean0", &mean[0]);
        iniGetFloat("net", "mean1", &mean[1]);
        iniGetFloat("net", "mean2", &mean[2]);
        iniGetFloat("net", "std0", &std[0]);
        iniGetFloat("net", "std1", &std[1]);
        iniGetFloat("net", "std2", &std[2]);

        iniGetString("path", "modelPath", modelpath);
        iniGetString("path", "labelPath", labelPath);
        iniGetString("path", "imgPath", imgPath);


        this->model_width = netWidth;
        this->model_height = netHeight;
        this->model_channel = 3;

        this->objNum = objNum;
        this->prop_box_size = 5 + objNum;

        this->classThreshold = classThreshold;
        this->nmsThreshold = nmsThreshold;
        this->boxThreshold = boxThreshold;

        this->keep_ratio = keep_ratio;

        strcpy(this->imgPath, imgPath);

        for (int i = 0; i < 3; i++) {
            this->mean[i] = mean[i];
            this->std[i] = std[i];
        }


#if (defined WIN32 || defined _WIN32 )
#else
        /* 去掉可能的出现的/r */
        if(modelpath[strlen(modelpath) - 1] == 0x0D){
            modelpath[strlen(modelpath) - 1] = '\0';
        }
        if(labelPath[strlen(labelPath) - 1] == 0x0D){
            labelPath[strlen(labelPath) - 1] = '\0';
        }
        if(imgPath[strlen(imgPath) - 1] == 0x0D){
            imgPath[strlen(imgPath) - 1] = '\0';
        }
#endif


        labels = (char **) malloc(objNum * GT_OBJ_NAME_MAX_SIZE * sizeof(char));
        ret = loadLabelName(labelPath, labels, objNum);

#ifdef GT_USING_RKNN
        /* Create the neural network */
        printf("Loading mode...\n");
        int model_data_size = 0;
        unsigned char *model_data = load_model(modelpath, &model_data_size);


        int len = strlen(modelpath);
        if (modelpath[len - 4] == 'r' && modelpath[len - 3] == 'k' && modelpath[len - 2] == 'n' &&
            modelpath[len - 1] == 'n') {
            printf("the model format is rknn\n");
        } else if (modelpath[len - 3] == 'v' && modelpath[len - 2] == 'i' && modelpath[len - 1] == 's') {
            printf("the model format is vis\n");
            ///////  make the dictionarie key here  /////////////////////////////////////////////////////////////////////////////////////
            unsigned char *dict = (unsigned char *) malloc(model_data_size * sizeof(unsigned char));
            memset(dict, 0, model_data_size);
            for (int i = 0; i < model_data_size; i += 3) {
                int half = floor(model_data_size * 0.5);
                if (i < half) {
                    dict[i] = 'l';
                    dict[i + 1] = 'y';
                    dict[i + 2] = 'c';
                } else {
                    dict[i] = 'c';
                    dict[i + 1] = 'y';
                    dict[i + 2] = 'l';
                }
            }
            ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            decode(model_data, model_data, dict, model_data_size);
        } else {
            printf("Unrecognized model format\n");
            return;
        }


        ret = rknn_init(&ctx, model_data, model_data_size, 0);
        if (ret < 0) {
            printf("rknn_init error ret=%d\n", ret);
            return;
        }

        rknn_sdk_version version;
        ret = rknn_query(ctx, RKNN_QUERY_SDK_VERSION, &version, sizeof(rknn_sdk_version));
        if (ret < 0) {
            printf("rknn_init error ret=%d\n", ret);
            return;
        }
        printf("sdk version: %s driver version: %s\n", version.api_version, version.drv_version);

        ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
        if (ret < 0) {
            printf("rknn_init error ret=%d\n", ret);
            return;
        }
        printf("model input num: %d, output num: %d\n", io_num.n_input, io_num.n_output);

        rknn_tensor_attr input_attrs[io_num.n_input];
        memset(input_attrs, 0, sizeof(input_attrs));
        for (int i = 0; i < io_num.n_input; i++) {
            input_attrs[i].index = i;
            ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
            if (ret < 0) {
                printf("rknn_init error ret=%d\n", ret);
                return;
            }
            dump_tensor_attr(&(input_attrs[i]));
        }

        rknn_tensor_attr output_attrs[io_num.n_output];
        memset(output_attrs, 0, sizeof(output_attrs));
        for (int i = 0; i < io_num.n_output; i++) {
            output_attrs[i].index = i;
            ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
            dump_tensor_attr(&(output_attrs[i]));
            if (output_attrs[i].qnt_type != RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC ||
                output_attrs[i].type != RKNN_TENSOR_UINT8) {
                fprintf(stderr,
                        "The Demo required for a Affine asymmetric u8 quantized rknn model, but output quant type is %s, output data type is %s\n",
                        get_qnt_type_string(output_attrs[i].qnt_type), get_type_string(output_attrs[i].type));
                return;
            }
        }

        for (int i = 0; i < io_num.n_output; ++i) {
            out_scales.push_back(output_attrs[i].scale);
            out_zps.push_back(output_attrs[i].zp);
        }

        this->model_channel = 3;

        if (input_attrs[0].fmt == RKNN_TENSOR_NCHW) {
            printf("model is NCHW input fmt\n");
            this->model_width = input_attrs[0].dims[0];
            this->model_height = input_attrs[0].dims[1];
        } else {
            printf("model is NHWC input fmt\n");
            this->model_width = input_attrs[0].dims[1];
            this->model_height = input_attrs[0].dims[2];
        }

        printf("model input model_height=%d, model_width=%d, model_channel=%d\n", this->model_height, this->model_width,
               this->model_channel);
#else
        this->net = readNet(modelpath);
#endif
    }


    void YOLOV5::detect(cv::Mat &frame, std::vector<DetObject> &det_object) {
        float nms_threshold = this->nmsThreshold;
        float class_threshold = this->classThreshold;

        int newh = 0, neww = 0, padh = 0, padw = 0;
        cv::Mat dstimg = letterbox(frame, model_height, model_width, keep_ratio, &newh, &neww, &padh, &padw);

#ifdef GT_USING_RKNN
        int status = 0;

        void *drm_buf = NULL;
        int drm_fd = -1;
        int buf_fd = -1; // converted from buffer handle
        unsigned int handle;
        size_t actual_size = 0;
        int img_width = 0;
        int img_height = 0;
        int img_channel = 0;
        //rga_context rga_ctx;
        //drm_context drm_ctx;

        //struct timeval start_time, stop_time;
        int ret;
        //memset(&rga_ctx, 0, sizeof(rga_context));
        //memset(&drm_ctx, 0, sizeof(drm_context));

        //printf("post process config: class_threshold = %.2f, nms_threshold = %.2f\n", class_threshold, nms_threshold);

        // preprocess image
#ifdef GT_USING_RGA
        rga_utils.imageTransformation(dstimg.cols, dstimg.rows, rga_utils.IMG_TYPE_BGR888, dstimg.data, dstimg.cols,
                                      dstimg.rows, rga_utils.IMG_TYPE_RGB888, dstimg.data);
#else
        cvtColor(dstimg, dstimg, COLOR_BGR2RGB);  //#include <opencv2/opencv.hpp>
#endif

        img_height = dstimg.rows;
        img_width = dstimg.cols;
        img_channel = dstimg.channels();

        rknn_input inputs[1];
        memset(inputs, 0, sizeof(inputs));
        inputs[0].index = 0;
        inputs[0].type = RKNN_TENSOR_UINT8;
        inputs[0].size = model_width * model_height * model_channel;
        inputs[0].fmt = RKNN_TENSOR_NHWC;
        inputs[0].pass_through = 0;

        /*
        // DRM alloc buffer
        drm_fd = drm_init(&drm_ctx);
        drm_buf = drm_buf_alloc(&drm_ctx, drm_fd, img_width, img_height, img_channel * 8, &buf_fd, &handle, &actual_size);

        //memcpy(drm_buf, input_data, img_width * img_height * img_channel);
        memcpy(drm_buf, frame.data, img_width * img_height * img_channel);

        void *resize_buf = malloc(model_height * model_width * model_channel);

        // init rga context
        RGA_init(&rga_ctx);
        img_resize_slow(&rga_ctx, drm_buf, img_width, img_height, resize_buf, model_width, model_height);
        */

        //imwrite("./check.bmp", dstimg);
        inputs[0].buf = dstimg.data;

        rknn_inputs_set(ctx, io_num.n_input, inputs);

        rknn_output outputs[io_num.n_output];
        memset(outputs, 0, sizeof(outputs));
        for (int i = 0; i < io_num.n_output; i++) {
            outputs[i].want_float = 0;
        }

        ret = rknn_run(ctx, NULL);
        ret = rknn_outputs_get(ctx, io_num.n_output, outputs, NULL);

        //imwrite("./out.bmp", frame);
#else
        normalize(dstimg, this->mean, this->std);
        cv::Mat blob = blobFromImage(dstimg);
        this->net.setInput(blob);
        std::vector<cv::Mat> outs;
        this->net.forward(outs, this->net.getUnconnectedOutLayersNames());
#endif

        //std::cout << outs[0].data << endl;

        //post process

        detect_result_group_t detect_result_group;

#ifdef GT_USING_RKNN
        post_process_u8((uint8_t *) outputs[0].buf, (uint8_t *) outputs[1].buf, (uint8_t *) outputs[2].buf,
                        this->labels, this->objNum, this->prop_box_size, model_height, model_width, class_threshold,
                        nms_threshold, out_zps, out_scales, &detect_result_group);
#else
        post_process_fp((float*)outs[0].data, (float*)outs[1].data, (float*)outs[2].data, this->labels, this->objNum, this->prop_box_size, model_height, model_width, 0, 0, 1.0f, class_threshold, nms_threshold, &detect_result_group);
#endif

        // Draw Objects
        //char text[256];
        //const unsigned char blue[] = { 0, 0, 255 };
        //const unsigned char white[] = { 255, 255, 255 };
        for (int i = 0; i < detect_result_group.count; i++) {
            detect_result_t *det_result = &(detect_result_group.results[i]);
            //sprintf(text, "%s %.2f", det_result->name, det_result->prop);
            //printf("%s @ (%d %d %d %d) %f\n", det_result->name, det_result->box.left, det_result->box.top, det_result->box.right, det_result->box.bottom, det_result->prop);
            int x1 = det_result->box.left;
            int y1 = det_result->box.top;
            int x2 = det_result->box.right;
            int y2 = det_result->box.bottom;

            x1 = (x1 - padw) * (float) frame.cols / neww;
            y1 = (y1 - padh) * (float) frame.rows / newh;
            x2 = (x2 - padw) * (float) frame.cols / neww;
            y2 = (y2 - padh) * (float) frame.rows / newh;

            //draw box
            //cv::Scalar blue(255, 0, 0);
            //cv::Scalar red(0, 0, 255);
            //rectangle(frame, Point(x1, y1), Point(x2, y2), blue, 2);
            //putText(frame, text, Point(x1, y1 - 12), FONT_HERSHEY_SIMPLEX, 0.8, red, 1);

            DetObject obj;
            obj.rect = cv::Rect(x1, y1, x2 - x1, y2 - y1);
            //obj.label = det_result->name;
            sprintf(obj.label, det_result->name);
            obj.score = det_result->prop;
            obj.isShow = true;
            det_object.push_back(obj);
        }

#ifdef GT_USING_RKNN
        ret = rknn_outputs_release(ctx, io_num.n_output, outputs);
#endif

        return;
    }

    void YOLOV5::getResult() {
        //do nothing
    }

    char *YOLOV5::getImgPath() { return this->imgPath; }

    float YOLOV5::getClassThreshold() { return this->classThreshold; }

}
