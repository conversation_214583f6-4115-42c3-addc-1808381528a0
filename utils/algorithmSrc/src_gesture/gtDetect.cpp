#include"gtDetect.h"


#if (defined WIN32 || defined _WIN32 )
#pragma warning(disable:4996)
#endif


using namespace cv;
#ifdef GT_USING_RKNN
#else
using namespace dnn;
#endif
using namespace std;

namespace gt {
    Detect::Detect() {}

    Detect::~Detect() {
        delete yolov5_model;
    }

    void Detect::init(int img_width, int img_height) {
        this->input_width = img_width;
        this->input_height = img_height;

        yolov5_model = new gt::YOLOV5();
        yolov5_model->init("./model/gesture.ini");

        IDcnt = 0;

        det_object.clear();
    }

    void Detect::detect(cv::Mat srcimg) {
        if (srcimg.cols != this->input_width || srcimg.rows != this->input_height)
            resize(srcimg, srcimg, Size(this->input_width, this->input_height), INTER_AREA);

        float class_threshold;

        det_object.clear();

        //detect
        class_threshold = yolov5_model->getClassThreshold();
        yolov5_model->detect(srcimg, det_object);
        yolov5_model->getResult();


        //limit
        for (int i = 0; i < this->det_object.size(); i++) {
            Rect rect = this->det_object[i].rect;
            int left = GT_MAX(0, rect.x);
            int right = GT_MIN(srcimg.cols - 1, rect.x + rect.width);
            int top = GT_MAX(0, rect.y);
            int bottom = GT_MIN(srcimg.rows - 1, rect.y + rect.height);
            this->det_object[i].rect = Rect(left, top, right - left, bottom - top);
        }
    }

    objectInfo_t Detect::getResult() {
        objectInfo_t obj_info;

        obj_info.alarmInfo = 0;
        obj_info.faultInfo = 0;

        for (int i = 0; i < det_object.size(); i++) {
            if (det_object[i].isShow == false)
                continue;

            object_t obj;

            Rect rect = det_object[i].rect;
            obj.mLeft = rect.x;
            obj.mRight = rect.x + rect.width;
            obj.mTop = rect.y;
            obj.mBottom = rect.y + rect.height;

            obj.mId = det_object[i].Id;
            sprintf(obj.label, det_object[i].label);
            obj.mScore = det_object[i].score;

            obj_info.objects.push_back(obj);
        }

        return obj_info;
    }

    cv::Mat Detect::draw(cv::Mat srcimg, objectInfo_t object_info) {
        //draw
        //Mat showimg(srcimg);
        Mat showimg = srcimg.clone();

        drawPred(object_info.objects, showimg);

        return showimg;
    }

    char *Detect::getImgPath() {
        return yolov5_model->getImgPath();
    }
}
