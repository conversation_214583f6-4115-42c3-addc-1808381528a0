#ifndef _GT_YOLOV5_H_
#define _GT_YOLOV5_H_

#include "gtShareDefine.h"
#include "gtFunction.h"

#include "ini/ini.h"

#ifdef GT_USING_RKNN

#include "rknn_api.h"  //#include "rknn/rknn_api.h"

#endif


#include <stdint.h>

#define GT_OBJ_NAME_MAX_SIZE 16
#define GT_OBJ_NUMB_MAX_SIZE 64

namespace gt {

    typedef struct _BOX_RECT {
        int left;
        int right;
        int top;
        int bottom;
    } BOX_RECT;

    typedef struct __detect_result_t {
        char name[GT_OBJ_NAME_MAX_SIZE];
        BOX_RECT box;
        float prop;
        int class_index;
    } detect_result_t;

    typedef struct _detect_result_group_t {
        int id;
        int count;
        detect_result_t results[GT_OBJ_NUMB_MAX_SIZE];
    } detect_result_group_t;


    class YOLOV5 {
    public:
        YOLOV5();

        ~YOLOV5();

        void init(char *iniPath);

        void detect(cv::Mat &frame, std::vector<DetObject> &det_object);

        void getResult();

        char *getImgPath();

        float getClassThreshold();

    private:
        int objNum;
        int prop_box_size;

        bool keep_ratio;

        float mean[3];
        float std[3];

        float boxThreshold;
        float classThreshold;
        float nmsThreshold;

        int model_width;
        int model_height;
        int model_channel;

        char imgPath[200];

        char **labels;

#ifdef GT_USING_RKNN
        rknn_context ctx;
        rknn_input_output_num io_num;
        std::vector<float> out_scales;
        std::vector<uint32_t> out_zps;
#else
        cv::dnn::Net net;
#endif
    };

}


#endif
