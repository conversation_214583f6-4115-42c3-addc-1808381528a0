//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/10/24.
//

#include "Detecter_Gesture.h"
#include "gtShareDefine.h"

#ifdef GT_G3_APP_IN_OUT

#include <unistd.h>
#include "Detecter_BSD.h"

#endif

#ifdef GT_USING_RGA

#include "XuRGAUtils.h"

#endif


#ifdef GT_G3_APP_IN_OUT
#else
#ifdef GT_USING_ENCRYPT_CHIP_XS508
#include "XS508_imp.h"
#endif
#endif

#include "gtDetect.h"

using namespace cv;
using namespace std;


namespace vis {


#ifdef GT_USING_RGA
    XuRGAUtils rga_utils;
#endif


#ifdef GT_G3_APP_IN_OUT

    int Detecter_Gesture::init(int cameraId, DetectDataCallback &detectDataCallback) {
        curCameraId = cameraId;
        detectionResult.curCameraType.cameraId = curCameraId;
        curDetectDataCallback = &detectDataCallback;
        return 0;
    }

#endif

#ifdef GT_G3_APP_IN_OUT

    void Detecter_Gesture::run()
#else
    void run()
#endif
    {
        std::string pthreadName = "Ges_";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        int ret = -1;

        gt::Detect *detect = new gt::Detect();

        const int input_width = 1280;
        const int input_height = 720;

        gt::objectInfo_t object_info;
        object_info.alarmInfo = 0;
        object_info.faultInfo = 0;
        object_info.objects.clear();

        Mat srcimg = Mat::zeros(input_height, input_width, CV_8UC3);

        detect->init(input_width, input_height);

#ifdef GT_G3_DEMO_IN_OUT
        uint8_t *cam_data;
CamCap G3_cap;

    // 摄像头初始化
cam_data = (uint8_t *)malloc(1280 * 1024 * 2);

if (G3_cap.setDevice("/dev/video0") < 0)
{
    perror("setDevice fail");
    return;
}

if (G3_cap.setFmt(input_width, input_height, V4L2_PIX_FMT_UYVY) < 0)
{
    perror("setFmt fail");
    return;
}

if (G3_cap.streamOn() < 0)
{
    perror("streamOn fail");
    return;
}
    Mat yuvImg = Mat::zeros(input_height, input_width, CV_8UC2);

    // 视频输出初始化
    const int G3_vo_width = 720;
    const int G3_vo_height = 576;
voInit();
    Mat displayImg = Mat::zeros(G3_vo_height, G3_vo_width, CV_8UC3);
    Mat displayYUVImg = Mat::zeros(G3_vo_height, G3_vo_width, CV_8UC2);
#elif defined GT_G3_APP_IN_OUT
        uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
        Mat yuvImg = Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);
#endif

#ifdef GT_RUN_VIDEO
        VideoCapture capture("20190723155320.mp4");   //20190723155320.mp4   20220726135704.mp4
if(!capture.isOpened())
    cout << "fail to open!" << endl;
#endif

        int frame_cnt = 0;
        /* 是否需要清理识别信息 */
        bool needclean = false;
        while (1) {
//                printf("--------------frame %d--------------------------\n", frame_cnt);

            frame_cnt++;


#ifdef GT_G3_DEMO_IN_OUT
            ret = G3_cap.getFmtData(cam_data, 1280 * 720 * 2);  // 获取摄像头图像   ret = G3_cap.getFmtData(cam_data, 1280 * 1024 * 2);
    if (ret < 0)
    {
        perror("getFmtData fail");
        return;
    }
    else if (0 == ret)
    {
        printf("timeout\n");
        continue;
    }

    //memcpy(yuvImg.data, (unsigned char*)cam_data, input_width * input_height * 2);
    yuvImg.data = (unsigned char*)cam_data;
    cvtColor(yuvImg, srcimg, COLOR_YUV2BGR_UYVY);
    //imwrite("out.jpg", srcimg);
    //waitKey(0);
    //return;
#elif defined GT_G3_APP_IN_OUT
            ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData,curVehicleStatus,&needclean);
            if (ret != 0) {
                usleep(5 * 1000);
                continue;
            }
            detectionResult.speed = curVehicleStatus.speed;

            double BeginTime_alg = (double) cv::getTickCount();
//                printf("get yuv data success, cameraId=%d size=%d  \n",cameraYuvData.getCameraId(),cameraYuvData.getDataLen());

#ifdef GT_USING_RGA
            rga_utils.imageTransformation(input_width, input_height, rga_utils.IMG_TYPE_NV21,
                                          cameraYuvData, input_width, input_height,
                                          rga_utils.IMG_TYPE_BGR888, srcimg.data);
#else
            yuvImg.data = cameraYuvData.getCurYuvData();
    cvtColor(yuvImg, srcimg, COLOR_YUV2BGR_NV12);
#endif

            //imwrite("out.jpg", srcimg);
            //return;
            //waitKey(0);
            //return;
#else
#ifdef GT_RUN_VIDEO
            capture >> srcimg;
#else
            srcimg = imread(detect->getImgPath());
#endif
#endif

            detect->detect(srcimg);
            object_info = detect->getResult();

            double time2_alg = ((double) cv::getTickCount() - BeginTime_alg) / cv::getTickFrequency();
            int time3_alg = (int) (time2_alg * 1000 + 0.5);
            printf("$$$$$$$$$$$$$$$$$ Ges total time %dms    cameraId:%d \n", time3_alg, curCameraId);
            printf("\n");

#if (defined WIN32 || defined _WIN32 )
            Mat showimg = detect->draw(srcimg, object_info);

    namedWindow("show");
    imshow("show", showimg);
    waitKey(0);
#else
#ifdef GT_G3_DEMO_IN_OUT
            Mat showimg = detect->draw(srcimg, object_info);
    resize(showimg, displayImg, Size(G3_vo_width, G3_vo_height), INTER_NEAREST);
    rgb2yuv422_image((unsigned char *)displayImg.data, (unsigned char *)displayYUVImg.data, G3_vo_width, G3_vo_height);
    voSetFmt((unsigned char *)displayYUVImg.data);  //cam_data  displayYUVImg.data
    // printf("get fmt cam_data size : %d\n", ret);
    // save_frame_to_file("./cap.yuv", cam_data, ret);
#elif defined GT_G3_APP_IN_OUT
            /* 把识别信息发出去 */
            curDetectDataCallback->onGetObjectInfo_GES(object_info, detectionResult);
//        printf("~~~~~~~~~~~~~~~~alarmDecisionBsd.parseObjectInfos %d    \n",object_info.objects.size());
            lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
//        printf("~~~~~~~~~~~~~~~~lastDetectTime = XuTimeUtil::getInstance().currentTimeMillis()    \n");
#else
            Mat showimg = detect->draw(srcimg, object_info);
    printf("object size = %d\n", object_info.objects.size());
    for (int t = 0; t < object_info.objects.size(); t++)
    {
        gt::object_t obj = object_info.objects[t];
          printf("label = %s, score = %f, left = %d, right = %d, top = %d, bottom = %d\n", obj.label, obj.mScore, obj.mLeft, obj.mRight, obj.mTop, obj.mBottom);
    }
    //imwrite("out.jpg", showimg);
#endif
#endif

        }

        delete detect;

#if (defined WIN32 || defined _WIN32 )
        destroyAllWindows();
#endif
#ifdef GT_G3_DEMO_IN_OUT
        voFree();  //释放视频输出资源
#endif

        return;
    }

    bool Detecter_Gesture::isDetectOpen() const {
        return detectOpen;
    }

    __time_t Detecter_Gesture::getLastDetectTime() const {
        return lastDetectTime;
    }

} // vis