#ifndef _GT_DETECT_H_
#define _GT_DETECT_H_


#include "gtYOLOV5.h"
#include "gtShareDefine.h"
#include "gtFunction.h"

#ifdef GT_G3_DEMO_IN_OUT
#include "CamCap/CamCap.h"
#include "vo/vo.h"
#endif


namespace gt {

    class Detect {
    public:
        Detect();

        ~Detect();

        void init(int img_width, int img_height);

        void detect(cv::Mat srcimg);

        objectInfo_t getResult();

        cv::Mat draw(cv::Mat srcimg, objectInfo_t object_info);

        char *getImgPath();

    private:
        unsigned int IDcnt;
        int input_width;
        int input_height;
        int netType;

        YOLOV5 *yolov5_model;

        std::vector<DetObject> det_object;
    };

}

#endif