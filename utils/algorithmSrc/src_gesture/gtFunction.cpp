﻿
#include "gtFunction.h"

#ifdef GT_USING_RGA

#include "XuRGAUtils.h"

#endif


using namespace cv;


#ifdef GT_USING_RGA
extern XuRGAUtils rga_utils;
#endif

namespace gt {

#ifdef GT_G3_DEMO_IN_OUT

    /************************************************************************/
    /* 按照opencv数据存储格式，函数传参顺序为b、g、r                        */
    /************************************************************************/
    void rgb2yuv_pixel(uchar b, uchar g, uchar r, uchar& y, uchar& u, uchar& v)
    {
        //rgb转yuv公式，参考资料<a target=_blank href="http://www.cnblogs.com/dwdxdy/p/3713990.html">http://www.cnblogs.com/dwdxdy/p/3713990.html</a>
        //y = 0.299 * r + 0.587 * g + 0.114 * b;
        //u = -0.1687 * r - 0.3313 * g + 0.5 * b + 128;
        //v = 0.5 * r - 0.4187 * g - 0.0813 * b + 128;

        //y =   0.257*r + 0.504*g + 0.098*b + 16;
        //u = -0.148*r - 0.291*g + 0.439*b + 128;
        //v  =  0.439*r - 0.368*g - 0.071*b + 128;

        y = 0.257 * r + 0.504 * g + 0.098 * b + 16;
        u = -0.148 * r - 0.291 * g + 0.439 * b + 128;
        v = 0.439 * r - 0.368 * g - 0.071 * b + 128;
    }

    /************************************************************************/
    /* rgb24转yuv422                                                        */
    /************************************************************************/
    void rgb2yuv422_image(uchar* pRgb, uchar* pYuv, int width, int height)
    {
        //考虑到每两个rgb像素对应一个yuyv组合，因此，width应为2的倍数
        int width1 = width = width / 2 * 2;

        for (int h = 0; h < height; h++)
        {
            uchar* ptr1 = pRgb + h * width * 3;
            uchar* ptr2 = pYuv + h * width * 2;

            for (int w = 0; w < width1; w += 2)
            {
                uchar y1, u1, v1, y2, u2, v2;
                rgb2yuv_pixel(*ptr1++, *ptr1++, *ptr1++, y1, u1, v1);
                rgb2yuv_pixel(*ptr1++, *ptr1++, *ptr1++, y2, u2, v2);
                //u、v分量取平均值
                uchar u = (u1 + u2) >> 1;  //(u1 + u2) / 2
                uchar v = (v1 + v2) >> 1;  //(v1 + v2) / 2
                *ptr2++ = u;  //y1
                *ptr2++ = y1;  //u
                *ptr2++ = v;  //y2
                *ptr2++ = y2;  //v
            }
        }
    }
#endif

    float get_iou_value(Rect rect1, Rect rect2) {
        int xx1, yy1, xx2, yy2;

        xx1 = max(rect1.x, rect2.x);
        yy1 = max(rect1.y, rect2.y);
        xx2 = min(rect1.x + rect1.width - 1, rect2.x + rect2.width - 1);
        yy2 = min(rect1.y + rect1.height - 1, rect2.y + rect2.height - 1);

        int insection_width, insection_height;
        insection_width = max(0, xx2 - xx1 + 1);
        insection_height = max(0, yy2 - yy1 + 1);

        float insection_area, union_area, iou;
        insection_area = float(insection_width) * insection_height;
        union_area = float(rect1.width * rect1.height + rect2.width * rect2.height - insection_area);
        iou = insection_area / union_area;
        return iou;
    }

    int sum(int a, int b)  //白盒测试的测试程序
    {
        if (a == 0)
            return b;
        else if (b == 0)
            return a;
        else
            return (a + b);
    }

    bool cmp(BBOX &b1, BBOX &b2) {
        return b1.confidence > b2.confidence;
    }

    void nms_boxes(std::vector<cv::Rect> &boxes, std::vector<float> &confidences, float confidencesThreshold,
                   float nmsThreshold, std::vector<int> &indices) {
        //非极大值抑制
        //input:  boxes: 原始检测框集合;
        //input:  confidences：原始检测框对应的置信度值集合
        //input:  confThreshold 和 nmsThreshold 分别是 检测框置信度阈值以及做nms时的阈值
        //output:  indices  经过上面两个阈值过滤后剩下的检测框的index

        BBOX bbox;
        std::vector<BBOX> bboxes;
        std::vector<bool> need_delete(boxes.size(), false);
        int i, j;
        for (i = 0; i < boxes.size(); i++) {
            bbox.box = boxes[i];
            bbox.confidence = confidences[i];
            bbox.index = i;
            bboxes.push_back(bbox);
        }
        sort(bboxes.begin(), bboxes.end(), cmp);

        for (i = 0; i < bboxes.size(); i++) {
            if (need_delete[i])
                continue;
            if (bboxes[i].confidence < confidencesThreshold)
                break;
            indices.push_back(bboxes[i].index);
            for (j = i + 1; j < bboxes.size(); j++) {
                if (need_delete[j])
                    continue;
                float iou = get_iou_value(bboxes[i].box, bboxes[j].box);
                if (iou > nmsThreshold) {
                    need_delete[j] = true;

                }
            }
        }
    }

    cv::Mat
    letterbox(cv::Mat srcimg, int inpHeight, int inpWidth, bool keep_ratio, int *newh, int *neww, int *top, int *left) {
        //图像缩放及添加pad至目标尺寸
        //input: srcimg 原始图像
        //input: inpHeight及inpWidth 设定的输入神经网络的尺寸
        //input: keep_ratio 若保持原始图像的长宽比，则在缩放后添加pad进行填充
        //output：newh及neww 原始图像等比缩放后的宽高，不算pad
        //output：top及left 原始图像等比缩放后，pad之后的偏移位置
        //return: 缩放及pad后的图像

        int srch = srcimg.rows, srcw = srcimg.cols;
        *newh = inpHeight;
        *neww = inpWidth;
        Mat dstimg;
        if (keep_ratio && srch != srcw) {
            float hw_scale = (float) srch / srcw;
            if (hw_scale > 1) {
                *newh = inpHeight;
                *neww = int(inpWidth / hw_scale);
#ifdef GT_USING_RGA
                dstimg.create(*newh, *neww, CV_8UC3);
                rga_utils.imageTransformation(srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data, *neww,
                                              *newh, rga_utils.IMG_TYPE_BGR888, dstimg.data);
#else
                resize(srcimg, dstimg, Size(*neww, *newh), INTER_AREA);
#endif
                *left = int((inpWidth - *neww) * 0.5);
                copyMakeBorder(dstimg, dstimg, 0, 0, *left, inpWidth - *neww - *left, BORDER_CONSTANT, 0);
            } else {
                *newh = (int) inpWidth * hw_scale;
                *neww = inpWidth;
#ifdef GT_USING_RGA
                dstimg.create(*newh, *neww, CV_8UC3);
                rga_utils.imageTransformation(srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data, *neww,
                                              *newh, rga_utils.IMG_TYPE_BGR888, dstimg.data);
#else
                resize(srcimg, dstimg, Size(*neww, *newh), INTER_AREA);
#endif
                *top = (int) (inpHeight - *newh) * 0.5;
                copyMakeBorder(dstimg, dstimg, *top, inpHeight - *newh - *top, 0, 0, BORDER_CONSTANT, 0);
            }
        } else {
#ifdef GT_USING_RGA
            dstimg.create(*newh, *neww, CV_8UC3);
            rga_utils.imageTransformation(srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data, *neww,
                                          *newh, rga_utils.IMG_TYPE_BGR888, dstimg.data);
#else
            resize(srcimg, dstimg, Size(*neww, *newh), INTER_AREA);
#endif
        }

        //imwrite("out.jpg", dstimg);

        return dstimg;
    }

    void normalize(cv::Mat &img, float mean[3], float std[3]) {
        img.convertTo(img, CV_32F);
        int i = 0, j = 0;
        const float scale = 1.0 / 255.0;
        for (i = 0; i < img.rows; i++) {
            float *pdata = (float *) (img.data + i * img.step);
            for (j = 0; j < img.cols; j++) {
                //printf("origin %f %f %f  ", pdata[0], pdata[1], pdata[2]);

                pdata[0] = (pdata[0] * scale - mean[0]) / std[0];
                pdata[1] = (pdata[1] * scale - mean[1]) / std[1];
                pdata[2] = (pdata[2] * scale - mean[2]) / std[2];

                //printf("%f %f %f\n", pdata[0], pdata[1], pdata[2]);

                pdata += 3;
            }
        }
    }

    void drawPred(std::vector<object_t> objects, Mat &frame)   // Draw the predicted bounding box
    {
        cv::Scalar black(0, 0, 0);
        cv::Scalar red(0, 0, 255);
        cv::Scalar blue(255, 0, 0);
        cv::Scalar green(0, 255, 0);
        cv::Scalar yellow(0, 255, 255);


        for (int i = 0; i < objects.size(); i++) {
            std::string label = objects[i].label;
            int left = objects[i].mLeft;
            int top = objects[i].mTop;
            int right = objects[i].mRight;
            int bottom = objects[i].mBottom;
            float conf = objects[i].mScore;

            cv::Scalar color = black;
            /*
            if (label == "person" || label == "rider")
                color = green;
            if (label == "car" || label == "bus" || label == "truck" || label == "train")
                color = red;
            if (label == "tl_green" || label == "tl_red" || label == "tl_yellow" || label == "tl_none")
                color = yellow;
            if (label == "traffic sign")
                color = blue;
            if (label == "bike" || label == "motor")
                color = black;
            */

            if (label == "person" || label == "rider")
                color = red;
            if (label == "car")
                color = green;
            if (label == "bus")
                color = yellow;
            if (label == "truck")
                color = blue;

            //Draw a rectangle displaying the bounding box
            rectangle(frame, Point(left, top), Point(right, bottom), color, 2);

            //Get the label for the class name and its confidence
            std::string label_score = format("%.2f", conf);
            std::string label_display = label + ":" + label_score;

            //Display the label at the top of the bounding box
            int baseLine;
            Size labelSize = getTextSize(label_display, FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);
            top = max(top, labelSize.height);
            //rectangle(frame, Point(left, top - int(1.5 * labelSize.height)), Point(left + int(1.5 * labelSize.width), top + baseLine), Scalar(0, 255, 0), FILLED);
            putText(frame, label_display, Point(left, top - 5), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);

            //Display the ID
            std::string id = format("%d", objects[i].mId);
            std::string ID_display = "ID:" + id;
            putText(frame, ID_display, Point(left + 3, top + 20), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);
        }
    }
}