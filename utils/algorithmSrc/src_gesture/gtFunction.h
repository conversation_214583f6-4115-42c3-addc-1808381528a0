#ifndef _GT_FUNCTION_H_
#define _GT_FUNCTION_H_


#include "gtShareDefine.h"

namespace gt {

    typedef struct {
        cv::Rect box;
        float confidence;
        int index;
    } BBOX;


#ifdef GT_G3_DEMO_IN_OUT
    void rgb2yuv_pixel(uchar b, uchar g, uchar r, uchar& y, uchar& u, uchar& v);  // ����opencv���ݴ洢��ʽ����������˳��Ϊb��g��r
    void rgb2yuv422_image(uchar* pRgb, uchar* pYuv, int width, int height);  //rgb24תyuv422
#endif

    float get_iou_value(cv::Rect rect1, cv::Rect rect2);

    void nms_boxes(std::vector<cv::Rect> &boxes, std::vector<float> &confidences, float confidencesThreshold,
                   float nmsThreshold, std::vector<int> &indices);

    cv::Mat
    letterbox(cv::Mat srcimg, int inpHeight, int inpWidth, bool keep_ratio, int *newh, int *neww, int *top, int *left);

    void normalize(cv::Mat &img, float mean[3], float std[3]);

    void drawPred(std::vector<object_t> objects, cv::Mat &frame);   // Draw the predicted bounding box

}

#endif
