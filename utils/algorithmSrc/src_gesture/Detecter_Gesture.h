//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/10/24.
//

#ifndef VIS_G3_SOFTWARE_DETECTER_GESTURE_H
#define VIS_G3_SOFTWARE_DETECTER_GESTURE_H

#include "DetectDataCallback.h"
#include <Poco/Runnable.h>

namespace vis {

    class Detecter_Gesture : public Poco::Runnable {
    public:
        /**
         * 初始化
         *
         * @param cameraId : 算法对应的相机ID
         * @param detectUnitManager ：DetectUnitManager的对象
         *
         * @return 结果     0：成功  其他：失败
         *
         * */
        int init(int cameraId, DetectDataCallback &detectDataCallback);

        void run() override;


        bool isDetectOpen() const;

        __time_t getLastDetectTime() const;




    private:

        DetectDataCallback *curDetectDataCallback;

        int curCameraId = -1;


        bool detectOpen = false;

        /* 上一帧图像解析成功的时间 */
        __time_t lastDetectTime = 0;

        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;


        /* 当前的车况 */
        Vehicle_RealtimeStatus curVehicleStatus = {};


    };

} // vis

#endif //VIS_G3_SOFTWARE_DETECTER_GESTURE_H
