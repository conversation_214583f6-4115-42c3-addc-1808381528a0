#include "ini.h"
#include <string.h>
#include <stdio.h>

#if (defined WIN32 || defined _WIN32 )
#pragma warning(disable:4996)
#endif

content *pContent = NULL;
char gfilename[255];

void iniGetInt(const char *lpSection, const char *lpKey, int *nDefault) {
    char sz[DEF_PROFILE_NUM_LEN + 1] = "";

    iniGetString(lpSection, lpKey, sz);
    sscanf(sz, "%d", nDefault);
}

void iniGetFloat(const char *lpSection, const char *lpKey, float *nDefault) {
    char sz[DEF_PROFILE_NUM_LEN + 1] = "";

    iniGetString(lpSection, lpKey, sz);
    sscanf(sz, "%f", nDefault);
}

BOOL iniWriteInt(const char *lpSection, const char *lpKey, unsigned int nValue,
                 const char *lpComment) {
    char szValue[DEF_PROFILE_NUM_LEN + 1] = "";

    sprintf(szValue, "%d", nValue);
    return iniWriteString(lpSection, lpKey, szValue, lpComment);
}

void iniGetBool(const char *lpSection, const char *lpKey, BOOL *bDefault) {
    char sz[DEF_PROFILE_NUM_LEN + 1] = "";

    iniGetString(lpSection, lpKey, sz);
    sscanf(sz, "%d", bDefault);
    *bDefault > 0 ? 1 : 0;
}

BOOL iniWriteBool(const char *lpSection, const char *lpKey, BOOL bDefault, const char *lpComment) {
    char szValue[DEF_PROFILE_NUM_LEN + 1] = "";

    sprintf(szValue, "%d", bDefault > 0 ? 1 : 0);
    return iniWriteString(lpSection, lpKey, szValue, lpComment);
}

DWORD iniInvertBool(const char *lpSection, const char *lpKey) {
    BOOL bDefault;
    char szValue[DEF_PROFILE_NUM_LEN + 1] = "";


    iniGetBool(lpSection, lpKey, &bDefault);
    bDefault > 0 ? 1 : 0;
    sprintf(szValue, "%d", !bDefault);

    struct _section *psection = iniGetSection(lpSection);
    if (psection == NULL) {
        return ERROR_NO_SECTION;
    }

    struct _record *precord = iniGetRecord(psection, lpKey);
    if (precord == NULL) {
        return ERROR_NO_KEY;
    }

    strcpy(precord->key, lpKey);
    {
        strcpy(precord->value, szValue);
    }
    return ERROR_OK;
}

DWORD iniGetString(const char *lpSection, const char *lpKey, char *lpBuffer) {
    struct _section *psection = iniGetSection(lpSection);
    if (psection == NULL) {
        return ERROR_NO_SECTION;
    }
    struct _record *precord = iniGetRecord(psection, lpKey);
    if (precord == NULL) {
        return ERROR_NO_KEY;
    } else {
        strcpy(lpBuffer, precord->value);
        return ERROR_OK;
    }
}

BOOL iniWriteString(const char *lpSection, const char *lpKey, const char *lpValue,
                    const char *lpComment) {
    struct _section *psection = iniGetSection(lpSection);
    if (psection == NULL) {
        iniAddSection(lpSection, "");
        psection = iniGetSection(lpSection);
    }

    struct _record *precord = iniGetRecord(psection, lpKey);
    if (precord == NULL) {
        precord = (struct _record *) malloc(sizeof(struct _record));
        if (precord == NULL) {
            printf("cannot malloc memory !\n");
            return ERROR;
        }
        precord->next = NULL;

        psection->sizeRecord++;

        if (psection->first == NULL) {
            psection->first = precord;
            psection->last = precord;
        } else {
            psection->last->next = precord;
            psection->last = precord;
        }
    }
    if ((lpComment[0] != '#' || lpComment[0] != ';') && (strlen(lpComment) > 0)) {
        sprintf(precord->comments, "#%s", lpComment);
    } else {
        strcpy(precord->comments, lpComment);
    }
    strcpy(precord->key, lpKey);
    {
        strcpy(precord->value, lpValue);
    }
    return ERROR_OK;
}

bool iniStart(const char *lpFile) {
    if (pContent == NULL)
        pContent = (content *) malloc(sizeof(content));
    if (pContent == NULL) {
        printf("cannot malloc memory !\n");
        return false;
    }

    pContent->sizeSection = 0;
    pContent->first = NULL;
    pContent->last = NULL;

    memcpy(gfilename, lpFile, sizeof(gfilename));
    if (iniLoad(gfilename) == false) {
        printf("initial parse file error !\n");
        return false;
    };
    return true;
}

bool iniLoad(const char *lpFile) {
    FILE *fp;
    char buffer[255];
    char comments[1024];
    char NowSection[255];
    char key[255];
    char value[255];
    char *pdest;
    int index;

    strcpy(comments, "");
    strcpy(NowSection, "");

    if ((fp = fopen(lpFile, "r")) != NULL) {
        while (fgets(buffer, sizeof(buffer), fp) != NULL) {
            if (buffer[strlen(buffer) - 1] == '\n') {
                buffer[strlen(buffer) - 1] = '\0';
            }
            switch (buffer[0]) {
                case '[':
                    pdest = strrchr(buffer, ']');
                    if (pdest == NULL) {
                        fclose(fp);
                        printf("parse ini error!\n");
                        return false;
                    }
                    index = pdest - buffer;
                    memcpy(NowSection, buffer + 1, index - 1);
                    NowSection[index - 1] = '\0';
                    iniAddSection(NowSection, comments);
                    strcpy(comments, "");
                    break;
                case '#' :
                case ';' :
                    if (strlen(comments) > 0) {
                        strcat(comments, "\n");
                    }
                    strcat(comments, buffer);
                    break;
                default :
                    pdest = strrchr(buffer, '=');
                    if (pdest == NULL) {
                        fclose(fp);
                        printf("parse ini error\n");
                        return false;
                    }
                    index = pdest - buffer;
                    memcpy(key, buffer, index);
                    key[index] = '\0';
                    memcpy(value, buffer + index + 1, strlen(buffer) - index);

                    if (strcmp(NowSection, "") == 0) {
                        fclose(fp);
                        printf("parse ini error\n");
                        return false;
                    } else {
                        iniWriteString(NowSection, key, value, comments);
                        strcpy(comments, "");
                    }
                    break;
            }
        }
        fclose(fp);
    } else {
        printf("open file error !\n");
        return false;
    }
    return true;
}

void iniAddSection(const char *lpSection, const char *lpComment) {
    struct _section *psection;
    psection = iniGetSection(lpSection);

    if (psection == NULL) {
        psection = (struct _section *) malloc(sizeof(struct _section));

        if (psection == NULL) {
            printf("cannot malloc memory !\n");
            return;
        }

        strcpy(psection->name, lpSection);

        if ((lpComment[0] != '#' || lpComment[0] != ';') && (strlen(lpComment) > 0)) {
            sprintf(psection->comments, "#%s", lpComment);
        } else {
            strcpy(psection->comments, lpComment);
        }

        psection->first = NULL;
        psection->last = NULL;
        psection->next = NULL;
        psection->sizeRecord = 0;

        pContent->sizeSection++;

        if (pContent->first == NULL) {
            pContent->first = psection;
            pContent->last = psection;
        } else {
            pContent->last->next = psection;
            pContent->last = psection;
        }
    } else {
        strcpy(psection->name, lpSection);
        if ((lpComment[0] != '#' || lpComment[0] != ';') && (strlen(lpComment) > 0)) {
            sprintf(psection->comments, "#%s", lpComment);
        } else {
            strcpy(psection->comments, lpComment);
        }
    }
}

struct _section *iniGetSection(const char *lpSection) {
    bool found = false;

    struct _section *psection = pContent->first;
    while (psection != NULL) {
        if (strcmp(psection->name, lpSection) == 0) {
            found = true;
            break;
        }
        psection = psection->next;
    }

    if (found == true) {
        return psection;
    } else {
        return NULL;
    }
}

struct _record *iniGetRecord(struct _section *psection, const char *lpKey) {
    bool found = false;
    struct _record *pRecord;

    if (psection != NULL) {
        pRecord = psection->first;

        while (pRecord != NULL) {
            if (strcmp(lpKey, pRecord->key) == 0) {
                found = true;
                break;
            }
            pRecord = pRecord->next;
        }
        if (found == true) {
            return pRecord;
        } else {
            return NULL;
        }
    } else {
        return NULL;
    }
}

char *iniGetValue(const char *lpSeciton, const char *lpKey) {
    struct _section *psection = iniGetSection(lpSeciton);
    if (psection == NULL) {
        return "NO Section!";
    }
    struct _record *precord = iniGetRecord(psection, lpKey);
    if (precord == NULL) {
        return "No Key!";
    } else {
        return precord->value;
    }
}

bool iniSave(const char *lpFile) {
    FILE *fp;
    struct _section *psection = pContent->first;
    struct _record *precord;


    if ((fp = fopen(lpFile, "w")) == NULL) {
        printf("open file error\n");
        return false;
    }

    while (psection != NULL) {
        if (strlen(psection->comments) != 0) {
            fprintf(fp, "%s\n", psection->comments);
        }
        fprintf(fp, "[%s]\n", psection->name);
        precord = psection->first;
        while (precord != NULL) {
            if (strlen(precord->comments) != 0) {
                fprintf(fp, "%s\n", precord->comments);
            }
            fprintf(fp, "%s=%s\n", precord->key, precord->value);

            precord = precord->next;
        }
        psection = psection->next;
    }
    fclose(fp);
    return true;
}

int iniRemoveAllSection(const char *lpSection) {
    struct _section *psection = iniGetSection(lpSection);
    struct _record *precord;
    int remove = 0;

    if (psection == NULL)
        return 0;

    precord = psection->first;
    while (precord != NULL) {
        psection->first = precord->next;
        psection->sizeRecord--;
        free(precord);
        remove++;
        precord = psection->first;
    }
    return remove;
}

int iniRemoveSelSection(const char *lpSection, const char *lpKey) {
    struct _section *psection = iniGetSection(lpSection);
    struct _record *precord1, *precord2;
    int remove = 0;

    if (psection == NULL)
        return 0;

    precord1 = psection->first;

    if (precord1 == NULL)
        return 0;

    if (strcmp(lpKey, precord1->key) == 0) {
        psection->first = precord1->next;
        psection->sizeRecord--;
        free(precord1);
        return 1;
    }

    while (precord1 != NULL) {
        if (precord1->next != NULL) {
            if (strcmp(lpKey, precord1->next->key) == 0) {
                precord2 = precord1->next;
                precord1->next = precord1->next->next;
                psection->sizeRecord--;
                free(precord2);
                remove = 1;
                break;
            }
        }
        precord1 = precord1->next;
    }
    return remove;
}
