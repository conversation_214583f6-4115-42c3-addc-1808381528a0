#include <fstream>
#include <iostream>
#include <string>
#include <opencv2/opencv.hpp>
#include <vector>
#include "fb_resnet18.h"
#include "fb_ini.h"

#define FBRESNET_CLASS_NUM 2

// 已经在main中定义全局变量，在这里只需要声明即可。
std::string fbResnet_model = dsm_fb::ini::Inputpth::getSharedValue1(); //模仿这里写安全带标定数据传输
std::string fblabels_txt_file = dsm_fb::ini::Inputpth::getSharedValue2();
extern dsm_fb::globals globalfb;

#ifdef DSM_USING_RKNN

#include "rknn_api.h"

/*-------------------------------------------
                  Functions
-------------------------------------------*/

namespace dsm_fb {
    static void printRKNNTensor(rknn_tensor_attr *attr)
    {
        printf("index=%d name=%s n_dims=%d dims=[%d %d %d %d] n_elems=%d size=%d fmt=%d type=%d qnt_type=%d fl=%d zp=%d scale=%f\n",
            attr->index, attr->name, attr->n_dims, attr->dims[3], attr->dims[2], attr->dims[1], attr->dims[0],
            attr->n_elems, attr->size, 0, attr->type, attr->qnt_type, attr->fl, attr->zp, attr->scale);
    }

    static unsigned char *load_model(const char *filename, int *model_size)
    {
        FILE *fp = fopen(filename, "rb");
        if (fp == nullptr)
        {
            printf("fopen %s fail!\n", filename);
            return NULL;
        }
        fseek(fp, 0, SEEK_END);
        int model_len = ftell(fp);
        unsigned char *model = (unsigned char *)malloc(model_len);
        fseek(fp, 0, SEEK_SET);
        if (model_len != fread(model, 1, model_len, fp))
        {
            printf("fread %s fail!\n", filename);
            free(model);
            return NULL;
        }
        *model_size = model_len;
        if (fp)
        {
            fclose(fp);
        }
        return model;
    }

    static int rknn_GetTopfb(
        float *pfProb,
        float *pfMaxProb,
        uint32_t *pMaxClass,
        uint32_t outputCount,
        uint32_t topNum)
    {
        uint32_t i, j;

    #define MAX_TOP_NUM 20
        if (topNum > MAX_TOP_NUM)
            return 0;

        memset(pfMaxProb, 0, sizeof(float) * topNum);
        memset(pMaxClass, 0xff, sizeof(float) * topNum);

        for (i = 0; i < 5; i++)
            printf("$$$$$$$$$$$$$$$$$$$ prob = %f\n", pfProb[i]);

        for (j = 0; j < topNum; j++)
        {
            for (i = 0; i < outputCount; i++)
            {
                if ((i == *(pMaxClass + 0)) || (i == *(pMaxClass + 1)) || (i == *(pMaxClass + 2)) ||
                    (i == *(pMaxClass + 3)) || (i == *(pMaxClass + 4)))
                {
                    continue;
                }

                if (pfProb[i] > *(pfMaxProb + j))
                {
                    *(pfMaxProb + j) = pfProb[i];
                    *(pMaxClass + j) = i;
                }
            }
        }

        return 1;
    }

    void softmax(float *buf, int num)
    {
        float total = 0.0;
        for (int i = 0; i < num; i++)
            total += exp(buf[i]);
        for (int i = 0; i < num; i++)
            buf[i] = exp(buf[i]) / total;
        return;
    }

    /*-------------------------------------------
                    Main Function
    -------------------------------------------*/
    static bool isInit = false;
    static rknn_context ctx;

    cv::Mat rknn_infer(cv::Mat img)
    {
        int ret;

        // imwrite("./img.jpg", img);

        if (isInit == false)
        {
            int model_len = 0;
            unsigned char *model;

            // const char *model_path = "./model/resnet18.rknn";
            //  Load RKNN Model
            model = load_model(fbResnet_model.c_str(), &model_len);

            ret = rknn_init(&ctx, model, model_len, 0);
            if (ret < 0)
                printf("rknn_init fail! ret=%d\n", ret);

            isInit = true;
        }

        rknn_input_output_num io_num;

        // Get Model Input Output Info
        ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
        if (ret != RKNN_SUCC)
            printf("rknn_query fail! ret=%d\n", ret);

        // printf("model input num: %d, output num: %d\n", io_num.n_input, io_num.n_output);

        // printf("input tensors:\n");
        rknn_tensor_attr input_attrs[io_num.n_input];
        memset(input_attrs, 0, sizeof(input_attrs));
        for (int i = 0; i < io_num.n_input; i++)
        {
            input_attrs[i].index = i;
            ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
            if (ret != RKNN_SUCC)
                printf("rknn_query fail! ret=%d\n", ret);
            // printRKNNTensor(&(input_attrs[i]));
        }

        // printf("output tensors:\n");
        rknn_tensor_attr output_attrs[io_num.n_output];
        memset(output_attrs, 0, sizeof(output_attrs));
        for (int i = 0; i < io_num.n_output; i++)
        {
            output_attrs[i].index = i;
            ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
            if (ret != RKNN_SUCC)
                printf("rknn_query fail! ret=%d\n", ret);
            // printRKNNTensor(&(output_attrs[i]));
        }

        // Set Input Data
        rknn_input inputs[1];
        memset(inputs, 0, sizeof(inputs));
        inputs[0].index = 0;
        inputs[0].type = RKNN_TENSOR_UINT8;
        inputs[0].size = input_attrs[0].size;
        inputs[0].fmt = RKNN_TENSOR_NHWC;
        inputs[0].buf = img.data;

        ret = rknn_inputs_set(ctx, io_num.n_input, inputs);
        if (ret < 0)
            printf("rknn_input_set fail! ret=%d\n", ret);

        // Run
        printf("Resnet18 rknn_run\n");
        ret = rknn_run(ctx, nullptr);
        if (ret < 0)
            printf("rknn_run fail! ret=%d\n", ret);

        // Get Output
        rknn_output outputs[1];
        memset(outputs, 0, sizeof(outputs));
        outputs[0].want_float = 1;
        ret = rknn_outputs_get(ctx, 1, outputs, NULL);
        if (ret < 0)
            printf("rknn_outputs_get fail! ret=%d\n", ret);

        int class_num = outputs[0].size / 4;

        float *buffer = (float *)outputs[0].buf;
        softmax(buffer, class_num);

        /*
        for (int i = 0; i < 5; i++)
            printf("score = %f\n", buffer[i]);
        */

        cv::Mat output_mat = cv::Mat(cv::Size(class_num, 1), CV_32FC1, buffer);

        float *ptr = output_mat.ptr<float>(0);

        /*
    printf("***********************************************\n");
            for (int n = 0; n < output_mat.cols; n++)
    {
    printf("[%d] = %f\n", n, *ptr);
                ptr++;
    }
    printf("***********************************************\n");
    */

        return output_mat;

        /*
        // Post Process
        int max_class_index = -1;
        for (int i = 0; i < io_num.n_output; i++)
        {
            uint32_t MaxClass[5];
            float fMaxProb[5];
            float *buffer = (float *)outputs[i].buf;
            uint32_t sz = outputs[i].size / 4;
            //printf("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ sz = %d\n", sz);

            rknn_GetTopfb(buffer, fMaxProb, MaxClass, sz, 5);

            printf(" --- Top5 ---\n");
            for (int i = 0; i < 5; i++)
            {
                printf("%3d: %8.6f\n", MaxClass[i], fMaxProb[i]);
            }
            max_class_index = MaxClass[0];
        }
        */
    }
}

#endif

std::vector<std::string> dsm_fb::Resnet::readClassNames()
{
    std::vector<std::string> classNames;

    std::ifstream fp(fblabels_txt_file);
    if (!fp.is_open())
    {
        printf("could not open file...\n");
        exit(-1);
    }
    std::string name;
    while (!fp.eof())
    {
        std::getline(fp, name);
        if (name.length())
            classNames.push_back(name);
    }
    fp.close();
    return classNames;
}

int dsm_fb::Resnet::glasses_detect(cv::Mat eye, std::vector<float> angle_vec)
{
    cv::resize(eye, eye, cv::Size(56, 56));
    cv::Mat hls;
    cv::cvtColor(eye, hls, cv::COLOR_BGR2HLS);
    std::vector<cv::Mat> hls_channels;
    cv::split(hls, hls_channels);
    cv::Mat hls_s;
    hls_s = hls_channels[2];
    cv::Mat res;
    cv::threshold(hls_s, res, 80, 255, cv::THRESH_BINARY);
    int count = cv::sum(res)[0]; // 计算 result 中元素的和，即为 dst 中值为 255 的像素数量
    count /= 255;
    // std::cout << std::endl << std::endl << count << "  Number of white pixels:angle_vec[3]  " << angle_vec[3]  << std::endl << std::endl << std::endl;
    if (count > 10 && 90 - int(angle_vec[3] * 100) > 30.0)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

#ifdef DSM_USING_RKNN

int dsm_fb::Resnet::resnet_recog(face_message face_info, std::vector<int> &result_task, int Muv_speed, ExternalParam exterIOparam)
#else
int dsm_fb::Resnet::resnet_recog(dnn::Net net, face_message face_info, std::vector<int> &result_task, int Muv_speed)
#endif
{
    int watch_dog = 0;
    std::vector<int> result_map = {0, 0, 0, 0, 0};
    cv::Mat img_eyer, img_eyel;
    std::vector<cv::Mat> img_in;
    cv::Mat eye_l = face_info.face_roi[0].clone();
    cv::Mat eye_r = face_info.face_roi[1].clone();
    //增加反光眼镜的识别，需要注释以下功能
    int left_glasses = 0; //= Resnet::glasses_detect(eye_l, face_info.face_angle);
    int right_glasses = 0 ; //= Resnet::glasses_detect(eye_r, face_info.face_angle);
    img_in.swap(face_info.face_roi);

    // std::string namepr = ".jpg";
    // uint64_t start_for = dsm_fb::getTimesfb();
    for (int i = 0; i < img_in.size(); i++)
    {
        // std::string tdddpr = std::to_string(i) + namepr;
        std::cout << "muv:-----save------------img_in.size(). " << img_in[i].size << std::endl;
        // cv::imwrite(tdddpr, img_in[i]);
        if (img_in[i].channels() == 1)
        {
            img_in[i] = convertTo3Channels(img_in[i]);
        }
        cv::resize(img_in[i], img_in[i], cv::Size(56, 56), cv::INTER_AREA);
        // img_in[i].convertTo(img_in[i], CV_32FC3);
        // Resnet::PreProcess(img_in[i], img_in[i]);
        cv::cvtColor(img_in[i], img_in[i], cv::COLOR_BGR2RGB);
    }
    // cv::imwrite("belt.jpg", img_in[3]);
    // uint64_t end_for = dsm_fb::getTimesfb();
    // std::cout << "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++Time FOR:getTimesfb   " << int(end_for-start_for) << std::endl;

    std::vector<std::string> labels = Resnet::readClassNames();

#ifdef DSM_USING_RKNN
    // cv::Mat prob = cv::Mat(Size(FBRESNET_CLASS_NUM, 4), CV_32FC1);  // resnet的分类数为6
    // float *prob_ptr = prob.ptr<float>(0);

    // 读入一张测试图片
    cv::Mat prob = cv::Mat(cv::Size(FBRESNET_CLASS_NUM, 4), CV_32FC1); // resnet的分类数为6
    float *prob_ptr = prob.ptr<float>(0);
    cv::Mat prob_r = rknn_infer(img_in[0]); // 读入一张图片    改为读入4张图片
    float *prob_r_ptr = prob_r.ptr<float>(0);
    for (int n = 0; n < prob_r.cols; n++)
        *prob_ptr++ = *prob_r_ptr++;

    // uint64_t start_eye = dsm_fb::getTimesfb();
    cv::Mat prob_l = rknn_infer(img_in[1]);
    float *prob_l_ptr = prob_l.ptr<float>(0);
    for (int n = 0; n < prob_l.cols; n++)
        *prob_ptr++ = *prob_l_ptr++;
    // uint64_t end_eye = dsm_fb::getTimesfb();
    // std::cout << "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++Time EYE:getTimesfb   " << int(end_eye-start_eye) << std::endl;

    uint64_t start_mat = dsm_fb::getTimesfb();
    cv::Mat test_img;
    uint64_t end_mat = dsm_fb::getTimesfb();
    std::cout << "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 创建一次 Mat 耗时:getTimesfb   " << int(end_mat-start_mat) << std::endl;

    // uint64_t start_smoke = dsm_fb::getTimesfb();    
    cv::Mat prob_smoke = rknn_infer(img_in[2]);
    float *prob_smoke_ptr = prob_smoke.ptr<float>(0);
    for (int n = 0; n < prob_smoke.cols; n++)
        *prob_ptr++ = *prob_smoke_ptr++;
    // uint64_t end_smoke = dsm_fb::getTimesfb();
    // std::cout << "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++Time SMOKE:getTimesfb   " << int(end_smoke-start_smoke) << std::endl;


          
    // cv::Mat test_img = cv::imread("./model/DSM.jpg");
    // cv::resize(test_img, test_img, cv::Size(56, 56), cv::INTER_AREA);
    // cv::cvtColor(test_img, test_img, cv::COLOR_BGR2RGB);
    // uint64_t start_belt = dsm_fb::getTimesfb();   
    cv::Mat prob_belt = rknn_infer(img_in[3]);
    float *prob_belt_ptr = prob_belt.ptr<float>(0);
    for (int n = 0; n < prob_belt.cols; n++)
        *prob_ptr++ = *prob_belt_ptr++;
    // uint64_t end_belt = dsm_fb::getTimesfb();
    // std::cout << "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++Time BELT:getTimesfb   " << int(end_belt-start_belt) << std::endl;
#else
    // 加载网络
    // cv::dnn::Net net = cv::dnn::readNetFromONNX(fbResnet_model);  // 加载训练好的识别模型

    if (net.empty())
    {
        printf("read onnx model data failure...\n");
        system("pause");
        exit(0);
    }
    Mat inputBlob = blobFromImages(img_in, 1.0, Size(res_weight, res_height), Scalar(0, 0, 0), false, true);
    // 执行图像分类
    net.setInput(inputBlob);
    cv::Mat prob = net.forward(); // 推理出结果
                                  // print(prob);
    vector<double> times;
    double time = net.getPerfProfile(times);
    float ms = (time * 1000) / getTickFrequency();
    printf("current inference time : %.2f ms \n", ms);
#endif
    // std::cout << "muv:------------Class:  " << prob.rows << std::endl;
    // std::cout << "muv:------------测试读取图片的固定结果（眼睛） prob:  " << std::endl  << prob << std::endl;
    // 同时闭眼才算闭眼，不存在一只眼闭眼睡觉
    int sleep_num = 0;
    cv::Scalar mean_l, mean_r;
    mean_l = cv::mean(eye_l);
    mean_r = cv::mean(eye_r);
    // 眼睛问题特殊处理操作
    int number = 0;
    bool eye_specil = false;

    // if ((mean_r[0] > 160) || (mean_r[0] > 160))  // || left_glasses == 1 || right_glasses == 1) //两种功能重复，待测试后调整
    if (left_glasses == 1 || right_glasses == 1)
    {
        watch_dog += 1;
        number = 2;
        result_map[0] = 0;
    }
    // Point3i rec;
    // rec_idx.clear();
    // 增加输出类别限定  n = 0 左眼   n = 1 右眼
    int eyel_idx = 0; // the eye is open 眼部状态
    int eyer_idx = 0; // the eye is open 眼部状态
    std::string top1, top2, top3, top4, top5;
    std::string topeye_L;
    std::string topeye_R;
    int state_idx = 0;
    std::vector<cv::Point> face_mask;
    std::cout << "muv:---------------------------------------------------------------------------------戴眼镜识别 number:  " << number << std::endl;
    // for (int n = 0; n < prob.rows; n++) {
    for (int n = number; n < prob.rows; n++)
    {
        double classProb;
        cv::Mat probMat = prob(cv::Rect(0, n, FBRESNET_CLASS_NUM, 1)).clone();
        cv::Mat result = probMat.reshape(1, 1);

        std::vector<float> res_vec;
        top1 = std::to_string(result.at<float>(0, 0));
        top2 = std::to_string(result.at<float>(0, 1));
        top3 = std::to_string(result.at<float>(0, 2));
        top4 = std::to_string(result.at<float>(0, 3));
        top5 = std::to_string(result.at<float>(0, 4));
        switch (n)
        {
        case 0:
        case 1:
        { // 处理1和2时，执行相同的代码块
            if (n == 1)
            {
                watch_dog += 1;
            }
            std::cout << n << ":~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~眼睛识别" << std::endl;
            std::cout << n << "muv::~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~归一化后的眼睛识别res_vec     " << result.at<float>(0, 1) << " <-正常 报警-> " << result.at<float>(0, 0) << std::endl;
            if ((result.at<float>(0, 0) > result.at<float>(0, 1)) && (result.at<float>(0, 0) >= 0.991) &&
                (globalfb.eye_button == true) && (Muv_speed >= globalfb.eye_speed))
            {
                classProb = result.at<float>(0, 0);
                state_idx = 1; // 闭眼结果
                //                    putText(face_info.frame, labels.at(n), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 0, 255), 1, 1);
            }
            else
            {
                classProb = result.at<float>(0, 1);
                state_idx = 0; // 睁眼
                               //                    putText(face_info.frame, labels.at(n), face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 255, 0),1, 1);
            }
            if ((state_idx == 1) && (eye_specil == false)) // 特殊处理
            {
                sleep_num += 1;
            }

            if (sleep_num == 2)
            {
                result_map[0] = 1;
            }
            else
            {
                result_map[0] = 0;
            }
            break; // 防止执行下一个case语句块
        }

        case 2:
        {
            watch_dog += 1;
            std::cout << "muv:2~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~抽烟状态识别" << std::endl;
            std::cout << "muv::~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~归一化后的抽烟状态识别res_vec     " << result.at<float>(0, 3) << " <-正常 报警-> " << result.at<float>(0, 2) << std::endl;
            if ((result.at<float>(0, 2) > result.at<float>(0, 3)) && (result.at<float>(0, 2) > 0.92) && (globalfb.smoking_button == true) &&
                (Muv_speed >= globalfb.smoking_speed))
            {
                result_map[1] = 1; // 抽烟，存在报警
                classProb = result.at<float>(0, 2);
                if (false)
                {
                    printf("\n current image classification : %s, possible : %.2f\n", labels.at(2).c_str(),
                           classProb);
                }
                //                    putText(face_info.frame, "smoke", face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 0, 255), 1, 1);
            }
            else
            {
                result_map[1] = 0; // 没抽烟，不存在报警
                classProb = result.at<float>(0, 3);
                if (false)
                {
                    printf("\n current image classification : %s, possible : %.2f\n", labels.at(3).c_str(),
                           classProb);
                }
                //                    putText(face_info.frame, "NO_smoke", face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 255, 0), 1, 1);
            }
            break;
        }
        case 3:
        {
            watch_dog += 1;
            std::cout << "muv:3~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~安全带识别" << std::endl;
            std::cout << "muv::~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~归一化后的安全带识别  res_vec     " << result.at<float>(0, 4) << " <-正常 报警-> " << result.at<float>(0, 5) << std::endl;
            // 增加CAN输入的安全带IO口消息
            if ((exterIOparam.safe_belt == dsm_fb::SafeBeltStatus::SAFE_BELT_OFF)&& (globalfb.seatbelt_button == true) &&
                    (Muv_speed >= globalfb.seatbelt_speed))
            {
                result_map[2] = 1; // 不戴安全带，存在报警
            }
            else
            {
                result_map[2] = 0; // 不戴安全带，存在报警 0829
            }
            // else
            // {
            //     if ((result.at<float>(0, 4) < result.at<float>(0, 5)) && (result.at<float>(0, 5) > 0.62) && (globalfb.seatbelt_button == true) &&
            //         (Muv_speed > globalfb.seatbelt_speed))
            //     {
            //         result_map[2] = 1; // 不戴安全带，存在报警
            //         classProb = result.at<float>(0, 5);
            //         if (false)
            //         {
            //             printf("\n current image classification : %s, possible : %.2f\n", labels.at(5).c_str(),
            //                    classProb);
            //         }
            //         //                    putText(face_info.frame, "nosafebelt", face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 0, 255),1, 1);
            //     }
            //     else
            //     {
            //         result_map[2] = 0; // 戴安全带，不存在报警
            //         classProb = result.at<float>(0, 4);
            //         if (false)
            //         {
            //             printf("\n current image classification : %s, possible : %.2f\n", labels.at(4).c_str(),
            //                    classProb);
            //         }
            //         //                    putText(face_info.frame, "safebelt", face_mask[n], FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 255, 0), 1, 1);
            //     }
            // }
            break;
        }
        default:
        {
            break; // 当输入值不匹配任何case时，执行default语句块
        }
        }
    }

    //[4]盲点检测   角度阈值较小
    // if ((globalfb.Blindspot_button == true) && (abs(face_info.face_angle[0]) > globalfb.Blindspot_mode) &&(globalfb.turn_lightb== 1)&&
    // int turn_light = (exterIOparam.turnL || exterIOparam.turnR) ? 1 : 0 ;
    // if (turn_light == 1)
    // {
    //     if ((globalfb.Blindspot_button == true) && (abs(face_info.face_angle[0]) < globalfb.Blindspot_mode) &&
    //     (Muv_speed > globalfb.Blindspot_speed)) // 18 可以设置为左顾右盼的灵敏度参数  42良好
    //     {
    //     std::cout << "muv:==================================4盲点判断角度：" << face_info.face_angle[0] << std::endl;
    //     result_map[3] = 1; // 转向角度过大 左顾右盼
    //     watch_dog += 1;
    //     //            putText(face_info.frame, "Blindspot", Blindspot, FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 28, 255), 1, 1);
    //     }
    //     else
    //     {
    //         result_map[3] = 0;
    //         watch_dog += 1;
    //     }
    // }
    // else
    // {
    //     result_map[3] = 0;
    //     watch_dog += 1;
    // }

    //11.01
    int turn_light = 0;
    if ((exterIOparam.turnL == true)||(exterIOparam.turnR == true))
    {
         turn_light = 1;
    }
    
    int  face_deg = abs(face_info.face_angle[0]);
    if (turn_light == 1)
    {
        if ((globalfb.Blindspot_button == true) && (face_deg < globalfb.Blindspot_mode) && (Muv_speed >= globalfb.Blindspot_speed))
        {
            
            result_map[3] = 1; // 转向角度过大 左顾右盼
            watch_dog += 1;
        }
        else
        {
            result_map[3] = 0;
            watch_dog += 1;
        }
    }
    else
    {
        result_map[3] = 0;
        watch_dog += 1;
    }
    //[5]分神检测  角度阈值较大 lookaround_mode  代表角度
    if ((globalfb.lookaround_button == true) && (abs(face_info.face_angle[0]) > globalfb.lookaround_mode) && (turn_light == 0) &&
        (Muv_speed >= globalfb.lookaround_speed)) // 18 可以设置为左顾右盼的灵敏度参数  42良好
    {
        std::cout << "muv:==================================5分神判断角度：" << face_info.face_angle[0] << std::endl;
        watch_dog += 1;
        result_map[4] = 1; // 转向角度过大 左顾右盼
        //            putText(face_info.frame, "lookaround ", lookaround, FONT_HERSHEY_SIMPLEX, 0.6, Scalar(255, 0, 20), 1, 1);
    }
    else
    {
        watch_dog += 1;
        result_map[4] = 0;
    }

    // cout << "muv:==================================WWWWWWWWWWWWWWWWWWwatch_dog：" << watch_dog << endl;
    if (watch_dog != 5)
    {
        return -1;
    }
    else
    {
        result_task.swap(result_map);
        return 0;
    }
}


//识别安全带 二分类
#ifdef DSM_USING_RKNN
int dsm_fb::Resnet::resnet_belt(cv::Mat input_img, std::vector<int> &result_task, int Muv_speed)
#else
int Resnet::resnet_mouth(dnn::Net net, Mat input_img, Mat img_face, vector<int> landmarks)
#endif
{
    int watch_dog = 0;
    std::vector<int> result_map = {0, 0, 0, 0, 0};
	cv::Mat img_mouth;
	input_img.copyTo(img_mouth);
    if ((input_img.rows >=720)||(input_img.cols >=1280))
    {
        return -1;
    }
    if (img_mouth.channels()==1)
    {
        img_mouth = convertTo3Channels(img_mouth);
    }
    
	//处理img_eyel
	cv::resize(img_mouth, img_mouth, cv::Size(56, 56), cv::INTER_AREA);
	// img_mouth.convertTo(img_mouth, CV_32FC3);   // 越界报错
	// PreProcess(img_mouth, img_mouth);         //标准化处理
    cv::cvtColor(img_mouth, img_mouth, cv::COLOR_BGR2RGB);

	std::vector<std::string> labels = readClassNames();

#ifdef DSM_USING_RKNN
    // 读入一张测试图片
    cv::Mat prob = cv::Mat(cv::Size(FBRESNET_CLASS_NUM, 1), CV_32FC1); // resnet的分类数为6
    
    prob = dsm_fb::rknn_infer(img_mouth);
#else
	// 加载网络
	//cv::dnn::Net net = cv::dnn::readNetFromONNX(Resnet_model);  // 加载训练好的识别模型

	if (net.empty()) {
		printf("read onnx model data failure...\n");
		system("pause");
		exit(0);
	}
	Mat inputBlob = blobFromImage(img_mouth, 1.0, Size(res_weight, res_height), Scalar(0, 0, 0), false, true);

	// 执行图像分类
	net.setInput(inputBlob);
	cv::Mat prob = net.forward();     // 推理出结果
        print(prob);
	vector<double> times;
	double time = net.getPerfProfile(times);
	float ms = (time * 1000) / getTickFrequency();
	printf("current inference time : %.2f ms \n", ms);
#endif
	// 得到最可能分类输出
	int classids = -1;
	// Point mouth_pos = Point(landmarks[8], landmarks[9]);

	int return_idx = 0;
	//Point3i rec;
	//rec_idx.clear();
	//增加输出类别限定
	for (int n = 0; n < prob.rows; n++) {
		cv::Point classNumber;
		double classProb;
		cv::Mat probMat = prob(cv::Rect(0, n, FBRESNET_CLASS_NUM, 1)).clone();
		cv::Mat result = probMat.reshape(1, 1);
		//minMaxLoc  获取最大最小值的位置 得到理想IDS  弃用
		//minMaxLoc(result, NULL, &classProb, NULL, &classNumber);
		// int classidx = classNumber.x;
		// 0是系了安全带不报警， 1是没有系安全带报警
        float anyles = abs(result.at<float>(0, 2) - result.at<float>(0, 3));
		if ((result.at<float>(0, 0) < result.at<float>(0, 1))&&(result.at<float>(0, 3)>0.66))  //&&( anyles <0.3)   // 0815之前  0.92
		 {
			classProb = result.at<float>(0, 1);
            result_map[0] = 1; // 没有系安全带报警
		}
		else 
		{
            classProb = result.at<float>(0, 0);
            result_map[0] = 0; // 系了安全带，不存在报警
			//printf("\n current image classification : %s, possible : %.2f\n", labels.at(mclassidx).c_str(), classProb);
			//putText(img_face, labels.at(mclassidx), mouth_pos, FONT_HERSHEY_SIMPLEX, 0.6, Scalar(0, 255, 255), 1, 1);
		}
		std::cout << "result1安全带： " << format(result, cv::Formatter::FMT_C) << std::endl;
        std::string top1 = std::to_string(result.at<float>(0, 0)) ;
        std::string top2 = std::to_string(result.at<float>(0, 1)) ;
        // std::string top3 = std::to_string(result.at<float>(0, 2)) ;
        // std::string top4 = std::to_string(result.at<float>(0, 3)) ;
        // std::string top5 = std::to_string(result.at<float>(0, 4)) ;
        std::string topcon = top1 +"_"+ top2; // +"_"+ top3 +"_"+ top4+"_"+ top5;
        double min = 0,max = 0;
        minMaxLoc(result, &min, &max);
	}
	//返回值  0.1闭眼  2.3哈欠  4口罩
//	cout << "return_idx： " << return_idx << endl;
 if (watch_dog != 1)
    {
        return -1;
    }
    else
    {
        result_task.swap(result_map);
        return 0;
    }
}


// 图像处理  标准化处理
void dsm_fb::Resnet::PreProcess(const cv::Mat &image, cv::Mat &image_blob)
{
    cv::Mat input;
    image.copyTo(input);

    // 数据处理 标准化
    std::vector<cv::Mat> channels, channel_p;
    cv::split(input, channels);
    cv::Mat R, G, B;
    B = channels.at(0);
    G = channels.at(1);
    R = channels.at(2);

    B = (B / 255. - 0.406) / 0.225;
    G = (G / 255. - 0.456) / 0.224;
    R = (R / 255. - 0.485) / 0.229;

    channel_p.push_back(R);
    channel_p.push_back(G);
    channel_p.push_back(B);

    cv::Mat outt;
    cv::merge(channel_p, outt);
    image_blob = outt;
}

int dsm_fb::Recong::check_cream(cv::Mat input_img, bool &state_camover)
{
    cv::Mat thr, image;
    cv::cvtColor(input_img, thr, cv::COLOR_BGR2GRAY); // Convert to gray
    cv::blur(thr, thr, cv::Size(3, 3));
    cv::Canny(thr, image, 18, 54, 3, true);
    // bitwise_not(thr, thr); //这里先变反转颜色

    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    cv::findContours(image, contours, hierarchy, cv::RETR_TREE, cv::CHAIN_APPROX_SIMPLE, cv::Point()); // 寻找最外层轮廓

    // 绘制轮廓
    // drawContours(input_img, contours, -1, Scalar(0, 255, 0), 1, 8, hierarchy);
    bool ftstate = isImageBlurry(image);

    if ((contours.size() < 140) && (ftstate == false))
    {
        state_camover = true;
        return 1;
    }
    else
    {
        return 0;
    }
}

bool dsm_fb::Recong::isImageBlurry(cv::Mat &img, double threshold)
{
    cv::Mat matImageGray;
    cv::Mat dst, abs_dst;
    cv::Laplacian(img, dst, CV_16S, 3);
    cv::convertScaleAbs(dst, abs_dst);
    cv::Mat tmp_m, tmp_sd;
    double sd = 0;
    cv::meanStdDev(dst, tmp_m, tmp_sd);
    sd = tmp_sd.at<double>(0, 0); // 方差
    int value = sd * sd;
    if (value > threshold)
    {
        return true;
    }
    else
    {
        return false;
    }
}

namespace dsm_fb
{
    // 实现 softmaxmuv 函数
    std::vector<float> Resnet::softmaxmuv(const cv::Mat &output)
    {
        // 首先检查输入矩阵是否适合做softmax运算（通常是1维或最后一维代表类别数量）
        if (output.channels() != 1 || output.dims > 2)
        {
            throw std::invalid_argument("Input matrix should be 1D or have one channel in the last dimension.");
        }

        std::vector<float> result(output.total());
        const float *data_ptr = output.ptr<float>(0); // 假设输入数据是浮点型且为一维

        // 计算最大值以防止数值过大导致下溢
        float max_val = *std::max_element(data_ptr, data_ptr + output.total());

        // 执行softmax计算
        float sum_exp = 0.0f;
        for (size_t i = 0; i < output.total(); ++i)
        {
            float exponent = std::exp(data_ptr[i] - max_val);
            result[i] = exponent;
            sum_exp += exponent;
        }

        // 归一化得到概率分布
        for (float &val : result)
        {
            val /= sum_exp;
        }

        return result;
    }
}
