/*
 *parameter: file_path ??��?����������: /user/home/<USER>
 *           key         ??��??����?   �� num
 *           def       	��?������?��?
 *	     readConfig ����string����   readConfigFloat ����float����  readConfigInt ����Int����
 *
 */
#include <string>
#include <fstream>
#include "fb_run.h"
#include "fb_ini.h"
#include "fb_resnet18.h"
#include "G3_Configuration.h"

namespace dsm_fb
{

    // ��?������
    std::string ini::Inputpth::Resnet_model_pth = "./model/fb_resnet18.rknn";
    std::string ini::Inputpth::Resmod_lable_pth = "./model/fb_resnet18c6.txt";
    // ??????��??��
    void ini::Inputpth::setSharedValue1(std::string newValue)
    {
        ini::Inputpth::Resnet_model_pth = newValue;
    }

    std::string ini::Inputpth::getSharedValue1()
    {
        return Resnet_model_pth;
    }
    void ini::Inputpth::setSharedValue2(std::string newValue)
    {
        ini::Inputpth::Resmod_lable_pth = newValue;
    }

    std::string ini::Inputpth::getSharedValue2()
    {
        return Resmod_lable_pth;
    }

    // ��??��????����??��?��?  ����?��??��
    void get_global_params(std::string yaml_path, globals globalfb)
    {
        // ��??��?
        //        globalfb.respirator_speed = readConfigInt(yaml_path, "respirator_speed", 10);
        //        globalfb.respirator_button = readConfigBool(yaml_path, "respirator_button", true);
        //        globalfb.event_respirator_time = readConfigInt(yaml_path, "event_respirator_time", 4000);
        //        globalfb.freeze_respirator_time = readConfigInt(yaml_path, "freeze_respirator_time", 2000);

        // ��??��?
        globalfb.eye_speed = readConfigInt(yaml_path, "eye_speed", 10);
        globalfb.eye_button = readConfigBool(yaml_path, "eye_button", true);
        globalfb.event_eye_time = readConfigInt(yaml_path, "event_eye_time", 4000);
        globalfb.freeze_eye_time = readConfigInt(yaml_path, "freeze_eye_time", 2000);

        // ��?����
        //        globalfb.mouth_speed = readConfigInt(yaml_path, "mouth_speed", 10);
        //        globalfb.mouth_button = readConfigBool(yaml_path, "mouth_button", true);
        //        globalfb.event_mouth_time = readConfigInt(yaml_path, "event_mouth_time", 4000);
        //        globalfb.freeze_mouth_time = readConfigInt(yaml_path, "freeze_mouth_time", 2000);

        // ������?��???
        globalfb.lookaround_speed = readConfigInt(yaml_path, "lookaround_speed", 10);
        globalfb.lookaround_button = readConfigBool(yaml_path, "lookaround_button", true);
        globalfb.event_lookaround_time = readConfigInt(yaml_path, "event_lookaround_time", 4000);
        globalfb.freeze_lookaround_time = readConfigInt(yaml_path, "freeze_lookaround_time", 2000);

        // ��?��???
        //        globalfb.facemissing_speed = readConfigInt(yaml_path, "facemissing_speed", 10);
        //        globalfb.facemissing_button = readConfigBool(yaml_path, "facemissing_button", true);
        //        globalfb.event_facemissing_time = readConfigInt(yaml_path, "event_facemissing_time", 4000);
        //        globalfb.freeze_facemissing_time = readConfigInt(yaml_path, "freeze_facemissing_time", 2000);

        //??����?
        globalfb.camcover_speed = readConfigInt(yaml_path, "camcover_speed", 10);
        globalfb.camcover_button = readConfigBool(yaml_path, "camcover_button", true);
        globalfb.event_camcover_time = readConfigInt(yaml_path, "event_camcover_time", 4000);
        globalfb.freeze_camcover_time = readConfigInt(yaml_path, "freeze_camcover_time", 2000);

        // ��??��?
        globalfb.smoking_speed = readConfigInt(yaml_path, "smoking_speed", 10);
        globalfb.smoking_button = readConfigBool(yaml_path, "smoking_button", true);
        globalfb.event_smoking_time = readConfigInt(yaml_path, "event_smoking_time", 4000);
        globalfb.freeze_smoking_time = readConfigInt(yaml_path, "freeze_smoking_time", 2000);

        // ��?����??
        globalfb.phone_speed = readConfigInt(yaml_path, "phone_speed", 10);
        globalfb.phone_button = readConfigBool(yaml_path, "phone_button", true);
        globalfb.event_phone_time = readConfigInt(yaml_path, "event_phone_time", 4000);
        globalfb.freeze_phone_time = readConfigInt(yaml_path, "freeze_phone_time", 2000);

        // ��??��?  ��?��?????
        //        globalfb.gesture_button = readConfigBool(yaml_path, "gesture_button", true);
        //        globalfb.gesture_sign = readConfigInt(yaml_path, "event_phone_time", 0);
        //
        //        cout << "respirator_button is " << (bool)globalfb.respirator_button << endl;
    }

    // ��?����?
    //  ����??�����yaml??������
    bool LoadConfigFile(const std::string &file_name, globals &globalfb)
    {

        //[1] sleepʶ��
        regions eye;
        eye.speed = G3_Configuration::getInstance().getEyeSpeed();          // 旭哥赋值
        eye.button =  G3_Configuration::getInstance().getEyeButton();         //旭哥赋值
        eye.mode =  G3_Configuration::getInstance().getEyeMode();           // 旭哥赋值
        eye.sensitivity =  G3_Configuration::getInstance().getEyeSensitivity();   // 旭哥赋值
        eye.event_time =  G3_Configuration::getInstance().getEventEyeTime();  // 旭哥赋值
        eye.freeze_time =  G3_Configuration::getInstance().getFreezeEyeTime(); // 旭哥赋值
        bool stat_eye = check_params(eye);
        globalfb.eye_speed = eye.speed;
        globalfb.eye_button = eye.button;
        globalfb.eye_mode = eye.mode;
        globalfb.eye_sensitivity = eye.sensitivity;
        globalfb.event_eye_time = eye.event_time;
        globalfb.freeze_eye_time = eye.freeze_time;
        //[2] smokingʶ��
        regions smoking;
        smoking.speed = G3_Configuration::getInstance().getSmokingSpeed(); // 旭哥赋值
        smoking.button = G3_Configuration::getInstance().getSmokingButton();// 旭哥赋值
        smoking.mode = G3_Configuration::getInstance().getSmokingMode();// 旭哥赋值
        smoking.sensitivity = G3_Configuration::getInstance().getSmokingSensitivity();// 旭哥赋值
        smoking.event_time = G3_Configuration::getInstance().getEventSmokingTime();// 旭哥赋值
        smoking.freeze_time = G3_Configuration::getInstance().getFreezeSmokingTime();// 旭哥赋值
        bool stat_smoking = check_params(smoking);
        globalfb.smoking_speed = smoking.speed;
        globalfb.smoking_button = smoking.button;
        globalfb.smoking_mode = smoking.mode;
        globalfb.smoking_sensitivity = smoking.sensitivity;
        globalfb.event_smoking_time = smoking.event_time;
        globalfb.freeze_smoking_time = smoking.freeze_time;
        //[3]beltʶ��
        regions seat_belt;
        seat_belt.speed = G3_Configuration::getInstance().getSeatbeltSpeed();          // 旭哥赋值
        seat_belt.button = G3_Configuration::getInstance().getSeatbeltButton();         // 旭哥赋值
        seat_belt.mode = G3_Configuration::getInstance().getSeatbeltMode();           // 旭哥赋值
        seat_belt.sensitivity = G3_Configuration::getInstance().getSeatbeltSensitivity();  // 旭哥赋值
        seat_belt.event_time = G3_Configuration::getInstance().getEventSeatbeltTime();  // 旭哥赋值
        seat_belt.freeze_time = G3_Configuration::getInstance().getFreezeSeatbeltTime(); // 旭哥赋值
        bool stat_seatbelt = check_params(seat_belt);
        globalfb.seatbelt_speed = seat_belt.speed;
        globalfb.seatbelt_button = seat_belt.button;
        globalfb.seatbelt_mode = seat_belt.mode;
        globalfb.seatbelt_sensitivity = seat_belt.sensitivity;
        globalfb.event_seatbelt_time = seat_belt.event_time;
        globalfb.freeze_seatbelt_time = seat_belt.freeze_time;
      
        //[4]blind慢点检测
        regions Blind_spot;
        Blind_spot.speed = G3_Configuration::getInstance().getBlindspotSpeed();  // 旭哥赋值
        Blind_spot.button = G3_Configuration::getInstance().getBlindspotButton(); // 旭哥赋值
        Blind_spot.mode = G3_Configuration::getInstance().getBlindspotMode(); // 旭哥赋值
        Blind_spot.sensitivity = G3_Configuration::getInstance().getBlindspotSensitivity();  // 旭哥赋值
        Blind_spot.event_time = G3_Configuration::getInstance().getEventBlindspotTime();  // 旭哥赋值
        Blind_spot.freeze_time = G3_Configuration::getInstance().getFreezeBlindspotTime(); // 旭哥赋值
        bool stat_Blindspot = check_params(Blind_spot);
        globalfb.Blindspot_speed = Blind_spot.speed;
        globalfb.Blindspot_button = Blind_spot.button;
        globalfb.Blindspot_mode = Blind_spot.mode;
        globalfb.Blindspot_sensitivity = Blind_spot.sensitivity;
        globalfb.event_Blindspot_time = Blind_spot.event_time;
        globalfb.freeze_Blindspot_time = Blind_spot.freeze_time;
        //[5]����ʶ��
        regions lookaround;
        lookaround.speed = G3_Configuration::getInstance().getLookaroundSpeed();
        lookaround.button = G3_Configuration::getInstance().getLookaroundButton();
        lookaround.mode = G3_Configuration::getInstance().getLookaroundMode();
        lookaround.sensitivity = G3_Configuration::getInstance().getLookaroundSensitivity();
        lookaround.event_time = G3_Configuration::getInstance().getEventLookaroundTime();
        lookaround.freeze_time = G3_Configuration::getInstance().getFreezeLookaroundTime();
        bool stat_lookaround = check_params(lookaround);
        globalfb.lookaround_speed = lookaround.speed;
        globalfb.lookaround_button = lookaround.button;
        globalfb.lookaround_mode = lookaround.mode;
        globalfb.lookaround_sensitivity = lookaround.sensitivity;
        globalfb.event_lookaround_time = lookaround.event_time;
        globalfb.freeze_lookaround_time = lookaround.freeze_time;
        // ����������??��
        globalfb.detect_move_button = G3_Configuration::getInstance().getDetectMoveButton();
        globalfb.move_pixel = G3_Configuration::getInstance().getMovePixel();
        globalfb.frame_early = G3_Configuration::getInstance().getFrameEarly();

        return true;
    }

    std::string readConfig(std::string file_path, const std::string &key, std::string def)
    {
        const char *cfgfilepath = file_path.c_str();
        std::fstream cfgFile;
        std::string value;
        std::string no_value = "error";
        cfgFile.open(cfgfilepath); // ��???
        if (!cfgFile.is_open())
        {
            std::cout << "can not open cfg file!" << std::endl;
            return no_value;
        }
        char tmp[100];
        while (!cfgFile.eof()) //?����???��
        {
            cfgFile.getline(tmp, 100); //??????1000��??��?1000��?��??��
            std::string line(tmp);
            size_t pos = line.find('='); //????????=����?��???��key?����value
            if (pos == std::string::npos)
                return def;
            std::string tmpKey = line.substr(0, pos); //?=��??
            if (key == tmpKey)
            {
                value = line.substr(pos + 1); //?=��?��
                return value;
            }
        }
        return def;
    }

    float readConfigFloat(std::string file_path, const std::string &key, float def)
    {
        const char *cfgfilepath = file_path.c_str();
        std::fstream cfgFile;
        std::string value;
        float no_value = -1;
        cfgFile.open(cfgfilepath); // ��???
        if (!cfgFile.is_open())
        {
            std::cout << "can not open cfg file!" << std::endl;
            return no_value;
        }
        char tmp[100];
        while (!cfgFile.eof()) //?����???��
        {
            cfgFile.getline(tmp, 100); //??????1000��??��?1000��?��??��
            std::string line(tmp);
            size_t pos = line.find('='); //????????=����?��???��key?����value
            if (pos == std::string::npos)
                return def;
            std::string tmpKey = line.substr(0, pos); //?=��??
            if (key == tmpKey)
            {
                value = line.substr(pos + 1); //?=��?��
                return atof(value.c_str());
            }
        }
        return def;
    }

    int readConfigInt(std::string file_path, const std::string &key, int def)
    {
        const char *cfgfilepath = file_path.c_str();
        std::fstream cfgFile;
        std::string value;
        int no_value = -1;
        cfgFile.open(cfgfilepath); // ��???
        if (!cfgFile.is_open())
        {
            std::cout << "can not open cfg file!" << std::endl;
            return no_value;
        }
        char tmp[100];
        while (!cfgFile.eof()) //?����???��
        {
            cfgFile.getline(tmp, 100); //??????100��??��?100��?��??��
            std::string line(tmp);
            size_t pos = line.find('='); //????????=����?��???��key?����value
            if (pos == std::string::npos)
                return def;
            std::string tmpKey = line.substr(0, pos); //?=��??
            if (key == tmpKey)
            {
                value = line.substr(pos + 1); //?=��?��
                return atoi(value.c_str());
            }
        }
        return def;
    }

    bool readConfigBool(std::string file_path, const std::string &key, bool def)
    {
        const char *cfgfilepath = file_path.c_str();
        std::fstream cfgFile;
        std::string value;
        int no_value = -1;
        cfgFile.open(cfgfilepath); // ��???
        if (!cfgFile.is_open())
        {
            std::cout << "can not open cfg file!" << std::endl;
            return no_value;
        }
        char tmp[100];
        while (!cfgFile.eof()) //?����???��
        {
            cfgFile.getline(tmp, 100); //??????100��??��?100��?��??��
            std::string line(tmp);
            size_t pos = line.find('='); //????????=����?��???��key?����value
            if (pos == std::string::npos)
                return def;
            std::string tmpKey = line.substr(0, pos); //?=��??
            if (key == tmpKey)
            {
                value = line.substr(pos + 1); //?=��?��
                return atoi(value.c_str());
            }
        }
        return def;
    }

    bool check_params(regions &region)
    {
        int stat_false = 0;
        if ((region.speed >= 0) && (region.speed < 180))
        {
            ;
        }
        else
        {
            // ������??��??��?
            region.speed = 5;
            stat_false = 1;
        }

        if ((region.button == 0) || (region.button == 1))
        {
            ;
        }
        else
        {
            // ������??��??��?
            region.button = 0;
            stat_false = 1;
        }

        if ((region.mode >= 0) || (region.mode <= 100))
        {
            ;
        }
        else
        {
            region.mode = 10;
            stat_false = 1;
        }

        if ((region.sensitivity >= 0) && (region.sensitivity <= 100))
        {
            ;
        }
        else
        {
            region.sensitivity = 80;
            stat_false = 1;
        }

        if ((region.event_time >= 0) && (region.event_time <= 3600000)) // 60min
        {
            ;
        }
        else
        {
            // ������??��??��?
            region.event_time = 5000;
            stat_false = 1;
        }

        if ((region.freeze_time >= 0) && (region.freeze_time <= 3600000))
        {
            ;
        }
        else
        {
            // ������??��??��?
            region.freeze_time = 5000;
            stat_false = 1;
        }
        if (stat_false == 1)
        {
            return false;
        }
        else
        {
            return true;
        }
    }

    static inline bool point_x_sort(cv::Point const &a, cv::Point const &b)
    {
        return a.x > b.x;
    }
    static inline bool point_y_sort(cv::Point const &a, cv::Point const &b)
    {
        return a.y < b.y;
    }

    // ��������?��?��??? ��??��?double����
    // int getDistance(cv::Point point1, cv::Point point2)
    // {
    //     int distance = sqrtf(powf((point1.x - point2.x), 2) + powf((point1.y - point2.y), 2));
    //     return distance;
    // }

    void LoadDefaultParam(ParamsToCheck &params)
    {
        params.yaw_angle = 0;
        params.debug_button = 0;
        params.left_right_button = 0;
        // std::vector<cv::Point> sourcePoints = { cv::Point(502,691), cv::Point(277,894), cv::Point(282,1010), cv::Point(465,1026), cv::Point(618,695) };
        // 640x640下初始坐标：
        std::vector<cv::Point> sourcePoints = {cv::Point(391, 345), cv::Point(278, 447), cv::Point(281, 505), cv::Point(372, 513), cv::Point(449, 347)};

        sourcePoints.swap(params.five_points);
    }

    bool check_debug_param(ParamsToCheck &params)
    {
        bool check_state = true;

        if (params.yaw_angle > 50 || params.yaw_angle < -50)
        {
            params.yaw_angle = 0;
            check_state = false;
        }

        if (!(params.debug_button == 0 || params.debug_button == 1))
        {
            params.debug_button = 0;
            check_state = false;
        }

        // 检查安全带坐标是否越界
        bool belt_state = true;
        if (params.five_points.size() != 5)
        {
             
            belt_state = false;
        }
        for (size_t i = 0; i < params.five_points.size(); i++)
        {
            // if (params.five_points[i].x < 0 || params.five_points[i].x > 719 || params.five_points[i].y < 0 || params.five_points[i].y > 1279)
            // 640x640下限界：
            if (params.five_points[i].x <= 140 || params.five_points[i].x > 499 || params.five_points[i].y <= 0 || params.five_points[i].y > 639)
            {
                belt_state = false;
             
            }
                
        }
        if (!(params.left_right_button == 0 || params.left_right_button == 1))
        {
            params.left_right_button = 0;
            belt_state = false;
        }
        // std::vector<cv::Point> sourcePoints = { cv::Point(502,691), cv::Point(277,894), cv::Point(282,1010), cv::Point(465,1026), cv::Point(618,695) };
        // 640x640下初始坐标：
        std::vector<cv::Point> sourcePoints = {cv::Point(391, 345), cv::Point(278, 447), cv::Point(281, 505), cv::Point(372, 513), cv::Point(449, 347)};
        if (belt_state == false) // 替换默认坐标点
        {
            std::cout << "=========================替换默认坐标、并设置左舵========================" << std::endl;
            sourcePoints.swap(params.five_points);
            params.left_right_button = 0;
            check_state = false;
        }

        return check_state;
    }
    void belt_point_deal(std::vector<cv::Point> &points, int left_right_button)
    {
        std::vector<cv::Point> five_point_vector;
        points.swap(five_point_vector);
        // vector->deque
        std::deque<cv::Point> five_point_deque;
        std::move(five_point_vector.begin(), five_point_vector.end(), std::back_inserter(five_point_deque));

        // x坐标从大到小排序
        std::sort(five_point_deque.begin(), five_point_deque.end(), point_x_sort);

        cv::Point p0, p1, p2, p3, p4;

        if (left_right_button == 0)
        {
            // 左舵
            p4 = five_point_deque[0];
            five_point_deque.pop_front();

            std::sort(five_point_deque.begin(), five_point_deque.end(), point_y_sort);
            p0 = five_point_deque[0];
            p1 = five_point_deque[1];
            five_point_deque.pop_front();
            five_point_deque.pop_front();
            std::sort(five_point_deque.begin(), five_point_deque.end(), point_x_sort);
            p2 = five_point_deque[1];
            p3 = five_point_deque[0];
        }
        else
        {
            // 右舵
            p4 = five_point_deque[4];
            five_point_deque.pop_back();
            std::sort(five_point_deque.begin(), five_point_deque.end(), point_y_sort);
            p0 = five_point_deque[0];
            p1 = five_point_deque[1];
            five_point_deque.pop_front();
            five_point_deque.pop_front();
            std::sort(five_point_deque.begin(), five_point_deque.end(), point_x_sort);
            p2 = five_point_deque[0];
            p3 = five_point_deque[1];
        }

        five_point_vector.clear();
        five_point_vector.push_back(p0);
        five_point_vector.push_back(p1);
        five_point_vector.push_back(p2);
        five_point_vector.push_back(p3);
        five_point_vector.push_back(p4);

        five_point_vector.swap(points);
    }
}