// FB项目 20250113融合第一版本

#include "fb_run.h"
#include <iostream>
#include <opencv2/core/types.hpp>
#include <ostream>
#include <thread>
#include <unistd.h>
#include "fb_resnet18.h"
#include "fb_get_alarm.h"
#include "fb_ini.h"

dsm_fb::DSMInfo alarm_fbinfo;
dsm_fb::globals globalfb;

// extern dsm_fb::YOLO yolo_model(yolo_nets);

#include "XuTimeUtil.h"

// 获取时间戳  获取毫秒级
uint64_t dsm_fb::getTimesfb()
{
	uint64_t atime = XuTimeUtil::getInstance().currentTimeMillis();
	return atime;
}

bool dsm_fb::cmpy(cv::Point const &a, cv::Point const &b)
{
	return a.y > b.y;
	// return a.y < b.y;
}

std::string dsm_fb::getStatusName(dsm_fb::Status status)
{
	switch (status)
	{
	case dsm_fb::Status::Begin:
		return "Begin";
	case dsm_fb::Status::Running:
		return "Running";
	case dsm_fb::Status::End:
		return "End";
	case dsm_fb::Status::Freeze:
		return "Freeze";
	case dsm_fb::Status::Worthless:
		return "Worthless";
	default:
		return "Worthless";
	}
}

// 6类  0eyec 1eyeo  2smoke 3nosmoke 4safebelt 5nosafebelt   盲点和分神驾驶检测
int dsm_fb::Kernelfb::run_detector(cv::Mat back_frame, DSMInfo &alarm_fbinfo, AlarmBox &box, int muv_speed, dsm_fb::ExternalParam exterIOparam)
{
	// dsm_fb::YOLO yolo_model(yolo_nets);
	// dsm_fb::YOLO yolo_model(inputcfg);
	//	static const string kWinName = "Vis_DSM in the MUV";
	Kernelfb collmap;
	try
	{
		// 识别人脸特征
		dsm_fb::face_message face_roi_info; // 人脸信息以及ROI截取结果
		// face_roi_info = yolo_model.detect_face(back_frame);
		// face_roi_info =  yolo_model.detect_face(back_frame, alarm_fbinfo);
		// face_roi_info = yolo_model.detect_face(back_frame,inputcfg, alarm_fbinfo);

		

		// int subway_speed = 15; // 车速单位km/h
		int recog_mouthstat = 0; // 有返回值时为异常状态
		//		cv::namedWindow(kWinName, WINDOW_NORMAL);
		//		cv::resizeWindow(kWinName, 720, 1280); // 设置窗口大小
		//		cv::imshow(kWinName, back_frame);bt
		// return -1;
		printf("***********run_detector in line %d  \n", __LINE__);
		// 增加异常打断的清空操作，（此处仅处理全局打断，局部打断更为复杂）。
		if( (face_roi_info.state == 2)||(exterIOparam.back_gear == true))
		{
			std::cout << "muv:-----------------------------------------------------------------------------------------------------------异常清空--------------------   back_frame： " << back_frame.size() << std::endl;
			alarm_fbinfo.face_angle = 0;
			collmap.superposition.clear();
			alarm_fbinfo.face_point.clear();
            alarm_fbinfo.five_point.clear();
			std::cout << "muv:))))))))))))))))))))))))))))超时问题！！！   back_frame： " << back_frame.size() << std::endl;
			// sleep(6);
			return 0; // 非正常识别情况
		}
		if (face_roi_info.state == 1)
		{
		//安全带赋值 720*1280坐标
		// alarm_fbinfo.belt_point[0] = cv::Point(alarm_fbinfo.belt_point[0].x,  alarm_fbinfo.face_point[1].y);
		// alarm_fbinfo.belt_point[1] = cv::Point(alarm_fbinfo.belt_point[1].x,  alarm_fbinfo.face_point[1].y + 50);
		// alarm_fbinfo.belt_point[2] = cv::Point(alarm_fbinfo.belt_point[2].x,  alarm_fbinfo.face_point[1].y + 150) ;
		// alarm_fbinfo.belt_point[3] = cv::Point(alarm_fbinfo.belt_point[3].x,  alarm_fbinfo.face_point[1].y + 150) ;
		// alarm_fbinfo.belt_point[4] = cv::Point(alarm_fbinfo.belt_point[4].x,  alarm_fbinfo.face_point[1].y ) ;


		//安全带赋值 1280*720 坐标
		// alarm_fbinfo.belt_point[0] = cv::Point(-alarm_fbinfo.face_point[1].y,alarm_fbinfo.belt_point[0].x);
		// alarm_fbinfo.belt_point[1] = cv::Point(50-alarm_fbinfo.face_point[1].y,alarm_fbinfo.belt_point[1].x);
		// alarm_fbinfo.belt_point[2] = cv::Point(150-alarm_fbinfo.face_point[1].y, alarm_fbinfo.belt_point[2].x) ;
		// alarm_fbinfo.belt_point[3] = cv::Point(150-alarm_fbinfo.face_point[1].y , alarm_fbinfo.belt_point[3].x) ;
		// alarm_fbinfo.belt_point[4] = cv::Point(-alarm_fbinfo.face_point[1].y, alarm_fbinfo.belt_point[4].x) ;

		// for (size_t i = 0; i < 5; i++)
		// {
		// 	// points.push_back(belt_info_.five_point[i]);
		// 	// std::cout<<"muv:=======================================================================666alarm_fbinfo.five_point[i]:  "<< alarm_fbinfo.belt_point[i] << std::endl;
		// }
		alarm_fbinfo.face_angle = face_roi_info.face_angle[0];
		std::cout << "muv:-------------------------------------------------[debug]  人脸正常：" << std::endl;
		//[1]同步执行识别程序
		std::vector<int> allresult_map(5);
#ifdef DSM_USING_RKNN
			// send_sign = Talarm::alarm_eye(face_roi_info);

			// int ret = dsm_fb::Resnet::resnet_recog(face_roi_info, allresult_map, muv_speed, exterIOparam);
            int ret = dsm_fb::Resnet::resnet_belt(face_roi_info.face_roi[0], allresult_map, muv_speed);
            

#else
			int ret = dsm_fb::Resnet::resnet_recog(resnet18, face_roi_info, allresult_map, subway_speed);
#endif

			if (ret < 0)
			{
				std::cout << "FB:----------------------------------------------Resnet18分类识别异常    ret： " << ret << std::endl;
				return -1; // test3
			}
			std::cout << "FB:----------------------------------------------resnet_recog-Resnet18分类识别正常   ret： " << ret << " allresult_map.size(): " << allresult_map.size() << std::endl;

			//[2]时序监控
			std::vector<int> timeout;
			// uint64_t st_ter = dsm_fb::getTimesmuv();
			std::vector<Nodeflags> c5pos_out = execute_alarm_box(allresult_map, box, timeout);
			// uint64_t edt_ter = dsm_fb::getTimesmuv();
			// std::cout << "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++耗时计算  execute_alarm_box   " << int(edt_ter - st_ter) << std::endl;
			// std::cout<<"muv:))))))))))))))))))))))))))))execute_alarm_box正常   c5pos_out： " << string(c5pos_out[0].notes) << endl;
			// if (c5pos_out[3].notes != "Worthless") {
			// 	std::cout << "**********************************************调试——盲点检测实时结果： " << string(c5pos_out[3].notes) << endl;
			// }
			// std::cout << "muv: *********************8888888888888888888888888*************************打印输出...    timeout" << timeout.size() << std::endl;
			// std::cout << "muv: *********************9999999999999999999999999*************************打印输出...    execute_alarm_box_out" << c5pos_out.size() << std::endl;
			// return -1;


				if (timeout[0] == 404) // 异常超时部分清空
				{
					collmap.superposition[0].clear();
					alarm_fbinfo.seatbelt_alarm = false;
					// return 0;  //非正常识别情况
				}

				if ((c5pos_out[0].notes == "Worthless") || ((c5pos_out[0].notes == "Freeze")))
				{
					// alarm_fbinfo.eye_alarm = false;   // 这里处理被打断时的判断
                    alarm_fbinfo.seatbelt_alarm = false;
				
				}
				else
				{
					// printf("***********result_bai   in line %d  \n",__LINE__);
					// std::cout << "**********************************************w :" << w << std::endl;
					// std::cout << "**********************************************collmap.superposition.size() :" << collmap.superposition.size() << std::endl;
					// std::cout << "**********************************************collmap.superposition[0] :" << collmap.superposition[0].size() << std::endl;
					collmap.superposition[0].push_back(c5pos_out[0].value);
					if (c5pos_out[0].notes == "End") // 读入五个类型，  但触发报警的时间不一定，所以以下代码至少只有一个结果输出，或者，该帧率没有输出报警的情况
					{
						// cout << "muv:))))))))))))))))))))))))))))成功识别一次数据  存在End c5pos_out： " << w << endl;
						// sleep(2);
						int count_occurrences = 0;
						float result_bai = 0;
						// 调用统计函数，输出结果，并清零
						if (!collmap.superposition[0].empty())
						{
							count_occurrences = std::count(collmap.superposition[0].begin(), collmap.superposition[0].end(), 1);
							result_bai = (static_cast<float>(count_occurrences) / static_cast<float>(collmap.superposition[0].size())) * 100;
						}
						// teststd::cout << "*******************置信度   :  " << result_bai << std::endl;

							std::cout << "muv:**********************************************【安全带】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[0].size() << std::endl;
							if (result_bai > globalfb.seatbelt_sensitivity) // 灵敏度判断
							{
								alarm_fbinfo.seatbelt_alarm = true;
							}
							else
							{
								alarm_fbinfo.seatbelt_alarm = false;
							}
						
						
						collmap.superposition[0].clear();
						// std::cout << "muv:______________________________________________输出一帧结果（应该是打印五次）：   " << w << std::endl;
					}
				}

				// 每次都会输出报警结果，一共输出五个
			//			string text_yaw = "left-right: " + to_string(int(face_roi_info.face_angle[0]));
			//			string text_pitch = "up-down: " + to_string(int(face_roi_info.face_angle[1]));
			//			string text_roll = "roll_angle: " + to_string(int(face_roi_info.face_angle[2]));
			//			string distance = to_string(90 - int(face_roi_info.face_angle[3] * 100)) + "cm";//距离
			//			putText(back_frame, text_yaw, Point(50, 50), FONT_HERSHEY_COMPLEX, 0.75, Scalar(0, 0, 255), 1, 1, 0);
			//			putText(back_frame, text_pitch, Point(50, 100), FONT_HERSHEY_COMPLEX, 0.75, Scalar(0, 0, 255), 1, 1, 0);
			//			putText(back_frame, text_roll, Point(50, 150), FONT_HERSHEY_COMPLEX, 0.75, Scalar(0, 0, 255), 1, 1, 0);
			//			putText(back_frame, distance, Point(50, 200), FONT_HERSHEY_COMPLEX, 0.75, Scalar(0, 0, 255), 1, 1, 0);
			//			cv::imshow(kWinName, back_frame);
			// return -1;
		}
		else
		{
			printf("*********face_roi_info.state == 4  人脸异常**run_detector in line %d  \n", __LINE__);
			return -1;
		}

		// 危险程度，包含0：不存在报警信息，1~3疲劳程度 4~6危险驾驶信息
		alarm_fbinfo.fatigue_rank = risk_level(alarm_fbinfo);

		if (alarm_fbinfo.fatigue_rank > 0)
		{
			std::cout << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << "叉车安全带检测项目FB:..........................................................……=……=……=……=……=……=……=……=……=……Alarm_info.fatigue_rank: " << alarm_fbinfo.fatigue_rank << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl;
			return 1;
		}
		else if (alarm_fbinfo.fatigue_rank == 0)
		{
			return 0;
		}
		else
		{
			return -1;
		}

		// teststring rank = "Alarm.size:  " + to_string(alarm_fbinfo.fatigue_rank);
		// testcv::putText(back_frame, rank, Point(20, 1000), FONT_HERSHEY_COMPLEX, 2, Scalar(2, 255, 155), 2, 8, 0);
		//		cv::imshow(kWinName, back_frame);
	}
	catch (const cv::Exception &ii)
	{
		return -1;
	}
	return 0;
}

/*************************************************
//  Method:    convertTo3Channels
//  Description: 将单通道图像转为三通道图像
//  Returns:   cv::Mat
//  Parameter: binImg 单通道图像对象
*************************************************/
cv::Mat dsm_fb::convertTo3Channels(const cv::Mat &binImg)
{
	cv::Mat three_channel = cv::Mat::zeros(binImg.rows, binImg.cols, CV_8UC3);
	std::vector<cv::Mat> channels;
	for (int i = 0; i < 3; i++)
	{
		channels.push_back(binImg);
	}
	cv::merge(channels, three_channel);
	return three_channel;
}

// 危险程度，包含0：不存在报警信息，1~3疲劳程度 4~6危险驾驶信息
int dsm_fb::risk_level(DSMInfo alarm_infos)
{
	// 后续会增加遮挡、抽烟、打电话的识别判断需要修改此处
	if ((alarm_infos.eye_alarm == 0) && (alarm_infos.smoking_alarm == 0) &&
		(alarm_infos.lookaround_alarm == 0) && (alarm_infos.Blindspot_alarm == 0) && (alarm_infos.seatbelt_alarm == 0))
	{
		alarm_infos.fatigue_rank = 0;
		return 0;
	}
	int sum_alarm;
	sum_alarm = alarm_infos.eye_alarm + alarm_infos.smoking_alarm + alarm_infos.lookaround_alarm + alarm_infos.Blindspot_alarm + alarm_infos.seatbelt_alarm;
	if (sum_alarm > 0)
	{
		if (alarm_infos.eye_alarm == 1)
		{
			std::cout << "muv:..........................................................……=……=……=……=……=……=……=……=……=……Alarm_info.==  Eye_alarm " << std::endl;
		}
		if (alarm_infos.smoking_alarm == 1)
		{
			std::cout << "muv:..........................................................……=……=……=……=……=……=……=……=……=……Alarm_info.==  Smoking_alarm " << std::endl;
		}
		if (alarm_infos.Blindspot_alarm == 1)
		{
			std::cout << "muv:..........................................................……=……=……=……=……=……=……=……=……=……Alarm_info.==  Blindspot_alarm " << std::endl;
		}
		if (alarm_infos.lookaround_alarm == 1)
		{
			std::cout << "muv:..........................................................……=……=……=……=……=……=……=……=……=……Alarm_info.==  Lookaround_alarm " << std::endl;
		}
		if (alarm_infos.seatbelt_alarm == 1)
		{
			std::cout << "muv:..........................................................……=……=……=……=……=……=……=……=……=……Alarm_info.==  Seatbelt_alarm " << std::endl;
		}
		alarm_infos.fatigue_rank = sum_alarm;
		return sum_alarm;
	}
	else
	{
		return 0;
	}
}

void dsm_fb::init_DSMInfo_parmas(DSMInfo alarm_fbinfo)
{
	alarm_fbinfo.isSameDriver = 0;
	alarm_fbinfo.face_score = 0.6666;
	alarm_fbinfo.eye_alarm = false;
	alarm_fbinfo.smoking_alarm = false;
	alarm_fbinfo.seatbelt_alarm = false;
	alarm_fbinfo.Blindspot_alarm = false;
	alarm_fbinfo.lookaround_alarm = false;
	alarm_fbinfo.fatigue_rank = 0;
}

std::vector<dsm_fb::Nodeflags> dsm_fb::Kernelfb::execute_alarm_box(std::vector<int> input, dsm_fb::AlarmBox &boxmuv, std::vector<int> &timeout)
{
	// std::vector<cv::Point3i> timepind = {{0, globalfb.event_eye_time, globalfb.freeze_eye_time},
	// 									 {0, globalfb.event_smoking_time, globalfb.freeze_smoking_time},
	// 									 {0, globalfb.event_seatbelt_time, globalfb.freeze_seatbelt_time},
	// 									 {0, globalfb.event_Blindspot_time, globalfb.freeze_Blindspot_time},
	// 									 {0, globalfb.event_lookaround_time, globalfb.freeze_lookaround_time}};
    std::vector<cv::Point3i> timepind = {{0, globalfb.event_seatbelt_time, globalfb.freeze_seatbelt_time}};
	std::vector<dsm_fb::Nodeflags> out_data(input.size());
	std::vector<dsm_fb::Status> restatus = boxmuv.alarm_box(input, timepind);
	for (size_t i = 0; i < input.size(); i++)
	{
		// std::cout << "muv:~~~~~~~~~~~~~~~~~~Alarm " << (i + 1) << " status: " << getStatusName(restatus[i]) << std::endl;
		out_data[i].value = input[i];
		out_data[i].notes = getStatusName(restatus[i]);
		timeout.push_back(timepind[i].x);
		// if (timepind[i].x > 0) // 404 中断操作
		// {
		// 	std::cout << " 99999999999999999999999999999999999999999999999999999timepind[i].x: " << timeout.size() << std::endl;
		// }
	}
	// std::cout << " out_data: " << out_data.size() << std::endl;
	return out_data;
}

bool dsm_fb::LoadbeltConfigFile(const std::string &file_name)
{
	cv::FileStorage config(file_name, cv::FileStorage::READ);
	bool flag = config.isOpened();
	if (flag)
	{
		std::cout << "     ********** Open config File true:" << file_name << std::endl;
	}
	else
	{
		std::cout << "     **********Open config File :" << file_name << " failed.";
		return false;
	}
	printf("**********load debug in line %d  \n", __LINE__);
	// return true;
	if (!(config.getFirstTopLevelNode().empty()))
	{
		// std::cout << "++++++~~~~~~~~~~~~~~~~~~~~~~~  config[yaw_angle].empty() " << !config["yaw_angle"].empty() << std::endl;
		dsm_fb::ParamsToCheck params;
		// 人脸角度参数
		params.yaw_angle = config["yaw_angle"];
		printf("**********params.yaw_angle debug in line %d  \n", params.yaw_angle);
		// 安全带坐标参数
		cv::Point p1, p2, p3, p4, p5;
		cv::Point ps1, ps2, ps3, ps4, ps5;
		config["point1"]["x"] >> p1.x;   ps1.x = (p1.x +280)*0.5;
		config["point1"]["y"] >> p1.y;	 ps1.y = p1.y *0.5;
		config["point2"]["x"] >> p2.x;	 ps2.x = (p2.x +280)*0.5;
		config["point2"]["y"] >> p2.y;	 ps2.y = p2.y *0.5;	
		config["point3"]["x"] >> p3.x;	 ps3.x = (p3.x +280)*0.5;	
		config["point3"]["y"] >> p3.y;	 ps3.y = p3.y *0.5;
		config["point4"]["x"] >> p4.x;	 ps4.x = (p4.x +280)*0.5;
		config["point4"]["y"] >> p4.y;	 ps4.y = p4.y *0.5;
		config["point5"]["x"] >> p5.x; 	 ps5.x = (p5.x +280)*0.5;
		config["point5"]["y"] >> p5.y;	 ps5.y = p5.y *0.5;
		
		// std::cout << "muv:----------------------------------------ps1 =  " << ps1 << std::endl;
		// std::cout << "muv:----------------------------------------ps2 =  " << ps2 << std::endl;
		// std::cout << "muv:----------------------------------------ps3 =  " << ps3 << std::endl;
		// std::cout << "muv:----------------------------------------ps4 =  " << ps4 << std::endl;
		// std::cout << "muv:----------------------------------------ps5 =  " << ps5 << std::endl;
		//params 坐标是填充后的640*640下的
		params.five_points.push_back(ps1);
		params.five_points.push_back(ps2);
		params.five_points.push_back(ps3);
		params.five_points.push_back(ps4);
		params.five_points.push_back(ps5);

		printf("muv:**********getFirstTopLevelNode debug in line %d  \n", __LINE__);
		// params.point_count = 5;
		params.left_right_button = config["left_right"];
		int dbutton = config["debug_button"];
		params.debug_button = dbutton == 1 ? true : false;
		printf("muv:**********getFirstTopLevelNode debug in line %d  \n", __LINE__);
		// std::cout << "muv:----------------------------------------acheck_debug_paramlmuv_info.belt_point =  " << alarm_fbinfo.belt_point.size() << std::endl;
		// 检查参数，错误则传默认值  徐榕佑 负责输出的坐标值，resize到640*640
		bool check_state = dsm_fb::check_debug_param(params);
		// std::cout << "muv:----------------------------------------acheck_debug_paramlmuv_info.belt_point =  " << alarm_fbinfo.belt_point.size() << std::endl;
		// printf("**********getFirstTopLevelNode debug in line %d  \n", __LINE__);
		dsm_fb::ad_angle angle_info;
		angle_info.yaw_angle = params.yaw_angle;
		angle_info.adjusted_button = true;
		dsm_fb::SkewParam::SetAngle(angle_info);

		// 安全带坐标排序
		dsm_fb::belt_point_deal(params.five_points, params.left_right_button);
		//转为UI显示界面 //输出安全带坐标 已转换为UI
		cv::Point outpos;
		for (size_t i = 0; i < params.five_points.size(); i++)
		{
			outpos.x = (params.five_points[i].x *2) -280;
			outpos.y = (params.five_points[i].y *2); 
			alarm_fbinfo.belt_point.push_back(outpos);
		}
		
		std::cout<<"muv:----------------------------------------LoadDebugConfigFileoutput [1]belt: alarm_fbinfo.belt_point =  " <<alarm_fbinfo.belt_point[0]<<std::endl; 
        std::cout<<"muv:----------------------------------------LoadDebugConfigFileoutput [2]belt: alarm_fbinfo.belt_point =  " <<alarm_fbinfo.belt_point[1]<<std::endl; 
        std::cout<<"muv:----------------------------------------LoadDebugConfigFileoutput [3]belt: alarm_fbinfo.belt_point =  " <<alarm_fbinfo.belt_point[2]<<std::endl; 
        std::cout<<"muv:----------------------------------------LoadDebugConfigFileoutput [4]belt: alarm_fbinfo.belt_point =  " <<alarm_fbinfo.belt_point[3]<<std::endl; 
        std::cout<<"muv:----------------------------------------LoadDebugConfigFileoutput [5]belt: alarm_fbinfo.belt_point =  " <<alarm_fbinfo.belt_point[4]<<std::endl; 
		dsm_fb::BELT_INFO belt_info;
		params.five_points.swap(belt_info.five_point);
		belt_info.left_right_button = params.left_right_button;
		dsm_fb::SkewParam::SetBeltInfo(belt_info);
		dsm_fb::SkewParam::debug_button = params.debug_button;

		config.release();
		printf("**********XUXU in line %d  \n", __LINE__);
	}
	else
	{
		std::cout << "debug_config File:" << file_name << " 文件内为空内容，读取默认参数" << std::endl;

		dsm_fb::ParamsToCheck params;
		// 读取默认参数
		dsm_fb::LoadDefaultParam(params);

		dsm_fb::ad_angle angle_info;
		angle_info.yaw_angle = params.yaw_angle;
		angle_info.adjusted_button = true;
		dsm_fb::SkewParam::SetAngle(angle_info);

		dsm_fb::belt_point_deal(params.five_points, params.left_right_button);
		dsm_fb::BELT_INFO belt_info;
				//转为UI显示界面 //输出安全带坐标 已转换为UI
		cv::Point outpos;
		for (size_t i = 0; i < params.five_points.size(); i++)
		{
			outpos.x = (params.five_points[i].x *2) -280;
			outpos.y = (params.five_points[i].y *2) ; 
			alarm_fbinfo.belt_point.push_back(outpos);
		}
		params.five_points.swap(belt_info.five_point);
		belt_info.left_right_button = params.left_right_button;
		dsm_fb::SkewParam::SetBeltInfo(belt_info);
		dsm_fb::SkewParam::debug_button = params.debug_button;
	}

// printf("**********alarm_fbinfo.belt_point.size()in line %d  \n", __LINE__);
	// std::copy(belt_info.five_point.begin(), belt_info.five_point.end(), std::back_inserter(alarm_fbinfo.belt_point));
	// alarm_fbinfo.belt_point.push_back(belt_info.five_point[0]);
	// alarm_fbinfo.belt_point.push_back(belt_info.five_point[1]);
	// alarm_fbinfo.belt_point.push_back(belt_info.five_point[2]);
	// alarm_fbinfo.belt_point.push_back(belt_info.five_point[3]);
	// alarm_fbinfo.belt_point.push_back(belt_info.five_point[4]);
	return true;
}

bool dsm_fb::SkewParam::debug_button = false;
dsm_fb::BELT_INFO dsm_fb::SkewParam::belt_info;
dsm_fb::ad_angle dsm_fb::SkewParam::adjusted_angle;

void dsm_fb::SkewParam::SetAngle(ad_angle ad_angle)
{
	SkewParam::adjusted_angle = ad_angle;
}
void dsm_fb::SkewParam::SetBeltInfo(BELT_INFO belt_info)
{
	SkewParam::belt_info = belt_info;
}

dsm_fb::BELT_INFO dsm_fb::SkewParam::GetBeltInfo()
{
	return SkewParam::belt_info;
}
dsm_fb::ad_angle dsm_fb::SkewParam::GetAngle()
{
	return SkewParam::adjusted_angle;
}