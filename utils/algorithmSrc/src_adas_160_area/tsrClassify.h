#ifndef _TSR_CLASSIFY_H_
#define _TSR_CLASSIFY_H_

#include <iostream>
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>


#if (defined WIN32 || defined _WIN32 )
#else
  #include "dasShareDefine.h"
#endif

#ifdef DAS_USING_RKNN

  #include "rknn_api.h"
#endif



class Classify
{
public:
	Classify();
	~Classify();
	void init(std::string model_path, cv::Size size, int classes, std::vector<int> labels);
	void PreProcess(const cv::Mat& image, cv::Mat& image_blob);
	bool net_detect(std::vector<cv::Mat> input_img, std::vector<int>& speeds, std::vector<int>& index);

	void single_img_detect(cv::Mat img, std::vector<int>& all_speeds, std::vector<int>& index);

	cv::Mat convertTo3Channels(const cv::Mat& binImg);
private:
	cv::Size input_size;

#ifdef DAS_USING_RKNN
	rknn_context ctx;
        rknn_tensor_attr input_attrs[1];
        rknn_input_output_num io_num;
#else
	cv::dnn::Net net;
#endif

	int classes;
	std::vector<int> labels;
 
        std::string model_path;
};

void write_num(cv::Mat img, std::string label);

#endif
