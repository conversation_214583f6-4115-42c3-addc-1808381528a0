﻿
#include "dasYOLOV5Seg.h"

#if (defined WIN32 || defined _WIN32 )
#include < Windows.h >
#include "windows.h"
#pragma warning(disable:4996)
#endif


#ifdef DAS_USING_RKNN
/*-------------------------------------------
Includes
-------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dlfcn.h>
#include <vector>
#include <sys/time.h>

//#define _BASETSD_H




//#include "drm_func.h"
//#include "rga_func.h"

//#include "postprocess.h"

#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>

#ifdef DAS_USING_RGA
#include "XuRGAUtils.h"
#endif

//#define PERF_WITH_POST 1

#endif

using namespace cv;
#ifdef DAS_USING_RKNN
#else
using namespace dnn;
#endif
using namespace std;


#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include <vector>
#include <set>
#include <stdint.h>


#ifdef DAS_USING_RGA
	extern XuRGAUtils rga_utils;
#endif

namespace das
{
	int yolov5seg_anchor0[6] = { 10, 13, 16, 30, 33, 23 };
	int yolov5seg_anchor1[6] = { 30, 61, 62, 45, 59, 119 };
	int yolov5seg_anchor2[6] = { 116, 90, 156, 198, 373, 326 };

	// segmentation
	//int yolov5seg_anchor0[6] = { 11, 10, 19, 20, 33, 34 };
	//int yolov5seg_anchor1[6] = { 59, 56, 265, 29, 76, 192 };
	//int yolov5seg_anchor2[6] = { 140, 152, 521, 113, 552, 303 };



	YOLOV5Seg::YOLOV5Seg()
	{}

	YOLOV5Seg::~YOLOV5Seg()
	{
#ifdef DAS_USING_RKNN
		// release
		/*
		ret = rknn_destroy(this->ctx);
		drm_buf_destroy(&drm_ctx, drm_fd, buf_fd, handle, drm_buf, actual_size);

		drm_deinit(&drm_ctx, drm_fd);
		RGA_deinit(&rga_ctx);
		if (model_data)
			free(model_data);

		if (resize_buf)
			free(resize_buf);
		*/
#endif
	}

	void YOLOV5Seg::infer(cv::Mat frame, std::vector<cv::Point> no_detection_area1, std::vector<cv::Point> no_detection_area2)
	{
		cv::Mat dstimg = letterbox(frame, model_height, model_width, keep_ratio, this->newh, this->neww, this->padh, this->padw);

		//cv::imshow("show", dstimg);
		//cv::waitKey(0);

		cv::Scalar color(255, 255, 0);
		if (no_detection_area1.size() > 0)
			fillNoDetectionArea(dstimg, no_detection_area1, frame.cols, frame.rows, newh, neww, padh, padw, color);
		if (no_detection_area2.size() > 0)
			fillNoDetectionArea(dstimg, no_detection_area2, frame.cols, frame.rows, newh, neww, padh, padw, color);

		//cv::imshow("show", dstimg);
        //cv::waitKey(0);

#ifdef DAS_USING_RKNN
		int status = 0;

		void* drm_buf = NULL;
		int drm_fd = -1;
		int buf_fd = -1; // converted from buffer handle
		unsigned int handle;
		size_t actual_size = 0;
		int img_width = 0;
		int img_height = 0;
		int img_channel = 0;
		//rga_context rga_ctx;
		//drm_context drm_ctx;

		//struct timeval start_time, stop_time;
		int ret;
		//memset(&rga_ctx, 0, sizeof(rga_context));
		//memset(&drm_ctx, 0, sizeof(drm_context));

		// preprocess image
#ifdef DAS_USING_RGA    
		rga_utils.imageTransformation(dstimg.cols, dstimg.rows, rga_utils.IMG_TYPE_BGR888, dstimg.data, dstimg.cols, dstimg.rows, rga_utils.IMG_TYPE_RGB888, dstimg.data);
#else
		cvtColor(dstimg, dstimg, COLOR_BGR2RGB);  //#include <opencv2/opencv.hpp>
#endif

		img_height = dstimg.rows;
		img_width = dstimg.cols;
		img_channel = dstimg.channels();

		rknn_input inputs[1];
		memset(inputs, 0, sizeof(inputs));
		inputs[0].index = 0;
		inputs[0].type = RKNN_TENSOR_UINT8;
		inputs[0].size = model_width * model_height * model_channel;
		inputs[0].fmt = RKNN_TENSOR_NHWC;
		inputs[0].pass_through = 0;

		/*
		// DRM alloc buffer
		drm_fd = drm_init(&drm_ctx);
		drm_buf = drm_buf_alloc(&drm_ctx, drm_fd, img_width, img_height, img_channel * 8, &buf_fd, &handle, &actual_size);

		//memcpy(drm_buf, input_data, img_width * img_height * img_channel);
		memcpy(drm_buf, frame.data, img_width * img_height * img_channel);

		void *resize_buf = malloc(model_height * model_width * model_channel);

		// init rga context
		RGA_init(&rga_ctx);
		img_resize_slow(&rga_ctx, drm_buf, img_width, img_height, resize_buf, model_width, model_height);
		*/

		//imwrite("./check.bmp", dstimg);
		inputs[0].buf = dstimg.data;

		rknn_inputs_set(this->ctx, this->io_num.n_input, inputs);

		memset(this->outs, 0, sizeof(this->outs));
		for (int i = 0; i < this->io_num.n_output; i++)
		    this->outs[i].want_float = 1;

		ret = rknn_run(this->ctx, NULL);
		ret = rknn_outputs_get(this->ctx, this->io_num.n_output, this->outs, NULL);

		//imwrite("./out.bmp", frame);
#else
		normalize(dstimg, this->mean, this->std);
		cv::Mat blob = blobFromImage(dstimg);
		this->net.setInput(blob);
		this->net.forward(outs, this->net.getUnconnectedOutLayersNames());
#endif

		//std::cout << outs[0].data << endl;
	}


struct ThreadParamsMatmut
{
	cv::Mat m1_in;
	cv::Mat m2_in;
	cv::Mat m3_out;
};

#if (defined WIN32 || defined _WIN32)
	LRESULT WINAPI MatmulThreadFunctionYoloV5Seg(LPARAM lp)
#else
	void* MatmulThreadFunctionYoloV5Seg(void* lp)
#endif
	{
		struct ThreadParamsMatmut* pm = (struct ThreadParamsMatmut*)lp;

                printf("++++++++++ ThreadParamsMatmut arg: %p\n", pm);

		pm->m3_out = pm->m1_in * pm->m2_in;   //矩阵相乘，顺序不能调转
		return 0;
	}


        void YOLOV5Seg::init(char* iniPath, int img_input_width, int img_input_height)
        {
			sprintf(this->modelVersion, "%s", "0.0.0");

          int ret;
          const bool keep_ratio = true;


		int netWidth;
		int netHeight;
		int objNum;
		float boxThreshold;
		float classThreshold;
		float nmsThreshold;
		float mean[3];
		float std[3];

		char modelpath[200];
		char labelPath[200];
		char imgPath[200];

		iniStart(iniPath);

		iniGetInt("net", "netWidth", &netWidth);
		iniGetInt("net", "netHeight", &netHeight);
		iniGetInt("net", "objNum", &objNum);
		iniGetFloat("net", "boxThreshold", &boxThreshold);
		iniGetFloat("net", "classThreshold", &classThreshold);
		iniGetFloat("net", "nmsThreshold", &nmsThreshold);
		iniGetFloat("net", "mean0", &mean[0]);
		iniGetFloat("net", "mean1", &mean[1]);
		iniGetFloat("net", "mean2", &mean[2]);
		iniGetFloat("net", "std0", &std[0]);
		iniGetFloat("net", "std1", &std[1]);
		iniGetFloat("net", "std2", &std[2]);

		iniGetString("path", "modelPath", modelpath);
		iniGetString("path", "labelPath", labelPath);
		iniGetString("path", "imgPath", imgPath);


		this->img_input_width = img_input_width;
		this->img_input_height = img_input_height;

		this->model_width = netWidth;
		this->model_height = netHeight;
		this->model_channel = 3;

		this->objNum = objNum;

		this->segChannels = 32;
		this->segWidth = this->model_width / 4.0;
		this->segHeight = this->model_height / 4.0;

		this->prop_box_size = 5 + objNum + segChannels;

		this->classThreshold = classThreshold;
		this->nmsThreshold = nmsThreshold;
		this->boxThreshold = boxThreshold;

		this->keep_ratio = keep_ratio;

          

#if (defined WIN32 || defined _WIN32 )
#else
		mThDo.init(MatmulThreadFunctionYoloV5Seg, "adas_Matmul_yolov5seg");
        
            /* 去掉可能的出现的/r */
            if(modelpath[strlen(modelpath) - 1] == 0x0D){
                modelpath[strlen(modelpath) - 1] = '\0';
            }
            if(labelPath[strlen(labelPath) - 1] == 0x0D){
                labelPath[strlen(labelPath) - 1] = '\0';
            }
            if(imgPath[strlen(imgPath) - 1] == 0x0D){
                imgPath[strlen(imgPath) - 1] = '\0';
            }
#endif

		labels = (char**)malloc(objNum * DAS_OBJ_NAME_MAX_SIZE * sizeof(char));
		ret = loadLabelName(labelPath, labels, objNum);

		strcpy(this->imgPath, imgPath);

		for (int i = 0; i < 3; i++)
		{
			this->mean[i] = mean[i];
			this->std[i] = std[i];
		}

		newh = netHeight;
		neww = netWidth;
		padw = 0;
		padh = 0;

		Mat dstimg;
		if ( keep_ratio && (this->img_input_height != this->img_input_width) )
		{
			float hw_scale = (float)this->img_input_height / this->img_input_width;
			if (hw_scale > 1)
			{
				newh = netHeight;
				neww = int(netWidth / hw_scale);
				padw = int((netWidth - neww) * 0.5);
			}
			else
			{
				newh = (int)netWidth * hw_scale;
				neww = netWidth;
				padh = (int)(netHeight - newh) * 0.5;
			}
		}

		this->segWidth_div_modelWidth = (float)this->segWidth / this->model_width;
		this->segHeight_div_modelHeight = (float)this->segHeight / this->model_height;
		this->frameCols_div_neww = (float)this->img_input_width / this->neww;
		this->frameRows_div_newh = (float)this->img_input_height / this->newh;
		
#ifdef DAS_USING_RKNN
		/* Create the neural network */
		printf("Loading mode...\n");
		int model_data_size = 0;
		unsigned char* model_data;


		int len = strlen(modelpath);
		if (modelpath[len - 4] == 'r' && modelpath[len - 3] == 'k' && modelpath[len - 2] == 'n' && modelpath[len - 1] == 'n')
		{
			model_data = load_model(modelpath, &model_data_size);
			printf("the model format is rknn\n");
		}
		else if (modelpath[len - 3] == 'v' && modelpath[len - 2] == 'i' && modelpath[len - 1] == 's')
		{
			char key1 = '0';
			char key2 = '0';
			char key3 = '0';
			int vis_model_data_size = 0;
			unsigned char* vis_model_data = load_model(modelpath, &vis_model_data_size);
			model_data = decode(vis_model_data, vis_model_data_size, &model_data_size, &key1, &key2, &key3, false, NULL);

			sprintf(this->modelVersion, "%c.%c.%c", key1, key2, key3);   //get version
		}
		else
		{
			printf("Unrecognized model format\n");
			return;
		}

		ret = rknn_init(&this->ctx, model_data, model_data_size, 0);
		if (ret < 0)
		{
			printf("rknn_init error ret=%d\n", ret);
			return;
		}

		rknn_sdk_version version;
		ret = rknn_query(this->ctx, RKNN_QUERY_SDK_VERSION, &version, sizeof(rknn_sdk_version));
		if (ret < 0)
		{
			printf("rknn_init error ret=%d\n", ret);
			return;
		}
		printf("sdk version: %s driver version: %s\n", version.api_version, version.drv_version);

		ret = rknn_query(this->ctx, RKNN_QUERY_IN_OUT_NUM, &this->io_num, sizeof(this->io_num));
		if (ret < 0)
		{
			printf("rknn_init error ret=%d\n", ret);
			return;
		}
		printf("model input num: %d, output num: %d\n", this->io_num.n_input, this->io_num.n_output);

		rknn_tensor_attr input_attrs[this->io_num.n_input];
		memset(input_attrs, 0, sizeof(input_attrs));
		for (int i = 0; i < this->io_num.n_input; i++)
		{
			input_attrs[i].index = i;
			ret = rknn_query(this->ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
			if (ret < 0)
			{
				printf("rknn_init error ret=%d\n", ret);
				return;
			}
			dump_tensor_attr(&(input_attrs[i]));
		}

		memset(this->output_attrs, 0, sizeof(this->output_attrs));
		for (int i = 0; i < this->io_num.n_output; i++)
		{
			this->output_attrs[i].index = i;
			ret = rknn_query(this->ctx, RKNN_QUERY_OUTPUT_ATTR, &(this->output_attrs[i]), sizeof(rknn_tensor_attr));
			dump_tensor_attr(&(this->output_attrs[i]));
			if (this->output_attrs[i].qnt_type != RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC || this->output_attrs[i].type != RKNN_TENSOR_UINT8)
			{
				fprintf(stderr, "The Demo required for a Affine asymmetric u8 quantized rknn model, but output quant type is %s, output data type is %s\n", get_qnt_type_string(this->output_attrs[i].qnt_type), get_type_string(this->output_attrs[i].type));
				return;
			}
		}

		for (int i = 0; i < this->io_num.n_output; ++i)
		{
			this->out_scales.push_back(this->output_attrs[i].scale);
			this->out_zps.push_back(this->output_attrs[i].zp);
		}

		this->model_channel = 3;

		if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
		{
			printf("model is NCHW input fmt\n");
			this->model_width = input_attrs[0].dims[0];
			this->model_height = input_attrs[0].dims[1];
		}
		else
		{
			printf("model is NHWC input fmt\n");
			this->model_width = input_attrs[0].dims[1];
			this->model_height = input_attrs[0].dims[2];
		}

		printf("model input model_height=%d, model_width=%d, model_channel=%d\n", this->model_height, this->model_width, this->model_channel);
#else
		this->net = readNet(modelpath);
#endif
	}

        void resultMaskArrange(bool isArrange, std::vector<detect_result_t> result_in, std::vector<detect_result_t> &result_out, int &mask_num)
	{
		if (isArrange == false)
		{
			mask_num = result_in.size();
			result_out = result_in;
		}
		else
		{
			std::vector<detect_result_t> mask_on;
			std::vector<detect_result_t> mask_off;

			for (int i = 0; i < result_in.size(); i++)
			{
				std::string label = result_in[i].name;
				BOX_RECT box = result_in[i].box;
				int box_width = box.right - box.left;
				int box_height = box.bottom - box.top;

				bool b1 = (label == "road") || (label == "lane");
				bool b2 = (box_width > 5) && (box_height > 5);

				if (b1 && b2)
					mask_on.push_back(result_in[i]);
				else
					mask_off.push_back(result_in[i]);
			}

			//push的顺序不能反，先push为mask_on，后push为mask_off
			for (int i = 0; i < mask_on.size(); i++)
				result_out.push_back(mask_on[i]);

			for (int i = 0; i < mask_off.size(); i++)
				result_out.push_back(mask_off[i]);

			mask_num = mask_on.size();

			/*
			for (int i = 0; i < result_in.size(); i++)
			{
				std::string label = result_in[i].name;
				if (label == "road" || label == "lane")   //  obj.rect.width > size_th || obj.rect.height > size_th
				{
					result_out.push_back(result_in[i]);
					mask_num++;
				}
			}
			for (int i = 0; i < result_in.size(); i++)
			{
				std::string label = result_in[i].name;
				if (label != "road" && label != "lane")   //  obj.rect.width > size_th || obj.rect.height > size_th
				{
					result_out.push_back(result_in[i]);
				}
			}
			*/
		}
	}


#ifdef DAS_USING_RKNN
	void YOLOV5Seg::postProcess(float **input_outs, std::vector<DetObject>& det_object, cv::Mat& mask_90p)
#else
	void YOLOV5Seg::postProcess(std::vector<cv::Mat> input_outs, std::vector<DetObject>& det_object, cv::Mat& mask_90p)
#endif
	{

#ifdef DEBUG_PRINT
		printf("lyc dasYOLOV5Seg.cpp postProcess() start\n");
#endif

		float nms_threshold = this->nmsThreshold;
		float class_threshold = this->classThreshold;

#ifdef DAS_USING_TRACK
		class_threshold *= 0.5;
#endif

		std::vector<detect_result_t> detect_result_group;

#ifdef DAS_USING_PARALLEL_PTHREAD
    #ifdef DAS_USING_RKNN
		post_process_fp_seg((float*)input_outs[0], (float*)input_outs[1], (float*)input_outs[2], yolov5seg_anchor0, yolov5seg_anchor1, yolov5seg_anchor2, this->labels, this->objNum, this->prop_box_size, this->segChannels, this->model_height, this->model_width, class_threshold, nms_threshold, detect_result_group);
    #else
		post_process_fp_seg((float*)input_outs[1].data, (float*)input_outs[2].data, (float*)input_outs[3].data, yolov5seg_anchor0, yolov5seg_anchor1, yolov5seg_anchor2, this->labels, this->objNum, this->prop_box_size, this->segChannels, this->model_height, this->model_width, class_threshold, nms_threshold, detect_result_group);
    #endif
#else
    #ifdef DAS_USING_RKNN
		post_process_fp_seg((float*)this->outs[0].buf, (float*)this->outs[1].buf, (float*)this->outs[2].buf, yolov5seg_anchor0, yolov5seg_anchor1, yolov5seg_anchor2, this->labels, this->objNum, this->prop_box_size, this->segChannels, this->model_height, this->model_width, class_threshold, nms_threshold, detect_result_group);
    #else
		post_process_fp_seg((float*)this->outs[1].data, (float*)this->outs[2].data, (float*)this->outs[3].data, yolov5seg_anchor0, yolov5seg_anchor1, yolov5seg_anchor2, this->labels, this->objNum, this->prop_box_size, this->segChannels, this->model_height, this->model_width, class_threshold, nms_threshold, detect_result_group);
    #endif
#endif

		int mask_num = 0;
		std::vector<detect_result_t> detect_result_group2;
		bool isRunArrange = true;  //设置为true时,为了提高效率，只计算道路、车道线等的mask，人、车等不计算；设置为false时，就所有目标都会计算mask
		resultMaskArrange(isRunArrange, detect_result_group, detect_result_group2, mask_num);  //将需要计算mask的结果放置最前，后续只是计算前面的几个mask，后面的为不需要算mask

		int detect_result_group_size = detect_result_group2.size();

		Rect rect_img(0, 0, this->img_input_width, this->img_input_height);  //防越界，标准图像

#ifdef DEBUG_PRINT
		printf("lyc %s, line = %d\n", __FILE__, __LINE__);
#endif

		if (mask_num > 0)
		{
			//需要计算mask的分支
			const int OPTIMIZE_GAP = 4;
			cv::Mat maskProposals1, maskProposals2;
			if (mask_num <= OPTIMIZE_GAP)
			{
				for (int i = 0; i < mask_num; i++)
				{
					detect_result_t* det_result = &(detect_result_group2[i]);
					maskProposals1.push_back(cv::Mat(det_result->temp_mask_proposals).t());
				}
			}
			else
			{
				int half_cnt = mask_num * 0.5;
				for (int i = 0; i < half_cnt; i++)
				{
					detect_result_t* det_result = &(detect_result_group2[i]);
					maskProposals1.push_back(cv::Mat(det_result->temp_mask_proposals).t());
				}
				for (int i = half_cnt; i < mask_num; i++)
				{
					detect_result_t* det_result = &(detect_result_group2[i]);
					maskProposals2.push_back(cv::Mat(det_result->temp_mask_proposals).t());
				}
			}

			// 处理mask
#ifdef DAS_USING_PARALLEL_PTHREAD
  #ifdef DAS_USING_RKNN
			float* pdata = (float*)input_outs[3];
  #else
			float* pdata = (float*)input_outs[0].data;
  #endif
#else
  #ifdef DAS_USING_RKNN
			float* pdata = (float*)this->outs[3].buf;
  #else
			float* pdata = (float*)this->outs[0].data;
  #endif
#endif

#ifdef DEBUG_PRINT
			printf("lyc %s, line = %d\n", __FILE__, __LINE__);
#endif

			if (detect_result_group_size > 0)
			{
				std::vector<float> mask(pdata, pdata + this->segChannels * this->segWidth * this->segHeight);

				cv::Mat mask_protos = cv::Mat(mask);
				cv::Mat protos = mask_protos.reshape(0, { this->segChannels, this->segWidth * this->segHeight });//将prob1的值 赋给mask_protos

#ifdef DEBUG_PRINT
				printf("lyc %s, line = %d\n", __FILE__, __LINE__);
#endif

				cv::Mat matmulRes;
				if (mask_num <= OPTIMIZE_GAP)
				{
					matmulRes = (maskProposals1 * protos);//n*32 32*25600 A*B是以数学运算中矩阵相乘的方式实现的，要求A的列数等于B的行数时  mat.t()为矩阵转置
				}
				else
				{
					struct ThreadParamsMatmut pThreadParamsMatmut;
					pThreadParamsMatmut.m1_in = maskProposals1;
					pThreadParamsMatmut.m2_in = protos;

					bool pthreadOk = true;

#if (defined WIN32 || defined _WIN32)
					HANDLE hThread;
					hThread = CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)MatmulThreadFunctionYoloV5Seg, (LPVOID)&pThreadParamsMatmut, 0, NULL);
#else
                    mThDo.doFun(&pThreadParamsMatmut);
#endif

					cv::Mat matmulRes2 = (maskProposals2 * protos);//n*32 32*25600 A*B是以数学运算中矩阵相乘的方式实现的，要求A的列数等于B的行数时  mat.t()为矩阵转置

#ifdef  DAS_USING_PARALLEL_PTHREAD
					if (pthreadOk)
					{
  #if (defined WIN32 || defined _WIN32)
						WaitForSingleObject(hThread, INFINITE);// 等待线程退出 
						DWORD dwCode;
						GetExitCodeThread(hThread, &dwCode);
						CloseHandle(hThread);
  #else
                        mThDo.waitFinish();
  #endif
					}
#endif

					cv::Mat matmulRes1 = pThreadParamsMatmut.m3_out;

					matmulRes.push_back(matmulRes1);
					matmulRes.push_back(matmulRes2);
				}

#ifdef DEBUG_PRINT
				printf("lyc %s, line = %d\n", __FILE__, __LINE__);
#endif

				matmulRes = matmulRes.t();

				cv::Mat masks = matmulRes.reshape(mask_num, { this->segHeight, this->segWidth });

				std::vector<cv::Mat> maskChannels;
				split(masks, maskChannels);

				Mat dest_tmp = Mat::zeros(this->segHeight, this->segWidth, CV_8UC1);  // DAS_MASK_WIDTH * DAS_MASK_HEIGHT

				for (int i = 0; i < detect_result_group_size; i++)
				{
					detect_result_t* det_result = &(detect_result_group2[i]);

					int ori_x1 = det_result->box.left;
					int ori_y1 = det_result->box.top;
					int ori_x2 = det_result->box.right;
					int ori_y2 = det_result->box.bottom;

					int x1 = (ori_x1 - this->padw) * this->frameCols_div_neww;
					int y1 = (ori_y1 - this->padh) * this->frameRows_div_newh;
					int x2 = (ori_x2 - this->padw) * this->frameCols_div_neww;
					int y2 = (ori_y2 - this->padh) * this->frameRows_div_newh;

					DetObject obj;
					obj.rect = cv::Rect(x1, y1, x2 - x1, y2 - y1);
					obj.rect &= rect_img;  //求交集，防越界

					sprintf(obj.label, det_result->name);

					int labelID = det_result->class_index;
					obj.labelID = labelID;

					obj.score = det_result->prop;
					obj.isShow = true;

					det_object.push_back(obj);

					//mask
					if (i > mask_num - 1)
						continue;

					cv::Mat dest;
					cv::exp(-maskChannels[i], dest);  //sigmoid
					dest = 1.0 / (1.0 + dest);  //160*160

					cv::Rect temp_rect;
					temp_rect.width = (ori_x2 - ori_x1) * this->segWidth_div_modelWidth;
					temp_rect.height = (ori_y2 - ori_y1) * this->segHeight_div_modelHeight;
					temp_rect.x = ori_x1 * this->segWidth_div_modelWidth;
					temp_rect.y = ori_y1 * this->segHeight_div_modelHeight;

					dest = dest(temp_rect) > 0.5;

					dest_tmp(temp_rect).setTo(labelID + 1, dest);  //0是背景，全部label id需要加1
				}

#ifdef DEBUG_PRINT
				printf("lyc %s, line = %d\n", __FILE__, __LINE__);
#endif

				int resize_padw = padw * this->segWidth_div_modelWidth;
				int resize_padh = padh * this->segHeight_div_modelHeight;
				cv::Rect roi(resize_padw, resize_padh, int(this->segWidth - resize_padw * 2), int(this->segHeight - resize_padh * 2));
				dest_tmp = dest_tmp(roi);

				memcpy(mask_90p.data, dest_tmp.data, DAS_MASK_WIDTH * DAS_MASK_HEIGHT);
			}

		}
		else
		{
		    //不需要计算mask的分支
			for (int i = 0; i < detect_result_group_size; i++)
			{
				detect_result_t* det_result = &(detect_result_group2[i]);

				int ori_x1 = det_result->box.left;
				int ori_y1 = det_result->box.top;
				int ori_x2 = det_result->box.right;
				int ori_y2 = det_result->box.bottom;

				int x1 = (ori_x1 - this->padw) * this->frameCols_div_neww;
				int y1 = (ori_y1 - this->padh) * this->frameRows_div_newh;
				int x2 = (ori_x2 - this->padw) * this->frameCols_div_neww;
				int y2 = (ori_y2 - this->padh) * this->frameRows_div_newh;

				DetObject obj;
				obj.rect = cv::Rect(x1, y1, x2 - x1, y2 - y1);
				obj.rect &= rect_img;  //求交集，防越界

				sprintf(obj.label, det_result->name);

				int labelID = det_result->class_index;
				obj.labelID = labelID;

				obj.score = det_result->prop;
				obj.isShow = true;

				det_object.push_back(obj);
			}
		}

#ifdef DEBUG_PRINT
		printf("lyc %s, line = %d\n", __FILE__, __LINE__);
#endif

		int road_index = 0;
		for (int i = 0; i < objNum; i++)
		{
			std::string label = labels[i];
			if (label == "road")
				road_index = i + 1;
		}
		CalcObjIsInsideRoad(det_object, this->img_input_width, this->img_input_height, mask_90p, road_index, this->objNum);

#ifdef DAS_USING_RKNN
		//ret = rknn_outputs_release(this->ctx, this->io_num.n_output, this->outs);
#endif

#ifdef DEBUG_PRINT
		printf("lyc dasYOLOV5Seg.cpp postProcess() end\n");
#endif
	}

	void YOLOV5Seg::getResult()
	{
		//do nothing
	}

	char* YOLOV5Seg::getImgPath() { return this->imgPath; }

	float YOLOV5Seg::getClassThreshold() { return this->classThreshold; }

	char* YOLOV5Seg::getModelVersion() { return this->modelVersion; };
}
