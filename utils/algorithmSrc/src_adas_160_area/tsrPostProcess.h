#ifndef _TSR_POST_PROCESS_H_
#define _TSR_POST_PROCESS_H_


#include <iostream>
#include <opencv2/opencv.hpp>
#include <string>
#include <opencv2/dnn.hpp>
#include <future>

#include "tsrGetNum.h"
#include "tsrClassify.h"


typedef struct _RESULT
{
	cv::Rect rect;
	std::string speed_limit_str;
	int speed_limit_int;
} RESULT;


void tsr_post_process(cv::Mat frame, cv::Rect pos_roi, Classify& classify_model, RESULT& tsr_result);
int run_sat(cv::Mat in_img, Classify* classify_model, std::string &type_result, int &res_speed);
int turn_size(cv::Mat& src, src_INFO& src_info, cv::Mat &source_img);
void draw_and_show(cv::Mat back_frame, std::vector <RESULT> result);
void tsr_post_process_two_thread(cv::Mat frame, std::vector<cv::Rect> pos_roi, std::vector<Classify>& classify_nets, std::vector<RESULT>& tsr_result_group);
void tsr_post_process_three_thread(cv::Mat frame, std::vector<cv::Rect> pos_roi, std::vector<Classify>& classify_nets, std::vector<RESULT>& tsr_result_group);
std::vector<RESULT> Taskfunction(std::vector<cv::Rect> pos_roi, cv::Mat frame, Classify& classify_model);

#endif