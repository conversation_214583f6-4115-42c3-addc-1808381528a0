#ifndef _DAS_PARAM_H_
#define _DAS_PARAM_H_

#include "dasShareDefine.h"
#include "ini/ini.h"


namespace das
{

	class Param
	{
	public:
		Param();
		~Param();
		void init(char *alarm_level_ini_path, char *camera_matrix_ini_path, char *car_install_ini_path, char* cam_install_ini_path, cv::Point2i vanishing_point);
		CamOutMRV220  update_camera_out(cv::Point2i& vanishing_point);
		param_t getParam();
		CamOutMRV220 getNewValue();
	private:
		param_t param;
		CamOutMRV220 cam_out_for_220;
	};
}


#endif