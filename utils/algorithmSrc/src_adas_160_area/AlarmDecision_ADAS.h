//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/1/3.
//

#ifndef VIS_G3_SOFTWARE_ALARMDECISION_ADAS_H
#define VIS_G3_SOFTWARE_ALARMDECISION_ADAS_H

#include "DetectDataCallback.h"
#include "dasShareDefine.h"

namespace vis {
    class AlarmDecision_ADAS {
    public:
        AlarmDecision_ADAS();

        ~AlarmDecision_ADAS();

        int init(int cameraId, DetectDataCallback &detectDataCallback);

        void parseObjectInfos(das::objectInfo_t &curObjectInfo, float speed);

    private:
        DetectDataCallback *curDetectDataCallback;
        /* 相机ID */
        int curCameraId = -1;
        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;
    };

} // vis
#endif //VIS_G3_SOFTWARE_ALARMDECISION_ADAS_H
