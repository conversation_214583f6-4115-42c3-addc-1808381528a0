﻿
#include "dasYOLOV5DS.h"

#if (defined WIN32 || defined _WIN32 )
#include < Windows.h >
#include "windows.h"
#pragma warning(disable:4996)
#endif


#ifdef DAS_USING_RKNN
/*-------------------------------------------
Includes
-------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dlfcn.h>
#include <vector>
#include <sys/time.h>

//#define _BASETSD_H




//#include "drm_func.h"
//#include "rga_func.h"

//#include "postprocess.h"

#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>

#ifdef DAS_USING_RGA
#include "XuRGAUtils.h"
#endif

//#define PERF_WITH_POST 1

#endif

using namespace cv;
#ifdef DAS_USING_RKNN
#else
using namespace dnn;
#endif
using namespace std;


#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include <vector>
#include <set>
#include <stdint.h>


#ifdef DAS_USING_RGA
extern XuRGAUtils rga_utils;
#endif

namespace das
{
	int yolov5ds_anchor0[6] = { 10, 13, 16, 30, 33, 23 };
	int yolov5ds_anchor1[6] = { 30, 61, 62, 45, 59, 119 };
	int yolov5ds_anchor2[6] = { 116, 90, 156, 198, 373, 326 };

	// segmentation
	//int yolov5ds_anchor0[6] = { 11, 10, 19, 20, 33, 34 };
	//int yolov5ds_anchor1[6] = { 59, 56, 265, 29, 76, 192 };
	//int yolov5ds_anchor2[6] = { 140, 152, 521, 113, 552, 303 };


	YOLOV5DS::YOLOV5DS()
	{}

	YOLOV5DS::~YOLOV5DS()
	{
#ifdef DAS_USING_RKNN
		// release
		/*
		ret = rknn_destroy(this->ctx);
		drm_buf_destroy(&drm_ctx, drm_fd, buf_fd, handle, drm_buf, actual_size);

		drm_deinit(&drm_ctx, drm_fd);
		RGA_deinit(&rga_ctx);
		if (model_data)
			free(model_data);

		if (resize_buf)
			free(resize_buf);
		*/
#endif
	}

	void YOLOV5DS::infer(cv::Mat frame, std::vector<cv::Point> no_detection_area1, std::vector<cv::Point> no_detection_area2)
	{
		//降低阈值在postProces中做
		cv::Mat dstimg = letterbox(frame, model_height, model_width, keep_ratio, this->newh, this->neww, this->padh, this->padw);

		//cv::imshow("show", dstimg);
		//cv::waitKey(0);

		cv::Scalar color(255, 255, 0);
		if (no_detection_area1.size() > 0)
			fillNoDetectionArea(dstimg, no_detection_area1, frame.cols, frame.rows, newh, neww, padh, padw, color);
		if (no_detection_area2.size() > 0)
			fillNoDetectionArea(dstimg, no_detection_area2, frame.cols, frame.rows, newh, neww, padh, padw, color);

		//cv::imshow("show", dstimg);
		//cv::waitKey(0);

#ifdef DAS_USING_RKNN
		int ret;

		// preprocess image
#ifdef DAS_USING_RGA    
		rga_utils.imageTransformation(dstimg.cols, dstimg.rows, rga_utils.IMG_TYPE_BGR888, dstimg.data, dstimg.cols, dstimg.rows, rga_utils.IMG_TYPE_RGB888, dstimg.data);
#else
		cvtColor(dstimg, dstimg, COLOR_BGR2RGB);  //#include <opencv2/opencv.hpp>
#endif

		rknn_input inputs[1];
		memset(inputs, 0, sizeof(inputs));
		inputs[0].index = 0;
		inputs[0].type = RKNN_TENSOR_UINT8;
		inputs[0].size = model_width * model_height * model_channel;
		inputs[0].fmt = RKNN_TENSOR_NHWC;
		inputs[0].pass_through = 0;

		
		//imwrite("./check.bmp", dstimg);
                //dstimg = imread("./check.bmp");
		inputs[0].buf = dstimg.data;

		rknn_inputs_set(this->ctx, this->io_num.n_input, inputs);

		memset(this->outs, 0, sizeof(this->outs));
		for (int i = 0; i < this->io_num.n_output; i++)
			this->outs[i].want_float = 1;

		ret = rknn_run(this->ctx, NULL);
		ret = rknn_outputs_get(this->ctx, this->io_num.n_output, this->outs, NULL);

		//imwrite("./out.bmp", frame);
#else
		normalize(dstimg, this->mean, this->std);
		cv::Mat blob = blobFromImage(dstimg);
		this->net.setInput(blob);
		this->net.forward(this->outs, this->net.getUnconnectedOutLayersNames());
#endif

		//std::cout << outs[0].data << endl;
	}


	struct ThreadParamsMatmut
	{
		cv::Mat m1_in;
		cv::Mat m2_in;
		cv::Mat m3_out;
	};


	void YOLOV5DS::init(char* iniPath, int img_input_width, int img_input_height, int alg_mode)
	{
		sprintf(this->modelVersion, "%s", "0.0.0");

		int ret;
		const bool keep_ratio = true;


		int netWidth;
		int netHeight;
		int objNum;
		float boxThreshold;
		float classThreshold;
		float nmsThreshold;
		float mean[3];
		float std[3];

		char modelpath[200];
		char labelPath[200];
		char imgPath[200];

		iniStart(iniPath);

		iniGetInt("net", "netWidth", &netWidth);
		iniGetInt("net", "netHeight", &netHeight);
		iniGetInt("net", "objNum", &objNum);
		iniGetFloat("net", "boxThreshold", &boxThreshold);
		iniGetFloat("net", "classThreshold", &classThreshold);
		iniGetFloat("net", "nmsThreshold", &nmsThreshold);
		iniGetFloat("net", "mean0", &mean[0]);
		iniGetFloat("net", "mean1", &mean[1]);
		iniGetFloat("net", "mean2", &mean[2]);
		iniGetFloat("net", "std0", &std[0]);
		iniGetFloat("net", "std1", &std[1]);
		iniGetFloat("net", "std2", &std[2]);

		iniGetString("path", "modelPath", modelpath);
		iniGetString("path", "labelPath", labelPath);
		iniGetString("path", "imgPath", imgPath);


		this->img_input_width = img_input_width;
		this->img_input_height = img_input_height;

		this->model_width = netWidth;
		this->model_height = netHeight;
		this->model_channel = 3;

		this->objNum = objNum;

		this->prop_box_size = 5 + objNum;

		this->classThreshold = classThreshold;
		this->nmsThreshold = nmsThreshold;
		this->boxThreshold = boxThreshold;

		this->keep_ratio = keep_ratio;



#if (defined WIN32 || defined _WIN32 )
#else
        /* 去掉可能的出现的/r */
        if(modelpath[strlen(modelpath) - 1] == 0x0D){
            modelpath[strlen(modelpath) - 1] = '\0';
        }
        if(labelPath[strlen(labelPath) - 1] == 0x0D){
            labelPath[strlen(labelPath) - 1] = '\0';
        }
        if(imgPath[strlen(imgPath) - 1] == 0x0D){
            imgPath[strlen(imgPath) - 1] = '\0';
        }

#endif

		labels = (char**)malloc(objNum * DAS_OBJ_NAME_MAX_SIZE * sizeof(char));
		ret = loadLabelName(labelPath, labels, objNum);

		strcpy(this->imgPath, imgPath);

		for (int i = 0; i < 3; i++)
		{
			this->mean[i] = mean[i];
			this->std[i] = std[i];
		}

		newh = netHeight;
		neww = netWidth;
		padw = 0;
		padh = 0;


		this->model_wh_len = this->model_width * this->model_height;
		this->pMaskOutput = (unsigned char*)malloc(this->model_wh_len * sizeof(unsigned char));
		this->data_compare = (float*)malloc(this->model_wh_len * sizeof(float));


		Mat dstimg;
		if (keep_ratio && (this->img_input_height != this->img_input_width))
		{
			float hw_scale = (float)this->img_input_height / this->img_input_width;
			if (hw_scale > 1)
			{
				newh = netHeight;
				neww = int(netWidth / hw_scale);
				padw = int((netWidth - neww) * 0.5);
			}
			else
			{
				newh = (int)netWidth * hw_scale;
				neww = netWidth;
				padh = (int)(netHeight - newh) * 0.5;
			}
		}

		this->frameCols_div_neww = (float)this->img_input_width / this->neww;
		this->frameRows_div_newh = (float)this->img_input_height / this->newh;


		if (alg_mode == YOLOV5DS_ADAS_160_SBST_MODE)
		{
			this->seg_nc = 3;  //n + 1，算上背景类
		}
		else if (alg_mode == YOLOV5DS_ADAS_60_MODE || alg_mode == YOLOV5DS_ADAS_160_MODE)
		{
			this->seg_nc = 2;  //n + 1，算上背景类
		}
		else
		{
			this->seg_nc = -1;
			printf("alg mode error!!\n");
		}

		for (int t = 0; t < this->seg_nc; t++)
			this->mask_bw_360p.push_back(Mat::zeros(this->newh, this->neww, CV_8UC1));

		this->lane_prob_360p = Mat::zeros(this->newh, this->neww, CV_32F);


#ifdef DAS_USING_RKNN
		/* Create the neural network */
		printf("Loading mode...\n");
		int model_data_size = 0;
		unsigned char* model_data;


		int len = strlen(modelpath);
		if (modelpath[len - 4] == 'r' && modelpath[len - 3] == 'k' && modelpath[len - 2] == 'n' && modelpath[len - 1] == 'n')
		{
			model_data = load_model(modelpath, &model_data_size);
			printf("the model format is rknn\n");
		}
		else if (modelpath[len - 3] == 'v' && modelpath[len - 2] == 'i' && modelpath[len - 1] == 's')
		{
			char key1 = '0';
			char key2 = '0';
			char key3 = '0';
			int vis_model_data_size = 0;
			unsigned char* vis_model_data = load_model(modelpath, &vis_model_data_size);
			model_data = decode(vis_model_data, vis_model_data_size, &model_data_size, &key1, &key2, &key3, false, NULL);

			sprintf(this->modelVersion, "%c.%c.%c", key1, key2, key3);   //get version
		}
		else
		{
			printf("Unrecognized model format\n");
			return;
		}

		ret = rknn_init(&this->ctx, model_data, model_data_size, 0);
		if (ret < 0)
		{
			printf("rknn_init error ret=%d\n", ret);
			return;
		}

		rknn_sdk_version version;
		ret = rknn_query(this->ctx, RKNN_QUERY_SDK_VERSION, &version, sizeof(rknn_sdk_version));
		if (ret < 0)
		{
			printf("rknn_init error ret=%d\n", ret);
			return;
		}
		printf("sdk version: %s driver version: %s\n", version.api_version, version.drv_version);

		ret = rknn_query(this->ctx, RKNN_QUERY_IN_OUT_NUM, &this->io_num, sizeof(this->io_num));
		if (ret < 0)
		{
			printf("rknn_init error ret=%d\n", ret);
			return;
		}
		printf("model input num: %d, output num: %d\n", this->io_num.n_input, this->io_num.n_output);

		rknn_tensor_attr input_attrs[this->io_num.n_input];
		memset(input_attrs, 0, sizeof(input_attrs));
		for (int i = 0; i < this->io_num.n_input; i++)
		{
			input_attrs[i].index = i;
			ret = rknn_query(this->ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
			if (ret < 0)
			{
				printf("rknn_init error ret=%d\n", ret);
				return;
			}
			dump_tensor_attr(&(input_attrs[i]));
		}

		memset(this->output_attrs, 0, sizeof(this->output_attrs));
		for (int i = 0; i < this->io_num.n_output; i++)
		{
			this->output_attrs[i].index = i;
			ret = rknn_query(this->ctx, RKNN_QUERY_OUTPUT_ATTR, &(this->output_attrs[i]), sizeof(rknn_tensor_attr));
			dump_tensor_attr(&(this->output_attrs[i]));
			if (this->output_attrs[i].qnt_type != RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC || this->output_attrs[i].type != RKNN_TENSOR_UINT8)
			{
				fprintf(stderr, "The Demo required for a Affine asymmetric u8 quantized rknn model, but output quant type is %s, output data type is %s\n", get_qnt_type_string(this->output_attrs[i].qnt_type), get_type_string(this->output_attrs[i].type));
				return;
			}
		}

		for (int i = 0; i < this->io_num.n_output; ++i)
		{
			this->out_scales.push_back(this->output_attrs[i].scale);
			this->out_zps.push_back(this->output_attrs[i].zp);
		}

		this->model_channel = 3;

		if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
		{
			printf("model is NCHW input fmt\n");
			this->model_width = input_attrs[0].dims[0];
			this->model_height = input_attrs[0].dims[1];
		}
		else
		{
			printf("model is NHWC input fmt\n");
			this->model_width = input_attrs[0].dims[1];
			this->model_height = input_attrs[0].dims[2];
		}

		printf("model input model_height=%d, model_width=%d, model_channel=%d\n", this->model_height, this->model_width, this->model_channel);
#else
		this->net = readNet(modelpath);
#endif
	}


	void CalcCurves(cv::Mat bw_360p, std::vector <curve_t>& curves, int horizontal_line, cv::Mat lane_prob_360p, int alg_mode)
	{
		//threshold(bw_360p, bw_360p, 50, 255, THRESH_BINARY);
		
		//imshow("img_ll_mask_720p", img_ll_mask_720p);
		//waitKey(0);

		Mat comp_out;
		int number = connectedComponents(bw_360p, comp_out, 8, CV_16U);  //统计图像中连通域的个数
		std::vector< std::vector<cv::Point> > curves_origin(number);

		const float resize_ratio = 2;   //  将点从360p图像切换到720p图像的缩放比例，720 ÷ 360 = 2

		std::vector< std::vector<cv::Point> > curves_origin_right(number);
		std::vector< std::vector<cv::Point> > curves_origin_left(number);

		std::vector<int> right_idx(number, 0);
		std::vector<int> left_idx(number, 9999);

		std::vector<float> score_array(number, 0.0f);  //用于统计每条车道线的置信度
		std::vector<float> sum_score(number, 0.0f);
		std::vector<int> sum_cnt(number, 0);

		std::vector<float> score_filter;

		for (int row = 0; row < comp_out.rows; row++)
		{		
			for (int k = 0; k < number; k++)
			{
				right_idx[k] = 0;
				left_idx[k] = 9999;
			}

			for (int col = 0; col < comp_out.cols; col++)
			{
				int label = comp_out.at<uint16_t>(row, col);
				if (label == 0)  //值为0是背景的黑色
					continue;

				sum_score[label] += lane_prob_360p.at<float>(row, col);
				sum_cnt[label]++;

				if (col > right_idx[label])
					right_idx[label] = col;

				if (col < left_idx[label])
					left_idx[label] = col;
			}

			for (int k = 0; k < number; k++)
			{
				if (right_idx[k] != 0)
				{
					cv::Point pt(right_idx[k] * resize_ratio, row * resize_ratio);   //  将点从360p图像切换到720p图像，输出的车道线函数以1280*720为基准 ！！！
					curves_origin_right[k].push_back(pt);
				}
				if (left_idx[k] != 9999)
				{
					cv::Point pt(left_idx[k] * resize_ratio, row * resize_ratio);   //  将点从360p图像切换到720p图像，输出的车道线函数以1280*720为基准 ！！！
					curves_origin_left[k].push_back(pt);
				}
			}			
		}

		for (int k = 0; k < number; k++)
		{
			if (sum_cnt[k] > 0)
				score_array[k] = sum_score[k] / sum_cnt[k];
		}

		std::vector< std::vector<cv::Point> > curves_filter;
		const int points_size_th = 10;
		for (int t = 0; t < number; t++)
		{
			int sum_val = 0;
			for (int p = 0; p < curves_origin_left[t].size(); p++)
				sum_val += curves_origin_left[t][p].x;
			for (int p = 0; p < curves_origin_right[t].size(); p++)
				sum_val += curves_origin_right[t][p].x;

			int mean_x = sum_val / (float)(curves_origin_left[t].size() + curves_origin_right[t].size());

			if (mean_x < 1280 * 0.5)
				curves_origin[t] = curves_origin_right[t];
			else
				curves_origin[t] = curves_origin_left[t];

			int points_size = curves_origin[t].size();
			if (points_size < points_size_th)
				continue;

			curves_filter.push_back(curves_origin[t]);

			score_filter.push_back(score_array[t]);
		}


		for (int t = 0; t < curves_filter.size(); t++)
		{
			const int n = 3;
			cv::Mat A;
			FitPolynomialCurve(curves_filter[t], n, A);

			int min_y = 99999, max_y = 0;
			cv::Point max_y_pt, min_y_pt;
			for (int n = 0; n < curves_filter[t].size(); n++)
			{
				cv::Point pt = curves_filter[t][n];
				if (pt.y > max_y)
				{
					max_y = pt.y;
					max_y_pt = pt;
				}
				if (pt.y < min_y)
				{
					min_y = pt.y;
					min_y_pt = pt;
				}
			}

			curve_t cu;
			cu.A0 = A.at<double>(0, 0);
			cu.A1 = A.at<double>(1, 0);
			cu.A2 = A.at<double>(2, 0);
			cu.A3 = A.at<double>(3, 0);
			cu.AX = max_y_pt.x;
			cu.AY = max_y_pt.y;
			cu.BX = min_y_pt.x;
			cu.BY = min_y_pt.y;
			cu.confidence = score_filter[t];
			//printf("cu.confidence = %f\n", cu.confidence);

			//注：目前对虚实线的判断只使用单一帧的置信度，很粗糙，不太可靠，后续需要对车道线进行跟踪，使得车道线有ID，然后对历史置信度进行统计
			//    目前考虑虚实线判断未有明确需求，且需要一些运算量，且未打通到客户端与L4，所以暂时未精细去做，但验证过使用车道线置信度来判断是可行的
		/*	if (cu.confidence < 3.0)  
				sprintf(cu.label, "dash");
			else
				sprintf(cu.label, "solid");*/


			if (MAX(cu.AY, cu.BY) < horizontal_line)
				continue;
			if (abs(cu.AY - cu.BY) <= 30)
				continue;
	

			if (alg_mode == YOLOV5DS_ADAS_60_MODE)
			{
				if (cu.confidence < 0.3)
					continue;
			}
			else if (alg_mode == YOLOV5DS_ADAS_160_MODE)
			{
				if (cu.confidence < 0.2)
					continue;
			}
			else if (alg_mode == YOLOV5DS_ADAS_160_SBST_MODE)
			{
				if (cu.confidence < 0.2)
					continue;
			}
			else
			{
				printf("alg mode is error!\n");
			}

			//printf("cu.confidence2= %f\n", cu.confidence);

			curves.push_back(cu);
		}
	}



#ifdef DAS_USING_RKNN
	void YOLOV5DS::postProcess(float** input_outs, std::vector<DetObject>& det_object, cv::Mat& mask_90p, std::vector <curve_t>& curves, int horizontal_line, int alg_mode)
#else
	void YOLOV5DS::postProcess(std::vector<cv::Mat> input_outs, std::vector<DetObject>& det_object, cv::Mat& mask_90p, std::vector <curve_t>& curves, int horizontal_line, int alg_mode)
#endif
	{

#ifdef DEBUG_PRINT
		printf("lyc dasYOLOV5Seg.cpp postProcess() start\n");
#endif

		float nms_threshold = this->nmsThreshold;
		float class_threshold = this->classThreshold;

#ifdef DAS_USING_TRACK
		class_threshold *= 0.5;
#endif

		std::vector<detect_result_t> detect_result_group;

#ifdef DAS_USING_PARALLEL_PTHREAD
#ifdef DAS_USING_RKNN
		post_process_fp((float*)input_outs[0], (float*)input_outs[1], (float*)input_outs[2], yolov5ds_anchor0, yolov5ds_anchor1, yolov5ds_anchor2, this->labels, this->objNum, this->prop_box_size, this->model_height, this->model_width, 0, 0, 1.0f, class_threshold, nms_threshold, detect_result_group);
#else
		post_process_fp((float*)input_outs[0].data, (float*)input_outs[1].data, (float*)input_outs[2].data, yolov5ds_anchor0, yolov5ds_anchor1, yolov5ds_anchor2, this->labels, this->objNum, this->prop_box_size, this->model_height, this->model_width,0, 0, 1.0f, class_threshold, nms_threshold, detect_result_group);
#endif
#else
#ifdef DAS_USING_RKNN
		post_process_fp((float*)this->outs[0].buf, (float*)this->outs[1].buf, (float*)this->outs[2].buf, yolov5ds_anchor0, yolov5ds_anchor1, yolov5ds_anchor2, this->labels, this->objNum, this->prop_box_size, this->model_height, this->model_width, 0, 0, 1.0f, class_threshold, nms_threshold, detect_result_group);
#else
		post_process_fp((float*)this->outs[0].data, (float*)this->outs[1].data, (float*)this->outs[2].data, yolov5ds_anchor0, yolov5ds_anchor1, yolov5ds_anchor2, this->labels, this->objNum, this->prop_box_size, this->model_height, this->model_width, 0, 0, 1.0f, class_threshold, nms_threshold, detect_result_group);
#endif
#endif

		Rect rect_img(0, 0, this->img_input_width, this->img_input_height);  //防越界，标准图像

#ifdef DEBUG_PRINT
		printf("lyc %s, line = %d\n", __FILE__, __LINE__);
#endif


		/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

		// get mask here

#ifdef DAS_USING_PARALLEL_PTHREAD
  #ifdef DAS_USING_RKNN
        float* pMask = (float*)input_outs[3];
  #else
		float* pMask = (float*)input_outs[3].data;
  #endif
#else
  #ifdef DAS_USING_RKNN
        float* pMask = (float*)outs[3].buf;
  #else
		float* pMask = (float*)outs[3].data;
  #endif
#endif

		const int lane_index = 1;
		const int road_index = 2;


		//获取pMaskOutput，比较每个图层，值最大的图层索引作为该点的类别归属
		if (this->seg_nc >= 2)
		{
			int model_wh_len = this->model_wh_len;

			int cnt = 0;
			float* data_compare_st = this->data_compare;
			while (cnt++ < model_wh_len)
				*data_compare_st++ = 0.0f;

			memset(pMaskOutput, 0, model_wh_len);

			for (unsigned char t = 0; t < this->seg_nc; t++)
			{
				float* A_st = pMask + t * model_wh_len;
				float* data_compare_st = this->data_compare;
				unsigned char* pMaskOutput_st = this->pMaskOutput;

				cnt = 0;
				while (cnt < model_wh_len)
				{
					if (*A_st > *data_compare_st)
					{
						*pMaskOutput_st = t;
						*data_compare_st = *A_st;
					}
					A_st++;
					data_compare_st++;
					pMaskOutput_st++;
					cnt++;
				}
			}
		}

		//写入mask_90图像    此图像用于给客户端显示，60度ADAS暂时不用
		float resize_ratio = (float)mask_90p.rows / this->newh;

		//this->mask_bw_360p[lane_index].zeros(this->newh, this->neww, CV_8UC1);
		memset(this->mask_bw_360p[lane_index].data, 0, this->newh * this->neww);

		//separate the mask to server binary image and resize
		for (int i = this->padh; i < this->model_height - this->padh; i++)
		{
			for (int j = this->padw; j < this->model_width - this->padw; j++)
			{
				int val = pMaskOutput[i * this->model_width + j];

				if (alg_mode == YOLOV5DS_ADAS_160_SBST_MODE)
				    mask_90p.at<uchar>((i - this->padh) * resize_ratio, (j - this->padw) * resize_ratio) = val;

				if (val == lane_index)
				{
					this->mask_bw_360p[val].at<uchar>(i - this->padh, j - this->padw) = 255;
					this->lane_prob_360p.at<float>(i - this->padh, j - this->padw) = data_compare[i * this->model_width + j];
				}
			}
		}

		//show the binary image
    #if ( defined WIN32 || defined _WIN32 )
		imshow("lane", mask_bw_360p[1]);
		/*
	    for (int t = 0; t < this->seg_nc; t++)
	    {
		    char win_name[50];
		    sprintf(win_name, "mask debug %d", t);
		    imshow(win_name, this->mask_bw_360p[t]);
	    }
		*/
    #endif

	    //calc the lane curves
		CalcCurves(this->mask_bw_360p[lane_index], curves, horizontal_line, this->lane_prob_360p, alg_mode);  //只计算与车道线相关的图
			
		/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

		int detect_result_group_size = detect_result_group.size();

		for (int i = 0; i < detect_result_group_size; i++)
		{
			detect_result_t* det_result = &(detect_result_group[i]);

			int ori_x1 = det_result->box.left;
			int ori_y1 = det_result->box.top;
			int ori_x2 = det_result->box.right;
			int ori_y2 = det_result->box.bottom;

			int x1 = (ori_x1 - this->padw) * this->frameCols_div_neww;
			int y1 = (ori_y1 - this->padh) * this->frameRows_div_newh;
			int x2 = (ori_x2 - this->padw) * this->frameCols_div_neww;
			int y2 = (ori_y2 - this->padh) * this->frameRows_div_newh;

			DetObject obj;
			obj.rect = cv::Rect(x1, y1, x2 - x1, y2 - y1);
			obj.rect &= rect_img;  //求交集，防越界

			sprintf(obj.label, det_result->name);

			int labelID = det_result->class_index;
			obj.labelID = labelID;

			obj.score = det_result->prop;
			obj.isShow = true;

			if (0 == strcmp("sign", obj.label))
			{ // sign do nothing 
			}
			else
			{
				if (y2 < horizontal_line - 15)
					continue;
			}

			det_object.push_back(obj);
		}

#ifdef DEBUG_PRINT
		printf("lyc %s, line = %d\n", __FILE__, __LINE__);
#endif

		if (alg_mode == YOLOV5DS_ADAS_160_SBST_MODE)
		    CalcObjIsInsideRoad(det_object, this->img_input_width, this->img_input_height, mask_90p, road_index, this->objNum);

#ifdef DAS_USING_RKNN
		//ret = rknn_outputs_release(this->ctx, this->io_num.n_output, this->outs);
#endif

#ifdef DEBUG_PRINT
		printf("lyc dasYOLOV5Seg.cpp postProcess() end\n");
#endif
	}

	void YOLOV5DS::getResult()
	{
		//do nothing
	}

	char* YOLOV5DS::getImgPath() { return this->imgPath; }

	float YOLOV5DS::getClassThreshold() { return this->classThreshold; }

	char* YOLOV5DS::getModelVersion() { return this->modelVersion; };
}
