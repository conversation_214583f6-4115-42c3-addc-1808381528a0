#ifndef _COM_DIS
#define _COM_DIS

#include <iostream>
#include <opencv2/opencv.hpp>
#include "dasShareDefine.h"

namespace das
{
    typedef struct _camera_imformation
    {
        cv::Mat camera_ins;
        cv::Mat camera_out;
        float camera_H;
        int skyline_v;
        float angle_alpha;
        cv::Mat camera_dist;
        std::string data_path;
        std::string index_img_path;
    } camera_imformation;

typedef struct _uvpoint
{
    int x;
    int y;
} uvpoint;

typedef struct _worldpoint
{
    float x;
    float y;
    float z;
} worldpoint;

typedef struct _save_st
{
    uvpoint point;
    int effi;
} save_st;

    class ComputerDistancer
    {
    private:
        float camera_H;
        cv::Mat camera_ins;
        cv::Mat camera_out;
        float angle_alpha;
        int skyline_v;
        cv::Mat camera_dist;
        float camera_v0;
        float camera_fy;
        cv::Mat map1;
        cv::Mat map2;
        std::vector<std::vector<save_st> > destory_point;
        cv::Mat points_effectiveness = cv::Mat(720, 1280, CV_8UC1);
        int x_num, lamuda;
        cv::Mat mat_lamuda;
    public:
        ComputerDistancer(camera_imformation cam_im);
        ~ComputerDistancer();
        int get_destory_map(std::string save_data_path);
        int get_destory_point(uvpoint& point);
        int read_destory_map(const std::string file_path, const std::string effient_map_path);
        int imagepoint2worldpoint(uvpoint& point, worldpoint& world_point);
        int rect2distance(int& RectWidth, int& yDistance, param_t param, int ActualWidth);
        cv::Mat curve_lane_fit(std::vector<worldpoint>& world_points, int n, int lamuda);
        int point_destroy(uvpoint& point);
        int get_angle_alpha(const float& skyline_v, float& camera_angle_alpha);
        int lane_fit(std::vector<uvpoint>& lane_points, cv::Mat& lane_pam, int alg_mode);
        int onepixel2worldpoint_160(uvpoint& point, worldpoint& wdpoint);
        int onepixel2worldpoint_60(uvpoint& point, worldpoint& wdpoint);
    };
}
#endif
