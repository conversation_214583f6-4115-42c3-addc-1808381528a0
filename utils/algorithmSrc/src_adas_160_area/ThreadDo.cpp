//
// Created by xwf on 2023/2/20.
//

#include "ThreadDo.h"
#include <iostream>

static void* threadFun(void* arg)
{
	ThreadDo* p = static_cast<ThreadDo*>(arg);

	// 运行线程的主函数
	p->run();

	return nullptr;
}

/**
 * 线程初始化函数，初始化完毕之后自动开始线程
 * @param fun 线程要执行的计算函数
 * @return 成功返回线程的pid，失败返回0
 */
pthread_t ThreadDo::init(void* (*fun)(void*), char* thName)
{
	pthread_t pid;
	int ret;

	// 初始化成员变量
	mDo.try_lock();
	mGet.try_lock();
	mArg = nullptr;
	mFun = fun;
	mKeep = true;

	// 启动线程
	ret = pthread_create(&pid, nullptr, threadFun, this);
	if (ret == 0)  //创建线程成功
	{
		if (nullptr != thName)
		{
			pthread_setname_np(pid, thName);
		}
		// 当线程退出之后自动回收资源
		pthread_detach(pid);
	}
	else  //创建线程失败
	{
		pid = 0;
	}

	return pid;
}

/**
 * 给线程传递参数，并开始让线程计算参数
 * @param arg 要计算的参数
 */
void ThreadDo::doFun(void* arg) 
{
	// 记录下参数
	mArg = arg;
	// 告诉计算线程开始计算
	mDo.unlock();
}

/**
 * 阻塞等待线程计算完毕
 */
void ThreadDo::waitFinish() 
{
	// 阻塞至结果计算完毕
	mGet.lock();
}

/**
 * 线程运行的主函数
 */
void ThreadDo::run() 
{
	while (mKeep)
	{
		// 阻塞至有数据可以计算
		mDo.lock();
		// 计算数据
		mFun(mArg);
		// 释放获取锁
		mGet.unlock();
	}
}

/**
 * 停止线程
 */
void ThreadDo::stop() 
{
	// 停止线程
	mKeep = false;
}
