//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/1/3.
//




#include "dasShareDefine.h"


#ifdef DAS_G3_APP_IN_OUT

#include <unistd.h>
#include "Detecter_Adas.h"

#endif


#include "dasDetect.h"
#include "XuTimeUtil.h"
#include "XuString.h"



using namespace cv;
using namespace std;


namespace vis {

    int Detecter_Adas::init(int cameraId, DetectDataCallback &detectDataCallback) {
        printf("******************Detecter_Adas::init  line=%d  \n",__LINE__);
        curCameraId = cameraId;
        curDetectDataCallback = &detectDataCallback;

        alarmDecisionAdas.init(cameraId, detectDataCallback);
        printf("******************Detecter_Adas::init  line=%d  \n",__LINE__);
        return 0;
    }

    float Detecter_Adas::getVelocity() {
        return curVehicleStatus.speed;
    }



    bool Detecter_Adas::isDetectOpen() const {
        return detectOpen;
    }

    __time_t Detecter_Adas::getLastDetectTime() const {
        return lastDetectTime;
    }

    void Detecter_Adas::run() {

        std::string pthreadName = "Adas_";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        int ret = -1;

        das::Detect *detect = new das::Detect();

        const int input_width = 1280;
        const int input_height = 720;

        das::objectInfo_t object_info;

        object_info.objects.clear();

        Mat srcimg = Mat::zeros(input_height, input_width, CV_8UC3);

        std::vector<cv::Point> ignoreArea1;
        std::vector<cv::Point> ignoreArea2;
        ignoreArea1 = getIgnoreAreaPoint1();
        ignoreArea2 = getIgnoreAreaPoint2();
        detect->init_yolov5seg(input_width, input_height,ignoreArea1,ignoreArea2);
        detectOpen = true;


        uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);

        Mat yuvImg = Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);

        int frame_cnt = 0;
        /* 是否需要清理识别信息 */
        bool needclean = false;
        while (1) {
            frame_cnt++;

            double BeginTime_alg = (double) cv::getTickCount();

            ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData,curVehicleStatus,&needclean);
            if (ret != 0) {
                usleep(5 * 1000);
                continue;
            }
//            printf("get yuv data success, cameraId=%d size=%d  \n", cameraYuvData.getCameraId(),cameraYuvData.getDataLen());

            xuRgaUtils.imageTransformation(1280, 720, xuRgaUtils.IMG_TYPE_NV21, cameraYuvData, 1280,
                                           720,
                                           xuRgaUtils.IMG_TYPE_BGR888, srcimg.data);
//            yuvImg.data = cameraYuvData.getCurYuvData();
//            cvtColor(yuvImg, srcimg, COLOR_YUV2BGR_NV12);




            detect->detect_yolov5seg(srcimg, getVelocity());
            object_info.objects.clear();
            object_info.traffics.clear();
            object_info.lanes.clear();

            object_info = detect->getResult_yolov5seg();

            /* 这里交给报警决策判断一下 */
            alarmDecisionAdas.parseObjectInfos(object_info, curVehicleStatus.speed);

            lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
            double time2_alg = ((double) cv::getTickCount() - BeginTime_alg) / cv::getTickFrequency();
            int time3_alg = (int) (time2_alg * 1000 + 0.5);
            printf("$$$$$$$$$$$$$$$$$ total time %dms    cameraId:%d \n", time3_alg, curCameraId);
            printf("\n");
        }

        pthread_setname_np(pthread_self(), "Finish");

        delete detect;

        return;
    }

    std::vector<cv::Point> Detecter_Adas::getIgnoreAreaPoint1() const {
        G3_Configuration &conf = G3_Configuration::getInstance();
        std::vector<cv::Point> point;
        std::vector<VISPoint> vPoint;

        // 根据镜头id获取各自忽略的区域
        if (CAMERA_ID_1 == curCameraId) {
            vPoint = conf.getUndetectedAreaListCamera1()[0];
        } else if (CAMERA_ID_2 == curCameraId) {
            vPoint = conf.getUndetectedAreaListCamera2()[0];
        }

        // 转换格式
        for (std::size_t i = 0; i < vPoint.size(); ++i) {
            cv::Point cp;
            cp.x = vPoint[i].x;
            cp.y = vPoint[i].y;

            point.push_back(cp);
        }

        return point;
    }

    std::vector<cv::Point> Detecter_Adas::getIgnoreAreaPoint2() const {
        G3_Configuration &conf = G3_Configuration::getInstance();
        std::vector<cv::Point> point;
        std::vector<VISPoint> vPoint;

        // 根据镜头id获取各自忽略的区域
        if (CAMERA_ID_1 == curCameraId) {
            vPoint = conf.getUndetectedAreaListCamera1()[1];
        } else if (CAMERA_ID_2 == curCameraId) {
            vPoint = conf.getUndetectedAreaListCamera2()[1];
        }

        // 转换格式
        for (std::size_t i = 0; i < vPoint.size(); ++i) {
            cv::Point cp;
            cp.x = vPoint[i].x;
            cp.y = vPoint[i].y;

            point.push_back(cp);
        }

        return point;
    }
} // vis
