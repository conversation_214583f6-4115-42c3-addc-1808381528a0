#ifndef _DAS_YOLOV5SEG_H_
#define _DAS_YOLOV5SEG_H_

#include "dasShareDefine.h"
#include "dasFunction.h"
#if ( defined WIN32 || defined _WIN32 )
#else
    #include "ThreadDo.h"
#endif

#include "ini/ini.h"

#ifdef DAS_USING_RKNN
    #include "rknn_api.h"  //#include "rknn/rknn_api.h"
#endif


#include <stdint.h>



namespace das
{

	class YOLOV5Seg
	{
	public:
		YOLOV5Seg();
		~YOLOV5Seg();
		void init(char* iniPath, int img_input_width, int img_input_height);
		void infer(cv::Mat frame, std::vector<cv::Point> no_detection_area1, std::vector<cv::Point> no_detection_area2);
#ifdef DAS_USING_RKNN
		void postProcess(float** input_outs, std::vector<DetObject>& det_object, cv::Mat& mask_90p);
#else
		void postProcess(std::vector<cv::Mat> input_outs, std::vector<DetObject>& det_object, cv::Mat& mask_90p);
#endif
		void getResult();
		char* getImgPath();
		float getClassThreshold();
		char* getModelVersion();

#ifdef DAS_USING_RKNN
		rknn_context ctx;
		rknn_input_output_num io_num;
		std::vector<float> out_scales;
		std::vector<uint32_t> out_zps;
    #define RKNN_OUTPUT_SIZE 4 
		rknn_output outs[RKNN_OUTPUT_SIZE];  // rknn_output outs[io_num.n_output];
        rknn_tensor_attr output_attrs[RKNN_OUTPUT_SIZE];
#else
		cv::dnn::Net net;
		std::vector<cv::Mat> outs;
#endif

	private:
		int objNum;
		int prop_box_size;

		int img_input_width;
		int img_input_height;

		//mask
		int segChannels;
		int segWidth;
		int segHeight;

		bool keep_ratio;

		float mean[3];
		float std[3];

		float boxThreshold;
		float classThreshold;
		float nmsThreshold;

		int model_width;
		int model_height;
		int model_channel;

		char imgPath[200];

		char modelVersion[20];

		char** labels;

		int newh;
		int neww;
		int padh;
		int padw;

		float segWidth_div_modelWidth;
		float segHeight_div_modelHeight;
		float frameCols_div_neww;
		float frameRows_div_newh;

#if ( defined WIN32 || defined _WIN32 )
#else
        ThreadDo mThDo;
#endif
	};

}


#endif
