//
// Created by xwf on 2023/2/20.
//

#ifndef VIS_G3_SOFTWARE_THREADDO_H
#define VIS_G3_SOFTWARE_THREADDO_H

#include <mutex>

class ThreadDo {
public:
  pthread_t init(void *(*fun)(void *), char *thName = nullptr);
  void doFun(void *arg);
  void waitFinish();
  void run();
  void stop();
private:
  void *(*mFun) (void *) = nullptr;
  std::mutex mDo;
  std::mutex mGet;
  void *mArg = nullptr;
  bool mKeep = true;
};

#endif // VIS_G3_SOFTWARE_THREADDO_H
