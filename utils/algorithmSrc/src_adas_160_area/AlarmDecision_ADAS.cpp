//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/1/3.
//

#include "AlarmDecision_ADAS.h"

namespace vis {

    AlarmDecision_ADAS::AlarmDecision_ADAS() {

    }

    AlarmDecision_ADAS::~AlarmDecision_ADAS() {

    }

    int AlarmDecision_ADAS::init(int cameraId, DetectDataCallback &detectDataCallback) {
        curCameraId = cameraId;
        curDetectDataCallback = &detectDataCallback;

        G3_Configuration::getInstance().getCameraTypeInfoOfCamera(detectionResult.curCameraType, cameraId);
        return 0;
    }

    void AlarmDecision_ADAS::parseObjectInfos(das::objectInfo_t &curObjectInfo, float speed) {

        curDetectDataCallback->onGetObjectInfo_Adas(curObjectInfo, detectionResult);
    }


} // vis