#ifndef _DAS_TRACKER_H_
#define _DAS_TRACKER_H_


#include "dasShareDefine.h"


namespace das
{

	class Tracker
	{
	public:
		Tracker();
		~Tracker();
		void init();
		void TrackBaseInterFrame(std::vector<DetObject> &det_object, std::vector<DetObject> &det_object_last_frame, unsigned int &IDcnt, float class_threshold, int img_cols, int img_rows);
		void TrackLaneFrame(std::vector<curve_t>& curve_obj, std::vector<curve_t>& curve_obj_last_frame, unsigned int& IDcnt,int alg_mode);
		bool CalcObjIsStatic(DetObject det_object, int confirm_frame_num);

	private:
		bool isSolid = false;
		
		std::vector<LastFrameId> last_frame_ids;
	};
}


#endif