﻿
#include "dasFunction.h"

#ifdef DAS_USING_RGA
    #include "XuRGAUtils.h"
#endif

#if (defined WIN32 || defined _WIN32 )
#pragma warning(disable:4996)
#endif



using namespace cv;


#ifdef DAS_USING_RGA
	extern XuRGAUtils rga_utils;
#endif

namespace das
{

#ifdef DAS_G3_DEMO_IN_OUT

	/************************************************************************/
	/* 按照opencv数据存储格式，函数传参顺序为b、g、r                        */
	/************************************************************************/
	void rgb2yuv_pixel(uchar b, uchar g, uchar r, uchar& y, uchar& u, uchar& v)
	{
		//rgb转yuv公式，参考资料<a target=_blank href="http://www.cnblogs.com/dwdxdy/p/3713990.html">http://www.cnblogs.com/dwdxdy/p/3713990.html</a>
		//y = 0.299 * r + 0.587 * g + 0.114 * b;
		//u = -0.1687 * r - 0.3313 * g + 0.5 * b + 128;
		//v = 0.5 * r - 0.4187 * g - 0.0813 * b + 128;

		//y =   0.257*r + 0.504*g + 0.098*b + 16;
		//u = -0.148*r - 0.291*g + 0.439*b + 128;
		//v  =  0.439*r - 0.368*g - 0.071*b + 128;

		y = 0.257 * r + 0.504 * g + 0.098 * b + 16;
		u = -0.148 * r - 0.291 * g + 0.439 * b + 128;
		v = 0.439 * r - 0.368 * g - 0.071 * b + 128;
	}

	/************************************************************************/
	/* rgb24转yuv422                                                        */
	/************************************************************************/
	void rgb2yuv422_image(uchar* pRgb, uchar* pYuv, int width, int height)
	{
		//考虑到每两个rgb像素对应一个yuyv组合，因此，width应为2的倍数
		int width1 = width = width / 2 * 2;

		for (int h = 0; h < height; h++)
		{
			uchar* ptr1 = pRgb + h * width * 3;
			uchar* ptr2 = pYuv + h * width * 2;

			for (int w = 0; w < width1; w += 2)
			{
				uchar y1, u1, v1, y2, u2, v2;
				rgb2yuv_pixel(*ptr1++, *ptr1++, *ptr1++, y1, u1, v1);
				rgb2yuv_pixel(*ptr1++, *ptr1++, *ptr1++, y2, u2, v2);
				//u、v分量取平均值
				uchar u = (u1 + u2) >> 1;  //(u1 + u2) / 2
				uchar v = (v1 + v2) >> 1;  //(v1 + v2) / 2
				*ptr2++ = u;  //y1
				*ptr2++ = y1;  //u
				*ptr2++ = v;  //y2
				*ptr2++ = y2;  //v
			}
		}
	}
#endif

	float get_iou_value(Rect rect1, Rect rect2)
	{
		int xx1, yy1, xx2, yy2;

		xx1 = max(rect1.x, rect2.x);
		yy1 = max(rect1.y, rect2.y);
		xx2 = min(rect1.x + rect1.width - 1, rect2.x + rect2.width - 1);
		yy2 = min(rect1.y + rect1.height - 1, rect2.y + rect2.height - 1);

		int insection_width, insection_height;
		insection_width = max(0, xx2 - xx1 + 1);
		insection_height = max(0, yy2 - yy1 + 1);

		float insection_area, union_area, iou;
		insection_area = float(insection_width) * insection_height;
		union_area = float(rect1.width * rect1.height + rect2.width * rect2.height - insection_area);
		iou = insection_area / union_area;
		return iou;
	}

	std::pair<float, float> get_lane_slope(cv::Point2i& up_point , cv::Point2i& down_point)
	{
		float k, b;

		k = static_cast<float>(down_point.y - up_point.y) / static_cast<float>(down_point.x - up_point.x);
		b = static_cast<float>(down_point.y) - static_cast<float>(k * down_point.x);
		return std::make_pair( k, b);

	}

	int sum(int a, int b)  //白盒测试的测试程序
	{
		if (a == 0)
			return b;
		else if (b == 0)
			return a;
		else
			return (a + b);
	}

	bool cmp(BBOX& b1, BBOX& b2) {
		return b1.confidence > b2.confidence;
	}

	void nms_boxes(std::vector<cv::Rect>& boxes, std::vector<float>& confidences, float confidencesThreshold, float nmsThreshold, std::vector<int>& indices)
	{
		//非极大值抑制
		//input:  boxes: 原始检测框集合;
		//input:  confidences：原始检测框对应的置信度值集合
		//input:  confThreshold 和 nmsThreshold 分别是 检测框置信度阈值以及做nms时的阈值
		//output:  indices  经过上面两个阈值过滤后剩下的检测框的index

		BBOX bbox;
		std::vector<BBOX> bboxes;
        std::vector<bool> need_delete(boxes.size(), false);
		int i, j;
		for (i = 0; i < boxes.size(); i++)
		{
			bbox.box = boxes[i];
			bbox.confidence = confidences[i];
			bbox.index = i;
			bboxes.push_back(bbox);
		}
		sort(bboxes.begin(), bboxes.end(), cmp);

		for (i = 0; i < bboxes.size(); i++)
		{
			if (need_delete[i])
				continue;
			if (bboxes[i].confidence < confidencesThreshold)
				break;
			indices.push_back(bboxes[i].index);
			for (j = i + 1; j < bboxes.size(); j++)
			{
				if (need_delete[j])
					continue;
				float iou = get_iou_value(bboxes[i].box, bboxes[j].box);
				if (iou > nmsThreshold)
				{
					need_delete[j] = true;

				}
			}
		}
	}

	cv::Mat letterbox(cv::Mat srcimg, int inpHeight, int inpWidth, bool keep_ratio, int newh, int neww, int top, int left)
	{
		//图像缩放及添加pad至目标尺寸
		//input: srcimg 原始图像
		//input: inpHeight及inpWidth 设定的输入神经网络的尺寸
		//input: keep_ratio 若保持原始图像的长宽比，则在缩放后添加pad进行填充
		//output：newh及neww 原始图像等比缩放后的宽高，不算pad
		//output：top及left 原始图像等比缩放后，pad之后的偏移位置
		//return: 缩放及pad后的图像

		int srch = srcimg.rows, srcw = srcimg.cols;
			
		Mat dstimg;
		if (keep_ratio && srch != srcw)
		{
			float hw_scale = (float)srch / srcw;
			if (hw_scale > 1)
			{			
#ifdef DAS_USING_RGA
				dstimg.create(newh, neww, CV_8UC3);
				rga_utils.imageTransformation(srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data, neww, newh, rga_utils.IMG_TYPE_BGR888, dstimg.data);
#else
				resize(srcimg, dstimg, Size(neww, newh), INTER_AREA);
#endif
				
				copyMakeBorder(dstimg, dstimg, 0, 0, left, inpWidth - neww - left, BORDER_CONSTANT, 0);
			}
			else
			{
#ifdef DAS_USING_RGA
				dstimg.create(newh, neww, CV_8UC3);
				rga_utils.imageTransformation(srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data, neww, newh, rga_utils.IMG_TYPE_BGR888, dstimg.data);
#else
				resize(srcimg, dstimg, Size(neww, newh), INTER_AREA);
#endif
				
				copyMakeBorder(dstimg, dstimg, top, inpHeight - newh - top, 0, 0, BORDER_CONSTANT, 0);
			}
		}
		else
		{
#ifdef DAS_USING_RGA
			dstimg.create(newh, neww, CV_8UC3);
			rga_utils.imageTransformation(srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data, neww, newh, rga_utils.IMG_TYPE_BGR888, dstimg.data);
#else
			resize(srcimg, dstimg, Size(neww, newh), INTER_AREA);
#endif
		}

		//imwrite("out.jpg", dstimg);

		return dstimg;
	}

	void normalize(cv::Mat& img, float mean[3], float std[3])
	{
		img.convertTo(img, CV_32F);
		int i = 0, j = 0;
		const float scale = 1.0 / 255.0;
		for (i = 0; i < img.rows; i++)
		{
			float* pdata = (float*)(img.data + i * img.step);
			for (j = 0; j < img.cols; j++)
			{
				//printf("origin %f %f %f  ", pdata[0], pdata[1], pdata[2]);

				pdata[0] = (pdata[0] * scale - mean[0]) / std[0];
				pdata[1] = (pdata[1] * scale - mean[1]) / std[1];
				pdata[2] = (pdata[2] * scale - mean[2]) / std[2];

				//printf("%f %f %f\n", pdata[0], pdata[1], pdata[2]);

				pdata += 3;
			}
		}
	}

	void LinearRegression(int* Array, int Array_size, float& k, float& b)
	{
		//最小二乘回归，输入：Array, Array_size, 输出：k，b
		//x表示0,1,2....Array_size，y表示Array数组内的数据

		int sum_x = 0, sum_y = 0, sum_xy = 0, sum_xx = 0;
		for (int i = 0; i < Array_size; i++)
		{
			int x = i;
			int y = Array[i];
			sum_x += x;
			sum_y += y;
			sum_xy += x * y;
			sum_xx += x * x;
		}

		k = (float)(Array_size * sum_xy - sum_x * sum_y) / (Array_size * sum_xx - sum_x * sum_x);
		b = (float)(sum_xx * sum_y - sum_x * sum_xy) / (Array_size * sum_xx - sum_x * sum_x);
	}

	void drawInfo(Param* pParam, Mat& frame, ObjAlarm* pObjAlarm)
	{
		cv::Scalar black(0, 0, 0);
		cv::Scalar red(0, 0, 255);
		cv::Scalar orange(0, 128, 255);
		cv::Scalar blue(255, 0, 0);
		cv::Scalar green(0, 255, 0);
		cv::Scalar yellow(0, 255, 255);

		param_t pm = pParam->getParam();
		 
		AREA default_ahead_area = pObjAlarm->getDefaultAheadArea();

		int horizontal_line = default_ahead_area.horizontal_line;
		int vertical_line = default_ahead_area.vertical_line;
		//std::cout << "test horizontal_line:" << horizontal_line << std::endl;

		//画天地线
		cv::line(frame, cv::Point(10, horizontal_line), cv::Point(frame.cols - 10, horizontal_line), red, 2);

		//画垂直线
		cv::line(frame, cv::Point(vertical_line, horizontal_line - 20), cv::Point(vertical_line, horizontal_line + 20), red, 2);
		
	}

	void drawPred(std::vector<object_t> objects, Mat& frame)   // Draw the predicted bounding box
	{
		bool isShowLabel = true;
		bool isShowID = true;
		bool isShowDistance = true;
		bool isShowHeadway = true;
		bool isShowTTC = true;

		cv::Scalar black(0, 0, 0);
		cv::Scalar red(0, 0, 255);
		cv::Scalar orange(0, 128, 255);
		cv::Scalar blue(255, 0, 0);
		cv::Scalar green(0, 255, 0);
		cv::Scalar yellow(0, 255, 255);


		for (int i = 0; i < objects.size(); i++)
		{
            std::string label = objects[i].label;
			int left = objects[i].mLeft;
			int top = objects[i].mTop;
			int right = objects[i].mRight;
			int bottom = objects[i].mBottom;
			float conf = objects[i].mScore;
			bool isAheadObj = objects[i].mIsAheadObj;

			cv::Scalar color = black;
			/*
			if (label == "person" || label == "rider")
				color = green;
			if (label == "car" || label == "bus" || label == "truck" || label == "train")
				color = red;
			if (label == "tl_green" || label == "tl_red" || label == "tl_yellow" || label == "tl_none")
				color = yellow;
			if (label == "traffic sign")
				color = blue;
			if (label == "bike" || label == "motor")
				color = black;
			*/

			if (label == "person" || label == "rider" || label == "cyclist" || label == "motorcyclist")
				color = red;
			if (label == "car")
				color = green;
			if (label == "bus")
				color = yellow;
			if (label == "truck")
				color = blue;

			/******/
			/*
			if (label == "person")
				color = red;
			if (label == "cyclist")
				color = orange;
			if (label == "motorcyclist")
				color = orange;
			if (label == "car")
				color = green;
			if (label == "bus")
				color = yellow;
			if (label == "truck")
				color = blue;
			*/
			/******/

			/*
			if (objects[i].mId == 2)
			{
				printf("!!!!!!!!!!!!!!!!!!!!!! dis = %.2f, bottom = %d, width = %d, height = %d\n", 
					objects[i].mYDistance * 0.001, objects[i].mBottom, objects[i].mRight - objects[i].mLeft, objects[i].mBottom - objects[i].mTop);
			}
			*/

			//Draw a rectangle displaying the bounding box
			if (isAheadObj)
				rectangle(frame, Point(left, top), Point(right, bottom), orange, 2);
			else
			    rectangle(frame, Point(left, top), Point(right, bottom), color, 2);

			if (isShowLabel)
			{
				//Get the label for the class name and its confidence
				std::string label_score = format("%.2f", conf);
				std::string label_display = label + ":" + label_score;

				//Display the label at the top of the bounding box
				int baseLine;
				Size labelSize = getTextSize(label_display, FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);
				top = max(top, labelSize.height);
				//rectangle(frame, Point(left, top - int(1.5 * labelSize.height)), Point(left + int(1.5 * labelSize.width), top + baseLine), Scalar(0, 255, 0), FILLED);
				putText(frame, label_display, Point(left, top - 5), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);
			}

			//Display the ID
			if (isShowID)
			{
				std::string id = format("%d", objects[i].mId);
				std::string ID_display = "ID:" + id;
				putText(frame, ID_display, Point(left + 3, top + 20), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);
			}

			//Display distance
			if (isShowDistance)
			{
				std::string distance = format("%d", (int)(objects[i].mYDistance * 0.001));  // mm -> m
				std::string distance_display = "D:" + distance;
				putText(frame, distance_display, Point(left + 3, bottom + 20), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);
			}

			//Display headway
			if (isShowHeadway)
			{
				std::string headway = format("%.1f", objects[i].mHeadway);  
				std::string headway_display = "HW:" + headway;
				putText(frame, headway_display, Point(left + 3, bottom + 40), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);
			}

			//Display ttc
			if (isShowTTC)
			{
				std::string ttc = format("%.1f", objects[i].mTTC);
				std::string ttc_display = "TTC:" + ttc;
				putText(frame, ttc_display, Point(left + 3, bottom + 60), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);
			}
		}
	}

	void drawMask(cv::Mat mask, cv::Mat& frame)
	{
		//将图1与图2线性混合
		//addWeighted(src1, 0.5, src2, 0.7, 3, dst);
		/*注释
		参数分别为：图1，图1的权重，图2，图2的权重，权重和添加的值为3，输出图片dst
		*/

		//cv::Mat tmp_mask = frame.clone();

		Vec3b a;
		std::vector <Vec3b> color;
		a[0] = 255, a[1] = 255, a[2] = 255;
		color.push_back(a);
		a[0] = 0, a[1] = 255, a[2] = 0;
		color.push_back(a);
		a[0] = 255, a[1] = 0, a[2] = 0;
		color.push_back(a);
		a[0] = 0, a[1] = 0, a[2] = 255;
		color.push_back(a);
		a[0] = 255, a[1] = 255, a[2] = 0;
		color.push_back(a);
		a[0] = 255, a[1] = 0, a[2] = 255;
		color.push_back(a);
		a[0] = 0, a[1] = 255, a[2] = 255;
		color.push_back(a);
		a[0] = 128, a[1] = 128, a[2] = 0;
		color.push_back(a);
		a[0] = 128, a[1] = 0, a[2] = 128;
		color.push_back(a);
		a[0] = 0, a[1] = 128, a[2] = 128;
		color.push_back(a);



		cv::Mat tmp_mask_small;
		cv::resize(frame, tmp_mask_small, mask.size());

		for (int i = 0; i < mask.rows; i++)
		{
			for (int j = 0; j < mask.cols; j++)
			{
				int val = mask.at<uchar>(i, j);

				if (val <= 0)
					continue;

				if (val > color.size())
					tmp_mask_small.at<Vec3b>(i, j) = color[0];
				else
					tmp_mask_small.at<Vec3b>(i, j) = color[val];
			}
		}

		cv::Mat tmp_mask_big;
		cv::resize(tmp_mask_small, tmp_mask_big, frame.size(), INTER_CUBIC);

		addWeighted(frame, 0.6, tmp_mask_big, 0.4, 3, frame);
	}

	void drawLanes(objectInfo_t object_info, cv::Mat& frame,int ID_record, int alg_mode)
	{
		bool isShowDistance = true;
		bool isShow_laneID = true;

		cv::Scalar black(0, 0, 0);
		cv::Scalar red(0, 0, 255);
		cv::Scalar orange(0, 128, 255);
		cv::Scalar blue(255, 0, 0);
		cv::Scalar green(0, 255, 0);
		cv::Scalar yellow(0, 255, 255);

		const int thickness = 3;

		for (int i = 0; i < object_info.lanes.size(); i++)
		{
			double A0 = object_info.lanes[i].mA0;
			double A1 = object_info.lanes[i].mA1;
			double A2 = object_info.lanes[i].mA2;
			double A3 = object_info.lanes[i].mA3;
			int AX = object_info.lanes[i].mEndpointAX;
			int AY = object_info.lanes[i].mEndpointAY;
			int BX = object_info.lanes[i].mEndpointBX;
			int BY = object_info.lanes[i].mEndpointBY;
		
			//std::cout << "AX AY: " << AX << "," << AY << std::endl;
			if (0 == strcmp("dash", object_info.lanes[i].label))  //dash
			{
				const int radius = 4;
				const int thickness = -1;

				int gap;
				if (MAX(AY, BY) - MIN(AY, BY) > 60)
					gap = 10;
				else
					gap = 5;

				for (int y = MIN(AY, BY); y < MAX(AY, BY); y += gap)
				{
					double x = A0 + A1 * y + A2 * std::pow(y, 2) + A3 * std::pow(y, 3);
					
					if ((object_info.alarmInfo.m_ldw_left_solid_alarm == true && object_info.lanes[i].mIsLeftLane == true) ||
						(object_info.alarmInfo.m_ldw_right_solid_alarm == true && object_info.lanes[i].mIsRightLane == true) ||
						(object_info.alarmInfo.m_ldw_left_dash_alarm == true && object_info.lanes[i].mIsLeftLane == true ) ||
						(object_info.alarmInfo.m_ldw_right_dash_alarm == true && object_info.lanes[i].mIsRightLane == true)
						)
					{
						cv::circle(frame, cv::Point(x, y), radius, red, thickness);
					}

					else
					{
						if (object_info.lanes[i].mIsLeftLane)
							cv::circle(frame, cv::Point(x, y), radius, green, thickness);
						else if (object_info.lanes[i].mIsRightLane)
							cv::circle(frame, cv::Point(x, y), radius, yellow, thickness);
						else
							cv::circle(frame, cv::Point(x, y), radius, blue, thickness);
					}
		
				
				}
			}
			else if (0 == strcmp("solid", object_info.lanes[i].label))  //solid
			{
				const int lineType = 8;
				const int shift = 0;

				std::vector<cv::Point> points_fitted;
				for (int y = MIN(AY, BY); y < MAX(AY, BY); y++)
				{
					double x = A0 + A1 * y + A2 * std::pow(y, 2) + A3 * std::pow(y, 3);
					points_fitted.push_back(cv::Point(x, y));
				}

				if ((object_info.alarmInfo.m_ldw_left_solid_alarm == true && object_info.lanes[i].mIsLeftLane == true) ||
					(object_info.alarmInfo.m_ldw_right_solid_alarm == true && object_info.lanes[i].mIsRightLane == true) ||
					(object_info.alarmInfo.m_ldw_left_dash_alarm == true && object_info.lanes[i].mIsLeftLane == true ) ||
					(object_info.alarmInfo.m_ldw_right_dash_alarm == true && object_info.lanes[i].mIsRightLane == true )
					)
				{
					cv::polylines(frame, points_fitted, false, red, thickness, lineType, shift);
				}
				else
				{
					if (object_info.lanes[i].mIsLeftLane)
						cv::polylines(frame, points_fitted, false, green, thickness, lineType, shift);
					else if (object_info.lanes[i].mIsRightLane)
						cv::polylines(frame, points_fitted, false, yellow, thickness, lineType, shift);
					else
						cv::polylines(frame, points_fitted, false, blue, thickness, lineType, shift);
				}
		
			}

			//mXDistance
			if (isShowDistance)
			{
				int show_y = MAX(AY, BY);
				int show_x = A0 + A1 * show_y + A2 * std::pow(show_y, 2) + A3 * std::pow(show_y, 3);
				std::string distance = format("%d", (int)(object_info.lanes[i].mXDistance));  // mm
				putText(frame, distance, Point(show_x + 5, show_y), FONT_HERSHEY_SIMPLEX, 0.5, blue, 2);
				
			}

			//lane_ID
			if (isShow_laneID)
			{
				int show_y1 = MAX(AY, BY);
				int show_y2 = MIN(AY, BY);
				int show_y = (show_y1+ show_y2)/2;
				int show_x = A0 + A1 * show_y + A2 * std::pow(show_y, 2) + A3 * std::pow(show_y, 3);
				std::string id = format("%d", object_info.lanes[i].mId);
				std::string ID_display = "ID:" + id;
				putText(frame, ID_display, Point(show_x + 15, show_y + 2), FONT_HERSHEY_SIMPLEX, 0.8, black, 2);
			}

		}

	}

	void drawAheadArea(ObjAlarm* pObjAlarm, Param* pParam, cv::Mat& frame)
	{
		bool isDrawAheadArea = true;

		cv::Scalar orange(0, 128, 255);
		cv::Scalar red(0, 0, 255);

		if (isDrawAheadArea)
		{
			AREA default_ahead_area = pObjAlarm->getDefaultAheadArea();

			float k_right = default_ahead_area.k_right;
			float b_right = default_ahead_area.b_right;
			float k_left = default_ahead_area.k_left;
			float b_left = default_ahead_area.b_left;
			int horizontal_line = default_ahead_area.horizontal_line;
		
			param_t pm = pParam->getParam();
			int img_height = pm.ori_height;

			//查看该正前方区域
			for (int y = horizontal_line; y < img_height - 1; y++)
			{
				int x_left = (y - b_left) / k_left;
				int x_right = (y - b_right) / k_right;
				cv::circle(frame, cv::Point(x_left, y), 2, orange);
				cv::circle(frame, cv::Point(x_right, y), 2, orange);
			}

		}
	}

	void drawTTCAlarm(std::vector<object_t> objects, Mat& frame, float ttc_th)   // Draw the TTC alarm
	{
		cv::Scalar red(0, 0, 255);

		for (int i = 0; i < objects.size(); i++)
		{
			std::string label = objects[i].label;
			int left = objects[i].mLeft;
			int top = objects[i].mTop;
			int right = objects[i].mRight;
			int bottom = objects[i].mBottom;
			float ttc = objects[i].mTTC;
			//printf("id = %d, width = %d, ttc = %f\n", objects[i].mId, objects[i].mRight - objects[i].mLeft, ttc);
			if (ttc < ttc_th)
			{
				cv::circle(frame, cv::Point(frame.cols * 0.5, 80), 80, red, -1);
				rectangle(frame, Point(left, top), Point(right, bottom), red, -1);
				//getchar();
			}
		}
	}

	void drawTrafficSign(std::vector<traffic_t> traffics, cv::Mat& frame)
	{
		bool isShowLabel = true;
		
		cv::Scalar black(0, 0, 0);
		cv::Scalar white(255, 255, 255);
		
		for (int i = 0; i < traffics.size(); i++)
		{
			std::string label = traffics[i].label;
			int left = traffics[i].mLeft;
			int top = traffics[i].mTop;
			int right = traffics[i].mRight;
			int bottom = traffics[i].mBottom;
		
			cv::Scalar color = black;
			
			if (label == "sign")
				color = white;

			rectangle(frame, Point(left, top), Point(right, bottom), color, 2);

			std::string speed_limit = format("%d", traffics[i].mSpeedLimit);
			std::string speed_limit_display = speed_limit + "KM/H";
			putText(frame, speed_limit_display, Point(left - 20, bottom + 25), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);
		}
	}

	void FitPolynomialCurve(const std::vector<cv::Point>& points, int n, cv::Mat& A) 
	{
		//最小二乘法多项式曲线拟合原理与实现 
		// https://blog.csdn.net/jairuschan/article/details/7517773/
		// https://www.cnblogs.com/fengliu-/p/8031406.html

		int N = points.size();
		cv::Mat X = cv::Mat::zeros(n + 1, n + 1, CV_64FC1);
		for (int i = 0; i < n + 1; i++) {
			for (int j = 0; j < n + 1; j++) {
				for (int k = 0; k < N; k++) {
					X.at<double>(i, j) = X.at<double>(i, j) +
						std::pow(points[k].y, i + j);
				}
			}
		}
		cv::Mat Y = cv::Mat::zeros(n + 1, 1, CV_64FC1);
		for (int i = 0; i < n + 1; i++) {
			for (int k = 0; k < N; k++) {
				Y.at<double>(i, 0) = Y.at<double>(i, 0) +
					std::pow(points[k].y, i) * points[k].x;
			}
		}
		A = cv::Mat::zeros(n + 1, 1, CV_64FC1);
		cv::solve(X, Y, A, cv::DECOMP_LU);  //cv::DECOMP_LU
	}


	char* readLine(FILE* fp, char* buffer, int* len)
	{
		int ch;
		int i = 0;
		size_t buff_len = 0;

		buffer = (char*)malloc(buff_len + 1);
		if (!buffer)
			return NULL; // Out of memory

		while ((ch = fgetc(fp)) != '\n' && ch != EOF)
		{
			buff_len++;
			void* tmp = realloc(buffer, buff_len + 1);
			if (tmp == NULL)
			{
				free(buffer);
				return NULL; // Out of memory
			}
			buffer = (char*)tmp;

			buffer[i] = (char)ch;
			i++;
		}
		buffer[i] = '\0';

		*len = buff_len;

		// Detect end
		if (ch == EOF && (i == 0 || ferror(fp)))
		{
			free(buffer);
			return NULL;
		}
		return buffer;
	}

	int readLines(const char* fileName, char* lines[], int max_line)
	{
		FILE* file = fopen(fileName, "r");
		char* s = NULL;
		int i = 0;
		int n = 0;
		while ((s = readLine(file, s, &n)) != NULL)
		{
			lines[i++] = s;
			if (i >= max_line)
				break;
		}
		return i;
	}

	int loadLabelName(const char* locationFilename, char* label[], int objNum)
	{
		printf("loadLabelName %s\n", locationFilename);
		readLines(locationFilename, label, objNum);
		return 0;
	}

	void fillNoDetectionArea(cv::Mat& img, std::vector<cv::Point> no_detection_area, int orgw, int orgh, int newh, int neww, int padh, int padw, cv::Scalar color)
	{
		//将不检测区域用黑色填充，避免车体等误检

		if (no_detection_area.size() <= 0)
			return;

		std::vector<cv::Point> trans_no_detection_area;

		float w_ratio = (float)neww / orgw;
		float h_ratio = (float)newh / orgh;

		for (int i = 0; i < no_detection_area.size(); i++)
		{
			int x = no_detection_area[i].x;
			int y = no_detection_area[i].y;

			cv::Point pt;
			pt.x = padw + x * w_ratio;
			pt.y = padh + y * h_ratio;

			trans_no_detection_area.push_back(pt);
		}

		cv::fillPoly(img, trans_no_detection_area, color);

		//cv::imshow("fill", img);
		//cv::waitKey(0);
	}

	void CalcObjIsInsideRoad(std::vector<DetObject>& det_object, const int img_input_width, const int img_input_height, cv::Mat mask_90p, int road_index, const int objNum)
	{
		//目标框与可行驶区域是否有交集，从而判断是否对目标进行报警

		float h_ratio = (float)mask_90p.rows / img_input_height;
		float w_ratio = (float)mask_90p.cols / img_input_width;

		for (int i = 0; i < det_object.size(); i++)
		{
			det_object[i].isInsideRoad = false;

			std::string label = det_object[i].label;
			if (label == "road" || label == "lane")
				continue;

			cv::Rect rect_src = det_object[i].rect;
			cv::Rect rect_dst;

			rect_dst.x = rect_src.x * w_ratio;
			rect_dst.y = rect_src.y * h_ratio;
			rect_dst.width = rect_src.width * w_ratio;
			rect_dst.height = rect_src.height * h_ratio;

			int up = rect_dst.y;
			int down = rect_dst.y + rect_dst.height;
			int left = rect_dst.x;
			int right = rect_dst.x + rect_dst.width;

			for (int m = up; m < down; m++)
			{
				for (int n = left; n < right; n++)
				{
					int val = mask_90p.at<uchar>(m, n);
					if (val == road_index)
						det_object[i].isInsideRoad = true;
				}
			}
		}
	}

	int process_u8(uint8_t* input, int* anchor, int objNum, int prop_box_size, int grid_h, int grid_w, int height, int width, int stride,
		std::vector<float>& boxes, std::vector<float>& objProbs, std::vector<int>& classId,
		float threshold, uint32_t zp, float scale)
	{

		int validCount = 0;
		int grid_len = grid_h * grid_w;
		float thres = unsigmoid(threshold);
		uint8_t thres_u8 = qnt_f32_to_affine(thres, zp, scale);
		for (int a = 0; a < 3; a++)
		{
			for (int i = 0; i < grid_h; i++)
			{
				for (int j = 0; j < grid_w; j++)
				{
					uint8_t box_confidence = input[(prop_box_size * a + 4) * grid_len + i * grid_w + j];
					if (box_confidence >= thres_u8)
					{
						int offset = (prop_box_size * a) * grid_len + i * grid_w + j;
						uint8_t* in_ptr = input + offset;
						float box_x = sigmoid(deqnt_affine_to_f32(*in_ptr, zp, scale)) * 2.0 - 0.5;
						float box_y = sigmoid(deqnt_affine_to_f32(in_ptr[grid_len], zp, scale)) * 2.0 - 0.5;
						float box_w = sigmoid(deqnt_affine_to_f32(in_ptr[2 * grid_len], zp, scale)) * 2.0;
						float box_h = sigmoid(deqnt_affine_to_f32(in_ptr[3 * grid_len], zp, scale)) * 2.0;
						box_x = (box_x + j) * (float)stride;
						box_y = (box_y + i) * (float)stride;
						box_w = box_w * box_w * (float)anchor[a * 2];
						box_h = box_h * box_h * (float)anchor[a * 2 + 1];
						box_x -= (box_w / 2.0);
						box_y -= (box_h / 2.0);
						boxes.push_back(box_x);
						boxes.push_back(box_y);
						boxes.push_back(box_w);
						boxes.push_back(box_h);

						uint8_t maxClassProbs = in_ptr[5 * grid_len];
						int maxClassId = 0;
						for (int k = 1; k < objNum; ++k)
						{
							uint8_t prob = in_ptr[(5 + k) * grid_len];
							if (prob > maxClassProbs)
							{
								maxClassId = k;
								maxClassProbs = prob;
							}
						}
						//objProbs.push_back(sigmoid(deqnt_affine_to_f32(maxClassProbs, zp, scale)));
						objProbs.push_back(sigmoid(deqnt_affine_to_f32(box_confidence, zp, scale)));

						classId.push_back(maxClassId);
						validCount++;
					}
				}
			}
		}
		return validCount;
	}

	int post_process_u8(uint8_t* input0, uint8_t* input1, uint8_t* input2, int *anchor0, int *anchor1, int *anchor2, char** labels, int objNum, int prop_box_size, int model_in_h, int model_in_w,
		float conf_threshold, float nms_threshold,
		std::vector<uint32_t>& qnt_zps, std::vector<float>& qnt_scales,
		std::vector<detect_result_t>& group)
	{

		std::vector<float> filterBoxes;
		std::vector<float> objProbs;
		std::vector<int> classId;

		// stride 8
		int stride0 = 8;
		int grid_h0 = model_in_h / stride0;
		int grid_w0 = model_in_w / stride0;
		int validCount0 = 0;
		validCount0 = process_u8(input0, (int*)anchor0, objNum, prop_box_size, grid_h0, grid_w0, model_in_h, model_in_w,
			stride0, filterBoxes, objProbs, classId, conf_threshold, qnt_zps[0], qnt_scales[0]);

		// stride 16
		int stride1 = 16;
		int grid_h1 = model_in_h / stride1;
		int grid_w1 = model_in_w / stride1;
		int validCount1 = 0;
		validCount1 = process_u8(input1, (int*)anchor1, objNum, prop_box_size, grid_h1, grid_w1, model_in_h, model_in_w,
			stride1, filterBoxes, objProbs, classId, conf_threshold, qnt_zps[1], qnt_scales[1]);

		// stride 32
		int stride2 = 32;
		int grid_h2 = model_in_h / stride2;
		int grid_w2 = model_in_w / stride2;
		int validCount2 = 0;
		validCount2 = process_u8(input2, (int*)anchor2, objNum, prop_box_size, grid_h2, grid_w2, model_in_h, model_in_w,
			stride2, filterBoxes, objProbs, classId, conf_threshold, qnt_zps[2], qnt_scales[2]);

		int validCount = validCount0 + validCount1 + validCount2;
		// no object detect
		if (validCount <= 0)
		{
			return 0;
		}

		std::vector<int> indexArray;
		for (int i = 0; i < validCount; ++i)
		{
			indexArray.push_back(i);
		}

		quick_sort_indice_inverse(objProbs, 0, validCount - 1, indexArray);

		std::set<int> class_set(std::begin(classId), std::end(classId));

		for (auto c : class_set)
		{
			nms_u8(validCount, filterBoxes, classId, indexArray, c, nms_threshold);
		}


		/* box valid detect target */
		for (int i = 0; i < validCount; ++i)
		{

			if (indexArray[i] == -1)
			{
				continue;
			}
			int n = indexArray[i];

			float x1 = filterBoxes[n * 4 + 0];
			float y1 = filterBoxes[n * 4 + 1];
			float x2 = x1 + filterBoxes[n * 4 + 2];
			float y2 = y1 + filterBoxes[n * 4 + 3];
			int id = classId[n];
			float obj_conf = objProbs[i];  //objProbs已经排序好，所以index与filterBoxes及classId不同

			detect_result_t detect_result;

			detect_result.box.left = (int)(clamp(x1, 0, model_in_w));
			detect_result.box.top = (int)(clamp(y1, 0, model_in_h));
			detect_result.box.right = (int)(clamp(x2, 0, model_in_w));
			detect_result.box.bottom = (int)(clamp(y2, 0, model_in_h));
			detect_result.prop = obj_conf;
			char* label = labels[id];
			strncpy(detect_result.name, label, DAS_OBJ_NAME_MAX_SIZE);

			// printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left, group->results[last_count].box.top,
			//        group->results[last_count].box.right, group->results[last_count].box.bottom, label);

			group.push_back(detect_result);

		}


		return 0;
	}

	int process_fp(float* input, int* anchor, int objNum, int prop_box_size, int grid_h, int grid_w, int height, int width, int stride,
		std::vector<float>& boxes, std::vector<float>& boxScores, std::vector<int>& classId,
		float threshold)
	{

		int validCount = 0;
		int grid_len = grid_h * grid_w;
		float thres_sigmoid = unsigmoid(threshold);
		for (int a = 0; a < 3; a++)
		{
			for (int i = 0; i < grid_h; i++)
			{
				for (int j = 0; j < grid_w; j++)
				{
					float box_confidence = input[(prop_box_size * a + 4) * grid_len + i * grid_w + j];
					if (box_confidence >= thres_sigmoid)
					{
						int offset = (prop_box_size * a) * grid_len + i * grid_w + j;
						float* in_ptr = input + offset;
						float box_x = sigmoid(*in_ptr) * 2.0 - 0.5;
						float box_y = sigmoid(in_ptr[grid_len]) * 2.0 - 0.5;
						float box_w = sigmoid(in_ptr[2 * grid_len]) * 2.0;
						float box_h = sigmoid(in_ptr[3 * grid_len]) * 2.0;
						box_x = (box_x + j) * (float)stride;
						box_y = (box_y + i) * (float)stride;
						box_w = box_w * box_w * (float)anchor[a * 2];
						box_h = box_h * box_h * (float)anchor[a * 2 + 1];
						box_x -= (box_w / 2.0);
						box_y -= (box_h / 2.0);
						boxes.push_back(box_x);
						boxes.push_back(box_y);
						boxes.push_back(box_w);
						boxes.push_back(box_h);

						float maxClassProbs = in_ptr[5 * grid_len];
						int maxClassId = 0;
						for (int k = 1; k < objNum; ++k)
						{
							float prob = in_ptr[(5 + k) * grid_len];
							if (prob > maxClassProbs)
							{
								maxClassId = k;
								maxClassProbs = prob;
							}
						}
						float box_conf_f32 = sigmoid(box_confidence);
						float class_prob_f32 = sigmoid(maxClassProbs);
						boxScores.push_back(box_conf_f32 * class_prob_f32);
						classId.push_back(maxClassId);
						validCount++;
					}
				}
			}
		}
		return validCount;
	}

	int post_process_fp(float* input0, float* input1, float* input2, int* anchor0, int* anchor1, int* anchor2, char** labels, int objNum, int prop_box_size, int model_in_h, int model_in_w,
		int h_offset, int w_offset, float resize_scale, float conf_threshold, float nms_threshold,
		std::vector<detect_result_t>& group)
	{

		std::vector<float> filterBoxes;
		std::vector<float> boxesScore;
		std::vector<int> classId;
		int stride0 = 8;
		int grid_h0 = model_in_h / stride0;
		int grid_w0 = model_in_w / stride0;
		int validCount0 = 0;
		validCount0 = process_fp(input0, (int*)anchor0, objNum, prop_box_size, grid_h0, grid_w0, model_in_h, model_in_w,
			stride0, filterBoxes, boxesScore, classId, conf_threshold);

		int stride1 = 16;
		int grid_h1 = model_in_h / stride1;
		int grid_w1 = model_in_w / stride1;
		int validCount1 = 0;
		validCount1 = process_fp(input1, (int*)anchor1, objNum, prop_box_size, grid_h1, grid_w1, model_in_h, model_in_w,
			stride1, filterBoxes, boxesScore, classId, conf_threshold);

		int stride2 = 32;
		int grid_h2 = model_in_h / stride2;
		int grid_w2 = model_in_w / stride2;
		int validCount2 = 0;
		validCount2 = process_fp(input2, (int*)anchor2, objNum, prop_box_size, grid_h2, grid_w2, model_in_h, model_in_w,
			stride2, filterBoxes, boxesScore, classId, conf_threshold);

		int validCount = validCount0 + validCount1 + validCount2;
		// no object detect
		if (validCount <= 0)
		{
			return 0;
		}

		std::vector<int> indexArray;
		for (int i = 0; i < validCount; ++i)
		{
			indexArray.push_back(i);
		}

		quick_sort_indice_inverse(boxesScore, 0, validCount - 1, indexArray);

		nms_fp(validCount, filterBoxes, indexArray, nms_threshold);

		/* box valid detect target */
		for (int i = 0; i < validCount; ++i)
		{
			if (indexArray[i] == -1 || boxesScore[i] < conf_threshold)
			{
				continue;
			}
			int n = indexArray[i];

			float x1 = filterBoxes[n * 4 + 0];
			float y1 = filterBoxes[n * 4 + 1];
			float x2 = x1 + filterBoxes[n * 4 + 2];
			float y2 = y1 + filterBoxes[n * 4 + 3];
			int id = classId[n];

			detect_result_t detect_result;

			detect_result.box.left = (int)((clamp(x1, 0, model_in_w) - w_offset) / resize_scale);
			detect_result.box.top = (int)((clamp(y1, 0, model_in_h) - h_offset) / resize_scale);
			detect_result.box.right = (int)((clamp(x2, 0, model_in_w) - w_offset) / resize_scale);
			detect_result.box.bottom = (int)((clamp(y2, 0, model_in_h) - h_offset) / resize_scale);
			detect_result.prop = boxesScore[i];  //boxesScore已经排序好，所以index与filterBoxes及classId不同
			detect_result.class_index = id;
			char* label = labels[id];
			strncpy(detect_result.name, label, DAS_OBJ_NAME_MAX_SIZE);

			// printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left, group->results[last_count].box.top,
			//        group->results[last_count].box.right, group->results[last_count].box.bottom, label);

			group.push_back(detect_result);
		}

		return 0;
	}

	int process_fp_seg(float* input, int* anchor, int objNum, int prop_box_size, int segChannels, int grid_h, int grid_w, int height, int width, int stride,
		std::vector<float>& boxes, std::vector<float>& boxScores, std::vector<int>& classId, std::vector< std::vector<float> >& picked_proposals, float threshold)
	{

		int validCount = 0;
		int grid_len = grid_h * grid_w;
		float thres_sigmoid = unsigmoid(threshold);
		for (int a = 0; a < 3; a++)
		{
			for (int i = 0; i < grid_h; i++)
			{
				for (int j = 0; j < grid_w; j++)
				{
					float box_confidence = input[(prop_box_size * a + 4) * grid_len + i * grid_w + j];
					if (box_confidence >= thres_sigmoid)
					{
						int offset = (prop_box_size * a) * grid_len + i * grid_w + j;
						float* in_ptr = input + offset;
						float box_x = sigmoid(*in_ptr) * 2.0 - 0.5;
						float box_y = sigmoid(in_ptr[grid_len]) * 2.0 - 0.5;
						float box_w = sigmoid(in_ptr[2 * grid_len]) * 2.0;
						float box_h = sigmoid(in_ptr[3 * grid_len]) * 2.0;
						box_x = (box_x + j) * (float)stride;
						box_y = (box_y + i) * (float)stride;
						box_w = box_w * box_w * (float)anchor[a * 2];
						box_h = box_h * box_h * (float)anchor[a * 2 + 1];
						box_x -= (box_w * 0.5);
						box_y -= (box_h * 0.5);

						boxes.push_back(box_x);
						boxes.push_back(box_y);
						boxes.push_back(box_w);
						boxes.push_back(box_h);

						float maxClassProbs = in_ptr[5 * grid_len];
						int maxClassId = 0;
						for (int k = 1; k < objNum; ++k)
						{
							float prob = in_ptr[(5 + k) * grid_len];
							if (prob > maxClassProbs)
							{
								maxClassId = k;
								maxClassProbs = prob;
							}
						}
						float box_conf_f32 = sigmoid(box_confidence);
						float class_prob_f32 = sigmoid(maxClassProbs);
						boxScores.push_back(box_conf_f32 * class_prob_f32);
						classId.push_back(maxClassId);

						//mask
						std::vector<float> temp_proto;
						for (int q = prop_box_size - segChannels; q < prop_box_size; q++)
						{
							float val = in_ptr[q * grid_len];
							temp_proto.push_back(val);
						}
						picked_proposals.push_back(temp_proto);

						validCount++;
					}
				}
			}
		}
		return validCount;
	}

	int post_process_fp_seg(float* input0, float* input1, float* input2, int* anchor0, int* anchor1, int* anchor2, char** labels, int objNum, int prop_box_size, int segChannels, int model_in_h, int model_in_w,
		float conf_threshold, float nms_threshold, std::vector<detect_result_t>& group)
	{
#ifdef DEBUG_PRINT
		printf("lyc dasYOLOV5Seg.cpp post_process_fp_seg() start\n");
#endif
		std::vector<float> filterBoxes;
		std::vector<float> boxesScore;
		std::vector<int> classId;
		std::vector< std::vector<float> > picked_proposals;  //存储output0[:,:, 5 + _className.size():net_width]用以后续计算mask

		int stride0 = 8;
		int grid_h0 = (float)model_in_h / stride0;
		int grid_w0 = (float)model_in_w / stride0;
		int validCount0 = 0;
		validCount0 = process_fp_seg(input0, (int*)anchor0, objNum, prop_box_size, segChannels, grid_h0, grid_w0, model_in_h, model_in_w,
			stride0, filterBoxes, boxesScore, classId, picked_proposals, conf_threshold);

		int stride1 = 16;
		int grid_h1 = (float)model_in_h / stride1;
		int grid_w1 = (float)model_in_w / stride1;
		int validCount1 = 0;
		validCount1 = process_fp_seg(input1, (int*)anchor1, objNum, prop_box_size, segChannels, grid_h1, grid_w1, model_in_h, model_in_w,
			stride1, filterBoxes, boxesScore, classId, picked_proposals, conf_threshold);

		int stride2 = 32;
		int grid_h2 = (float)model_in_h / stride2;
		int grid_w2 = (float)model_in_w / stride2;
		int validCount2 = 0;
		validCount2 = process_fp_seg(input2, (int*)anchor2, objNum, prop_box_size, segChannels, grid_h2, grid_w2, model_in_h, model_in_w,
			stride2, filterBoxes, boxesScore, classId, picked_proposals, conf_threshold);

		int validCount = validCount0 + validCount1 + validCount2;
		// no object detect
		if (validCount <= 0)
		{
			return 0;
		}

		std::vector<int> indexArray;
		for (int i = 0; i < validCount; ++i)
		{
			indexArray.push_back(i);
		}

		quick_sort_indice_inverse(boxesScore, 0, validCount - 1, indexArray);

		std::set<int> class_set(std::begin(classId), std::end(classId));

		for (auto c : class_set)
		{
			nms_u8(validCount, filterBoxes, classId, indexArray, c, nms_threshold);
		}

		/* box valid detect target */
		for (int i = 0; i < validCount; ++i)
		{
			if (indexArray[i] == -1)
			{
				continue;
			}
			int n = indexArray[i];

			float x1 = filterBoxes[n * 4 + 0];
			float y1 = filterBoxes[n * 4 + 1];
			float x2 = x1 + filterBoxes[n * 4 + 2];
			float y2 = y1 + filterBoxes[n * 4 + 3];
			int id = classId[n];
			float obj_conf = boxesScore[i];  //boxesScore已经排序好，所以index与filterBoxes及classId不同

			detect_result_t detect_result;

			detect_result.box.left = (int)(clamp(x1, 0, model_in_w));
			detect_result.box.top = (int)(clamp(y1, 0, model_in_h));
			detect_result.box.right = (int)(clamp(x2, 0, model_in_w));
			detect_result.box.bottom = (int)(clamp(y2, 0, model_in_h));
			detect_result.prop = obj_conf;
			char* label = labels[id];
			strncpy(detect_result.name, label, DAS_OBJ_NAME_MAX_SIZE);

			detect_result.class_index = id;

			//mask
			detect_result.temp_mask_proposals.resize(picked_proposals[n].size());
			for (int q = 0; q < picked_proposals[n].size(); q++)
				detect_result.temp_mask_proposals[q] = picked_proposals[n][q];

			// printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left, group->results[last_count].box.top,
			//        group->results[last_count].box.right, group->results[last_count].box.bottom, label);

			group.push_back(detect_result);
		}

#ifdef DEBUG_PRINT
		printf("lyc dasYOLOV5Seg.cpp post_process_fp_seg() end\n");
#endif

		return 0;
	}


		inline int clamp(float val, int min, int max)
		{
			return val > min ? (val < max ? val : max) : min;
		}

		float CalculateOverlap(float xmin0, float ymin0, float xmax0, float ymax0, float xmin1, float ymin1, float xmax1, float ymax1)
		{
			float w = fmax(0.f, fmin(xmax0, xmax1) - fmax(xmin0, xmin1) + 1.0);
			float h = fmax(0.f, fmin(ymax0, ymax1) - fmax(ymin0, ymin1) + 1.0);
			float i = w * h;
			float u = (xmax0 - xmin0 + 1.0) * (ymax0 - ymin0 + 1.0) + (xmax1 - xmin1 + 1.0) * (ymax1 - ymin1 + 1.0) - i;
			return u <= 0.f ? 0.f : (i / u);
		}

		int nms_u8(int validCount, std::vector<float>& outputLocations, std::vector<int> classIds, std::vector<int>& order, int filterId, float threshold)
		{
			for (int i = 0; i < validCount; ++i)
			{
				if (order[i] == -1 || classIds[i] != filterId)
				{
					continue;
				}
				int n = order[i];
				for (int j = i + 1; j < validCount; ++j)
				{
					int m = order[j];
					if (m == -1 || classIds[i] != filterId)
					{
						continue;
					}
					float xmin0 = outputLocations[n * 4 + 0];
					float ymin0 = outputLocations[n * 4 + 1];
					float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
					float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

					float xmin1 = outputLocations[m * 4 + 0];
					float ymin1 = outputLocations[m * 4 + 1];
					float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
					float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

					float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);

					if (iou > threshold)
					{
						order[j] = -1;
					}
				}
			}
			return 0;
		}

		int nms_fp(int validCount, std::vector<float>& outputLocations, std::vector<int>& order, float threshold)
		{
			for (int i = 0; i < validCount; ++i)
			{
				if (order[i] == -1)
				{
					continue;
				}
				int n = order[i];
				for (int j = i + 1; j < validCount; ++j)
				{
					int m = order[j];
					if (m == -1)
					{
						continue;
					}
					float xmin0 = outputLocations[n * 4 + 0];
					float ymin0 = outputLocations[n * 4 + 1];
					float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
					float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

					float xmin1 = outputLocations[m * 4 + 0];
					float ymin1 = outputLocations[m * 4 + 1];
					float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
					float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

					float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);

					if (iou > threshold)
					{
						order[j] = -1;
					}
				}
			}
			return 0;
		}

		int quick_sort_indice_inverse(
			std::vector<float>& input,
			int left,
			int right,
			std::vector<int>& indices)
		{
			float key;
			int key_index;
			int low = left;
			int high = right;
			if (left < right)
			{
				key_index = indices[left];
				key = input[left];
				while (low < high)
				{
					while (low < high && input[high] <= key)
					{
						high--;
					}
					input[low] = input[high];
					indices[low] = indices[high];
					while (low < high && input[low] >= key)
					{
						low++;
					}
					input[high] = input[low];
					indices[high] = indices[low];
				}
				input[low] = key;
				indices[low] = key_index;
				quick_sort_indice_inverse(input, left, low - 1, indices);
				quick_sort_indice_inverse(input, low + 1, right, indices);
			}
			return low;
		}

		float sigmoid(float x)
		{
			return 1.0 / (1.0 + expf(-x));
		}

		float unsigmoid(float y)
		{
			return -1.0 * logf((1.0 / y) - 1.0);
		}

		inline int32_t __clip(float val, float min, float max)
		{
			float f = val <= min ? min : (val >= max ? max : val);
			return f;
		}

		uint8_t qnt_f32_to_affine(float f32, uint32_t zp, float scale)
		{
			float dst_val = (f32 / scale) + zp;
			uint8_t res = (uint8_t)__clip(dst_val, 0, 255);
			return res;
		}

		float deqnt_affine_to_f32(uint8_t qnt, uint32_t zp, float scale)
		{
			return ((float)qnt - (float)zp) * scale;
		}



#ifdef DAS_USING_RKNN


		/*-------------------------------------------
		Functions
		-------------------------------------------*/

		inline const char* get_type_string(rknn_tensor_type type)
		{
			switch (type) {
			case RKNN_TENSOR_FLOAT32: return "FP32";
			case RKNN_TENSOR_FLOAT16: return "FP16";
			case RKNN_TENSOR_INT8: return "INT8";
			case RKNN_TENSOR_UINT8: return "UINT8";
			case RKNN_TENSOR_INT16: return "INT16";
			default: return "UNKNOW";
			}
		}

		inline const char* get_qnt_type_string(rknn_tensor_qnt_type type)
		{
			switch (type) {
			case RKNN_TENSOR_QNT_NONE: return "NONE";
			case RKNN_TENSOR_QNT_DFP: return "DFP";
			case RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC: return "AFFINE";
			default: return "UNKNOW";
			}
		}

		inline const char* get_format_string(rknn_tensor_format fmt)
		{
			switch (fmt) {
			case RKNN_TENSOR_NCHW: return "NCHW";
			case RKNN_TENSOR_NHWC: return "NHWC";
			default: return "UNKNOW";
			}
		}

		void dump_tensor_attr(rknn_tensor_attr* attr)
		{
			printf("  index=%d, name=%s, n_dims=%d, dims=[%d, %d, %d, %d], n_elems=%d, size=%d, fmt=%s, type=%s, qnt_type=%s, "
				"zp=%d, scale=%f\n",
				attr->index, attr->name, attr->n_dims, attr->dims[3], attr->dims[2], attr->dims[1], attr->dims[0],
				attr->n_elems, attr->size, get_format_string(attr->fmt), get_type_string(attr->type),
				get_qnt_type_string(attr->qnt_type), attr->zp, attr->scale);
		}

		double __get_us(struct timeval t) { return (t.tv_sec * 1000000 + t.tv_usec); }

		unsigned char* load_data(FILE* fp, size_t ofst, size_t sz)
		{
			unsigned char* data;
			int ret;

			data = NULL;

			if (NULL == fp)
			{
				return NULL;
			}

			ret = fseek(fp, ofst, SEEK_SET);
			if (ret != 0)
			{
				printf("blob seek failure.\n");
				return NULL;
			}

			data = (unsigned char*)malloc(sz);
			if (data == NULL)
			{
				printf("buffer malloc failure.\n");
				return NULL;
			}
			ret = fread(data, 1, sz, fp);
			return data;
		}

		unsigned char* load_model(const char* filename, int* model_size)
		{

			FILE* fp;
			unsigned char* data;

			fp = fopen(filename, "rb");
			if (NULL == fp)
			{
				printf("Open file %s failed.\n", filename);
				return NULL;
			}

			fseek(fp, 0, SEEK_END);
			int size = ftell(fp);

			data = load_data(fp, 0, size);

			fclose(fp);

			*model_size = size;
			return data;
		}

		/**
		 * @brief 对神经网络vis模型进行解密并读出版本号
		 * @param vis_model_input vis模型的输入buffer
		 * @param vis_model_input_len vis模型的buffer长度
		 * @param rknn_model_output_len 输出的rknn模型buffer长度
		 * @param key1_output 版本号数
		 * @param key2_output 版本号数
		 * @param key3_output 版本号数
		 * @param isWriteFile 是否将rknn模型写成文件，一般在windows端的工具中保存，G4上不做保存操作
		 * @param path_write_file 写rknn文件的路径，不做保存操作时置NULL，一般在windows端的工具中保存，G4上不做保存操作
		 * @return 输出的rknn模型buffer
		*/
		unsigned char* decode(unsigned char* vis_model_input, int vis_model_input_len, int* rknn_model_output_len, char* key1_output, char* key2_output, char* key3_output, bool isWriteFile, char* path_write_file)
		{
			*rknn_model_output_len = vis_model_input_len - 3;

			*key1_output = vis_model_input[0];
			*key2_output = vis_model_input[1];
			*key3_output = vis_model_input[2];

			unsigned char* rknn_model_output = (unsigned char*)malloc((*rknn_model_output_len + 5) * sizeof(unsigned char));

			for (int i = 0; i < *rknn_model_output_len; i++)
				rknn_model_output[i] = vis_model_input[i + 3];

			///////  make the dictionarie key here  /////////////////////////////////////////////////////////////////////////////////////
			unsigned char* dict = (unsigned char*)malloc((*rknn_model_output_len + 5) * sizeof(unsigned char));
			memset(dict, 0, *rknn_model_output_len);
			for (int i = 0; i < *rknn_model_output_len; i += 3)
			{
				int half = floor(*rknn_model_output_len * 0.5);
				if (i < half)
				{
					dict[i] = *key1_output;
					dict[i + 1] = *key2_output;
					dict[i + 2] = *key3_output;
				}
				else
				{
					dict[i] = *key3_output;
					dict[i + 1] = *key2_output;
					dict[i + 2] = *key1_output;
				}
			}
			////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

			for (int i = 0; i < *rknn_model_output_len; i++)
				rknn_model_output[i] = rknn_model_output[i] ^ dict[i];



			if (isWriteFile && path_write_file != NULL)
			{
				FILE* fp;
				fp = fopen(path_write_file, "wb");

				fwrite(rknn_model_output, sizeof(unsigned char), *rknn_model_output_len, fp);
				//fputs((char *)out, fp);

				fclose(fp);
			}



			free(dict);
			dict = NULL;

			return rknn_model_output;
	}

		/*
		void decode(unsigned char* input, unsigned char* output, unsigned char* dict, int len)
		{
			for (int i = 0; i < len; i++)
				output[i] = input[i] ^ dict[i];
		}
		*/

		int saveFloat(const char* file_name, float* output, int element_size)
		{
			FILE* fp;
			fp = fopen(file_name, "w");
			for (int i = 0; i < element_size; i++)
			{
				fprintf(fp, "%.6f\n", output[i]);
			}
			fclose(fp);
			return 0;
		}

#endif


		void CalcCamCoverd(cv::Mat srcimg, float& imgBrightness, bool& isCamCoverd, bool& isFullBlack)
		{
			//G3给过来算法的图片是原始图片，叠加前，没有时间水印的！！！

			//图像亮度imgBrightness是G4-GJ项目用到，用于根据环境亮度来控制灯的开关
			//isCamCoverd用于判断相机是否有被遮挡
			//isFullBlack用于判断图像是否全黑（用于相机被拔掉或相机通路出问题时的故障上报）


			//cvNamedWindow("pResizeGrayImg");
			//cvShowImage("pResizeGrayImg", mPpImgs->pResizeGrayImg);
			//cvWaitKey(0);

			//srcimg = imread("C://Users//lyc_s//Desktop//coverImg.bmp");

			float scale = 0.278;
			Mat img_resize_rgb, img_resize_gray, img_canny;
			cv::resize(srcimg, img_resize_rgb, Size(srcimg.cols * scale, srcimg.rows * scale), INTER_NEAREST);

			//imshow("img_resize_rgb", img_resize_rgb);

			float del_percent = 0.05;
			img_resize_rgb = img_resize_rgb(Rect(0, img_resize_rgb.rows * del_percent, img_resize_rgb.cols, img_resize_rgb.rows * (1 - del_percent) - 1));

			cv::cvtColor(img_resize_rgb, img_resize_gray, COLOR_BGR2GRAY);


			//imshow("img_resize_gray", img_resize_gray);
			//waitKey(0);

			blur(img_resize_gray, img_resize_gray, Size(5, 5));
			Canny(img_resize_gray, img_canny, 10, 20, 3);  // Canny(img_resize_gray, img_canny, 30, 60, 3);

			//imshow("canny", img_canny);
			//waitKey(0);

			//Mat element = getStructuringElement(MORPH_RECT, Size(3, 3));
			//erode(img_canny, img_canny, element);

			int white_cnt = 0;

			int total_cnt = img_canny.rows * img_canny.cols * img_canny.channels();

			for (int i = 0; i < total_cnt; i++)
				if (img_canny.data[i] > 200)
					white_cnt++;

			float ratio = (float)white_cnt / total_cnt;


			int sum_gray = 0;
			for (int i = 0; i < total_cnt; i++)
				sum_gray += img_resize_gray.data[i];
			float avg_gray = (float)sum_gray / total_cnt;


			//printf("ratio = %f\n", ratio);
			//imshow("canny", img_canny);
			//waitKey(0);

			//return ratio < 0.003 ? true : false;

			//get result
			imgBrightness = avg_gray;
			isCamCoverd = ((ratio < 0.005) || (avg_gray < 22)) ? true : false;
			isFullBlack = (white_cnt < 5 && avg_gray < 30) ? true : false;
		}


}