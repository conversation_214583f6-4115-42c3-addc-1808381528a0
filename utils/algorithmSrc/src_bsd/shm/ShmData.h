//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/7.
//

#ifndef VISPECT_SHMDATA_H
#define VISPECT_SHMDATA_H

namespace vis
{

    class ShmData
    {
    public:
        int getLastIndex() const;
        void setLastIndex(int lastIndex);
        int getNextIndex() const;
        void setNextIndex(int nextIndex);
        int getSize() const;
        void setSize(int size);
        int getId() const;
        void setId(int id);
        int getType() const;
        void setType(int type);
        int getIsReaded() const;
        void setIsReaded(int isReaded);
        int getIsUsed() const;
        void setIsUsed(int isUsed);
        unsigned char *getData() const;
        void setData(unsigned char *data);

    private:
        int lastIndex;         /* 4 byte    上一包数据的偏移地址*/
        int nextIndex;         /* 4 byte    下一包数据的偏移地址*/
        int size;              /* 4 byte    长度仅仅是指 data的长度*/
        int id;                /* 1 byte*/
        int type;              /* 1 byte  0:H264数据  2：从未被写过的内存 */
        int isReaded;          /* 1 byte  0:false  1:true*/
        int isUsed; /*0:false  1:true*/
        unsigned char *data /*size byte*/;
    };

} // namespace vis

#endif // VISPECT_SHMDATA_H
