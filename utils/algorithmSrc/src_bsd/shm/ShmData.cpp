//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/7.
//

#include "ShmData.h"

namespace vis
{

    int ShmData::getLastIndex() const
    {
        return lastIndex;
    }
    void ShmData::setLastIndex(int lastIndex)
    {
        ShmData::lastIndex = lastIndex;
    }
    int ShmData::getNextIndex() const
    {
        return nextIndex;
    }
    void ShmData::setNextIndex(int nextIndex)
    {
        ShmData::nextIndex = nextIndex;
    }
    int ShmData::getSize() const
    {
        return size;
    }
    void ShmData::setSize(int size)
    {
        ShmData::size = size;
    }
    int ShmData::getId() const
    {
        return id;
    }
    void ShmData::setId(int id)
    {
        ShmData::id = id;
    }
    int ShmData::getType() const
    {
        return type;
    }
    void ShmData::setType(int type)
    {
        ShmData::type = type;
    }
    int ShmData::getIsReaded() const
    {
        return isReaded;
    }
    void ShmData::setIsReaded(int isReaded)
    {
        ShmData::isReaded = isReaded;
    }
    int ShmData::getIsUsed() const
    {
        return isUsed;
    }
    void ShmData::setIsUsed(int isUsed)
    {
        ShmData::isUsed = isUsed;
    }
    unsigned char *ShmData::getData() const
    {
        return data;
    }
    void ShmData::setData(unsigned char *data)
    {
        ShmData::data = data;
    }
} // namespace vis