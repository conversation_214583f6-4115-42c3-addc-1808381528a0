//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/5/27.
//
#include "ShmManager.h"
#include <Poco/String.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/shm.h>
#include <sys/stat.h>
#include <unistd.h>
#include <vector>
namespace vis
{



    int ShmManager::openShm(const char *const name, const int allSize, const int onePackageLen)
    {

        umask(0);
        const int fd = shm_open(name, O_CREAT | O_RDWR, 438);
        ftruncate(fd, allSize);
        curOnePackageLen = onePackageLen;
        shmSize = allSize;
        shmFD = fd;
        return fd;
    }

    int ShmManager::init(const int elementsSize)
    {
        int result = -1;
        /* 计算需要多长的长度 */
        const int needLen = elementsSize * curOnePackageLen;
        /* 看看现有的长度是不是不够缓存那么多包 */
        if (shmSize >= needLen)
        {
            /* 把内存映射出来 */
            (void)mmapShm();
            /* 初始化一下 */
            for(int i = 0; i < elementsSize; i ++){
                dataArrays->dataItems[i].size = 4194304U;
                dataArrays->dataItems[i].id = i;
                dataArrays->dataItems[i].type = 2;
                dataArrays->dataItems[i].isReaded = true;
                dataArrays->dataItems[i].isUsed = true;
            }
            /* 取消内存的映射 */
            (void)munmapShm();
            result = 0;
        }
        else
        {
            ;
        }

        return result;
    }

    int ShmManager::deleteShm(const char *const name)
    {
        const int result = shm_unlink(name);
        return result;
    }

    int ShmManager::mmapShm()
    {
        int ret = -1;
        /* 单纯为了过代码规范  所以放到一个奇怪的结构体里 */
        void *point = mmap(nullptr, shmSize, PROT_READ | PROT_WRITE, MAP_SHARED, shmFD, 0);
        (void)memcpy(&dataArrays, &point, sizeof(point));
        if(nullptr != dataArrays){
            ret = 0;
        }else{
            ret = -1;
        }
        return ret;
    }

    int ShmManager::munmapShm()
    {
        const int result = munmap(dataArrays, shmSize);
        return result;
    }
    /**
     * read完之后必须调用  `releaseOnePackage` 回收掉该内存
     * */
    int ShmManager::readOnePackage(ShmData &newShmData)
    {

        int result = -1;
        int curID = lastReadId;
        uint32_t checkIdCount = 0U;
        while (checkIdCount < DATA_MAX_ID)
        {
            /* 判断上次读的位置是否被读过了  如果变成未读的话  说明是一份新的  可以继续使用 */
            if (!dataArrays->dataItems[curID].isReaded)
            {
                /* 获取长度 */
                newShmData.setSize(dataArrays->dataItems[curID].size);
                /* 获取ID */
                newShmData.setId(dataArrays->dataItems[curID].id);
                /* 获取type */
                newShmData.setType(dataArrays->dataItems[curID].type);
                /* 获取是否被读过一次了 */
                newShmData.setIsReaded(dataArrays->dataItems[curID].isReaded);
                /* 获取是否被使用 */
                newShmData.setIsUsed(dataArrays->dataItems[curID].isUsed);
                /* 获取实际数据 */
                newShmData.setData(dataArrays->dataItems[curID].data);
                // 设置下这一份数据已经被读过了
                dataArrays->dataItems[curID].isReaded = true;
                // 更新下下标
                lastReadId = newShmData.getId();
                result = 0;
                break;
            }
            else
            {
                /* 还是处于被读状态  没有新数据  所以直接跳到下一个包 */
                curID = curID + 1;
                if(curID >= DATA_MAX_ID){
                    curID = 0;
                }
                checkIdCount++;
            }
        }


    }

    /**
     * 释放掉这块内存
     * */
    int ShmManager::releaseOnePackage(const ShmData &newShmData)
    {
        dataArrays->dataItems[newShmData.getId()].isUsed = true;
        return 0;
    }

    int ShmManager::writeOnePackage(const void *const data, const int len)
    {
        int result = -1;
        int curID = lastWriteId;
        int checkIdCount = 0;

        while (checkIdCount < DATA_MAX_ID)
        {
            /* 判断上次读的位置是否被读过了  如果变成未读的话  说明是一份新的  可以继续使用 */
            if (dataArrays->dataItems[curID].isUsed)
            {
                /* 先把长度写进去 */
                dataArrays->dataItems[curID].size = len;
                /* 再把type写进去 */
                dataArrays->dataItems[curID].type = 0U;
                /* 再把是否被读写进去 */
                dataArrays->dataItems[curID].isReaded = false;
                /* 再把是否被使用进去 */
                dataArrays->dataItems[curID].isUsed = false;
                /* 把实际数据写进去 */
                if (len > 0)
                {
                  (void)memcpy(dataArrays->dataItems[curID].data, data, len);
                }
                else
                {
                    ; //not to do
                }
                // 把这一包的ID存起来
                lastWriteId = curID;
                result = 0;
                break;
            }
            else
            {
                    /* 还是处于被读状态  没有新数据  所以直接跳到下一个包 */
                    curID = curID + 1;
                    if(curID >= DATA_MAX_ID){
                        curID = 0;
                    }
                    checkIdCount++;
            }
        }


        return result;
    }
} // namespace vis