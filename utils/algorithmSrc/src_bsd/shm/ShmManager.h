//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/5/27.
//

#ifndef VISPECT_SHMMANAGER_H
#define VISPECT_SHMMANAGER_H

#include <cstdint>
#include "ShmData.h"

namespace vis
{

    class ShmManager
    {

    public:
        int openShm(const char *const name, const int allSize, const int onePackageLen = 4194312);
        int init(const int elementsSize);
        int deleteShm(const char *const name);
        int mmapShm();
        int munmapShm();
        int readOnePackage(ShmData &newShmData);
        int releaseOnePackage(const ShmData &newShmData);
        int writeOnePackage(const void *const data, const int len);

    private:
        struct VisShmData
        {
            int size;     /* 4 byte    长度仅仅是指 data的长度*/
            int id;       /* 1 byte*/
            int type;     /* 1 byte  0:H264数据  2：从未被写过的内存 */
            bool isReaded; /* 1 byte  0:false  1:true*/
            bool isUsed;  /*0:false  1:true*/
            uint8_t data[1024*1024*4] /*size byte*/;
        };

        struct VisShmDataList
        {
            struct VisShmData dataItems[5];
        };

        int lastReadId = 0;
        int lastWriteId = 0;

        /* 数据存储的最大ID数 */
        static const long DATA_MAX_ID = 5;

        /* 一包数据的总长度的最大值 */
        const int ONE_SHM_PACKAGE_LENGTH_MAX = 4194312;

        int curOnePackageLen = ONE_SHM_PACKAGE_LENGTH_MAX;
        /*共享内存的FD*/
        int shmFD = -1;

        /*共享内存的大小*/
        long shmSize = 0;

        /*映射出来的内存*/
        VisShmDataList *dataArrays = nullptr;
    };
} // namespace vis
#endif // VISPECT_SHMMANAGER_H
