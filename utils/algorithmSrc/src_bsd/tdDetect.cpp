#include"tdDetect.h"

#ifdef TD_AODS_IN_OUT
#include "config/SchConfig.h"
#endif


#if (defined WIN32 || defined _WIN32 )
#pragma warning(disable:4996)
#endif



using namespace cv;
#ifdef TD_USING_RKNN
#else
    using namespace dnn;
#endif
using namespace std;

namespace td
{
	Detect::Detect() {}

	Detect::~Detect()
	{
		delete yolop_model;
		delete yolov5_model;
	}

	void Detect::setG4RunMode(int mode)
	{
		this->g4_run_mode = mode;
	}

	int Detect::getG4RunMode()
	{
		return this->g4_run_mode;
	}

#ifdef TD_G3_APP_IN_OUT
    int Detect::init(int img_width, int img_height, ROI_RECT roiRect, std::vector<cv::Point> no_detection_area1_input, std::vector<cv::Point> no_detection_area2_input,ALGORITHM_TYPE algorithmType)
#else
	int Detect::init(int img_width, int img_height, ROI_RECT roiRect, std::vector<cv::Point> no_detection_area1_input, std::vector<cv::Point> no_detection_area2_input)
#endif
	{
		/*
		* 正常成功返回0， 有异常返回-1
		*/
		int ret = 0;

		this->input_width = img_width;
		this->input_height = img_height;

		netType = netType_YOLOV5;  // netType_YOLOV5   netType_YOLOP   netType_YOLOV5DS  netType_YOLOV6

#ifdef TD_G3_APP_IN_OUT
		netType = netType_YOLOV5;
#elif defined TD_AODS_IN_OUT
		//netType = netType_YOLOP;
		netType = netType_YOLOV5DS;
#endif

		yolop_model = new YOLOP();
		if (netType == netType_YOLOP)
			ret = yolop_model->init("./model/yolop.ini");  //   "./model/yolop.ini"

		yolov5_model = new YOLOV5();
		if (netType == netType_YOLOV5)
		{
#ifdef TD_G3_APP_IN_OUT
            switch (algorithmType) 
			{
                case ALGORITHM_TYPE_SBST_160_OBJECT_DETECTION:
				{
  #ifdef TD_G3_APP_IN_OUT
                    float classThreshold = G3_Configuration::getInstance().getSbst160OdClassThreshold();
                    if(G3_Configuration::getInstance().getSbst160OdImproveTestEnable() == 1 && G3_Configuration::getInstance().getSbst160OdClassThresholdImproveTest() < classThreshold){
                        classThreshold =  G3_Configuration::getInstance().getSbst160OdClassThresholdImproveTest();
                    }
                    yolov5_model->init("./model/td_SBST_yolov5.ini",classThreshold);
  #else
                    yolov5_model->init("./model/td_SBST_yolov5.ini");
  #endif
                }
                    break;
  #ifdef TD_G3_APP_IN_OUT
				case ALGORITHM_TYPE_DE_BSD_OBJECT_DETECTION:
				{
					float classThreshold = G3_Configuration::getInstance().getDeBsdOdClassThreshold();
					if (G3_Configuration::getInstance().getDeBsdOdImproveTestEnable() == 1 && G3_Configuration::getInstance().getDeBsdOdClassThresholdImproveTest() < classThreshold) {
						classThreshold = G3_Configuration::getInstance().getDeBsdOdClassThresholdImproveTest();
					}
					yolov5_model->init("./model/td_R151_yolov5.ini", classThreshold);
				}
  #else
				yolov5_model->init("./model/td_R151_yolov5.ini");
  #endif
				    break;
#ifdef TD_G3_APP_IN_OUT
                case ALGORITHM_TYPE_FORKLIFT_160_OBJECT_DETECTION:
                {
                    float classThreshold = G3_Configuration::getInstance().getForklift160OdClassThreshold();
                    if (G3_Configuration::getInstance().getForklift160OdImproveTestEnable() == 1 && G3_Configuration::getInstance().getForklift160OdClassThresholdImproveTest() < classThreshold) {
                        classThreshold = G3_Configuration::getInstance().getForklift160OdClassThresholdImproveTest();
                    }
                    yolov5_model->init("./model/td_forklift_yolov5.ini", classThreshold);
                }
#else
                    yolov5_model->init("./model/td_forklift_yolov5.ini");
#endif
                    break;

#ifdef TD_G3_APP_IN_OUT
                case ALGORITHM_TYPE_UK_DD_QRCODE:
                {
                    float classThreshold = G3_Configuration::getInstance().getTdClassThreshold();
                    if(G3_Configuration::getInstance().getTdImproveTestEnable() == 1 && G3_Configuration::getInstance().getTdClassThresholdImproveTest() < classThreshold){
                        classThreshold =  G3_Configuration::getInstance().getTdClassThresholdImproveTest();
                    }
                    yolov5_model->init("./model/td_RGB_yolov5.ini", classThreshold);
                }
#else
                    yolov5_model->init("./model/td_RGB_yolov5.ini");
#endif
                    break;

                case ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION:
                default:
				{
  #ifdef TD_G3_APP_IN_OUT
                    float classThreshold = G3_Configuration::getInstance().getTdClassThreshold();
                    if(G3_Configuration::getInstance().getTdImproveTestEnable() == 1 && G3_Configuration::getInstance().getTdClassThresholdImproveTest() < classThreshold){
                        classThreshold =  G3_Configuration::getInstance().getTdClassThresholdImproveTest();
                    }
                    yolov5_model->init("./model/td_BSD_yolov5.ini",classThreshold);
  #else
                    yolov5_model->init("./model/td_BSD_yolov5.ini");
  #endif
                }
                    break;

            }
#else
			ret = yolov5_model->init("./model/td_yolov5.ini");   //   "./model/td_yolov5.ini"   "./model/test.ini"
#endif
		}

		yolov5ds_model = new YOLOV5DS();
		if (netType == netType_YOLOV5DS)
			ret = yolov5ds_model->init("./model/td_yolov5ds.ini", 1280, 720);  //   "./model/td_yolov5ds.ini"

		yolov6_model = new YOLOV6();
		if (netType == netType_YOLOV6)
		{
#ifdef TD_G3_APP_IN_OUT
			if (algorithmType == ALGORITHM_TYPE_DE_BSD_OBJECT_DETECTION)
			{
				float classThreshold = G3_Configuration::getInstance().getDeBsdOdClassThreshold();
				if (G3_Configuration::getInstance().getDeBsdOdImproveTestEnable() == 1 && G3_Configuration::getInstance().getDeBsdOdClassThresholdImproveTest() < classThreshold) {
					classThreshold = G3_Configuration::getInstance().getDeBsdOdClassThresholdImproveTest();
				}
				ret = yolov6_model->init("./model/td_R151_yolov6.ini", classThreshold);  //   "./model/td_yolov5ds.ini"
			}
#else
			ret = yolov6_model->init("./model/td_R151_yolov6.ini");  //   "./model/td_yolov5ds.ini"
#endif
        }

		IDcnt = 0;

		//默认值
		this->roiRect.x = 0;
		this->roiRect.y = 0;
		this->roiRect.width = 1280;
		this->roiRect.height = 720;

		this->roiRect.x = roiRect.x;
		this->roiRect.y = roiRect.y;
		this->roiRect.width = roiRect.width;
		this->roiRect.height = roiRect.height;

		this->no_detection_area1.clear();
		for (int i = 0; i < no_detection_area1_input.size(); i++)
			this->no_detection_area1.push_back(no_detection_area1_input[i]);

		this->no_detection_area2.clear();
		for (int i = 0; i < no_detection_area2_input.size(); i++)
			this->no_detection_area2.push_back(no_detection_area2_input[i]);


		det_object.clear();
#ifdef TD_USING_TRACK
		det_object_last_frame.clear();
#endif

#ifdef TD_AODS_IN_OUT
		this->isLongCamera = false;
		this->isShortCamera = false;

		vis::SchConfig& conf = vis::SchConfig::getInstance();

		if (conf.getCameraInfo().isLongCamera())
			this->isLongCamera = true;
		else if (conf.getCameraInfo().isShortCamera())
			this->isShortCamera = true;
#endif

		return ret;
	}

	void Detect::detect(cv::Mat srcimg)
	{
		if (srcimg.cols != this->input_width || srcimg.rows != this->input_height)
			resize(srcimg, srcimg, Size(this->input_width, this->input_height), INTER_AREA);

		float class_threshold;

		det_object.clear();

		//detect
		if (netType == netType_YOLOP)
		{
			class_threshold = yolop_model->getClassThreshold();
			yolop_model->detect(srcimg, det_object);
			yolop_model->getResult(this->maskBufDA, this->maskBufLL);
		}
		else if (netType == netType_YOLOV5)
		{
			class_threshold = yolov5_model->getClassThreshold();
			yolov5_model->detect(this->g4_run_mode, srcimg, this->roiRect, this->no_detection_area1, this->no_detection_area2, det_object);
			yolov5_model->getResult();
		}
		else if (netType == netType_YOLOV5DS)
		{
			class_threshold = yolov5ds_model->getClassThreshold();

			this->yolov5ds_model->infer(srcimg, this->no_detection_area1, this->no_detection_area2);
			this->yolov5ds_model->postProcess(det_object);
			//yolov5ds_model->detect(srcimg, this->no_detection_area1, this->no_detection_area2, det_object);

			yolov5ds_model->getResult(this->maskBufDA);
		}
		else if (netType == netType_YOLOV6)
		{
            class_threshold = yolov6_model->getObjThreshold();
			yolov6_model->detect(srcimg, this->no_detection_area1, this->no_detection_area2, det_object);
			yolov6_model->getResult();
		}
		else
		{
			printf("ERROR !!! netType is illegal\n");
		}

		//limit
		for (int i = 0; i < this->det_object.size(); i++)
		{
			Rect rect = this->det_object[i].rect;
			int left = TD_MAX(0, rect.x);
			int right = TD_MIN(srcimg.cols - 1, rect.x + rect.width);
			int top = TD_MAX(0, rect.y);
			int bottom = TD_MIN(srcimg.rows - 1, rect.y + rect.height);
			this->det_object[i].rect = Rect(left, top, right - left, bottom - top);
		}

#ifdef TD_USING_TRACK
		//if (this->g4_run_mode != INDOOR_LOCATION_MODE)  //DD室内定位标志版的识别暂定不采用跟踪
		this->TrackBaseInterFrame(class_threshold, srcimg);
#endif

		std::unique_ptr<RGBDetect> rgbDetect = std::make_unique<RGBDetect>();
		if (this->g4_run_mode == INDOOR_LOCATION_MODE)
		{
			std::vector<DetObject> tmp_det_object;
			for (int i = 0; i < this->det_object.size(); i++)
				tmp_det_object.push_back(this->det_object[i]);
			
			this->det_object.clear();

			for (int i = 0; i < tmp_det_object.size(); i++)
			{
				Rect rect = tmp_det_object[i].rect;
				string label = tmp_det_object[i].label;
				if (label=="RGB")
				{
					rgbDetect->RGBSeg(srcimg(rect));  //计算并解码，同时判断了是否一个正常可用的码
					if (rgbDetect->getRGBOut().rgb_number == -1)
						continue;

				this->RGB_statistics.push_back(rgbDetect->getRGBOut().rgb_number);
				if (RGB_statistics.size() >100)
				{
					RGB_statistics.erase(RGB_statistics.begin(), RGB_statistics.begin() + (RGB_statistics.size() - 100)); // 删除数组的第一个元素
				}
			
				rgbDetect->JudgeAvailable(rect, this->RGB_statistics,this->input_width,this->input_height);  //判断是否给振岩的MCU往TK5端发送


				tmp_det_object[i].shelf_number = rgbDetect->getRGBOut().rgb_number;
				tmp_det_object[i].is_code_info_available = rgbDetect->getRGBOut().rgb_is_available;
				tmp_det_object[i].warehouse_number = 0;
				std::cout << "det_object[i].shelf_number：" << tmp_det_object[i].shelf_number << std::endl;
				std::cout << "是否可用：" << tmp_det_object[i].is_code_info_available << std::endl;

					this->det_object.push_back(tmp_det_object[i]);
				}
				else
				{
					this->det_object.push_back(tmp_det_object[i]);//先把人传出来 后续有需要加功能
				}

			}



			int Mat_width = this->input_width;
			int Mat_height = this->input_height;
			int count = 0;
			int near_centre = 999999;

			for (int i = 0; i < det_object.size(); i++)
			{
                if (strcmp(det_object[i].label,"RGB") == 0)
                {
                    int x_centre = det_object[i].rect.x + (det_object[i].rect.width / 2);
                    int near_abs = abs(x_centre - Mat_width / 2);

                    if (near_abs < near_centre)
                    {
                        near_centre = near_abs;
                    }

                    if (det_object[i].is_code_info_available == true)
                    {
                        count++;
                    }
                }

			}

			if (count >= 2)
			{
				for (int i = 0; i < det_object.size(); i++)
				{
					int x_centre = det_object[i].rect.x + (det_object[i].rect.width / 2);
					int near_abs = abs(x_centre - Mat_width / 2);
					if (near_abs > near_centre)
					{
						det_object[i].is_code_info_available = false;
					}
				}
			}

		}
	}

	objectInfo_t Detect::getResult()
	{
		objectInfo_t obj_info;
		this->initObjInfo(obj_info);

		for (int i = 0; i < det_object.size(); i++)
		{
			if (det_object[i].isShow == false)
				continue;

			object_t obj = {};  // 执行零初始化，避免随机bug

			Rect rect = det_object[i].rect;
			obj.mLeft = rect.x;
			obj.mRight = rect.x + rect.width;
			obj.mTop = rect.y;
			obj.mBottom = rect.y + rect.height;

			obj.mId = det_object[i].Id;
			sprintf(obj.label, det_object[i].label);
			obj.mScore = det_object[i].score;
#ifdef TD_AODS_IN_OUT
#else
			obj.mDetScore = det_object[i].detScore;
			obj.mMaxScore = det_object[i].maxScore;
			obj.mMinScore = det_object[i].minScore;
#endif
			obj.mTrend = det_object[i].trend;

			if (this->g4_run_mode == INDOOR_LOCATION_MODE)
			{
				obj.shelf_number = det_object[i].shelf_number;
				obj.warehouse_number = det_object[i].warehouse_number;
				obj.is_code_info_available = det_object[i].is_code_info_available;
			}

			obj_info.objects.push_back(obj);
		}

		memcpy(obj_info.maskBufDA, this->maskBufDA, TD_MASK_BUF_SIZE * sizeof(unsigned char));
		memcpy(obj_info.maskBufLL, this->maskBufLL, TD_MASK_BUF_SIZE * sizeof(unsigned char));

		return obj_info;
	}

	cv::Mat Detect::draw(cv::Mat srcimg, objectInfo_t object_info, int G4RunMode, int frame_cnt)
	{
		//draw      
		//Mat showimg(srcimg);
		Mat showimg = srcimg.clone();

		cv::Scalar black(0, 0, 0);
		cv::Scalar red(0, 0, 255);
		cv::Scalar blue(255, 0, 0);
		cv::Scalar green(0, 255, 0);
		cv::Scalar yellow(0, 255, 255);
		cv::Scalar purple(255, 0, 255);


		if (this->no_detection_area1.size() > 0)
		    cv::fillPoly(showimg, this->no_detection_area1, black);
		if (this->no_detection_area2.size() > 0)
			cv::fillPoly(showimg, this->no_detection_area2, black);


		std::string frame_cnt_string = format("%d", frame_cnt);
		putText(showimg, frame_cnt_string, Point(showimg.cols - 50, 50), FONT_HERSHEY_SIMPLEX, 0.8, green, 1);


		if (netType == netType_YOLOP || netType == netType_YOLOV5 || netType == netType_YOLOV5DS || netType == netType_YOLOV6)
			drawPred(object_info.objects, showimg, G4RunMode);

		if (netType == netType_YOLOP)
		{
			maskCodec mskCodec;
			mskCodec.setEncodeImgSize(yolop_model->getInpWidth(), yolop_model->getInpHeight());
			cv::Mat dst(showimg.rows, showimg.cols, CV_8UC1);
			if (yolop_model->isRunDA())
			{
				mskCodec.decode(dst, object_info.maskBufDA, TD_MASK_BUF_SIZE);
				drawDASeg(dst, showimg);
			}
			if (yolop_model->isRunLL())
			{
				mskCodec.decode(dst, object_info.maskBufLL, TD_MASK_BUF_SIZE);
				drawLLSeg(dst, showimg);
			}
		}

		if (netType == netType_YOLOV5DS)
		{
			maskCodec mskCodec;
			mskCodec.setEncodeImgSize(640, 384);
			cv::Mat dst(showimg.rows, showimg.cols, CV_8UC1);

			mskCodec.decode(dst, object_info.maskBufDA, TD_MASK_BUF_SIZE);
			drawDASeg(dst, showimg);
		}

		return showimg;
	}

	char* Detect::getImgPath()
	{
		if (netType == netType_YOLOP)
			return yolop_model->getImgPath();
		else if (netType == netType_YOLOV5)
			return yolov5_model->getImgPath();
		else if (netType == netType_YOLOV5DS)
			return yolov5ds_model->getImgPath();
        else if (netType == netType_YOLOV6)
            return yolov6_model->getImgPath();
		else
			return "";
	}

	char* Detect::getModelVersion()
	{
		if (netType == netType_YOLOP)
			return yolop_model->getModelVersion();
		else if (netType == netType_YOLOV5)
			return yolov5_model->getModelVersion();
		else if (netType == netType_YOLOV5DS)
			return yolov5ds_model->getModelVersion();
		else if (netType == netType_YOLOV6)
			return yolov6_model->getModelVersion();
		else
			return "";
	}

#ifdef TD_USING_TRACK
	void Detect::TrackBaseInterFrame(float class_threshold, cv::Mat img)
	{
		// 注意：此跟踪算法有可能导致出现两个目标同一ID的情况

		const float iou_th = 0.5;

		std::vector<DetObject> det_obj_output;

		for (int i = 0; i < det_object.size(); i++)
		{
			DetObject obj = det_object[i];
			string label = obj.label;
			
			if (label =="RGB")
			{
				//std::cout << "rgb_zhiixndu:" << obj.score << std::endl;
				//std::cout << "77777777777777777777777" << std::endl;
				obj.isShow = true;
				det_obj_output.push_back(obj);
			}
			else
			{

			bool has_overlap = false;
			DetObject obj_last_frame;

			for (int j = 0; j < det_object_last_frame.size(); j++)
			{
				obj_last_frame = det_object_last_frame[j];

				if (0 != strcmp(obj.label, obj_last_frame.label))
					continue;

				cv::Rect rect1 = obj.rect;
				cv::Rect rect2 = obj_last_frame.rect;

				float iou_val = get_iou_value(rect1, rect2);

				if (iou_val > iou_th)
				{
					has_overlap = true;
					break;
				}
			}

			if (has_overlap == true)
			{
				obj.Id = obj_last_frame.Id;

#ifdef TD_AODS_IN_OUT
#else
				obj.detScore = obj_last_frame.detScore;
				obj.maxScore = MAX(obj_last_frame.maxScore, obj.score);
				obj.minScore = MIN(obj_last_frame.minScore, obj.score);
#endif

				obj.track_frame_cnt = obj_last_frame.track_frame_cnt + 1;
				obj.isShow = obj_last_frame.isShow;

				obj.trend = obj_last_frame.trend;

				for (int p = 0; p < TD_RECT_SEQ_NUM; p++)
					obj.rectSeq[p] = obj_last_frame.rectSeq[p];

				for (int j = TD_RECT_SEQ_NUM - 2; j >= 0; j--)
					obj.rectSeq[j + 1] = obj.rectSeq[j];
				obj.rectSeq[0] = obj.rect;

				//靠近图像边缘的不加入趋势计算的统计，避免结果错误	
				bool isNear = false;
				const int gap = 5;
				if ((obj.rect.x < gap) || (obj.rect.x + obj.rect.width > img.cols - gap) || (obj.rect.y + obj.rect.height > img.rows - gap))
					isNear = true;
				if (isNear)
					obj.rectSeq[0].x = -1;

				if (obj.track_frame_cnt == 2)
				{
					if (obj.score > class_threshold)
					{
#ifdef TD_AODS_IN_OUT
#else
						obj.detScore = obj.score;
						obj.maxScore = obj_last_frame.detScore;
						obj.minScore = obj_last_frame.detScore;
#endif
						obj.isShow = true;
						det_obj_output.push_back(obj);
					}
				}
				else
				{
					det_obj_output.push_back(obj);
				}
			}
			else
			{
				if (obj.score > class_threshold)
				{
					obj.Id = this->IDcnt;
					this->IDcnt++;
#ifdef TD_AODS_IN_OUT
#else
					obj.detScore = obj.score;
					obj.maxScore = obj.score;
					obj.minScore = obj.score;
#endif

					obj.trend = TD_TREND_DEFAULT;
					for (int k = 0; k < TD_RECT_SEQ_NUM; k++)
						obj.rectSeq[k].x = -1;
					obj.rectSeq[0] = obj.rect;

					obj.track_frame_cnt = 1;
					obj.isShow = false;

						det_obj_output.push_back(obj);
					}
				}
			}

		}

		//用det_obj_output更新当前帧det_object
		det_object.clear();
		for (int i = 0; i < det_obj_output.size(); i++)
			det_object.push_back(det_obj_output[i]);


		///////////////////////////////////////////////////////////////////////////
		//趋势判断
		const int confirm_frame_num = 8;

		for (int i = 0; i < det_object.size(); i++)
		{
			int trend_last_frame = det_object[i].trend;

			if (TD_RECT_SEQ_NUM < confirm_frame_num)
			{
				printf("trend calculate error !!!\n");
				continue;
			}

			bool isFail = false;
			for (int idx = 0; idx < confirm_frame_num; idx++)
				if (det_object[i].rectSeq[idx].x <= 0)
					isFail = true;
			if (isFail == true)
				continue;

			bool isStatic = CalcObjIsStatic(det_object[i], confirm_frame_num); //静止状态判断

			int obj_height_array[confirm_frame_num];
			for (int idx = 0; idx < confirm_frame_num; idx++)
				obj_height_array[idx] = det_object[i].rectSeq[idx].height;

			float k = 0.0f, b = 0.0f;
			LinearRegression(obj_height_array, confirm_frame_num, k, b);

			int obj_x_array[confirm_frame_num];
			int obj_y_array[confirm_frame_num];
			for (int idx = 0; idx < confirm_frame_num; idx++)
			{
				obj_x_array[idx] = det_object[i].rectSeq[idx].x + det_object[i].rectSeq[idx].width * 0.5;
				obj_y_array[idx] = det_object[i].rectSeq[idx].y + det_object[i].rectSeq[idx].height;
			}

			bool isXAlwaysIncrease = true;
			bool isXAlwaysDecrease = true;
			bool isYAlwaysIncrease = true;
			bool isYAlwaysDecrease = true;
			for (int idx = 0; idx < confirm_frame_num - 1; idx++)
			{
				if (obj_x_array[idx] > obj_x_array[idx + 1])
				{
					isXAlwaysDecrease = false;
				}
				else if (obj_x_array[idx] < obj_x_array[idx + 1])
				{
					isXAlwaysIncrease = false;
				}
				else
				{
					isXAlwaysDecrease = false;
					isXAlwaysIncrease = false;
				}

				if (obj_y_array[idx] > obj_y_array[idx + 1])
				{
					isYAlwaysDecrease = false;
				}
				else if (obj_y_array[idx] < obj_y_array[idx + 1])
				{
					isYAlwaysIncrease = false;
				}
				else
				{
					isYAlwaysDecrease = false;
					isYAlwaysIncrease = false;
				}
			}

			//这里判断需要按照顺序，先判断远近变化，最后才判断静止状态
			if (k < -2)
				det_object[i].trend = TD_TREND_FAR_TO_NEAR;
			else if (k > 2)
				det_object[i].trend = TD_TREND_NEAR_TO_FAR;
			else if (isXAlwaysIncrease == true)
				det_object[i].trend = TD_TREND_LEFT_TO_RIGHT;
			else if (isXAlwaysDecrease == true)
				det_object[i].trend = TD_TREND_RIGHT_TO_LEFT;
			else if (isYAlwaysIncrease == true)
				det_object[i].trend = TD_TREND_TOP_TO_BOTTOM;
			else if (isYAlwaysDecrease == true)
				det_object[i].trend = TD_TREND_BOTTOM_TO_TOP;
			else if (isStatic)
				det_object[i].trend = TD_TREND_STATIC;
			else
				det_object[i].trend = trend_last_frame;
		}
		///////////////////////////////////////////////////////////////////////////


		//用当前帧det_object结果更新det_object_last_frame
		det_object_last_frame.clear();
		for (int i = 0; i < det_object.size(); i++)
		{
			//	printf("id is %d, trend is %d, track_frame_cnt = %d\n", det_object[i].Id, det_object[i].trend, det_object[i].track_frame_cnt);
			det_object_last_frame.push_back(det_object[i]);
		}
	}
#endif


	void Detect::initObjInfo(objectInfo_t& objInfo)
	{
		objInfo.objects.clear();

		//alarmInfo_output
		objInfo.alarmInfo.m_PEDESTRIAN_INVASION_CLEARANCES_ALARM_INFO = false;
		objInfo.alarmInfo.m_TRAIN_INVASION_CLEARANCES_ALARM_INFO = false;
		objInfo.alarmInfo.m_OTHER_OBSTACLE_INVASION_CLEARANCES_ALARM_INFO = false;
		objInfo.alarmInfo.m_INTRUSION_OBJECT_DISTANCE_CALCULATE_FAILED_ALARM_INFO = false;
		objInfo.alarmInfo.m_INTRUSION_OBJECT_VELOCITY_CALCULATE_FAILED_ALARM_INFO = false;
		objInfo.alarmInfo.m_CLEARANCES_AREA_LENGTH_TOO_SHORT_ALARM_INFO = false;
		objInfo.alarmInfo.m_CLEARANCES_AREA_RECOGNITION_FAILED_ALARM_INFO = false;
		objInfo.alarmInfo.m_LIDAR_SENSOR_FAULT_ALARM_INFO = false;
		objInfo.alarmInfo.m_LONG_FOCUS_CAMERA_SENSOR_FAULT_ALARM_INFO = false;
		objInfo.alarmInfo.m_SHORT_FOCUS_CAMERA_SENSOR_FAULT_ALARM_INFO = false;
		objInfo.alarmInfo.m_MILLIMETERM_WAVE_RADAR_SENSOR_FAULT_ALARM_INFO = false;
		objInfo.alarmInfo.m_ATP_COMMUNICATION_TIMEOUT_ALARM_INFO = false;
		objInfo.alarmInfo.m_TRAIN_INTERFACE_COMMUNICATION_TIMEOUT_ALARM_INFO = false;
		objInfo.alarmInfo.m_RESERVE = 0;

		//faultInfo_output
		objInfo.faultInfo.m_AODS_SOFTWARE_FATAL_ERROR_FAULT_INFO = false;
		objInfo.faultInfo.m_POINTER_IS_NULL_FAULT_INFO = false;
		objInfo.faultInfo.m_PARAMETER_VALUE_RANGE_ERROR_FAULT_INFO = false;
		objInfo.faultInfo.m_ILLEGAL_RETURN_VALUE_FAULT_INFO = false;
		objInfo.faultInfo.m_STATIC_DATA_CHECK_ERROR_FAULT_INFO = false;
		objInfo.faultInfo.m_CONFIGURATION_DATA_PROTOCOL_VERSION_CHECK_FAILED_FAULT_INFO = false;
		objInfo.faultInfo.m_NEURAL_NETWORK_STRUCTURE_CHECK_FAILED_FAULT_INFO = false;
		objInfo.faultInfo.m_SAMPLE_DATABASE_VERSION_CHECK_FAILED_FAULT_INFO = false;
		objInfo.faultInfo.m_ATP_ELECTRONIC_MAP_VERSION_CHECK_FAILED_FAULT_INFO = false;
		objInfo.faultInfo.m_ATP_COMMUNICATION_PROTOCOL_VERSION_CHECK_FAILED_FAULT_INFO = false;
		objInfo.faultInfo.m_UNEXPECTED_AODS_SOFTWARE_INTERNAL_ERROR_FAULT_INFO = false;
		objInfo.faultInfo.m_PRIMARY_LIDAR_ARITHMETIC_LOGIC_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_STANDBY_LIDAR_ARITHMETIC_LOGIC_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_PRIMARY_LONG_FOCUS_ARITHMETIC_LOGIC_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_STANDBY_LONG_FOCUS_ARITHMETIC_LOGIC_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_PRIMARY_SHORT_FOCUS_ARITHMETIC_LOGIC_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_STANDBY_SHORT_FOCUS_ARITHMETIC_LOGIC_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_PRIMARY_MILLIMETERM_WAVE_RADAR_ARITHMETIC_LOGIC_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_STANDBY_MILLIMETERM_WAVE_RADAR_ARITHMETIC_LOGIC_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_PRIMARY_DECISION_MODULE_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_STANDBY_DECISION_MODULE_UNIT_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_COMMUNICATION_AND_STORAGE_MODULE_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_POWER_MODULE_A_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_POWER_MODULE_B_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_FAN_FAILURE_FAULT_INFO = false;
		objInfo.faultInfo.m_RESERVE = 0;

#ifdef TD_AODS_IN_OUT
		objInfo.mRestrictAreaIsValid = true;
		objInfo.RestrictAreaLenght = -1;
#endif
	}

}