#ifndef __INI_H__
#define __INI_H__


#include <stdlib.h>

typedef int BOOL;
typedef unsigned long DWORD;

#define DEF_PROFILE_NUM_LEN 	64
#define ERROR				-1
#define ERROR_OK				0
#define ERROR_NO_SECTION		1
#define ERROR_NO_KEY			2	

typedef struct _record
{
	char comments[DEF_PROFILE_NUM_LEN];
	char key[DEF_PROFILE_NUM_LEN];
	char value[DEF_PROFILE_NUM_LEN];
	struct _record* next;
}record;

typedef struct _section
{
	record* first;
	record* last;
	unsigned int sizeRecord;
	char comments[DEF_PROFILE_NUM_LEN];
	char name[DEF_PROFILE_NUM_LEN];
	struct _section* next;
}section;

typedef struct _content
{
	unsigned int sizeSection;
	section* first;
	section* last;
}content;

extern bool iniStart(const char* filename);
extern bool iniLoad(const char* filename);
bool iniLoad(const char* lpFile);
extern void iniAddSection(const char* sec, const char* comment);
extern struct _section* iniGetSection(const char* sec);
extern struct _record* iniGetRecord(struct _section* sec, const char* key);
extern char* iniGetValue(const char* sec, const char* key);
extern bool iniSave(const char* filename);
extern int iniRemoveAllSection(const char* section);
extern int iniRemoveSelSection(const char* sec, const char* key);
extern BOOL iniWriteInt(const char* lpSection, const char* lpKey, unsigned int nValue,const char* lpComment);
extern void iniGetInt(const char* lpSection, const char* lpKey, int* nDefault);
extern void iniGetFloat(const char* lpSection, const char* lpKey, float* nDefault);
extern DWORD iniGetString(const char* lpSection, const char* lpKey, char* lpBuffer);
extern BOOL iniWriteString(const char* lpSection, const char* lpKey, const char* lpValue, const char* lpComment);
extern void iniGetBool(const char* lpSection, const char* lpKey, BOOL* bDefault);
extern BOOL iniWriteBool(const char* lpSection, const char* lpKey, BOOL bDefault, const char* lpComment);
extern DWORD iniInvertBool(const char* lpSection, const char* lpKey);

#endif