﻿
#include "tdYOLOV5.h"
#ifdef TD_G3_APP_IN_OUT
#include "G3_Configuration.h"
#endif


#if (defined WIN32 || defined _WIN32 )
#pragma warning(disable:4996)
#endif


#ifdef TD_USING_RKNN
/*-------------------------------------------
Includes
-------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dlfcn.h>
#include <vector>
#include <sys/time.h>

//#define _BASETSD_H




//#include "drm_func.h"
//#include "rga_func.h"

//#include "postprocess.h"

#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>

#ifdef TD_USING_RGA
#include "XuRGAUtils.h"
#endif


#endif

using namespace cv;
#ifdef TD_USING_RKNN
#else
using namespace dnn;
#endif
using namespace std;


#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include <vector>
#include <set>
#include <stdint.h>


#ifdef TD_USING_RGA
	extern XuRGAUtils rga_utils;
#endif

namespace td
{
	const int anchor0[6] = { 10, 13, 16, 30, 33, 23 };
	const int anchor1[6] = { 30, 61, 62, 45, 59, 119 };
	const int anchor2[6] = { 116, 90, 156, 198, 373, 326 };


	inline static int32_t __clip(float val, float min, float max)
	{
		float f = val <= min ? min : (val >= max ? max : val);
		return f;
	}

	static uint8_t qnt_f32_to_affine(float f32, uint32_t zp, float scale)
	{
		float dst_val = (f32 / scale) + zp;
		uint8_t res = (uint8_t)__clip(dst_val, 0, 255);
		return res;
	}

	static float deqnt_affine_to_f32(uint8_t qnt, uint32_t zp, float scale)
	{
		return ((float)qnt - (float)zp) * scale;
	}

	static int process_u8(uint8_t* input, int* anchor, int objNum, int prop_box_size, int grid_h, int grid_w, int height, int width, int stride,
		std::vector<float>& boxes, std::vector<float>& objProbs, std::vector<int>& classId,
		float threshold, uint32_t zp, float scale)
	{

		int validCount = 0;
		int grid_len = grid_h * grid_w;
		float thres = unsigmoid(threshold);
		uint8_t thres_u8 = qnt_f32_to_affine(thres, zp, scale);
		for (int a = 0; a < 3; a++)
		{
			for (int i = 0; i < grid_h; i++)
			{
				for (int j = 0; j < grid_w; j++)
				{
					uint8_t box_confidence = input[(prop_box_size * a + 4) * grid_len + i * grid_w + j];
					if (box_confidence >= thres_u8)
					{
						int offset = (prop_box_size * a) * grid_len + i * grid_w + j;
						uint8_t* in_ptr = input + offset;
						float box_x = sigmoid(deqnt_affine_to_f32(*in_ptr, zp, scale)) * 2.0 - 0.5;
						float box_y = sigmoid(deqnt_affine_to_f32(in_ptr[grid_len], zp, scale)) * 2.0 - 0.5;
						float box_w = sigmoid(deqnt_affine_to_f32(in_ptr[2 * grid_len], zp, scale)) * 2.0;
						float box_h = sigmoid(deqnt_affine_to_f32(in_ptr[3 * grid_len], zp, scale)) * 2.0;
						box_x = (box_x + j) * (float)stride;
						box_y = (box_y + i) * (float)stride;
						box_w = box_w * box_w * (float)anchor[a * 2];
						box_h = box_h * box_h * (float)anchor[a * 2 + 1];
						box_x -= (box_w / 2.0);
						box_y -= (box_h / 2.0);
						boxes.push_back(box_x);
						boxes.push_back(box_y);
						boxes.push_back(box_w);
						boxes.push_back(box_h);

						uint8_t maxClassProbs = in_ptr[5 * grid_len];
						int maxClassId = 0;
						for (int k = 1; k < objNum; ++k)
						{
							uint8_t prob = in_ptr[(5 + k) * grid_len];
							if (prob > maxClassProbs)
							{
								maxClassId = k;
								maxClassProbs = prob;
							}
						}
						//objProbs.push_back(sigmoid(deqnt_affine_to_f32(maxClassProbs, zp, scale)));
						objProbs.push_back(sigmoid(deqnt_affine_to_f32(box_confidence, zp, scale)));

						classId.push_back(maxClassId);
						validCount++;
					}
				}
			}
		}
		return validCount;
	}

	int post_process_u8(uint8_t* input0, uint8_t* input1, uint8_t* input2, char** labels, int objNum, int prop_box_size, int model_in_h, int model_in_w,
		float conf_threshold, float nms_threshold,
		std::vector<uint32_t>& qnt_zps, std::vector<float>& qnt_scales,
		detect_result_group_t* group)
	{
		memset(group, 0, sizeof(detect_result_group_t));

		std::vector<float> filterBoxes;
		std::vector<float> objProbs;
		std::vector<int> classId;

		// stride 8
		int stride0 = 8;
		int grid_h0 = model_in_h / stride0;
		int grid_w0 = model_in_w / stride0;
		int validCount0 = 0;
		validCount0 = process_u8(input0, (int*)anchor0, objNum, prop_box_size, grid_h0, grid_w0, model_in_h, model_in_w,
			stride0, filterBoxes, objProbs, classId, conf_threshold, qnt_zps[0], qnt_scales[0]);

		// stride 16
		int stride1 = 16;
		int grid_h1 = model_in_h / stride1;
		int grid_w1 = model_in_w / stride1;
		int validCount1 = 0;
		validCount1 = process_u8(input1, (int*)anchor1, objNum, prop_box_size, grid_h1, grid_w1, model_in_h, model_in_w,
			stride1, filterBoxes, objProbs, classId, conf_threshold, qnt_zps[1], qnt_scales[1]);

		// stride 32
		int stride2 = 32;
		int grid_h2 = model_in_h / stride2;
		int grid_w2 = model_in_w / stride2;
		int validCount2 = 0;
		validCount2 = process_u8(input2, (int*)anchor2, objNum, prop_box_size, grid_h2, grid_w2, model_in_h, model_in_w,
			stride2, filterBoxes, objProbs, classId, conf_threshold, qnt_zps[2], qnt_scales[2]);

		int validCount = validCount0 + validCount1 + validCount2;
		// no object detect
		if (validCount <= 0)
		{
			return 0;
		}

		std::vector<int> indexArray;
		for (int i = 0; i < validCount; ++i)
		{
			indexArray.push_back(i);
		}

		quick_sort_indice_inverse(objProbs, 0, validCount - 1, indexArray);

		std::set<int> class_set(std::begin(classId), std::end(classId));

		for (auto c : class_set)
		{
			nms_u8(validCount, filterBoxes, classId, indexArray, c, nms_threshold);
		}

		int last_count = 0;
		group->count = 0;
		/* box valid detect target */
		for (int i = 0; i < validCount; ++i)
		{

			if (indexArray[i] == -1 || i >= TD_OBJ_NUMB_MAX_SIZE)
			{
				continue;
			}
			int n = indexArray[i];

			float x1 = filterBoxes[n * 4 + 0];
			float y1 = filterBoxes[n * 4 + 1];
			float x2 = x1 + filterBoxes[n * 4 + 2];
			float y2 = y1 + filterBoxes[n * 4 + 3];
			int id = classId[n];
			float obj_conf = objProbs[i];  //objProbs已经排序好，所以index与filterBoxes及classId不同

			group->results[last_count].box.left = (int)(clamp(x1, 0, model_in_w));
			group->results[last_count].box.top = (int)(clamp(y1, 0, model_in_h));
			group->results[last_count].box.right = (int)(clamp(x2, 0, model_in_w));
			group->results[last_count].box.bottom = (int)(clamp(y2, 0, model_in_h));
			group->results[last_count].prop = obj_conf;  
			char* label = labels[id];
			strncpy(group->results[last_count].name, label, TD_OBJ_NAME_MAX_SIZE);

			// printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left, group->results[last_count].box.top,
			//        group->results[last_count].box.right, group->results[last_count].box.bottom, label);
			last_count++;
		}
		group->count = last_count;

		return 0;
	}

	static int process_fp(float* input, int* anchor, int objNum, int prop_box_size, int grid_h, int grid_w, int height, int width, int stride,
		std::vector<float>& boxes, std::vector<float>& boxScores, std::vector<int>& classId,
		float threshold)
	{

		int validCount = 0;
		int grid_len = grid_h * grid_w;
		float thres_sigmoid = unsigmoid(threshold);
		for (int a = 0; a < 3; a++)
		{
			for (int i = 0; i < grid_h; i++)
			{
				for (int j = 0; j < grid_w; j++)
				{
					float box_confidence = input[(prop_box_size * a + 4) * grid_len + i * grid_w + j];
					if (box_confidence >= thres_sigmoid)
					{
						int offset = (prop_box_size * a) * grid_len + i * grid_w + j;
						float* in_ptr = input + offset;
						float box_x = sigmoid(*in_ptr) * 2.0 - 0.5;
						float box_y = sigmoid(in_ptr[grid_len]) * 2.0 - 0.5;
						float box_w = sigmoid(in_ptr[2 * grid_len]) * 2.0;
						float box_h = sigmoid(in_ptr[3 * grid_len]) * 2.0;
						box_x = (box_x + j) * (float)stride;
						box_y = (box_y + i) * (float)stride;
						box_w = box_w * box_w * (float)anchor[a * 2];
						box_h = box_h * box_h * (float)anchor[a * 2 + 1];
						box_x -= (box_w / 2.0);
						box_y -= (box_h / 2.0);
						boxes.push_back(box_x);
						boxes.push_back(box_y);
						boxes.push_back(box_w);
						boxes.push_back(box_h);

						float maxClassProbs = in_ptr[5 * grid_len];
						int maxClassId = 0;
						for (int k = 1; k < objNum; ++k)
						{
							float prob = in_ptr[(5 + k) * grid_len];
							if (prob > maxClassProbs)
							{
								maxClassId = k;
								maxClassProbs = prob;
							}
						}
						float box_conf_f32 = sigmoid(box_confidence);
						float class_prob_f32 = sigmoid(maxClassProbs);
						boxScores.push_back(box_conf_f32 * class_prob_f32);
						classId.push_back(maxClassId);
						validCount++;
					}
				}
			}
		}
		return validCount;
	}

	int post_process_fp(float* input0, float* input1, float* input2, char** labels, int objNum, int prop_box_size, int model_in_h, int model_in_w,
		int h_offset, int w_offset, float resize_scale, float conf_threshold, float nms_threshold,
		detect_result_group_t* group)
	{
		memset(group, 0, sizeof(detect_result_group_t));

		std::vector<float> filterBoxes;
		std::vector<float> boxesScore;
		std::vector<int> classId;
		int stride0 = 8;
		int grid_h0 = model_in_h / stride0;
		int grid_w0 = model_in_w / stride0;
		int validCount0 = 0;
		validCount0 = process_fp(input0, (int*)anchor0, objNum, prop_box_size, grid_h0, grid_w0, model_in_h, model_in_w,
			stride0, filterBoxes, boxesScore, classId, conf_threshold);

		int stride1 = 16;
		int grid_h1 = model_in_h / stride1;
		int grid_w1 = model_in_w / stride1;
		int validCount1 = 0;
		validCount1 = process_fp(input1, (int*)anchor1, objNum, prop_box_size, grid_h1, grid_w1, model_in_h, model_in_w,
			stride1, filterBoxes, boxesScore, classId, conf_threshold);

		int stride2 = 32;
		int grid_h2 = model_in_h / stride2;
		int grid_w2 = model_in_w / stride2;
		int validCount2 = 0;
		validCount2 = process_fp(input2, (int*)anchor2, objNum, prop_box_size, grid_h2, grid_w2, model_in_h, model_in_w,
			stride2, filterBoxes, boxesScore, classId, conf_threshold);

		int validCount = validCount0 + validCount1 + validCount2;
		// no object detect
		if (validCount <= 0)
		{
			return 0;
		}

		std::vector<int> indexArray;
		for (int i = 0; i < validCount; ++i)
		{
			indexArray.push_back(i);
		}

		quick_sort_indice_inverse(boxesScore, 0, validCount - 1, indexArray);

		nms_fp(validCount, filterBoxes, indexArray, nms_threshold);

		int last_count = 0;
		group->count = 0;
		/* box valid detect target */
		for (int i = 0; i < validCount; ++i)
		{

			if (indexArray[i] == -1 || boxesScore[i] < conf_threshold || last_count >= TD_OBJ_NUMB_MAX_SIZE)
			{
				continue;
			}
			int n = indexArray[i];

			float x1 = filterBoxes[n * 4 + 0];
			float y1 = filterBoxes[n * 4 + 1];
			float x2 = x1 + filterBoxes[n * 4 + 2];
			float y2 = y1 + filterBoxes[n * 4 + 3];
			int id = classId[n];

			group->results[last_count].box.left = (int)((clamp(x1, 0, model_in_w) - w_offset) / resize_scale);
			group->results[last_count].box.top = (int)((clamp(y1, 0, model_in_h) - h_offset) / resize_scale);
			group->results[last_count].box.right = (int)((clamp(x2, 0, model_in_w) - w_offset) / resize_scale);
			group->results[last_count].box.bottom = (int)((clamp(y2, 0, model_in_h) - h_offset) / resize_scale);
			group->results[last_count].prop = boxesScore[i];  //boxesScore已经排序好，所以index与filterBoxes及classId不同
			group->results[last_count].class_index = id;
			char* label = labels[id];
			strncpy(group->results[last_count].name, label, TD_OBJ_NAME_MAX_SIZE);

			// printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left, group->results[last_count].box.top,
			//        group->results[last_count].box.right, group->results[last_count].box.bottom, label);
			last_count++;
		}
		group->count = last_count;

		return 0;
	}




	YOLOV5::YOLOV5()
	{}

	YOLOV5::~YOLOV5()
	{
#ifdef TD_USING_RKNN
		// release
		/*
		ret = rknn_destroy(ctx);
		drm_buf_destroy(&drm_ctx, drm_fd, buf_fd, handle, drm_buf, actual_size);

		drm_deinit(&drm_ctx, drm_fd);
		RGA_deinit(&rga_ctx);
		if (model_data)
			free(model_data);

		if (resize_buf)
			free(resize_buf);
		*/
#endif
	}

#ifdef TD_G3_APP_IN_OUT
    int YOLOV5::init(const char* iniPath, const float algClassThreshold)
#else
	int YOLOV5::init(char* iniPath)
#endif
	{
		sprintf(this->modelVersion, "%s", "0.0.0");

		int ret = 0;

		const bool keep_ratio = true;


		int netWidth;
		int netHeight;
		int objNum;
		float boxThreshold;
#ifdef TD_G3_APP_IN_OUT
#else
		float classThreshold;
#endif
		float classThreshold_person;
		float nmsThreshold;
		float mean[3];
		float std[3];

		char modelpath[200];
		char labelPath[200];
		char imgPath[200];

		iniStart(iniPath);

		iniGetInt("net", "netWidth", &netWidth);
		iniGetInt("net", "netHeight", &netHeight);
		iniGetInt("net", "objNum", &objNum);
		iniGetFloat("net", "boxThreshold", &boxThreshold);
#ifdef TD_G3_APP_IN_OUT
#else
		iniGetFloat("net", "classThreshold", &classThreshold);
#endif
		iniGetFloat("net", "nmsThreshold", &nmsThreshold);
		iniGetFloat("net", "mean0", &mean[0]);
		iniGetFloat("net", "mean1", &mean[1]);
		iniGetFloat("net", "mean2", &mean[2]);
		iniGetFloat("net", "std0", &std[0]);
		iniGetFloat("net", "std1", &std[1]);
		iniGetFloat("net", "std2", &std[2]);

		iniGetString("path", "modelPath", modelpath);
		iniGetString("path", "labelPath", labelPath);
		iniGetString("path", "imgPath", imgPath);


		this->model_width = netWidth;
		this->model_height = netHeight;
		this->model_channel = 3;

		this->objNum = objNum;
		this->prop_box_size = 5 + objNum;

#ifdef TD_G3_APP_IN_OUT
		/* 这个不由ini读取了，改成由MRV220的配置表读取 */
		this->classThreshold = algClassThreshold;
#else
		this->classThreshold = classThreshold;
#endif
		

		this->nmsThreshold = nmsThreshold;
		this->boxThreshold = boxThreshold;

		this->keep_ratio = keep_ratio;

#if (defined WIN32 || defined _WIN32 )
#else

        /* 去掉可能的出现的/r */
        if(modelpath[strlen(modelpath) - 1] == 0x0D){
            modelpath[strlen(modelpath) - 1] = '\0';
        }
        if(labelPath[strlen(labelPath) - 1] == 0x0D){
            labelPath[strlen(labelPath) - 1] = '\0';
        }
        if(imgPath[strlen(imgPath) - 1] == 0x0D){
            imgPath[strlen(imgPath) - 1] = '\0';
        }

#endif

		labels = (char**)malloc(objNum * TD_OBJ_NAME_MAX_SIZE * sizeof(char));
		ret = loadLabelName(labelPath, labels, objNum);

		strcpy(this->imgPath, imgPath);

		for (int i = 0; i < 3; i++)
		{
			this->mean[i] = mean[i];
			this->std[i] = std[i];
		}

#ifdef TD_USING_RKNN
		/* Create the neural network */
		printf("Loading mode...\n");
		int model_data_size = 0;
		unsigned char* model_data;


		int len = strlen(modelpath);
		if (modelpath[len - 4] == 'r' && modelpath[len - 3] == 'k' && modelpath[len - 2] == 'n' && modelpath[len - 1] == 'n')
		{
            model_data = load_model(modelpath, &model_data_size);
			printf("the model format is rknn\n");
		}
		else if (modelpath[len - 3] == 'v' && modelpath[len - 2] == 'i' && modelpath[len - 1] == 's')
		{
            char key1 = '0';
            char key2 = '0';
            char key3 = '0';
            int vis_model_data_size = 0;
            unsigned char* vis_model_data = load_model(modelpath, &vis_model_data_size);
			model_data = decode(vis_model_data, vis_model_data_size, &model_data_size, &key1, &key2, &key3, false, NULL);
            sprintf(this->modelVersion, "%c.%c.%c", key1, key2, key3);   //get version
		}
		else
		{
			printf("Unrecognized model format\n");
			return -1;
		}


		ret = rknn_init(&ctx, model_data, model_data_size, 0);
		if (ret < 0)
		{
			printf("rknn_init error ret=%d\n", ret);
			return -1;
		}

		rknn_sdk_version version;
		ret = rknn_query(ctx, RKNN_QUERY_SDK_VERSION, &version, sizeof(rknn_sdk_version));
		if (ret < 0)
		{
			printf("rknn_init error ret=%d\n", ret);
			return -1;
		}
		printf("sdk version: %s driver version: %s\n", version.api_version, version.drv_version);

		ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
		if (ret < 0)
		{
			printf("rknn_init error ret=%d\n", ret);
			return -1;
		}
		printf("model input num: %d, output num: %d\n", io_num.n_input, io_num.n_output);

		rknn_tensor_attr input_attrs[io_num.n_input];
		memset(input_attrs, 0, sizeof(input_attrs));
		for (int i = 0; i < io_num.n_input; i++)
		{
			input_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
			if (ret < 0)
			{
				printf("rknn_init error ret=%d\n", ret);
				return -1;
			}
			dump_tensor_attr(&(input_attrs[i]));
		}

		rknn_tensor_attr output_attrs[io_num.n_output];
		memset(output_attrs, 0, sizeof(output_attrs));
		for (int i = 0; i < io_num.n_output; i++)
		{
			output_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
			dump_tensor_attr(&(output_attrs[i]));
			if (output_attrs[i].qnt_type != RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC || output_attrs[i].type != RKNN_TENSOR_UINT8)
			{
				fprintf(stderr, "The Demo required for a Affine asymmetric u8 quantized rknn model, but output quant type is %s, output data type is %s\n", get_qnt_type_string(output_attrs[i].qnt_type), get_type_string(output_attrs[i].type));
				return -1;
			}
		}

		for (int i = 0; i < io_num.n_output; ++i)
		{
			out_scales.push_back(output_attrs[i].scale);
			out_zps.push_back(output_attrs[i].zp);
		}

		this->model_channel = 3;

		if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
		{
			printf("model is NCHW input fmt\n");
			this->model_width = input_attrs[0].dims[0];
			this->model_height = input_attrs[0].dims[1];
		}
		else
		{
			printf("model is NHWC input fmt\n");
			this->model_width = input_attrs[0].dims[1];
			this->model_height = input_attrs[0].dims[2];
		}

		printf("model input model_height=%d, model_width=%d, model_channel=%d\n", this->model_height, this->model_width, this->model_channel);
#else
		this->net = readNet(modelpath);
#endif

		return ret;
	}


	void YOLOV5::detect(int g4_run_mode, cv::Mat& frame, ROI_RECT roiRect, std::vector<cv::Point> no_detection_area1, std::vector<cv::Point> no_detection_area2, std::vector<DetObject>& det_object)
	{
		cv::Mat rz_frame;
		if (roiRect.x == 0 && roiRect.y == 0 && roiRect.width == 1280 && roiRect.height == 720)
			rz_frame = frame;
		else
		    rz_frame = frame(cv::Rect(roiRect.x, roiRect.y, roiRect.width, roiRect.height)).clone();


		//cv::namedWindow("frame");
		//cv::imshow("frame", rz_frame);

		float nms_threshold = this->nmsThreshold;
		float class_threshold = this->classThreshold;
#ifdef TD_USING_TRACK
		class_threshold *= 0.5;
#endif

		int newh = 0, neww = 0, padh = 0, padw = 0;
		cv::Mat dstimg = letterbox(rz_frame, model_height, model_width, keep_ratio, &newh, &neww, &padh, &padw);


		for (int k = 0; k < no_detection_area1.size(); k++)
		{
			no_detection_area1[k].x -= roiRect.x;
			no_detection_area1[k].y -= roiRect.y;
			if (no_detection_area1[k].x < 0)
				no_detection_area1[k].x = 0;
			if (no_detection_area1[k].y < 0)
				no_detection_area1[k].y = 0;
		}
		for (int k = 0; k < no_detection_area2.size(); k++)
		{
			no_detection_area2[k].x -= roiRect.x;
			no_detection_area2[k].y -= roiRect.y;
			if (no_detection_area2[k].x < 0)
				no_detection_area2[k].x = 0;
			if (no_detection_area2[k].y < 0)
				no_detection_area2[k].y = 0;
		}


		cv::Scalar color(255, 255, 0);
		if (no_detection_area1.size() > 0)
			fillNoDetectionArea(dstimg, no_detection_area1, rz_frame.cols, rz_frame.rows, newh, neww, padh, padw, color);
		if (no_detection_area2.size() > 0)
			fillNoDetectionArea(dstimg, no_detection_area2, rz_frame.cols, rz_frame.rows, newh, neww, padh, padw, color);

		//imwrite("./out.bmp", dstimg);

#ifdef TD_USING_RKNN
		int status = 0;

		void* drm_buf = NULL;
		int drm_fd = -1;
		int buf_fd = -1; // converted from buffer handle
		unsigned int handle;
		size_t actual_size = 0;
		int img_width = 0;
		int img_height = 0;
		int img_channel = 0;
		//rga_context rga_ctx;
		//drm_context drm_ctx;

		//struct timeval start_time, stop_time;
		int ret;
		//memset(&rga_ctx, 0, sizeof(rga_context));
		//memset(&drm_ctx, 0, sizeof(drm_context));

		//printf("post process config: class_threshold = %.2f, nms_threshold = %.2f\n", class_threshold, nms_threshold);

		// preprocess image
#ifdef TD_USING_RGA    
		rga_utils.imageTransformation(dstimg.cols, dstimg.rows, rga_utils.IMG_TYPE_BGR888, dstimg.data, dstimg.cols, dstimg.rows, rga_utils.IMG_TYPE_RGB888, dstimg.data);
#else
		cvtColor(dstimg, dstimg, COLOR_BGR2RGB);  //#include <opencv2/opencv.hpp>
#endif

		img_height = dstimg.rows;
		img_width = dstimg.cols;
		img_channel = dstimg.channels();

		rknn_input inputs[1];
		memset(inputs, 0, sizeof(inputs));
		inputs[0].index = 0;
		inputs[0].type = RKNN_TENSOR_UINT8;
		inputs[0].size = model_width * model_height * model_channel;
		inputs[0].fmt = RKNN_TENSOR_NHWC;
		inputs[0].pass_through = 0;

		/*
		// DRM alloc buffer
		drm_fd = drm_init(&drm_ctx);
		drm_buf = drm_buf_alloc(&drm_ctx, drm_fd, img_width, img_height, img_channel * 8, &buf_fd, &handle, &actual_size);

		//memcpy(drm_buf, input_data, img_width * img_height * img_channel);
		memcpy(drm_buf, frame.data, img_width * img_height * img_channel);

		void *resize_buf = malloc(model_height * model_width * model_channel);

		// init rga context
		RGA_init(&rga_ctx);
		img_resize_slow(&rga_ctx, drm_buf, img_width, img_height, resize_buf, model_width, model_height);
		*/

		//imwrite("./check.bmp", dstimg);
		inputs[0].buf = dstimg.data;

		rknn_inputs_set(ctx, io_num.n_input, inputs);

		rknn_output outputs[io_num.n_output];
		memset(outputs, 0, sizeof(outputs));
		for (int i = 0; i < io_num.n_output; i++)
		{
			//outputs[i].want_float = 0;
			outputs[i].want_float = 1;
		}

		ret = rknn_run(ctx, NULL);
		ret = rknn_outputs_get(ctx, io_num.n_output, outputs, NULL);

		//imwrite("./out.bmp", rz_frame);
#else
		normalize(dstimg, this->mean, this->std);
		cv::Mat blob = blobFromImage(dstimg);
		this->net.setInput(blob);
		std::vector<cv::Mat> outs;
		this->net.forward(outs, this->net.getUnconnectedOutLayersNames());
#endif

		//std::cout << outs[0].data << endl;

		//post process

		detect_result_group_t detect_result_group;

		//注意！！以后基本不考虑使用u8进行运算，因为对阈值进行u8量化转变后会导致精度损失，如0.65的值变换后就成了0.63，导致出来了低于阈值的框
#ifdef TD_USING_RKNN
	  //post_process_u8((uint8_t*)outputs[0].buf, (uint8_t*)outputs[1].buf, (uint8_t*)outputs[2].buf, this->labels, this->objNum, this->prop_box_size, model_height, model_width, class_threshold, nms_threshold, out_zps, out_scales, &detect_result_group);
		post_process_fp((float*)outputs[0].buf, (float*)outputs[1].buf, (float*)outputs[2].buf, this->labels, this->objNum, this->prop_box_size, model_height, model_width, 0, 0, 1.0f, class_threshold, nms_threshold, &detect_result_group);
#else
		post_process_fp((float*)outs[0].data, (float*)outs[1].data, (float*)outs[2].data, this->labels, this->objNum, this->prop_box_size, model_height, model_width, 0, 0, 1.0f, class_threshold, nms_threshold, &detect_result_group);
#endif


		// Draw Objects
		//char text[256];
		//const unsigned char blue[] = { 0, 0, 255 };
		//const unsigned char white[] = { 255, 255, 255 };
		for (int i = 0; i < detect_result_group.count; i++)
		{
			detect_result_t* det_result = &(detect_result_group.results[i]);
			//sprintf(text, "%s %.2f", det_result->name, det_result->prop);
			//printf("%s @ (%d %d %d %d) %f\n", det_result->name, det_result->box.left, det_result->box.top, det_result->box.right, det_result->box.bottom, det_result->prop);
			int x1 = det_result->box.left;
			int y1 = det_result->box.top;
			int x2 = det_result->box.right;
			int y2 = det_result->box.bottom;

			//cv::Scalar red(0, 0, 255);
			//rectangle(dstimg, Point(x1, y1), Point(x2, y2), red, 2);

			x1 = (x1 - padw) * (float)rz_frame.cols / neww;
			y1 = (y1 - padh) * (float)rz_frame.rows / newh;
			x2 = (x2 - padw) * (float)rz_frame.cols / neww;
			y2 = (y2 - padh) * (float)rz_frame.rows / newh;

			//draw box
		   //cv::Scalar red(0, 0, 255);
			//rectangle(rz_frame, Point(x1, y1), Point(x2, y2), red, 2);


			x1 += roiRect.x;
			x2 += roiRect.x;
			y1 += roiRect.y;
			y2 += roiRect.y;


			//draw box
			//cv::Scalar blue(255, 0, 0);
			//cv::Scalar red(0, 0, 255);
			//rectangle(rz_frame, Point(x1, y1), Point(x2, y2), blue, 2);
			//putText(rz_frame, text, Point(x1, y1 - 12), FONT_HERSHEY_SIMPLEX, 0.8, red, 1);

			DetObject obj;
			obj.rect = cv::Rect(x1, y1, x2 - x1, y2 - y1);
			//obj.label = det_result->name;
			sprintf(obj.label, det_result->name);
			obj.score = det_result->prop;
			//printf("########### obj.score = %f  \n", obj.score);
			obj.isShow = true;
			det_object.push_back(obj);
		}

		//printf("$$$$$$$$$$$$$$$$$$$ dstimg.cols = %d, dstimg.rows = %d\n", dstimg.cols, dstimg.rows);
		//cv::namedWindow("dstimg");
		//cv::imshow("dstimg", dstimg);
		//cv::namedWindow("rz_frame");
		//cv::imshow("rz_frame", rz_frame);

#ifdef TD_USING_RKNN
		ret = rknn_outputs_release(ctx, io_num.n_output, outputs);
#endif

		return;
	}

	void YOLOV5::getResult()
	{
		//do nothing
	}

	char* YOLOV5::getImgPath() { return this->imgPath; }

	float YOLOV5::getClassThreshold() { return this->classThreshold; }

	char* YOLOV5::getModelVersion() { return this->modelVersion; };
}
