//
// Created by xwf on 2021/12/10.
//

#ifndef VISPECT__CAMCAP_H
#define VISPECT__CAMCAP_H

#include <linux/videodev2.h>

#include <string>
#include <vector>

struct camMem_s
{
    void *start;
    uint32_t length;
};

struct camMemInfo_s
{
    std::vector<struct v4l2_plane> plane;
    std::vector<struct camMem_s> memPoint;
};

typedef struct
{
    int videoFd;
    struct v4l2_capability cap;

    std::vector<struct camMemInfo_s> meminfo;
} cameraInfo_t;

class CamCap
{
public:
    CamCap() = default;
    ~CamCap();

    explicit CamCap(const std::string &deviceNode);
    int setDevice(const std::string &deviceNode);

    bool isCamera();
    bool isMplaneCamera();

    int getFmt(struct v4l2_format &fmt);
    int getFmt(uint32_t &fource);
    int getFmt(uint32_t &width, uint32_t &height);
    int getFmt(uint32_t &width, uint32_t &height, uint32_t &fource);
    int setFmt(uint32_t width, uint32_t height, uint32_t fourcc = 0);
    int setFmt(uint32_t fourcc);
    int setFmt(struct v4l2_format fmt);     // 慎用

    int streamOn();
    int streamOff();

    int getFmtData(uint8_t *data, uint32_t dataLen);

private:
    cameraInfo_t _cameraInfo = {-1};

private:
    int memMap();
};

#endif // VISPECT__CAMCAP_H
