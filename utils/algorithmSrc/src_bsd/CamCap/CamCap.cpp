//
// Created by xwf on 2021/12/10.
//

#include <fcntl.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

#include "CamCap.h"


CamCap::CamCap(const std::string &deviceNode)
{
    setDevice(deviceNode);
}

int CamCap::setDevice(const std::string &deviceNode)
{
    _cameraInfo.videoFd = open(deviceNode.c_str(), O_RDWR);
    if (_cameraInfo.videoFd < 0)
    {
        return _cameraInfo.videoFd;
    }

    // 获取指定节点的能力
    memset(&_cameraInfo.cap, 0, sizeof(_cameraInfo.cap));
    if (ioctl(_cameraInfo.videoFd, VIDIOC_QUERYCAP, &_cameraInfo.cap) < 0)
    {
        return -1;
    }

    return _cameraInfo.videoFd;
}

bool CamCap::isCamera()
{
    bool ret = false;

    ret = (_cameraInfo.cap.capabilities & (V4L2_CAP_VIDEO_CAPTURE_MPLANE | V4L2_CAP_VIDEO_CAPTURE))
          != 0;

    return ret;
}

bool CamCap::isMplaneCamera()
{
    return (_cameraInfo.cap.capabilities & V4L2_CAP_VIDEO_CAPTURE_MPLANE) != 0;
}

int CamCap::getFmt(v4l2_format &fmt)
{
    memset(&fmt, 0, sizeof(fmt));

    if (isMplaneCamera())
    {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    }
    else if (isCamera())
    {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    }
    else
    {
        return -1;
    }

    if (ioctl(_cameraInfo.videoFd, VIDIOC_G_FMT, &fmt))
    {
        return -1;
    }

    return 0;
}

int CamCap::setFmt(uint32_t width, uint32_t height, uint32_t fourcc)
{
    int ret = 0;
    struct v4l2_format fmt = {0};

    ret = getFmt(fmt);
    if (ret < 0)
    {
        return ret;
    }

    if (isMplaneCamera())
    {
        fmt.fmt.pix_mp.width = width;
        fmt.fmt.pix_mp.height = height;
        if (fourcc > 0)
        {
            fmt.fmt.pix_mp.pixelformat = fourcc;
        }
    }
    else if (isCamera())
    {
        fmt.fmt.pix.width = width;
        fmt.fmt.pix.height = height;
        if (fourcc > 0)
        {
            fmt.fmt.pix.pixelformat = fourcc;
        }
    }

    if (ioctl(_cameraInfo.videoFd, VIDIOC_S_FMT, &fmt) < 0)
    {
        return -1;
    }

    return 0;
}

int CamCap::setFmt(uint32_t fourcc)
{
    int ret = 0;
    struct v4l2_format fmt = {0};

    ret = getFmt(fmt);
    if (ret < 0)
    {
        return ret;
    }

    if (isMplaneCamera())
    {
        fmt.fmt.pix_mp.pixelformat = fourcc;
    }
    else if (isCamera())
    {
        fmt.fmt.pix.pixelformat = fourcc;
    }

    if (ioctl(_cameraInfo.videoFd, VIDIOC_S_FMT, &fmt) < 0)
    {
        return -1;
    }

    return 0;
}

int CamCap::memMap()
{
    struct v4l2_requestbuffers req = {0};
    struct v4l2_buffer buf = {0};
    struct v4l2_format fmt = {0};

    // 默认3个缓冲
    _cameraInfo.meminfo.resize(3);

    if (isMplaneCamera())
    {
        req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    }
    else if (isCamera())
    {
        req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    }
    else
    {
        return -1;
    }
    req.count = _cameraInfo.meminfo.size();
    req.memory = V4L2_MEMORY_MMAP;

    // 申请缓存
    if (ioctl(_cameraInfo.videoFd, VIDIOC_REQBUFS, &req) < 0)
    {
        return -1;
    }

    if (getFmt(fmt) < 0)
    {
        return -1;
    }

    // 初始化获取每个缓存
    for (int memIndex = 0; memIndex < req.count; ++memIndex)
    {
        memset(&buf, 0, sizeof(buf));

        if (isMplaneCamera())
        {
            buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
            // 获取到多平面的平面个数
            buf.length = fmt.fmt.pix_mp.num_planes;
            _cameraInfo.meminfo[memIndex].plane.resize(fmt.fmt.pix_mp.num_planes);
            _cameraInfo.meminfo[memIndex].memPoint.resize(fmt.fmt.pix_mp.num_planes);
            buf.m.planes = _cameraInfo.meminfo[memIndex].plane.data();
        }
        else
        {
            buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
            _cameraInfo.meminfo[memIndex].memPoint.resize(1);
        }

        buf.index = memIndex;
        buf.memory = V4L2_MEMORY_MMAP;

        // 获取单个缓存信息
        if (ioctl(_cameraInfo.videoFd, VIDIOC_QUERYBUF, &buf) < 0)
        {
            return -1;
        }

        // 如果是多平面缓存的话
        if (isMplaneCamera())
        {
            // 映射每个平面
            for (int planeIndex = 0; planeIndex < _cameraInfo.meminfo[memIndex].plane.size();
                 ++planeIndex)
            {
                buf.m.planes = _cameraInfo.meminfo[memIndex].plane.data() + planeIndex;
                // 内存映射
                _cameraInfo.meminfo[memIndex].memPoint[planeIndex].start =
                    mmap(NULL, buf.m.planes[planeIndex].length, PROT_READ | PROT_WRITE, MAP_SHARED,
                         _cameraInfo.videoFd, buf.m.planes[planeIndex].m.mem_offset);
                if (NULL == _cameraInfo.meminfo[memIndex].memPoint[planeIndex].start)
                {
                    return -1;
                }
                // 映射的内存大小
                _cameraInfo.meminfo[memIndex].memPoint[planeIndex].length =
                    buf.m.planes[planeIndex].length;

                printf(" map buffer index: %d, meminfo: %p, len: %x, offset: %x\n", memIndex,
                       _cameraInfo.meminfo[memIndex].memPoint[planeIndex].start,
                       _cameraInfo.meminfo[memIndex].memPoint[planeIndex].length,
                       buf.m.planes[planeIndex].m.mem_offset);
            }
        }
        else // 如果是单平面
        {
            _cameraInfo.meminfo[memIndex].memPoint[0].start =
                mmap(NULL, buf.length, PROT_WRITE | PROT_READ, MAP_SHARED, _cameraInfo.videoFd,
                     buf.m.offset);
            if (NULL == _cameraInfo.meminfo[memIndex].memPoint[0].start)
            {
                return -1;
            }
            _cameraInfo.meminfo[memIndex].memPoint[0].length = buf.length;
        }
    }

    return 0;
}

int CamCap::setFmt(struct v4l2_format fmt)
{
    if (ioctl(_cameraInfo.videoFd, VIDIOC_S_FMT, &fmt) < 0)
    {
        return -1;
    }
    return 0;
}

int CamCap::getFmt(uint32_t &width, uint32_t &height, uint32_t &fource)
{
    v4l2_format fmt = {0};
    int ret = 0;

    ret = getFmt(fmt);
    if (ret < 0)
    {
        return ret;
    }

    if (isMplaneCamera())
    {
        width = fmt.fmt.pix_mp.width;
        height = fmt.fmt.pix_mp.height;
        fource = fmt.fmt.pix_mp.pixelformat;
    }
    else if (isCamera())
    {
        width = fmt.fmt.pix.width;
        height = fmt.fmt.pix.height;
        fource = fmt.fmt.pix.pixelformat;
    }
    else
    {
        return -1;
    }

    return 0;
}

int CamCap::getFmt(uint32_t &width, uint32_t &height)
{
    uint32_t fource = 0;

    return getFmt(width, height, fource);
}

int CamCap::getFmt(uint32_t &fource)
{
    uint32_t h;
    uint32_t w;

    return getFmt(w, h, fource);
}

int CamCap::streamOn()
{
    enum v4l2_buf_type type;
    struct v4l2_buffer buf = {0};

    if (memMap() < 0)
    {
        return -1;
    }

    if (isMplaneCamera())
    {
        type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    }
    else if (isCamera())
    {
        type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    }
    else
    {
        return -1;
    }

    if (ioctl(_cameraInfo.videoFd, VIDIOC_STREAMON, &type) < 0)
    {
        return -1;
    }

    // buf入队列
    for (int memIndex = 0; memIndex < _cameraInfo.meminfo.size(); ++memIndex)
    {
        if (isMplaneCamera())
        {
            for (int planeIndex = 0; planeIndex < _cameraInfo.meminfo[memIndex].plane.size();
                 planeIndex++)
            {
                memset(&buf, 0, sizeof(buf));
                buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
                buf.memory = V4L2_MEMORY_MMAP;
                buf.index = memIndex;
                buf.length = _cameraInfo.meminfo[memIndex].memPoint.size();
                buf.m.planes = _cameraInfo.meminfo[memIndex].plane.data();
                if (ioctl(_cameraInfo.videoFd, VIDIOC_QBUF, &buf) < 0)
                {
                    return -1;
                }
            }
        }
        else
        {
            memset(&buf, 0, sizeof(buf));
            buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
            buf.memory = V4L2_MEMORY_MMAP;
            buf.index = memIndex;
            if (ioctl(_cameraInfo.videoFd, VIDIOC_QBUF, &buf) < 0)
            {
                return -1;
            }
        }
    }

    return 0;
}

int CamCap::streamOff()
{
    enum v4l2_buf_type type;

    if (memMap() < 0)
    {
        return -1;
    }

    if (isMplaneCamera())
    {
        type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    }
    else if (isCamera())
    {
        type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    }
    else
    {
        return -1;
    }

    if (ioctl(_cameraInfo.videoFd, VIDIOC_STREAMOFF, &type) < 0)
    {
        return -1;
    }

    return 0;
}

int CamCap::getFmtData(uint8_t *data, uint32_t dataLen)
{
    struct v4l2_buffer buf = {0};
    struct v4l2_plane lane = {0};
    int ret = 0;

    if (NULL == data)
    {
        return -1;
    }

    if (isMplaneCamera())
    {
        buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
        buf.length = 1;       // 默认取第0个plane
        buf.m.planes = &lane; // &lane;
    }
    else if (isCamera())
    {
        buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    }
    buf.memory = V4L2_MEMORY_MMAP;

    // 出队列
    if (ioctl(_cameraInfo.videoFd, VIDIOC_DQBUF, &buf))
    {
        return -1;
    }

    if (isMplaneCamera())
    {
        // data = (uint8_t *)_cameraInfo.meminfo[buf.index].memPoint[buf.length].start;
        ret = lane.bytesused;
        if (ret > dataLen)
        {
            ret = dataLen;
        }
        printf("[%d] cp %d Byte, point %p, lane : %d\n", __LINE__, ret,
               _cameraInfo.meminfo[buf.index].memPoint[buf.length].start, buf.length);
        memcpy(data, _cameraInfo.meminfo[buf.index].memPoint[buf.length - 1].start, ret);
        printf("[%d] cp %d Byte, point %p, lane : %d\n", __LINE__, ret,
               _cameraInfo.meminfo[buf.index].memPoint[buf.length].start, buf.length);
    }
    else
    {
        // data = (uint8_t *)_cameraInfo.meminfo[buf.index].memPoint[0].start;
        ret = buf.bytesused;
        if (ret > dataLen)
        {
            ret = dataLen;
        }
        memcpy(data, _cameraInfo.meminfo[buf.index].memPoint[0].start, ret);
    }

    // 入队列
    if (ioctl(_cameraInfo.videoFd, VIDIOC_QBUF, &buf))
    {
        return -1;
    }

    return ret;
}

CamCap::~CamCap()
{
    streamOff();
}

int save_frame_to_file(const char *str, void *start, int length)
{
    FILE *fp = NULL;

    printf("save to %s, %d Byte\n", str, length);

    fp = fopen(str, "a+"); // save more frames
    if (!fp)
    {
        printf(" Open %s error\n", (char *)str);

        return -1;
    }

    if (fwrite(start, length, 1, fp))
    {
        fclose(fp);

        return 0;
    }
    else
    {
        printf(" Write file fail (%s)\n", strerror(errno));
        fclose(fp);

        return -1;
    }

    return 0;
}

#if 0
int main()
{
    int ret = 0;
    uint8_t *data;
    CamCap cap;

    data = (uint8_t *)malloc(1024 * 1024 * 2);

    if (cap.setDevice("/dev/video0") < 0)
    {
        perror("setDevice fail");
        return -1;
    }

    if (cap.setFmt(720, 576, V4L2_PIX_FMT_UYVY) < 0)
    {
        perror("setFmt fail");
        return -1;
    }

    if (cap.streamOn() < 0)
    {
        perror("streamOn fail");
        return -1;
    }

    for (int i = 0; i < 10; ++i)
    {
        ret = cap.getFmtData(data, 1024 * 1024 * 2);
        if (ret < 0)
        {
            perror("getFmtData fail");
            return -1;
        }
        else if (0 == ret)
        {
            printf("timeout\n");
            continue;
        }
        printf("get fmt data size : %d\n", ret);

        save_frame_to_file("./cap.yuv", data, ret);

        printf("get fmt data size : %d\n", ret);
    }

    return 0;
}
#endif