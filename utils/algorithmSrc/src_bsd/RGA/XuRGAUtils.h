//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/23.
//

#ifndef VIS_G3_SOFTWARE_XURGAUTILS_H
#define VIS_G3_SOFTWARE_XURGAUTILS_H


#include <stdint.h>
#include <sys/types.h>
#include <math.h>
#include <fcntl.h>
#include <signal.h>
#include <time.h>
#include <stdint.h>
#include <sys/types.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <unistd.h>
#include <sys/mman.h>
#include <linux/stddef.h>


#include <rga/RockchipRga.h>
#include <rga/RgaUtils.h>

class XuRGAUtils {
public:
    /* 格式转换中对应的格式  这里只写一部分可能用到   后续还可以增加 可增加的类型参考 _Rga_SURF_FORMAT */
    static const int IMG_TYPE_RGB888 = RK_FORMAT_RGB_888;
    static const int IMG_TYPE_BGR888 = RK_FORMAT_BGR_888;
    static const int IMG_TYPE_RGBA8888 = RK_FORMAT_RGBA_8888;
    static const int IMG_TYPE_BGRA8888 = RK_FORMAT_BGRA_8888;
    static const int IMG_TYPE_NV12 = RK_FORMAT_YCrCb_420_SP;
    static const int IMG_TYPE_NV21 = RK_FORMAT_YCbCr_420_SP;
    static const int IMG_TYPE_UYVY_422 = RK_FORMAT_UYVY_422;


    /* 翻转类型--水平翻转 */
    static const int FLIP_TYPE_H = HAL_TRANSFORM_FLIP_H;
    /* 翻转类型--垂直翻转 */
    static const int FLIP_TYPE_V = HAL_TRANSFORM_FLIP_V;


    /* 旋转类型--顺时针旋转90度 */
    static const int ROTATION_TYPE_90 = HAL_TRANSFORM_ROT_90;
    /* 旋转类型--顺时针旋转180度 */
    static const int ROTATION_TYPE_180 = HAL_TRANSFORM_ROT_180;
    /* 旋转类型--顺时针旋转270度 */
    static const int ROTATION_TYPE_270 = HAL_TRANSFORM_ROT_270;


    /**
     * 把两张图片合成成一张左右分屏的图片
     * ps：
     * 1、该效果是裁剪图片再合成而不是缩放后再合成
     * 2、输入的图像数据仅支持RGB888格式的数据且宽高要一样
     * 3、输出的图像数据跟输入的图像数据格式一致且宽高一致
     *
     *
     * @param leftWidth ： 需要放在左边图片的宽
     * @param leftHeight : 需要放在左边图片的高
     * @param leftBuf : 需要放在左边的图片的数据内存指针
     * @param rightWidth ： 需要放在右边图片的宽
     * @param righttHeight : 需要放在右边图片的高
     * @param rightBuf : 需要放在右边的图片的数据内存指针  ***最终合成完成的数据也是在这个内存中***
     *
     * @return 合成后图像数据的大小
     * */
    int synthesisImg_LR(const int leftWidth, const int leftHeight, const uint8_t *leftBuf, const int rightWidth,
                        const int rightHeight, const uint8_t *rightBuf);

    /**
     * 图像转换(格式转换、缩放、裁剪、翻转、旋转)
     *
     * @param srcWidth : 输入图像的宽度
     * @param srcHeight : 输入图像的高度
     * @param srcFormat : 输入图像的格式
     * @param srcBuf : 输入图像数据的内存指针
     * @param dstWidth : 输出图像的宽度
     * @param dstHeight : 输出图像的高度
     * @param dstFormat : 输出图像的格式
     * @param dstBuf : 输入图像数据的内存指针
     * @param isClip : 是否使用裁剪的形式缩小图片（原理是从(clipStartX，clipStartY)到(clipStartX+dstWidth,clipStartY,dstHeight)裁出一个矩形，所以输出必须小于输入）
     * @param clipStartX : 开始裁剪的起点的X坐标
     * @param clipStartY : 开始裁剪的起点的Y坐标
     * @param flipType : 翻转类型（只支持水平翻转和垂直翻转）    翻转跟旋转只能开启一个  一个不为-1 另一个必须是-1
     * @param rotationType : 旋转类型（目前只支持顺时针旋转90°、180°、270°）
     *
     * @return  输出图像的长度
     * */
    int imageTransformation(const int srcWidth, const int srcHeight, const int srcFormat, const uint8_t *srcBuf,
                            const int dstWidth, const int dstHeight, const int dstFormat, const uint8_t *dstBuf,
                            const bool isClip = false, const int clipStartX = 0, const int clipStartY = 0,
                            const int flipType = -1, const int rotationType = -1);


private:
    RockchipRga rkRga;

};


#endif //VIS_G3_SOFTWARE_XURGAUTILS_H
