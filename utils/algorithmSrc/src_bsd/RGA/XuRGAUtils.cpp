//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/23.
//

#include <sys/time.h>
#include "XuRGAUtils.h"

int XuRGAUtils::synthesisImg_LR(const int leftWidth, const int leftHeight, const uint8_t *leftBuf, const int rightWidth,
                                const int righttHeight, const uint8_t *rightBuf) {
    int ret = -1;
    /* 定义RGA需要的图像信息  所有给RGA的图像内容跟需要进行什么操作 都是封装在这个结构体里面再给到RGA的 */
    rga_info_t rgasrc;
    memset(&rgasrc, 0, sizeof(rga_info_t));
    rgasrc.fd = -1;
    rgasrc.mmuFlag = 1;
    rgasrc.virAddr = (char *)leftBuf;
    rga_info_t rgadst;
    memset(&rgadst, 0, sizeof(rga_info_t));
    rgadst.fd = -1;
    rgadst.mmuFlag = 1;
    rgadst.virAddr = (char *)rightBuf;

    /**
     * 这里矩形的设置要注意  贴图的原理是把SRC贴到DST上
     * SRC裁剪出一个区域是需要显示的区域    然后DST裁剪出一个区域   这个区域就不是DST需要显示的区域  而是DST准备让SRC贴上去的位置
     *（此点细节文档没有说明，按照裁剪的那一节描述  容易把DST裁剪的区域理解成是需要显示的  此大坑要注意   耗时半天多才试出来）
     * */
    /* 设置好左边图像的图像信息  左边的图就从(0,0)到(leftWidth/2,leftHeight)切矩形或者整张贴上去 因为它在最低层，无所谓 */
    rga_set_rect(&rgasrc.rect, 0, 0, leftWidth/2, leftHeight, leftWidth/*stride*/, leftHeight, RK_FORMAT_RGB_888);
    /* 设置好右边图像的图像信息  右边的图就从(rightWidth / 2,0)到(rightWidth,leftHeight)切矩形 */
    rga_set_rect(&rgadst.rect, 0, 0, rightWidth / 2, righttHeight, rightWidth/*stride*/, righttHeight, RK_FORMAT_RGB_888);

    /* 设置合成模式为不改变透明度直接贴上去 */
    rgasrc.blend = 0xFF0100;

    /********** 定义两个时针看下合成的耗时 （仅仅测试使用） **********/
    struct timeval tpend1, tpend2;
    long usec1 = 0;
    gettimeofday(&tpend1, NULL);
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgasrc, &rgadst, NULL);

    gettimeofday(&tpend2, NULL);
    usec1 = 1000 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000;
//    printf("synthesisImg_LR cost_time=%ld ms   ret = %d \n   ", usec1,ret);

    /* 判断下是否成功 */
    if (ret) {
        printf("synthesisImg_LR error : %s\n",
               strerror(errno));
    } else {
        /* 合成完成的图像是在rightBuf中  故不需要在复制一次 */

        /* 计算出输出图像的数据大小 */
        ret = rightWidth * righttHeight * 3;
    }
    return ret;
}

int XuRGAUtils::imageTransformation(const int srcWidth, const int srcHeight, const int srcFormat, const uint8_t *srcBuf,
                                    const int dstWidth, const int dstHeight, const int dstFormat, const uint8_t *dstBuf,
                                    const bool isClip, const int clipStartX, const int clipStartY, const int flipType,
                                    const int rotationType) {
    int ret = -1;
    /* 定义RGA需要的图像信息  所有给RGA的图像内容跟需要进行什么操作 都是封装在这个结构体里面再给到RGA的 */
    rga_info_t rgasrc;
    memset(&rgasrc, 0, sizeof(rga_info_t));
    rgasrc.fd = -1;
    rgasrc.mmuFlag = 1;
    rgasrc.virAddr = (char *)srcBuf;
    rga_info_t rgadst;
    memset(&rgadst, 0, sizeof(rga_info_t));
    rgadst.fd = -1;
    rgadst.mmuFlag = 1;
    rgadst.virAddr = (char *)dstBuf;


    /* 设置好输入输出图像的图像信息 由于允许输出输入大小不一样 那么就可能是缩放或者裁剪 故这里要根据类型调整输出图像的实际宽高 */
    if(isClip){
        /* 设置好输入图像的图像信息 */
        rga_set_rect(&rgasrc.rect, clipStartX, clipStartY, srcWidth, srcHeight, srcWidth/*stride*/, srcHeight, srcFormat);
        /* 如果是裁剪的话  那么最后的图像实际宽高就是裁剪出来的区域的大小 */
        rga_set_rect(&rgadst.rect, 0, 0, dstWidth, dstHeight, dstWidth/*stride*/, dstHeight, dstFormat);
    }else{
        /* 设置好输入图像的图像信息 */
        rga_set_rect(&rgasrc.rect, 0, 0, srcWidth, srcHeight, srcWidth/*stride*/, srcHeight, srcFormat);
        /* 如果是缩放的话  那么最后的图像实际宽高就是缩放后大小 */
        rga_set_rect(&rgadst.rect, 0, 0, dstWidth, dstHeight, dstWidth/*stride*/, dstHeight, dstFormat);

    }

    /* 看看是不是需要翻转 */
    if(flipType != -1){
        rgasrc.rotation = flipType;

    }else {
        /* 如果不需要翻转  那么看看是不是需要缩放 */
        if(rotationType != -1){
            rgasrc.rotation = rotationType;
        }
    }

    /********** 定义两个时针看下合成的耗时 （仅仅测试使用） **********/
    struct timeval tpend1, tpend2;
    long usec1 = 0;
    gettimeofday(&tpend1, NULL);
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgasrc, &rgadst, NULL);

    gettimeofday(&tpend2, NULL);
    usec1 = 1000 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000;
//    printf("image transformation  cost_time=%ld ms   ret = %d   dstFormat = %d  \n   ", usec1,ret,dstFormat);

    if (ret != 0) {
        printf("image transformation error : %s\n",
               strerror(errno));
    } else {
        /* RGA处理的时候已经放到输出的内存里面了  所以这里判断下输出的图像格式  计算出数据的大小就行了 */
        switch (dstFormat) {
            case IMG_TYPE_NV21:
            case IMG_TYPE_NV12:{
                ret = dstWidth * dstHeight * 3 / 2;
            }
                break;

            case IMG_TYPE_UYVY_422:{
                ret = dstWidth * dstHeight * 2;
            }
                break;

            case IMG_TYPE_RGB888:
            case IMG_TYPE_BGR888:{
                ret = dstWidth * dstHeight * 3;
            }
                break;

            case IMG_TYPE_RGBA8888:
            case IMG_TYPE_BGRA8888:{
                ret = dstWidth * dstHeight * 4;
            }
                break;
        }
    }
    return ret;
}

