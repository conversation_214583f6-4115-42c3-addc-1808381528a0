#ifndef _MASK_CODEC_H_
#define _MASK_CODEC_H_


#include "../tdShareDefine.h"

class maskCodec
{
public:
	maskCodec();
	~maskCodec();
	void encode(float *mask, unsigned char *buf, const int dim);
	void decode(cv::Mat &output, unsigned char *buf, const int dim);
	void setEncodeImgSize(int imgWidth, int imgHeight);
	void saveBufToDAT(char *save_path, unsigned char *buf, const int dim);
	void readDATToBuf(char *read_path, unsigned char *buf, const int dim);

private:
	int encodeImgWidth;
	int encodeImgHeight;
};


#endif