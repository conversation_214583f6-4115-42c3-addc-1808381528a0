#include "maskCodec.h"

#if (defined WIN32 || defined _WIN32 )
    #pragma warning(disable:4996)
#endif


maskCodec::maskCodec()
{}

maskCodec::~maskCodec()
{}


void maskCodec::encode(float *mask, unsigned char *buf, const int dim)
{
	int area = encodeImgWidth * encodeImgHeight;
	int dim_cnt = 0;

	for (int i = 0; i < area; i += 8)
	{
		unsigned char val = 0;
		val = mask[i] < mask[area + i] > 0 ? val | 1 : val;
		val = mask[i+1] < mask[area + i+1] > 0 ? val | 2 : val;
		val = mask[i+2] < mask[area + i+2] > 0 ? val | 4 : val;
		val = mask[i+3] < mask[area + i+3] > 0 ? val | 8 : val;
		val = mask[i+4] < mask[area + i+4] > 0 ? val | 16 : val;
		val = mask[i+5] < mask[area + i+5] > 0 ? val | 32 : val;
		val = mask[i+6] < mask[area + i+6] > 0 ? val | 64 : val;
		val = mask[i+7] < mask[area + i+7] > 0 ? val | 128 : val;

		buf[dim_cnt++] = val;
	}
}

void maskCodec::decode(cv::Mat &output, unsigned char *buf, const int dim)
{
	//int img_width = output.cols;
	//int img_height = output.rows;

	cv::Mat imgMask(encodeImgHeight, encodeImgWidth, CV_8UC1);  // encodeImgHeight-384   encodeImgWidth-640
	int dim_cnt = 0;
	for (int i = 0; i < imgMask.rows; i++)
	{
		for (int j = 0; j < imgMask.cols; j += 8)
		{
			unsigned char val = buf[dim_cnt++];

			imgMask.at<uchar>(i, j) = val & 1 ? 255 : 0;
			imgMask.at<uchar>(i, j + 1) = val & 2 ? 255 : 0;
			imgMask.at<uchar>(i, j + 2) = val & 4 ? 255 : 0;
			imgMask.at<uchar>(i, j + 3) = val & 8 ? 255 : 0;
			imgMask.at<uchar>(i, j + 4) = val & 16 ? 255 : 0;
			imgMask.at<uchar>(i, j + 5) = val & 32 ? 255 : 0;
			imgMask.at<uchar>(i, j + 6) = val & 64 ? 255 : 0;
			imgMask.at<uchar>(i, j + 7) = val & 128 ? 255 : 0;
		}
	}

	//cv::namedWindow("show");
	//cv::imshow("show", imgMask);
	//cv::waitKey(0);

	bool keep_ratio = true;
	int org_width = imgMask.cols;
	int org_height = imgMask.rows;
	int output_width = output.cols;
	int output_height = output.rows;
	if (keep_ratio && output_height > org_height && output_width > org_width)
	{
		float scale = TD_MIN((float)org_height / output_height, (float)org_width / output_width);
		int pad_h = (org_height - scale * output_height) * 0.5;
		int pad_w = (org_width - scale * output_width) * 0.5;
		cv::Mat img_new = imgMask(cv::Rect(pad_w, pad_h, scale * output_width, scale * output_height));
		cv::resize(img_new, output, cv::Size(output.cols, output.rows), cv::INTER_LINEAR);
	}	
}

void maskCodec::setEncodeImgSize(int imgWidth, int imgHeight)
{
	this->encodeImgWidth = imgWidth;
	this->encodeImgHeight = imgHeight;
}


void maskCodec::saveBufToDAT(char *save_path, unsigned char *buf, const int dim)
{
	FILE *fp;
	fp = fopen(save_path, "wb");
	if (fp == NULL)
	{
		printf("saveBufToDAT error : file can not open!\n");
		return;
	}
	
	if (fwrite(buf, sizeof(unsigned char), dim, fp) != 1)
	    printf("file write error!\n");

	fclose(fp);
}

void maskCodec::readDATToBuf(char *read_path, unsigned char *buf, const int dim)
{
	FILE *fp;
	fp = fopen(read_path, "rb");
	if (fp == NULL)
	{
		printf("readDATToBuf error : file can not open!\n");
		return;
	}

	fread(buf, sizeof(unsigned char), dim, fp);
	
	fclose(fp);
}