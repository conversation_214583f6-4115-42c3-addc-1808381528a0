//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/22.
//

#include <cstring>
#include "AlarmDecision_BSD.h"

AlarmDecision_BSD::AlarmDecision_BSD() {

}

AlarmDecision_BSD::~AlarmDecision_BSD() {

}

int AlarmDecision_BSD::init(int cameraId, DetectDataCallback &detectDataCallback) {
    curCameraId = cameraId;
    curDetectDataCallback = &detectDataCallback;
    G3_Configuration::getInstance().getCameraTypeInfoOfCamera(curCameraType, cameraId);
    return 0;
}

void AlarmDecision_BSD::setAlarmAreaPoints(const VISPoint *pointList, int len) {
    if (len > 0) {
        for (int i = 0; i < len; i++) {
            alarmAreaPointList_level1[i].x = pointList[i].x;
            alarmAreaPointList_level1[i].y = pointList[i].y;
        };
    }
}

bool AlarmDecision_BSD::isPedestrianInAlarmAreaLevel1(td::object_ &pedestrianInfo) {
    /* 判断下一级区域是否有设置（前面两个点不为0） */
    if (alarmAreaPointList_level1[0].x == 0 && alarmAreaPointList_level1[0].y == 0 &&
        alarmAreaPointList_level1[1].x == 0 && alarmAreaPointList_level1[1].y == 0) {
        return false;
    }

    VISPoint pedestrianPOints[4];
    bool isIn = false;

    pedestrianPOints[0].x = pedestrianInfo.mLeft;
    pedestrianPOints[0].y = pedestrianInfo.mTop;

    pedestrianPOints[1].x = pedestrianInfo.mRight;
    pedestrianPOints[1].y = pedestrianInfo.mTop;

    pedestrianPOints[2].x = pedestrianInfo.mRight;
    pedestrianPOints[2].y = pedestrianInfo.mBottom;

    pedestrianPOints[3].x = pedestrianInfo.mLeft;
    pedestrianPOints[3].y = pedestrianInfo.mBottom;

    /* 判断下是否在报警区域内 */
    isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                     alarmAreaPointList_level1, 6);
    /* 如果没在报警区域内容  那么判断下上次的识别结果中 他是否在报警区域内容 */
    if (!isIn) {
        bool isLastIn = false;
        for (std::size_t i = 0; i < lastInAlarm1IdList.size(); i++) {
            if (lastInAlarm1IdList[i] == pedestrianInfo.mId) {
                isLastIn = true;
                break;
            }
        }
        /* 如果上次有在报警区域  那么这次得不在退出区域才能算退出了 */
        if (isLastIn) {

            /* 设置报警区域1的退出区域的点 */
            XuCalculationTool::getInstance().polygonIsometricScaling2(alarmAreaPointList_level1,
                                                                      alarmAreaPointList_level1_exit,
                                                                      ALARM_EXIT_AREA_DIST);
            bool inInExit = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints,
                                                                                                      4,
                                                                                                      alarmAreaPointList_level1_exit,
                                                                                                      6);
            isIn = inInExit;
        }
    }

    return isIn;
}

void AlarmDecision_BSD::parseObjectInfos(td::objectInfo_t &curObjectInfo, float speed,bool cameraCovered) {


    hasPedestrianInAlarmAreaLevel1 = false;
    hasPedestrianInAlarmAreaLevel2 = false;
    lastAlaramYDistance_level1 = 999999;
    lastAlaramYDistance_level2 = 999999;

    /* 由于结构体里面没有裸指针，故直接用={}重置就好了 */
    curAlarmEventInfo = {};
    detectionResult = {};

    detectionResult.curCameraType.copy(curCameraType);
    /* 看看是不是有遮挡 */
    cameraCover = cameraCovered;
    cameraBypass = false;
    switch (curCameraType.installPosition) {
        case INSTALL_POSITION_FRONT: {
            detectionResult.bsdDetectionInfo.cameraCoverStatus.forward = cameraCover;
            detectionResult.bsdDetectionInfo.bypassStatus.forward = cameraBypass;
        }
            break;
        case INSTALL_POSITION_BACK: {
            detectionResult.bsdDetectionInfo.cameraCoverStatus.backward = cameraCover;
            detectionResult.bsdDetectionInfo.bypassStatus.backward = cameraBypass;
        }
            break;
        case INSTALL_POSITION_LEFT: {
            detectionResult.bsdDetectionInfo.cameraCoverStatus.left = cameraCover;
            detectionResult.bsdDetectionInfo.bypassStatus.left = cameraBypass;
        }
            break;
        case INSTALL_POSITION_RIGHT: {
            detectionResult.bsdDetectionInfo.cameraCoverStatus.right = cameraCover;
            detectionResult.bsdDetectionInfo.bypassStatus.right = cameraBypass;
        }
            break;
    }

    int curAlarmLeve1PedestrianId = -1;
    int curAlarmLeve2PedestrianId = -1;

    int curAlarmLeve1PedestrianTrend = 0;
    int curAlarmLeve2PedestrianTrend = 0;

    /* 定义一下当前在报警区域内的行人ID的列表 */
    std::vector<uint32_t> curAlarmArea1IdList;
    std::vector<uint32_t> curAlarmArea2IdList;

    if (!curObjectInfo.objects.empty()) {
        for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {
            if (strcmp("person", curObjectInfo.objects[i].label) == 0) {
                curObjectInfo.objects[i].mLeft = (curObjectInfo.objects[i].mLeft < 0) ? 0
                                                                                      : ((curObjectInfo.objects[i].mLeft >
                                                                                          1280) ? 1280
                                                                                                : curObjectInfo.objects[i].mLeft);
                curObjectInfo.objects[i].mTop = (curObjectInfo.objects[i].mTop < 0) ? 0
                                                                                    : ((curObjectInfo.objects[i].mTop >
                                                                                        720) ? 720
                                                                                             : curObjectInfo.objects[i].mTop);
                curObjectInfo.objects[i].mRight = (curObjectInfo.objects[i].mRight < 0) ? 0
                                                                                        : ((curObjectInfo.objects[i].mRight >
                                                                                            1280) ? 1280
                                                                                                  : curObjectInfo.objects[i].mRight);
                curObjectInfo.objects[i].mBottom = (curObjectInfo.objects[i].mBottom < 0) ? 0
                                                                                          : ((curObjectInfo.objects[i].mBottom >
                                                                                              720) ? 720

                                                                                                   : curObjectInfo.objects[i].mBottom);

                switch (curCameraId) {
                    case 0: {
                        /* 设置报警区域1的点 */
                        alarmAreaPointList_level1[0].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[0].x;
                        alarmAreaPointList_level1[0].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[0].y;
                        alarmAreaPointList_level1[1].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[1].x;
                        alarmAreaPointList_level1[1].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[1].y;
                        alarmAreaPointList_level1[2].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[2].x;
                        alarmAreaPointList_level1[2].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[2].y;
                        alarmAreaPointList_level1[3].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[3].x;
                        alarmAreaPointList_level1[3].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[3].y;
                        alarmAreaPointList_level1[4].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[4].x;
                        alarmAreaPointList_level1[4].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[4].y;
                        alarmAreaPointList_level1[5].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[5].x;
                        alarmAreaPointList_level1[5].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[5].y;

                        /* 设置报警区域2的点 */
                        alarmAreaPointList_level2[0].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[0].x;
                        alarmAreaPointList_level2[0].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[0].y;
                        alarmAreaPointList_level2[1].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[1].x;
                        alarmAreaPointList_level2[1].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[1].y;
                        alarmAreaPointList_level2[2].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[2].x;
                        alarmAreaPointList_level2[2].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[2].y;
                        alarmAreaPointList_level2[3].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[3].x;
                        alarmAreaPointList_level2[3].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[3].y;
                        alarmAreaPointList_level2[4].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[4].x;
                        alarmAreaPointList_level2[4].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[4].y;
                        alarmAreaPointList_level2[5].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[5].x;
                        alarmAreaPointList_level2[5].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[5].y;

                    }
                        break;

                    case 1: {
                        alarmAreaPointList_level1[0].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[0].x;
                        alarmAreaPointList_level1[0].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[0].y;
                        alarmAreaPointList_level1[1].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[1].x;
                        alarmAreaPointList_level1[1].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[1].y;
                        alarmAreaPointList_level1[2].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[2].x;
                        alarmAreaPointList_level1[2].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[2].y;
                        alarmAreaPointList_level1[3].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[3].x;
                        alarmAreaPointList_level1[3].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[3].y;
                        alarmAreaPointList_level1[4].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[4].x;
                        alarmAreaPointList_level1[4].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[4].y;
                        alarmAreaPointList_level1[5].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[5].x;
                        alarmAreaPointList_level1[5].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[5].y;
                        /* 设置报警区域2的点 */
                        alarmAreaPointList_level2[0].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[0].x;
                        alarmAreaPointList_level2[0].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[0].y;
                        alarmAreaPointList_level2[1].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[1].x;
                        alarmAreaPointList_level2[1].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[1].y;
                        alarmAreaPointList_level2[2].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[2].x;
                        alarmAreaPointList_level2[2].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[2].y;
                        alarmAreaPointList_level2[3].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[3].x;
                        alarmAreaPointList_level2[3].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[3].y;
                        alarmAreaPointList_level2[4].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[4].x;
                        alarmAreaPointList_level2[4].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[4].y;
                        alarmAreaPointList_level2[5].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[5].x;
                        alarmAreaPointList_level2[5].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[5].y;
                    }
                        break;
                }



                /* 判断下是否在二级区域内 */
                if (isPedestrianInAlarmAreaLevel2(curObjectInfo.objects[i])) {
                    memset(curObjectInfo.objects[i].label, 0, 16);
                    memcpy(curObjectInfo.objects[i].label, "IN ALARM2", 9);
                    hasPedestrianInAlarmAreaLevel2 = true;
                    /* 报警区域2内的行人的ID存起来 */
                    if (strcmp(curObjectInfo.objects[i].label, "IN ALARM2") == 0) {
                        curAlarmArea1IdList.push_back(curObjectInfo.objects[i].mId);
                    }
                    /* 离得更近的话就用这个离得近的 */
                    if (lastAlaramYDistance_level2 >= curObjectInfo.objects[i].mYDistance) {
                        curAlarmEventInfo.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                        curAlarmEventInfo.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                        curAlarmEventInfo.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                        curAlarmEventInfo.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                        lastAlaramYDistance_level2 = curObjectInfo.objects[i].mYDistance;
                        curAlarmLeve2PedestrianId = curObjectInfo.objects[i].mId;
                        curAlarmLeve2PedestrianTrend = curObjectInfo.objects[i].mTrend;
                    }
                }

                /* 判断下是否在一级区域内 */
                if (isPedestrianInAlarmAreaLevel1(curObjectInfo.objects[i])) {
                    memset(curObjectInfo.objects[i].label, 0, 16);
                    memcpy(curObjectInfo.objects[i].label, "IN ALARM1", 9);
                    hasPedestrianInAlarmAreaLevel1 = true;
                    /* 报警区域1内的行人的ID存起来 */
                    if (strcmp(curObjectInfo.objects[i].label, "IN ALARM1") == 0) {
                        curAlarmArea1IdList.push_back(curObjectInfo.objects[i].mId);
                    }

                    /* 离得更近的话就用这个离得近的 */
                    if (lastAlaramYDistance_level1 >= curObjectInfo.objects[i].mYDistance) {
                        curAlarmEventInfo.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                        curAlarmEventInfo.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                        curAlarmEventInfo.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                        curAlarmEventInfo.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                        lastAlaramYDistance_level1 = curObjectInfo.objects[i].mYDistance;
                        curAlarmLeve1PedestrianId = curObjectInfo.objects[i].mId;
                        curAlarmLeve1PedestrianTrend = curObjectInfo.objects[i].mTrend;

                    }
                }


            }
        }
    }

    /* 更新一下上一次报警行人ID */
    lastInAlarm1IdList.clear();
    lastInAlarm1IdList.insert(lastInAlarm1IdList.begin(),curAlarmArea1IdList.begin(),curAlarmArea1IdList.end());
    lastInAlarm2IdList.clear();
    lastInAlarm2IdList.insert(lastInAlarm2IdList.begin(),curAlarmArea2IdList.begin(),curAlarmArea2IdList.end());


    /* 判断是否在需要报警的车速区间内,在的话需要判断报警 */
    if (speed >= G3_Configuration::getInstance().getBsdMinAlramSpeedLevel1() &&
        speed <= G3_Configuration::getInstance().getBsdMaxAlramSpeedLevel1()) {
        parseEvent_level1(curAlarmLeve1PedestrianId, curAlarmLeve1PedestrianTrend);

    }

    /* 判断是否在需要报警的车速区间内,在的话需要判断报警 */
    if (speed >= G3_Configuration::getInstance().getBsdMinAlramSpeedLevel2() &&
        speed <= G3_Configuration::getInstance().getBsdMaxAlramSpeedLevel2()) {
        parseEvent_level2(curAlarmLeve2PedestrianId, curAlarmLeve2PedestrianTrend);
    }


    detectionResult.speed = speed;


    detectionResult.alarmDecisionType = ALARM_DECISION_TYPE_PEDESTRIAN_AREA;
    /* 把识别信息发出去 */
    curDetectDataCallback->onGetObjectInfo_BSD(curObjectInfo, detectionResult);
}


void AlarmDecision_BSD::parseEvent_level1(uint32_t curAlaramId, uint32_t curPedestrianTrend) {
    if (hasPedestrianInAlarmAreaLevel1) {
        bool needToSendAlarmEvent = true;
        int eventCode = EVENT_UNKNOW;

        /* 根据安装装位置确定这个报警应该转成什么类型的 */
        switch (curCameraType.installPosition) {
            case INSTALL_POSITION_LEFT: {
                /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                switch (curCameraType.cameraOrientation) {
                    case CAMERA_ORIENTATION_FORWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                        eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;
                    case CAMERA_ORIENTATION_BACKWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                        eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;

                    case CAMERA_ORIENTATION_LEFT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                        eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;
                    case CAMERA_ORIENTATION_RIGHT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                        eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;
                }
            }
                break;


            case INSTALL_POSITION_RIGHT: {
                /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                switch (curCameraType.cameraOrientation) {
                    case CAMERA_ORIENTATION_FORWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                        eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;
                    case CAMERA_ORIENTATION_BACKWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                        eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;

                    case CAMERA_ORIENTATION_LEFT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                        eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;
                    case CAMERA_ORIENTATION_RIGHT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                        eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;
                }
            }
                break;


            case INSTALL_POSITION_FRONT:
            case INSTALL_POSITION_BACK: {
                /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                switch (curCameraType.cameraOrientation) {
                    case CAMERA_ORIENTATION_FORWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.forward_level1 = true;
                        eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;
                    case CAMERA_ORIENTATION_BACKWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level1 = true;
                        eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;

                    case CAMERA_ORIENTATION_LEFT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                        eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;

                    case CAMERA_ORIENTATION_RIGHT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                        eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                    }
                        break;
                }
            }
                break;

        }


        /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
        if (needToSendAlarmEvent) {
            /* 看看是否需要启动同ID过滤   */
            if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel1() == 0 &&
                lastAlaramId_level1 == curAlaramId) {
//                printf("lastAlaramId_level1=%d    curAlaramId=%d   \n", lastAlaramId_level1, curAlaramId);
                return;
            }
            /* 看看是否需要启动趋势过滤 */

            /* 如果打开了趋势  没识别出趋势  直接不报 */
            if (((G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_static) ||
                 (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_far_to_near) ||
                 (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_near_to_far)) &&
                curPedestrianTrend == TD_TREND_DEFAULT) {
                printf("curPedestrianTrend == TD_TREND_DEFAULT, did not need send alarm. \n");
                return;
            }

            /* 看看是否需要过滤掉禁止的行人 */
            if (curPedestrianTrend == TD_TREND_STATIC &&
                (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_static)) {
                printf("curPedestrianTrend == TD_TREND_STATIC, did not need send alarm. \n");
                return;
            }

            /* 看看是否需要过滤掉靠近相机的行人 */
            if (curPedestrianTrend == TD_TREND_FAR_TO_NEAR &&
                (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_far_to_near)) {
                printf("curPedestrianTrend == TD_TREND_FAR_TO_NEAR, did not need send alarm. \n");
                return;
            }

            /* 看看是否需要过滤掉远离相机的行人 */
            if (curPedestrianTrend == TD_TREND_NEAR_TO_FAR &&
                (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_near_to_far)) {
                printf("curPedestrianTrend == TD_TREND_NEAR_TO_FAR, did not need send alarm. \n");
                return;
            }

            lastAlaramId_level1 = curAlaramId;
            curAlarmEventInfo.bsdAlarmInfo.alarmPedestrianId = curAlaramId;
            curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, curAlarmEventInfo);

//            printf("*******************curDetectDataCallback->onGetDetectionEvent  eventCode=%d  alarmPedestrianId = %d\n",
//                   eventCode, curAlaramId);
        }
    }
}

bool AlarmDecision_BSD::isCameraBypass() const {
    return cameraBypass;
}

void AlarmDecision_BSD::setCameraBypass(bool cameraBypass) {
    AlarmDecision_BSD::cameraBypass = cameraBypass;
}

bool AlarmDecision_BSD::isPedestrianInAlarmAreaLevel2(td::object_ &pedestrianInfo) {
    /* 判断下二级区域是否有设置（前面两个点不为0） */
    if (alarmAreaPointList_level2[0].x == 0 && alarmAreaPointList_level2[0].y == 0 &&
        alarmAreaPointList_level2[1].x == 0 && alarmAreaPointList_level2[1].y == 0) {
        return false;
    }


    VISPoint pedestrianPOints[4];
    bool isIn = false;

    pedestrianPOints[0].x = pedestrianInfo.mLeft;
    pedestrianPOints[0].y = pedestrianInfo.mTop;

    pedestrianPOints[1].x = pedestrianInfo.mRight;
    pedestrianPOints[1].y = pedestrianInfo.mTop;

    pedestrianPOints[2].x = pedestrianInfo.mRight;
    pedestrianPOints[2].y = pedestrianInfo.mBottom;

    pedestrianPOints[3].x = pedestrianInfo.mLeft;
    pedestrianPOints[3].y = pedestrianInfo.mBottom;


    /* 判断下是否在报警区域内 */
    isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                     alarmAreaPointList_level2, 6);

//    printf("lt:%d,%d     rb:%d,%d    a1:%d,%d   a2:%d,%d   a3:%d,%d   a4:%d,%d    a5:%d,%d    a6:%d,%d\n",
//           pedestrianPOints[0].x, pedestrianPOints[0].y, pedestrianPOints[2].x, pedestrianPOints[2].y,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[0].x,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[0].y,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[1].x,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[1].y,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[2].x,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[2].y,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[3].x,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[3].y,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[4].x,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[4].y,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[5].x,
//           G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[5].y);

    /* 如果没在报警区域内容  那么判断下上次的识别结果中 他是否在报警区域内容 */
    if (!isIn) {
        bool isLastIn = false;
        for (std::size_t i = 0; i < lastInAlarm2IdList.size(); i++) {
            if (lastInAlarm2IdList[i] == pedestrianInfo.mId) {
                isLastIn = true;
                break;
            }
        }
        /* 如果上次有在报警区域  那么这次得不在退出区域才能算退出了 */
        if (isLastIn) {
            /* 设置报警区域2的退出区域的点 */
            XuCalculationTool::getInstance().polygonIsometricScaling2(alarmAreaPointList_level2,
                                                                      alarmAreaPointList_level2_exit,
                                                                      ALARM_EXIT_AREA_DIST);
            bool inInExit = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints,
                                                                                                      4,
                                                                                                      alarmAreaPointList_level2_exit,
                                                                                                      6);
            isIn = inInExit;
        }
    }


//    printf("isIn:%d   lt:%d,%d     rb:%d,%d \n", isIn, pedestrianPOints[0].x, pedestrianPOints[0].y,
//           pedestrianPOints[2].x, pedestrianPOints[2].y);

    return isIn;
}

void AlarmDecision_BSD::parseEvent_level2(uint32_t curAlaramId, uint32_t curPedestrianTrend) {
    if (hasPedestrianInAlarmAreaLevel2) {
        bool needToSendAlarmEvent = true;
        int eventCode = EVENT_UNKNOW;

        /* 根据安装装位置确定这个报警应该转成什么类型的 */
        switch (curCameraType.installPosition) {
            case INSTALL_POSITION_LEFT: {
                /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                switch (curCameraType.cameraOrientation) {
                    case CAMERA_ORIENTATION_FORWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                        eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;
                    case CAMERA_ORIENTATION_BACKWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                        eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;
                    case CAMERA_ORIENTATION_LEFT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                        eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;
                    case CAMERA_ORIENTATION_RIGHT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                        eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;
                }
            }
                break;


            case INSTALL_POSITION_RIGHT: {
                /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                switch (curCameraType.cameraOrientation) {
                    case CAMERA_ORIENTATION_FORWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                        eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;
                    case CAMERA_ORIENTATION_BACKWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                        eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;
                    case CAMERA_ORIENTATION_LEFT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                        eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;
                    case CAMERA_ORIENTATION_RIGHT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                        eventCode = EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;


                }
            }
                break;

            case INSTALL_POSITION_FRONT:
            case INSTALL_POSITION_BACK: {
                /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                switch (curCameraType.cameraOrientation) {
                    case CAMERA_ORIENTATION_FORWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.forward_level2 = true;
                        eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;
                    case CAMERA_ORIENTATION_BACKWARD: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level2 = true;
                        eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;

                    case CAMERA_ORIENTATION_LEFT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                        eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;

                    case CAMERA_ORIENTATION_RIGHT: {
                        curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                        eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                    }
                        break;
                }
            }
                break;

        }
        /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
        if (needToSendAlarmEvent) {
            /* 看看是否需要启动同ID过滤   */
            if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel2() == 0 &&
                lastAlaramId_level2 == curAlaramId) {
                printf("lastAlaramId_level2=%d    curAlaramId=%d   \n", lastAlaramId_level2, curAlaramId);
                return;
            }
            /* 看看是否需要启动趋势过滤 */
            /* 如果打开了趋势  如果没识别出趋势  直接不报 */
            if (((G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_static) ||
                 (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_far_to_near) ||
                 (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_near_to_far)) &&
                curPedestrianTrend == TD_TREND_DEFAULT) {
                printf("curPedestrianTrend == TD_TREND_DEFAULT, did not need send alarm. \n");
                return;
            }

            /* 看看是否需要过滤掉禁止的行人 */
            if (curPedestrianTrend == TD_TREND_STATIC &&
                (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_static)) {
                return;
            }

            /* 看看是否需要过滤掉靠近相机的行人 */
            if (curPedestrianTrend == TD_TREND_FAR_TO_NEAR &&
                (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_far_to_near)) {
                return;
            }

            /* 看看是否需要过滤掉远离相机的行人 */
            if (curPedestrianTrend == TD_TREND_NEAR_TO_FAR &&
                (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_near_to_far)) {
                return;
            }
            lastAlaramId_level2 = curAlaramId;
            curAlarmEventInfo.bsdAlarmInfo.alarmPedestrianId = curAlaramId;
            curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, curAlarmEventInfo);

//            printf("*******************curDetectDataCallback->onGetDetectionEvent  eventCode=%d  curAlaramId=%d \n",
//                   eventCode, curAlaramId);
        }


    }
}




