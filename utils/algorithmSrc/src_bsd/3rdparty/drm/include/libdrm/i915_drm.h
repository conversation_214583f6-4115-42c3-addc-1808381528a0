/*
 * Copyright 2003 Tungsten Graphics, Inc., Cedar Park, Texas.
 * All Rights Reserved.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sub license, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice (including the
 * next paragraph) shall be included in all copies or substantial portions
 * of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 * IN NO EVENT SHALL TUNGSTEN GRAPHICS AND/OR ITS SUPPLIERS BE LIABLE FOR
 * ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
 * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */

#ifndef _I915_DRM_H_
#define _I915_DRM_H_

#include "drm.h"

#if defined(__cplusplus)
extern "C" {
#endif

/* Please note that modifications to all structs defined here are
 * subject to backwards-compatibility constraints.
 */

/**
 * DOC: uevents generated by i915 on it's device node
 *
 * I915_L3_PARITY_UEVENT - Generated when the driver receives a parity mismatch
 *	event from the gpu l3 cache. Additional information supplied is ROW,
 *	BANK, SUBBANK, SLICE of the affected cacheline. Userspace should keep
 *	track of these events and if a specific cache-line seems to have a
 *	persistent error remap it with the l3 remapping tool supplied in
 *	intel-gpu-tools.  The value supplied with the event is always 1.
 *
 * I915_ERROR_UEVENT - Generated upon error detection, currently only via
 *	hangcheck. The error detection event is a good indicator of when things
 *	began to go badly. The value supplied with the event is a 1 upon error
 *	detection, and a 0 upon reset completion, signifying no more error
 *	exists. NOTE: Disabling hangcheck or reset via module parameter will
 *	cause the related events to not be seen.
 *
 * I915_RESET_UEVENT - Event is generated just before an attempt to reset the
 *	the GPU. The value supplied with the event is always 1. NOTE: Disable
 *	reset via module parameter will cause this event to not be seen.
 */
#define I915_L3_PARITY_UEVENT		"L3_PARITY_ERROR"
#define I915_ERROR_UEVENT		"ERROR"
#define I915_RESET_UEVENT		"RESET"

/*
 * i915_user_extension: Base class for defining a chain of extensions
 *
 * Many interfaces need to grow over time. In most cases we can simply
 * extend the struct and have userspace pass in more data. Another option,
 * as demonstrated by Vulkan's approach to providing extensions for forward
 * and backward compatibility, is to use a list of optional structs to
 * provide those extra details.
 *
 * The key advantage to using an extension chain is that it allows us to
 * redefine the interface more easily than an ever growing struct of
 * increasing complexity, and for large parts of that interface to be
 * entirely optional. The downside is more pointer chasing; chasing across
 * the boundary with pointers encapsulated inside u64.
 */
struct i915_user_extension {
	__u64 next_extension;
	__u32 name;
	__u32 flags; /* All undefined bits must be zero. */
	__u32 rsvd[4]; /* Reserved for future use; must be zero. */
};

/*
 * MOCS indexes used for GPU surfaces, defining the cacheability of the
 * surface data and the coherency for this data wrt. CPU vs. GPU accesses.
 */
enum i915_mocs_table_index {
	/*
	 * Not cached anywhere, coherency between CPU and GPU accesses is
	 * guaranteed.
	 */
	I915_MOCS_UNCACHED,
	/*
	 * Cacheability and coherency controlled by the kernel automatically
	 * based on the DRM_I915_GEM_SET_CACHING IOCTL setting and the current
	 * usage of the surface (used for display scanout or not).
	 */
	I915_MOCS_PTE,
	/*
	 * Cached in all GPU caches available on the platform.
	 * Coherency between CPU and GPU accesses to the surface is not
	 * guaranteed without extra synchronization.
	 */
	I915_MOCS_CACHED,
};

/*
 * Different engines serve different roles, and there may be more than one
 * engine serving each role. enum drm_i915_gem_engine_class provides a
 * classification of the role of the engine, which may be used when requesting
 * operations to be performed on a certain subset of engines, or for providing
 * information about that group.
 */
enum drm_i915_gem_engine_class {
	I915_ENGINE_CLASS_RENDER	= 0,
	I915_ENGINE_CLASS_COPY		= 1,
	I915_ENGINE_CLASS_VIDEO		= 2,
	I915_ENGINE_CLASS_VIDEO_ENHANCE	= 3,

	/* should be kept compact */

	I915_ENGINE_CLASS_INVALID	= -1
};

/**
 * DOC: perf_events exposed by i915 through /sys/bus/event_sources/drivers/i915
 *
 */

enum drm_i915_pmu_engine_sample {
	I915_SAMPLE_BUSY = 0,
	I915_SAMPLE_WAIT = 1,
	I915_SAMPLE_SEMA = 2
};

#define I915_PMU_SAMPLE_BITS (4)
#define I915_PMU_SAMPLE_MASK (0xf)
#define I915_PMU_SAMPLE_INSTANCE_BITS (8)
#define I915_PMU_CLASS_SHIFT \
	(I915_PMU_SAMPLE_BITS + I915_PMU_SAMPLE_INSTANCE_BITS)

#define __I915_PMU_ENGINE(class, instance, sample) \
	((class) << I915_PMU_CLASS_SHIFT | \
	(instance) << I915_PMU_SAMPLE_BITS | \
	(sample))

#define I915_PMU_ENGINE_BUSY(class, instance) \
	__I915_PMU_ENGINE(class, instance, I915_SAMPLE_BUSY)

#define I915_PMU_ENGINE_WAIT(class, instance) \
	__I915_PMU_ENGINE(class, instance, I915_SAMPLE_WAIT)

#define I915_PMU_ENGINE_SEMA(class, instance) \
	__I915_PMU_ENGINE(class, instance, I915_SAMPLE_SEMA)

#define __I915_PMU_OTHER(x) (__I915_PMU_ENGINE(0xff, 0xff, 0xf) + 1 + (x))

#define I915_PMU_ACTUAL_FREQUENCY	__I915_PMU_OTHER(0)
#define I915_PMU_REQUESTED_FREQUENCY	__I915_PMU_OTHER(1)
#define I915_PMU_INTERRUPTS		__I915_PMU_OTHER(2)
#define I915_PMU_RC6_RESIDENCY		__I915_PMU_OTHER(3)

#define I915_PMU_LAST I915_PMU_RC6_RESIDENCY

/* Each region is a minimum of 16k, and there are at most 255 of them.
 */
#define I915_NR_TEX_REGIONS 255	/* table size 2k - maximum due to use
				 * of chars for next/prev indices */
#define I915_LOG_MIN_TEX_REGION_SIZE 14

typedef struct _drm_i915_init {
	enum {
		I915_INIT_DMA = 0x01,
		I915_CLEANUP_DMA = 0x02,
		I915_RESUME_DMA = 0x03
	} func;
	unsigned int mmio_offset;
	int sarea_priv_offset;
	unsigned int ring_start;
	unsigned int ring_end;
	unsigned int ring_size;
	unsigned int front_offset;
	unsigned int back_offset;
	unsigned int depth_offset;
	unsigned int w;
	unsigned int h;
	unsigned int pitch;
	unsigned int pitch_bits;
	unsigned int back_pitch;
	unsigned int depth_pitch;
	unsigned int cpp;
	unsigned int chipset;
} drm_i915_init_t;

typedef struct _drm_i915_sarea {
	struct drm_tex_region texList[I915_NR_TEX_REGIONS + 1];
	int last_upload;	/* last time texture was uploaded */
	int last_enqueue;	/* last time a buffer was enqueued */
	int last_dispatch;	/* age of the most recently dispatched buffer */
	int ctxOwner;		/* last context to upload state */
	int texAge;
	int pf_enabled;		/* is pageflipping allowed? */
	int pf_active;
	int pf_current_page;	/* which buffer is being displayed? */
	int perf_boxes;		/* performance boxes to be displayed */
	int width, height;      /* screen size in pixels */

	drm_handle_t front_handle;
	int front_offset;
	int front_size;

	drm_handle_t back_handle;
	int back_offset;
	int back_size;

	drm_handle_t depth_handle;
	int depth_offset;
	int depth_size;

	drm_handle_t tex_handle;
	int tex_offset;
	int tex_size;
	int log_tex_granularity;
	int pitch;
	int rotation;           /* 0, 90, 180 or 270 */
	int rotated_offset;
	int rotated_size;
	int rotated_pitch;
	int virtualX, virtualY;

	unsigned int front_tiled;
	unsigned int back_tiled;
	unsigned int depth_tiled;
	unsigned int rotated_tiled;
	unsigned int rotated2_tiled;

	int pipeA_x;
	int pipeA_y;
	int pipeA_w;
	int pipeA_h;
	int pipeB_x;
	int pipeB_y;
	int pipeB_w;
	int pipeB_h;

	/* fill out some space for old userspace triple buffer */
	drm_handle_t unused_handle;
	__u32 unused1, unused2, unused3;

	/* buffer object handles for static buffers. May change
	 * over the lifetime of the client.
	 */
	__u32 front_bo_handle;
	__u32 back_bo_handle;
	__u32 unused_bo_handle;
	__u32 depth_bo_handle;

} drm_i915_sarea_t;

/* due to userspace building against these headers we need some compat here */
#define planeA_x pipeA_x
#define planeA_y pipeA_y
#define planeA_w pipeA_w
#define planeA_h pipeA_h
#define planeB_x pipeB_x
#define planeB_y pipeB_y
#define planeB_w pipeB_w
#define planeB_h pipeB_h

/* Flags for perf_boxes
 */
#define I915_BOX_RING_EMPTY    0x1
#define I915_BOX_FLIP          0x2
#define I915_BOX_WAIT          0x4
#define I915_BOX_TEXTURE_LOAD  0x8
#define I915_BOX_LOST_CONTEXT  0x10

/*
 * i915 specific ioctls.
 *
 * The device specific ioctl range is [DRM_COMMAND_BASE, DRM_COMMAND_END) ie
 * [0x40, 0xa0) (a0 is excluded). The numbers below are defined as offset
 * against DRM_COMMAND_BASE and should be between [0x0, 0x60).
 */
#define DRM_I915_INIT		0x00
#define DRM_I915_FLUSH		0x01
#define DRM_I915_FLIP		0x02
#define DRM_I915_BATCHBUFFER	0x03
#define DRM_I915_IRQ_EMIT	0x04
#define DRM_I915_IRQ_WAIT	0x05
#define DRM_I915_GETPARAM	0x06
#define DRM_I915_SETPARAM	0x07
#define DRM_I915_ALLOC		0x08
#define DRM_I915_FREE		0x09
#define DRM_I915_INIT_HEAP	0x0a
#define DRM_I915_CMDBUFFER	0x0b
#define DRM_I915_DESTROY_HEAP	0x0c
#define DRM_I915_SET_VBLANK_PIPE	0x0d
#define DRM_I915_GET_VBLANK_PIPE	0x0e
#define DRM_I915_VBLANK_SWAP	0x0f
#define DRM_I915_HWS_ADDR	0x11
#define DRM_I915_GEM_INIT	0x13
#define DRM_I915_GEM_EXECBUFFER	0x14
#define DRM_I915_GEM_PIN	0x15
#define DRM_I915_GEM_UNPIN	0x16
#define DRM_I915_GEM_BUSY	0x17
#define DRM_I915_GEM_THROTTLE	0x18
#define DRM_I915_GEM_ENTERVT	0x19
#define DRM_I915_GEM_LEAVEVT	0x1a
#define DRM_I915_GEM_CREATE	0x1b
#define DRM_I915_GEM_PREAD	0x1c
#define DRM_I915_GEM_PWRITE	0x1d
#define DRM_I915_GEM_MMAP	0x1e
#define DRM_I915_GEM_SET_DOMAIN	0x1f
#define DRM_I915_GEM_SW_FINISH	0x20
#define DRM_I915_GEM_SET_TILING	0x21
#define DRM_I915_GEM_GET_TILING	0x22
#define DRM_I915_GEM_GET_APERTURE 0x23
#define DRM_I915_GEM_MMAP_GTT	0x24
#define DRM_I915_GET_PIPE_FROM_CRTC_ID	0x25
#define DRM_I915_GEM_MADVISE	0x26
#define DRM_I915_OVERLAY_PUT_IMAGE	0x27
#define DRM_I915_OVERLAY_ATTRS	0x28
#define DRM_I915_GEM_EXECBUFFER2	0x29
#define DRM_I915_GEM_EXECBUFFER2_WR	DRM_I915_GEM_EXECBUFFER2
#define DRM_I915_GET_SPRITE_COLORKEY	0x2a
#define DRM_I915_SET_SPRITE_COLORKEY	0x2b
#define DRM_I915_GEM_WAIT	0x2c
#define DRM_I915_GEM_CONTEXT_CREATE	0x2d
#define DRM_I915_GEM_CONTEXT_DESTROY	0x2e
#define DRM_I915_GEM_SET_CACHING	0x2f
#define DRM_I915_GEM_GET_CACHING	0x30
#define DRM_I915_REG_READ		0x31
#define DRM_I915_GET_RESET_STATS	0x32
#define DRM_I915_GEM_USERPTR		0x33
#define DRM_I915_GEM_CONTEXT_GETPARAM	0x34
#define DRM_I915_GEM_CONTEXT_SETPARAM	0x35
#define DRM_I915_PERF_OPEN		0x36
#define DRM_I915_PERF_ADD_CONFIG	0x37
#define DRM_I915_PERF_REMOVE_CONFIG	0x38
#define DRM_I915_QUERY			0x39
/* Must be kept compact -- no holes */

#define DRM_IOCTL_I915_INIT		DRM_IOW( DRM_COMMAND_BASE + DRM_I915_INIT, drm_i915_init_t)
#define DRM_IOCTL_I915_FLUSH		DRM_IO ( DRM_COMMAND_BASE + DRM_I915_FLUSH)
#define DRM_IOCTL_I915_FLIP		DRM_IO ( DRM_COMMAND_BASE + DRM_I915_FLIP)
#define DRM_IOCTL_I915_BATCHBUFFER	DRM_IOW( DRM_COMMAND_BASE + DRM_I915_BATCHBUFFER, drm_i915_batchbuffer_t)
#define DRM_IOCTL_I915_IRQ_EMIT         DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_IRQ_EMIT, drm_i915_irq_emit_t)
#define DRM_IOCTL_I915_IRQ_WAIT         DRM_IOW( DRM_COMMAND_BASE + DRM_I915_IRQ_WAIT, drm_i915_irq_wait_t)
#define DRM_IOCTL_I915_GETPARAM         DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GETPARAM, drm_i915_getparam_t)
#define DRM_IOCTL_I915_SETPARAM         DRM_IOW( DRM_COMMAND_BASE + DRM_I915_SETPARAM, drm_i915_setparam_t)
#define DRM_IOCTL_I915_ALLOC            DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_ALLOC, drm_i915_mem_alloc_t)
#define DRM_IOCTL_I915_FREE             DRM_IOW( DRM_COMMAND_BASE + DRM_I915_FREE, drm_i915_mem_free_t)
#define DRM_IOCTL_I915_INIT_HEAP        DRM_IOW( DRM_COMMAND_BASE + DRM_I915_INIT_HEAP, drm_i915_mem_init_heap_t)
#define DRM_IOCTL_I915_CMDBUFFER	DRM_IOW( DRM_COMMAND_BASE + DRM_I915_CMDBUFFER, drm_i915_cmdbuffer_t)
#define DRM_IOCTL_I915_DESTROY_HEAP	DRM_IOW( DRM_COMMAND_BASE + DRM_I915_DESTROY_HEAP, drm_i915_mem_destroy_heap_t)
#define DRM_IOCTL_I915_SET_VBLANK_PIPE	DRM_IOW( DRM_COMMAND_BASE + DRM_I915_SET_VBLANK_PIPE, drm_i915_vblank_pipe_t)
#define DRM_IOCTL_I915_GET_VBLANK_PIPE	DRM_IOR( DRM_COMMAND_BASE + DRM_I915_GET_VBLANK_PIPE, drm_i915_vblank_pipe_t)
#define DRM_IOCTL_I915_VBLANK_SWAP	DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_VBLANK_SWAP, drm_i915_vblank_swap_t)
#define DRM_IOCTL_I915_HWS_ADDR		DRM_IOW(DRM_COMMAND_BASE + DRM_I915_HWS_ADDR, struct drm_i915_gem_init)
#define DRM_IOCTL_I915_GEM_INIT		DRM_IOW(DRM_COMMAND_BASE + DRM_I915_GEM_INIT, struct drm_i915_gem_init)
#define DRM_IOCTL_I915_GEM_EXECBUFFER	DRM_IOW(DRM_COMMAND_BASE + DRM_I915_GEM_EXECBUFFER, struct drm_i915_gem_execbuffer)
#define DRM_IOCTL_I915_GEM_EXECBUFFER2	DRM_IOW(DRM_COMMAND_BASE + DRM_I915_GEM_EXECBUFFER2, struct drm_i915_gem_execbuffer2)
#define DRM_IOCTL_I915_GEM_EXECBUFFER2_WR	DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GEM_EXECBUFFER2_WR, struct drm_i915_gem_execbuffer2)
#define DRM_IOCTL_I915_GEM_PIN		DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GEM_PIN, struct drm_i915_gem_pin)
#define DRM_IOCTL_I915_GEM_UNPIN	DRM_IOW(DRM_COMMAND_BASE + DRM_I915_GEM_UNPIN, struct drm_i915_gem_unpin)
#define DRM_IOCTL_I915_GEM_BUSY		DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GEM_BUSY, struct drm_i915_gem_busy)
#define DRM_IOCTL_I915_GEM_SET_CACHING		DRM_IOW(DRM_COMMAND_BASE + DRM_I915_GEM_SET_CACHING, struct drm_i915_gem_caching)
#define DRM_IOCTL_I915_GEM_GET_CACHING		DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GEM_GET_CACHING, struct drm_i915_gem_caching)
#define DRM_IOCTL_I915_GEM_THROTTLE	DRM_IO ( DRM_COMMAND_BASE + DRM_I915_GEM_THROTTLE)
#define DRM_IOCTL_I915_GEM_ENTERVT	DRM_IO(DRM_COMMAND_BASE + DRM_I915_GEM_ENTERVT)
#define DRM_IOCTL_I915_GEM_LEAVEVT	DRM_IO(DRM_COMMAND_BASE + DRM_I915_GEM_LEAVEVT)
#define DRM_IOCTL_I915_GEM_CREATE	DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GEM_CREATE, struct drm_i915_gem_create)
#define DRM_IOCTL_I915_GEM_PREAD	DRM_IOW (DRM_COMMAND_BASE + DRM_I915_GEM_PREAD, struct drm_i915_gem_pread)
#define DRM_IOCTL_I915_GEM_PWRITE	DRM_IOW (DRM_COMMAND_BASE + DRM_I915_GEM_PWRITE, struct drm_i915_gem_pwrite)
#define DRM_IOCTL_I915_GEM_MMAP		DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GEM_MMAP, struct drm_i915_gem_mmap)
#define DRM_IOCTL_I915_GEM_MMAP_GTT	DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GEM_MMAP_GTT, struct drm_i915_gem_mmap_gtt)
#define DRM_IOCTL_I915_GEM_SET_DOMAIN	DRM_IOW (DRM_COMMAND_BASE + DRM_I915_GEM_SET_DOMAIN, struct drm_i915_gem_set_domain)
#define DRM_IOCTL_I915_GEM_SW_FINISH	DRM_IOW (DRM_COMMAND_BASE + DRM_I915_GEM_SW_FINISH, struct drm_i915_gem_sw_finish)
#define DRM_IOCTL_I915_GEM_SET_TILING	DRM_IOWR (DRM_COMMAND_BASE + DRM_I915_GEM_SET_TILING, struct drm_i915_gem_set_tiling)
#define DRM_IOCTL_I915_GEM_GET_TILING	DRM_IOWR (DRM_COMMAND_BASE + DRM_I915_GEM_GET_TILING, struct drm_i915_gem_get_tiling)
#define DRM_IOCTL_I915_GEM_GET_APERTURE	DRM_IOR  (DRM_COMMAND_BASE + DRM_I915_GEM_GET_APERTURE, struct drm_i915_gem_get_aperture)
#define DRM_IOCTL_I915_GET_PIPE_FROM_CRTC_ID DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GET_PIPE_FROM_CRTC_ID, struct drm_i915_get_pipe_from_crtc_id)
#define DRM_IOCTL_I915_GEM_MADVISE	DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GEM_MADVISE, struct drm_i915_gem_madvise)
#define DRM_IOCTL_I915_OVERLAY_PUT_IMAGE	DRM_IOW(DRM_COMMAND_BASE + DRM_I915_OVERLAY_PUT_IMAGE, struct drm_intel_overlay_put_image)
#define DRM_IOCTL_I915_OVERLAY_ATTRS	DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_OVERLAY_ATTRS, struct drm_intel_overlay_attrs)
#define DRM_IOCTL_I915_SET_SPRITE_COLORKEY DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_SET_SPRITE_COLORKEY, struct drm_intel_sprite_colorkey)
#define DRM_IOCTL_I915_GET_SPRITE_COLORKEY DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GET_SPRITE_COLORKEY, struct drm_intel_sprite_colorkey)
#define DRM_IOCTL_I915_GEM_WAIT		DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_GEM_WAIT, struct drm_i915_gem_wait)
#define DRM_IOCTL_I915_GEM_CONTEXT_CREATE	DRM_IOWR (DRM_COMMAND_BASE + DRM_I915_GEM_CONTEXT_CREATE, struct drm_i915_gem_context_create)
#define DRM_IOCTL_I915_GEM_CONTEXT_CREATE_EXT	DRM_IOWR (DRM_COMMAND_BASE + DRM_I915_GEM_CONTEXT_CREATE, struct drm_i915_gem_context_create_ext)
#define DRM_IOCTL_I915_GEM_CONTEXT_DESTROY	DRM_IOW (DRM_COMMAND_BASE + DRM_I915_GEM_CONTEXT_DESTROY, struct drm_i915_gem_context_destroy)
#define DRM_IOCTL_I915_REG_READ			DRM_IOWR (DRM_COMMAND_BASE + DRM_I915_REG_READ, struct drm_i915_reg_read)
#define DRM_IOCTL_I915_GET_RESET_STATS		DRM_IOWR (DRM_COMMAND_BASE + DRM_I915_GET_RESET_STATS, struct drm_i915_reset_stats)
#define DRM_IOCTL_I915_GEM_USERPTR			DRM_IOWR (DRM_COMMAND_BASE + DRM_I915_GEM_USERPTR, struct drm_i915_gem_userptr)
#define DRM_IOCTL_I915_GEM_CONTEXT_GETPARAM	DRM_IOWR (DRM_COMMAND_BASE + DRM_I915_GEM_CONTEXT_GETPARAM, struct drm_i915_gem_context_param)
#define DRM_IOCTL_I915_GEM_CONTEXT_SETPARAM	DRM_IOWR (DRM_COMMAND_BASE + DRM_I915_GEM_CONTEXT_SETPARAM, struct drm_i915_gem_context_param)
#define DRM_IOCTL_I915_PERF_OPEN	DRM_IOW(DRM_COMMAND_BASE + DRM_I915_PERF_OPEN, struct drm_i915_perf_open_param)
#define DRM_IOCTL_I915_PERF_ADD_CONFIG	DRM_IOW(DRM_COMMAND_BASE + DRM_I915_PERF_ADD_CONFIG, struct drm_i915_perf_oa_config)
#define DRM_IOCTL_I915_PERF_REMOVE_CONFIG	DRM_IOW(DRM_COMMAND_BASE + DRM_I915_PERF_REMOVE_CONFIG, __u64)
#define DRM_IOCTL_I915_QUERY			DRM_IOWR(DRM_COMMAND_BASE + DRM_I915_QUERY, struct drm_i915_query)

/* Allow drivers to submit batchbuffers directly to hardware, relying
 * on the security mechanisms provided by hardware.
 */
typedef struct drm_i915_batchbuffer {
	int start;		/* agp offset */
	int used;		/* nr bytes in use */
	int DR1;		/* hw flags for GFX_OP_DRAWRECT_INFO */
	int DR4;		/* window origin for GFX_OP_DRAWRECT_INFO */
	int num_cliprects;	/* mulitpass with multiple cliprects? */
	struct drm_clip_rect *cliprects;	/* pointer to userspace cliprects */
} drm_i915_batchbuffer_t;

/* As above, but pass a pointer to userspace buffer which can be
 * validated by the kernel prior to sending to hardware.
 */
typedef struct _drm_i915_cmdbuffer {
	char *buf;	/* pointer to userspace command buffer */
	int sz;			/* nr bytes in buf */
	int DR1;		/* hw flags for GFX_OP_DRAWRECT_INFO */
	int DR4;		/* window origin for GFX_OP_DRAWRECT_INFO */
	int num_cliprects;	/* mulitpass with multiple cliprects? */
	struct drm_clip_rect *cliprects;	/* pointer to userspace cliprects */
} drm_i915_cmdbuffer_t;

/* Userspace can request & wait on irq's:
 */
typedef struct drm_i915_irq_emit {
	int *irq_seq;
} drm_i915_irq_emit_t;

typedef struct drm_i915_irq_wait {
	int irq_seq;
} drm_i915_irq_wait_t;

/*
 * Different modes of per-process Graphics Translation Table,
 * see I915_PARAM_HAS_ALIASING_PPGTT
 */
#define I915_GEM_PPGTT_NONE	0
#define I915_GEM_PPGTT_ALIASING	1
#define I915_GEM_PPGTT_FULL	2

/* Ioctl to query kernel params:
 */
#define I915_PARAM_IRQ_ACTIVE            1
#define I915_PARAM_ALLOW_BATCHBUFFER     2
#define I915_PARAM_LAST_DISPATCH         3
#define I915_PARAM_CHIPSET_ID            4
#define I915_PARAM_HAS_GEM               5
#define I915_PARAM_NUM_FENCES_AVAIL      6
#define I915_PARAM_HAS_OVERLAY           7
#define I915_PARAM_HAS_PAGEFLIPPING	 8
#define I915_PARAM_HAS_EXECBUF2          9
#define I915_PARAM_HAS_BSD		 10
#define I915_PARAM_HAS_BLT		 11
#define I915_PARAM_HAS_RELAXED_FENCING	 12
#define I915_PARAM_HAS_COHERENT_RINGS	 13
#define I915_PARAM_HAS_EXEC_CONSTANTS	 14
#define I915_PARAM_HAS_RELAXED_DELTA	 15
#define I915_PARAM_HAS_GEN7_SOL_RESET	 16
#define I915_PARAM_HAS_LLC     	 	 17
#define I915_PARAM_HAS_ALIASING_PPGTT	 18
#define I915_PARAM_HAS_WAIT_TIMEOUT	 19
#define I915_PARAM_HAS_SEMAPHORES	 20
#define I915_PARAM_HAS_PRIME_VMAP_FLUSH	 21
#define I915_PARAM_HAS_VEBOX		 22
#define I915_PARAM_HAS_SECURE_BATCHES	 23
#define I915_PARAM_HAS_PINNED_BATCHES	 24
#define I915_PARAM_HAS_EXEC_NO_RELOC	 25
#define I915_PARAM_HAS_EXEC_HANDLE_LUT   26
#define I915_PARAM_HAS_WT     	 	 27
#define I915_PARAM_CMD_PARSER_VERSION	 28
#define I915_PARAM_HAS_COHERENT_PHYS_GTT 29
#define I915_PARAM_MMAP_VERSION          30
#define I915_PARAM_HAS_BSD2		 31
#define I915_PARAM_REVISION              32
#define I915_PARAM_SUBSLICE_TOTAL	 33
#define I915_PARAM_EU_TOTAL		 34
#define I915_PARAM_HAS_GPU_RESET	 35
#define I915_PARAM_HAS_RESOURCE_STREAMER 36
#define I915_PARAM_HAS_EXEC_SOFTPIN	 37
#define I915_PARAM_HAS_POOLED_EU	 38
#define I915_PARAM_MIN_EU_IN_POOL	 39
#define I915_PARAM_MMAP_GTT_VERSION	 40

/*
 * Query whether DRM_I915_GEM_EXECBUFFER2 supports user defined execution
 * priorities and the driver will attempt to execute batches in priority order.
 * The param returns a capability bitmask, nonzero implies that the scheduler
 * is enabled, with different features present according to the mask.
 *
 * The initial priority for each batch is supplied by the context and is
 * controlled via I915_CONTEXT_PARAM_PRIORITY.
 */
#define I915_PARAM_HAS_SCHEDULER	 41
#define   I915_SCHEDULER_CAP_ENABLED	(1ul << 0)
#define   I915_SCHEDULER_CAP_PRIORITY	(1ul << 1)
#define   I915_SCHEDULER_CAP_PREEMPTION	(1ul << 2)
#define   I915_SCHEDULER_CAP_SEMAPHORES	(1ul << 3)

#define I915_PARAM_HUC_STATUS		 42

/* Query whether DRM_I915_GEM_EXECBUFFER2 supports the ability to opt-out of
 * synchronisation with implicit fencing on individual objects.
 * See EXEC_OBJECT_ASYNC.
 */
#define I915_PARAM_HAS_EXEC_ASYNC	 43

/* Query whether DRM_I915_GEM_EXECBUFFER2 supports explicit fence support -
 * both being able to pass in a sync_file fd to wait upon before executing,
 * and being able to return a new sync_file fd that is signaled when the
 * current request is complete. See I915_EXEC_FENCE_IN and I915_EXEC_FENCE_OUT.
 */
#define I915_PARAM_HAS_EXEC_FENCE	 44

/* Query whether DRM_I915_GEM_EXECBUFFER2 supports the ability to capture
 * user specified buffers for post-mortem debugging of GPU hangs. See
 * EXEC_OBJECT_CAPTURE.
 */
#define I915_PARAM_HAS_EXEC_CAPTURE	 45

#define I915_PARAM_SLICE_MASK		 46

/* Assuming it's uniform for each slice, this queries the mask of subslices
 * per-slice for this system.
 */
#define I915_PARAM_SUBSLICE_MASK	 47

/*
 * Query whether DRM_I915_GEM_EXECBUFFER2 supports supplying the batch buffer
 * as the first execobject as opposed to the last. See I915_EXEC_BATCH_FIRST.
 */
#define I915_PARAM_HAS_EXEC_BATCH_FIRST	 48

/* Query whether DRM_I915_GEM_EXECBUFFER2 supports supplying an array of
 * drm_i915_gem_exec_fence structures.  See I915_EXEC_FENCE_ARRAY.
 */
#define I915_PARAM_HAS_EXEC_FENCE_ARRAY  49

/*
 * Query whether every context (both per-file default and user created) is
 * isolated (insofar as HW supports). If this parameter is not true, then
 * freshly created contexts may inherit values from an existing context,
 * rather than default HW values. If true, it also ensures (insofar as HW
 * supports) that all state set by this context will not leak to any other
 * context.
 *
 * As not every engine across every gen support contexts, the returned
 * value reports the support of context isolation for individual engines by
 * returning a bitmask of each engine class set to true if that class supports
 * isolation.
 */
#define I915_PARAM_HAS_CONTEXT_ISOLATION 50

/* Frequency of the command streamer timestamps given by the *_TIMESTAMP
 * registers. This used to be fixed per platform but from CNL onwards, this
 * might vary depending on the parts.
 */
#define I915_PARAM_CS_TIMESTAMP_FREQUENCY 51

/*
 * Once upon a time we supposed that writes through the GGTT would be
 * immediately in physical memory (once flushed out of the CPU path). However,
 * on a few different processors and chipsets, this is not necessarily the case
 * as the writes appear to be buffered internally. Thus a read of the backing
 * storage (physical memory) via a different path (with different physical tags
 * to the indirect write via the GGTT) will see stale values from before
 * the GGTT write. Inside the kernel, we can for the most part keep track of
 * the different read/write domains in use (e.g. set-domain), but the assumption
 * of coherency is baked into the ABI, hence reporting its true state in this
 * parameter.
 *
 * Reports true when writes via mmap_gtt are immediately visible following an
 * lfence to flush the WCB.
 *
 * Reports false when writes via mmap_gtt are indeterminately delayed in an in
 * internal buffer and are _not_ immediately visible to third parties accessing
 * directly via mmap_cpu/mmap_wc. Use of mmap_gtt as part of an IPC
 * communications channel when reporting false is strongly disadvised.
 */
#define I915_PARAM_MMAP_GTT_COHERENT	52

/* Must be kept compact -- no holes and well documented */

typedef struct drm_i915_getparam {
	__s32 param;
	/*
	 * WARNING: Using pointers instead of fixed-size u64 means we need to write
	 * compat32 code. Don't repeat this mistake.
	 */
	int *value;
} drm_i915_getparam_t;

/* Ioctl to set kernel params:
 */
#define I915_SETPARAM_USE_MI_BATCHBUFFER_START            1
#define I915_SETPARAM_TEX_LRU_LOG_GRANULARITY             2
#define I915_SETPARAM_ALLOW_BATCHBUFFER                   3
#define I915_SETPARAM_NUM_USED_FENCES                     4
/* Must be kept compact -- no holes */

typedef struct drm_i915_setparam {
	int param;
	int value;
} drm_i915_setparam_t;

/* A memory manager for regions of shared memory:
 */
#define I915_MEM_REGION_AGP 1

typedef struct drm_i915_mem_alloc {
	int region;
	int alignment;
	int size;
	int *region_offset;	/* offset from start of fb or agp */
} drm_i915_mem_alloc_t;

typedef struct drm_i915_mem_free {
	int region;
	int region_offset;
} drm_i915_mem_free_t;

typedef struct drm_i915_mem_init_heap {
	int region;
	int size;
	int start;
} drm_i915_mem_init_heap_t;

/* Allow memory manager to be torn down and re-initialized (eg on
 * rotate):
 */
typedef struct drm_i915_mem_destroy_heap {
	int region;
} drm_i915_mem_destroy_heap_t;

/* Allow X server to configure which pipes to monitor for vblank signals
 */
#define	DRM_I915_VBLANK_PIPE_A	1
#define	DRM_I915_VBLANK_PIPE_B	2

typedef struct drm_i915_vblank_pipe {
	int pipe;
} drm_i915_vblank_pipe_t;

/* Schedule buffer swap at given vertical blank:
 */
typedef struct drm_i915_vblank_swap {
	drm_drawable_t drawable;
	enum drm_vblank_seq_type seqtype;
	unsigned int sequence;
} drm_i915_vblank_swap_t;

typedef struct drm_i915_hws_addr {
	__u64 addr;
} drm_i915_hws_addr_t;

struct drm_i915_gem_init {
	/**
	 * Beginning offset in the GTT to be managed by the DRM memory
	 * manager.
	 */
	__u64 gtt_start;
	/**
	 * Ending offset in the GTT to be managed by the DRM memory
	 * manager.
	 */
	__u64 gtt_end;
};

struct drm_i915_gem_create {
	/**
	 * Requested size for the object.
	 *
	 * The (page-aligned) allocated size for the object will be returned.
	 */
	__u64 size;
	/**
	 * Returned handle for the object.
	 *
	 * Object handles are nonzero.
	 */
	__u32 handle;
	__u32 pad;
};

struct drm_i915_gem_pread {
	/** Handle for the object being read. */
	__u32 handle;
	__u32 pad;
	/** Offset into the object to read from */
	__u64 offset;
	/** Length of data to read */
	__u64 size;
	/**
	 * Pointer to write the data into.
	 *
	 * This is a fixed-size type for 32/64 compatibility.
	 */
	__u64 data_ptr;
};

struct drm_i915_gem_pwrite {
	/** Handle for the object being written to. */
	__u32 handle;
	__u32 pad;
	/** Offset into the object to write to */
	__u64 offset;
	/** Length of data to write */
	__u64 size;
	/**
	 * Pointer to read the data from.
	 *
	 * This is a fixed-size type for 32/64 compatibility.
	 */
	__u64 data_ptr;
};

struct drm_i915_gem_mmap {
	/** Handle for the object being mapped. */
	__u32 handle;
	__u32 pad;
	/** Offset in the object to map. */
	__u64 offset;
	/**
	 * Length of data to map.
	 *
	 * The value will be page-aligned.
	 */
	__u64 size;
	/**
	 * Returned pointer the data was mapped at.
	 *
	 * This is a fixed-size type for 32/64 compatibility.
	 */
	__u64 addr_ptr;

	/**
	 * Flags for extended behaviour.
	 *
	 * Added in version 2.
	 */
	__u64 flags;
#define I915_MMAP_WC 0x1
};

struct drm_i915_gem_mmap_gtt {
	/** Handle for the object being mapped. */
	__u32 handle;
	__u32 pad;
	/**
	 * Fake offset to use for subsequent mmap call
	 *
	 * This is a fixed-size type for 32/64 compatibility.
	 */
	__u64 offset;
};

struct drm_i915_gem_set_domain {
	/** Handle for the object */
	__u32 handle;

	/** New read domains */
	__u32 read_domains;

	/** New write domain */
	__u32 write_domain;
};

struct drm_i915_gem_sw_finish {
	/** Handle for the object */
	__u32 handle;
};

struct drm_i915_gem_relocation_entry {
	/**
	 * Handle of the buffer being pointed to by this relocation entry.
	 *
	 * It's appealing to make this be an index into the mm_validate_entry
	 * list to refer to the buffer, but this allows the driver to create
	 * a relocation list for state buffers and not re-write it per
	 * exec using the buffer.
	 */
	__u32 target_handle;

	/**
	 * Value to be added to the offset of the target buffer to make up
	 * the relocation entry.
	 */
	__u32 delta;

	/** Offset in the buffer the relocation entry will be written into */
	__u64 offset;

	/**
	 * Offset value of the target buffer that the relocation entry was last
	 * written as.
	 *
	 * If the buffer has the same offset as last time, we can skip syncing
	 * and writing the relocation.  This value is written back out by
	 * the execbuffer ioctl when the relocation is written.
	 */
	__u64 presumed_offset;

	/**
	 * Target memory domains read by this operation.
	 */
	__u32 read_domains;

	/**
	 * Target memory domains written by this operation.
	 *
	 * Note that only one domain may be written by the whole
	 * execbuffer operation, so that where there are conflicts,
	 * the application will get -EINVAL back.
	 */
	__u32 write_domain;
};

/** @{
 * Intel memory domains
 *
 * Most of these just align with the various caches in
 * the system and are used to flush and invalidate as
 * objects end up cached in different domains.
 */
/** CPU cache */
#define I915_GEM_DOMAIN_CPU		0x00000001
/** Render cache, used by 2D and 3D drawing */
#define I915_GEM_DOMAIN_RENDER		0x00000002
/** Sampler cache, used by texture engine */
#define I915_GEM_DOMAIN_SAMPLER		0x00000004
/** Command queue, used to load batch buffers */
#define I915_GEM_DOMAIN_COMMAND		0x00000008
/** Instruction cache, used by shader programs */
#define I915_GEM_DOMAIN_INSTRUCTION	0x00000010
/** Vertex address cache */
#define I915_GEM_DOMAIN_VERTEX		0x00000020
/** GTT domain - aperture and scanout */
#define I915_GEM_DOMAIN_GTT		0x00000040
/** WC domain - uncached access */
#define I915_GEM_DOMAIN_WC		0x00000080
/** @} */

struct drm_i915_gem_exec_object {
	/**
	 * User's handle for a buffer to be bound into the GTT for this
	 * operation.
	 */
	__u32 handle;

	/** Number of relocations to be performed on this buffer */
	__u32 relocation_count;
	/**
	 * Pointer to array of struct drm_i915_gem_relocation_entry containing
	 * the relocations to be performed in this buffer.
	 */
	__u64 relocs_ptr;

	/** Required alignment in graphics aperture */
	__u64 alignment;

	/**
	 * Returned value of the updated offset of the object, for future
	 * presumed_offset writes.
	 */
	__u64 offset;
};

struct drm_i915_gem_execbuffer {
	/**
	 * List of buffers to be validated with their relocations to be
	 * performend on them.
	 *
	 * This is a pointer to an array of struct drm_i915_gem_validate_entry.
	 *
	 * These buffers must be listed in an order such that all relocations
	 * a buffer is performing refer to buffers that have already appeared
	 * in the validate list.
	 */
	__u64 buffers_ptr;
	__u32 buffer_count;

	/** Offset in the batchbuffer to start execution from. */
	__u32 batch_start_offset;
	/** Bytes used in batchbuffer from batch_start_offset */
	__u32 batch_len;
	__u32 DR1;
	__u32 DR4;
	__u32 num_cliprects;
	/** This is a struct drm_clip_rect *cliprects */
	__u64 cliprects_ptr;
};

struct drm_i915_gem_exec_object2 {
	/**
	 * User's handle for a buffer to be bound into the GTT for this
	 * operation.
	 */
	__u32 handle;

	/** Number of relocations to be performed on this buffer */
	__u32 relocation_count;
	/**
	 * Pointer to array of struct drm_i915_gem_relocation_entry containing
	 * the relocations to be performed in this buffer.
	 */
	__u64 relocs_ptr;

	/** Required alignment in graphics aperture */
	__u64 alignment;

	/**
	 * When the EXEC_OBJECT_PINNED flag is specified this is populated by
	 * the user with the GTT offset at which this object will be pinned.
	 * When the I915_EXEC_NO_RELOC flag is specified this must contain the
	 * presumed_offset of the object.
	 * During execbuffer2 the kernel populates it with the value of the
	 * current GTT offset of the object, for future presumed_offset writes.
	 */
	__u64 offset;

#define EXEC_OBJECT_NEEDS_FENCE		 (1<<0)
#define EXEC_OBJECT_NEEDS_GTT		 (1<<1)
#define EXEC_OBJECT_WRITE		 (1<<2)
#define EXEC_OBJECT_SUPPORTS_48B_ADDRESS (1<<3)
#define EXEC_OBJECT_PINNED		 (1<<4)
#define EXEC_OBJECT_PAD_TO_SIZE		 (1<<5)
/* The kernel implicitly tracks GPU activity on all GEM objects, and
 * synchronises operations with outstanding rendering. This includes
 * rendering on other devices if exported via dma-buf. However, sometimes
 * this tracking is too coarse and the user knows better. For example,
 * if the object is split into non-overlapping ranges shared between different
 * clients or engines (i.e. suballocating objects), the implicit tracking
 * by kernel assumes that each operation affects the whole object rather
 * than an individual range, causing needless synchronisation between clients.
 * The kernel will also forgo any CPU cache flushes prior to rendering from
 * the object as the client is expected to be also handling such domain
 * tracking.
 *
 * The kernel maintains the implicit tracking in order to manage resources
 * used by the GPU - this flag only disables the synchronisation prior to
 * rendering with this object in this execbuf.
 *
 * Opting out of implicit synhronisation requires the user to do its own
 * explicit tracking to avoid rendering corruption. See, for example,
 * I915_PARAM_HAS_EXEC_FENCE to order execbufs and execute them asynchronously.
 */
#define EXEC_OBJECT_ASYNC		(1<<6)
/* Request that the contents of this execobject be copied into the error
 * state upon a GPU hang involving this batch for post-mortem debugging.
 * These buffers are recorded in no particular order as "user" in
 * /sys/class/drm/cardN/error. Query I915_PARAM_HAS_EXEC_CAPTURE to see
 * if the kernel supports this flag.
 */
#define EXEC_OBJECT_CAPTURE		(1<<7)
/* All remaining bits are MBZ and RESERVED FOR FUTURE USE */
#define __EXEC_OBJECT_UNKNOWN_FLAGS -(EXEC_OBJECT_CAPTURE<<1)
	__u64 flags;

	union {
		__u64 rsvd1;
		__u64 pad_to_size;
	};
	__u64 rsvd2;
};

struct drm_i915_gem_exec_fence {
	/**
	 * User's handle for a drm_syncobj to wait on or signal.
	 */
	__u32 handle;

#define I915_EXEC_FENCE_WAIT            (1<<0)
#define I915_EXEC_FENCE_SIGNAL          (1<<1)
#define __I915_EXEC_FENCE_UNKNOWN_FLAGS (-(I915_EXEC_FENCE_SIGNAL << 1))
	__u32 flags;
};

struct drm_i915_gem_execbuffer2 {
	/**
	 * List of gem_exec_object2 structs
	 */
	__u64 buffers_ptr;
	__u32 buffer_count;

	/** Offset in the batchbuffer to start execution from. */
	__u32 batch_start_offset;
	/** Bytes used in batchbuffer from batch_start_offset */
	__u32 batch_len;
	__u32 DR1;
	__u32 DR4;
	__u32 num_cliprects;
	/**
	 * This is a struct drm_clip_rect *cliprects if I915_EXEC_FENCE_ARRAY
	 * is not set.  If I915_EXEC_FENCE_ARRAY is set, then this is a
	 * struct drm_i915_gem_exec_fence *fences.
	 */
	__u64 cliprects_ptr;
#define I915_EXEC_RING_MASK              (0x3f)
#define I915_EXEC_DEFAULT                (0<<0)
#define I915_EXEC_RENDER                 (1<<0)
#define I915_EXEC_BSD                    (2<<0)
#define I915_EXEC_BLT                    (3<<0)
#define I915_EXEC_VEBOX                  (4<<0)

/* Used for switching the constants addressing mode on gen4+ RENDER ring.
 * Gen6+ only supports relative addressing to dynamic state (default) and
 * absolute addressing.
 *
 * These flags are ignored for the BSD and BLT rings.
 */
#define I915_EXEC_CONSTANTS_MASK 	(3<<6)
#define I915_EXEC_CONSTANTS_REL_GENERAL (0<<6) /* default */
#define I915_EXEC_CONSTANTS_ABSOLUTE 	(1<<6)
#define I915_EXEC_CONSTANTS_REL_SURFACE (2<<6) /* gen4/5 only */
	__u64 flags;
	__u64 rsvd1; /* now used for context info */
	__u64 rsvd2;
};

/** Resets the SO write offset registers for transform feedback on gen7. */
#define I915_EXEC_GEN7_SOL_RESET	(1<<8)

/** Request a privileged ("secure") batch buffer. Note only available for
 * DRM_ROOT_ONLY | DRM_MASTER processes.
 */
#define I915_EXEC_SECURE		(1<<9)

/** Inform the kernel that the batch is and will always be pinned. This
 * negates the requirement for a workaround to be performed to avoid
 * an incoherent CS (such as can be found on 830/845). If this flag is
 * not passed, the kernel will endeavour to make sure the batch is
 * coherent with the CS before execution. If this flag is passed,
 * userspace assumes the responsibility for ensuring the same.
 */
#define I915_EXEC_IS_PINNED		(1<<10)

/** Provide a hint to the kernel that the command stream and auxiliary
 * state buffers already holds the correct presumed addresses and so the
 * relocation process may be skipped if no buffers need to be moved in
 * preparation for the execbuffer.
 */
#define I915_EXEC_NO_RELOC		(1<<11)

/** Use the reloc.handle as an index into the exec object array rather
 * than as the per-file handle.
 */
#define I915_EXEC_HANDLE_LUT		(1<<12)

/** Used for switching BSD rings on the platforms with two BSD rings */
#define I915_EXEC_BSD_SHIFT	 (13)
#define I915_EXEC_BSD_MASK	 (3 << I915_EXEC_BSD_SHIFT)
/* default ping-pong mode */
#define I915_EXEC_BSD_DEFAULT	 (0 << I915_EXEC_BSD_SHIFT)
#define I915_EXEC_BSD_RING1	 (1 << I915_EXEC_BSD_SHIFT)
#define I915_EXEC_BSD_RING2	 (2 << I915_EXEC_BSD_SHIFT)

/** Tell the kernel that the batchbuffer is processed by
 *  the resource streamer.
 */
#define I915_EXEC_RESOURCE_STREAMER     (1<<15)

/* Setting I915_EXEC_FENCE_IN implies that lower_32_bits(rsvd2) represent
 * a sync_file fd to wait upon (in a nonblocking manner) prior to executing
 * the batch.
 *
 * Returns -EINVAL if the sync_file fd cannot be found.
 */
#define I915_EXEC_FENCE_IN		(1<<16)

/* Setting I915_EXEC_FENCE_OUT causes the ioctl to return a sync_file fd
 * in the upper_32_bits(rsvd2) upon success. Ownership of the fd is given
 * to the caller, and it should be close() after use. (The fd is a regular
 * file descriptor and will be cleaned up on process termination. It holds
 * a reference to the request, but nothing else.)
 *
 * The sync_file fd can be combined with other sync_file and passed either
 * to execbuf using I915_EXEC_FENCE_IN, to atomic KMS ioctls (so that a flip
 * will only occur after this request completes), or to other devices.
 *
 * Using I915_EXEC_FENCE_OUT requires use of
 * DRM_IOCTL_I915_GEM_EXECBUFFER2_WR ioctl so that the result is written
 * back to userspace. Failure to do so will cause the out-fence to always
 * be reported as zero, and the real fence fd to be leaked.
 */
#define I915_EXEC_FENCE_OUT		(1<<17)

/*
 * Traditionally the execbuf ioctl has only considered the final element in
 * the execobject[] to be the executable batch. Often though, the client
 * will known the batch object prior to construction and being able to place
 * it into the execobject[] array first can simplify the relocation tracking.
 * Setting I915_EXEC_BATCH_FIRST tells execbuf to use element 0 of the
 * execobject[] as the * batch instead (the default is to use the last
 * element).
 */
#define I915_EXEC_BATCH_FIRST		(1<<18)

/* Setting I915_FENCE_ARRAY implies that num_cliprects and cliprects_ptr
 * define an array of i915_gem_exec_fence structures which specify a set of
 * dma fences to wait upon or signal.
 */
#define I915_EXEC_FENCE_ARRAY   (1<<19)

#define __I915_EXEC_UNKNOWN_FLAGS (-(I915_EXEC_FENCE_ARRAY<<1))

#define I915_EXEC_CONTEXT_ID_MASK	(0xffffffff)
#define i915_execbuffer2_set_context_id(eb2, context) \
	(eb2).rsvd1 = context & I915_EXEC_CONTEXT_ID_MASK
#define i915_execbuffer2_get_context_id(eb2) \
	((eb2).rsvd1 & I915_EXEC_CONTEXT_ID_MASK)

struct drm_i915_gem_pin {
	/** Handle of the buffer to be pinned. */
	__u32 handle;
	__u32 pad;

	/** alignment required within the aperture */
	__u64 alignment;

	/** Returned GTT offset of the buffer. */
	__u64 offset;
};

struct drm_i915_gem_unpin {
	/** Handle of the buffer to be unpinned. */
	__u32 handle;
	__u32 pad;
};

struct drm_i915_gem_busy {
	/** Handle of the buffer to check for busy */
	__u32 handle;

	/** Return busy status
	 *
	 * A return of 0 implies that the object is idle (after
	 * having flushed any pending activity), and a non-zero return that
	 * the object is still in-flight on the GPU. (The GPU has not yet
	 * signaled completion for all pending requests that reference the
	 * object.) An object is guaranteed to become idle eventually (so
	 * long as no new GPU commands are executed upon it). Due to the
	 * asynchronous nature of the hardware, an object reported
	 * as busy may become idle before the ioctl is completed.
	 *
	 * Furthermore, if the object is busy, which engine is busy is only
	 * provided as a guide and only indirectly by reporting its class
	 * (there may be more than one engine in each class). There are race
	 * conditions which prevent the report of which engines are busy from
	 * being always accurate.  However, the converse is not true. If the
	 * object is idle, the result of the ioctl, that all engines are idle,
	 * is accurate.
	 *
	 * The returned dword is split into two fields to indicate both
	 * the engine classess on which the object is being read, and the
	 * engine class on which it is currently being written (if any).
	 *
	 * The low word (bits 0:15) indicate if the object is being written
	 * to by any engine (there can only be one, as the GEM implicit
	 * synchronisation rules force writes to be serialised). Only the
	 * engine class (offset by 1, I915_ENGINE_CLASS_RENDER is reported as
	 * 1 not 0 etc) for the last write is reported.
	 *
	 * The high word (bits 16:31) are a bitmask of which engines classes
	 * are currently reading from the object. Multiple engines may be
	 * reading from the object simultaneously.
	 *
	 * The value of each engine class is the same as specified in the
	 * I915_CONTEXT_SET_ENGINES parameter and via perf, i.e.
	 * I915_ENGINE_CLASS_RENDER, I915_ENGINE_CLASS_COPY, etc.
	 * reported as active itself. Some hardware may have parallel
	 * execution engines, e.g. multiple media engines, which are
	 * mapped to the same class identifier and so are not separately
	 * reported for busyness.
	 *
	 * Caveat emptor:
	 * Only the boolean result of this query is reliable; that is whether
	 * the object is idle or busy. The report of which engines are busy
	 * should be only used as a heuristic.
	 */
	__u32 busy;
};

/**
 * I915_CACHING_NONE
 *
 * GPU access is not coherent with cpu caches. Default for machines without an
 * LLC.
 */
#define I915_CACHING_NONE		0
/**
 * I915_CACHING_CACHED
 *
 * GPU access is coherent with cpu caches and furthermore the data is cached in
 * last-level caches shared between cpu cores and the gpu GT. Default on
 * machines with HAS_LLC.
 */
#define I915_CACHING_CACHED		1
/**
 * I915_CACHING_DISPLAY
 *
 * Special GPU caching mode which is coherent with the scanout engines.
 * Transparently falls back to I915_CACHING_NONE on platforms where no special
 * cache mode (like write-through or gfdt flushing) is available. The kernel
 * automatically sets this mode when using a buffer as a scanout target.
 * Userspace can manually set this mode to avoid a costly stall and clflush in
 * the hotpath of drawing the first frame.
 */
#define I915_CACHING_DISPLAY		2

struct drm_i915_gem_caching {
	/**
	 * Handle of the buffer to set/get the caching level of. */
	__u32 handle;

	/**
	 * Caching level to apply or return value
	 *
	 * bits0-15 are for generic caching control (i.e. the above defined
	 * values). bits16-31 are reserved for platform-specific variations
	 * (e.g. l3$ caching on gen7). */
	__u32 caching;
};

#define I915_TILING_NONE	0
#define I915_TILING_X		1
#define I915_TILING_Y		2
#define I915_TILING_LAST	I915_TILING_Y

#define I915_BIT_6_SWIZZLE_NONE		0
#define I915_BIT_6_SWIZZLE_9		1
#define I915_BIT_6_SWIZZLE_9_10		2
#define I915_BIT_6_SWIZZLE_9_11		3
#define I915_BIT_6_SWIZZLE_9_10_11	4
/* Not seen by userland */
#define I915_BIT_6_SWIZZLE_UNKNOWN	5
/* Seen by userland. */
#define I915_BIT_6_SWIZZLE_9_17		6
#define I915_BIT_6_SWIZZLE_9_10_17	7

struct drm_i915_gem_set_tiling {
	/** Handle of the buffer to have its tiling state updated */
	__u32 handle;

	/**
	 * Tiling mode for the object (I915_TILING_NONE, I915_TILING_X,
	 * I915_TILING_Y).
	 *
	 * This value is to be set on request, and will be updated by the
	 * kernel on successful return with the actual chosen tiling layout.
	 *
	 * The tiling mode may be demoted to I915_TILING_NONE when the system
	 * has bit 6 swizzling that can't be managed correctly by GEM.
	 *
	 * Buffer contents become undefined when changing tiling_mode.
	 */
	__u32 tiling_mode;

	/**
	 * Stride in bytes for the object when in I915_TILING_X or
	 * I915_TILING_Y.
	 */
	__u32 stride;

	/**
	 * Returned address bit 6 swizzling required for CPU access through
	 * mmap mapping.
	 */
	__u32 swizzle_mode;
};

struct drm_i915_gem_get_tiling {
	/** Handle of the buffer to get tiling state for. */
	__u32 handle;

	/**
	 * Current tiling mode for the object (I915_TILING_NONE, I915_TILING_X,
	 * I915_TILING_Y).
	 */
	__u32 tiling_mode;

	/**
	 * Returned address bit 6 swizzling required for CPU access through
	 * mmap mapping.
	 */
	__u32 swizzle_mode;

	/**
	 * Returned address bit 6 swizzling required for CPU access through
	 * mmap mapping whilst bound.
	 */
	__u32 phys_swizzle_mode;
};

struct drm_i915_gem_get_aperture {
	/** Total size of the aperture used by i915_gem_execbuffer, in bytes */
	__u64 aper_size;

	/**
	 * Available space in the aperture used by i915_gem_execbuffer, in
	 * bytes
	 */
	__u64 aper_available_size;
};

struct drm_i915_get_pipe_from_crtc_id {
	/** ID of CRTC being requested **/
	__u32 crtc_id;

	/** pipe of requested CRTC **/
	__u32 pipe;
};

#define I915_MADV_WILLNEED 0
#define I915_MADV_DONTNEED 1
#define __I915_MADV_PURGED 2 /* internal state */

struct drm_i915_gem_madvise {
	/** Handle of the buffer to change the backing store advice */
	__u32 handle;

	/* Advice: either the buffer will be needed again in the near future,
	 *         or wont be and could be discarded under memory pressure.
	 */
	__u32 madv;

	/** Whether the backing store still exists. */
	__u32 retained;
};

/* flags */
#define I915_OVERLAY_TYPE_MASK 		0xff
#define I915_OVERLAY_YUV_PLANAR 	0x01
#define I915_OVERLAY_YUV_PACKED 	0x02
#define I915_OVERLAY_RGB		0x03

#define I915_OVERLAY_DEPTH_MASK		0xff00
#define I915_OVERLAY_RGB24		0x1000
#define I915_OVERLAY_RGB16		0x2000
#define I915_OVERLAY_RGB15		0x3000
#define I915_OVERLAY_YUV422		0x0100
#define I915_OVERLAY_YUV411		0x0200
#define I915_OVERLAY_YUV420		0x0300
#define I915_OVERLAY_YUV410		0x0400

#define I915_OVERLAY_SWAP_MASK		0xff0000
#define I915_OVERLAY_NO_SWAP		0x000000
#define I915_OVERLAY_UV_SWAP		0x010000
#define I915_OVERLAY_Y_SWAP		0x020000
#define I915_OVERLAY_Y_AND_UV_SWAP	0x030000

#define I915_OVERLAY_FLAGS_MASK		0xff000000
#define I915_OVERLAY_ENABLE		0x01000000

struct drm_intel_overlay_put_image {
	/* various flags and src format description */
	__u32 flags;
	/* source picture description */
	__u32 bo_handle;
	/* stride values and offsets are in bytes, buffer relative */
	__u16 stride_Y; /* stride for packed formats */
	__u16 stride_UV;
	__u32 offset_Y; /* offset for packet formats */
	__u32 offset_U;
	__u32 offset_V;
	/* in pixels */
	__u16 src_width;
	__u16 src_height;
	/* to compensate the scaling factors for partially covered surfaces */
	__u16 src_scan_width;
	__u16 src_scan_height;
	/* output crtc description */
	__u32 crtc_id;
	__u16 dst_x;
	__u16 dst_y;
	__u16 dst_width;
	__u16 dst_height;
};

/* flags */
#define I915_OVERLAY_UPDATE_ATTRS	(1<<0)
#define I915_OVERLAY_UPDATE_GAMMA	(1<<1)
#define I915_OVERLAY_DISABLE_DEST_COLORKEY	(1<<2)
struct drm_intel_overlay_attrs {
	__u32 flags;
	__u32 color_key;
	__s32 brightness;
	__u32 contrast;
	__u32 saturation;
	__u32 gamma0;
	__u32 gamma1;
	__u32 gamma2;
	__u32 gamma3;
	__u32 gamma4;
	__u32 gamma5;
};

/*
 * Intel sprite handling
 *
 * Color keying works with a min/mask/max tuple.  Both source and destination
 * color keying is allowed.
 *
 * Source keying:
 * Sprite pixels within the min & max values, masked against the color channels
 * specified in the mask field, will be transparent.  All other pixels will
 * be displayed on top of the primary plane.  For RGB surfaces, only the min
 * and mask fields will be used; ranged compares are not allowed.
 *
 * Destination keying:
 * Primary plane pixels that match the min value, masked against the color
 * channels specified in the mask field, will be replaced by corresponding
 * pixels from the sprite plane.
 *
 * Note that source & destination keying are exclusive; only one can be
 * active on a given plane.
 */

#define I915_SET_COLORKEY_NONE		(1<<0) /* Deprecated. Instead set
						* flags==0 to disable colorkeying.
						*/
#define I915_SET_COLORKEY_DESTINATION	(1<<1)
#define I915_SET_COLORKEY_SOURCE	(1<<2)
struct drm_intel_sprite_colorkey {
	__u32 plane_id;
	__u32 min_value;
	__u32 channel_mask;
	__u32 max_value;
	__u32 flags;
};

struct drm_i915_gem_wait {
	/** Handle of BO we shall wait on */
	__u32 bo_handle;
	__u32 flags;
	/** Number of nanoseconds to wait, Returns time remaining. */
	__s64 timeout_ns;
};

struct drm_i915_gem_context_create {
	__u32 ctx_id; /* output: id of new context*/
	__u32 pad;
};

struct drm_i915_gem_context_create_ext {
	__u32 ctx_id; /* output: id of new context*/
	__u32 flags;
#define I915_CONTEXT_CREATE_FLAGS_USE_EXTENSIONS	(1u << 0)
#define I915_CONTEXT_CREATE_FLAGS_UNKNOWN \
	(-(I915_CONTEXT_CREATE_FLAGS_USE_EXTENSIONS << 1))
	__u64 extensions;
};

struct drm_i915_gem_context_param {
	__u32 ctx_id;
	__u32 size;
	__u64 param;
#define I915_CONTEXT_PARAM_BAN_PERIOD	0x1
#define I915_CONTEXT_PARAM_NO_ZEROMAP	0x2
#define I915_CONTEXT_PARAM_GTT_SIZE	0x3
#define I915_CONTEXT_PARAM_NO_ERROR_CAPTURE	0x4
#define I915_CONTEXT_PARAM_BANNABLE	0x5
#define I915_CONTEXT_PARAM_PRIORITY	0x6
#define   I915_CONTEXT_MAX_USER_PRIORITY	1023 /* inclusive */
#define   I915_CONTEXT_DEFAULT_PRIORITY		0
#define   I915_CONTEXT_MIN_USER_PRIORITY	-1023 /* inclusive */
	/*
	 * When using the following param, value should be a pointer to
	 * drm_i915_gem_context_param_sseu.
	 */
#define I915_CONTEXT_PARAM_SSEU		0x7

/*
 * Not all clients may want to attempt automatic recover of a context after
 * a hang (for example, some clients may only submit very small incremental
 * batches relying on known logical state of previous batches which will never
 * recover correctly and each attempt will hang), and so would prefer that
 * the context is forever banned instead.
 *
 * If set to false (0), after a reset, subsequent (and in flight) rendering
 * from this context is discarded, and the client will need to create a new
 * context to use instead.
 *
 * If set to true (1), the kernel will automatically attempt to recover the
 * context by skipping the hanging batch and executing the next batch starting
 * from the default context state (discarding the incomplete logical context
 * state lost due to the reset).
 *
 * On creation, all new contexts are marked as recoverable.
 */
#define I915_CONTEXT_PARAM_RECOVERABLE	0x8
/* Must be kept compact -- no holes and well documented */

	__u64 value;
};

/**
 * Context SSEU programming
 *
 * It may be necessary for either functional or performance reason to configure
 * a context to run with a reduced number of SSEU (where SSEU stands for Slice/
 * Sub-slice/EU).
 *
 * This is done by configuring SSEU configuration using the below
 * @struct drm_i915_gem_context_param_sseu for every supported engine which
 * userspace intends to use.
 *
 * Not all GPUs or engines support this functionality in which case an error
 * code -ENODEV will be returned.
 *
 * Also, flexibility of possible SSEU configuration permutations varies between
 * GPU generations and software imposed limitations. Requesting such a
 * combination will return an error code of -EINVAL.
 *
 * NOTE: When perf/OA is active the context's SSEU configuration is ignored in
 * favour of a single global setting.
 */
struct drm_i915_gem_context_param_sseu {
	/*
	 * Engine class & instance to be configured or queried.
	 */
	__u16 engine_class;
	__u16 engine_instance;

	/*
	 * Unused for now. Must be cleared to zero.
	 */
	__u32 flags;

	/*
	 * Mask of slices to enable for the context. Valid values are a subset
	 * of the bitmask value returned for I915_PARAM_SLICE_MASK.
	 */
	__u64 slice_mask;

	/*
	 * Mask of subslices to enable for the context. Valid values are a
	 * subset of the bitmask value return by I915_PARAM_SUBSLICE_MASK.
	 */
	__u64 subslice_mask;

	/*
	 * Minimum/Maximum number of EUs to enable per subslice for the
	 * context. min_eus_per_subslice must be inferior or equal to
	 * max_eus_per_subslice.
	 */
	__u16 min_eus_per_subslice;
	__u16 max_eus_per_subslice;

	/*
	 * Unused for now. Must be cleared to zero.
	 */
	__u32 rsvd;
};

struct drm_i915_gem_context_create_ext_setparam {
#define I915_CONTEXT_CREATE_EXT_SETPARAM 0
	struct i915_user_extension base;
	struct drm_i915_gem_context_param param;
};

struct drm_i915_gem_context_destroy {
	__u32 ctx_id;
	__u32 pad;
};

/*
 * DRM_I915_GEM_VM_CREATE -
 *
 * Create a new virtual memory address space (ppGTT) for use within a context
 * on the same file. Extensions can be provided to configure exactly how the
 * address space is setup upon creation.
 *
 * The id of new VM (bound to the fd) for use with I915_CONTEXT_PARAM_VM is
 * returned in the outparam @id.
 *
 * No flags are defined, with all bits reserved and must be zero.
 *
 * An extension chain maybe provided, starting with @extensions, and terminated
 * by the @next_extension being 0. Currently, no extensions are defined.
 *
 * DRM_I915_GEM_VM_DESTROY -
 *
 * Destroys a previously created VM id, specified in @id.
 *
 * No extensions or flags are allowed currently, and so must be zero.
 */
struct drm_i915_gem_vm_control {
	__u64 extensions;
	__u32 flags;
	__u32 vm_id;
};

struct drm_i915_reg_read {
	/*
	 * Register offset.
	 * For 64bit wide registers where the upper 32bits don't immediately
	 * follow the lower 32bits, the offset of the lower 32bits must
	 * be specified
	 */
	__u64 offset;
#define I915_REG_READ_8B_WA (1ul << 0)

	__u64 val; /* Return value */
};

/* Known registers:
 *
 * Render engine timestamp - 0x2358 + 64bit - gen7+
 * - Note this register returns an invalid value if using the default
 *   single instruction 8byte read, in order to workaround that pass
 *   flag I915_REG_READ_8B_WA in offset field.
 *
 */

struct drm_i915_reset_stats {
	__u32 ctx_id;
	__u32 flags;

	/* All resets since boot/module reload, for all contexts */
	__u32 reset_count;

	/* Number of batches lost when active in GPU, for this context */
	__u32 batch_active;

	/* Number of batches lost pending for execution, for this context */
	__u32 batch_pending;

	__u32 pad;
};

struct drm_i915_gem_userptr {
	__u64 user_ptr;
	__u64 user_size;
	__u32 flags;
#define I915_USERPTR_READ_ONLY 0x1
#define I915_USERPTR_UNSYNCHRONIZED 0x80000000
	/**
	 * Returned handle for the object.
	 *
	 * Object handles are nonzero.
	 */
	__u32 handle;
};

enum drm_i915_oa_format {
	I915_OA_FORMAT_A13 = 1,	    /* HSW only */
	I915_OA_FORMAT_A29,	    /* HSW only */
	I915_OA_FORMAT_A13_B8_C8,   /* HSW only */
	I915_OA_FORMAT_B4_C8,	    /* HSW only */
	I915_OA_FORMAT_A45_B8_C8,   /* HSW only */
	I915_OA_FORMAT_B4_C8_A16,   /* HSW only */
	I915_OA_FORMAT_C4_B8,	    /* HSW+ */

	/* Gen8+ */
	I915_OA_FORMAT_A12,
	I915_OA_FORMAT_A12_B8_C8,
	I915_OA_FORMAT_A32u40_A4u32_B8_C8,

	I915_OA_FORMAT_MAX	    /* non-ABI */
};

enum drm_i915_perf_property_id {
	/**
	 * Open the stream for a specific context handle (as used with
	 * execbuffer2). A stream opened for a specific context this way
	 * won't typically require root privileges.
	 */
	DRM_I915_PERF_PROP_CTX_HANDLE = 1,

	/**
	 * A value of 1 requests the inclusion of raw OA unit reports as
	 * part of stream samples.
	 */
	DRM_I915_PERF_PROP_SAMPLE_OA,

	/**
	 * The value specifies which set of OA unit metrics should be
	 * be configured, defining the contents of any OA unit reports.
	 */
	DRM_I915_PERF_PROP_OA_METRICS_SET,

	/**
	 * The value specifies the size and layout of OA unit reports.
	 */
	DRM_I915_PERF_PROP_OA_FORMAT,

	/**
	 * Specifying this property implicitly requests periodic OA unit
	 * sampling and (at least on Haswell) the sampling frequency is derived
	 * from this exponent as follows:
	 *
	 *   80ns * 2^(period_exponent + 1)
	 */
	DRM_I915_PERF_PROP_OA_EXPONENT,

	DRM_I915_PERF_PROP_MAX /* non-ABI */
};

struct drm_i915_perf_open_param {
	__u32 flags;
#define I915_PERF_FLAG_FD_CLOEXEC	(1<<0)
#define I915_PERF_FLAG_FD_NONBLOCK	(1<<1)
#define I915_PERF_FLAG_DISABLED		(1<<2)

	/** The number of u64 (id, value) pairs */
	__u32 num_properties;

	/**
	 * Pointer to array of u64 (id, value) pairs configuring the stream
	 * to open.
	 */
	__u64 properties_ptr;
};

/**
 * Enable data capture for a stream that was either opened in a disabled state
 * via I915_PERF_FLAG_DISABLED or was later disabled via
 * I915_PERF_IOCTL_DISABLE.
 *
 * It is intended to be cheaper to disable and enable a stream than it may be
 * to close and re-open a stream with the same configuration.
 *
 * It's undefined whether any pending data for the stream will be lost.
 */
#define I915_PERF_IOCTL_ENABLE	_IO('i', 0x0)

/**
 * Disable data capture for a stream.
 *
 * It is an error to try and read a stream that is disabled.
 */
#define I915_PERF_IOCTL_DISABLE	_IO('i', 0x1)

/**
 * Common to all i915 perf records
 */
struct drm_i915_perf_record_header {
	__u32 type;
	__u16 pad;
	__u16 size;
};

enum drm_i915_perf_record_type {

	/**
	 * Samples are the work horse record type whose contents are extensible
	 * and defined when opening an i915 perf stream based on the given
	 * properties.
	 *
	 * Boolean properties following the naming convention
	 * DRM_I915_PERF_SAMPLE_xyz_PROP request the inclusion of 'xyz' data in
	 * every sample.
	 *
	 * The order of these sample properties given by userspace has no
	 * affect on the ordering of data within a sample. The order is
	 * documented here.
	 *
	 * struct {
	 *     struct drm_i915_perf_record_header header;
	 *
	 *     { u32 oa_report[]; } && DRM_I915_PERF_PROP_SAMPLE_OA
	 * };
	 */
	DRM_I915_PERF_RECORD_SAMPLE = 1,

	/*
	 * Indicates that one or more OA reports were not written by the
	 * hardware. This can happen for example if an MI_REPORT_PERF_COUNT
	 * command collides with periodic sampling - which would be more likely
	 * at higher sampling frequencies.
	 */
	DRM_I915_PERF_RECORD_OA_REPORT_LOST = 2,

	/**
	 * An error occurred that resulted in all pending OA reports being lost.
	 */
	DRM_I915_PERF_RECORD_OA_BUFFER_LOST = 3,

	DRM_I915_PERF_RECORD_MAX /* non-ABI */
};

/**
 * Structure to upload perf dynamic configuration into the kernel.
 */
struct drm_i915_perf_oa_config {
	/** String formatted like "%08x-%04x-%04x-%04x-%012x" */
	char uuid[36];

	__u32 n_mux_regs;
	__u32 n_boolean_regs;
	__u32 n_flex_regs;

	/*
	 * These fields are pointers to tuples of u32 values (register address,
	 * value). For example the expected length of the buffer pointed by
	 * mux_regs_ptr is (2 * sizeof(u32) * n_mux_regs).
	 */
	__u64 mux_regs_ptr;
	__u64 boolean_regs_ptr;
	__u64 flex_regs_ptr;
};

struct drm_i915_query_item {
	__u64 query_id;
#define DRM_I915_QUERY_TOPOLOGY_INFO    1
/* Must be kept compact -- no holes and well documented */

	/*
	 * When set to zero by userspace, this is filled with the size of the
	 * data to be written at the data_ptr pointer. The kernel sets this
	 * value to a negative value to signal an error on a particular query
	 * item.
	 */
	__s32 length;

	/*
	 * Unused for now. Must be cleared to zero.
	 */
	__u32 flags;

	/*
	 * Data will be written at the location pointed by data_ptr when the
	 * value of length matches the length of the data to be written by the
	 * kernel.
	 */
	__u64 data_ptr;
};

struct drm_i915_query {
	__u32 num_items;

	/*
	 * Unused for now. Must be cleared to zero.
	 */
	__u32 flags;

	/*
	 * This points to an array of num_items drm_i915_query_item structures.
	 */
	__u64 items_ptr;
};

/*
 * Data written by the kernel with query DRM_I915_QUERY_TOPOLOGY_INFO :
 *
 * data: contains the 3 pieces of information :
 *
 * - the slice mask with one bit per slice telling whether a slice is
 *   available. The availability of slice X can be queried with the following
 *   formula :
 *
 *           (data[X / 8] >> (X % 8)) & 1
 *
 * - the subslice mask for each slice with one bit per subslice telling
 *   whether a subslice is available. The availability of subslice Y in slice
 *   X can be queried with the following formula :
 *
 *           (data[subslice_offset +
 *                 X * subslice_stride +
 *                 Y / 8] >> (Y % 8)) & 1
 *
 * - the EU mask for each subslice in each slice with one bit per EU telling
 *   whether an EU is available. The availability of EU Z in subslice Y in
 *   slice X can be queried with the following formula :
 *
 *           (data[eu_offset +
 *                 (X * max_subslices + Y) * eu_stride +
 *                 Z / 8] >> (Z % 8)) & 1
 */
struct drm_i915_query_topology_info {
	/*
	 * Unused for now. Must be cleared to zero.
	 */
	__u16 flags;

	__u16 max_slices;
	__u16 max_subslices;
	__u16 max_eus_per_subslice;

	/*
	 * Offset in data[] at which the subslice masks are stored.
	 */
	__u16 subslice_offset;

	/*
	 * Stride at which each of the subslice masks for each slice are
	 * stored.
	 */
	__u16 subslice_stride;

	/*
	 * Offset in data[] at which the EU masks are stored.
	 */
	__u16 eu_offset;

	/*
	 * Stride at which each of the EU masks for each subslice are stored.
	 */
	__u16 eu_stride;

	__u8 data[];
};

#if defined(__cplusplus)
}
#endif

#endif /* _I915_DRM_H_ */
