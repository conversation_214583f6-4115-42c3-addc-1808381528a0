/*
 * Copyright (C) 2016 Rockchip Electronics Co.Ltd
 * Authors: <AUTHORS>
 *
 * This program is free software; you can redistribute  it and/or modify it
 * under  the terms of  the GNU General  Public License as published by the
 * Free Software Foundation;  either version 2 of the  License, or (at your
 * option) any later version.
 *
 */

#include "../include/RockchipRga.h"
#include <cstring>
#include <cstdio>

// Stub implementation for RockchipRga class
// This is a minimal implementation that allows compilation without actual RGA hardware

RockchipRga::RockchipRga() {
    mSupportRga = false;  // Indicate RGA is not supported in this stub
    mLogOnce = 0;
    mLogAlways = false;
    mContext = nullptr;
    printf("RockchipRga: Using stub implementation (no hardware acceleration)\n");
}

RockchipRga::~RockchipRga() {
    RkRgaDeInit();
}

int RockchipRga::RkRgaInit() {
    printf("RockchipRga::RkRgaInit() - stub implementation\n");
    return 0;  // Success
}

void RockchipRga::RkRgaDeInit() {
    printf("RockchipRga::RkRgaDeInit() - stub implementation\n");
}

int RockchipRga::RkRgaAllocBuffer(int drm_fd, bo_t *bo_info, int width, int height, int bpp) {
    printf("RockchipRga::RkRgaAllocBuffer() - stub implementation\n");
    return -1;  // Not implemented
}

int RockchipRga::RkRgaFreeBuffer(int drm_fd, bo_t *bo_info) {
    printf("RockchipRga::RkRgaFreeBuffer() - stub implementation\n");
    return -1;  // Not implemented
}

int RockchipRga::RkRgaGetAllocBuffer(bo_t *bo_info, int width, int height, int bpp) {
    printf("RockchipRga::RkRgaGetAllocBuffer() - stub implementation\n");
    return -1;  // Not implemented
}

int RockchipRga::RkRgaGetMmap(bo_t *bo_info) {
    printf("RockchipRga::RkRgaGetMmap() - stub implementation\n");
    return -1;  // Not implemented
}

int RockchipRga::RkRgaUnmap(bo_t *bo_info) {
    printf("RockchipRga::RkRgaUnmap() - stub implementation\n");
    return -1;  // Not implemented
}

int RockchipRga::RkRgaFree(bo_t *bo_info) {
    printf("RockchipRga::RkRgaFree() - stub implementation\n");
    return -1;  // Not implemented
}

int RockchipRga::RkRgaGetBufferFd(bo_t *bo_info, int *fd) {
    printf("RockchipRga::RkRgaGetBufferFd() - stub implementation\n");
    return -1;  // Not implemented
}

// Simple software-based image format conversion (basic implementation)
int RockchipRga::RkRgaBlit(rga_info *src, rga_info *dst, rga_info *src1) {
    if (!src || !dst) {
        printf("RockchipRga::RkRgaBlit() - Invalid parameters\n");
        return -1;
    }
    
    printf("RockchipRga::RkRgaBlit() - stub implementation (software fallback)\n");
    printf("  src: %dx%d format=%d\n", src->rect.width, src->rect.height, src->rect.format);
    printf("  dst: %dx%d format=%d\n", dst->rect.width, dst->rect.height, dst->rect.format);
    
    // Simple memcpy for same format and size (basic fallback)
    if (src->rect.format == dst->rect.format && 
        src->rect.width == dst->rect.width && 
        src->rect.height == dst->rect.height &&
        src->virAddr && dst->virAddr) {
        
        int bytes_per_pixel = 3; // Default to RGB888
        if (src->rect.format == 0x15) bytes_per_pixel = 4; // RGBA8888
        else if (src->rect.format == 0x11) bytes_per_pixel = 2; // YUV420SP (1.5 bytes per pixel)
        
        int data_size = src->rect.width * src->rect.height * bytes_per_pixel;
        if (src->rect.format == 0x11) data_size = src->rect.width * src->rect.height * 3 / 2; // YUV420SP
        
        memcpy(dst->virAddr, src->virAddr, data_size);
        printf("  Copied %d bytes (simple memcpy fallback)\n", data_size);
        return 0; // Success
    }
    
    printf("  Format conversion not implemented in stub - returning error\n");
    return -1; // Format conversion not implemented in stub
}

int RockchipRga::RkRgaCollorFill(rga_info *dst) {
    printf("RockchipRga::RkRgaCollorFill() - stub implementation\n");
    return -1;  // Not implemented
}

void RockchipRga::RkRgaLogOutRgaReq(struct rga_req rgaReg) {
    printf("RockchipRga::RkRgaLogOutRgaReq() - stub implementation\n");
}

int RockchipRga::RkRgaLogOutUserPara(rga_info *rgaInfo) {
    printf("RockchipRga::RkRgaLogOutUserPara() - stub implementation\n");
    return 0;
}
