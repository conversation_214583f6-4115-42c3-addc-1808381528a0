/*
    i2cbusses.h - Part of the i2c-tools package

    Copyright (C) 2004-2010  <PERSON> <j<PERSON><EMAIL>>

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
    MA 02110-1301 USA.
*/

#ifndef _I2CBUSSES_H
#define _I2CBUSSES_H

#include <unistd.h>

struct i2c_adap {
	int nr;
	char *name;
	const char *funcs;
	const char *algo;
};

#ifdef __cplusplus
extern "C" {
#endif

struct i2c_adap *gather_i2c_busses(void);
extern void free_adapters(struct i2c_adap *adapters);

extern int lookup_i2c_bus(const char *i2cbus_arg);
extern int parse_i2c_address(const char *address_arg);
extern int open_i2c_dev(int i2cbus, char *filename, size_t size, int quiet);
extern int set_slave_addr(int file, int address, int force);
#ifdef __cplusplus
}
#endif
#define MISSING_FUNC_FMT	"Error: Adapter does not have %s capability\n"

#endif
