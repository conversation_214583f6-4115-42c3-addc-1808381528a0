/***********************************************************************
		XS508G_濞村鐦稉鑽も柤鎼村唬13 2016
***********************************************************************/
#include "XS508.h"
#include <stdio.h>		//printf
#include <stdlib.h>		//srand rand
//#include "delay.h"		//delay_ms() delay_us()

#include <sys/ioctl.h>
#include <errno.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

#include <linux/i2c.h>
#include <linux/i2c-dev.h>
#include "i2c/smbus.h"
#include "i2cbusses.h"
#include "util.h"


#include <string.h>
//#include <android/log.h>
//#define TAG    "zzz" // 这个是自定义的LOG的标识
//#define LOGEE(...)  __android_log_print(ANDROID_LOG_ERROR,TAG,__VA_ARGS__) // 定义LOGD类型


#define I2CBUS 2    /*G3 -- 2 ,  AODS -- 5*/
#define XS508_ADDR 0x29

#define XS508_DT1    1          //1ms
#define EC_test 1
#define EC_SLEEP 0		//娴兼垹婀㈤崥搴礉閼侯垳澧栭懓妤冩暩鐏忓繋绨�1uA閵嗗倷绲鹃弰鐥�2c閸炪倝鍟嬮棁锟界憰浣规闂傛番锟斤拷
#define EC_STOP  0		//濞夈劍鍓伴幍褑顢憇top娴犮儱鎮楅崣顏呮箒闁插秵鏌婃稉濠勬暩閿涘S508G閹靛秴褰插銉ょ稊閵嗗倽顕柅澶嬪閸氬牓锟藉倻娈戦崷鐗堟煙娴ｈ法鏁ら妴锟�

static int gAplufile=0;
extern int XS508_I2C_Handshake(unsigned char *XS508_16B_Ukey,unsigned char *X508_16B_ID);

extern int XS508_Handshake(unsigned char *Ukey,unsigned char *Udata);
/***********************************************************************
//闂囷拷鐟曚胶鏁ら幋宄扮杽閻滅櫡2c鏉╃偟鐢婚崘娆忓毐閺侊拷
//return  0:  i2c write pass ,ack pass
//       -1:  i2c write error ,no ack     
***********************************************************************/
int HI2C_write25Byte( unsigned char *buffer)
{
//XS508I2C閸︽澘娼冩稉锟�0x29,鐎电懓绨查惃鍕晸閹稿洣鎶ゆ稉锟�0x52,鐠囩粯瀵氭禒銈勮礋0x53;闁俺顔嗛柅鐔峰100K閵嗭拷
//鐠囬鏁ら幋宄扮殺buffer娑擄拷25娑擃亝鏆熼幑顕嗙礉閼风寜S508閻拷0x07婢跺嫯绻涚紒顓炲晸閸戠尨绱濋獮鑸殿梾濞村鐦″▎顡濻508閻ㄥ嫭婀侀弫鍫濈安缁涙柣锟斤拷
    int sub_addr = 0x07;
	int ByteNo=25;
	int ret = i2c_smbus_write_i2c_block_data(gAplufile, sub_addr, ByteNo, buffer);
	if(ret > 0)
		ret=0;
	return ret;
}
/***********************************************************************
//闂囷拷鐟曚胶鏁ら幋宄扮杽閻滅櫡2c鏉╃偟鐢荤拠璇插毐閺侊拷
//return  0:  i2c read pass,ack pass
//       -1:  i2c read error 
***********************************************************************/
int  HI2C_read32Byte(unsigned char *buffer)
{
//鐠囬鏁ら幋鐤殰XS508閻拷0x00婢跺嫯绻涚紒顓☆嚢閸忥拷32娑擃亜鐡ч懞鍌涙殶閹诡噯绱濈�涙ê鍙哹uffer閿涳拷
//閺堢喖妫块崷銊ュ絺閸愭瑦瀵氭禒锟�0x52閵嗕簛s508閸愬懏瀵氶柦锟�0x00閸滃矁顕伴幐鍥︽姢0x53閺冭绱濇惔鏃�顥呭ù濯係508閻拷3濞嗏剝婀侀弫鍫濈安缁涙柣锟斤拷
    int sub_addr = 0x00;
	int ByteNo=32;
	int ret =i2c_smbus_read_i2c_block_data(gAplufile, sub_addr, ByteNo, buffer);
	if(ret > 0)
		ret=0;
	return ret;	
}

//闂囷拷鐟曚胶鏁ら幋宄扮杽閻滅櫩s瀵よ埖妞傞崙鑺ユ殶
void XS508_delay_ms(int D_TIME)
{
	//delay_ms(D_TIME);	
	usleep(D_TIME*1000);
	return;
}


//闂囷拷鐟曚胶鏁ら幋閿嬪絹娓氭稖绶熼崝鈺呮閺堝搫鍤遍弫锟�
int get_xs_srand(void)
{
	/*
	volatile int Xstimeus; 
	#include "sys/time.h"
	struct timeval tv;
	int tz;
	gettimeofday (&tv , &tz);
	Xstimeus += (int)(tv.tv_sec*1000000+tv.tv_usec);
	srand(Xstimeus);
	Xstimeus+=rand();	
	*/
	int Xstimeus = rand();
	return Xstimeus;
}

/*********************************************
//  XS508G -> stop  XS508閸忔娊妫撮崥搴濈窗闁插﹥鏂両2C閹崵鍤庨敍灞藉讲閸忓秹娅庣�电懓鍙炬禒鏈�2C鐠佹儳顦惃鍕閸濆秲锟斤拷
//  IN:  null
// OUT:  0->ok
//      -1->I2C WRITE OR READ ERROR
//      -2->other error
*********************************************/
int XS508G_STOP_Handshake(void)
{
	unsigned char wr_data_25_byte[25];
	unsigned char rd_data_32_byte[32]={0};
	int i,check_cnt;
	//int debugset=0;;
	int debugset=0xa8;

#ifdef DEBUG_SX508
	if (debugset==0xa8)	 printf("\r\n XS508G ->stop  \r\n T_I2C write	BYTES =\r\n"); 
#endif
		wr_data_25_byte[0]=0XF2;

		
	for(i=1;i<22;i++)
		wr_data_25_byte[i]+=(char)rand();

	wr_data_25_byte[22]=0x5a;;wr_data_25_byte[23]=0x5a;wr_data_25_byte[24]=0x5a;//stop CMD

#ifdef DEBUG_SX508
	if (debugset==0xa8)
		{
			for(i=0;i<25;i++)  
				printf("0x%2x,",wr_data_25_byte[i]);

			printf("\r\n"); 
		}
#endif

	check_cnt=0;
	while(HI2C_write25Byte(wr_data_25_byte))
		{
			XS508_delay_ms(XS508_DT1);//
			check_cnt++;
			if (check_cnt>100)
			{
#ifdef DEBUG_SX508
				if (debugset==0xa8)	 printf("X508 I2C Write  error \n");
#endif
					return -1;  
			}
		}


		XS508_delay_ms(10);//delay 10ms
	

	check_cnt=0;
	while(HI2C_read32Byte(rd_data_32_byte))
		{
				XS508_delay_ms(XS508_DT1);//
				check_cnt++;
				if (check_cnt>5)
				{
#ifdef DEBUG_SX508
					if (debugset==0xa8)	 printf("XS508 is stop \r\n");
#endif
					return 0;   
				}
		}
		return -2; 	
	
}

/*********************************************
//   XS508G -> sleep
//  IN:  null
// OUT:  0-> sleep
//      -1->I2C WRITE OR READ ERROR
*********************************************/
int XS508G_SLEEP_Handshake(void)
{
	unsigned char wr_data_25_byte[25];
	int i,check_cnt;
		//int debugset=0;
	int debugset=0xa8;

#ifdef DEBUG_SX508
	if (debugset==0xa8)	 printf("\r\nXS508G -> sleep \r\n T_I2C write	BYTES =\r\n"); 
#endif
	for(i=1;i<22;i++)
		wr_data_25_byte[i]+=(char)rand();

	wr_data_25_byte[22]=0xa5;;wr_data_25_byte[23]=0xa5;wr_data_25_byte[24]=0xa5;//sleep CMD
	wr_data_25_byte[0]=0xf2;
#ifdef DEBUG_SX508
	if (debugset==0xa8)
		{
			for(i=0;i<25;i++)  
				printf("0x%2x,",wr_data_25_byte[i]);

			printf("\r\n"); 
		}
#endif
	check_cnt=0;
	while(HI2C_write25Byte(wr_data_25_byte))
		{
			XS508_delay_ms(XS508_DT1);//
			check_cnt++;
			if (check_cnt>100)
			{
#ifdef DEBUG_SX508
				if (debugset==0xa8)	 printf("X508E I2C Write  error \n");
#endif
					return -1;  
			}																	
		}
#ifdef DEBUG_SX508
	if (debugset==0xa8)	printf ("X508E is sleep  !\r\n");
#endif
   return 0;				
				
}



//濮濄倕顦╅悽銊ょ艾缂傛牞鐦S508_Main()閿涘本婀佹惔鎾冲毐閺佹澘鎮楅棁锟界憰浣稿灩闂勶拷
/*
int T_XS508_Handshake(unsigned char *Ukey,unsigned char *Udata)
{
	printf("\r\n no LibXS508\r\n"); 
	return -2;
}
*/
/*********************************************
//   XS508_Main()
//  IN:  null
// OUT:  0		-> Pass
//      Other	->Fail
*********************************************/
//int XS508_Main(void)
//int main(void)
int XS508_alg_run(void)
{
	  char filename[20];
      	  int res=0, i2cbus=I2CBUS, address=XS508_ADDR;
	  int daddress;
	  int force = 0;
	  
	  gAplufile = open_i2c_dev(i2cbus, filename, sizeof(filename), 0);

	  if (gAplufile < 0 || set_slave_addr(gAplufile, address, force)){
		printf("open i2cbus %d %s\ error %d",i2cbus,filename,gAplufile);
		return -11;
	  }

	 // printf("i2cbus: 0x%x, address 0x%x\n",i2cbus,address);	
	
	/*閻㈢喍楠囬崜宥夘暕缂冾喚娈戦敍宀�鏁ら幋宄板讲閼奉亜鐣炬稊澶屾畱16鐎涙濡璳ey,閿涘牆顓归幋閿嬫弓閹稿洤鐣鹃惃鍕帛鐠併倕锟介棿璐�16娑擃亜鐡ч懞锟�0xff閿涳拷,瀵ら缚顔呮担璺ㄦ暏鐎瑰本鐦粩瀣煝閺囧瓨鏁奸崗璺哄敶鐎癸拷*/
	//妫板嫮鐤嗛弮鍫曟付鐎广垺鍩涢幓鎰返key閿涘本鏋冩禒鑸电壐瀵繐褰查崣鍌濓拷鍍i160508Key508_16Bytes.txt
	unsigned char XS508_KEY[16]={0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF, 0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF};	
	/*婢圭増妲�16BYTE缁屾椽妫� 閿涘苯缂撶拋顔诲▏閻€劌鐣В鏇犵彌閸掔粯娲块弨鐟板従閸愬懎顔愰妴锟�*/
	unsigned char XS508_DAT[16]={0};//閼侯垳澧栭崘鍛毉閸樺倸锟介棿璐�16鐎涙濡�0XFF

  //閻㈢喍楠囬崜宥夘暕缂冾喚娈戦敍宀�鏁ら幋宄板讲閼奉亜鐣炬稊澶屾畱16鐎涙濡璱d閻ㄥ嫬锟斤拷,閿涘牆顓归幋閿嬫弓閹稿洤鐣鹃惃鍕帛鐠併倕锟介棿璐�16娑擃亜鐡ч懞锟�0xff閿涳拷,瀵ら缚顔呮担璺ㄦ暏鐎瑰本鐦粩瀣煝閺囧瓨鏁奸崗璺哄敶鐎癸拷*/
  //妫板嫮鐤嗛弮鍫曟付鐎广垺鍩涢幓鎰返id閿涘本鐦￠悧鍥П閻楀洨娈慖D閸婄厧褰叉稉宥呮倱閿涘本鏋冩禒鑸电壐瀵繐褰查崣鍌濓拷鍍i160508ID508_16Bytes.txt
	unsigned char XS508_id0[16]={0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF, 0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF};		
		
	int i1 =0, i2,t1_cnt;
	//LOGEE("zzz  To be run XS508_Handshake()\n");
	//printf("  To be run XS508_Handshake()\r\n"); 
	XS508_delay_ms(100);  	    //娑撳﹦鏁哥粵澶婄窡100ms

	

	for(t1_cnt=0;t1_cnt<2;t1_cnt++)//瀵倸鐖堕崚娆忔儕閻滎垱绁寸拠锟�2
	{
			i1=XS508_I2C_Handshake(XS508_KEY,XS508_DAT);
			if( i1!=0 )
			{
				//LOGEE("zzz XS508_I2C_Handshake() err=%d:\n",i1);
				//printf("\r\n XS508_I2C_Handshake() err=%d:\r\n",i1); 
				i1=-1;
			}
			else
			{
				XS508_delay_ms(10); 
				//--------------------get ID from xs508-----------------------------------//
				if (EC_test)
				{
				#if 1
					i1=XS508_Handshake(XS508_KEY,XS508_DAT);	// 閺堝鏁ら幋铚傜瑩閻€劌绨遍崙鑺ユ殶閸氬骸鎯庨悽锟�
				#else
					i1=T_XS508_Handshake(XS508_KEY,XS508_DAT);// 閺堝绨遍崙鑺ユ殶閸氬骸鍨归梽锟�
				#endif
					if(0==i1)
					{
						//鐠囬鏁ら幋鐤殰鐎规矮绠焛d閸婅偐娈戦悽銊︾《閿涘本顒濇径鍕矌閸掋倕鐣鹃崘鍛啇閵嗭拷
						//LOGEE("zzz \n XS508_Handshake() Pass,ID=" );
#ifdef DEBUG_SX508
						printf("\r\n XS508_Handshake() Pass,ID=" );
#endif
						for(i2=0;i2<16;i2++)
						{
							//LOGEE("zzz 0x%2x,",XS508_DAT[i2] );
#ifdef DEBUG_SX508
							printf("0x%2x,",XS508_DAT[i2] );
#endif
							if (XS508_DAT[i2]!=XS508_id0[i2])
							{
								//LOGEE("zzz !!! XS508 ID CHECK FAIL :" );
#ifdef DEBUG_SX508
								printf(" !!! XS508 ID CHECK FAIL :" );
#endif
								i1=-5;
								goto NG508;//id闂堢偞纭堕崚娆撳櫢濞村锟斤拷
							}
					
						}
						for(i2=0;i2<16;i2++)
						{
							XS508_KEY[i2]+=rand();//閻€劌鐣崚娆撴敘濮ｄ降锟斤拷
							XS508_DAT[i2]+=rand();
							XS508_id0[i2]+=rand();
						}								
						return 0;	//pass 閸掓瑨绻戦崶锟�0
						if (EC_SLEEP)
						{
					
							return XS508G_SLEEP_Handshake();						
							
						}
						
						if (EC_STOP)
						{
					
							return XS508G_STOP_Handshake();						
							
						}							
											
					}
					else
					{
						//LOGEE("zzz \n XS508_Handshake() err=");
#ifdef DEBUG_SX508
						printf("\r\n XS508_Handshake() err="); 
#endif
NG508:							
						printf("%d:\r\n",i1); 

						
					}
					
				}
			}

				
	}	
	
	for(i2=0;i2<16;i2++)
	{
		XS508_KEY[i2]+=rand();//閻€劌鐣崚娆撴敘濮ｄ共ey閵嗭拷
		XS508_DAT[i2]+=rand();
		XS508_id0[i2]+=rand();
	}		
	return i1;	


}

