#ifndef _TD_RGB_DETECT_H_
#define _TD_RGB_DETECT_H_

#include"tdDetect.h"
#include "tdShareDefine.h"
#include <string>
#include <vector>
#include <unordered_map>

namespace td
{

	typedef struct _RGBInfo
	{
		std::string RGB_text;  //轮廓标签  (每个小圆的标签)
		cv::Point center;
		double dPerimeter;

	} RGBInfo;

	typedef struct _RGBInfos   //每个vector表示一个板子里的内容（不是一张图片中的所有板子）
	{
		std::vector<RGBInfo> RGB_info;
	} RGBInfos;

	typedef struct _RGBOut   //最终给外部函数传出的信息
	{
		int rgb_number=-1;  //初始化-1，防止统计时把随机值统计进去
		bool rgb_is_available=false;

	} RGBOut;



	class RGBDetect
	{
	public:
		RGBDetect();
		~RGBDetect();
		void ComputeRGB(RGBInfos RGB_infos);
		void RGBSeg(cv::Mat image);
		void Three2Ten(std::vector<std::string> colorstr);
		void RGB2Number(std::vector<std::string> sortedColors);
		void JudgeAvailable(cv::Rect rect, std::vector<int> RGB_statistics, int img_width, int img_height);
		void RGBResize(cv::Mat& image);
		RGBOut getRGBOut();
	private:
		RGBInfos RGB_infos;
		RGBInfos RGB_original;
		RGBOut RGB_outs;
		int prevNum = -1;
	};




	
}


#endif