﻿
#include "tdYOLOP.h"


#if (defined WIN32 || defined _WIN32 )
    #pragma warning(disable:4996)
#endif




using namespace cv;
#ifdef TD_USING_RKNN
#else
    using namespace dnn;
#endif
using namespace std;


namespace td
{

#ifdef TD_USING_RKNN
	unsigned char* load_model_yolop(const char* filename, int* model_size)
	{
		FILE* fp = fopen(filename, "rb");
		if (fp == nullptr) {
			printf("fopen %s fail!\n", filename);
			return NULL;
		}
		fseek(fp, 0, SEEK_END);
		int model_len = ftell(fp);
		unsigned char* model = (unsigned char*)malloc(model_len);
		fseek(fp, 0, SEEK_SET);
		if (model_len != fread(model, 1, model_len, fp)) {
			printf("fread %s fail!\n", filename);
			free(model);
			return NULL;
		}
		*model_size = model_len;
		if (fp) {
			fclose(fp);
		}
		return model;
	}

	void printRKNNTensor(rknn_tensor_attr* attr)
	{
		printf("index=%d name=%s n_dims=%d dims=[%d %d %d %d] n_elems=%d size=%d fmt=%d type=%d qnt_type=%d fl=%d zp=%d scale=%f\n",
			attr->index, attr->name, attr->n_dims, attr->dims[3], attr->dims[2], attr->dims[1], attr->dims[0],
			attr->n_elems, attr->size, 0, attr->type, attr->qnt_type, attr->fl, attr->zp, attr->scale);
	}
#endif

	YOLOP::YOLOP()
	{}

	YOLOP::~YOLOP()
	{}

	int YOLOP::init(char* iniPath)
	{
		int ret = 0;

		mIsRunDA = true;
		mIsRunLL = false;

		const float anchors[3][6] = { { 3,9,5,11,4,20 },{ 7,18,6,39,12,31 },{ 19,50,38,81,68,157 } };
		const float stride[3] = { 8.0, 16.0, 32.0 };

		const bool keep_ratio = true;

		int netWidth;
		int netHeight;
		float boxThreshold;
		float classThreshold;
		float nmsThreshold;
		float mean[3];
		float std[3];

		char modelpath[200];
		char labelPath[200];
		char imgPath[200];

		iniStart(iniPath);

		iniGetInt("net", "netWidth", &netWidth);
		iniGetInt("net", "netHeight", &netHeight);
		iniGetFloat("net", "boxThreshold", &boxThreshold);
		iniGetFloat("net", "classThreshold", &classThreshold);
		iniGetFloat("net", "nmsThreshold", &nmsThreshold);
		iniGetFloat("net", "mean0", &mean[0]);
		iniGetFloat("net", "mean1", &mean[1]);
		iniGetFloat("net", "mean2", &mean[2]);
		iniGetFloat("net", "std0", &std[0]);
		iniGetFloat("net", "std1", &std[1]);
		iniGetFloat("net", "std2", &std[2]);

		iniGetString("path", "modelPath", modelpath);
		iniGetString("path", "labelPath", labelPath);
		iniGetString("path", "imgPath", imgPath);

#if (defined WIN32 || defined _WIN32 )
#else
		modelpath[strlen(modelpath) - 1] = '\0';
		labelPath[strlen(labelPath) - 1] = '\0';
		imgPath[strlen(imgPath) - 1] = '\0';
#endif


		this->inpWidth = netWidth;
		this->inpHeight = netHeight;
		this->classThreshold = classThreshold;
		this->nmsThreshold = nmsThreshold;
		this->boxThreshold = boxThreshold;

		this->keep_ratio = keep_ratio;

		strcpy(this->imgPath, imgPath);

		this->pdata_da_mask = (float*)malloc(inpWidth * inpHeight * 2 * sizeof(float));
		this->pdata_ll_mask = (float*)malloc(inpWidth * inpHeight * 2 * sizeof(float));

		for (int i = 0; i < 3; i++)
		{
			this->mean[i] = mean[i];
			this->std[i] = std[i];
		}

		for (int i = 0; i < 3; i++)
			for (int j = 0; j < 6; j++)
				this->anchors[i][j] = anchors[i][j];

		for (int i = 0; i < 3; i++)
			this->stride[i] = stride[i];



		ifstream ifs(labelPath);
		string line;
		while (getline(ifs, line))
			this->classes.push_back(line);

#if (defined WIN32 || defined _WIN32 )
#else
		for (int i = 0; i < classes.size() - 1; i++)
			classes[i].erase(classes[i].end() - 1);
		for (int i = 0; i < classes.size(); i++)
			printf("%s\n", classes[i].c_str());
#endif

#ifdef TD_USING_RKNN
		int model_len = 0;
		unsigned char* model;

		// Load RKNN Model
		printf("Loading model ...\n");
		model = load_model_yolop(modelpath, &model_len);
		ret = rknn_init(&ctx, model, model_len, 0);
		if (ret < 0)
		{
			printf("rknn_init fail! ret=%d\n", ret);
			return -1;
		}

		// Get Model Input Output Info
		ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
		if (ret != RKNN_SUCC)
		{
			printf("rknn_query fail! ret=%d\n", ret);
			return -1;
		}
		printf("model input num: %d, output num: %d\n", io_num.n_input, io_num.n_output);

		printf("input tensors:\n");
		rknn_tensor_attr input_attrs[io_num.n_input];
		memset(input_attrs, 0, sizeof(input_attrs));
		for (int i = 0; i < io_num.n_input; i++)
		{
			input_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
			if (ret != RKNN_SUCC)
			{
				printf("rknn_query fail! ret=%d\n", ret);
				return -1;
			}
			printRKNNTensor(&(input_attrs[i]));
		}

		printf("output tensors:\n");
		rknn_tensor_attr output_attrs[io_num.n_output];
		memset(output_attrs, 0, sizeof(output_attrs));
		for (int i = 0; i < io_num.n_output; i++)
		{
			output_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
			if (ret != RKNN_SUCC)
			{
				printf("rknn_query fail! ret=%d\n", ret);
				return -1;
			}
			printRKNNTensor(&(output_attrs[i]));
		}
#else
		this->net = readNet(modelpath);
#endif

		return 0;
	}

	int YOLOP::getInpWidth() { return this->inpWidth; };
	int YOLOP::getInpHeight() { return this->inpHeight; };
	bool YOLOP::isRunDA() { return this->mIsRunDA; };
	bool YOLOP::isRunLL() { return this->mIsRunLL; };
	float YOLOP::getClassThreshold() { return this->classThreshold; }

	void YOLOP::detect(Mat& srcimg, std::vector<DetObject>& det_object)
	{
		int ret;

		float class_threshold = this->classThreshold;
#ifdef TD_USING_TRACK
		class_threshold *= 0.5;
#endif

		int newh = 0, neww = 0, padh = 0, padw = 0;

		Mat dstimg = letterbox(srcimg, this->inpHeight, this->inpWidth, this->keep_ratio, &newh, &neww, &padh, &padw);

		//imwrite("letter.jpg", dstimg);

		//cvtColor(dstimg, dstimg, COLOR_BGR2RGB);  //  #include <opencv2/opencv.hpp>

		float ratioh = (float)newh / srcimg.rows;
		float ratiow = (float)neww / srcimg.cols;
		int i = 0, j = 0;

		const int copy_len = inpWidth * inpHeight * 2 * sizeof(float);
		float* pdata = NULL;

#ifdef TD_USING_RKNN    
		cvtColor(dstimg, dstimg, CV_BGR2RGB);  //COLOR_BGR2RGB

		// Set Input Data
		//printf("dstimg w = %d, h = %d, channel = %d\n", dstimg.cols, dstimg.rows, dstimg.channels());
		rknn_input inputs[1];
		memset(inputs, 0, sizeof(inputs));
		inputs[0].index = 0;
		inputs[0].type = RKNN_TENSOR_UINT8;
		inputs[0].size = dstimg.cols * dstimg.rows * dstimg.channels();
		inputs[0].fmt = RKNN_TENSOR_NHWC;//NCHW  NHWC
		inputs[0].buf = dstimg.data;
		//inputs[0].pass_through = 0;

		ret = rknn_inputs_set(ctx, io_num.n_input, inputs);
		if (ret < 0)
		{
			printf("rknn_input_set fail! ret=%d\n", ret);
			//return -1;
		}

		// Run
		//printf("rknn_run\n");
		//double inference_BeginTime = (double)cv::getTickCount();
		ret = rknn_run(ctx, nullptr);
		//double inference_time2 = ((double)cv::getTickCount() - inference_BeginTime) / cv::getTickFrequency();
		//int inference_time3 = (int)(inference_time2 * 1000 + 0.5);
		//printf("$$$ inference time %dms\n", inference_time3);
		if (ret < 0)
		{
			printf("rknn_run fail! ret=%d\n", ret);
			//return -1;
		}

		/*******************  get result  ***************************************************************************************/

		rknn_output outs[io_num.n_output];
		memset(outs, 0, sizeof(outs));
		for (int i = 0; i < io_num.n_output; i++)
			outs[i].want_float = 1;

		ret = rknn_outputs_get(ctx, io_num.n_output, outs, NULL);
		if (ret < 0)
		{
			printf("rknn_outputs_get fail! ret=%d\n", ret);
			//return -1;
		}

		pdata = (float*)outs[0].buf;
		if (mIsRunDA)
			memcpy(this->pdata_da_mask, (float*)outs[1].buf, copy_len);  ///drive area segment
		if (mIsRunLL)
			memcpy(this->pdata_ll_mask, (float*)outs[2].buf, copy_len);  ///lane line segment
#else
		normalize(dstimg, this->mean, this->std);
		Mat blob = blobFromImage(dstimg);
		this->net.setInput(blob);
		vector<Mat> outs;

		//////////  debug  ////////////////////////////////////////////////////////////////////////////////////////////////
		/*
		std::vector<String> aa;
		String bb("1040");  //节点名称在netron中查看
		aa.push_back(bb);
		this->net.forward(outs, aa);

		FileStorage fs("debug.xml", FileStorage::WRITE);
		fs << "vocabulary" << outs[0];
		fs.release();
		*/
		//////////////////////////////////////////////////////////////////////////////////////////////////////////////////

		this->net.forward(outs, this->net.getUnconnectedOutLayersNames());

		pdata = (float*)outs[0].data;
		//for (int t = 0; t < 10; t++)
			//printf("%f\n", pdata[t]);

		if (mIsRunDA == true && mIsRunLL == false)
		{
			memcpy(this->pdata_da_mask, (float*)outs[1].data, copy_len);  ///drive area segment
		}
		else if (mIsRunDA == false && mIsRunLL == true)
		{
			memcpy(this->pdata_ll_mask, (float*)outs[1].data, copy_len);  ///lane line segment
		}
		else if (mIsRunDA == true && mIsRunLL == true)
		{
			memcpy(this->pdata_da_mask, (float*)outs[1].data, copy_len);  ///drive area segment
			memcpy(this->pdata_ll_mask, (float*)outs[2].data, copy_len);  ///lane line segment
		}
		else if (mIsRunDA == false && mIsRunLL == false)
		{
		}
#endif

		// get mask
		//this->getMask(this->pdata_da_mask, this->pdata_ll_mask);

		/////generate proposals
		vector<int> classIds;
		vector<float> confidences;
		vector<Rect> boxes;
		ratioh = (float)srcimg.rows / newh;
		ratiow = (float)srcimg.cols / neww;
		int n = 0, q = 0, nout = this->classes.size() + 5, row_ind = 0;

		for (n = 0; n < 3; n++)   ///�߶�
		{
			int num_grid_x = (int)(this->inpWidth / this->stride[n]);
			int num_grid_y = (int)(this->inpHeight / this->stride[n]);
			for (q = 0; q < 3; q++)    ///anchor��
			{
				const float anchor_w = this->anchors[n][q * 2];
				const float anchor_h = this->anchors[n][q * 2 + 1];
				for (i = 0; i < num_grid_y; i++)
				{
					for (j = 0; j < num_grid_x; j++)
					{
						const float box_score = pdata[4];
						if (box_score > this->boxThreshold)
						{
							float max_class_socre = -999.0f;
							int max_class_index = 0;
							for (int s_cnt = 5; s_cnt < nout; s_cnt++)
							{
								float score = pdata[s_cnt];
								if (score > max_class_socre)
								{
									max_class_socre = score;
									max_class_index = s_cnt - 5;
								}
							}
							if (max_class_socre > class_threshold)
							{
								float cx = (pdata[0] * 2.f - 0.5f + j) * this->stride[n];  ///cx
								float cy = (pdata[1] * 2.f - 0.5f + i) * this->stride[n];   ///cy
								float w = powf(pdata[2] * 2.f, 2.f) * anchor_w;   ///w
								float h = powf(pdata[3] * 2.f, 2.f) * anchor_h;  ///h

								int left = (cx - 0.5 * w - padw) * ratiow;
								int top = (cy - 0.5 * h - padh) * ratioh;

								//printf("max_class_socre = %f, box_score = %f\n", max_class_socre, box_score);
								classIds.push_back(max_class_index);
								confidences.push_back(max_class_socre * box_score);
								boxes.push_back(Rect(left, top, (int)(w * ratiow), (int)(h * ratioh)));
							}
						}
						row_ind++;
						pdata += nout;
					}
				}
			}
		}

		// Perform non maximum suppression to eliminate redundant overlapping boxes with
		// lower confidences
		vector<int> indices;
		float confidencesThreshold = class_threshold;
		nms_boxes(boxes, confidences, confidencesThreshold, this->nmsThreshold, indices);
		//NMSBoxes(boxes, confidences, confidencesThreshold, this->nmsThreshold, indices);

		for (size_t i = 0; i < indices.size(); ++i)
		{
			int idx = indices[i];
			Rect box = boxes[idx];

			DetObject obj;
			obj.rect = box;
			obj.score = confidences[idx];
			sprintf(obj.label, this->classes[classIds[idx]].c_str());
			obj.isShow = true;
			det_object.push_back(obj);
			printf("object label: %s, width = %d, height = %d, score = %f\n", obj.label, obj.rect.width, obj.rect.height, obj.score);
		}
	}

	void YOLOP::getResult(unsigned char* maskBufDA, unsigned char* maskBufLL)
	{
		maskCodec mskCodec;
		mskCodec.setEncodeImgSize(this->inpWidth, this->inpHeight);
		if (mIsRunDA)
		{
			mskCodec.encode(this->pdata_da_mask, maskBufDA, TD_MASK_BUF_SIZE);
			//mskCodec.readDATToBuf("DA999.dat", maskBufDA, TD_MASK_BUF_SIZE);
			//mskCodec.saveBufToDAT("DA999.dat", maskBufDA, TD_MASK_BUF_SIZE);
		}
		if (mIsRunLL)
		{
			mskCodec.encode(this->pdata_ll_mask, maskBufLL, TD_MASK_BUF_SIZE);
		}
	}

	void YOLOP::getMask(float* pdata_da_mask, float* pdata_ll_mask)
	{
		///////////////////////////////////////////////////////////////
		/*
		Mat img_show(Size(640, 640), CV_8U);
		int idx = 0;
		for (int row = 0; row < 640; row++)
		{
			for (int col = 0; col < 640; col++)
			{
				img_show.at<uchar>(row, col) = 255 * pdata_da_mask[idx++];
			}
		}
		imshow("img_show", img_show);

		Mat img_show2(Size(640, 640), CV_8U);
		idx = 640 * 640;
		for (int row = 0; row < 640; row++)
		{
			for (int col = 0; col < 640; col++)
			{
				img_show2.at<uchar>(row, col) = 255 * pdata_da_mask[idx++];
			}
		}
		imshow("img_show2", img_show2);

		waitKey(0);
		*/
		//////////////////////////////////////////////////////////////

		Mat da_seg_mask = Mat::zeros(this->inpHeight, this->inpWidth, CV_8UC1);
		Mat ll_seg_mask = Mat::zeros(this->inpHeight, this->inpWidth, CV_8UC1);


		int i, j;
		int area = this->inpHeight * this->inpWidth;
		if (mIsRunDA || mIsRunLL)
		{
			if (da_seg_mask.isContinuous() && ll_seg_mask.isContinuous())
			{
				uchar* da_data = NULL;
				uchar* ll_data = NULL;
				for (i = 0; i < this->inpHeight; i++)
				{
					int y = i;
					int index1 = y * this->inpWidth;

					if (mIsRunDA)
						da_data = da_seg_mask.ptr<uchar>(i);
					if (mIsRunLL)
						ll_data = ll_seg_mask.ptr<uchar>(i);

					int j = 0;
					while (j++ < this->inpWidth)
					{
						int x = j;
						int index2 = index1 + x;
						int index2_area = area + index2;

						if (mIsRunDA)
							*da_data++ = pdata_da_mask[index2] < pdata_da_mask[index2_area] ? 255 : 0;
						if (mIsRunLL)
							*ll_data++ = pdata_ll_mask[index2] < pdata_ll_mask[index2_area] ? 255 : 0;
					}
				}
			}
			else
			{
				for (i = 0; i < this->inpHeight; i++)
				{
					for (j = 0; j < this->inpWidth; j++)
					{
						const int x = j;
						const int y = i;
						if (mIsRunDA)
						{
							if (pdata_da_mask[y * this->inpWidth + x] < pdata_da_mask[area + y * this->inpWidth + x])
								da_seg_mask.at<uchar>(i, j) = 255;
						}
						if (mIsRunLL)
						{
							if (pdata_ll_mask[y * this->inpWidth + x] < pdata_ll_mask[area + y * this->inpWidth + x])
								ll_seg_mask.at<uchar>(i, j) = 255;
						}
					}
				}
			}
		}
	}

	char* YOLOP::getImgPath() { return this->imgPath; }

	char* YOLOP::getModelVersion() { return this->modelVersion; };
}