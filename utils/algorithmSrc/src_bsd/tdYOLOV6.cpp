﻿
#include "tdYOLOV6.h"

#ifdef TD_G3_APP_IN_OUT
#include "G3_Configuration.h"
#endif

#ifdef TD_USING_RGA
#include "XuRGAUtils.h"
#endif

#if (defined WIN32 || defined _WIN32 )
#else
  #ifndef STB_IMAGE_IMPLEMENTATION
  #define STB_IMAGE_IMPLEMENTATION
   #include "stb/stb_image.h"
  #endif

  //#ifndef STB_IMAGE_RESIZE_IMPLEMENTATION
  //#define STB_IMAGE_RESIZE_IMPLEMENTATION
  //  #include "stb/stb_image_resize.h"
  //#endif

  #ifndef STB_IMAGE_WRITE_IMPLEMENTATION
  #define STB_IMAGE_WRITE_IMPLEMENTATION
    #include "stb/stb_image_write.h"
  #endif
#endif


#if (defined WIN32 || defined _WIN32 )
#pragma warning(disable:4996)
#endif

#ifdef TD_USING_RGA
	extern XuRGAUtils rga_utils;
#endif


#define PERF_WITH_POST 1
#define COCO_IMG_NUMBER 5000
#define INDENT "    "

namespace td
{

    /*-------------------------------------------
                  Functions
-------------------------------------------*/

/*
    static int saveFloat(const char* file_name, float* output, int element_size)
    {
        FILE* fp;
        fp = fopen(file_name, "w");
        for (int i = 0; i < element_size; i++)
        {
            fprintf(fp, "%.6f\n", output[i]);
        }
        fclose(fp);
        return 0;
    }
*/

#if (defined WIN32 || defined _WIN32 )
#else
    static unsigned char* load_image(const char* image_path, int* org_height, int* org_width, int* org_ch, rknn_tensor_attr* input_attr)
    {
        int req_height = 0;
        int req_width = 0;
        int req_channel = 0;

        switch (input_attr->fmt)
        {
        case RKNN_TENSOR_NHWC:
            req_height = input_attr->dims[2];
            req_width = input_attr->dims[1];
            req_channel = input_attr->dims[0];
            break;
        case RKNN_TENSOR_NCHW:
            //Need to double check dims!!!!!
            req_height = input_attr->dims[1];
            req_width = input_attr->dims[0];
            req_channel = input_attr->dims[2];
            break;
        default:
            printf("meet unsupported layout\n");
            return NULL;
        }


        int height = 0;
        int width = 0;
        int channel = 0;

        
        unsigned char* image_data = stbi_load(image_path, &width, &height, &channel, req_channel);


        //cv::Mat srcimg = cv::imread(image_path);
        //unsigned char* image_data = (unsigned char*)malloc(1280 * 720 * 3);
        //image_data = srcimg.data;
        //width = srcimg.cols;
        //height = srcimg.rows;
        //channel = 3;


        if (image_data == NULL)
        {
            printf("load image-%s failed!\n", image_path);
            return NULL;
        }

        if (channel == 1) 
        {
            printf("image is grey, convert to RGB");
            void* rgb_data = malloc(width * height * 3);
            for (int i = 0; i < height; i++) 
            {
                for (int j = 0; j < width; j++) 
                {
                    int offset = (i * width + j) * 3;
                    ((unsigned char*)rgb_data)[offset] = ((unsigned char*)image_data)[offset];
                    ((unsigned char*)rgb_data)[offset + 1] = ((unsigned char*)image_data)[offset];
                    ((unsigned char*)rgb_data)[offset + 2] = ((unsigned char*)image_data)[offset];
                }
            }
            free(image_data);
            image_data = (unsigned char*)rgb_data;
            channel = 3;
        }

        //     int align_width = 4;

        // #ifdef PLATFORM_RK3588
        //     align_width = 16;    
        // #endif

        //     if (width % align_width != 0){
        //         int new_width = width+ (align_width - width % align_width);
        //         printf("%d is not pixel align at %d, which RGA REQUIRE. Using stb resize to %d, this will make the result shift slightly\n",width, align_width, new_width);
        //         void* resize_data = malloc(new_width* height* channel);
        //         stbir_resize_uint8(image_data, width, height, 0, (unsigned char*)resize_data, new_width, height, 0, channel);
        //         free(image_data);
        //         image_data = (unsigned char*)resize_data;
        //         *org_width = new_width;
        //     }
        //     else{
        //         *org_width = width;
        //     }

        * org_width = width;
        *org_height = height;
        *org_ch = channel;

        return image_data;
    }

    int load_hyperm_param(YOLO_INFO* m, MODEL_TYPE m_type, POST_PROCESS_TYPE post_type, char* img_path)
    {  
        printf("MODEL HYPERPARAM:\n");
        int ret = 0;
     
        m->in_path = (char*)img_path;

        m->m_type = m_type;

        const char* anchor_path;
        switch (m->m_type)
        {
        case M_YOLOV5:
            m->anchor_per_branch = 3;
            anchor_path = "./model/anchors_yolov5.txt";
            break;
        case M_YOLOV7:
            m->anchor_per_branch = 3;
            anchor_path = "./model/anchors_yolov7.txt";
            break;
        case M_YOLOX:
            m->anchor_per_branch = 1;
            /*
                RK_FORMAT_RGB_888 if normal api
                RK_FORMAT_BGR_888 if pass_through/ zero_copy
            */
            // m->color_expect = RK_FORMAT_RGB_888;
            break;
        default:
            m->anchor_per_branch = 1;
            break;
        }

        if ((m->m_type == M_YOLOV5) || (m->m_type == M_YOLOV7)) 
        {
            int n = 2 * 3 * m->anchor_per_branch;
            printf("%sAnchors: ", INDENT);
            float result[n];
            int valid_number;
            ret = readFloats(anchor_path, &result[0], n, &valid_number);
            for (int i = 0; i < valid_number; i++) 
            {
                m->anchors[i] = (int)result[i];
                printf("%d ", m->anchors[i]);
            }
            printf("\n");
        }
        else 
        {
            printf("%sAnchor free\n", INDENT);
        }

        m->post_type = post_type;

        m->in_source = SINGLE_IMG;

        return 0;
    }

    void query_dfl_len(MODEL_INFO* m, YOLO_INFO* y_info) 
    {
        // set dfl_len
        if ( (y_info->m_type == M_YOLOV8) || (y_info->m_type == M_PPYOLOE_PLUS) || (y_info->m_type == M_YOLOV6) )
        {
            if (m->n_output > 6) 
                y_info->score_sum_available = true;
            else 
                y_info->score_sum_available = false;

            if ((y_info->m_type == M_YOLOV8) || (y_info->m_type == M_PPYOLOE_PLUS)) 
                y_info->dfl_len = (int)(m->out_attr[0].dims[2] / 4);
            
            if (y_info->m_type == M_YOLOV6) 
            {
                // dump_tensor_attr(&m->out_attr[0]);
                if (m->out_attr[0].dims[2] != 4) 
                    y_info->dfl_len = (int)(m->out_attr[0].dims[2] / 4);
            }
        }
    }
#endif

	YOLOV6::YOLOV6()
	{}

	YOLOV6::~YOLOV6()
	{
#if (defined WIN32 || defined _WIN32 )
#else
         rkdemo_release(&m_info);
#endif
    }

#ifdef TD_G3_APP_IN_OUT
    int YOLOV6::init(const char* iniPath, const float algClassThreshold)
#else
	int YOLOV6::init(char* iniPath)
#endif
	{
            sprintf(this->modelVersion, "%s", "0.0.0");


#if (defined WIN32 || defined _WIN32 )
        printf("this function can't be used in Windows System !!\n");
        return -1;
#else
	    int ret = 0;
            
        MODEL_TYPE m_type = M_YOLOV6;
	    POST_PROCESS_TYPE post_type = Q8;
	    //char* model_path = "last_ckpt_RV1109_1126_u8_precompile.rknn";
	    //char* img_path = this->getImgPath(); //"1280.jpg";

        const bool keep_ratio = true;

	    int netWidth;
	    int netHeight;
	    int objNum;

#ifdef TD_G3_APP_IN_OUT
#else
	    float objThreshold;
#endif  

	    float nmsThreshold;
	    float mean[3];
	    float std[3];

	    char modelpath[200];
	    char labelPath[200];
	    char imgPath[200];

	    iniStart(iniPath);

	    iniGetInt("net", "netWidth", &netWidth);
	    iniGetInt("net", "netHeight", &netHeight);
	    iniGetInt("net", "objNum", &objNum);

#ifdef TD_G3_APP_IN_OUT
#else
	    iniGetFloat("net", "objThreshold", &objThreshold);
#endif

	    iniGetFloat("net", "nmsThreshold", &nmsThreshold);
	    iniGetFloat("net", "mean0", &mean[0]);
	    iniGetFloat("net", "mean1", &mean[1]);
	    iniGetFloat("net", "mean2", &mean[2]);
	    iniGetFloat("net", "std0", &std[0]);
	    iniGetFloat("net", "std1", &std[1]);
	    iniGetFloat("net", "std2", &std[2]);

	    iniGetString("path", "modelPath", modelpath);
            iniGetString("path", "labelPath", labelPath);
	    iniGetString("path", "imgPath", imgPath);


	    this->model_width = netWidth;
	    this->model_height = netHeight;
	    this->model_channel = 3;

	    this->objNum = objNum;
	    this->prop_box_size = 5 + objNum;
		
	    this->nmsThreshold = nmsThreshold;

#ifdef TD_G3_APP_IN_OUT
	    /* 这个不由ini读取了，改成由MRV220的配置表读取 */
	    this->objThreshold = algClassThreshold;
#else
	    this->objThreshold = objThreshold;
#endif
	    
	    this->keep_ratio = keep_ratio;

#if (defined WIN32 || defined _WIN32 )
#else
            /* 去掉可能的出现的/r */
            if(modelpath[strlen(modelpath) - 1] == 0x0D)
                modelpath[strlen(modelpath) - 1] = '\0';
        
            if(labelPath[strlen(labelPath) - 1] == 0x0D)
                labelPath[strlen(labelPath) - 1] = '\0';
        
            if(imgPath[strlen(imgPath) - 1] == 0x0D)
                imgPath[strlen(imgPath) - 1] = '\0';
#endif

	    labels = (char**)malloc(objNum * TD_OBJ_NAME_MAX_SIZE_YOLOV6 * sizeof(char));
	    ret = loadLabelName(labelPath, labels, objNum);

	    strcpy(this->imgPath, imgPath);

	    for (int i = 0; i < 3; i++)
	    {
		this->mean[i] = mean[i];
		this->std[i] = std[i];
	    }

        ret = load_hyperm_param(&y_info, m_type, post_type, imgPath);
        if (ret < 0) 
            return ret;

        m_info.m_path = modelpath;
        // m_info.verbose_log = true;



/////////////////////////////////////////////////////////////////////////////////////////////
        //rkdemo_init(&m_info);

        if (m_info.m_path == nullptr)
        {
            printf("ERROR model path is null");
            return -1;
        }

    
    	int model_data_size = 0;
	unsigned char* model_data;


	int len = strlen(modelpath);
	if (modelpath[len - 4] == 'r' && modelpath[len - 3] == 'k' && modelpath[len - 2] == 'n' && modelpath[len - 1] == 'n')
	{
            model_data = load_model(modelpath, &model_data_size);
	    printf("the model format is rknn\n");
	}
	else if (modelpath[len - 3] == 'v' && modelpath[len - 2] == 'i' && modelpath[len - 1] == 's')
	{
            char key1 = '0';
            char key2 = '0';
            char key3 = '0';
            int vis_model_data_size = 0;
            unsigned char* vis_model_data = load_model(modelpath, &vis_model_data_size);
	    model_data = decode(vis_model_data, vis_model_data_size, &model_data_size, &key1, &key2, &key3, false, NULL);

            sprintf(this->modelVersion, "%c.%c.%c", key1, key2, key3);   //get version
	}
	else
	{
	    printf("Unrecognized model format\n");
	    return -1;
	}

        ret = rknn_init(&m_info.ctx, model_data, model_data_size, 0);

        if (ret < 0)
	{
	    printf("rknn_init error ret=%d\n", ret);
	    return -1;
	}

    
        ret = rkdemo_query_model_info(&m_info);


/////////////////////////////////////////////////////////////////////////////////////////////


        rkdemo_init_input_buffer_all(&m_info, NORMAL_API, RKNN_TENSOR_UINT8);

        if (y_info.post_type == Q8) 
            rkdemo_init_output_buffer_all(&m_info, NORMAL_API, 0);
        else 
            rkdemo_init_output_buffer_all(&m_info, NORMAL_API, 1);
        
        query_dfl_len(&m_info, &y_info);

        switch (m_info.in_attr[0].fmt)
        {
        case RKNN_TENSOR_NHWC:
            letter_box.target_height = m_info.in_attr[0].dims[2];
            letter_box.target_width = m_info.in_attr[0].dims[1];
            break;
        case RKNN_TENSOR_NCHW:
            letter_box.target_height = m_info.in_attr[0].dims[1];
            letter_box.target_width = m_info.in_attr[0].dims[0];
            break;
        default:
            printf("meet unsupported layout\n");
            return ret;
        }

	    return ret;
#endif
	}



	void YOLOV6::detect(cv::Mat frame, std::vector<cv::Point> no_detection_area1, std::vector<cv::Point> no_detection_area2, std::vector<DetObject>& det_object)
	{
#if (defined WIN32 || defined _WIN32 )
        printf("this function can't be used in Windows System !!\n");
        return;
#else
        int ret = 0;

	    float nms_threshold = this->nmsThreshold;
	    float obj_threshold = this->objThreshold;
  #ifdef TD_USING_TRACK
	    obj_threshold *= 0.5;
  #endif

        int newh = 0, neww = 0, padh = 0, padw = 0;
	    cv::Mat dstimg = letterbox(frame, model_height, model_width, keep_ratio, &newh, &neww, &padh, &padw);

        cv::Scalar color(255, 255, 0);
	    if (no_detection_area1.size() > 0)
	        fillNoDetectionArea(dstimg, no_detection_area1, frame.cols, frame.rows, newh, neww, padh, padw, color);
	    if (no_detection_area2.size() > 0)
		    fillNoDetectionArea(dstimg, no_detection_area2, frame.cols, frame.rows, newh, neww, padh, padw, color);

        //cv::Mat dstimg = frame.clone();
        //cv::resize(frame, dstimg, cv::Size(letter_box.target_width, letter_box.target_height), cv::INTER_AREA);

#ifdef TD_USING_RGA    
        rga_utils.imageTransformation(dstimg.cols, dstimg.rows, rga_utils.IMG_TYPE_BGR888, dstimg.data, dstimg.cols, dstimg.rows, rga_utils.IMG_TYPE_RGB888, dstimg.data);
#else
        cv::cvtColor(dstimg, dstimg, cv::COLOR_BGR2RGB);  //#include <opencv2/opencv.hpp>
#endif

        //cv::imwrite("aaa.bmp", dstimg);
        m_info.inputs[0].buf = dstimg.data;

        double BeginTime_alg = (double)cv::getTickCount();
     
        rknn_inputs_set(m_info.ctx, m_info.n_input, m_info.inputs);  // input set    
        ret = rknn_run(m_info.ctx, NULL);  // rknn run
        ret = rknn_outputs_get(m_info.ctx, m_info.n_output, m_info.outputs, NULL);  // output get

        double time2_alg = ((double)cv::getTickCount() - BeginTime_alg) / cv::getTickFrequency();
	    int time3_alg = (int)(time2_alg * 1000 + 0.5);
	    printf("$$$$$$$$$$$$$$$$$ rknn time %dms\n", time3_alg);

        /* Post process */
        detect_result_group_t detect_result_group;

        for (int i = 0; i < m_info.n_output; i++) 
            output_buf_list[i] = m_info.outputs[i].buf;
            
        post_process(this->labels, obj_threshold, nms_threshold, output_buf_list, &m_info, &y_info, &detect_result_group);

        // Draw Objects
        for (int i = 0; i < detect_result_group.count; i++)
        {
		    detect_result_t* det_result = &(detect_result_group.results[i]);
		    //sprintf(text, "%s %.2f", det_result->name, det_result->prop);
		    //printf("%s @ (%d %d %d %d) %f\n", det_result->name, det_result->box.left, det_result->box.top, det_result->box.right, det_result->box.bottom, det_result->prop);
		    int x1 = det_result->box.left;
		    int y1 = det_result->box.top;
		    int x2 = det_result->box.right;
		    int y2 = det_result->box.bottom;

		    x1 = (x1 - padw) * (float)frame.cols / neww;
		    y1 = (y1 - padh) * (float)frame.rows / newh;
		    x2 = (x2 - padw) * (float)frame.cols / neww;
		    y2 = (y2 - padh) * (float)frame.rows / newh;

		    //draw box
		    //cv::Scalar blue(255, 0, 0);
		    //cv::Scalar red(0, 0, 255);
		    //rectangle(frame, Point(x1, y1), Point(x2, y2), blue, 2);
		    //putText(frame, text, Point(x1, y1 - 12), FONT_HERSHEY_SIMPLEX, 0.8, red, 1);

		    DetObject obj;
		    obj.rect = cv::Rect(x1, y1, x2 - x1, y2 - y1);
		    //obj.label = det_result->name;
		    sprintf(obj.label, det_result->name);
		    obj.score = det_result->prop;
		    //printf("########### obj.score = %f  \n", obj.score);
		    obj.isShow = true;
		    det_object.push_back(obj);
        }

        //cv::imwrite("out.bmp", frame);
        //cv::imwrite("out.bmp", dstimg);

        ret = rknn_outputs_release(m_info.ctx, m_info.n_output, m_info.outputs);

        return;
#endif
	}

	void YOLOV6::getResult()
	{}

    char* YOLOV6::getImgPath()
    {
        return this->imgPath;
    }

    float YOLOV6::getObjThreshold() { return this->objThreshold; }

    char* YOLOV6::getModelVersion() { return this->modelVersion; };
}
