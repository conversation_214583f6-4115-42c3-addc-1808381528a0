//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/14.
//

#ifndef VIS_G3_SOFTWARE_DETECTER_BSD_H
#define VIS_G3_SOFTWARE_DETECTER_BSD_H

#include "DetectDataCallback.h"
#include <Poco/Runnable.h>
#include "AlarmDecision_BSD.h"

class Detecter_BSD : public Poco::Runnable {

public:

    /**
     * 初始化
     *
     * @param cameraId : 算法对应的相机ID
     * @param detectUnitManager ：DetectUnitManager的对象
     *
     * @return 结果     0：成功  其他：失败
     *
     * */
    int init(int cameraId, DetectDataCallback &detectDataCallback);

    void run() override;


    bool isDetectOpen() const;

    __time_t getLastDetectTime() const;



private:
    std::vector<cv::Point> getIgnoreAreaPoint1() const;

    std::vector<cv::Point> getIgnoreAreaPoint2() const;

    DetectDataCallback *curDetectDataCallback;

    int curCameraId = -1;


    bool detectOpen = false;

    /* 上一帧图像解析成功的时间 */
    __time_t lastDetectTime = 0;

    AlarmDecision_BSD alarmDecisionBsd;

    /* 当前的车况 */
    Vehicle_RealtimeStatus curVehicleStatus = {};



};


#endif //VIS_G3_SOFTWARE_DETECTER_BSD_H
