﻿
#include "tdFunction.h"

#ifdef TD_USING_RGA
    #include "XuRGAUtils.h"
#endif


#if (defined WIN32 || defined _WIN32 )
#pragma warning(disable:4996)
#endif



using namespace cv;


#ifdef TD_USING_RGA
	extern XuRGAUtils rga_utils;
#endif

namespace td
{

#ifdef TD_G3_DEMO_IN_OUT

	/************************************************************************/
	/* 按照opencv数据存储格式，函数传参顺序为b、g、r                        */
	/************************************************************************/
	void rgb2yuv_pixel(uchar b, uchar g, uchar r, uchar& y, uchar& u, uchar& v)
	{
		//rgb转yuv公式，参考资料<a target=_blank href="http://www.cnblogs.com/dwdxdy/p/3713990.html">http://www.cnblogs.com/dwdxdy/p/3713990.html</a>
		//y = 0.299 * r + 0.587 * g + 0.114 * b;
		//u = -0.1687 * r - 0.3313 * g + 0.5 * b + 128;
		//v = 0.5 * r - 0.4187 * g - 0.0813 * b + 128;

		//y =   0.257*r + 0.504*g + 0.098*b + 16;
		//u = -0.148*r - 0.291*g + 0.439*b + 128;
		//v  =  0.439*r - 0.368*g - 0.071*b + 128;

		y = 0.257 * r + 0.504 * g + 0.098 * b + 16;
		u = -0.148 * r - 0.291 * g + 0.439 * b + 128;
		v = 0.439 * r - 0.368 * g - 0.071 * b + 128;
	}

	/************************************************************************/
	/* rgb24转yuv422                                                        */
	/************************************************************************/
	void rgb2yuv422_image(uchar* pRgb, uchar* pYuv, int width, int height)
	{
		//考虑到每两个rgb像素对应一个yuyv组合，因此，width应为2的倍数
		int width1 = width = width / 2 * 2;

		for (int h = 0; h < height; h++)
		{
			uchar* ptr1 = pRgb + h * width * 3;
			uchar* ptr2 = pYuv + h * width * 2;

			for (int w = 0; w < width1; w += 2)
			{
				uchar y1, u1, v1, y2, u2, v2;
				rgb2yuv_pixel(*ptr1++, *ptr1++, *ptr1++, y1, u1, v1);
				rgb2yuv_pixel(*ptr1++, *ptr1++, *ptr1++, y2, u2, v2);
				//u、v分量取平均值
				uchar u = (u1 + u2) >> 1;  //(u1 + u2) / 2
				uchar v = (v1 + v2) >> 1;  //(v1 + v2) / 2
				*ptr2++ = u;  //y1
				*ptr2++ = y1;  //u
				*ptr2++ = v;  //y2
				*ptr2++ = y2;  //v
			}
		}
	}
#endif

	float get_iou_value(Rect rect1, Rect rect2)
	{
		int xx1, yy1, xx2, yy2;

		xx1 = max(rect1.x, rect2.x);
		yy1 = max(rect1.y, rect2.y);
		xx2 = min(rect1.x + rect1.width - 1, rect2.x + rect2.width - 1);
		yy2 = min(rect1.y + rect1.height - 1, rect2.y + rect2.height - 1);

		int insection_width, insection_height;
		insection_width = max(0, xx2 - xx1 + 1);
		insection_height = max(0, yy2 - yy1 + 1);

		float insection_area, union_area, iou;
		insection_area = float(insection_width) * insection_height;
		union_area = float(rect1.width * rect1.height + rect2.width * rect2.height - insection_area);
		iou = insection_area / union_area;
		return iou;
	}

	int sum(int a, int b)  //白盒测试的测试程序
	{
		if (a == 0)
			return b;
		else if (b == 0)
			return a;
		else
			return (a + b);
	}

	bool cmp(BBOX& b1, BBOX& b2) {
		return b1.confidence > b2.confidence;
	}

	void nms_boxes(std::vector<cv::Rect>& boxes, std::vector<float>& confidences, float confidencesThreshold, float nmsThreshold, std::vector<int>& indices)
	{
		//非极大值抑制
		//input:  boxes: 原始检测框集合;
		//input:  confidences：原始检测框对应的置信度值集合
		//input:  confThreshold 和 nmsThreshold 分别是 检测框置信度阈值以及做nms时的阈值
		//output:  indices  经过上面两个阈值过滤后剩下的检测框的index

		BBOX bbox;
		std::vector<BBOX> bboxes;
        std::vector<bool> need_delete(boxes.size(), false);
		int i, j;
		for (i = 0; i < boxes.size(); i++)
		{
			bbox.box = boxes[i];
			bbox.confidence = confidences[i];
			bbox.index = i;
			bboxes.push_back(bbox);
		}
		sort(bboxes.begin(), bboxes.end(), cmp);

		for (i = 0; i < bboxes.size(); i++)
		{
			if (need_delete[i])
				continue;
			if (bboxes[i].confidence < confidencesThreshold)
				break;
			indices.push_back(bboxes[i].index);
			for (j = i + 1; j < bboxes.size(); j++)
			{
				if (need_delete[j])
					continue;
				float iou = get_iou_value(bboxes[i].box, bboxes[j].box);
				if (iou > nmsThreshold)
				{
					need_delete[j] = true;

				}
			}
		}
	}

	cv::Mat letterbox(cv::Mat srcimg, int inpHeight, int inpWidth, bool keep_ratio, int* newh, int* neww, int* top, int* left)
	{
		//图像缩放及添加pad至目标尺寸
		//input: srcimg 原始图像
		//input: inpHeight及inpWidth 设定的输入神经网络的尺寸
		//input: keep_ratio 若保持原始图像的长宽比，则在缩放后添加pad进行填充
		//output：newh及neww 原始图像等比缩放后的宽高，不算pad
		//output：top及left 原始图像等比缩放后，pad之后的偏移位置
		//return: 缩放及pad后的图像

		int srch = srcimg.rows, srcw = srcimg.cols;
		*newh = inpHeight;
		*neww = inpWidth;
		Mat dstimg;
		if (keep_ratio && srch != srcw)
		{
			float hw_scale = (float)srch / srcw;
			if (hw_scale > 1)
			{
				*newh = inpHeight;
				*neww = int(inpWidth / hw_scale);
#ifdef TD_USING_RGA
				dstimg.create(*newh, *neww, CV_8UC3);
				rga_utils.imageTransformation(srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data, *neww, *newh, rga_utils.IMG_TYPE_BGR888, dstimg.data);
#else
				resize(srcimg, dstimg, Size(*neww, *newh), INTER_AREA);
#endif
				* left = int((inpWidth - *neww) * 0.5);
				copyMakeBorder(dstimg, dstimg, 0, 0, *left, inpWidth - *neww - *left, BORDER_CONSTANT, 0);
			}
			else
			{
				*newh = MIN((int)inpWidth * hw_scale, inpHeight);  // MIN( (int)inpWidth * hw_scale, inpHeight)   (int)inpWidth * hw_scale
				*neww = inpWidth;
#ifdef TD_USING_RGA
				dstimg.create(*newh, *neww, CV_8UC3);
				rga_utils.imageTransformation(srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data, *neww, *newh, rga_utils.IMG_TYPE_BGR888, dstimg.data);
#else
				resize(srcimg, dstimg, Size(*neww, *newh), INTER_AREA);
#endif
				* top = (int)(inpHeight - *newh) * 0.5;
				copyMakeBorder(dstimg, dstimg, *top, inpHeight - *newh - *top, 0, 0, BORDER_CONSTANT, 0);
			}
		}
		else
		{
#ifdef TD_USING_RGA
			dstimg.create(*newh, *neww, CV_8UC3);
			rga_utils.imageTransformation(srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data, *neww, *newh, rga_utils.IMG_TYPE_BGR888, dstimg.data);
#else
			resize(srcimg, dstimg, Size(*neww, *newh), INTER_AREA);
#endif
		}

		//imwrite("out.jpg", dstimg);

		return dstimg;
	}

	void normalize(cv::Mat& img, float mean[3], float std[3])
	{
		img.convertTo(img, CV_32F);
		int i = 0, j = 0;
		const float scale = 1.0 / 255.0;
		for (i = 0; i < img.rows; i++)
		{
			float* pdata = (float*)(img.data + i * img.step);
			for (j = 0; j < img.cols; j++)
			{
				//printf("origin %f %f %f  ", pdata[0], pdata[1], pdata[2]);

				pdata[0] = (pdata[0] * scale - mean[0]) / std[0];
				pdata[1] = (pdata[1] * scale - mean[1]) / std[1];
				pdata[2] = (pdata[2] * scale - mean[2]) / std[2];

				//printf("%f %f %f\n", pdata[0], pdata[1], pdata[2]);

				pdata += 3;
			}
		}
	}

	void LinearRegression(int* Array, int Array_size, float& k, float& b)
	{
		//最小二乘回归，输入：Array, Array_size, 输出：k，b
		//x表示0,1,2....Array_size，y表示Array数组内的数据

		int sum_x = 0, sum_y = 0, sum_xy = 0, sum_xx = 0;
		for (int i = 0; i < Array_size; i++)
		{
			int x = i;
			int y = Array[i];
			sum_x += x;
			sum_y += y;
			sum_xy += x * y;
			sum_xx += x * x;
		}

		k = (float)(Array_size * sum_xy - sum_x * sum_y) / (Array_size * sum_xx - sum_x * sum_x);
		b = (float)(sum_xx * sum_y - sum_x * sum_xy) / (Array_size * sum_xx - sum_x * sum_x);
	}

	bool CalcObjIsStatic(DetObject det_object, int confirm_frame_num)
	{
		//根据每帧之间框的iou判断目标是否静止状态

		bool isStatic = true;
		const float iou_th = 0.9;

		for (int i = 1; i < confirm_frame_num; i++)
		{
			cv::Rect rect1 = det_object.rectSeq[i - 1];
			cv::Rect rect2 = det_object.rectSeq[i];
			float iou_val = get_iou_value(rect1, rect2);
			if (iou_val < iou_th)
			{
				isStatic = false;
				break;
			}
		}
		return isStatic;
	}

	void CalcCamCoverd(cv::Mat srcimg, float& imgBrightness, bool& isCamCoverd, bool& isFullBlack)
	{
		//G3给过来算法的图片是原始图片，叠加前，没有时间水印的！！！
		
		//图像亮度imgBrightness是G4-GJ项目用到，用于根据环境亮度来控制灯的开关
		//isCamCoverd用于判断相机是否有被遮挡
		//isFullBlack用于判断图像是否全黑（用于相机被拔掉或相机通路出问题时的故障上报）

	
		//cvNamedWindow("pResizeGrayImg");
		//cvShowImage("pResizeGrayImg", mPpImgs->pResizeGrayImg);
		//cvWaitKey(0);

		//srcimg = imread("C://Users//lyc_s//Desktop//coverImg.bmp");

		float scale = 0.278;
		Mat img_resize_rgb, img_resize_gray, img_canny;
		cv::resize(srcimg, img_resize_rgb, Size(srcimg.cols * scale, srcimg.rows * scale), INTER_NEAREST);

		//imshow("img_resize_rgb", img_resize_rgb);

		float del_percent = 0.05;
		img_resize_rgb = img_resize_rgb(Rect(0, img_resize_rgb.rows * del_percent, img_resize_rgb.cols, img_resize_rgb.rows * (1 - del_percent) - 1));

		cv::cvtColor(img_resize_rgb, img_resize_gray, COLOR_BGR2GRAY);


		//imshow("img_resize_gray", img_resize_gray);
		//waitKey(0);

		blur(img_resize_gray, img_resize_gray, Size(5, 5));
		Canny(img_resize_gray, img_canny, 10, 20, 3);  // Canny(img_resize_gray, img_canny, 30, 60, 3);

		//imshow("canny", img_canny);
		//waitKey(0);

		//Mat element = getStructuringElement(MORPH_RECT, Size(3, 3));
		//erode(img_canny, img_canny, element);

		int white_cnt = 0;

		int total_cnt = img_canny.rows * img_canny.cols * img_canny.channels();

		for (int i = 0; i < total_cnt; i++)
			if (img_canny.data[i] > 200)
				white_cnt++;

		float ratio = (float)white_cnt / total_cnt;


		int sum_gray = 0;
		for (int i = 0; i < total_cnt; i++)
			sum_gray += img_resize_gray.data[i];
		float avg_gray = (float)sum_gray / total_cnt;


		//printf("ratio = %f\n", ratio);
		//imshow("canny", img_canny);
		//waitKey(0);

		//return ratio < 0.003 ? true : false;

		//get result
		imgBrightness = avg_gray;
		isCamCoverd = ((ratio < 0.005) || (avg_gray < 22)) ? true : false;
		isFullBlack = (white_cnt < 5 && avg_gray < 30) ? true : false;
	}

	void drawPred(std::vector<object_t> objects, Mat& frame, int G4RunMode)   // Draw the predicted bounding box
	{
		cv::Scalar black(0, 0, 0);
		cv::Scalar red(0, 0, 255);
		cv::Scalar blue(255, 0, 0);
		cv::Scalar green(0, 255, 0);
		cv::Scalar yellow(0, 255, 255);
		cv::Scalar purple(255, 0, 255);


		for (int i = 0; i < objects.size(); i++)
		{
            std::string label = objects[i].label;
			int left = objects[i].mLeft;
			int top = objects[i].mTop;
			int right = objects[i].mRight;
			int bottom = objects[i].mBottom;
			float conf = objects[i].mScore;

#ifdef TD_AODS_IN_OUT
#else			
			//if (label == "person")
				//printf("$$$$$$$$$$$$$$$$$$$$$$$$$$ id = %d, detScore = %f , maxScore = %f, minScore = %f, score = %f\n", objects[i].mId, objects[i].mDetScore, objects[i].mMaxScore, objects[i].mMinScore, objects[i].mScore);
				/*
				if (objects[i].mMinScore < 0.3)
				{
					printf("i got you\n");
					getchar();
				}
				*/
#endif

			cv::Scalar color = black;
			/*
			if (label == "person" || label == "rider")
				color = green;
			if (label == "car" || label == "bus" || label == "truck" || label == "train")
				color = red;
			if (label == "tl_green" || label == "tl_red" || label == "tl_yellow" || label == "tl_none")
				color = yellow;
			if (label == "traffic sign")
				color = blue;
			if (label == "bike" || label == "motor")
				color = black;
			*/

			if (label == "person" || label == "rider")
				color = red;
			if (label == "car")
				color = green;
			if (label == "bus")
				color = yellow;
			if (label == "truck")
				color = blue;
			if (label == "train")
				color = purple;

            if (G4RunMode == INDOOR_LOCATION_MODE)
            {
			    if (objects[i].is_code_info_available==true)
				    color = red;
            }

			//Draw a rectangle displaying the bounding box
			rectangle(frame, Point(left, top), Point(right, bottom), color, 2);

			//Get the label for the class name and its confidence
            std::string label_score = format("%.2f", conf);
            std::string label_display = label + ":" + label_score;

			//Display the label at the top of the bounding box
			int baseLine;
			Size labelSize = getTextSize(label_display, FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);
			top = max(top, labelSize.height);
			//rectangle(frame, Point(left, top - int(1.5 * labelSize.height)), Point(left + int(1.5 * labelSize.width), top + baseLine), Scalar(0, 255, 0), FILLED);
			putText(frame, label_display, Point(left, top - 5), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);

			//Display the ID
            std::string id = format("%d", objects[i].mId);
            std::string ID_display = "ID:" + id;
			putText(frame, ID_display, Point(left + 3, top + 20), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);

			//Display the trend
			std::string trend = format("%d", objects[i].mTrend);
			std::string trend_display = "Trend:" + trend;
			putText(frame, trend_display, Point(left + 3, bottom + 20), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);

			//Display the location
			if (G4RunMode == INDOOR_LOCATION_MODE)
            {
				if (label=="RGB")
				{
					std::string RGBtext = format("%d", objects[i].shelf_number);
					std::string RGB_display = "RGBtext:" + RGBtext;
					putText(frame, RGB_display, Point(left + 3, top - 25), FONT_HERSHEY_SIMPLEX, 0.8, color, 1);

				}
				else
				{
					continue;
				}
					
	
            }
		}
	}

	void drawDASeg(cv::Mat da_seg_mask, cv::Mat& frame)
	{
		//将图1与图2线性混合
		//addWeighted(src1, 0.5, src2, 0.7, 3, dst);
		/*注释
		参数分别为：图1，图1的权重，图2，图2的权重，权重和添加的值为3，输出图片dst
		*/

		cv::Mat tmp_mask = frame.clone();

		for (int i = 0; i < frame.rows; i++)
		{
			for (int j = 0; j < frame.cols; j++)
			{
				if (da_seg_mask.at<uchar>(i, j) > 128)
				{
					tmp_mask.at<Vec3b>(i, j)[0] = 0;  //B
					tmp_mask.at<Vec3b>(i, j)[1] = 255;  //G
					tmp_mask.at<Vec3b>(i, j)[2] = 0;  //R
				}
			}
		}

		addWeighted(frame, 0.6, tmp_mask, 0.4, 3, frame);
	}

	void drawLLSeg(cv::Mat ll_seg_mask, cv::Mat& frame)
	{
		//将图1与图2线性混合
		//addWeighted(src1, 0.5, src2, 0.7, 3, dst);
		/*注释
		参数分别为：图1，图1的权重，图2，图2的权重，权重和添加的值为3，输出图片dst
		*/

		cv::Mat tmp_mask = frame.clone();

		for (int i = 0; i < frame.rows; i++)
		{
			for (int j = 0; j < frame.cols; j++)
			{
				if (ll_seg_mask.at<uchar>(i, j) > 128)
				{
					tmp_mask.at<Vec3b>(i, j)[0] = 0;  //B
					tmp_mask.at<Vec3b>(i, j)[1] = 0;  //G
					tmp_mask.at<Vec3b>(i, j)[2] = 255;  //R
				}
			}
		}

		addWeighted(frame, 0.6, tmp_mask, 0.4, 3, frame);
	}


	void fillNoDetectionArea(cv::Mat& img, std::vector<cv::Point> no_detection_area, int orgw, int orgh, int newh, int neww, int padh, int padw, cv::Scalar color)
	{
		//将不检测区域用黑色填充，避免车体等误检

		if (no_detection_area.size() <= 0)
			return;

		std::vector<cv::Point> trans_no_detection_area;

		float w_ratio = (float)neww / orgw;
		float h_ratio = (float)newh / orgh;

		for (int i = 0; i < no_detection_area.size(); i++)
		{
			int x = no_detection_area[i].x;
			int y = no_detection_area[i].y;

			cv::Point pt;
			pt.x = padw + x * w_ratio;
			pt.y = padh + y * h_ratio;

			trans_no_detection_area.push_back(pt);
		}

		cv::fillPoly(img, trans_no_detection_area, color);

		//cv::imshow("fill", img);
		//cv::waitKey(0);
	}

	char* readLine_Td(FILE* fp, char* buffer, int* len)
	{
		int ch;
		int i = 0;
		size_t buff_len = 0;

		buffer = (char*)malloc(buff_len + 1);
		if (!buffer)
			return NULL; // Out of memory

		while ((ch = fgetc(fp)) != '\n' && ch != EOF)
		{
			buff_len++;
			void* tmp = realloc(buffer, buff_len + 1);
			if (tmp == NULL)
			{
				free(buffer);
				return NULL; // Out of memory
			}
			buffer = (char*)tmp;

			buffer[i] = (char)ch;
			i++;
		}
		buffer[i] = '\0';

		*len = buff_len;

		// Detect end
		if (ch == EOF && (i == 0 || ferror(fp)))
		{
			free(buffer);
			return NULL;
		}
		return buffer;
	}

	int readLines_Td(const char* fileName, char* lines[], int max_line)
	{
		FILE* file = fopen(fileName, "r");
		char* s = NULL;
		int i = 0;
		int n = 0;
		while ((s = readLine_Td(file, s, &n)) != NULL)
		{
			lines[i++] = s;
			if (i >= max_line)
				break;
		}
		return i;
	}

	int loadLabelName(const char* locationFilename, char* label[], int objNum)
	{
		printf("loadLabelName %s\n", locationFilename);
		readLines_Td(locationFilename, label, objNum);
		return 0;
	}

	int clamp(float val, int min, int max)
	{
		return val > min ? (val < max ? val : max) : min;
	}

	float sigmoid(float x)
	{
		return 1.0 / (1.0 + expf(-x));
	}

	float unsigmoid(float y)
	{
		return -1.0 * logf((1.0 / y) - 1.0);
	}

	float CalculateOverlap(float xmin0, float ymin0, float xmax0, float ymax0, float xmin1, float ymin1, float xmax1, float ymax1)
	{
		float w = fmax(0.f, fmin(xmax0, xmax1) - fmax(xmin0, xmin1) + 1.0);
		float h = fmax(0.f, fmin(ymax0, ymax1) - fmax(ymin0, ymin1) + 1.0);
		float i = w * h;
		float u = (xmax0 - xmin0 + 1.0) * (ymax0 - ymin0 + 1.0) + (xmax1 - xmin1 + 1.0) * (ymax1 - ymin1 + 1.0) - i;
		return u <= 0.f ? 0.f : (i / u);
	}

	int nms_u8(int validCount, std::vector<float>& outputLocations, std::vector<int> classIds, std::vector<int>& order, int filterId, float threshold)
	{
		for (int i = 0; i < validCount; ++i)
		{
			if (order[i] == -1 || classIds[i] != filterId)
			{
				continue;
			}
			int n = order[i];
			for (int j = i + 1; j < validCount; ++j)
			{
				int m = order[j];
				if (m == -1 || classIds[i] != filterId)
				{
					continue;
				}
				float xmin0 = outputLocations[n * 4 + 0];
				float ymin0 = outputLocations[n * 4 + 1];
				float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
				float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

				float xmin1 = outputLocations[m * 4 + 0];
				float ymin1 = outputLocations[m * 4 + 1];
				float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
				float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

				float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);

				if (iou > threshold)
				{
					order[j] = -1;
				}
			}
		}
		return 0;
	}

	int nms_fp(int validCount, std::vector<float>& outputLocations, std::vector<int>& order, float threshold)
	{
		for (int i = 0; i < validCount; ++i)
		{
			if (order[i] == -1)
			{
				continue;
			}
			int n = order[i];
			for (int j = i + 1; j < validCount; ++j)
			{
				int m = order[j];
				if (m == -1)
				{
					continue;
				}
				float xmin0 = outputLocations[n * 4 + 0];
				float ymin0 = outputLocations[n * 4 + 1];
				float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
				float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

				float xmin1 = outputLocations[m * 4 + 0];
				float ymin1 = outputLocations[m * 4 + 1];
				float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
				float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

				float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);

				if (iou > threshold)
				{
					order[j] = -1;
				}
			}
		}
		return 0;
	}

	int quick_sort_indice_inverse(std::vector<float>& input, int left, int right, std::vector<int>& indices)
	{
		float key;
		int key_index;
		int low = left;
		int high = right;
		if (left < right)
		{
			key_index = indices[left];
			key = input[left];
			while (low < high)
			{
				while (low < high && input[high] <= key)
				{
					high--;
				}
				input[low] = input[high];
				indices[low] = indices[high];
				while (low < high && input[low] >= key)
				{
					low++;
				}
				input[high] = input[low];
				indices[high] = indices[low];
			}
			input[low] = key;
			indices[low] = key_index;
			quick_sort_indice_inverse(input, left, low - 1, indices);
			quick_sort_indice_inverse(input, low + 1, right, indices);
		}
		return low;
	}

#ifdef TD_USING_RKNN
	unsigned char* load_data(FILE* fp, size_t ofst, size_t sz)
	{
		unsigned char* data;
		int ret;

		data = NULL;

		if (NULL == fp)
		{
			return NULL;
		}

		ret = fseek(fp, ofst, SEEK_SET);
		if (ret != 0)
		{
			printf("blob seek failure.\n");
			return NULL;
		}

		data = (unsigned char*)malloc(sz);
		if (data == NULL)
		{
			printf("buffer malloc failure.\n");
			return NULL;
		}
		ret = fread(data, 1, sz, fp);
		return data;
	}

	unsigned char* load_model(const char* filename, int* model_size)
	{

		FILE* fp;
		unsigned char* data;

		fp = fopen(filename, "rb");
		if (NULL == fp)
		{
			printf("Open file %s failed.\n", filename);
			return NULL;
		}

		fseek(fp, 0, SEEK_END);
		int size = ftell(fp);

		data = load_data(fp, 0, size);

		fclose(fp);

		*model_size = size;
		return data;
	}

	char* get_type_string(rknn_tensor_type type)
	{
		switch (type) {
		case RKNN_TENSOR_FLOAT32: return "FP32";
		case RKNN_TENSOR_FLOAT16: return "FP16";
		case RKNN_TENSOR_INT8: return "INT8";
		case RKNN_TENSOR_UINT8: return "UINT8";
		case RKNN_TENSOR_INT16: return "INT16";
		default: return "UNKNOW";
		}
	}

	char* get_qnt_type_string(rknn_tensor_qnt_type type)
	{
		switch (type) {
		case RKNN_TENSOR_QNT_NONE: return "NONE";
		case RKNN_TENSOR_QNT_DFP: return "DFP";
		case RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC: return "AFFINE";
		default: return "UNKNOW";
		}
	}

	char* get_format_string(rknn_tensor_format fmt)
	{
		switch (fmt) {
		case RKNN_TENSOR_NCHW: return "NCHW";
		case RKNN_TENSOR_NHWC: return "NHWC";
		default: return "UNKNOW";
		}
	}

	void dump_tensor_attr(rknn_tensor_attr* attr)
	{
		printf("  index=%d, name=%s, n_dims=%d, dims=[%d, %d, %d, %d], n_elems=%d, size=%d, fmt=%s, type=%s, qnt_type=%s, "
			"zp=%d, scale=%f\n",
			attr->index, attr->name, attr->n_dims, attr->dims[3], attr->dims[2], attr->dims[1], attr->dims[0],
			attr->n_elems, attr->size, get_format_string(attr->fmt), get_type_string(attr->type),
			get_qnt_type_string(attr->qnt_type), attr->zp, attr->scale);
	}

	double __get_us_td(struct timeval t) { return (t.tv_sec * 1000000 + t.tv_usec); }




	/**
     * @brief 对神经网络vis模型进行解密并读出版本号
     * @param vis_model_input vis模型的输入buffer
     * @param vis_model_input_len vis模型的buffer长度
     * @param rknn_model_output_len 输出的rknn模型buffer长度
     * @param key1_output 版本号数
     * @param key2_output 版本号数
     * @param key3_output 版本号数
     * @param isWriteFile 是否将rknn模型写成文件，一般在windows端的工具中保存，G4上不做保存操作
     * @param path_write_file 写rknn文件的路径，不做保存操作时置NULL，一般在windows端的工具中保存，G4上不做保存操作
     * @return 输出的rknn模型buffer
    */
	unsigned char* decode(unsigned char* vis_model_input, int vis_model_input_len, int* rknn_model_output_len, char* key1_output, char* key2_output, char* key3_output, bool isWriteFile, char* path_write_file)
	{
		*rknn_model_output_len = vis_model_input_len - 3;

		*key1_output = vis_model_input[0];
		*key2_output = vis_model_input[1];
		*key3_output = vis_model_input[2];

		unsigned char* rknn_model_output = (unsigned char*)malloc((*rknn_model_output_len + 5) * sizeof(unsigned char));

		for (int i = 0; i < *rknn_model_output_len; i++)
			rknn_model_output[i] = vis_model_input[i + 3];

		///////  make the dictionarie key here  /////////////////////////////////////////////////////////////////////////////////////
		unsigned char* dict = (unsigned char*)malloc((*rknn_model_output_len + 5) * sizeof(unsigned char));
		memset(dict, 0, *rknn_model_output_len);
		for (int i = 0; i < *rknn_model_output_len; i += 3)
		{
			int half = floor(*rknn_model_output_len * 0.5);
			if (i < half)
			{
				dict[i] = *key1_output;
				dict[i + 1] = *key2_output;
				dict[i + 2] = *key3_output;
			}
			else
			{
				dict[i] = *key3_output;
				dict[i + 1] = *key2_output;
				dict[i + 2] = *key1_output;
			}
		}
		////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

		for (int i = 0; i < *rknn_model_output_len; i++)
			rknn_model_output[i] = rknn_model_output[i] ^ dict[i];



		if (isWriteFile && path_write_file != NULL)
		{
			FILE* fp;
			fp = fopen(path_write_file, "wb");

			fwrite(rknn_model_output, sizeof(unsigned char), *rknn_model_output_len, fp);
			//fputs((char *)out, fp);

			fclose(fp);
		}



		free(dict);
		dict = NULL;

		return rknn_model_output;
    }

	/*
	void decode(unsigned char* input, unsigned char* output, unsigned char* dict, int len)
	{
		for (int i = 0; i < len; i++)
			output[i] = input[i] ^ dict[i];
	}
	*/

	int saveFloat(const char* file_name, float* output, int element_size)
	{
		FILE* fp;
		fp = fopen(file_name, "w");
		for (int i = 0; i < element_size; i++)
		{
			fprintf(fp, "%.6f\n", output[i]);
		}
		fclose(fp);
		return 0;
	}
#endif

}