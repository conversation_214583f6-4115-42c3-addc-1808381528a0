#include  "tdShareDefine.h"

#ifdef TD_G3_APP_IN_OUT

#include <unistd.h>
#include "Detecter_BSD.h"

#elif defined TD_AODS_IN_OUT
#include <string>
#include <unistd.h>
#include "DomainDgram.h"
#include "ObjectInfoMessage.h"
#include "VisDomainPath.h"

#include "ShmManager.h"

#include "RegisterToWatchdog.h"
#include "RegisterStatusToWatchdog.h"
#include "HeartbeatMessage.h"
#endif

#ifdef TD_USING_RGA

#include "XuRGAUtils.h"

#endif

#include "tdDetect.h"

using namespace cv;
using namespace std;

#ifdef TD_USING_RGA
XuRGAUtils rga_utils;
#endif


#ifdef TD_G3_APP_IN_OUT

int Detecter_BSD::init(int cameraId, DetectDataCallback &detectDataCallback) {

    curCameraId = cameraId;
    curDetectDataCallback = &detectDataCallback;

    alarmDecisionBsd.init(cameraId, detectDataCallback);


    return 0;
}

#endif

#ifdef TD_AODS_IN_OUT
bool isRegisted = false;

void registerToWatchDog(vis::DomainDgram &domainDgram, vis::VisModule::id_e id)
{
    vis::RegisterToWatchdog reg;
    vis::RegisterStatusToWatchdog status(id);

    domainDgram.sendDgramTo(vis::VisModule::watchdog, reg);
    domainDgram.sendDgramTo(vis::VisModule::watchdog, status);
}


void parseRegisterStat(const char *data, int len)
{
    vis::RegisterStatusToWatchdog status(vis::VisModule::algorithm);
    vis::RegisterStatusToWatchdog::taskStat_e taskStat;

    status.doUnSerialize(data, len);

    taskStat = status.getStat();

    if (vis::RegisterStatusToWatchdog::taskRegistered == taskStat)
    {
        isRegisted = true;
    }
    else
    {
        isRegisted = false;
    }
}

void parseMessage(const char *data, int len)
{
    int id = 0;

    id = vis::Message::getCmd(data);

    switch (id)
    {
        case vis::Message::mid_tw_isRegister:
            parseRegisterStat(data, len);
            break;
        default:
            break;
    }
}
#endif
#ifdef TD_G3_APP_IN_OUT

void Detecter_BSD::run()
#else
void run()
#endif
{
    std::string pthreadName = "BSD_";
    pthreadName.append(std::to_string(curCameraId));
    pthread_setname_np(pthread_self(), pthreadName.c_str());


    int ret = -1;
    std::vector<cv::Point> ignoreArea1;
    std::vector<cv::Point> ignoreArea2;

    td::Detect *detect = new td::Detect();

    const int input_width = 1280;
    const int input_height = 720;

    td::objectInfo_t object_info;
    object_info.alarmInfo = {};
    object_info.faultInfo = {};
    object_info.objects.clear();

    Mat srcimg = Mat::zeros(input_height, input_width, CV_8UC3);

    ignoreArea1 = getIgnoreAreaPoint1();
    ignoreArea2 = getIgnoreAreaPoint2();

    td::ROI_RECT roiRect;
    roiRect.x = 0;
    roiRect.y = 0;
    roiRect.width = 1280;
    roiRect.height = 720;

    detect->init(input_width, input_height, roiRect, ignoreArea1, ignoreArea2,ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION);
    detectOpen = true;



#ifdef TD_G3_DEMO_IN_OUT
    uint8_t *cam_data;
    CamCap G3_cap;

    // ÉãÏñÍ·³õÊ¼»¯
    cam_data = (uint8_t *)malloc(1280 * 1024 * 2);

    if (G3_cap.setDevice("/dev/video0") < 0)
    {
        perror("setDevice fail");
        return;
    }

    if (G3_cap.setFmt(input_width, input_height, V4L2_PIX_FMT_UYVY) < 0)
    {
        perror("setFmt fail");
        return;
    }

    if (G3_cap.streamOn() < 0)
    {
        perror("streamOn fail");
        return;
    }

        Mat yuvImg = Mat::zeros(input_height, input_width, CV_8UC2);

        // ÊÓÆµÊä³ö³õÊ¼»¯
        const int G3_vo_width = 720;
        const int G3_vo_height = 576;
        voInit();
        Mat displayImg = Mat::zeros(G3_vo_height, G3_vo_width, CV_8UC3);
        Mat displayYUVImg = Mat::zeros(G3_vo_height, G3_vo_width, CV_8UC2);
#elif defined TD_G3_APP_IN_OUT
    uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
    Mat yuvImg = Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);
#elif defined TD_AODS_IN_OUT
    ShmManager shmmanager;
    shmmanager.openShm((char *)"testShm", (4194320 * 5));
    shmmanager.mmapShm();
    ShmData shmData;
    int failedSum = 0;
    int len = 0;
    vector<char> data;


    int hd_width = 1920;
    int hd_height = 1080;
    Mat yuvimg_hd(hd_height * 3 / 2, hd_width, CV_8UC1);
    Mat rgbimg_hd(hd_height, hd_width, CV_8UC3);


    //vis::DomainDgram dgram("/tmp/vistest");      // 这个对象用算法的DomainDgram对象替代
    vis::DomainDgram dgram(vis::VisDomainPath::getDomainPath(vis::VisModule::algorithm));      // 这个对象用算法的DomainDgram对象替代
    // 注意：ObjectInfoMessage对象尽量不要频繁初始化，因为该对象每实例化一次都会申请堆内存，都需要部分耗时,会影响效率，尽量实例化一次后循环使用
    vis::ObjectInfoMessage message;              // 将要被发送的消息
    object_t aods_obj{};                         // 单个obj
    objectInfo_t &aods_info = message.getInfo(); // 直接从消息中获取objectInfo_t对象

    data.resize(1024*1024);

    // 循环注册，当注册成功后退出
    while (!isRegisted)
    {
        len = dgram.recvDgram(data.data(), data.size(), 1000);
        if (len > 0 && len >= vis::Message::getMinLen())
        {
            parseMessage(data.data(), len);
        }
        else
        {
        }

        if (!isRegisted)
        {
            registerToWatchDog(dgram, vis::VisModule::algorithm);
        }
    }
#endif

#ifdef TD_RUN_VIDEO
    VideoCapture capture("20190723155320.mp4");   //20190723155320.mp4   20220726135704.mp4
    if(!capture.isOpened())
        cout << "fail to open!" << endl;
#endif

    int frame_cnt = 0;

    /* 当前镜头是否被遮挡 */
    bool isCamCoverd = false;
    /* 当前画面的亮度 */
    float imgBrightness = -1;
    /* 当前的镜头是否全黑 */
    bool isFullBlack = false;
    /* 是否需要清理识别信息 */
    bool needclean = false;
    while (1) {
        frame_cnt++;

        double BeginTime_alg = (double) cv::getTickCount();

#ifdef TD_G3_DEMO_IN_OUT
        ret = G3_cap.getFmtData(cam_data, 1280 * 720 * 2);  // »ñÈ¡ÉãÏñÍ·Í¼Ïñ   ret = G3_cap.getFmtData(cam_data, 1280 * 1024 * 2);
        if (ret < 0)
        {
            perror("getFmtData fail");
            return;
        }
        else if (0 == ret)
        {
            continue;
        }

        //memcpy(yuvImg.data, (unsigned char*)cam_data, input_width * input_height * 2);
        yuvImg.data = (unsigned char*)cam_data;
        cvtColor(yuvImg, srcimg, COLOR_YUV2BGR_UYVY);
        //imwrite("out.jpg", srcimg);
        //waitKey(0);
        //return 0;
#elif defined TD_G3_APP_IN_OUT
        ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData,curVehicleStatus,&needclean);
        if (ret != 0) {
            usleep(5 * 1000);
            continue;
        }

#ifdef TD_USING_RGA
        rga_utils.imageTransformation(input_width, input_height, rga_utils.IMG_TYPE_NV21, cameraYuvData,
                                      input_width, input_height, rga_utils.IMG_TYPE_BGR888, srcimg.data);


#else


        yuvImg.data = cameraYuvData.getCurYuvData();
    cvtColor(yuvImg, srcimg, COLOR_YUV2BGR_NV12);
#endif

        //imwrite("out.jpg", srcimg);
        //return;
        //waitKey(0);
        //return;
#elif defined TD_AODS_IN_OUT
        if (failedSum >= 500)
        {
            break;
        }
        else
        {
            int resulr = shmmanager.readOnePackage(shmData);
            if (resulr == 0)
            {


                /////////////////////////////////////////////////////////
                unsigned char* yuv_data = shmData.data += 23040;    //跳过文件头

#ifdef TD_USING_RGA
                rga_utils.imageTransformation(hd_width, hd_height, rga_utils.IMG_TYPE_NV21, yuv_data, hd_width, hd_height, rga_utils.IMG_TYPE_BGR888, rgbimg_hd.data);
                rga_utils.imageTransformation(hd_width, hd_height, rga_utils.IMG_TYPE_BGR888, rgbimg_hd.data, srcimg.cols, srcimg.rows, rga_utils.IMG_TYPE_BGR888, srcimg.data);
#else
                memcpy(yuvimg_hd.data, yuv_data, hd_width * hd_height * 3 / 2);
                cvtColor(yuvimg_hd, rgbimg_hd, COLOR_YUV2BGR_NV12);
                resize(rgbimg_hd, srcimg, Size(srcimg.cols, srcimg.rows));
#endif

                shmmanager.releaseOnePackage(shmData);
                failedSum = 0;

                //imwrite("out.jpg", srcimg);
                //return;
                /////////////////////////////////////////////////////////
            }
            else
            {
                failedSum++;
                usleep(1000 * 5);  //5ms
                continue;
            }
        }
#else
        #ifdef RUN_VIDEO
        capture >> srcimg;
#else
        srcimg = imread(detect->getImgPath());
#endif
#endif

//        double lastTime_alg = (double) cv::getTickCount();
        detect->detect(srcimg);
//        double time_onlyDetect = ((double) cv::getTickCount() - lastTime_alg) / cv::getTickFrequency();
//        int time_onlyDetect_alg = (int) (time_onlyDetect * 1000 + 0.5);
        object_info = detect->getResult();

//        /* 打印出所有的识别框的内容 */
//        if(object_info.objects.size() > 0){
//            printf("++++++++++++++++++++++++++++++++++objects.size()=%d   \n",object_info.objects.size());
//            for(int i = 0; i < object_info.objects.size(); i ++){
//                printf("+++++++++++++++++++index=%d   label=%s    top=%d  bottom=%d   left=%d   right=%d  \n",i,object_info.objects[i].label,object_info.objects[i].mTop,object_info.objects[i].mBottom,object_info.objects[i].mLeft,object_info.objects[i].mRight);
//            }
//        }else{
//            printf("++++++++++++++++++++++++++++++++++object_info.objects.size() < 0 \n");
//        }



//        printf("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~frame_cnt = %d\n", frame_cnt);
        if (frame_cnt % 50 == 3) {

//            double BeginTime_alg7 = (double)cv::getTickCount();

            td::CalcCamCoverd(srcimg,imgBrightness,isCamCoverd,isFullBlack);

//            double time4_alg7 = ((double) cv::getTickCount() - BeginTime_alg7) / cv::getTickFrequency();
//            int time5_alg7 = (int) (time4_alg7 * 1000 + 0.5);
//            printf("~~~~~~~~~~~~~~~~cam covered is time %dms\n ", time5_alg7);

            //printf("~~~~~~~~~~~~~~~~cam covered is %d\n", isCamCoverd);
        }

        if (isCamCoverd) {
#if (defined TD_G3_APP_IN_OUT || defined TD_G3_DEMO_IN_OUT || defined WIN32 || defined _WIN32 )
//            object_info.alarmInfo = object_info.alarmInfo | CAMERA_COVERED_ALARM_INFO;
        }
//        printf("~~~~~~~~~~~~~~~~cam covered is %d   objects.size=%d  faultInfo=%llu alarmInfo=%llu    \n", isCamCoverd,object_info.objects.size(),object_info.faultInfo,object_info.alarmInfo);


#elif defined TD_AODS_IN_OUT
            object_info.alarmInfo = object_info.alarmInfo | LONG_FOCUS_CAMERA_SENSOR_FAULT_ALARM_INFO;   //暂定
        }
#else
#endif

#if (defined WIN32 || defined _WIN32 )
        Mat showimg = detect->draw(srcimg, object_info);

        namedWindow("show");
        imshow("show", showimg);
        waitKey(0);
#else
#ifdef TD_G3_DEMO_IN_OUT
        Mat showimg = detect->draw(srcimg, object_info);
        resize(showimg, displayImg, Size(G3_vo_width, G3_vo_height), INTER_NEAREST);
        rgb2yuv422_image((unsigned char *)displayImg.data, (unsigned char *)displayYUVImg.data, G3_vo_width, G3_vo_height);
        voSetFmt((unsigned char *)displayYUVImg.data);  //cam_data  displayYUVImg.data
        // printf("get fmt cam_data size : %d\n", ret);
        // save_frame_to_file("./cap.yuv", cam_data, ret);
#elif defined TD_G3_APP_IN_OUT




        /* 这里交给报警决策判断一下 */
        alarmDecisionBsd.parseObjectInfos(object_info, curVehicleStatus.speed,isCamCoverd);
//        printf("~~~~~~~~~~~~~~~~alarmDecisionBsd.parseObjectInfos(object_info,10)    \n");
        lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
//        printf("~~~~~~~~~~~~~~~~lastDetectTime = XuTimeUtil::getInstance().currentTimeMillis()    \n");



#elif defined TD_AODS_IN_OUT
        aods_info.alarmInfo = object_info.alarmInfo;
        aods_info.faultInfo = object_info.faultInfo;
        aods_info.objects.clear();
        for (int t = 0; t < object_info.objects.size(); t++)
        {
            strcpy(aods_obj.label, object_info.objects[t].label);
            aods_obj.mId = object_info.objects[t].mId;
            aods_obj.mTop = object_info.objects[t].mTop;
            aods_obj.mBottom = object_info.objects[t].mBottom;
            aods_obj.mLeft = object_info.objects[t].mLeft;
            aods_obj.mRight = object_info.objects[t].mRight;
            aods_obj.mXDistance = object_info.objects[t].mXDistance;
            aods_obj.mYDistance = object_info.objects[t].mYDistance;
            aods_obj.mHeadway = object_info.objects[t].mHeadway;
            aods_obj.mRealWidth = object_info.objects[t].mRealWidth;
            aods_obj.mRealHeight = object_info.objects[t].mRealHeight;
            aods_obj.mXVelocity = object_info.objects[t].mXVelocity;
            aods_obj.mYVelocity = object_info.objects[t].mYVelocity;
            aods_obj.mTrend = object_info.objects[t].mTrend;
            aods_obj.mScore = object_info.objects[t].mScore;
            aods_info.objects.push_back(aods_obj);
        }
        //memcpy(aods_info.maskBufDA.data(), object_info.maskBufDA, TD_MASK_BUF_SIZE * sizeof(unsigned char));
        //memcpy(aods_info.maskBufLL.data(), object_info.maskBufLL, TD_MASK_BUF_SIZE * sizeof(unsigned char));
        memcpy(aods_info.maskBufDA, object_info.maskBufDA, TD_MASK_BUF_SIZE * sizeof(unsigned char));
        memcpy(aods_info.maskBufLL, object_info.maskBufLL, TD_MASK_BUF_SIZE * sizeof(unsigned char));
        ret = dgram.sendDgramTo(vis::VisModule::sch, message);
        if (ret < 0)
#else
            Mat showimg = detect->draw(srcimg, object_info);
        printf("object size = %d\n", object_info.objects.size());
        for (int t = 0; t < object_info.objects.size(); t++)
        {
            object_t obj = object_info.objects[t];
              printf("label = %s, score = %f, left = %d, right = %d, top = %d, bottom = %d\n", obj.label, obj.mScore, obj.mLeft, obj.mRight, obj.mTop, obj.mBottom);
        }

        //imwrite("out.jpg", showimg);
#endif
#endif

#ifdef TD_AODS_IN_OUT
            if (isRegisted)  // 如果注册成功，则每个循环都发一次心跳
                vis::HeartbeatMessage().sendHeartbeat(dgram);
#endif

//        double time2_alg = ((double) cv::getTickCount() - BeginTime_alg) / cv::getTickFrequency();
//        int time3_alg = (int) (time2_alg * 1000 + 0.5);
////        printf("~~~~~~~~~~~~~~~~time3_alg = (int) (time2_alg * 1000 + 0.5);    \n");
//
//        double time4_alg = ((double) cv::getTickCount() - lastTime_alg) / cv::getTickFrequency();
//        int time5_alg = (int) (time4_alg * 1000 + 0.5);


//  printf("\n$$$$$$$$$$$$$$$$$ total time %dms\n ", time3_alg);

    }

    delete detect;


#if (defined WIN32 || defined _WIN32 )
    destroyAllWindows();
#endif
#ifdef TD_G3_DEMO_IN_OUT
    voFree();  //ÊÍ·ÅÊÓÆµÊä³ö×ÊÔ´
#endif

    return;
}


#ifdef TD_G3_APP_IN_OUT

bool Detecter_BSD::isDetectOpen() const {
    return detectOpen;
}

__time_t Detecter_BSD::getLastDetectTime() const {
    return lastDetectTime;
}



/**
 * 获得忽略报警区域1
 * @return 忽略区域1的所有点
 */
std::vector<cv::Point> Detecter_BSD::getIgnoreAreaPoint1() const {
    G3_Configuration &conf = G3_Configuration::getInstance();
    std::vector<cv::Point> point;
    std::vector<VISPoint> vPoint;

    // 根据镜头id获取各自忽略的区域
    if (CAMERA_ID_1 == curCameraId) {
        vPoint = conf.getUndetectedAreaListCamera1()[0];
    } else if (CAMERA_ID_2 == curCameraId) {
        vPoint = conf.getUndetectedAreaListCamera2()[0];
    }

    // 转换格式
    for (size_t i = 0; i < vPoint.size(); ++i) {
        cv::Point cp;
        cp.x = vPoint[i].x;
        cp.y = vPoint[i].y;

        point.push_back(cp);
    }

    return point;
}

/**
 * 获得忽略报警区域2
 * @return 忽略区域2的所有点
 */
std::vector<cv::Point> Detecter_BSD::getIgnoreAreaPoint2() const {
    G3_Configuration &conf = G3_Configuration::getInstance();
    std::vector<cv::Point> point;
    std::vector<VISPoint> vPoint;

    // 根据镜头id获取各自忽略的区域
    if (CAMERA_ID_1 == curCameraId) {
        vPoint = conf.getUndetectedAreaListCamera1()[1];
    } else if (CAMERA_ID_2 == curCameraId) {
        vPoint = conf.getUndetectedAreaListCamera2()[1];
    }

    // 转换格式
    for (size_t i = 0; i < vPoint.size(); ++i) {
        cv::Point cp;
        cp.x = vPoint[i].x;
        cp.y = vPoint[i].y;

        point.push_back(cp);
    }

    return point;
}

#endif


#ifdef TD_G3_APP_IN_OUT
#else
int main()
{
    run();
    return 0;
}
#endif
