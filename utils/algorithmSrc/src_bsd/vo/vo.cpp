/*
 * libusb example program to list devices on the bus
 * Copyright © 2007 <PERSON> <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#include <libusb-1.0/libusb.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include "ms2160.h"
#include "usbtransfer.h"
#include "vo.h"

const int img_width = 720;
const int img_height = 576;

libusb_device_handle *handle = NULL;

static void print_devs(libusb_device **devs)
{
    libusb_device *dev;
    int i = 0, j = 0;
    uint8_t path[8];

    while ((dev = devs[i++]) != NULL)
    {
        struct libusb_device_descriptor desc;
        int r = libusb_get_device_descriptor(dev, &desc);
        if (r < 0)
        {
            fprintf(stderr, "failed to get device descriptor");
            return;
        }

        printf("%04x:%04x (bus %d, device %d)", desc.idVendor, desc.idProduct,
               libusb_get_bus_number(dev), libusb_get_device_address(dev));

        if (desc.idVendor == 0x534D && desc.idProduct == 0x6021)
        {
            handle = 0;
            libusb_open(dev, &handle);
            printf("handle = 0x%p \n", handle);
        }

        r = libusb_get_port_numbers(dev, path, sizeof(path));
        if (r > 0)
        {
            printf(" path: %d", path[0]);
            for (j = 1; j < r; j++)
                printf(".%d", path[j]);
        }
        printf("\n");
    }
}

void colorspaceTransfer(int width, int height, unsigned char *src, unsigned char *dst)
{
    int srcCount = 0;
    bool ifgetU = true;
    unsigned char *p1 = (unsigned char *)src;
    unsigned char *p0 = (unsigned char *)dst;
    int temp = 0;

    for (int i = 0; i < height; i++)
    {
        for (int j = 0; j < width; j++)
        {
            if (ifgetU)
            {
                temp = (int)(-0.148 * (*(p1 + 2)) - 0.291 * (*(p1 + 1)) + 0.439 * (*p1) + 128); // U
                *p0 = (unsigned char)((temp < 0) ? 0 : ((temp > 255) ? 255 : temp));
                ifgetU = false;
            }
            else
            {
                temp = (int)(0.439 * (*(p1 + 2)) - 0.368 * (*(p1 + 1)) - 0.071 * (*p1) + 128); // V
                *p0 = (unsigned char)((temp < 0) ? 0 : ((temp > 255) ? 255 : temp));
                ifgetU = true;
            }

            temp = (int)(0.257 * (*(p1 + 2)) + 0.504 * (*(p1 + 1)) + 0.098 * (*p1) + 16); // Y
            *(p0 + 1) = (unsigned char)((temp < 0) ? 0 : ((temp > 255) ? 255 : temp));

            p1 += 3;
            p0 += 2;
        }
    }
}

int mVideoDisplayVIC = VFMT_CEA_17_720x576P_50HZ;  //origin:VFMT_CEA_17_720x576P_50HZ

int mVideoInColorSpace = YUV422;  //YUV422

int mVieonInMEMColorSpace = YUV422;  //YUV422

int mVideoDisplayColorSpace = RGB888;

int mTransferMode = FRAME;

struct RESOLUTION mVideoInResolution = {0};

void start_transaction()
{
    byte color = 0;
    if (mTransferMode == FRAME)
    {
        set_transfer_mode_frame((long)handle);
        color = (byte)(((byte)mVieonInMEMColorSpace << 4) | (byte)mVideoInColorSpace);
        printf("mVieonInMEMColorSpace = %d\n", mVieonInMEMColorSpace);
        printf("mVideoInColorSpace = %d\n", mVideoInColorSpace);
    }

    int width = (mVideoInResolution.width + 3) & ~3;
    set_video_in((long)handle, width, mVideoInResolution.height, color, 0x00);
    set_video_out((long)handle, mVideoDisplayVIC, mVideoDisplayColorSpace, mVideoInResolution.width,
                  mVideoInResolution.height);

    printf("width = %d\n", width);
    // 1.向下位机发送开始传输HID指令
    set_start_trans((long)handle, 0x01);
}
#if 0
int main(void)
{
    libusb_device **devs;

    int r;
    ssize_t cnt;

    r = libusb_init(NULL);
    if (r < 0)
        return r;

    cnt = libusb_get_device_list(NULL, &devs);
    if (cnt < 0)
        return (int)cnt;

    print_devs(devs);

    unsigned char *pictureBuffer = (unsigned char *)malloc(720 * 576 * 3);

    unsigned char *yuvBuffer = (unsigned char *)malloc(720 * 576 * 2);

    mVideoInResolution.width = 720;
    mVideoInResolution.height = 576;

    mVideoInResolution.framerate = 50;

    claimInterface(handle, 0);

    set_start_trans(handle, 0x00);
    set_power_on(handle, (byte)0x01);

    usleep(50000);

    start_transaction();

    set_video_on(handle, (byte)0x01);

    int frame_id = 0;

    for (int i = 0; i < 300; i++)
    {
        memset(pictureBuffer, i % 255, 720 * 576 * 3);
        colorspaceTransfer(720, 576, pictureBuffer, yuvBuffer);
        usbBulkTransfer(handle, 4, yuvBuffer, 720 * 576 * 2, 300);
        frame_id = frame_id == 0x00 ? 0x01 : 0x00;
        // m_winLibusb->frame_trigger(frame_id, 0);
        frame_trigger_switch(handle, frame_id);
        xdata_write(handle, 0xF202, (unsigned char)0x01);
    }

    releaseInterface(handle, 0);
    libusb_free_device_list(devs, 1);

    libusb_exit(NULL);
    return 0;
}
#endif

libusb_device **devs = NULL;

int voInit()
{
    int r;
    ssize_t cnt;

    r = libusb_init(NULL);
    if (r < 0)
        return r;

    cnt = libusb_get_device_list(NULL, &devs);
    if (cnt < 0)
        return (int)cnt;

    print_devs(devs);

    // unsigned char *pictureBuffer = (unsigned char *)malloc(720 * 576 * 3);

    // unsigned char *yuvBuffer = (unsigned char *)malloc(720 * 576 * 2);

    mVideoInResolution.width = img_width;
    mVideoInResolution.height = img_height;

    mVideoInResolution.framerate = 50;  //origin 50     24

    claimInterface((long)handle, 0);

    set_start_trans((long)handle, 0x00);
    set_power_on((long)handle, (byte)0x01);

    usleep(50000);

    start_transaction();

    set_video_on((long)handle, (byte)0x01);

    return 0;
}

int voFree()
{
    releaseInterface((long)handle, 0);
    libusb_free_device_list(devs, 1);

    libusb_exit(NULL);

    return 0;
}

int voSetFmt(unsigned char *yuvBuffer)
{
    static int frame_id = 0;
    usbBulkTransfer((long)handle, 4, yuvBuffer, img_width * img_height * 2, 300);
    frame_id = frame_id == 0x00 ? 0x01 : 0x00;
    // m_winLibusb->frame_trigger(frame_id, 0);
    frame_trigger_switch((long)handle, frame_id);
    xdata_write((long)handle, 0xF202, (unsigned char)0x01);

    return 0;
}