#ifndef USBTRANSFER_H
#define USBTRANSFER_H

typedef struct t_TRANSFER_INFO
{
    int indexEndpoint;
    int indexInterface;
    int requestType;
    int request;
    int value;
    int index;
    unsigned char* buffer;
    int length;
    int timeout;
    int vid;
    int pid;
}TRANSFER_INFO;

typedef unsigned char byte;

int releaseInterface(long handle, int interface);

int claimInterface(long handle, int interface);

int usbBulkTransfer(long handle, int endpoint, unsigned char* buffer, int length, int timeout);

int usbControlTransfer(long handle, TRANSFER_INFO *p_transfer_info);

unsigned char xdata_read(long handle,int addr);

void xdata_write(long handle,int addr, unsigned char xdata_value);

void frame_trigger(long handle, unsigned char fid, unsigned char delay);

void frame_trigger_switch(long handle,unsigned char frame_index);

void set_start_trans(long handle,byte start_flag);

void set_transfer_mode_frame(long handle);

void set_transfer_mode_manual_block(long handle);

void set_transfer_mode_membypass(long handle);

void set_transfer_mode_membypass_frame(long handle);

void set_video_in(long handle,int width, int height, byte color, byte colSel);

void set_video_out(long handle,byte timing_index, byte color, int width, int height);

void set_video_on(long handle,byte video_on_enable);

void set_power_on(long handle, byte power_on_enable);
#endif
