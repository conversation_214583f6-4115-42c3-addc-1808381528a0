//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/22.
//

#ifndef VIS_G3_SOFTWARE_ALARMDECISION_BSD_H
#define VIS_G3_SOFTWARE_ALARMDECISION_BSD_H

#include "XuCalculationTool.h"
#include  "tdShareDefine.h"
#include "DetectDataCallback.h"
#include "utils/G3_Configuration.h"
#include "utils/XuTimeUtil.h"
#include "G3DetectionDefind.h"

class AlarmDecision_BSD {
public:
    AlarmDecision_BSD();

    ~AlarmDecision_BSD();

    int init(int cameraId, DetectDataCallback &detectDataCallback);

    void setAlarmAreaPoints(const VISPoint *pointList, int len);

    bool isPedestrianInAlarmAreaLevel1(td::object_ &pedestrianInfo);

    bool isPedestrianInAlarmAreaLevel2(td::object_ &pedestrianInfo);

    void parseObjectInfos(td::objectInfo_t &curObjectInfo, float speed,bool cameraCovered);

    void parseEvent_level1(uint32_t curAlaramId, uint32_t curPedestrianTrend);

    void parseEvent_level2(uint32_t curAlaramId, uint32_t curPedestrianTrend);

    void parseEvent_level1_Of_Left(uint32_t curAlaramId, uint32_t curPedestrianTrend);

    bool isCameraBypass() const;

    void setCameraBypass(bool cameraBypass);


private:
    /* 报警间隔时间  1.5s */
    const __time_t PEDSESTRIAN_ALARM_INTERVAL_TIME = 1500;
    /* 报警区域扩大1米的时候  在画面中应该扩大的像素点距离 */
    const float ALARM_EXIT_AREA_DIST = 1.35;

    /* 相机ID */
    int curCameraId = -1;
    /* 相机是否被遮挡 */
    bool cameraCover = false;
    /* 相机是否被bypass */
    bool cameraBypass = false;
    /* 报警区域1内是否有人 */
    bool hasPedestrianInAlarmAreaLevel1 = false;
    /* 报警区域2内是否有人 */
    bool hasPedestrianInAlarmAreaLevel2 = false;
    /* 上次发送行人报警的时间 */
    __suseconds_t lastSendEventTime = 0;
    /* 报警事件信息 */
    AlarmEventInfo curAlarmEventInfo;
    /* 识别出来的信息整理好的结果 */
    G3DetectionResult detectionResult;

    /* 报警区域1的顶点 */
    VISPoint alarmAreaPointList_level1[6];
    /* 报警区域1的退出区域的顶点 */
    VISPoint alarmAreaPointList_level1_exit[6];

    /* 报警区域2的顶点 */
    VISPoint alarmAreaPointList_level2[6];
    /* 报警区域2的退出区域的顶点 */
    VISPoint alarmAreaPointList_level2_exit[6];

    /* 上一包结果中在报警区域1内的行人ID */
    std::vector<uint32_t> lastInAlarm1IdList;
    /* 上一包结果中在报警区域2内的行人ID */
    std::vector<uint32_t> lastInAlarm2IdList;

    CameraType curCameraType;


    /* 上次报警的行人ID */
    uint32_t lastAlaramId_level1 = 0xFFFFFFFF;
    uint32_t lastAlaramId_level2 = 0xFFFFFFFF;
    /* 上次报警的行人Y距离 */
    int lastAlaramYDistance_level1 = -1;
    int lastAlaramYDistance_level2 = -1;


    DetectDataCallback *curDetectDataCallback;


    /* 这个镜头是否被bypassle */
    bool isbypass = false;


};


#endif //VIS_G3_SOFTWARE_ALARMDECISION_BSD_H
