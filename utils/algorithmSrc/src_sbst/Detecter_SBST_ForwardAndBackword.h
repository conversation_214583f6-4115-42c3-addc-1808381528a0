//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/1/30.
//

#ifndef VIS_G3_SOFTWARE_DETECTER_SBST_FORWARDANDBACKWORD_H
#define VIS_G3_SOFTWARE_DETECTER_SBST_FORWARDANDBACKWORD_H

#include "DetectDataCallback.h"
#include <Poco/Runnable.h>
#include "AlarmDecision_SBST_Forward.h"
#include "XuRGAUtils.h"
#include "AlarmDecision_SBST_Backward.h"

namespace vis {
    class Detecter_SBST_ForwardAndBackword : public Poco::Runnable {
    public:
        Detecter_SBST_ForwardAndBackword();

        ~Detecter_SBST_ForwardAndBackword();

        /**
         * 初始化
         *
         * @param cameraId : 算法对应的相机ID
         * @param detectUnitManager ：DetectUnitManager的对象
         *
         * @return 结果     0：成功  其他：失败
         *
         * */
        int init(int cameraId, DetectDataCallback &detectDataCallback);

        /**
         * 获取当前车速
         *
         * @return 当前的车速
         */
        float getVelocity();


        bool isDetectOpen() const;

        __time_t getLastDetectTime() const;


        void run() override;



    private:

        DetectDataCallback *curDetectDataCallback;
        /* 当前相机ID */
        int curCameraId = -1;
        /* 此算法是否初始化成功了 */
        bool detectOpen = false;

        /* 上一帧图像解析成功的时间 */
        __time_t lastDetectTime = 0;
        AlarmDecision_SBST_Forward alarmDecisionSbstForward;
        AlarmDecision_SBST_Backward alarmDecisionSbstBackward;
        bool isforwardCamera = true;
        XuRGAUtils xuRgaUtils;

        Vehicle_RealtimeStatus curVehicleRealtimeStatus = {};

        /**
         * 获得忽略报警区域1
         * @return 忽略区域1的所有点
         */
        std::vector<cv::Point> getIgnoreAreaPoint1() const;
        /**
         * 获得忽略报警区域2
         * @return 忽略区域2的所有点
         */
        std::vector<cv::Point> getIgnoreAreaPoint2() const;

    };

} // vis
#endif //VIS_G3_SOFTWARE_DETECTER_SBST_FORWARDANDBACKWORD_H
