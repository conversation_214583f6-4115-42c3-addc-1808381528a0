//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/1/30.
//

#include "dasShareDefine.h"


#ifdef DAS_G3_APP_IN_OUT

#include <unistd.h>
#include "Detecter_SBST_ForwardAndBackword.h"

#endif

#include "XuString.h"
#include "XuTimeUtil.h"
#include "dasDetect.h"

using namespace cv;
using namespace std;
namespace vis {

    Detecter_SBST_ForwardAndBackword::Detecter_SBST_ForwardAndBackword() {

    }

    Detecter_SBST_ForwardAndBackword::~Detecter_SBST_ForwardAndBackword() {

    }

    int Detecter_SBST_ForwardAndBackword::init(int cameraId, DetectDataCallback &detectDataCallback) {
        curCameraId = cameraId;
        curDetectDataCallback = &detectDataCallback;
        CameraType curCameraType;
        G3_Configuration::getInstance().getCameraTypeInfoOfCamera(curCameraType, cameraId);
        isforwardCamera = (curCameraType.installPosition == INSTALL_POSITION_FRONT);
        if (isforwardCamera) {
            alarmDecisionSbstForward.init(cameraId, detectDataCallback);
        } else {
            alarmDecisionSbstBackward.init(cameraId, detectDataCallback);
        }

        return 0;
    }

    float Detecter_SBST_ForwardAndBackword::getVelocity() {
        return curVehicleRealtimeStatus.speed;
    }


    bool Detecter_SBST_ForwardAndBackword::isDetectOpen() const {
        return detectOpen;
    }

    __time_t Detecter_SBST_ForwardAndBackword::getLastDetectTime() const {
        return lastDetectTime;
    }

    void Detecter_SBST_ForwardAndBackword::run() {
        std::string pthreadName = "Forward_";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        int ret = -1;
        das::Detect *detect = new das::Detect();


        const int input_width = 1280;
        const int input_height = 720;

        das::objectInfo_t object_info;

        object_info.objects.clear();

        Mat srcimg = Mat::zeros(input_height, input_width, CV_8UC3);
        std::vector<cv::Point> ignoreArea1;
        std::vector<cv::Point> ignoreArea2;
        ignoreArea1 = getIgnoreAreaPoint1();
        ignoreArea2 = getIgnoreAreaPoint2();
        detect->init_yolov5seg(input_width, input_height,ignoreArea1,ignoreArea2);
        detectOpen = true;


        uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
        Mat yuvImg = Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);

        int frame_cnt = 0;
        /* 是否需要清理识别信息 */
        bool needclean = false;
        while (1) {
            frame_cnt++;



            ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData,curVehicleRealtimeStatus,&needclean);
            if (ret != 0) {
                usleep(5 * 1000);
                continue;
            }
//            printf("get yuv data success, cameraId=%d size=%d  \n", cameraYuvData.getCameraId(),cameraYuvData.getDataLen());


            xuRgaUtils.imageTransformation(1280, 720, xuRgaUtils.IMG_TYPE_NV21, cameraYuvData, 1280,
                                           720, xuRgaUtils.IMG_TYPE_BGR888, srcimg.data);
//            yuvImg.data = cameraYuvData.getCurYuvData();
//            cvtColor(yuvImg, srcimg, COLOR_YUV2BGR_NV12);




//            double BeginTime_alg = (double) cv::getTickCount();

            detect->detect_yolov5seg(srcimg, getVelocity());
            object_info.objects.clear();
            object_info.traffics.clear();
            object_info.lanes.clear();
            object_info = detect->getResult_yolov5seg();

//            double time2_alg = ((double) cv::getTickCount() - BeginTime_alg) / cv::getTickFrequency();
//            int time3_alg = (int) (time2_alg * 1000 + 0.5);
//            printf("$$$$$$$$$$$$$$$$$ total time %dms    cameraId:%d \n", time3_alg, curCameraId);
//            printf("\n");

            /* 这里交给报警决策判断一下 */
            if (isforwardCamera) {
                alarmDecisionSbstForward.parseObjectInfos(object_info, curVehicleRealtimeStatus.speed, curVehicleRealtimeStatus.turnL,
                                                          curVehicleRealtimeStatus.turnR,
                                                          curVehicleRealtimeStatus.frontDoor,
                                                          curVehicleRealtimeStatus.backDoor);
            } else {
                alarmDecisionSbstBackward.parseObjectInfos(object_info, curVehicleRealtimeStatus.speed, curVehicleRealtimeStatus.turnL,
                                                           curVehicleRealtimeStatus.turnR,
                                                           curVehicleRealtimeStatus.frontDoor,
                                                           curVehicleRealtimeStatus.backDoor);
            }

            lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();

        }


        delete detect;

        return;
    }

    std::vector<cv::Point> Detecter_SBST_ForwardAndBackword::getIgnoreAreaPoint1() const {
        G3_Configuration &conf = G3_Configuration::getInstance();
        std::vector<cv::Point> point;
        std::vector<VISPoint> vPoint;

        // 根据镜头id获取各自忽略的区域
        if (CAMERA_ID_1 == curCameraId) {
            vPoint = conf.getUndetectedAreaListCamera1()[0];
        } else if (CAMERA_ID_2 == curCameraId) {
            vPoint = conf.getUndetectedAreaListCamera2()[0];
        }

        // 转换格式
        for (size_t i = 0; i < vPoint.size(); ++i) {
            cv::Point cp;
            cp.x = vPoint[i].x;
            cp.y = vPoint[i].y;

            point.push_back(cp);
        }

        return point;
    }

    std::vector<cv::Point> Detecter_SBST_ForwardAndBackword::getIgnoreAreaPoint2() const {
        G3_Configuration &conf = G3_Configuration::getInstance();
        std::vector<cv::Point> point;
        std::vector<VISPoint> vPoint;

        // 根据镜头id获取各自忽略的区域
        if (CAMERA_ID_1 == curCameraId) {
            vPoint = conf.getUndetectedAreaListCamera1()[1];
        } else if (CAMERA_ID_2 == curCameraId) {
            vPoint = conf.getUndetectedAreaListCamera2()[1];
        }

        // 转换格式
        for (size_t i = 0; i < vPoint.size(); ++i) {
            cv::Point cp;
            cp.x = vPoint[i].x;
            cp.y = vPoint[i].y;

            point.push_back(cp);
        }

        return point;
    }

} // vis
