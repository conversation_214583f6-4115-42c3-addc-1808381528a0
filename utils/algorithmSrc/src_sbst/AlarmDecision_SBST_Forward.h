//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/1/30.
//

#ifndef VIS_G3_SOFTWARE_ALARMDECISION_SBST_FORWARD_H
#define VIS_G3_SOFTWARE_ALARMDECISION_SBST_FORWARD_H

#include "DetectDataCallback.h"

namespace vis {

    class AlarmDecision_SBST_Forward {
    public:
        AlarmDecision_SBST_Forward();

        ~AlarmDecision_SBST_Forward();

        /**
         * 初始化
         *
         * @param cameraId ： 当前算法对应的相机ID
         * @param detectDataCallback ： 时间回调的接口
         * @return
         */
        int init(int cameraId, DetectDataCallback &detectDataCallback);

        /**
         * 解析识别信息
         *
         * @param curObjectInfo ： 识别信息
         * @param speed ： 当前车速
         * @param turnL : 左转
         * @param turnR : 右转
         */
        void parseObjectInfos(das::objectInfo_t &curObjectInfo, float speed, bool turnL, bool turnR, bool frontDoor,
                              bool backDoor);

        /**
         *
         * 解析行人信息的方法
         *
         * @param pedestrianInfo ： 行人的框的信息
         * @param speed ： 当前车速
         */
        void parsePedestrianInfo(das::object_t &pedestrianInfo, float speed, bool turnL, bool turnR);


        /**
         * 获取行人报警区域的点
         */
        void getPedestrianAlarmArea(float speed, bool turnL, bool turnR);

        /**
          *
          * 解析车辆信息的方法
          *
          * @param pedestrianInfo ： 车辆的框的信息
          * @param speed ： 当前车速
          */
        void parseVehicleInfo(das::object_t &vehicleInfo, float speed,
                              bool turnL, bool turnR);

        /**
         * 获取车辆报警区域的点
         */
        void getVehicleAlarmArea(float speed = 0, bool turnL = false, bool turnR = false);

        /**
         * 检查下是否有行人的报警
         */
        void checkPedestrianEvent(int speed);

        /**
         * 检查下是否有车辆的报警
         */
        void checkVehicleEvent(int speed);

        /**
         * 检查下是否有车道线的报警
         */
        void checkLanelineEvent(das::alarmInfo_t &alarmInfo, int speed, bool turnL, bool turnR);

        /**
         *
         * 检查一个object是否在可行驶区域内
         *
         * @param object_tInfo ： 需要判断的object
         *
         * @return 是否在可行驶区域内
         */
        bool isInTravelableArea(das::object_t &object_tInfo);


    private:
        void setPedestrianAlramAreaLevel1(const std::vector<VISPoint> &points);

        void setPedestrianAlramAreaLevel2(const std::vector<VISPoint> &points);

        void setVehicleAlramAreaLevel1(const std::vector<VISPoint> &points);

        void setVehicleAlramAreaLevel2(const std::vector<VISPoint> &points);

        void pointConvertVectorToFixed(VISPoint dest[6], const std::vector<VISPoint> &src);

    private:

        DetectDataCallback *curDetectDataCallback;

        /* 相机ID */
        int curCameraId = -1;

        CameraType curCameraType;

        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;

        /* 相机是否被遮挡 */
        bool cameraCover = false;
        /* 相机是否被bypass */
        bool cameraBypass = false;


        /* 行人报警报警区域1的顶点 */
        VISPoint pedestrianAlarmAreaPointList_level1[6];
        /* 行人报警报警区域2的顶点 */
        VISPoint pedestrianAlarmAreaPointList_level2[6];
        /* 车辆报警报警区域1的顶点 */
        VISPoint vehicleAlarmAreaPointList_level1[6];
        /* 车辆报警报警区域2的顶点 */
        VISPoint vehicleAlarmAreaPointList_level2[6];

        /* 行人报警区域1内是否有人 */
        bool hasPedestrianInAlarmAreaLevel1 = false;
        /* 行人报警区域2内是否有人 */
        bool hasPedestrianInAlarmAreaLevel2 = false;
        /* 当前行人报警区域1的行人信息（在行人报警区域1内距离镜头最近的行人） */
        das::object_t curAlarmpedestrianInfo_level1;
        /* 当前行人报警区域2的行人信息（在行人报警区域2内距离镜头最近的行人） */
        das::object_t curAlarmpedestrianInfo_level2;


        /* 车辆报警区域1内是否有车 */
        bool hasVehicleInAlarmAreaLevel1 = false;
        /* 车辆报警区域2内是否有车 */
        bool hasVehicleInAlarmAreaLevel2 = false;
        /* 当前车辆报警区域1的车辆信息（在车辆报警区域1内距离镜头最近的车辆） */
        das::object_t curAlarmVehicleInfo_level1;
        /* 当前车辆报警区域2的车辆信息（在车辆报警区域2内距离镜头最近的车辆） */
        das::object_t curAlarmVehicleInfo_level2;


        AlarmEventInfo alarmEventInfo;


    };

} // vis

#endif //VIS_G3_SOFTWARE_ALARMDECISION_SBST_FORWARD_H
