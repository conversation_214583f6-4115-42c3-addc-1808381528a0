//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/2/3.
//

#include "AlarmDecision_SBST_Backward.h"

namespace vis {
    AlarmDecision_SBST_Backward::AlarmDecision_SBST_Backward() {

    }

    AlarmDecision_SBST_Backward::~AlarmDecision_SBST_Backward() {

    }

    int AlarmDecision_SBST_Backward::init(int cameraId, DetectDataCallback &detectDataCallback) {
        curCameraId = cameraId;
        curDetectDataCallback = &detectDataCallback;
        G3_Configuration::getInstance().getCameraTypeInfoOfCamera(curCameraType, cameraId);
        detectionResult.curCameraType = curCameraType;
        return 0;
    }

    void
    AlarmDecision_SBST_Backward::parseObjectInfos(das::objectInfo_t &curObjectInfo, float speed, bool turnL, bool turnR,
                                                  bool frontDoor, bool backDoor) {
        hasPedestrianInAlarmAreaLevel1 = false;
        hasPedestrianInAlarmAreaLevel2 = false;
        hasVehicleInLeftAlarmAreaLevel1 = false;
        hasVehicleInLeftAlarmAreaLevel2 = false;
        hasVehicleInRightAlarmAreaLevel1 = false;
        hasVehicleInRightAlarmAreaLevel2 = false;
        curAlarmpedestrianInfo_level1.mYDistance = 999999;
        curAlarmpedestrianInfo_level2.mYDistance = 999999;
        curLeftAlarmVehicleInfo_level1.mYDistance = 999999;
        curLeftAlarmVehicleInfo_level2.mYDistance = 999999;
        curRightAlarmVehicleInfo_level1.mYDistance = 999999;
        curRightAlarmVehicleInfo_level2.mYDistance = 999999;


        /* 由于结构体里面没有裸指针，故直接用={}重置就好了 */
        alarmEventInfo = {};
        detectionResult = {};
        detectionResult.curCameraType.copy(curCameraType);
        /* 看看是不是有遮挡 */
        cameraCover = curObjectInfo.alarmInfo.m_camera_covered_alarm;
        cameraBypass = false;

        detectionResult.bsdDetectionInfo.cameraCoverStatus.backward = cameraCover;
        detectionResult.bsdDetectionInfo.bypassStatus.backward = cameraBypass;
        detectionResult.speed = speed;

        /* 遍历拿到的object信息，把人跟车的框分别拿出去解析 */
        if (!curObjectInfo.objects.empty()) {
            for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {
                /* 把行人挑出来，给行人信息的识别类去识别 */
                if ((strcmp(curObjectInfo.objects[i].label, "person") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "cyclist") == 0)) {
                    parsePedestrianInfo(curObjectInfo.objects[i], speed, turnL, turnR);
                }

                /* 把车辆挑出来，给行人信息的识别类去识别 */
                if ((strcmp(curObjectInfo.objects[i].label, "car") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "bus") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "truck") == 0)) {
                    parseVehicleInfo(curObjectInfo.objects[i], speed, turnL, turnR);
                }
                /* motorcyclist需要特殊处理 */

            }

        }

        /* 整理解析出来的数据，看看是不是需要发送报警  先看是不是有车门打开了  打开了就不用报警  没打开才需要判断报警 */
        if (!frontDoor && !backDoor) {
            /* 先检查行人的事件发送 */
            checkPedestrianEvent(speed, turnL, turnR);
            /* 再检查下车辆的事件发送 */
            checkVehicleEvent(speed, turnL, turnR);
//        /* 再检查下车道线的事件发送 */
//        checkLanelineEvent(speed,turnL,turnR);
        }

        /* 把识别信息发送出去 */
        curDetectDataCallback->onGetObjectInfo_Adas(curObjectInfo, detectionResult);

    }

    void AlarmDecision_SBST_Backward::parsePedestrianInfo(das::object_t &pedestrianInfo, float speed, bool turnL,
                                                          bool turnR) {
        /* 判断一下是否在可行驶区域内  不在的话就不用判断报警什么的 */
        if (isInTravelableArea(pedestrianInfo)) {
            /* 先获取一下行人的报警区域 */
            getPedestrianAlarmArea(speed, turnL, turnR);
            /* 判断一下行人报警区域1的点是否有效（如果前两个点都是0，那么就说无效的）*/
            if ((pedestrianAlarmAreaPointList_level1[0].x != 0) || (pedestrianAlarmAreaPointList_level1[0].y != 0) ||
                (pedestrianAlarmAreaPointList_level1[1].x != 0) || (pedestrianAlarmAreaPointList_level1[1].y != 0)) {
                /* 判断一下这个人是否在行人的报警区域1里 */
                VISPoint pedestrianPOints[4];
                bool isIn = false;

                pedestrianPOints[0].x = pedestrianInfo.mLeft;
                pedestrianPOints[0].y = pedestrianInfo.mTop;

                pedestrianPOints[1].x = pedestrianInfo.mRight;
                pedestrianPOints[1].y = pedestrianInfo.mTop;

                pedestrianPOints[2].x = pedestrianInfo.mRight;
                pedestrianPOints[2].y = pedestrianInfo.mBottom;

                pedestrianPOints[3].x = pedestrianInfo.mLeft;
                pedestrianPOints[3].y = pedestrianInfo.mBottom;


                /* 判断下是否在报警区域内 */
                isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                                 pedestrianAlarmAreaPointList_level1,
                                                                                                 6);
                if(isIn){
                    /* 把label改一下 */
                    std::string newlabel;
                    newlabel.append("A1-");
                    newlabel.append(pedestrianInfo.label);
                    memset(pedestrianInfo.label,0x00, sizeof(pedestrianInfo.label));
                    memcpy(pedestrianInfo.label,newlabel.c_str(), newlabel.size());
                }
                /* 把是否有人的结果存起来结果存起来 */
                hasPedestrianInAlarmAreaLevel1 = (hasPedestrianInAlarmAreaLevel1 | isIn);
                /* 如果这个人离得跟近，那么这个人作为报警目标 */
                if (isIn && (curAlarmpedestrianInfo_level1.mYDistance >= pedestrianInfo.mYDistance)) {
                    curAlarmpedestrianInfo_level1 = pedestrianInfo;


                }

            }


            /* 判断一下行人报警区域2的点是否有效（如果前两个点都是0，那么就说无效的）*/
            if ((pedestrianAlarmAreaPointList_level2[0].x != 0) || (pedestrianAlarmAreaPointList_level2[0].y != 0) ||
                (pedestrianAlarmAreaPointList_level2[1].x != 0) || (pedestrianAlarmAreaPointList_level2[1].y != 0)) {
                /* 判断一下这个人是否在行人的报警区域2里 */
                VISPoint pedestrianPOints[4];
                bool isIn = false;

                pedestrianPOints[0].x = pedestrianInfo.mLeft;
                pedestrianPOints[0].y = pedestrianInfo.mTop;

                pedestrianPOints[1].x = pedestrianInfo.mRight;
                pedestrianPOints[1].y = pedestrianInfo.mTop;

                pedestrianPOints[2].x = pedestrianInfo.mRight;
                pedestrianPOints[2].y = pedestrianInfo.mBottom;

                pedestrianPOints[3].x = pedestrianInfo.mLeft;
                pedestrianPOints[3].y = pedestrianInfo.mBottom;


                /* 判断下是否在报警区域内 */
                isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                                 pedestrianAlarmAreaPointList_level2,
                                                                                                 6);
                if(isIn){
                    /* 把label改一下 */
                    std::string newlabel;
                    newlabel.append("A2-");
                    newlabel.append(pedestrianInfo.label);
                    memset(pedestrianInfo.label,0x00, sizeof(pedestrianInfo.label));
                    memcpy(pedestrianInfo.label,newlabel.c_str(), newlabel.size());
                }

                /* 把是否有人的结果存起来结果存起来 */
                hasPedestrianInAlarmAreaLevel2 = (hasPedestrianInAlarmAreaLevel2 | isIn);
                /* 如果这个人离得跟近，那么这个人作为报警目标 */
                if (isIn && (curAlarmpedestrianInfo_level2.mYDistance >= pedestrianInfo.mYDistance)) {
                    curAlarmpedestrianInfo_level2 = pedestrianInfo;

                }
            }
        }
    }

    void AlarmDecision_SBST_Backward::getPedestrianAlarmArea(float speed, bool turnL, bool turnR) {
        int minSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMinSpeed();
        int maxSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMaxSpeed();
        // 如果速度处于[1,30]区间，且打了转向灯，则使用转向时专用的报警区间
        if (speed >= minSpeed && speed <= maxSpeed && (turnR || turnL)) {
            // 如果打左转向灯，则使用左转向灯的报警区间
            if (turnL) {
                if (CAMERA_ID_1 == curCameraId) {
                    setPedestrianAlramAreaLevel1(
                            G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera1TurnleftAngle1Level1());
                    setPedestrianAlramAreaLevel2(
                            G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera1TurnleftAngle1Level2());
                } else if (CAMERA_ID_2 == curCameraId) {
                    setPedestrianAlramAreaLevel1(
                            G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera2TurnleftAngle1Level1());
                    setPedestrianAlramAreaLevel2(
                            G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera2TurnleftAngle1Level2());
                }
            }
                // 如果打右转向灯，则使用右转向灯的报警区间
            else {
                if (CAMERA_ID_1 == curCameraId) {
                    setPedestrianAlramAreaLevel1(
                            G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera1TurnrightAngle1Level1());
                    setPedestrianAlramAreaLevel2(
                            G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera1TurnrightAngle1Level2());
                } else if (CAMERA_ID_2 == curCameraId) {
                    setPedestrianAlramAreaLevel1(
                            G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera2TurnrightAngle1Level1());
                    setPedestrianAlramAreaLevel2(
                            G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera2TurnrightAngle1Level2());
                }
            }
        } else {
            switch (curCameraId) {
                case 0: {
                    /* 设置报警区域1的点 */
                    pedestrianAlarmAreaPointList_level1[0].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[0].x;
                    pedestrianAlarmAreaPointList_level1[0].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[0].y;
                    pedestrianAlarmAreaPointList_level1[1].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[1].x;
                    pedestrianAlarmAreaPointList_level1[1].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[1].y;
                    pedestrianAlarmAreaPointList_level1[2].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[2].x;
                    pedestrianAlarmAreaPointList_level1[2].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[2].y;
                    pedestrianAlarmAreaPointList_level1[3].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[3].x;
                    pedestrianAlarmAreaPointList_level1[3].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[3].y;
                    pedestrianAlarmAreaPointList_level1[4].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[4].x;
                    pedestrianAlarmAreaPointList_level1[4].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[4].y;
                    pedestrianAlarmAreaPointList_level1[5].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[5].x;
                    pedestrianAlarmAreaPointList_level1[5].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1()[5].y;

                    /* 设置报警区域2的点 */
                    pedestrianAlarmAreaPointList_level2[0].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[0].x;
                    pedestrianAlarmAreaPointList_level2[0].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[0].y;
                    pedestrianAlarmAreaPointList_level2[1].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[1].x;
                    pedestrianAlarmAreaPointList_level2[1].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[1].y;
                    pedestrianAlarmAreaPointList_level2[2].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[2].x;
                    pedestrianAlarmAreaPointList_level2[2].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[2].y;
                    pedestrianAlarmAreaPointList_level2[3].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[3].x;
                    pedestrianAlarmAreaPointList_level2[3].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[3].y;
                    pedestrianAlarmAreaPointList_level2[4].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[4].x;
                    pedestrianAlarmAreaPointList_level2[4].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[4].y;
                    pedestrianAlarmAreaPointList_level2[5].x = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[5].x;
                    pedestrianAlarmAreaPointList_level2[5].y = G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2()[5].y;

                }
                    break;

                case 1: {
                    pedestrianAlarmAreaPointList_level1[0].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[0].x;
                    pedestrianAlarmAreaPointList_level1[0].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[0].y;
                    pedestrianAlarmAreaPointList_level1[1].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[1].x;
                    pedestrianAlarmAreaPointList_level1[1].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[1].y;
                    pedestrianAlarmAreaPointList_level1[2].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[2].x;
                    pedestrianAlarmAreaPointList_level1[2].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[2].y;
                    pedestrianAlarmAreaPointList_level1[3].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[3].x;
                    pedestrianAlarmAreaPointList_level1[3].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[3].y;
                    pedestrianAlarmAreaPointList_level1[4].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[4].x;
                    pedestrianAlarmAreaPointList_level1[4].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[4].y;
                    pedestrianAlarmAreaPointList_level1[5].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[5].x;
                    pedestrianAlarmAreaPointList_level1[5].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1()[5].y;
                    /* 设置报警区域2的点 */
                    pedestrianAlarmAreaPointList_level2[0].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[0].x;
                    pedestrianAlarmAreaPointList_level2[0].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[0].y;
                    pedestrianAlarmAreaPointList_level2[1].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[1].x;
                    pedestrianAlarmAreaPointList_level2[1].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[1].y;
                    pedestrianAlarmAreaPointList_level2[2].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[2].x;
                    pedestrianAlarmAreaPointList_level2[2].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[2].y;
                    pedestrianAlarmAreaPointList_level2[3].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[3].x;
                    pedestrianAlarmAreaPointList_level2[3].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[3].y;
                    pedestrianAlarmAreaPointList_level2[4].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[4].x;
                    pedestrianAlarmAreaPointList_level2[4].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[4].y;
                    pedestrianAlarmAreaPointList_level2[5].x = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[5].x;
                    pedestrianAlarmAreaPointList_level2[5].y = G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2()[5].y;
                }
                    break;
            }
        }
    }

    void
    AlarmDecision_SBST_Backward::parseVehicleInfo(das::object_t &vehicleInfo, float speed, bool turnL, bool turnR) {
        /* 判断一下是否在可行驶区域内  不在的话就不用判断报警什么的 */
        if (isInTravelableArea(vehicleInfo)) {
            /* 先获取一下车辆的报警区域 */
            getVehicleAlarmArea(speed, turnL, turnR);

            /* 判断一下左边车辆报警区域1的点是否有效（如果前两个点都是0，那么就说无效的）*/
            if ((vehicleAlarmAreaPointList_level1_left[0].x == 0) &&
                (vehicleAlarmAreaPointList_level1_left[0].y == 0) &&
                (vehicleAlarmAreaPointList_level1_left[1].x == 0) &&
                (vehicleAlarmAreaPointList_level1_left[1].y == 0)) {
                /* 判断一下这辆车是否在车辆的报警区域1里 */
                VISPoint pedestrianPOints[4];
                bool isIn = false;

                pedestrianPOints[0].x = vehicleInfo.mLeft;
                pedestrianPOints[0].y = vehicleInfo.mTop;

                pedestrianPOints[1].x = vehicleInfo.mRight;
                pedestrianPOints[1].y = vehicleInfo.mTop;

                pedestrianPOints[2].x = vehicleInfo.mRight;
                pedestrianPOints[2].y = vehicleInfo.mBottom;

                pedestrianPOints[3].x = vehicleInfo.mLeft;
                pedestrianPOints[3].y = vehicleInfo.mBottom;


                /* 判断下是否在报警区域内 */
                isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                                 vehicleAlarmAreaPointList_level1_left,
                                                                                                 6);

                if(isIn){
                    /* 把label改一下 */
                    std::string newlabel;
                    newlabel.append("A1-");
                    newlabel.append(vehicleInfo.label);
                    memset(vehicleInfo.label,0x00, sizeof(vehicleInfo.label));
                    memcpy(vehicleInfo.label,newlabel.c_str(), newlabel.size());
                }

                /* 把报警区域是否有车的结果存起来结果存起来 */
                hasVehicleInLeftAlarmAreaLevel1 = (hasVehicleInLeftAlarmAreaLevel1 | isIn);
                /* 如果这辆车离得跟近，那么这辆车作为报警目标 */
                if (isIn && (curLeftAlarmVehicleInfo_level1.mYDistance >= vehicleInfo.mYDistance)) {
                    curLeftAlarmVehicleInfo_level1 = vehicleInfo;
                }

            }


            /* 判断一下左边车辆报警区域2的点是否有效（如果前两个点都是0，那么就说无效的）*/
            if ((vehicleAlarmAreaPointList_level2_left[0].x == 0) &&
                (vehicleAlarmAreaPointList_level2_left[0].y == 0) &&
                (vehicleAlarmAreaPointList_level2_left[1].x == 0) &&
                (vehicleAlarmAreaPointList_level2_left[1].y == 0)) {
                /* 判断一下这辆车是否在车辆的报警区域2里 */
                VISPoint pedestrianPOints[4];
                bool isIn = false;

                pedestrianPOints[0].x = vehicleInfo.mLeft;
                pedestrianPOints[0].y = vehicleInfo.mTop;

                pedestrianPOints[1].x = vehicleInfo.mRight;
                pedestrianPOints[1].y = vehicleInfo.mTop;

                pedestrianPOints[2].x = vehicleInfo.mRight;
                pedestrianPOints[2].y = vehicleInfo.mBottom;

                pedestrianPOints[3].x = vehicleInfo.mLeft;
                pedestrianPOints[3].y = vehicleInfo.mBottom;


                /* 判断下是否在报警区域内 */
                isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                                 vehicleAlarmAreaPointList_level2_left,
                                                                                                 6);
                if(isIn){
                    /* 把label改一下 */
                    std::string newlabel;
                    newlabel.append("A2-");
                    newlabel.append(vehicleInfo.label);
                    memset(vehicleInfo.label,0x00, sizeof(vehicleInfo.label));
                    memcpy(vehicleInfo.label,newlabel.c_str(), newlabel.size());
                }
                /* 把报警区域是否有车的结果存起来结果存起来 */
                hasVehicleInLeftAlarmAreaLevel2 = (hasVehicleInLeftAlarmAreaLevel2 | isIn);
                /* 如果这辆车离得跟近，那么这辆车作为报警目标 */
                if (isIn && (curLeftAlarmVehicleInfo_level2.mYDistance >= vehicleInfo.mYDistance)) {
                    curLeftAlarmVehicleInfo_level2 = vehicleInfo;

                }

            }

            /* 判断一下右边车辆报警区域1的点是否有效（如果前两个点都是0，那么就说无效的）*/
            if ((vehicleAlarmAreaPointList_level1_right[0].x == 0) &&
                (vehicleAlarmAreaPointList_level1_right[0].y == 0) &&
                (vehicleAlarmAreaPointList_level1_right[1].x == 0) &&
                (vehicleAlarmAreaPointList_level1_right[1].y == 0)) {
                /* 判断一下这辆车是否在车辆的报警区域1里 */
                VISPoint pedestrianPOints[4];
                bool isIn = false;

                pedestrianPOints[0].x = vehicleInfo.mLeft;
                pedestrianPOints[0].y = vehicleInfo.mTop;

                pedestrianPOints[1].x = vehicleInfo.mRight;
                pedestrianPOints[1].y = vehicleInfo.mTop;

                pedestrianPOints[2].x = vehicleInfo.mRight;
                pedestrianPOints[2].y = vehicleInfo.mBottom;

                pedestrianPOints[3].x = vehicleInfo.mLeft;
                pedestrianPOints[3].y = vehicleInfo.mBottom;


                /* 判断下是否在报警区域内 */
                isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                                 vehicleAlarmAreaPointList_level1_right,
                                                                                                 6);
                if(isIn){
                    /* 把label改一下 */
                    std::string newlabel;
                    newlabel.append("A1-");
                    newlabel.append(vehicleInfo.label);
                    memset(vehicleInfo.label,0x00, sizeof(vehicleInfo.label));
                    memcpy(vehicleInfo.label,newlabel.c_str(), newlabel.size());
                }
                /* 把报警区域是否有车的结果存起来结果存起来 */
                hasVehicleInRightAlarmAreaLevel1 = (hasVehicleInRightAlarmAreaLevel1 | isIn);
                /* 如果这辆车离得跟近，那么这辆车作为报警目标 */
                if (isIn && (curRightAlarmVehicleInfo_level1.mYDistance >= vehicleInfo.mYDistance)) {
                    curRightAlarmVehicleInfo_level1 = vehicleInfo;

                }

            }

            /* 判断一下右边车辆报警区域2的点是否有效（如果前两个点都是0，那么就说无效的）*/
            if ((vehicleAlarmAreaPointList_level2_right[0].x == 0) &&
                (vehicleAlarmAreaPointList_level2_right[0].y == 0) &&
                (vehicleAlarmAreaPointList_level2_right[1].x == 0) &&
                (vehicleAlarmAreaPointList_level2_right[1].y == 0)) {
                /* 判断一下这辆车是否在车辆的报警区域2里 */
                VISPoint pedestrianPOints[4];
                bool isIn = false;

                pedestrianPOints[0].x = vehicleInfo.mLeft;
                pedestrianPOints[0].y = vehicleInfo.mTop;

                pedestrianPOints[1].x = vehicleInfo.mRight;
                pedestrianPOints[1].y = vehicleInfo.mTop;

                pedestrianPOints[2].x = vehicleInfo.mRight;
                pedestrianPOints[2].y = vehicleInfo.mBottom;

                pedestrianPOints[3].x = vehicleInfo.mLeft;
                pedestrianPOints[3].y = vehicleInfo.mBottom;


                /* 判断下是否在报警区域内 */
                isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                                 vehicleAlarmAreaPointList_level2_right,
                                                                                                 6);
                if(isIn){
                    /* 把label改一下 */
                    std::string newlabel;
                    newlabel.append("A2-");
                    newlabel.append(vehicleInfo.label);
                    memset(vehicleInfo.label,0x00, sizeof(vehicleInfo.label));
                    memcpy(vehicleInfo.label,newlabel.c_str(), newlabel.size());
                }
                /* 把报警区域是否有车的结果存起来结果存起来 */
                hasVehicleInRightAlarmAreaLevel2 = (hasVehicleInRightAlarmAreaLevel2 | isIn);
                /* 如果这辆车离得跟近，那么这辆车作为报警目标 */
                if (isIn && (curRightAlarmVehicleInfo_level2.mYDistance >= vehicleInfo.mYDistance)) {
                    curRightAlarmVehicleInfo_level2 = vehicleInfo;

                }

            }

        } else { ; //not to do
        }
    }

    void AlarmDecision_SBST_Backward::getVehicleAlarmArea(float speed, bool turnL, bool turnR) {
        /* 设置左边报警区域1的点 */
        vehicleAlarmAreaPointList_level1_left[0].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[0].x;
        vehicleAlarmAreaPointList_level1_left[0].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[0].y;
        vehicleAlarmAreaPointList_level1_left[1].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[1].x;
        vehicleAlarmAreaPointList_level1_left[1].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[1].y;
        vehicleAlarmAreaPointList_level1_left[2].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[2].x;
        vehicleAlarmAreaPointList_level1_left[2].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[2].y;
        vehicleAlarmAreaPointList_level1_left[3].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[3].x;
        vehicleAlarmAreaPointList_level1_left[3].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[3].y;
        vehicleAlarmAreaPointList_level1_left[4].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[4].x;
        vehicleAlarmAreaPointList_level1_left[4].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[4].y;
        vehicleAlarmAreaPointList_level1_left[5].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[5].x;
        vehicleAlarmAreaPointList_level1_left[5].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left()[5].y;

        /* 设置左边报警区域2的点 */
        vehicleAlarmAreaPointList_level2_left[0].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[0].x;
        vehicleAlarmAreaPointList_level2_left[0].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[0].y;
        vehicleAlarmAreaPointList_level2_left[1].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[1].x;
        vehicleAlarmAreaPointList_level2_left[1].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[1].y;
        vehicleAlarmAreaPointList_level2_left[2].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[2].x;
        vehicleAlarmAreaPointList_level2_left[2].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[2].y;
        vehicleAlarmAreaPointList_level2_left[3].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[3].x;
        vehicleAlarmAreaPointList_level2_left[3].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[3].y;
        vehicleAlarmAreaPointList_level2_left[4].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[4].x;
        vehicleAlarmAreaPointList_level2_left[4].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[4].y;
        vehicleAlarmAreaPointList_level2_left[5].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[5].x;
        vehicleAlarmAreaPointList_level2_left[5].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left()[5].y;

        /* 设置右边报警区域1的点 */
        vehicleAlarmAreaPointList_level1_right[0].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[0].x;
        vehicleAlarmAreaPointList_level1_right[0].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[0].y;
        vehicleAlarmAreaPointList_level1_right[1].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[1].x;
        vehicleAlarmAreaPointList_level1_right[1].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[1].y;
        vehicleAlarmAreaPointList_level1_right[2].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[2].x;
        vehicleAlarmAreaPointList_level1_right[2].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[2].y;
        vehicleAlarmAreaPointList_level1_right[3].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[3].x;
        vehicleAlarmAreaPointList_level1_right[3].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[3].y;
        vehicleAlarmAreaPointList_level1_right[4].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[4].x;
        vehicleAlarmAreaPointList_level1_right[4].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[4].y;
        vehicleAlarmAreaPointList_level1_right[5].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[5].x;
        vehicleAlarmAreaPointList_level1_right[5].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right()[5].y;
        /* 设置左边报警区域2的点 */
        vehicleAlarmAreaPointList_level2_right[0].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[0].x;
        vehicleAlarmAreaPointList_level2_right[0].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[0].y;
        vehicleAlarmAreaPointList_level2_right[1].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[1].x;
        vehicleAlarmAreaPointList_level2_right[1].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[1].y;
        vehicleAlarmAreaPointList_level2_right[2].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[2].x;
        vehicleAlarmAreaPointList_level2_right[2].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[2].y;
        vehicleAlarmAreaPointList_level2_right[3].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[3].x;
        vehicleAlarmAreaPointList_level2_right[3].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[3].y;
        vehicleAlarmAreaPointList_level2_right[4].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[4].x;
        vehicleAlarmAreaPointList_level2_right[4].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[4].y;
        vehicleAlarmAreaPointList_level2_right[5].x = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[5].x;
        vehicleAlarmAreaPointList_level2_right[5].y = G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right()[5].y;
    }

    bool AlarmDecision_SBST_Backward::isInTravelableArea(das::object_t &object_tInfo) {
        return object_tInfo.mIsInsideRoad;
    }

    void AlarmDecision_SBST_Backward::checkPedestrianEvent(int speed, bool turnL, bool turnR) {
        /* 先判断下是否在行人1级报警的车速区间内 */
        if ((speed >= G3_Configuration::getInstance().getSbstSidewardPedestrianMinAlramSpeedLevel1()) &&
            (speed <= G3_Configuration::getInstance().getSbstSidewardPedestrianMaxAlramSpeedLevel1())) {
            /* 判断下行人的1级报警区域内是否有行人 有人直接报 */
            if (hasPedestrianInAlarmAreaLevel1) {
                int eventCode = EVENT_UNKNOW;
                alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level1 = true;
                eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                alarmEventInfo.bsdAlarmInfo.alarmPedestrianId = curAlarmpedestrianInfo_level1.mId;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            }

        } else { ; //not to do
        }

        /* 先判断下是否在行人2级报警的车速区间内 */
        if ((speed >= G3_Configuration::getInstance().getSbstSidewardPedestrianMinAlramSpeedLevel2()) &&
            (speed <= G3_Configuration::getInstance().getSbstSidewardPedestrianMaxAlramSpeedLevel2())) {
            /* 判断下行人的2级报警区域内是否有行人 有人直接报 */
            if (hasPedestrianInAlarmAreaLevel2) {
                int eventCode = EVENT_UNKNOW;
                alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level2 = true;
                eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                alarmEventInfo.bsdAlarmInfo.alarmPedestrianId = curAlarmpedestrianInfo_level2.mId;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            }

        } else { ; //not to do
        }
    }

    void AlarmDecision_SBST_Backward::checkVehicleEvent(int speed, bool turnL, bool turnR) {
        /* 车辆左边一级     先判断下是否在车辆1级报警的车速区间内 */
        if ((speed >= G3_Configuration::getInstance().getSbstSidewardVehicleMinAlramSpeedLevel1()) &&
            (speed <= G3_Configuration::getInstance().getSbstSidewardVehicleMaxAlramSpeedLevel1())) {
            /* 判断下转向灯  如果设置了需要依据转向灯  那么就需要有打转向灯才报  如果设置不需要 那么就不判断转向灯 */
            if ((G3_Configuration::getInstance().getSbstSidewardAlarmNeedTurnLight() && (turnL || turnR)) ||
                (!G3_Configuration::getInstance().getSbstSidewardAlarmNeedTurnLight())) {
                /* 判断下车辆的左边1级报警区域内是否有车辆 有人直接报 */
                if (hasVehicleInLeftAlarmAreaLevel1) {
                    alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1;
                    detectionResult.bsdDetectionInfo.vehicleStatus.left_level1 = 1;
                    alarmEventInfo.bsdAlarmInfo.alarmVehicleId = curLeftAlarmVehicleInfo_level1.mId;
                    alarmEventInfo.bsdAlarmInfo.yDistance = curLeftAlarmVehicleInfo_level1.mYDistance;
                    curDetectDataCallback->onGetDetectionEvent(curCameraType, EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1, alarmEventInfo);
                }

                /* 判断下车辆的右边1级报警区域内是否有车辆 有人直接报 */
                if (hasVehicleInRightAlarmAreaLevel1) {
                    alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
                    detectionResult.bsdDetectionInfo.vehicleStatus.right_level1 = true;
                    alarmEventInfo.bsdAlarmInfo.alarmVehicleId = curRightAlarmVehicleInfo_level1.mId;
                    alarmEventInfo.bsdAlarmInfo.yDistance = curRightAlarmVehicleInfo_level1.mYDistance;
                    curDetectDataCallback->onGetDetectionEvent(curCameraType, EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1, alarmEventInfo);
                }
            }


        } else { ; //not to do
        }

        /* 车辆左边二级     先判断下是否在车辆2级报警的车速区间内 */
        if ((speed >= G3_Configuration::getInstance().getSbstSidewardPedestrianMinAlramSpeedLevel2()) &&
            (speed <= G3_Configuration::getInstance().getSbstSidewardPedestrianMaxAlramSpeedLevel2())) {
            /* 判断下转向灯  如果设置了需要依据转向灯  那么就需要有打转向灯才报  如果设置不需要 那么就不判断转向灯 */
            if ((G3_Configuration::getInstance().getSbstSidewardAlarmNeedTurnLight() && (turnL || turnR)) ||
                (!G3_Configuration::getInstance().getSbstSidewardAlarmNeedTurnLight())) {
                /* 判断下车辆的左边2级报警区域内是否有车辆 有人直接报 */
                if (hasVehicleInLeftAlarmAreaLevel2) {
                    alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2;
                    detectionResult.bsdDetectionInfo.vehicleStatus.left_level2 = true;
                    alarmEventInfo.bsdAlarmInfo.alarmVehicleId = curLeftAlarmVehicleInfo_level2.mId;
                    alarmEventInfo.bsdAlarmInfo.yDistance = curLeftAlarmVehicleInfo_level2.mYDistance;
                    curDetectDataCallback->onGetDetectionEvent(curCameraType, EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2, alarmEventInfo);
                }

                /* 判断下车辆的右边2级报警区域内是否有车辆 有人直接报 */
                if (hasVehicleInRightAlarmAreaLevel2) {
                    alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
                    detectionResult.bsdDetectionInfo.vehicleStatus.right_level2 = true;
                    alarmEventInfo.bsdAlarmInfo.alarmVehicleId = curRightAlarmVehicleInfo_level2.mId;
                    alarmEventInfo.bsdAlarmInfo.yDistance = curRightAlarmVehicleInfo_level2.mYDistance;
                    curDetectDataCallback->onGetDetectionEvent(curCameraType, EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2, alarmEventInfo);
                }
            }


        } else { ; //not to do
        }
    }

    /**
     * 把变长的point转为定长的point
     * @param dest 定长的point数组
     * @param src 变长的point数组
     */
    void AlarmDecision_SBST_Backward::pointConvertVectorToFixed(
            VISPoint *dest, const std::vector<VISPoint> &src) {
        std::size_t min = 0;
        min = std::min(6u, src.size());

        for (std::size_t i = 0; i < min; ++i) {
            dest[i].x = src[i].x;
            dest[i].y = src[i].y;
        }

    }

    /**
     * 设置行人一级报警的框
     * @param points 报警框的点
     */
    void AlarmDecision_SBST_Backward::setPedestrianAlramAreaLevel1(
            const std::vector<VISPoint> &points) {
        pointConvertVectorToFixed(pedestrianAlarmAreaPointList_level1, points);
    }

    /**
     * 设置行人二级报警的框
     * @param points 报警框的点
     */
    void AlarmDecision_SBST_Backward::setPedestrianAlramAreaLevel2(
            const std::vector<VISPoint> &points) {
        pointConvertVectorToFixed(pedestrianAlarmAreaPointList_level2, points);
    }

    /**
     * 设置车辆左一级报警的框
     * @param points 报警框的点
     */
    void AlarmDecision_SBST_Backward::setVehicleAlramAreaLevel1Left(
            const std::vector<VISPoint> &points) {
        pointConvertVectorToFixed(vehicleAlarmAreaPointList_level1_left, points);
    }

    /**
     * 设置车辆左二级报警的框
     * @param points 报警框的点
     */
    void AlarmDecision_SBST_Backward::setVehicleAlramAreaLevel2Left(
            const std::vector<VISPoint> &points) {
        pointConvertVectorToFixed(vehicleAlarmAreaPointList_level2_left, points);
    }

    /**
     * 设置车辆右一级报警的框
     * @param points 报警框的点
     */
    void AlarmDecision_SBST_Backward::setVehicleAlramAreaLevel1Right(
            const std::vector<VISPoint> &points) {
        pointConvertVectorToFixed(vehicleAlarmAreaPointList_level1_right, points);
    }

    /**
     * 设置车辆右一级报警的框
     * @param points 报警框的点
     */
    void AlarmDecision_SBST_Backward::setVehicleAlramAreaLevel2Right(
            const std::vector<VISPoint> &points) {
        pointConvertVectorToFixed(vehicleAlarmAreaPointList_level2_right, points);
    }

} // vis
