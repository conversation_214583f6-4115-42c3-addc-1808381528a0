// STD项目 20240320 融合第一版本
// STD项目 202401120 融合第一版本
// STD项目 20250320 测试第伞版本
// STD项目 20250430 德国马来西亚第一版本
#include "std_run.h"
#include "std_resnet18.h"
#include "std_get_alarm.h"
#include "std_yolov5s.h"
#include "std_dsmini.h"
#include <iostream>
#include <opencv2/core/types.hpp>
#include <ostream>
#include <thread>
#include <unistd.h>
// #include "transit.h"
#include "detecter_list/VisDetecter.h"
#include "utils/XuTimeUtil.h"



// extern dsm_std::DSMInfo alarm_stdinfo;
extern dsm_std::DSMInfo alarm_stdinfo;
// extern cv::dnn::Net resnet18;
extern dsm_std::globals globalstd;

bool dsm_std::cmpy(cv::Point const &a, cv::Point const &b)
{
	return a.y > b.y;
	// return a.y < b.y;
}

std::string dsm_std::getStatusName(dsm_std::Status status)
{
	switch (status)
	{
	case dsm_std::Status::Begin:
		return "Begin";
	case dsm_std::Status::Running:
		return "Running";
	case dsm_std::Status::End:
		return "End";
	case dsm_std::Status::Freeze:
		return "Freeze";
	case dsm_std::Status::Worthless:
		return "Worthless";
	default:
		return "Worthless";
	}
}

// 6类  0eyec 1eyeo  2smoke 3nosmoke 4safebelt 5nosafebelt   盲点和分神驾驶检测
int dsm_std::Kernelstd::runs_detector(cv::Mat beltimg, cv::Mat back_frame, dsm_std::YOLO5S yolo_model, DSMInfo &alarm_stdinfo, AlarmBox &box, int std_speed, dsm_std::ExternalParam exterIOparam)
{
	Kernelstd collmap;
	try
	{
		// 识别人脸特征
		dsm_std::face_message face_roi_info; // 人脸信息以及ROI截取结果
		face_roi_info = yolo_model.detect_face(back_frame,beltimg);
		
 printf("***********run_detector in line %d  \n", __LINE__);
		std::cout << "std:-------------------------------------------------[debug]face_roi_info.state：" <<face_roi_info.state << std::endl;
		if (face_roi_info.state != 4)
		{
		//[1]同步执行识别程序
		int ret = 0;
		std::vector<int> allresult_map(9);
		
		// 增加异常打断的清空操作，（此处仅处理全局打断，倒车或者其它异常时，不进行任何类别识别）。
		if( (face_roi_info.state == 2)||(exterIOparam.back_gear == true))
		{
			std::cout << "std:------------------------------------------------------------------------------------------------未检测到人脸--------------------   back_frame： " << back_frame.size() << std::endl;
			alarm_stdinfo.face_angle = 0;
			// collmap.superposition.clear();
			// alarm_stdinfo.face_point.clear();
			alarm_stdinfo.face_point[0]= cv::Point(0,0); 
			alarm_stdinfo.face_point[1]= cv::Point(0,0); 
            // alarm_stdinfo.five_point.clear();
			
			 //[9]离岗检测  角度阈值较大 lookaround_mode  代表角度
			if ((globalstd.facemissing_button == true)  &&  (std_speed >= globalstd.facemissing_speed)) // 离岗检测
			{
				allresult_map = {0, 0, 0, 0, 0, 0,0,0,1};
				// allresult_map = {0, 0, 0, 0, 0, 0, 1, 0, 0};
			}
			else
			{
				allresult_map = {0, 0, 0, 0, 0, 0, 0, 0, 0};
			}
			
		}
		else
		{
			alarm_stdinfo.face_angle = face_roi_info.face_angle[0];
			std::cout << "std:-------------------------------------------------[debug]  人脸正常：" << std::endl;
#ifdef DSM_USING_RKNN
			ret = dsm_std::Resnet::resnet_recog(face_roi_info, allresult_map, std_speed, exterIOparam);
			// ret = dsm_std::Resnet::resnet_std5recog(face_roi_info, allresult_map, std_speed, exterIOparam);
#else
			// int ret = dsm_std::Resnet::resnet_recog(resnet18, face_roi_info, allresult_map, subway_speed);
#endif
			// allresult_map.clear();
			// allresult_map = {0, 0, 0, 0, 0, 0, 1, 0, 0};
			allresult_map.push_back(0);
		}
			
			if (ret < 0)
			{
				std::cout << "std:----------------------------------------------Resnet18分类识别异常    ret： " << ret << std::endl;
				dsm_std::printLog::Write("resnet_recog异常 ret<0 ");
				return -1; // test3
			}
			

			std::vector<int> button_status;
            button_status.push_back(globalstd.eye_button);
			button_status.push_back(globalstd.mouth_button);
			button_status.push_back(globalstd.smoking_button);
			button_status.push_back(globalstd.phone_button);
			button_status.push_back(globalstd.hat_button);
			button_status.push_back(globalstd.seatbelt_button);
			button_status.push_back(globalstd.lookaround_button);
			button_status.push_back(globalstd.Blindspot_button);
			button_status.push_back(globalstd.facemissing_button);

			// std::cout << "std:---------------------------------------------- allresult_map: " << allresult_map.size()<< std::endl;
			// for (size_t i = 0; i < allresult_map.size(); i++)
			// {
			// 	std::cout << "std:---------------------------------------------- aaaaaaaaa: " << allresult_map[i]<< std::endl;
			// 	std::cout << "std:---------------------------------------------- bbbbbbbbb: " << button_status[i]<< std::endl;
			// }
			
			//[2]时序监控
			std::vector<int> timeout;
			// uint64_t st_ter = dsm_std::getTimestd();

        
       
			std::vector<Nodeflags> c5pos_out = dsm_std::Kernelstd::execute_alarm_box(allresult_map, box, timeout, button_status);
			// uint64_t edt_ter = dsm_std::getTimestd();
			// std::cout << "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++耗时计算  execute_alarm_box   " << int(edt_ter - st_ter) << std::endl;
			// std::cout<<"std:))))))))))))))))))))))))))))execute_alarm_box正常   c5pos_out： " << string(c5pos_out[0].notes) << endl;
			// if (c5pos_out[3].notes != "Worthless") {
			// 	std::cout << "**********************************************调试——盲点检测实时结果： " << string(c5pos_out[3].notes) << endl;
			// }
			// std::cout << "std: *********************8888888888888888888888888*************************打印输出...    timeout" << timeout.size() << std::endl;
			// std::cout << "std: *********************9999999999999999999999999*************************打印输出...    execute_alarm_box_out" << c5pos_out.size() << std::endl;
			// return -1;
			// for (size_t w = 0; w < 3; w++)  // C5041
			for (size_t w = 0; w < allresult_map.size(); w++)
			{
				// std::cout << "std: **********************************************报警数据记录中...    类别" << w << "  :" << std::string(c5pos_out[w].notes) << std::endl;

				if (timeout[w] == 404) // 异常超时部分清空
				{
					collmap.superposition[w].clear();
					switch (w)
					{
					case 0:
						alarm_stdinfo.eye_alarm = false;
						break;
					case 1:
						alarm_stdinfo.mouth_alarm = false;
						break;
					case 2:
						alarm_stdinfo.smoking_alarm = false;
						break;
					case 3:
						alarm_stdinfo.phone_alarm = false;
						break;
					case 4:
						alarm_stdinfo.hat_alarm = false;
						break;
					case 5:
						alarm_stdinfo.seatbelt_alarm = false;
						break;
					case 6:
						alarm_stdinfo.lookaround_alarm = false;
						break;
					case 7:
						alarm_stdinfo.Blindspot_alarm = false;
						break;
					case 8:
						alarm_stdinfo.facemissing_alarm = false;
					default:
						break;
					}
					// return 0;  //非正常识别情况
				}

				if ((c5pos_out[w].notes == "Worthless") || ((c5pos_out[w].notes == "Freeze")))
				{
					// alarm_stdinfo.eye_alarm = false;   // 这里处理被打断时的判断

					switch (w)
					{
					case 0:
						alarm_stdinfo.eye_alarm = false;
						break;
					case 1:
						alarm_stdinfo.mouth_alarm = false;
						break;
					case 2:
						alarm_stdinfo.smoking_alarm = false;
						break;
					case 3:
						alarm_stdinfo.phone_alarm = false;
						break;
					case 4:
						alarm_stdinfo.hat_alarm = false;
						break;
					case 5:
						alarm_stdinfo.seatbelt_alarm = false;
						break;
					case 6:
						alarm_stdinfo.lookaround_alarm = false;
						break;
					case 7:
						alarm_stdinfo.Blindspot_alarm = false;
						break;
					case 8:
						alarm_stdinfo.facemissing_alarm = false;
					default:
						break;
					}
				}
				else
				{
					// printf("***********result_bai   in line %d  \n",__LINE__);
					// std::cout << "**********************************************w :" << w << std::endl;
					// std::cout << "**********************************************collmap.superposition.size() :" << collmap.superposition.size() << std::endl;
					// std::cout << "std look**********************************************c5pos_out[w].value :" << c5pos_out[w].value << std::endl;
					collmap.superposition[w].push_back(c5pos_out[w].value);
					if (c5pos_out[w].notes == "End") // 读入五个类型，  但触发报警的时间不一定，所以以下代码至少只有一个结果输出，或者，该帧率没有输出报警的情况
					{
						// std::cout << "std:))))))))))))))))))))))))))))成功识别一次数据  存在End c5pos_out： " << w << std::endl;
						// sleep(2);
						int count_occurrences = 0;
						float result_bai = 0;
						// 调用统计函数，输出结果，并清零
						if (!collmap.superposition[w].empty())
						{
							count_occurrences = std::count(collmap.superposition[w].begin(), collmap.superposition[w].end(), 1);
							result_bai = (static_cast<float>(count_occurrences) / static_cast<float>(collmap.superposition[w].size())) * 100;
						}
						std::cout << "*******************置信度   :  " << result_bai << std::endl;

						switch (w)
						{
						case 0:
							std::cout << "std:**********************************************【1闭眼】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[w].size() << std::endl;
							if (result_bai > globalstd.eye_sensitivity) // 灵敏度判断
							{
								alarm_stdinfo.eye_alarm = true;
								std::cout << "std:**********************************************【闭眼报警： result_bai：" << result_bai << std::endl;
								// sleep(2);
							}
							else
							{
								alarm_stdinfo.eye_alarm = false;
							}
							break;
						case 1:
							std::cout << "std:**********************************************【2哈切】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[w].size() << std::endl;
							if (result_bai > globalstd.mouth_sensitivity) // 灵敏度判断
							{
								alarm_stdinfo.mouth_alarm = true;
							}
							else
							{
								alarm_stdinfo.mouth_alarm = false;
							}
							break;
						case 2:
							std::cout << "std:**********************************************【3抽烟】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[w].size() << std::endl;
							if (result_bai > globalstd.smoking_sensitivity) // 灵敏度判断
							{
								alarm_stdinfo.smoking_alarm = true;
							}
							else
							{
								alarm_stdinfo.smoking_alarm = false;
							}
							break;
						case 3:
							std::cout << "std:**********************************************【4电话】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[w].size() << std::endl;
							if (result_bai > globalstd.phone_sensitivity) // 灵敏度判断
							{
								alarm_stdinfo.phone_alarm = true;
							}
							else
							{
								alarm_stdinfo.phone_alarm = false;
							}
							break;
						case 4:
							std::cout << "std:**********************************************【5帽子】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[w].size() << std::endl;
							if (result_bai > globalstd.hat_sensitivity) // 灵敏度判断
							{
								alarm_stdinfo.hat_alarm = true;
							}
							else
							{
								alarm_stdinfo.hat_alarm = false;
							}
							break;
						case 5:
							std::cout << "std:**********************************************【6安全带】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[w].size() << std::endl;
							if (result_bai > globalstd.seatbelt_sensitivity) // 灵敏度判断
							{
								alarm_stdinfo.seatbelt_alarm = true;
							}
							else
							{
								alarm_stdinfo.seatbelt_alarm = false;
							}
							break;
						case 6:
							std::cout << "std:**********************************************【7分神】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[w].size() << std::endl;
							if (result_bai > globalstd.lookaround_sensitivity) // 灵敏度判断
							{
								alarm_stdinfo.lookaround_alarm = true;
							}
							else
							{
								alarm_stdinfo.lookaround_alarm = false;
							}
							break;
						case 7:
							std::cout << "std:**********************************************【8盲点】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[w].size() << std::endl;
							if (result_bai >= globalstd.Blindspot_sensitivity) // 灵敏度判断
							{
								alarm_stdinfo.Blindspot_alarm = true;
							}
							else
							{
								alarm_stdinfo.Blindspot_alarm = false;
							}
							std::cout << "alarm_stdinfo.Blindspot_alarm:    " << alarm_stdinfo.Blindspot_alarm << std::endl;
							break;
						case 8:
							std::cout << "std:**********************************************【9离岗】结果记录superposition:    总帧率（识别次数）： " << collmap.superposition[w].size() << std::endl;
							if (result_bai > globalstd.facemissing_sensitivity) // 灵敏度判断
							{
								alarm_stdinfo.facemissing_alarm = true;
							}
							else
							{
								alarm_stdinfo.facemissing_alarm = false;
							}
							break;
						default:
							break;
						}
						collmap.superposition[w].clear();
						// std::cout << "std:______________________________________________输出一帧结果（应该是打印五次）：   " << w << std::endl;
					}
				}

				// 每次都会输出报警结果，一共输出五个
			}

			//			string text_yaw = "left-right: " + to_string(int(face_roi_info.face_angle[0]));
			//			string text_pitch = "up-down: " + to_string(int(face_roi_info.face_angle[1]));
			//			string text_roll = "roll_angle: " + to_string(int(face_roi_info.face_angle[2]));
			//			string distance = to_string(90 - int(face_roi_info.face_angle[3] * 100)) + "cm";//距离
			//			putText(back_frame, text_yaw, Point(50, 50), FONT_HERSHEY_COMPLEX, 0.75, Scalar(0, 0, 255), 1, 1, 0);
			//			putText(back_frame, text_pitch, Point(50, 100), FONT_HERSHEY_COMPLEX, 0.75, Scalar(0, 0, 255), 1, 1, 0);
			//			putText(back_frame, text_roll, Point(50, 150), FONT_HERSHEY_COMPLEX, 0.75, Scalar(0, 0, 255), 1, 1, 0);
			//			putText(back_frame, distance, Point(50, 200), FONT_HERSHEY_COMPLEX, 0.75, Scalar(0, 0, 255), 1, 1, 0);
			//			cv::imshow(kWinName, back_frame);
		}
		else
		{
			printf("*********face_roi_info.state == 4  人脸异常**run_detector in line %d  \n", __LINE__);
			// std::string eye_log = to_string(abs(etime)) + "_Alarm";
            dsm_std::printLog::Write("人脸异常信息");
			return -1;
		}


		// 危险程度，包含0：不存在报警信息，1~3疲劳程度 4~6危险驾驶信息
		alarm_stdinfo.fatigue_rank = risk_level(alarm_stdinfo);

		if (alarm_stdinfo.fatigue_rank > 0)
		{
			std::cout << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.fatigue_rank: " << alarm_stdinfo.fatigue_rank << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl
					  << std::endl;
			if (alarm_stdinfo.facemissing_alarm)
			{
				return 3;
			}
					  
			return 1;
		}
		else if (alarm_stdinfo.fatigue_rank == 0)
		{
			printf("std***                                                       ******此帧无任何报警**run_detector in line %d  \n", __LINE__);
			return 0;
		}
		else
		{
			return -1;
		}

		// teststring rank = "Alarm.size:  " + to_string(alarm_stdinfo.fatigue_rank);
		// testcv::putText(back_frame, rank, Point(20, 1000), FONT_HERSHEY_COMPLEX, 2, Scalar(2, 255, 155), 2, 8, 0);
		//		cv::imshow(kWinName, back_frame);
	}
	catch (const cv::Exception &ii)
	{
		return -1;
	}
	return 0;
}

/*************************************************
//  Method:    convertTo3Channels
//  Description: 将单通道图像转为三通道图像
//  Returns:   cv::Mat
//  Parameter: binImg 单通道图像对象
*************************************************/
cv::Mat dsm_std::convertTo3Channels(const cv::Mat &binImg)
{
	cv::Mat three_channel = cv::Mat::zeros(binImg.rows, binImg.cols, CV_8UC3);
	std::vector<cv::Mat> channels;
	for (int i = 0; i < 3; i++)
	{
		channels.push_back(binImg);
	}
	cv::merge(channels, three_channel);
	return three_channel;
}

#include "XuTimeUtil.h"

// 获取时间戳  获取毫秒级
uint64_t dsm_std::getTimestd()
{
	uint64_t atime = XuTimeUtil::getInstance().currentTimeMillis();
	// printf("··········430时间atime:  %llu  \n", atime);
	return atime;
}

// 危险程度，包含0：不存在报警信息，1~3疲劳程度 4~6危险驾驶信息
int dsm_std::risk_level(DSMInfo alarm_stdinfos)
{
	//后续会增加遮挡、抽烟、打电话的识别判断需要修改此处
	if ((alarm_stdinfos.eye_alarm == 0) && (alarm_stdinfos.mouth_alarm == 0) && (alarm_stdinfos.smoking_alarm == 0) && 
		(alarm_stdinfos.phone_alarm == 0) && (alarm_stdinfos.hat_alarm == 0) && (alarm_stdinfos.seatbelt_alarm == 0) &&
		(alarm_stdinfos.lookaround_alarm == 0) && (alarm_stdinfos.facemissing_alarm == 0) && (alarm_stdinfos.camcover_alarm == 0))
	{
		alarm_stdinfos.fatigue_rank = 0;
		return 0;
	}
	int sum_alarm;
	sum_alarm = alarm_stdinfos.eye_alarm + alarm_stdinfos.mouth_alarm + alarm_stdinfos.smoking_alarm + alarm_stdinfos.phone_alarm + 
		alarm_stdinfos.hat_alarm + alarm_stdinfos.seatbelt_alarm + alarm_stdinfos.lookaround_alarm + alarm_stdinfos.facemissing_alarm + alarm_stdinfos.camcover_alarm;
	if (sum_alarm > 0)
	{
		if (alarm_stdinfos.eye_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  Eye_alarm " << std::endl;
		}
		if (alarm_stdinfos.mouth_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  mouth_alarm " << std::endl;
		}
		if (alarm_stdinfos.smoking_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  smoking_alarm " << std::endl;
		}
		if (alarm_stdinfos.phone_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  phone_alarm " << std::endl;
		}
		if (alarm_stdinfos.hat_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  hat_alarm " << std::endl;
		}
        if (alarm_stdinfos.seatbelt_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  seatbelt_alarm " << std::endl;
		}
		if (alarm_stdinfos.Blindspot_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  Blindspot_alarm " << std::endl;
		}
        if (alarm_stdinfos.lookaround_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  lookaround_alarm " << std::endl;
		}
        if (alarm_stdinfos.facemissing_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  facemissing_alarm " << std::endl;
		}
        if (alarm_stdinfos.camcover_alarm == 1)
		{
			std::cout << "std:..........................................................……=……=……=……=……=……=……=……=……=……alarm_stdinfo.==  camcover_alarm " << std::endl;
		}
		alarm_stdinfos.fatigue_rank = sum_alarm;
		return sum_alarm;
	}
	else
	{
		return 0;
	}
}

void dsm_std::init_DSMInfo_parmas(DSMInfo alarm_stdinfo)
{
    alarm_stdinfo.isSameDriver = 0;
	alarm_stdinfo.face_score = 0.6666;
	alarm_stdinfo.eye_alarm = false;
	alarm_stdinfo.mouth_alarm = false;
	alarm_stdinfo.smoking_alarm = false;
	alarm_stdinfo.phone_alarm = false;
	alarm_stdinfo.hat_alarm = false;
	alarm_stdinfo.seatbelt_alarm = false;
	alarm_stdinfo.lookaround_alarm = false;
	alarm_stdinfo.facemissing_alarm = false;
	alarm_stdinfo.fatigue_rank = 0;
}

std::vector<dsm_std::Nodeflags> dsm_std::Kernelstd::execute_alarm_box(std::vector<int> input, dsm_std::AlarmBox &boxstd, std::vector<int> &timeout,std::vector<int> button_status)
{
	std::vector<cv::Point3i> timepind = {{0, globalstd.eye_eventime, globalstd.eye_freetime},
										 {0, globalstd.mouth_eventime, globalstd.mouth_freetime},
										 {0, globalstd.smoking_eventime, globalstd.smoking_freetime},
										 {0, globalstd.phone_eventime, globalstd.phone_freetime},
										 {0, globalstd.hat_eventime, globalstd.hat_freetime},
										 {0, globalstd.seatbelt_eventime, globalstd.seatbelt_freetime},
										 {0, globalstd.lookaround_eventime, globalstd.lookaround_freetime},
										 {0, globalstd.Blindspot_eventime, globalstd.Blindspot_freetime},
										 {0, globalstd.facemissing_eventime, globalstd.facemissing_freetime}};

	// for (size_t i = 0; i < input.size(); i++)
	// {
	// 	std::cout << "std:---------------------------------------------- KKKK识别结果allresult_map.size(): " << input[i]<< std::endl;
	// }
	// std::cout << "std:----------------------------------------------test0506  globalstd.facemissing_freetime: " <<  globalstd.facemissing_freetime<< std::endl;
	int chooice_make = 0 ;
	std::vector<int> bginput;
	std::vector<cv::Point3i> bgtimepind; 
	for (size_t i = 0; i < input.size(); i++)
	{
		if (button_status[i])
		{
			// std::cout << "std:~~~~~~~~~~~~~~~~~~Alarm i" << i << "      chooice_make " << chooice_make << std::endl;
			bginput.push_back(input[i]);
			// bgtimepind[chooice_make]=timepind[i];
			bgtimepind.push_back(timepind[i]);
			// chooice_make++;
		}
	}

	std::vector<dsm_std::Nodeflags> out_data(input.size());
	std::vector<dsm_std::Status> restatus = boxstd.alarm_box(bginput, bgtimepind);	
	int chooice_sum = 0 ;
	//结果回归原始河流中，跳出再跳回  错位修复restatus
	for (size_t i = 0; i < input.size(); i++)
	{
		if (button_status[i])
		{
			// std::cout << "std:~~~~~~~~~~~~~~~~~~AlarmX " << i  << " status: " << getStatusName(restatus[chooice_sum]) << "  restatus.SIZE= "<< restatus.size() << std::endl;
			out_data[i].value = input[i];
			// printf("**********load debug in line %d  \n", __LINE__);
			// std::cout << "std:~~~~~~~~~~~~~~~~~~getStatusName(restatus[chooice_sum]) " << getStatusName(restatus[chooice_sum]) << std::endl;
			// std::cout << "std:~~~~~~~~~~~~~~~~~~out_data[i].notes " << out_data[i].notes << std::endl;
			// std::cout << "std:~~~~~~~~~~~~~~~~~~test" << i << "      chooice_sum " << chooice_sum << std::endl;

			out_data[i].notes = getStatusName(restatus[chooice_sum]);
			timeout.push_back(timepind[chooice_sum].x);
			chooice_sum++;

		}
		else
		{
			// std::cout << "std:~~~~~~~~~~~~~~~~~~Alarm " << i  << " status:  未开启 "  << std::endl;
			out_data[i].value = input[i];  // 4 = Worthless
			out_data[i].notes ="Worthless";
			timeout.push_back(timepind[i].x);
		}

	
	// out_data[i].value = bginput[i];
	// out_data[i].notes = getStatusName(restatus[i]);
	// timeout.push_back(bgtimepind[i].x);
	// std::cout << "std:~~~~~~~~~~~~~~~~~~Alarm " << (i + 1) << " status: " << getStatusName(restatus[i]) << std::endl;
		// if (timepind[i].x > 0) // 404 中断操作
		// {
		// 	std::cout << " 99999999999999999999999999999999999999999999999999999timepind[i].x: " << timeout.size() << std::endl;
		// }
	}
	// std::cout << " std:````````````````out_data: " << out_data.size() << std::endl;
	return out_data;
}

bool dsm_std::LoadbeltConfigFile(const std::string &file_name)
{
	cv::FileStorage config(file_name, cv::FileStorage::READ);
	bool flag = config.isOpened();
	if (flag)
	{
		std::cout << "     ********** Open config File true:" << file_name << std::endl;
	}
	else
	{
		std::cout << "     **********Open config File :" << file_name << " failed.";
		return false;
	}
	printf("**********load debug in line %d  \n", __LINE__);
	// return true;
	if (!(config.getFirstTopLevelNode().empty()))
	{
		// std::cout << "++++++~~~~~~~~~~~~~~~~~~~~~~~  config[yaw_angle].empty() " << !config["yaw_angle"].empty() << std::endl;
		dsm_std::ParamsToCheck params;
		// 人脸角度参数
		params.yaw_angle = config["yaw_angle"];
		printf("**********params.yaw_angle debug in line %d  \n", params.yaw_angle);
		// 安全带坐标参数
		cv::Point p1, p2, p3, p4, p5;
		cv::Point ps1, ps2, ps3, ps4, ps5;  //客户端输入1280*720
		config["point1"]["x"] >> p1.x;   ps1.x = p1.x *0.5;
		config["point1"]["y"] >> p1.y;	 ps1.y = (p1.y +280)*0.5;
		config["point2"]["x"] >> p2.x;	 ps2.x = p2.x *0.5;
		config["point2"]["y"] >> p2.y;	 ps2.y = (p2.y +280)*0.5;
		config["point3"]["x"] >> p3.x;	 ps3.x = p3.x *0.5;
		config["point3"]["y"] >> p3.y;	 ps3.y = (p3.y +280)*0.5;
		config["point4"]["x"] >> p4.x;	 ps4.x = p4.x *0.5;
		config["point4"]["y"] >> p4.y;	 ps4.y = (p4.y +280)*0.5;
		config["point5"]["x"] >> p5.x; 	 ps5.x = p5.x *0.5;
		config["point5"]["y"] >> p5.y;	 ps5.y = (p5.y +280)*0.5;
		
		std::cout << "std:640*640的安全带坐标----------------------------------------p1 =  " << p1 << std::endl;
		std::cout << "std:640*640的安全带坐标----------------------------------------p2 =  " << p2 << std::endl;
		std::cout << "std:640*640的安全带坐标----------------------------------------p3 =  " << p3 << std::endl;
		std::cout << "std:640*640的安全带坐标----------------------------------------p4 =  " << p4 << std::endl;
		std::cout << "std:640*640的安全带坐标----------------------------------------p5 =  " << p5 << std::endl;
		//params 坐标是填充后的640*640下的
		params.five_points.push_back(p1);
		params.five_points.push_back(p2);
		params.five_points.push_back(p3);
		params.five_points.push_back(p4);
		params.five_points.push_back(p5);

		printf("std:**********getFirstTopLevelNode debug in line %d  \n", __LINE__);
		// params.point_count = 5;
		params.left_right_button = config["left_right"];
		int dbutton = config["debug_button"];
		params.debug_button = dbutton == 1 ? true : false;
		printf("std:**********getFirstTopLevelNode debug in line %d  \n", __LINE__);
		// std::cout << "std:----------------------------------------acheck_debug_paramlstd_info.belt_point =  " << alarm_stdinfo.belt_point.size() << std::endl;
		// 检查参数，错误则传默认值  徐榕佑 负责输出的坐标值，resize到640*640
		bool check_state = dsm_std::check_debug_param(params);
		// std::cout << "std:----------------------------------------acheck_debug_paramlstd_info.belt_point =  " << alarm_stdinfo.belt_point.size() << std::endl;
		// printf("**********getFirstTopLevelNode debug in line %d  \n", __LINE__);
		dsm_std::ad_angle angle_info;
		angle_info.yaw_angle = params.yaw_angle;
		angle_info.adjusted_button = true;
		dsm_std::SkewParam::SetAngle(angle_info);

		// 安全带坐标排序  0401取消排序
		// dsm_std::belt_point_deal(params.five_points, params.left_right_button);
		//转为UI显示界面 //输出安全带坐标 已转换为UI
		cv::Point outpos;
		for (size_t i = 0; i < params.five_points.size(); i++)
		{
			outpos.x = params.five_points[i].x;
			outpos.y = params.five_points[i].y; 
			alarm_stdinfo.belt_point.push_back(outpos);
		}
		
		std::cout<<"std:----------------------------------------LoadDebugConfigFileoutput [1]belt: alarm_stdinfo.belt_point1 =  " <<alarm_stdinfo.belt_point[0]<<std::endl; 
        std::cout<<"std:----------------------------------------LoadDebugConfigFileoutput [2]belt: alarm_stdinfo.belt_point =  " <<alarm_stdinfo.belt_point[1]<<std::endl; 
        std::cout<<"std:----------------------------------------LoadDebugConfigFileoutput [3]belt: alarm_stdinfo.belt_point3 =  " <<alarm_stdinfo.belt_point[2]<<std::endl; 
        std::cout<<"std:----------------------------------------LoadDebugConfigFileoutput [4]belt: alarm_stdinfo.belt_point =  " <<alarm_stdinfo.belt_point[3]<<std::endl; 
        std::cout<<"std:----------------------------------------LoadDebugConfigFileoutput [5]belt: alarm_stdinfo.belt_point =  " <<alarm_stdinfo.belt_point[4]<<std::endl; 
		dsm_std::BELT_INFO belt_info;
		params.five_points.swap(belt_info.five_point);
		belt_info.left_right_button = params.left_right_button;
		dsm_std::SkewParam::SetBeltInfo(belt_info);
		dsm_std::SkewParam::debug_button = params.debug_button;

		config.release();
		printf("**********XUXU in line %d  \n", __LINE__);
	}
	else
	{
		std::cout << "debug_config File:" << file_name << " 文件内为空内容，读取默认参数" << std::endl;

		dsm_std::ParamsToCheck params;
		// 读取默认参数
		dsm_std::LoadDefaultParam(params);

		dsm_std::ad_angle angle_info;
		angle_info.yaw_angle = params.yaw_angle;
		angle_info.adjusted_button = true;
		dsm_std::SkewParam::SetAngle(angle_info);

		dsm_std::belt_point_deal(params.five_points, params.left_right_button);
		dsm_std::BELT_INFO belt_info;
				//转为UI显示界面 //输出安全带坐标 已转换为UI
		cv::Point outpos;
		for (size_t i = 0; i < params.five_points.size(); i++)
		{
			outpos.x = (params.five_points[i].x *2) -280;
			outpos.y = (params.five_points[i].y *2) ; 
			alarm_stdinfo.belt_point.push_back(outpos);
		}
		params.five_points.swap(belt_info.five_point);
		belt_info.left_right_button = params.left_right_button;
		dsm_std::SkewParam::SetBeltInfo(belt_info);
		dsm_std::SkewParam::debug_button = params.debug_button;
	}

// printf("**********alarm_stdinfo.belt_point.size()in line %d  \n", __LINE__);
	// std::copy(belt_info.five_point.begin(), belt_info.five_point.end(), std::back_inserter(alarm_stdinfo.belt_point));
	// alarm_stdinfo.belt_point.push_back(belt_info.five_point[0]);
	// alarm_stdinfo.belt_point.push_back(belt_info.five_point[1]);
	// alarm_stdinfo.belt_point.push_back(belt_info.five_point[2]);
	// alarm_stdinfo.belt_point.push_back(belt_info.five_point[3]);
	// alarm_stdinfo.belt_point.push_back(belt_info.five_point[4]);
	return true;
}

bool dsm_std::SkewParam::debug_button = false;
dsm_std::BELT_INFO dsm_std::SkewParam::belt_info;
dsm_std::ad_angle dsm_std::SkewParam::adjusted_angle;

void dsm_std::SkewParam::SetAngle(ad_angle ad_angle)
{
	SkewParam::adjusted_angle = ad_angle;
}
void dsm_std::SkewParam::SetBeltInfo(BELT_INFO belt_info)
{
	SkewParam::belt_info = belt_info;
}

dsm_std::BELT_INFO dsm_std::SkewParam::GetBeltInfo()
{
	return SkewParam::belt_info;
}
dsm_std::ad_angle dsm_std::SkewParam::GetAngle()
{
	return SkewParam::adjusted_angle;
}
