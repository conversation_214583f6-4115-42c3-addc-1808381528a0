#include <iostream>
#include <string>
#include <opencv2/opencv.hpp>
#include "std_face_angle.h"
#include "std_cut_faceroi.h"
#include "std_run.h"

dsm_std::ad_angle adjusted_angle;
// ��������Ƕ�
std::vector<float> dsm_std::Tface::face_angle(cv::Mat input_img, cv::Rect input_box, std::vector<int> landmarks)
{
	// �޶�roi����
	cv::Rect face_box = safeROI(input_img, input_box);
	cv::Point origin;
	std::vector<float> result_vec;
	origin.x = face_box.x;
	origin.y = face_box.y + face_box.height;

	float base_pos =  0;
	float angle_roll = Tface::get_roll_arctan(landmarks);					 // �����
	float angle_pitch = Tface::get_pitch_arctan(landmarks);					 // ������
	float angle_yaw = Tface::get_yaw_arctan(landmarks, base_pos, angle_pitch, angle_roll); // ƫ����
// printf("***********face_angle in line %d  \n", __LINE__);
	float ratio = Tface::get_face_distance(input_img, input_box, landmarks); // ��������

	adjusted_angle = SkewParam::GetAngle();
	
	std::cout << "std::客户端标定输入信息=================================adjusted_angle.yaw_angle:   " <<adjusted_angle.yaw_angle << std::endl;
	// std::cout << "std::555555==========================::::::::::::::::::::::::::::::::::::::::::::::::::::=======angle_yaw::::      " << angle_yaw << std::endl;
	// std::cout << "std::555555==========================::::::::::::::::::::::::::::::::::::::::::::::::::::=======angle_roll::::      " << angle_roll << std::endl;
	// std::cout << "std::555555==========================::::::::::::::::::::::::::::::::::::::::::::::::::::=======angle_pitch::::      " << angle_pitch << std::endl;
	// if (true)
	// if (adjusted_angle.adjusted_button == true)
	// {
	// 	cab_pos = (angle_yaw - adjusted_angle.yaw_angle) ;
	// }
// printf("***********face_angle in line %d  \n", __LINE__);
	result_vec.clear();
	result_vec.push_back(base_pos);  //原始基准点
	result_vec.push_back(angle_pitch); //
	result_vec.push_back(angle_roll);
	result_vec.push_back(angle_yaw);  //校正后的偏航点
	result_vec.push_back(ratio);

	return result_vec;
}

// ��ȡб�� ˮƽֱ�ߵĽǶȣ�Ϊ���� ƫ����
float dsm_std::Tface::get_yaw_arctan(std::vector<int> landmark, float& cab_pos, float angle_pitch, float angle_roll)
{
	float k = 0; // ֱ��б��
	float angle = 0;
	// vector <float> lines_arctan;//ֱ��б�ʵķ�����ֵ
	// for (unsigned int i = 0; i < lines.size(); i++)
	//{

	//	k = (double)(lines[i][3] - lines[i][1]) / (double)(lines[i][2] - lines[i][0]); //���ֱ�ߵ�б��
	//	lines_arctan.push_back(atan(k));
	//}
	// return lines_arctan;

	float tandb =0;
	float tanlb =0;
	//�۾��ͱ��ӿ������쳣����
	float Newtandb = float((landmark[7] + landmark[9]) * 0.5 - landmark[5]);

	int limit_piexl = landmark[5] - ((landmark[1] + landmark[3]) * 0.5);
	// std::cout<<"std:-------------------------------------------------------  ``````````````````````````````` limit_piexl:::::     " <<limit_piexl << std::endl;
	if (limit_piexl < 15)
	{
		tandb = (float)(landmark[7]  - ((landmark[1] + landmark[3]) * 0.5));
		tanlb = (float)(landmark[4]  - ((landmark[0] + landmark[2]) * 0.5));
	}
	else
	{
		tandb = (float)(landmark[5] - ((landmark[1] + landmark[3]) * 0.5));
		tanlb = (float)(landmark[4] - ((landmark[0] + landmark[2]) * 0.5));
	}
	/*2024.11.13����*/
	if (((angle_roll > 8) && (angle_roll < 20)) || ((angle_roll < -8) && (angle_roll > -20)))
	{
		tanlb += (angle_roll * 0.75);
		// std::cout << "=============== angle_roll * 0.75 = " << angle_roll * 0.75 << std::endl;
	}
	if (tanlb == 0)
	{
		cab_pos = 0;
		return angle;
	}
	k = Newtandb / tanlb; //���ֱ�ߵ�б��
	// k = tandb / tanlb; // ���ֱ�ߵ�б��
	if (angle_pitch < -20)
	{
		k = tandb / tanlb;
	}
	// cout << "tandbΪ�� " << tandb << endl;
	// cout << "tanlbΪ�� " << tanlb << endl;
	// cout << "б��Ϊ�� " << k << endl;
	angle = 180 - ((atan(k) * 180) / 3.1415926);
// printf("***********face_angle in line %d  \n", __LINE__);
// std::cout << "std:_______________ _____________ ______________ _______________ ____________ttt::" << angle << std::endl;
	if (k < 0)
	{
		angle = (float)(-1 * (90 + (atan(k) * 57.29577)));
	}
	else
	{
		angle = (float)(90 - (atan(k) * 57.29577));
	}

	cab_pos = angle;
	// printf("***********face_angle in line %d  \n", __LINE__);
// std::cout << "std:_______________ _____________ ______________ _______________ ____________kkk::  " << angle << std::endl;
	//�����Ӻ��۾���ˮƽ�߽ӽ�
	// cout << "��⵽��ֱ�߽Ƕ�Ϊ��" << angle << endl;
/*2024.11.13����*/
	//��װ�Զ���Ƕȵ���
	float AdjustedAngle = 0.00;
	float TempAdjustedAngle = 0.00;
	if (adjusted_angle.adjusted_button == true)
	{
		AdjustedAngle = adjusted_angle.yaw_angle;
		TempAdjustedAngle = AdjustedAngle / 0.82;

		if (((angle + TempAdjustedAngle) > -8) && ((angle + TempAdjustedAngle) < 8))
		{
			angle -= AdjustedAngle;
			// angle += AdjustedAngle;
			angle = angle * 0.4;
// 			printf("***********face_angle in line %d  \n", __LINE__);
// std::cout << "std:_______________ _____________ ______________ _______________ ____________ttt" << angle << std::endl;
		}
		else
		{
			// printf("***********face_angle in line %d  \n", __LINE__);
			// std::cout << "std:_______________ _____________ ______________ _______________ ____________ccc:   " << angle << std::endl;
			// angle += AdjustedAngle;
			angle -= AdjustedAngle;
			angle = angle * 0.86;  //angle = angle * 0.82;

			// std::cout << "std:_______________ _____________ ______________ _______________ ____________ddd" << angle << std::endl;
			//������ת������
			// if (angle > 13)
			if (angle > 18)
			{
				angle *= 0.85;
			}
			if (angle < -20)
			{
				//angle *= 1.15;
			}
			// printf("***********face_angle in line %d  \n", __LINE__);
// std::cout << "std:_______________ _____________ ______________ _______________ ____________ttt" << angle << std::endl;
		}
	}
	else
	{
		if ((angle > -8) && (angle < 8))
		{
			angle = angle * 0.4;
		}
		else
		{
			angle = angle * 0.82;
		}
		// printf("***********face_angle in line %d  \n", __LINE__);
// std::cout << "std:_______________ _____________ ______________ _______________ ____________ttt" << angle << std::endl;
	}

	//��Ƕȣ��������غ�
	if (angle < 0)
	{
		int EyeWidth = std::abs(landmark[2] - landmark[0]);
		int Nose2LeftEye = std::abs(landmark[4] - landmark[0]);
		// std::cout << "EAyeWidth = " << EyeWidth << std::endl;
		// std::cout << "Nose2LeftEye = " << Nose2LeftEye << std::endl;
		if (Nose2LeftEye > (EyeWidth * 1.7))
		{
			angle = -99;
		}
	}
	// std::cout << "std:_______________ _____________ ______________ _______________ ____________face_angle" << angle << std::endl;
	return angle;
}

// ͷ��������ת��������
float dsm_std::Tface::get_pitch_arctan(std::vector<int> landmark)
{
	// ����ȡ������������ͷ�ľ��룬�õ�һ����Ȩϵ��  ����������������

	float angle = 0;
	// ������ͥ����ͥ�Ĵ�С��ϵ  �жϸ�����
	float mouth = (float(landmark[7]) + float(landmark[9])) / 2;
	float eye = (float(landmark[1]) + float(landmark[1])) / 2;

	// ��ֻͥ��������֮��
	// float shangt = float(mouth - landmark[5]) / float(eye - face_box.y);
	// float zhonot = (float(landmark[5]) - eye) /  float(eye - face_box.y) ;
	// string tet = to_string(shangt);
	// string text = to_string(zhonot);
	// putText(input_img, tet, Point(landmark[2], landmark[3]), FONT_HERSHEY_COMPLEX, 0.6, Scalar(0, 0, 255), 1, 8, 0);
	// putText(input_img, text, Point(landmark[4], landmark[5]), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 0, 255), 1, 8, 0);

	/*������Ӹ����۾�y���ñ�����Զ�����۾�һ������*/
	if (float(landmark[5] - eye < 0))
	{
		eye = landmark[5] - 2;
	}
	float xiat = float(mouth - landmark[5]) / float(landmark[5] - eye);
	angle = (float)((atan(xiat) * 57.29577) - 45);
	if (true)
	{
		angle = angle * 0.96;
	}
	// vector <float> lines_arctan;//ֱ��б�ʵķ�����ֵ
	// for (unsigned int i = 0; i < lines.size(); i++)
	//{
	//	k = (double)(lines[i][3] - lines[i][1]) / (double)(lines[i][2] - lines[i][0]); //���ֱ�ߵ�б��
	//	lines_arctan.push_back(atan(k));
	// }
	// return lines_arctan;
	// cout << "��⵽��ֱ�߽Ƕ�Ϊ��" << txt << endl;
	//  ȡֵ��Χ
	// 1.0~1.2  ������0
	//<1.0  ����
	//>1.4  ����
	return angle;
}

// ͷ��������б�������  �����ǶȲ�����10��
float dsm_std::Tface::get_roll_arctan(std::vector<int> landmark)
{
	float k = 0; // ֱ��б��
	float angle = 0;

	float tandb = (float)(landmark[3] - landmark[1]);
	float tanlb = (float)(landmark[2] - landmark[0]);
	if (tandb == 0)
	{
		return angle;
	}
	k = tandb / tanlb; // ���ֱ�ߵ�б��
	// cout << "tandbΪ�� " << tandb << endl;
	// cout << "tanlbΪ�� " << tanlb << endl;
	// cout << "б��Ϊ�� " << k << endl;
	// angle = 180 - ((atan(k) * 180) / 3.1415926);
	angle = (float)(atan(k) * 57.29577);
	// cout << "��⵽��������б�ĽǶ�Ϊ��" << angle << endl;
	return angle;
}

// ʶ����������
float dsm_std::Tface::get_face_distance(cv::Mat input_img, cv::Rect face_box, std::vector<int> landmark)
{
	// ����ȡ������������ͷ�ľ��룬�õ�һ����Ȩϵ��  ����������������

	float ratio = 0;
	// ������ͥ����ͥ�Ĵ�С��ϵ  �жϸ�����
	float mouth = (float(landmark[7]) + float(landmark[9])) / 2;
	float eye = (float(landmark[1]) + float(landmark[1])) / 2;

	// ��ֻͥ��������֮��
	// float shangt = float(mouth - landmark[5]) / float(eye - face_box.y);
	// float zhonot = (float(landmark[5]) - eye) /  float(eye - face_box.y) ;

	// float shangt = float(eye - face_box.y)/ float(face_box.height);//�㶨����

	// ��������ϵ��
	// float zhonot = float(input_img.cols) / float(eye - face_box.y);
	// string text =  to_string(zhonot);
	// putText(input_img, text, Point(landmark[4], landmark[5]), FONT_HERSHEY_COMPLEX, 0.6, Scalar(255, 0, 255), 1, 8, 0);

	float shangt = float(face_box.height) / float(input_img.cols);
	std::string tet = "distance: " + std::to_string(int(shangt * 100));
	// putText(input_img, tet, Point(face_box.x, face_box.y + face_box.height + 10), FONT_HERSHEY_COMPLEX, 0.6, Scalar(0, 0, 255), 1, 8, 0);

	return shangt;

	// float xiat = float(mouth - landmark[5]) / float(landmark[5] - eye);
	// ratio = (double)((atan(xiat) * 57.29577) - 45);

	// vector <float> lines_arctan;//ֱ��б�ʵķ�����ֵ
	// for (unsigned int i = 0; i < lines.size(); i++)
	//{
	//	k = (double)(lines[i][3] - lines[i][1]) / (double)(lines[i][2] - lines[i][0]); //���ֱ�ߵ�б��
	//	lines_arctan.push_back(atan(k));
	// }
	// return lines_arctan;
	// cout << "��⵽��ֱ�߽Ƕ�Ϊ��" << txt << endl;
	//  ȡֵ��Χ
	// 1.0~1.2  ������0
	//<1.0  ����
	//>1.4  ����
}

void dsm_std::Tface::draw_face(cv::Mat input_img, cv::Rect roi, cv::Rect face_box, std::vector<float> angle_vec)
{
	// ����ת����ж�ǰ��������ˮƽ�����������ˮƽ ��ֻ��Ϊ����б����������ת��
	cv::Mat eyer_roi;
	cv::Rect center;
	if ((angle_vec[2] < 22) && (angle_vec[2] > -22)) // 12
	{
		if (angle_vec[0] > 18)
		{
			center.x = roi.x - 18;
			center.y = roi.y;
			center.width = roi.width;
			center.height = roi.height;
			// eyer_roi = Tcut::cut_rotate_roi(input_img, center, angle_vec[2]);
		}
		else if (angle_vec[0] < -18)
		{
			center.x = roi.x + 18;
			center.y = roi.y;
			center.width = roi.width;
			center.height = roi.height;
			// eyer_roi = Tcut::cut_rotate_roi(input_img, center, angle_vec[2]);
		}
		else if ((angle_vec[0] < 6.8) && (angle_vec[0] > -6.8))
		{
			// ��б�ǶȲ���ʱ  ��yolo�������
			// rectangle(input_img, face_box, Scalar(0, 0, 255), 2);
		}
		else
		{
			// eyer_roi = Tcut::cut_rotate_roi(input_img, roi, angle_vec[2]);
		}
		cv::rectangle(input_img, face_box, cv::Scalar(255, 255, 255), 1); // �˴���ʾ������Ϊ����ʾ����ת����Ч��
	}
	else
	{
		// ��б�Ƕȹ���ʱ  ��yolo�������
		cv::rectangle(input_img, face_box, cv::Scalar(255, 0, 0), 1);
	}

	// namedWindow("����ROI", WINDOW_NORMAL);
	// imshow("����ROI", input_img);
}


// ROI越界限制
cv::Rect dsm_std::Tface::safeROI(const cv::Mat& img, cv::Rect& roi) {
	// std::cout << "std:---------------------------------------- roi.width  fun =  " <<roi.width<< std::endl;
   // 处理负尺寸
   roi.width = std::max(roi.width, 0);
   roi.height = std::max(roi.height, 0);

   // 调整左边界：x < 0 时，同步减少 width
   if (roi.x < 0) {
	   roi.width += roi.x; // 等价于 width -= |x|
	   roi.x = 0;
	   roi.width = std::max(roi.width, 0); // 二次保护，防止负数
   }
   // 调整上边界：y < 0 时，同步减少 height
   if (roi.y < 0) {
	   roi.height += roi.y;
	   roi.y = 0;
	   roi.height = std::max(roi.height, 0);
   }

   // 限制右/下边界
   roi.x = std::min(roi.x, img.cols);
   roi.y = std::min(roi.y, img.rows);

	// 计算右/下边界的最大允许范围
   const int max_width = img.cols - roi.x;
   const int max_height = img.rows - roi.y;
//    std::cout << "std:---------------------------------------- max_width =  " <<max_width<< std::endl;
//    std::cout << "std:---------------------------------------- max_height =  " << max_height << std::endl;

	// 限制 width 和 height 不超过实际可用范围
   roi.width = std::min(roi.width, max_width);
   roi.height = std::min(roi.height, max_height);
//    std::cout << "std:---------------------------------------- roi.width =  " << roi.width << std::endl;
//    std::cout << "std:---------------------------------------- roi.height =  " <<  roi.height << std::endl;
   // 最终确保尺寸非负（防御性编程）
   roi.width = std::max(roi.width, 0);
   roi.height = std::max(roi.height, 0);

   return roi;
}