#ifndef STD_RUN_H
#define STD_RUN_H
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include <string>
#include <vector>
#include <future>
#include <numeric>
#include <unordered_map>
#include "std_transit.h"

//配置参数
//识别开关  1：眼睛识别开关   2：哈欠   3：抽烟   4：打电话  5:帽子   6:安全带   7：左顾右盼  8：盲点检测   [9]：离岗
//事件时间  1：闭眼事件的固定时间  2：哈欠事件的固定事件
//灵敏度    1：闭眼频次占比 2：哈欠频次占比
//冷却事件  1：闭眼报警后冷却时间  2：哈欠报警后冷却时间
//模型顺序  1：闭眼 2：没系带 3：睁眼 4：哈切 5：抽烟 6：正常 7：电话 8：没电话 9：帽子 10：没帽子 11：安全带
// extern cv::dnn::Net net;
namespace dsm_std
{
    uint64_t getTimestd();

    //列出安全带所有可能的状态
    typedef enum
    {
            SAFE_BELT_OFF = 0, // 安全带未系
            SAFE_BELT_ON = 1,      // 安全带已系
            SAFE_BELT_ERROR= 2    // 安全带状态错误
    } SafeBeltStatus;

    struct ExternalParam
    {
            int table;
            bool turnL;
            bool turnR;
            bool back_gear;
            SafeBeltStatus safe_belt;

            // 构造函数
            ExternalParam(int sum, bool left, bool right, bool backgear, SafeBeltStatus belt)
                : table(sum), turnL(left), turnR(right),back_gear(backgear), safe_belt(belt) {}
    };

    struct globals
    {
         //1 闭眼参数
        int eye_speed;
        int eye_button;
        int eye_mode;
        int eye_sensitivity;
        int eye_eventime;
        int eye_freetime;

        //2 哈欠参数
        int mouth_speed;
        int mouth_button;
        int mouth_mode;
        int mouth_sensitivity;
        int mouth_eventime;
        int mouth_freetime;

        //3 抽烟参数
        int smoking_speed;
        int smoking_button;
        int smoking_mode;
        int smoking_sensitivity;
        int smoking_eventime;
        int smoking_freetime;

        //4 打电话参数
        int phone_speed;
        int phone_button;
        int phone_mode;
        int phone_sensitivity;
        int phone_eventime;
        int phone_freetime;

        //5 头盔参数
        int hat_speed;
        int hat_button;
        int hat_mode;
        int hat_sensitivity;
        int hat_eventime;
        int hat_freetime;

        //6 安全带参数
        int seatbelt_speed;
        int seatbelt_button;
        int seatbelt_mode;
        int seatbelt_sensitivity;
        int seatbelt_eventime;
        int seatbelt_freetime;

        //7 左顾右盼参数 
        int lookaround_speed;
        int lookaround_button;
        int lookaround_mode;
        int lookaround_sensitivity;
        int lookaround_eventime;
        int lookaround_freetime;

        //8 盲点检测参数
        int Blindspot_speed;
        int Blindspot_button;
        int Blindspot_mode;
        int Blindspot_sensitivity;
        int Blindspot_eventime;
        int Blindspot_freetime;

        //9 离岗参数
        int facemissing_speed;
        int facemissing_button;
        int facemissing_mode;
        int facemissing_sensitivity;
        int facemissing_eventime;
        int facemissing_freetime;

        //10 遮挡参数
        int camcover_speed;
        int camcover_button;
        int camcover_mode;
        int camcover_sensitivity;
        int camcover_eventime;
        int camcover_freetime;

        //[其他] 手势参数  开始和停止信号  
        int gesture_button;
        int gesture_mode;
        int gesture_sensitivity;
        int gesture_sign;
    
        //动静检测参数
        int detect_move_button;
        int move_pixel;
        int frame_early;
    };

    struct regions
    {
        //速度参数 
        int speed;

        //开关参数
        int button;

        //模式参数
        int mode;

        //事件参数
        int sensitivity;

        //事件参数
        int event_time;

        //冷却参数 
        int freeze_time;

        cv::Point limit;
    };

    struct Nodeflags
    {
        int value;
        std::string notes; // start  run  end  bad
    };

    enum class Status : int
    {
        Begin= 0,
        Running,
        End,
        Freeze,
        Worthless
        
    };

    class printLog
    {
    public:
        static void Write(std::string log)
        {
            std::ofstream ofs;
            //            time_t t = time(0);
            //            char tmp[64];
            // strftime(tmp, sizeof(tmp), "[%Y-%m-%d %X]", localtime(&t));
            std::string path = "/userdata/media/AlarmLog.log";
            ofs.open(path, std::ofstream::app);

            // ��ӡ��¼ʱ��
            // ofs << tmp << " - ";
            // ����ӡ�����ַ���  ����ӡʱ��
            ofs.write(log.c_str(), log.size());
            ofs << std::endl;
            ofs.close();
        }
    };
    // 定义一个基类，包含一个处理输入的成员函数
    class Alarm
    {
    public:
        virtual Status check(int input, cv::Point3i &timep3d) = 0;
    };

    // 定义四个派生类，每个类都有自己的处理逻辑
    class AlarmA : public Alarm
    {
    private:
        uint64_t recog_startA = 0;
        uint64_t freeze_startA = 0;
        std::mutex mtx;
    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            std::lock_guard<std::mutex> lock(mtx);
            // int timer1 = 0; //初始值 用来触发start状态
            // 开启冷却状态
            if (freeze_startA > 0)
            {
                uint64_t freeze_t = getTimestd() - freeze_startA;
                // std::cout << "  已经冷却时间:  " << freeze_t << std::endl;
                if (freeze_t < timep3d.z)
                {
                    // std::cout << "  冷却中:  " << timep3d.z << std::endl;
                    return Status::Freeze;
                }
                else
                {
                    freeze_startA = 0;
                    // std::cout << ">>  >> >>冷却结束:  " << timep3d.z << std::endl;
                    return Status::Worthless;
                }
            }
            if (recog_startA > 0)
            {
                uint64_t recog_t = getTimestd() - recog_startA;

                if (recog_t > timep3d.y)
                {

                    // 识别时间到
                    recog_startA = 0;
                    // 赋值给冷却状态开始
                    freeze_startA = getTimestd();

                    // 异常超时需要清理地图
                    int timeout = recog_t - timep3d.y;
                    if (timeout > 3000) // 超时3秒 认为是异常超时，这里可能是车速变化切换的导致的等待超时
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_startA == 0))
            {
                recog_startA = getTimestd();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmB : public Alarm
    {
    private:
        uint64_t recog_startB = 0;
        uint64_t freeze_startB = 0;
        std::mutex mtx;
    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            std::lock_guard<std::mutex> lock(mtx);
            if (freeze_startB > 0)
            {
                uint64_t freeze_t = getTimestd() - freeze_startB;
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    freeze_startB = 0;
                    return Status::Worthless;
                }
            }
            if (recog_startB > 0)
            {
                uint64_t recog_t = getTimestd() - recog_startB;
                if (recog_t > timep3d.y)
                {
                    recog_startB = 0;
                    freeze_startB = getTimestd();
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000)
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_startB == 0))
            {
                recog_startB = getTimestd();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmC : public Alarm
    {
    private:
        uint64_t recog_startc = 0;
        uint64_t freeze_startc = 0;
        std::mutex mtx;
    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            std::lock_guard<std::mutex> lock(mtx);
            if (freeze_startc > 0)
            {
                uint64_t freeze_t = getTimestd() - freeze_startc;
                if (freeze_t < timep3d.z)
                {
                    //  std::cout << "muv:=====================================================  ====== >> Testing  Freeze  " << getTimestd() << std::endl;
                    return Status::Freeze;
                }
                else
                {
                    freeze_startc = 0;
                    // std::cout << "muv:=====================================================  ====== >> Testing 冷却后和触发前位置 Worthless  " << getTimestd() << std::endl;
                    return Status::Worthless;
                }
            }
            if (recog_startc > 0)
            {
                uint64_t recog_t = getTimestd() - recog_startc;
                if (recog_t > timep3d.y)
                {
                    // ʶ��ʱ�䵽
                    recog_startc = 0;
                    //  std::cout << "muv:=====================================================  ====== >> Testing glb识别时间timep3d.y  " << timep3d.y << std::endl;
                    freeze_startc = getTimestd();
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000)
                    {
                        timep3d.x = 404;
                        std::cout << "muv:=====================================================  ====== >> Testing 超时位置 Worthless  " << timeout << std::endl;
                        return Status::Worthless;
                    }
                    else
                    {
                        // std::cout << "muv:=====================================================  ====== >> Testing 识别过程位置 recog_t  " << recog_t << std::endl;
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    // std::cout << "muv:=====================================================  ====== >> Testing 识别过程位置 Running  " << getTimestd() << std::endl;
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_startc == 0))
            {
                recog_startc = getTimestd();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmD : public Alarm
    {
    private:
        uint64_t recog_startd = 0;
        uint64_t freeze_startd = 0;
        std::mutex mtx;
    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            std::lock_guard<std::mutex> lock(mtx);
            if (freeze_startd > 0)
            {
                uint64_t freeze_t = getTimestd() - freeze_startd;
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    freeze_startd = 0;
                    return Status::Worthless;
                }
            }
            if (recog_startd > 0)
            {
                uint64_t recog_t = getTimestd() - recog_startd;
                if (recog_t > timep3d.y)
                {
                    // 识别时间到
                    recog_startd = 0;
                    // 赋值给冷却状态开始
                    freeze_startd = getTimestd();

                    // 异常超时需要清理地图
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000) // 超时3秒 认为是异常超时，这里可能是车速变化切换的导致的等待超时
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_startd == 0))
            {
                recog_startd = getTimestd();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmE : public Alarm
    {
    private:
        uint64_t recog_starte = 0;
        uint64_t freeze_starte = 0;
        std::mutex mtx;
    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            std::lock_guard<std::mutex> lock(mtx);
            if (freeze_starte > 0)
            {
                uint64_t freeze_t = getTimestd() - freeze_starte;
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    freeze_starte = 0;
                    return Status::Worthless;
                }
            }
            if (recog_starte > 0)
            {
                uint64_t recog_t = getTimestd() - recog_starte;
                if (recog_t > timep3d.y)
                {
                    // 识别时间到
                    recog_starte = 0;
                    // 赋值给冷却状态开始
                    freeze_starte = getTimestd();

                    // 异常超时需要清理地图
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000) // 超时3秒 认为是异常超时，这里可能是车速变化切换的导致的等待超时
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_starte == 0))
            {
                recog_starte = getTimestd();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    class AlarmF : public Alarm
    {
    private:
        uint64_t recog_startf = 0;
        uint64_t freeze_startf = 0;
        std::mutex mtx;
    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            std::lock_guard<std::mutex> lock(mtx);
            if (freeze_startf > 0)
            {
                uint64_t freeze_t = getTimestd() - freeze_startf;
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    freeze_startf = 0;
                    return Status::Worthless;
                }
            }
            if (recog_startf > 0)
            {
                uint64_t recog_t = getTimestd() - recog_startf;
                if (recog_t > timep3d.y)
                {
                    // 识别时间到
                    recog_startf = 0;
                    // 赋值给冷却状态开始
                    freeze_startf = getTimestd();

                    // 异常超时需要清理地图
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000)  //超时3秒 认为是异常超时，这里可能是车速变化切换的导致的等待超时
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_startf == 0))
            {
                recog_startf = getTimestd();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

     class AlarmG : public Alarm
    {
    private:
        uint64_t recog_startg = 0;
        uint64_t freeze_startg = 0;
        std::mutex mtx;
    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            // std::cout << "std:22222222222222222222222222222222222222222222222222input  " << input << std::endl;
            std::lock_guard<std::mutex> lock(mtx);
            if (freeze_startg > 0)
            {
                uint64_t freeze_t = getTimestd() - freeze_startg;
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    freeze_startg = 0;
                    // printf("**********load debug in line %d  \n", __LINE__);
                    return Status::Worthless;
                }
            }
            if (recog_startg > 0)
            {
                uint64_t recog_t = getTimestd() - recog_startg;
                if (recog_t > timep3d.y)
                {
                    // 识别时间到
                    recog_startg = 0;
                    // 赋值给冷却状态开始
                    freeze_startg = getTimestd();

                    // 异常超时需要清理地图
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000)  //超时3秒 认为是异常超时，这里可能是车速变化切换的导致的等待超时
                    {
                        timep3d.x = 404;
                        printf("**********load debug in line %d  \n", __LINE__);
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((input == 1) && (recog_startg == 0))
            {
                recog_startg = getTimestd();
                
                // std::cout << "std:22222222222222222222222222222222222222222222222222识别开始freeze:  " <<std::this_thread::get_id() << timep3d.z << std::endl;
                // std::cout << "std:22222222222222222222222222222222222222222222222222222识别firsetrecog_starte:  " <<std::this_thread::get_id() << recog_startg << std::endl;
                // asm volatile("" ::: "memory");

                return Status::Begin;
            }
            // printf("**********load debug in last line %d  \n", __LINE__);
            // std::cout << "std:1111111111111111111111111111111111111111111111111getTimestd():  " <<std::this_thread::get_id() << getTimestd()<< std::endl;
            // std::cout << "std:0000000000000000000000000000000000000000000000000识别开始recog_starte:  " << recog_startg << "   input=" << input << "   recog_startg=" << recog_startg << std::endl;
            return Status::Worthless;
        }
    };

     class AlarmH : public Alarm
    {
    private:
        uint64_t recog_startH = 0;
        uint64_t freeze_startH = 0;
        std::mutex mtx;
    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            std::lock_guard<std::mutex> lock(mtx);
            if (freeze_startH > 0)
            {
                uint64_t freeze_t = getTimestd() - freeze_startH;
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    freeze_startH = 0;
                    return Status::Worthless;
                }
            }
            if (recog_startH > 0)
            {
                uint64_t recog_t = getTimestd() - recog_startH;
                if (recog_t > timep3d.y)
                {
                    // 识别时间到
                    recog_startH = 0;
                    // 赋值给冷却状态开始
                    freeze_startH = getTimestd();

                    // 异常超时需要清理地图
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000)  //超时3秒 认为是异常超时，这里可能是车速变化切换的导致的等待超时
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_startH == 0))
            {
                recog_startH = getTimestd();
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

     class AlarmI : public Alarm
    {
    private:
        uint64_t recog_starti = 0;
        uint64_t freeze_starti = 0;
        std::mutex mtx;
    public:
        Status check(int input, cv::Point3i &timep3d) override
        {
            std::lock_guard<std::mutex> lock(mtx);
            if (freeze_starti > 0)
            {
                uint64_t freeze_t = getTimestd() - freeze_starti; // 计时系统验证多次版本都没有问题，排查问题不考虑此处
                std::cout << " ==========================================离岗 已经冷却时间:  " << freeze_t << std::endl;
                // printf("**********694冷却判断时间getTimestd():  %llu  \n", getTimestd());
                // printf("**********695冷却判断时间freeze_starti:  %llu  \n", freeze_starti);
                // printf("··········696冷却循环时间freeze_t:  %llu  \n", freeze_t);
                if (freeze_t < timep3d.z)
                {
                    return Status::Freeze;
                }
                else
                {
                    // printf("··········冷却结束    循环时间freeze_t:  %llu  \n", freeze_t);
                    freeze_starti = 0;
                    return Status::Worthless;
                }
            }
            if (recog_starti > 0)
            {
                uint64_t recog_t = getTimestd() - recog_starti;
                if (recog_t > timep3d.y)
                {
                    // 识别时间到
                    recog_starti = 0;
                    // 赋值给冷却状态开始
                    freeze_starti = getTimestd();  
                    // printf("===720===**********720END识别结束时间freeze_starti:  %llu  \n", freeze_starti);
                    // 异常超时需要清理地图
                    uint64_t timeout = recog_t - timep3d.y;
                    if (timeout > 3000)  //超时3秒 认为是异常超时，这里可能是车速变化切换的导致的等待超时
                    {
                        timep3d.x = 404;
                        return Status::Worthless;
                    }
                    else
                    {
                        timep3d.x = 0;
                        return Status::End;
                    }
                }
                else
                {
                    return Status::Running;
                }
            }
            if ((1 == input) && (recog_starti == 0))
            {
                recog_starti = getTimestd();
                printf("··········识别开始    recog_starti:  %llu  \n", recog_starti);
                return Status::Begin;
            }
            return Status::Worthless;
        }
    };

    // class AlarmBox
    // {
    // public:
    //     AlarmBox(Alarm *a, Alarm *b, Alarm *c, Alarm *d, Alarm *e, Alarm *f, Alarm *g, Alarm *h, Alarm *i) : alarms_{a, b, c, d, e, f, g, h, i} {}

    //     std::vector<Status> alarm_box(std::vector<int> input, std::vector<cv::Point3i> &timep3d)
    //     {
    //         std::vector<std::thread> threads;
    //         std::vector<Status> statuses(9, Status::Running);

    //         for (std::size_t i = 0; i < alarms_.size(); ++i)
    //         {
    //             threads.push_back(std::thread([this, input, i, &statuses, &timep3d]()
    //                                           {
    //                                               statuses[i] = alarms_[i]->check(input[i], timep3d[i]); // 传入input和time
    //                                           }));
    //         }

    //         for (auto &thread : threads)
    //         {
    //             thread.join();
    //         }
    //         threads.clear();
    //         // for (size_t i = 0; i < 5; i++)
    //         // {
    //         //     std::cout <<std::endl<< "muv:==================================多线程输出结果[i]" << input[i]<< std::endl;
    //         // }
    //         return statuses;
    //         // 输出结果...
    //     }

    // private:
    //     std::vector<Alarm *> alarms_; // 存储Alarm对象的指针
    // };

    class AlarmBox {
        public:
            // 使用 vector 替代固定参数
            AlarmBox(const std::vector<Alarm*>& alarms) : alarms_(alarms) {}
        
            // 原有 alarm_box 方法保持不变
            std::vector<Status> alarm_box(std::vector<int> input, std::vector<cv::Point3i>& timep3d) {
                std::vector<std::thread> threads;
                std::vector<Status> statuses(alarms_.size(), Status::Running);
                // for (size_t i = 0; i < input.size(); i++)
                // {
                //     std::cout << "std:-------------------------------------------8888888888888(): " << input[i]<< std::endl;
                // }

                for (std::size_t i = 0; i < alarms_.size(); ++i) {
                    threads.push_back(std::thread([this, input, i, &statuses, &timep3d]() {
                        statuses[i] = alarms_[i]->check(input[i], timep3d[i]);
                    }));
                }

                // for (std::size_t i = 0; i < alarms_.size(); ++i) 
                // {
                //     threads.push_back(std::thread([this, input, i, &statuses, &timep3d]() 
                //     {
                //         dsm_std::Status stmp = alarms_[i]->check(input[i], timep3d[i]);
                //         std::cout << "statuses.size=" << statuses.size() << "   stmp=" << static_cast<int>(stmp)  << "   i=" << i << std::endl; 
                //         statuses[i] = stmp;
                //     }));
                // }
        
                for (auto& thread : threads) {
                    thread.join();
                }
        
                return statuses;
            }
        
        private:
            std::vector<Alarm*> alarms_;
        };


    int risk_level(DSMInfo alarm_infos);
    void init_DSMInfo_parmas(DSMInfo alarm_stdinfo_info);
    bool check_params(regions &region);
    void get_global_params(std::string yaml_path, globals globalstd); // ǰ�ڵ���ʹ��
    bool LoadConfigFile(const std::string &file_name, globals &globalstd);
    std::string readConfig(std::string file_path, const std::string &key, std::string def);
    int readConfigInt(std::string file_path, const std::string &key, int def);
    bool readConfigBool(std::string file_path, const std::string &key, bool def);
    float readConfigFloat(std::string file_path, const std::string &key, float def);
    int getDistance(cv::Point point1, cv::Point point2);
    bool cmpy(cv::Point const &a, cv::Point const &b);
    // subfunction

    std::string getStatusName(dsm_std::Status status);
    extern cv::Mat convertTo3Channels(const cv::Mat &binImg);

    class Kernelstd
    {
    public:
        static std::vector<dsm_std::Nodeflags> execute_alarm_box(std::vector<int> input, dsm_std::AlarmBox &box, std::vector<int> &timeout,std::vector<int> button_status);
        // 公有成员变量，可以在类的外部直接访问和修改
        std::vector<std::vector<int>> superposition;
        // 构造函数
        Kernelstd() : superposition(9, std::vector<int>(0)) {}

        int runs_detector(cv::Mat src_img, cv::Mat back_frame, dsm_std::YOLO5S yolo_model, DSMInfo &alarm_stdinfo, AlarmBox &box, int muv_speed, dsm_std::ExternalParam exterIOparam);
    };

    struct ad_angle
    {
        bool adjusted_button = false;
        float yaw_angle;
    };
    struct BELT_INFO
    {
        // 0:未标定 1:标定待确认 2:标定完成
        int belt_demarcate_state = 0;
        cv::Mat belt_frame;
        std::vector<cv::Point2i> five_point;
        int left_right_button;
    };

    struct ParamsToCheck
    {
        int yaw_angle;

        std::vector<cv::Point2i> five_points;
        int left_right_button;

        bool debug_button;
    };

    class SkewParam
    {
    public:
        static bool debug_button;
        static BELT_INFO belt_info;
        static ad_angle adjusted_angle;

        static void SetAngle(ad_angle ad_angle);

        static void SetBeltInfo(BELT_INFO belt_info);

        static BELT_INFO GetBeltInfo();

        static ad_angle GetAngle();
    };

    // bool Beltcalibration(cv::Mat frame);
    bool LoadbeltConfigFile(const std::string &file_name);
    bool check_debug_param(ParamsToCheck &params);
    void LoadDefaultParam(ParamsToCheck &params);
    void belt_point_deal(std::vector<cv::Point> &points, int left_right_button);
    static inline bool point_x_sort(cv::Point const &a, cv::Point const &b);
    static inline bool point_y_sort(cv::Point const &a, cv::Point const &b);

}
#endif