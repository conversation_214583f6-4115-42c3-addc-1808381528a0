/*
 *parameter: file_path ??��?����������: /user/home/<USER>
 *           key         ??��??����?   �� num
 *           def       	��?������?��?
 *	     readConfig ����string����   readConfigFloat ����float����  readConfigInt ����Int����
 *
 */
#include <string>
#include <fstream>
#include "std_run.h"
#include "std_dsmini.h"
#include "std_resnet18.h"
#include "G3_Configuration.h"

namespace dsm_std
{

    // ��?������
    std::string ini::Inputpth::Resnet_model_pth = "./model/std_resnet18C11.rknn";  //std_328resnet18C11     std_0206resnet18C11
    std::string ini::Inputpth::Resmod_lable_pth = "./model/std_resnet18.txt";
    // ??????��??��
    void ini::Inputpth::setSharedValue1(std::string newValue)
    {
        ini::Inputpth::Resnet_model_pth = newValue;
    }

    std::string ini::Inputpth::getSharedValue1()
    {
        return Resnet_model_pth;
    }
    void ini::Inputpth::setSharedValue2(std::string newValue)
    {
        ini::Inputpth::Resmod_lable_pth = newValue;
    }

    std::string ini::Inputpth::getSharedValue2()
    {
        return Resmod_lable_pth;
    }

    // 读取config.yml配置参数
    void get_global_params(std::string yaml_path, globals globalstd)
    {
        // 口罩识别
        //        globalstd.respirator_speed = readConfigInt(yaml_path, "respirator_speed", 10);
        //        globalstd.respirator_button = readConfigBool(yaml_path, "respirator_button", true);
        //        globalstd.event_respirator_time = readConfigInt(yaml_path, "event_respirator_time", 4000);
        //        globalstd.freeze_respirator_time = readConfigInt(yaml_path, "freeze_respirator_time", 2000);

        //1 闭眼识别
        globalstd.eye_speed = readConfigInt(yaml_path, "eye_speed", 10);
        globalstd.eye_button = readConfigBool(yaml_path, "eye_button", true);
        globalstd.eye_eventime = readConfigInt(yaml_path, "event_eye_time", 4000);
        globalstd.eye_freetime = readConfigInt(yaml_path, "freeze_eye_time", 2000);

        //2 哈切识别
        globalstd.mouth_speed = readConfigInt(yaml_path, "mouth_speed", 10);
        globalstd.mouth_button = readConfigBool(yaml_path, "mouth_button", true);
        globalstd.mouth_eventime = readConfigInt(yaml_path, "event_mouth_time", 4000);
        globalstd.mouth_freetime = readConfigInt(yaml_path, "freeze_mouth_time", 2000);

        //3 抽烟识别
        globalstd.smoking_speed = readConfigInt(yaml_path, "smoking_speed", 10);
        globalstd.smoking_button = readConfigBool(yaml_path, "smoking_button", true);
        globalstd.smoking_eventime = readConfigInt(yaml_path, "event_smoking_time", 4000);
        globalstd.smoking_freetime = readConfigInt(yaml_path, "freeze_smoking_time", 2000);

        //4 打电话识别
        globalstd.phone_speed = readConfigInt(yaml_path, "phone_speed", 10);
        globalstd.phone_button = readConfigBool(yaml_path, "phone_button", true);
        globalstd.phone_eventime = readConfigInt(yaml_path, "event_phone_time", 4000);
        globalstd.phone_freetime = readConfigInt(yaml_path, "freeze_phone_time", 2000);

        //5 帽子识别
        globalstd.hat_speed = readConfigInt(yaml_path, "hat_speed", 10);
        globalstd.hat_button = readConfigBool(yaml_path, "hat_button", true);
        globalstd.hat_eventime = readConfigInt(yaml_path, "event_hat_time", 4000);
        globalstd.hat_freetime = readConfigInt(yaml_path, "freeze_hat_time", 2000);

        //6 安全带识别
        globalstd.phone_speed = readConfigInt(yaml_path, "phone_speed", 10);
        globalstd.phone_button = readConfigBool(yaml_path, "phone_button", true);
        globalstd.phone_eventime = readConfigInt(yaml_path, "event_phone_time", 4000);
        globalstd.phone_freetime = readConfigInt(yaml_path, "freeze_phone_time", 2000);

        //7 分神识别
        globalstd.lookaround_speed = readConfigInt(yaml_path, "lookaround_speed", 10);
        globalstd.lookaround_button = readConfigBool(yaml_path, "lookaround_button", true);
        globalstd.lookaround_eventime = readConfigInt(yaml_path, "event_lookaround_time", 4000);
        globalstd.lookaround_freetime = readConfigInt(yaml_path, "freeze_lookaround_time", 2000);

        //8 离岗识别
        globalstd.facemissing_speed = readConfigInt(yaml_path, "facemissing_speed", 10);
        globalstd.facemissing_button = readConfigBool(yaml_path, "facemissing_button", true);
        globalstd.facemissing_eventime = readConfigInt(yaml_path, "event_facemissing_time", 4000);
        globalstd.facemissing_freetime = readConfigInt(yaml_path, "freeze_facemissing_time", 2000);

        //9 盲点识别
        globalstd.Blindspot_speed = readConfigInt(yaml_path, "blindspot_speed", 10);
        globalstd.Blindspot_button = readConfigBool(yaml_path, "blindspot_button", true);
        globalstd.Blindspot_eventime = readConfigInt(yaml_path, "event_blindspot_time", 4000);
        globalstd.Blindspot_freetime = readConfigInt(yaml_path, "freeze_blindspot_time", 2000);

        //10 摄像头遮挡识别
        globalstd.camcover_speed = readConfigInt(yaml_path, "camcover_speed", 10);
        globalstd.camcover_button = readConfigBool(yaml_path, "camcover_button", true);
        globalstd.camcover_eventime = readConfigInt(yaml_path, "event_camcover_time", 4000);
        globalstd.camcover_freetime = readConfigInt(yaml_path, "freeze_camcover_time", 2000);

        // ��??��?  ��?��?????
        //        globalstd.gesture_button = readConfigBool(yaml_path, "gesture_button", true);
        //        globalstd.gesture_sign = readConfigInt(yaml_path, "event_phone_time", 0);
        //
        //        cout << "respirator_button is " << (bool)globalstd.respirator_button << endl;
    }

    // MRV220赋值
    // 读取config.yml配置参数
    bool LoadConfigFile(const std::string &file_name, globals &globalstd)
    {
        //[1] 闭眼
        regions eye;
        eye.speed = G3_Configuration::getInstance().getEyeSpeed();          // 旭哥赋值
        eye.button =  G3_Configuration::getInstance().getEyeButton();         //旭哥赋值
        eye.mode =  G3_Configuration::getInstance().getEyeMode();           // 旭哥赋值
        eye.sensitivity =  G3_Configuration::getInstance().getEyeSensitivity();   // 旭哥赋值
        eye.event_time =  G3_Configuration::getInstance().getEventEyeTime();  // 旭哥赋值
        eye.freeze_time =  G3_Configuration::getInstance().getFreezeEyeTime(); // 旭哥赋值
        bool stat_eye = check_params(eye);
        globalstd.eye_speed = eye.speed;
        globalstd.eye_button = eye.button;
        globalstd.eye_mode = eye.mode;
        globalstd.eye_sensitivity = eye.sensitivity;
        globalstd.eye_eventime = eye.event_time;
        globalstd.eye_freetime = eye.freeze_time;
        //[2] 哈切
        regions mouth;
        mouth.speed = G3_Configuration::getInstance().getMouthSpeed();          // 旭哥赋值
        mouth.button =  G3_Configuration::getInstance().getMouthButton();         //旭哥赋值
        mouth.mode =  G3_Configuration::getInstance().getMouthMode();           // 旭哥赋值
        mouth.sensitivity =  G3_Configuration::getInstance().getMouthSensitivity();   // 旭哥赋值
        mouth.event_time =  G3_Configuration::getInstance().getEventMouthTime();  // 旭哥赋值
        mouth.freeze_time =  G3_Configuration::getInstance().getFreezeMouthTime(); // 旭哥赋值
        bool stat_mouth = check_params(mouth);
        globalstd.mouth_speed = mouth.speed;
        globalstd.mouth_button = mouth.button;
        globalstd.mouth_mode = mouth.mode;
        globalstd.mouth_sensitivity = mouth.sensitivity;
        globalstd.mouth_eventime = mouth.event_time;
        globalstd.mouth_freetime = mouth.freeze_time;
        //[3] 抽烟
        regions smoking;
        smoking.speed = G3_Configuration::getInstance().getSmokingSpeed(); // 旭哥赋值
        smoking.button = G3_Configuration::getInstance().getSmokingButton();// 旭哥赋值
        smoking.mode = G3_Configuration::getInstance().getSmokingMode();// 旭哥赋值
        smoking.sensitivity = G3_Configuration::getInstance().getSmokingSensitivity();// 旭哥赋值
        smoking.event_time = G3_Configuration::getInstance().getEventSmokingTime();// 旭哥赋值
        smoking.freeze_time = G3_Configuration::getInstance().getFreezeSmokingTime();// 旭哥赋值
        bool stat_smoking = check_params(smoking);
        globalstd.smoking_speed = smoking.speed;
        globalstd.smoking_button = smoking.button;
        globalstd.smoking_mode = smoking.mode;
        globalstd.smoking_sensitivity = smoking.sensitivity;
        globalstd.smoking_eventime = smoking.event_time;
        globalstd.smoking_freetime = smoking.freeze_time;
        //[4] 打电话
        regions phone;
        phone.speed = G3_Configuration::getInstance().getPhoneSpeed();          // 旭哥赋值
        phone.button =  G3_Configuration::getInstance().getPhoneButton();         //旭哥赋值
        phone.mode =  G3_Configuration::getInstance().getPhoneMode();           // 旭哥赋值
        phone.sensitivity =  G3_Configuration::getInstance().getPhoneSensitivity();   // 旭哥赋值
        phone.event_time =  G3_Configuration::getInstance().getEventPhoneTime();  // 旭哥赋值
        phone.freeze_time =  G3_Configuration::getInstance().getFreezePhoneTime(); // 旭哥赋值
        bool stat_phone = check_params(phone);
        globalstd.phone_speed = phone.speed;
        globalstd.phone_button = phone.button;
        globalstd.phone_mode = phone.mode;
        globalstd.phone_sensitivity = phone.sensitivity;
        globalstd.phone_eventime = phone.event_time;
        globalstd.phone_freetime = phone.freeze_time;
        //[5] 帽子
        regions hat;
        hat.speed = G3_Configuration::getInstance().getRespiratorSpeed();          // 旭哥赋值
        hat.button =  G3_Configuration::getInstance().getRespiratorButton();         //旭哥赋值
        hat.mode =  G3_Configuration::getInstance().getRespiratorMode();           // 旭哥赋值
        hat.sensitivity =  G3_Configuration::getInstance().getRespiratorSensitivity();   // 旭哥赋值
        hat.event_time =  G3_Configuration::getInstance().getEventRespiratorTime();  // 旭哥赋值
        hat.freeze_time =  G3_Configuration::getInstance().getFreezeRespiratorTime(); // 旭哥赋值
        bool stat_hat = check_params(hat);
        globalstd.hat_speed = hat.speed;
        globalstd.hat_button = hat.button;
        globalstd.hat_mode = hat.mode;
        globalstd.hat_sensitivity = hat.sensitivity;
        globalstd.hat_eventime = hat.event_time;
        globalstd.hat_freetime = hat.freeze_time;
        //[6]安全带
        regions seat_belt;
        seat_belt.speed = G3_Configuration::getInstance().getSeatbeltSpeed();          // 旭哥赋值
        seat_belt.button = G3_Configuration::getInstance().getSeatbeltButton();         // 旭哥赋值
        seat_belt.mode = G3_Configuration::getInstance().getSeatbeltMode();           // 旭哥赋值
        seat_belt.sensitivity = G3_Configuration::getInstance().getSeatbeltSensitivity();  // 旭哥赋值
        seat_belt.event_time = G3_Configuration::getInstance().getEventSeatbeltTime();  // 旭哥赋值
        seat_belt.freeze_time = G3_Configuration::getInstance().getFreezeSeatbeltTime(); // 旭哥赋值
        bool stat_seatbelt = check_params(seat_belt);
        globalstd.seatbelt_speed = seat_belt.speed;
        globalstd.seatbelt_button = seat_belt.button;
        globalstd.seatbelt_mode = seat_belt.mode;
        globalstd.seatbelt_sensitivity = seat_belt.sensitivity;
        globalstd.seatbelt_eventime = seat_belt.event_time;
        globalstd.seatbelt_freetime = seat_belt.freeze_time;
      
        //[7]盲点检测
        regions Blind_spot;
        Blind_spot.speed = G3_Configuration::getInstance().getBlindspotSpeed();  // 旭哥赋值
        Blind_spot.button = G3_Configuration::getInstance().getBlindspotButton(); // 旭哥赋值
        Blind_spot.mode = G3_Configuration::getInstance().getBlindspotMode(); // 旭哥赋值
        Blind_spot.sensitivity = G3_Configuration::getInstance().getBlindspotSensitivity();  // 旭哥赋值
        Blind_spot.event_time = G3_Configuration::getInstance().getEventBlindspotTime();  // 旭哥赋值
        Blind_spot.freeze_time = G3_Configuration::getInstance().getFreezeBlindspotTime(); // 旭哥赋值
        bool stat_Blindspot = check_params(Blind_spot);
        globalstd.Blindspot_speed = Blind_spot.speed;
        globalstd.Blindspot_button = Blind_spot.button;
        globalstd.Blindspot_mode = Blind_spot.mode;
        globalstd.Blindspot_sensitivity = Blind_spot.sensitivity;
        globalstd.Blindspot_eventime = Blind_spot.event_time;
        globalstd.Blindspot_freetime = Blind_spot.freeze_time;
        //[8]分神检测
        regions lookaround;
        lookaround.speed = G3_Configuration::getInstance().getLookaroundSpeed();
        lookaround.button = G3_Configuration::getInstance().getLookaroundButton();
        lookaround.mode = G3_Configuration::getInstance().getLookaroundMode();
        lookaround.sensitivity = G3_Configuration::getInstance().getLookaroundSensitivity();
        lookaround.event_time = G3_Configuration::getInstance().getEventLookaroundTime();
        lookaround.freeze_time = G3_Configuration::getInstance().getFreezeLookaroundTime();
        bool stat_lookaround = check_params(lookaround);
        globalstd.lookaround_speed = lookaround.speed;
        globalstd.lookaround_button = lookaround.button;
        globalstd.lookaround_mode = lookaround.mode;
        globalstd.lookaround_sensitivity = lookaround.sensitivity;
        globalstd.lookaround_eventime = lookaround.event_time;
        globalstd.lookaround_freetime = lookaround.freeze_time;
        //[9]离岗检测
        regions facemissing;
        facemissing.speed = G3_Configuration::getInstance().getFacemissingSpeed();
        facemissing.button = G3_Configuration::getInstance().getFacemissingButton();
        facemissing.mode = G3_Configuration::getInstance().getFacemissingMode();
        facemissing.sensitivity = G3_Configuration::getInstance().getFacemissingSensitivity();
        facemissing.event_time = G3_Configuration::getInstance().getEventFacemissingTime();
        facemissing.freeze_time = G3_Configuration::getInstance().getFreezeFacemissingTime();
        bool stat_facemissing = check_params(facemissing);
        globalstd.facemissing_speed = facemissing.speed;
        globalstd.facemissing_button = facemissing.button;
        globalstd.facemissing_mode = facemissing.mode;
        globalstd.facemissing_sensitivity = facemissing.sensitivity;
        globalstd.facemissing_eventime = facemissing.event_time;
        globalstd.facemissing_freetime = facemissing.freeze_time;
        //[10]遮挡检测
        regions camcover;
        camcover.speed = G3_Configuration::getInstance().getCamcoverSpeed();
        camcover.button = G3_Configuration::getInstance().getCamcoverButton();
        camcover.mode = G3_Configuration::getInstance().getCamcoverMode();
        camcover.sensitivity = G3_Configuration::getInstance().getCamcoverSensitivity();
        camcover.event_time = G3_Configuration::getInstance().getEventCamcoverTime();
        camcover.freeze_time = G3_Configuration::getInstance().getFreezeCamcoverTime();
        bool stat_camcover = check_params(camcover);
        globalstd.camcover_speed = camcover.speed;
        globalstd.camcover_button = camcover.button;
        globalstd.camcover_mode = camcover.mode;
        globalstd.camcover_sensitivity = camcover.sensitivity;
        globalstd.camcover_eventime = camcover.event_time;
        globalstd.camcover_freetime = camcover.freeze_time;
        // 动静检测
        globalstd.detect_move_button = G3_Configuration::getInstance().getDetectMoveButton();
        globalstd.move_pixel = G3_Configuration::getInstance().getMovePixel();
        globalstd.frame_early = G3_Configuration::getInstance().getFrameEarly();

        return true;
    }

    std::string readConfig(std::string file_path, const std::string &key, std::string def)
    {
        const char *cfgfilepath = file_path.c_str();
        std::fstream cfgFile;
        std::string value;
        std::string no_value = "error";
        cfgFile.open(cfgfilepath); // ��???
        if (!cfgFile.is_open())
        {
            std::cout << "can not open cfg file!" << std::endl;
            return no_value;
        }
        char tmp[100];
        while (!cfgFile.eof()) //?����???��
        {
            cfgFile.getline(tmp, 100); //??????1000��??��?1000��?��??��
            std::string line(tmp);
            size_t pos = line.find('='); //????????=����?��???��key?����value
            if (pos == std::string::npos)
                return def;
            std::string tmpKey = line.substr(0, pos); //?=��??
            if (key == tmpKey)
            {
                value = line.substr(pos + 1); //?=��?��
                return value;
            }
        }
        return def;
    }

    float readConfigFloat(std::string file_path, const std::string &key, float def)
    {
        const char *cfgfilepath = file_path.c_str();
        std::fstream cfgFile;
        std::string value;
        float no_value = -1;
        cfgFile.open(cfgfilepath); // ��???
        if (!cfgFile.is_open())
        {
            std::cout << "can not open cfg file!" << std::endl;
            return no_value;
        }
        char tmp[100];
        while (!cfgFile.eof()) //?����???��
        {
            cfgFile.getline(tmp, 100); //??????1000��??��?1000��?��??��
            std::string line(tmp);
            size_t pos = line.find('='); //????????=����?��???��key?����value
            if (pos == std::string::npos)
                return def;
            std::string tmpKey = line.substr(0, pos); //?=��??
            if (key == tmpKey)
            {
                value = line.substr(pos + 1); //?=��?��
                return atof(value.c_str());
            }
        }
        return def;
    }

    int readConfigInt(std::string file_path, const std::string &key, int def)
    {
        const char *cfgfilepath = file_path.c_str();
        std::fstream cfgFile;
        std::string value;
        int no_value = -1;
        cfgFile.open(cfgfilepath); // ��???
        if (!cfgFile.is_open())
        {
            std::cout << "can not open cfg file!" << std::endl;
            return no_value;
        }
        char tmp[100];
        while (!cfgFile.eof()) //?����???��
        {
            cfgFile.getline(tmp, 100); //??????100��??��?100��?��??��
            std::string line(tmp);
            size_t pos = line.find('='); //????????=����?��???��key?����value
            if (pos == std::string::npos)
                return def;
            std::string tmpKey = line.substr(0, pos); //?=��??
            if (key == tmpKey)
            {
                value = line.substr(pos + 1); //?=��?��
                return atoi(value.c_str());
            }
        }
        return def;
    }

    bool readConfigBool(std::string file_path, const std::string &key, bool def)
    {
        const char *cfgfilepath = file_path.c_str();
        std::fstream cfgFile;
        std::string value;
        int no_value = -1;
        cfgFile.open(cfgfilepath); // ��???
        if (!cfgFile.is_open())
        {
            std::cout << "can not open cfg file!" << std::endl;
            return no_value;
        }
        char tmp[100];
        while (!cfgFile.eof()) //?����???��
        {
            cfgFile.getline(tmp, 100); //??????100��??��?100��?��??��
            std::string line(tmp);
            size_t pos = line.find('='); //????????=����?��???��key?����value
            if (pos == std::string::npos)
                return def;
            std::string tmpKey = line.substr(0, pos); //?=��??
            if (key == tmpKey)
            {
                value = line.substr(pos + 1); //?=��?��
                return atoi(value.c_str());
            }
        }
        return def;
    }

    bool check_params(regions &region)
    {
        int stat_false = 0;
        if ((region.speed >= 0) && (region.speed < 180))
        {
            ;
        }
        else
        {
            // ������??��??��?
            region.speed = 5;
            stat_false = 1;
        }

        if ((region.button == 0) || (region.button == 1))
        {
            ;
        }
        else
        {
            // ������??��??��?
            region.button = 0;
            stat_false = 1;
        }

        if ((region.mode >= 0) || (region.mode <= 100))
        {
            ;
        }
        else
        {
            region.mode = 10;
            stat_false = 1;
        }

        if ((region.sensitivity >= 0) && (region.sensitivity <= 100))
        {
            ;
        }
        else
        {
            region.sensitivity = 80;
            stat_false = 1;
        }

        if ((region.event_time >= 0) && (region.event_time <= 3600000)) // 60min
        {
            ;
        }
        else
        {
            // ������??��??��?
            region.event_time = 5000;
            stat_false = 1;
        }

        if ((region.freeze_time >= 0) && (region.freeze_time <= 3600000))
        {
            ;
        }
        else
        {
            // ������??��??��?
            region.freeze_time = 5000;
            stat_false = 1;
        }
        if (stat_false == 1)
        {
            return false;
        }
        else
        {
            return true;
        }
    }

    static inline bool point_x_sort(cv::Point const &a, cv::Point const &b)
    {
        return a.x > b.x;
    }
    static inline bool point_y_sort(cv::Point const &a, cv::Point const &b)
    {
        return a.y < b.y;
    }

    // ��������?��?��??? ��??��?double����
    int getDistance(cv::Point point1, cv::Point point2)
    {
        int distance = sqrtf(powf((point1.x - point2.x), 2) + powf((point1.y - point2.y), 2));
        return distance;
    }

    void LoadDefaultParam(ParamsToCheck &params)
    {
        params.yaw_angle = 0;
        params.debug_button = 0;
        params.left_right_button = 0;
        // std::vector<cv::Point> sourcePoints = { cv::Point(502,691), cv::Point(277,894), cv::Point(282,1010), cv::Point(465,1026), cv::Point(618,695) };
        // 640x640下初始坐标：
        // std::vector<cv::Point> sourcePoints = {cv::Point(391, 345), cv::Point(278, 447), cv::Point(281, 505), cv::Point(372, 513), cv::Point(449, 347)};
        std::vector<cv::Point> sourcePoints = {cv::Point(1120, 300), cv::Point(1120, 450), cv::Point(1216, 450), cv::Point(1216, 300), cv::Point(1216, 300)};  // 20250331

        sourcePoints.swap(params.five_points);
    }

    bool check_debug_param(ParamsToCheck &params)
    {
        bool check_state = true;

        if (params.yaw_angle > 50 || params.yaw_angle < -50)
        {
            params.yaw_angle = 0;
            check_state = false;
        }

        if (!(params.debug_button == 0 || params.debug_button == 1))
        {
            params.debug_button = 0;
            check_state = false;
        }

        // 检查安全带坐标是否越界
        bool belt_state = true;
        if (params.five_points.size() != 5)
        {
             
            belt_state = false;
        }
        for (size_t i = 0; i < params.five_points.size(); i++)
        {
            if (params.five_points[i].x < 0 || params.five_points[i].x > 1279 || params.five_points[i].y < 0 || params.five_points[i].y > 719)
            // 640x640下限界：
            // if (params.five_points[i].x <= 140 || params.five_points[i].x > 499 || params.five_points[i].y <= 0 || params.five_points[i].y > 639)
            {
                belt_state = false;
             
            }
                
        }
        if (!(params.left_right_button == 0 || params.left_right_button == 1))
        {
            params.left_right_button = 0;
            belt_state = false;
        }
        // std::vector<cv::Point> sourcePoints = { cv::Point(502,691), cv::Point(277,894), cv::Point(282,1010), cv::Point(465,1026), cv::Point(618,695) };
        // 640x640下初始坐标：
        std::vector<cv::Point> sourcePoints = {cv::Point(391, 345), cv::Point(278, 447), cv::Point(281, 505), cv::Point(372, 513), cv::Point(449, 347)};
        // std::vector<cv::Point> sourcePoints = {cv::Point(1120, 300), cv::Point(1120, 450), cv::Point(1216, 450), cv::Point(1216, 300), cv::Point(1216, 300)}; //20250331
        if (belt_state == false) // 替换默认坐标点
        {
            std::cout << "=========================替换默认坐标、并设置左舵========================" << std::endl;
            sourcePoints.swap(params.five_points);
            params.left_right_button = 0;
            check_state = false;
        }

        return check_state;
    }
    void belt_point_deal(std::vector<cv::Point> &points, int left_right_button)
    {
        std::vector<cv::Point> five_point_vector;
        points.swap(five_point_vector);
        // vector->deque
        std::deque<cv::Point> five_point_deque;
        std::move(five_point_vector.begin(), five_point_vector.end(), std::back_inserter(five_point_deque));

        // x坐标从大到小排序
        std::sort(five_point_deque.begin(), five_point_deque.end(), point_x_sort);

        cv::Point p0, p1, p2, p3, p4;

        if (left_right_button == 0)
        {
            // 左舵
            p4 = five_point_deque[0];
            five_point_deque.pop_front();

            std::sort(five_point_deque.begin(), five_point_deque.end(), point_y_sort);
            p0 = five_point_deque[0];
            p1 = five_point_deque[1];
            five_point_deque.pop_front();
            five_point_deque.pop_front();
            std::sort(five_point_deque.begin(), five_point_deque.end(), point_x_sort);
            p2 = five_point_deque[1];
            p3 = five_point_deque[0];
        }
        else
        {
            // 右舵
            p4 = five_point_deque[4];
            five_point_deque.pop_back();
            std::sort(five_point_deque.begin(), five_point_deque.end(), point_y_sort);
            p0 = five_point_deque[0];
            p1 = five_point_deque[1];
            five_point_deque.pop_front();
            five_point_deque.pop_front();
            std::sort(five_point_deque.begin(), five_point_deque.end(), point_x_sort);
            p2 = five_point_deque[0];
            p3 = five_point_deque[1];
        }

        five_point_vector.clear();
        five_point_vector.push_back(p0);
        five_point_vector.push_back(p1);
        five_point_vector.push_back(p2);
        five_point_vector.push_back(p3);
        five_point_vector.push_back(p4);

        five_point_vector.swap(points);
    }
}