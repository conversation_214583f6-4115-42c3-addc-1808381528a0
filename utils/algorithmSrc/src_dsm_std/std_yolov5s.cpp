#include <sstream>
#include <iostream>
#include <opencv2/dnn.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>
#include "std_yolov5s.h"
#include "std_face_angle.h"
#include "std_cut_faceroi.h"

#ifdef DSM_USING_RKNN
#include "dsmstdPostprocess.h"
#endif


dsm_std::face_message face_params_std;
std::vector<cv::Point> vec_frame_std;
extern dsm_std::DSMInfo alarm_stdinfo;
extern dsm_std::globals globalstd;
static inline float sigmoid_x(float x);

namespace dsm_std{
#ifdef DSM_USING_RKNN
    rknn_context yolov5_ctx;
    rknn_input_output_num  io_num;
#else
    Net net;
#endif
};

#ifdef DSM_USING_RKNN
/*-------------------------------------------
                  Functions
-------------------------------------------*/
static void printRKNNTensor(rknn_tensor_attr *attr)
{
    printf("index=%d name=%s n_dims=%d dims=[%d %d %d %d] n_elems=%d size=%d fmt=%d type=%d qnt_type=%d fl=%d zp=%d scale=%f\n",
           attr->index, attr->name, attr->n_dims, attr->dims[3], attr->dims[2], attr->dims[1], attr->dims[0],
           attr->n_elems, attr->size, 0, attr->type, attr->qnt_type, attr->fl, attr->zp, attr->scale);
}

inline const char* get_type_string(rknn_tensor_type type)
{
    switch(type) {
    case RKNN_TENSOR_FLOAT32: return "FP32";
    case RKNN_TENSOR_FLOAT16: return "FP16";
    case RKNN_TENSOR_INT8: return "INT8";
    case RKNN_TENSOR_UINT8: return "UINT8";
    case RKNN_TENSOR_INT16: return "INT16";
    default: return "UNKNOW";
    }
}

inline const char* get_qnt_type_string(rknn_tensor_qnt_type type)
{
    switch(type) {
    case RKNN_TENSOR_QNT_NONE: return "NONE";
    case RKNN_TENSOR_QNT_DFP: return "DFP";
    case RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC: return "AFFINE";
    default: return "UNKNOW";
    }
}

inline const char* get_format_string(rknn_tensor_format fmt)
{
    switch(fmt) {
    case RKNN_TENSOR_NCHW: return "NCHW";
    case RKNN_TENSOR_NHWC: return "NHWC";
    default: return "UNKNOW";
    }
}

static void dump_tensor_attr(rknn_tensor_attr *attr)
{
    printf("  index=%d, name=%s, n_dims=%d, dims=[%d, %d, %d, %d], n_elems=%d, size=%d, fmt=%s, type=%s, qnt_type=%s, "
           "zp=%d, scale=%f\n",
           attr->index, attr->name, attr->n_dims, attr->dims[3], attr->dims[2], attr->dims[1], attr->dims[0],
           attr->n_elems, attr->size, get_format_string(attr->fmt), get_type_string(attr->type),
           get_qnt_type_string(attr->qnt_type), attr->zp, attr->scale);
}

double __stdget_us(struct timeval t) { return (t.tv_sec * 1000000 + t.tv_usec); }

static unsigned char *load_data(FILE *fp, size_t ofst, size_t sz)
{
    unsigned char *data;
    int ret;

    data = NULL;

    if (NULL == fp)
    {
        return NULL;
    }

    ret = fseek(fp, ofst, SEEK_SET);
    if (ret != 0)
    {
        printf("blob seek failure.\n");
        return NULL;
    }

    data = (unsigned char *)malloc(sz);
    if (data == NULL)
    {
        printf("buffer malloc failure.\n");
        return NULL;
    }
    ret = fread(data, 1, sz, fp);
    return data;
}

static unsigned char *load_model(const char *filename, int *model_size)
{

    FILE *fp;
    unsigned char *data;

    fp = fopen(filename, "rb");
    if (NULL == fp)
    {
        printf("Open file %s failed.\n", filename);
        return NULL;
    }

    fseek(fp, 0, SEEK_END);
    int size = ftell(fp);

    data = load_data(fp, 0, size);

    fclose(fp);

    *model_size = size;
    return data;
}
#endif

dsm_std::YOLO5S::YOLO5S(Net_config config)
{
    std::cout << "STD_Net use " << config.netname <<std::endl;
    this->confThreshold = config.confThreshold;
    this->nmsThreshold = config.nmsThreshold;
    this->objThreshold = config.objThreshold;

#ifdef DSM_USING_RKNN
#else
    strcpy_s(this->netname, config.netname.c_str());
#endif

#ifdef DSM_USING_RKNN
    char *yolov5_model_name = "./model/std_sumyoloface.rknn";

    int ret;

    /* Create the yolov5 neural network */
    printf("===============================================MUV Loading yolov5 mode=============================================\n");
    int model_data_size = 0;
    unsigned char *model_data = load_model(yolov5_model_name, &model_data_size);
    ret = rknn_init(&yolov5_ctx, model_data, model_data_size, 0);
    if (ret < 0)
    {
        printf("rknn_init error ret=%d\n", ret);
        return;
    }
    rknn_sdk_version version;
    ret = rknn_query(yolov5_ctx, RKNN_QUERY_SDK_VERSION, &version, sizeof(rknn_sdk_version));
    if (ret < 0)
    {
        printf("rknn_init error ret=%d\n", ret);
        return;
    }
    printf("sdk version: %s driver version: %s\n", version.api_version,
           version.drv_version);

    ret = rknn_query(yolov5_ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
    if (ret < 0)
    {
        printf("rknn_init error ret=%d\n", ret);
        return;
    }
    printf("model input num: %d, output num: %d\n", io_num.n_input, io_num.n_output);


    rknn_tensor_attr input_attrs[io_num.n_input];
    memset(input_attrs, 0, sizeof(input_attrs));
    for (int i = 0; i < io_num.n_input; i++)
    {
        input_attrs[i].index = i;
        ret = rknn_query(yolov5_ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]),
                         sizeof(rknn_tensor_attr));
        if (ret < 0)
        {
            printf("rknn_init error ret=%d\n", ret);
            return;
        }
        dump_tensor_attr(&(input_attrs[i]));
    }

    rknn_tensor_attr output_attrs[io_num.n_output];
    memset(output_attrs, 0, sizeof(output_attrs));
    for (int i = 0; i < io_num.n_output; i++)
    {
        output_attrs[i].index = i;
        ret = rknn_query(yolov5_ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]),
                         sizeof(rknn_tensor_attr));
        dump_tensor_attr(&(output_attrs[i]));
        if(output_attrs[i].qnt_type != RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC || output_attrs[i].type != RKNN_TENSOR_UINT8)
        {
            fprintf(stderr,"The Demo required for a Affine asymmetric u8 quantized rknn model, but output quant type is %s, output data type is %s\n",
                    get_qnt_type_string(output_attrs[i].qnt_type),get_type_string(output_attrs[i].type));
            return;
        }
    }
#else
    string modelFile = this->netname;
    modelFile += "-face.onnx";
    this->net = readNet(modelFile);
#endif
}

void dsm_std::YOLO5S::drawPred(float conf, int left, int top, int right, int bottom, cv::Mat& frame, std::vector<int> landmark)   // Draw the predicted bounding box
{
    //Draw a rectangle displaying the bounding box   ע��ԭ���Ļ�����
    cv::rectangle(frame, cv::Point(left, top), cv::Point(right, bottom), cv::Scalar(0, 0, 255), 2);

    //Get the label for the class name and its confidence
    std::string label = cv::format("%.8f", conf);

    //Display the label at the top of the bounding box
    int baseLine;
    cv::Size labelSize = cv::getTextSize(label, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);
    top = std::max(top, labelSize.height);
    // rectangle(frame, Point(left, top - int(1.5 * labelSize.height)), Point(left + int(1.5 * labelSize.width), top + baseLine), Scalar(0, 255, 0), FILLED);
    //putText(frame, label, Point(left, top), FONT_HERSHEY_SIMPLEX, 0.75, Scalar(0, 255, 0), 1);

    Tface sense_roi;
    //sense_roi.setroi();
    for (int i = 0; i < 5; i++)
    {
        cv::circle(frame, cv::Point(landmark[2 * i], landmark[2 * i + 1]), 1, cv::Scalar(128, 128, 0), -1);
    }
    // cv::imwrite("MUV_draw0327.jpg", frame);
}

void dsm_std::YOLO5S::sigmoid(cv::Mat* out, int length)
{
    float* pdata = (float*)(out->data);
    int i = 0;
    for (i = 0; i < length; i++)
    {
        pdata[i] = 1.0 / (1 + expf(-pdata[i]));
    }
}

dsm_std::face_message dsm_std::YOLO5S::detect_face(cv::Mat& frame, cv::Mat beltimg)  //, dsm_std::DSMInfo& alarm_stdinfo
{
    int inpWidth = 640;
    int inpHeight =640;
    std::vector<float> confidences;
    std::vector<int> indices;
    std::vector<cv::Rect> boxes;
    std::vector<std::vector<int>> landmarks;
    cv::Mat frame_copy = frame.clone();
    // imwrite("oor.jpg", frame_copy);
#ifdef DSM_USING_RKNN
    int ret = 0;
    rknn_input yolov5_inputs[1];
    memset(yolov5_inputs, 0, sizeof(yolov5_inputs));
    yolov5_inputs[0].index = 0;
    yolov5_inputs[0].type = RKNN_TENSOR_UINT8;  //RKNN_TENSOR_UINT8
    yolov5_inputs[0].size = inpWidth * inpHeight * 3;
    yolov5_inputs[0].fmt = RKNN_TENSOR_NHWC;    //RKNN_TENSOR_NHWC;demo����ԭ����NHWC,��ĳ���NCHW��
    yolov5_inputs[0].pass_through = 0;
// printf("***********detect_face in line %d  \n", __LINE__);
    yolov5_inputs[0].buf = frame.data;
    rknn_inputs_set(yolov5_ctx, io_num.n_input, yolov5_inputs);//��������ɫͨ����������һ����������NHWC ת���� NCHW �Ĺ���

    rknn_output yolov5_outputs[io_num.n_output];
    memset(yolov5_outputs, 0, sizeof(yolov5_outputs));
    for (int i = 0; i < io_num.n_output; i++)
        yolov5_outputs[i].want_float = 1;
// printf("***********detect_face in line %d  \n", __LINE__);
    ret = rknn_run(yolov5_ctx, NULL);
    ret = rknn_outputs_get(yolov5_ctx, io_num.n_output, yolov5_outputs, NULL);

    //post process
    detect_result_group_t detect_result_group;

    post_process_fp_std((float *)yolov5_outputs[0].buf, (float *)yolov5_outputs[1].buf, (float *)yolov5_outputs[2].buf, inpHeight, inpWidth,
                this->confThreshold, this->nmsThreshold, &detect_result_group);

    for (int i_cnt = 0; i_cnt < detect_result_group.count; i_cnt++)
    {
        int left = detect_result_group.results[i_cnt].box.left;
        int right = detect_result_group.results[i_cnt].box.right;
        int top = detect_result_group.results[i_cnt].box.top;
        int bottom = detect_result_group.results[i_cnt].box.bottom;

        boxes.push_back(cv::Rect(left, top, right - left, bottom - top));
        indices.push_back(i_cnt);
        std::vector<int> lm;
        for (int t = 0; t < detect_result_group.results[i_cnt].landmark.size(); t++){
            lm.push_back((int)detect_result_group.results[i_cnt].landmark[t]);
        }
        landmarks.push_back(lm);
        confidences.push_back(detect_result_group.results[i_cnt].prop);
    }

#else
    Mat blob;
    blobFromImage(frame, blob, 1 / 255.0, Size(this->inpWidth, this->inpHeight), Scalar(0, 0, 0), true, false);
    this->net.setInput(blob);
    vector<Mat> outs;
    this->net.forward(outs, this->net.getUnconnectedOutLayersNames());

    /////generate proposals
    float ratioh = (float)frame.rows / this->inpHeight, ratiow = (float)frame.cols / this->inpWidth;
    int n = 0, q = 0, i = 0, j = 0, nout = 16, row_ind = 0, k = 0; ///xmin,ymin,xamx,ymax,box_score,x1,y1, ... ,x5,y5,face_score
    for (n = 0; n < 3; n++)   ///����ͼ�߶�
    {
        int num_grid_x = (int)(this->inpWidth / this->stride[n]);
        int num_grid_y = (int)(this->inpHeight / this->stride[n]);
        for (q = 0; q < 3; q++)    ///anchor
        {
            const float anchor_w = this->anchors[n][q * 2];
            const float anchor_h = this->anchors[n][q * 2 + 1];
            for (i = 0; i < num_grid_y; i++)
            {
                for (j = 0; j < num_grid_x; j++)
                {
                    float* pdata = (float*)outs[0].data + row_ind * nout;
                    float box_score = sigmoid_x(pdata[4]);
                    if (box_score > this->objThreshold)
                    {
                        //cout<<endl << "pdata[15] : " << pdata[15] << endl;
                        float face_score = sigmoid_x(pdata[15]);
                        if (face_score > this->confThreshold)
                        {
                            float cx = (sigmoid_x(pdata[0]) * 2.f - 0.5f + j) * this->stride[n];  ///cx
                            float cy = (sigmoid_x(pdata[1]) * 2.f - 0.5f + i) * this->stride[n];   ///cy
                            float w = powf(sigmoid_x(pdata[2]) * 2.f, 2.f) * anchor_w;   ///w
                            float h = powf(sigmoid_x(pdata[3]) * 2.f, 2.f) * anchor_h;  ///h

                            int left = (cx - 0.5 * w) * ratiow;
                            int top = (cy - 0.5 * h) * ratioh;

                            confidences.push_back(face_score);
                            boxes.push_back(Rect(left, top, (int)(w * ratiow), (int)(h * ratioh)));
                            vector<int> landmark(10);
                            for (k = 5; k < 15; k += 2)
                            {
                                const int ind = k - 5;
                                landmark[ind] = (int)(pdata[k] * anchor_w + j * this->stride[n]) * ratiow;
                                landmark[ind + 1] = (int)(pdata[k + 1] * anchor_h + i * this->stride[n]) * ratioh;
                            }
                            landmarks.push_back(landmark);


                        }

                    }
                    row_ind++;
                }
            }
        }
    }

    // Perform non maximum suppression to eliminate redundant overlapping boxes with
    // lower confidences
    NMSBoxes(boxes, confidences, this->confThreshold, this->nmsThreshold, indices);
#endif
// printf("***********detect_face in line %d  \n", __LINE__);
    std::cout << "stdxxx=================================++ 人脸数量 indices.size() :   " <<  indices.size()  << std::endl;
    if (indices.size() < 1)
    {
        //没有人脸是返回值2
        face_params_std.state = 2;
        /* 没有人脸 */
        return face_params_std;
    }
    // printf("***********detect_face in line %d  \n", __LINE__);
    // else
    // {
    //     for (size_t i = 0; i < landmarks.size(); i++)
    //     {
    //         std::cout << "muv: =================================landmarks[i][0] 眼睛X " << landmarks[i][0] << std::endl;
    //         std::cout << "muv: =================================landmarks[i][1] 眼睛Y  " << landmarks[i][1] << std::endl;
    //         std::cout << "muv: =================================boxes[idx].width: 人脸X  " <<  boxes[i].x << std::endl;
    //         std::cout << "muv: =================================boxes[idx].height:人脸Y  " <<  boxes[i].y<< std::endl << std::endl;
    //         std::cout << "muv: =================================boxes[idx].width: 人脸宽度  " <<  boxes[i].width << std::endl;
    //         std::cout << "muv: =================================boxes[idx].height:人脸高度  " <<  boxes[i].height<< std::endl << std::endl;
    //     }
    // }

    cv::Rect box;
    int faces_ids = 0;
    std::vector<cv::Point> face_add;
    int idx = indices[0]; 
    
    //此处限定为最大人脸
    for (size_t i = 0; i < indices.size(); i++) {
        idx = indices[i];
        box = boxes[idx];
        int faces_state = 0;
        if (indices.size() == 1) {
            //过滤函数
            faces_state = invade_face(idx, box, landmarks);
            faces_ids++;
            //show
            // dsm_std::YOLO5S::drawPred(confidences[idx], box.x, box.y,box.x + box.width, box.y + box.height, frame, landmarks[idx]);
            // cv::imwrite("STD_yolo.jpg", frame);
            break;
        }
        //�������ǰ����������бȽ�


        //多个人脸过滤函数
        faces_state = invade_face(idx, box, landmarks);
        if (faces_state == -1)
        {
             continue; //剔除不合格人脸
        }
        else	    
        {
            faces_ids++;
        }
        if (faces_ids >= 5) {
            break;
        }
        //�����������??
        // dsm_std::YOLO5S::drawPred(confidences[idx], box.x, box.y,box.x + box.width, box.y + box.height, frame, landmarks[idx]);
        
        int facesize = box.width * box.height;
        face_add.push_back(cv::Point(idx, facesize));
    }

    if (!face_add.empty())
    {
        std::sort(face_add.begin(), face_add.end(), cmpy);
        idx = face_add[0].x;       
        std::cout << "=====输出最终std:********================================idx:   " <<  idx << std::endl;
    }


    //*****在此处添加动静检测功能*****
    int ok_state = 0;
    if (globalstd.detect_move_button == 1)
    {

        int p_distance = 0;
        //Point center_pos;
        //center_pos.x = box.br().x + box.width*0.5;
        //center_pos.y = box.br().y + box.height*0.5;  // ����Box����׼ȷ
        cv::Point nose_pos;
        nose_pos.x = landmarks[idx][4];
        nose_pos.y = landmarks[idx][5];

        //每�?�缓�?5帧率
        if (vec_frame_std.size() >= 5)
        {
            //cout << "vec_frame_std.size()�� " << vec_frame_std.size() << endl;
            //vec.erase(k);
            vec_frame_std.erase(vec_frame_std.begin());
            vec_frame_std.push_back(nose_pos);
            p_distance = getDistance(vec_frame_std[0], vec_frame_std[4]);
            //判断vector�?位移大小，返回状态�?
        }
        else
        {
            if (nose_pos.x == 0)
            {
                //���˿հ�֡��
            }
            else
            {
                vec_frame_std.push_back(nose_pos);
            }
            /* ��������ʼ��δ�ռ��㹻��֡�� */
            face_params_std.state = 4;  //ԭʼֵ3
            return face_params_std;
        }
        //当静止状态下，才�?以进行cut,移动状态下禁�?�识�?
		if (p_distance > globalstd.move_pixel)//45为可配置参数，像素�?
		{
			// 动态范围过大，不予识别�?
			face_params_std.state = 4;
			vec_frame_std.clear(); //清空�? 新的数据稳定4帧后 在�?�测条�?
			return face_params_std;
		}
		else
		{
			//符合动态�?�测�?�求
			ok_state = 1;
		}

    }
    //人脸靠近边缘时，人脸效果不佳，不予识�?
    int eye_mean = landmarks[idx][1] > landmarks[idx][3] ? landmarks[idx][1] : landmarks[idx][3];
    //眼睛Y靠近左右两侧边界的极限值
    int sface_limit = abs(eye_mean - boxes[idx].y); // box.y + box.height
    int mouth_mean = landmarks[idx][7] > landmarks[idx][9] ? landmarks[idx][7] : landmarks[idx][9];
    int xface_limit = abs(  640 - mouth_mean);
    // std::cout<<"xface_limit : "<<xface_limit <<std::endl;
    // std::cout<<"sface_limit : "<<sface_limit <<std::endl;
    //添加左右两侧人脸限制，之前没必要添加
    if ((sface_limit < 15) || (xface_limit < 15))
    {
        face_params_std.state = 4;
        // printf("***********detect_face in line %d  \n", __LINE__);
        return face_params_std;
    }

     if ((ok_state == 1) || (faces_ids >= 1))
    {
        //添加脸部的角度识别
        cv::Rect base_box;
        base_box.x = box.x;
        base_box.y = box.y;
        base_box.width = box.width;
        base_box.height = box.height;

        cv::Rect face_box;
        face_box.x = landmarks[idx][4];
        face_box.y = landmarks[idx][5];
        face_box.width = box.width;
        face_box.height = box.height;
        std::vector<float> angle_vec;
        angle_vec = dsm_std::Tface::face_angle(frame, box, landmarks[idx]);

        // std::cout << "std::6666666666666=================================原始角度 " << angle_vec[0] << std::endl;
        // std::cout << "std::6666666666666=================================标定后角度 " << angle_vec[3] << std::endl;
        float XDiff = dsm_std::Tcut::GetXDiff(landmarks[idx], boxes[idx]);
        //添加截取五官roi区域的功能
        cv::Mat eye_l(56, 56, CV_8UC3, cv::Scalar::all(0));
        cv::Mat eye_r(56, 56, CV_8UC3, cv::Scalar::all(0));
        cv::Mat mouth(100, 100, CV_8UC3, cv::Scalar::all(0));
        cv::Mat smoke(100, 100, CV_8UC3, cv::Scalar::all(0));
        cv::Mat phonel(100, 100, CV_8UC3, cv::Scalar::all(0));
        cv::Mat phoner(100, 100, CV_8UC3, cv::Scalar::all(0));
        cv::Mat hat(100, 100, CV_8UC3, cv::Scalar::all(0));
        // cv::Mat belt(100, 100, CV_8UC3, cv::Scalar::all(0));
        // dsm_std::Tface::draw_face(frame, face_box, base_box, angle_vec);
        // alarm_stdinfo.belt_point = { cv::Point(602, 191) };
        // std::cout << "muv:----------------------------------------acheck_debug_paramlmuv_info.belt_point =  " << alarm_stdinfo.belt_point.size() << std::endl;
        // 0208 工作内容
        eye_l = dsm_std::Tcut::cut_eye_L(frame_copy, angle_vec, landmarks[idx],XDiff);
        eye_r = dsm_std::Tcut::cut_eye_R(frame_copy, angle_vec, landmarks[idx],XDiff);
        mouth = dsm_std::Tcut::cut_mouth(frame_copy, angle_vec, landmarks[idx]); //用抽烟roi代替哈切ROI
        smoke = dsm_std::Tcut::cut_smoke(frame_copy, boxes[idx], angle_vec, landmarks[idx], XDiff);
        phonel = dsm_std::Tcut::cut_phone_l(frame_copy, boxes[idx], angle_vec, landmarks[idx], XDiff);
        phoner = dsm_std::Tcut::cut_phone_r(frame_copy, boxes[idx], angle_vec, landmarks[idx], XDiff);  
        hat = dsm_std::Tcut::cut_hat(frame_copy, boxes[idx], angle_vec, XDiff);
// printf("***********cut_belt in line %d  \n", __LINE__);
        // belt = dsm_std::Tcut::cut_belt(frame_copy, boxes[idx], angle_vec);
        // std::cout << "std:----------------------------------------belt.size() =  " << belt.size() << std::endl;
// printf("***********cut_belt in line %d  \n", __LINE__);
        // imwrite("stdeye.jpg", eye_l);
        // imwrite("stdeyer.jpg", eye_r);
        // imwrite("stdmouth.jpg", mouth);
        // imwrite("stdphonel.jpg", phonel);
        // imwrite("stdphoner.jpg", phoner);
        // imwrite("stdhat.jpg", hat);
        // imwrite("stdbelt.jpg", belt);
        // imwrite("stdsmoke.jpg", smoke);
// printf("***********cut_belt in line %d  \n", __LINE__);  //验证此处的情况  有空
        if ((eye_l.channels() == 1) || (eye_r.channels() == 1) || (smoke.channels() == 1))
        {
            eye_l = eye_l.reshape(3, 0);
            eye_r = eye_r.reshape(3, 0);
            smoke = smoke.reshape(3, 0);
        }

        //过滤不合格的ROI
        int elseimg = (box.height + 90 ) - box.width;
        float rightleftbase = box.height / box.width;
        if (eye_l.empty() || eye_r.empty() || smoke.empty()||box.width>=639||box.height>=639 || elseimg <=0 || rightleftbase < 0.32 || rightleftbase> 2.8 || boxes[idx].width <10)
        {
            face_params_std.state = 4;
            std::cout << "xxx=================================eye_l.empty() or ROI超限 "  << std::endl;
            return face_params_std;
        }
        else
        {
            face_params_std.state = 1;
        }
        //返回值 状态时1  证明有人脸状态。 有人脸
        face_params_std.frame = frame;
        face_params_std.face_roi.clear();
        //报警信息传递 4号线人脸矩形框++++++
        if ((angle_vec[0] < 10) || (angle_vec[0] > -10))
        {
            alarm_stdinfo.face_score = confidences[idx];
        }
        else
        {
            alarm_stdinfo.face_score = 0;
        }
        face_params_std.face_angle.swap(angle_vec);
        if (eye_l.rows >5)
        {
            face_params_std.face_roi.push_back(eye_l); 
        }
        if (eye_r.rows >5)
        {
            face_params_std.face_roi.push_back(eye_r); 
        }
        if (mouth.rows >5)
        {
            face_params_std.face_roi.push_back(mouth); 
        }
        if (smoke.rows >5)
        {
            face_params_std.face_roi.push_back(smoke); 
        }
        if (phonel.rows >5)
        {
            face_params_std.face_roi.push_back(phonel); 
        }
        if (phoner.rows >5)
        {
            face_params_std.face_roi.push_back(phoner); 
        }
        if (hat.rows >5)
        {
            face_params_std.face_roi.push_back(hat); 
        }
        if (face_params_std.face_roi.size()<7)
        {
            face_params_std.state = 4;
            std::cout << "std:=================================识别到人脸，但五官截取的某个图片截取失败 "  << std::endl;
            return face_params_std;
        }
        
        if ((globalstd.seatbelt_button) && (beltimg.rows >5))
        {
            face_params_std.face_roi.push_back(beltimg); // 安全带为空的判断
        }
        
        face_params_std.landmarks = landmarks[idx];

        cv::Point2i face_zs = cv::Point2i(boxes[idx].x, boxes[idx].y);   //修改为boxes[idx]
        cv::Point2i face_yx = cv::Point2i(boxes[idx].x + boxes[idx].width, boxes[idx].y + boxes[idx].height);
        alarm_stdinfo.face_point.clear();
        alarm_stdinfo.face_point.push_back(face_zs);
        alarm_stdinfo.face_point.push_back(face_yx);
        std::cout << "xxx=================================face_zs人脸坐标 "  <<face_zs<< std::endl;
        std::cout << "xxx=================================face_yx人脸坐标 "  <<face_yx<< std::endl;
        //五官特征点
        cv::Point2i landmark1 = cv::Point2i(landmarks[idx][0], landmarks[idx][1]);
        cv::Point2i landmark2 = cv::Point2i(landmarks[idx][2], landmarks[idx][3]);
        cv::Point2i landmark3 = cv::Point2i(landmarks[idx][4], landmarks[idx][5]);
        cv::Point2i landmark4 = cv::Point2i(landmarks[idx][6], landmarks[idx][7]);
        cv::Point2i landmark5 = cv::Point2i(landmarks[idx][8], landmarks[idx][9]);
        alarm_stdinfo.five_point.clear();
        alarm_stdinfo.five_point.push_back(landmark1);
        alarm_stdinfo.five_point.push_back(landmark2);
        alarm_stdinfo.five_point.push_back(landmark3);
        alarm_stdinfo.five_point.push_back(landmark4);
        alarm_stdinfo.five_point.push_back(landmark5);

        //状态识别resnet18和报警逻辑，放在人脸识别外部，解决没有人脸时 意外打断的情况
        return face_params_std;
    }
    else
    {
        face_params_std.state = 4;
        // printf("***********detect_face in line %d  \n", __LINE__);
        return face_params_std;
    }
}



static inline float sigmoid_x(float x)
{
    return static_cast<float>(1.f / (1.f + exp(-x)));
}

//过滤人脸信息   摄像头旋转时注意更改  4号线固定位置过滤已过滤
int dsm_std::YOLO5S::invade_face(int idx, cv::Rect box, std::vector<std::vector<int>> landmarks)
{
    int faces_states = 0;
    int pos_height = landmarks[idx][9] - landmarks[idx][1];
    int pos_whith = landmarks[idx][2] - landmarks[idx][0];

    // std::cout << "the 0429 xu is very good works, are you ok???  pos_height xu is superman"  <<pos_height<<std::endl;
    //  std::cout << "xuxuxuxxuuxuxuxuu1111111pos_height:  "  <<pos_height<<std::endl;
    //   std::cout << "xuxuxuxxuuxuxuxuu1111111*******************pos_whith:  " <<pos_whith <<std::endl;

    if (abs(pos_height) > 148 || abs(pos_whith) > 130) //160
    {
        face_params_std.state = 4;
        //continue;
        //   printf("***********detect_face in line %d  \n", __LINE__);
        faces_states =  -1;
        // std::cout << "xuxuxuxxuuxuxuxuu1111111"  <<std::endl;
        return faces_states;
    }
    else {
        faces_states = 1;
    }
    // printf("***********detect_face in line %d  \n", __LINE__);
    //过滤小于60*60的脸 1米以内????
    //   std::cout << "xuxuxuxxuuxuxuxuu**************333333box.width:  "  <<box.width<<std::endl;
    //     std::cout << "xuxuxuxxuuxuxuxuu333333box.height:  " <<box.height <<std::endl;
    if ((box.width <= 50) || (box.height < 70))
    {
        face_params_std.state = 4; //������ ��С��ʱ Ӧ��������ڵ�ʶ��??
        //drawPred(confidences[idx], box.x, box.y, box.x + box.width, box.y + box.height, frame, landmarks[idx]);
        //continue;
        // std::cout << "xuxuxuxxuuxuxuxuu222222******************box.width:  "  <<box.width<<std::endl;
        // std::cout << "xuxuxuxxuuxuxuxuu222222box.height:  " <<box.height <<std::endl;
        faces_states = -1;
        //  printf("***********detect_face in line %d  \n", __LINE__);
        //return face_params_std;
    }
    else
    {
        faces_states = 1;
    }
    return faces_states;
}
