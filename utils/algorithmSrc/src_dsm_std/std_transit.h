#ifndef STD_TRANSIT_H_
#define STD_TRANSIT_H_
#include <string>
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
// #include "run.h"

// #include "dsmShareDefine.h"
#ifdef DSM_USING_RKNN
#include "rknn_api.h"
#endif


namespace dsm_std {

    struct face_message {

        int state;
        cv::Mat frame;
        std::vector<cv::Mat> face_roi;
        std::vector<int> landmarks;
        std::vector<float> face_angle; //人脸转动参数
    };

    struct DSMInfo
    {
        //人脸是否是相同驾驶员
        int isSameDriver;

        //人脸得分
        float face_score;
        //人脸坐标 左上和右下两个点 
        std::vector<cv::Point2i> face_point;
        //人脸五官特征点，五个点XY
        std::vector<cv::Point2i> five_point;
        //安全带标记点，五个int型的坐标XY
        std::vector<cv::Point2i> belt_point;
        //人脸标定角度
        int face_angle;

        //闭眼报警状态 类别1
        bool eye_alarm;
        //哈欠报警状态 类别2
        bool mouth_alarm;
        //抽烟报警状态 类别3
        bool smoking_alarm;
         //打电话报警状态 4
        bool phone_alarm;
        //头盔报警状态 5
        bool hat_alarm;
        //安全带报警状态 类别6
        bool seatbelt_alarm;
       
        //左顾右盼报警状态 类别7
        bool lookaround_alarm;
        //盲点检测报警状态 类别8
        bool Blindspot_alarm;
        //离岗报警状态 9
        bool facemissing_alarm;
        //遮挡报警状态 10
        bool camcover_alarm;
        
        //疲劳程度
        int fatigue_rank;

    };


    struct Net_config
    {
        float confThreshold; // Confidence threshold
        float nmsThreshold;  // Non-maximum suppression threshold
        float objThreshold;  //Object Confidence threshold
        std::string netname;
    };

   class YOLO5S;
	
// #ifdef DSM_USING_RKNN
//     // rknn_context yolov5_ctx;
//     // rknn_input_output_num  io_num;
// #else
//     // Net net;
// #endif


}

#endif // TRANSIT_H_

