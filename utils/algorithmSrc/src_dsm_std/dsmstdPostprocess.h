#ifndef _STD_RKNN_ZERO_COPY_DEMO_POSTPROCESS_H_
#define _STD_RKNN_ZERO_COPY_DEMO_POSTPROCESS_H_
 
#include <stdint.h>
#include <iostream>
//#include <opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>
 
#define OBJ_NAME_MAX_SIZE 16
#define OBJ_NUMB_MAX_SIZE 9400
 
//#define OBJ_CLASS_NUM     1 /* 80 */
#define OBJ_CLASS_NUM     1 /* yolov5-face的类别数是1. */
 
#define NMS_THRESH        0.4
#define BOX_THRESH        0.5   /* 0.5  0.87 */
 
// #define NMS_THRESH        0.5
// #define BOX_THRESH        0.3   //0.5
 
#define LAND_MARK_SIZE    10  /* 五个关键点，每个关键点有两个坐标x,y。*/
#define PROP_BOX_SIZE     (5 + OBJ_CLASS_NUM + LAND_MARK_SIZE) /* 16 */
//#define PROP_BOX_SIZE     (5+OBJ_CLASS_NUM) /* 10 */
 
 
typedef struct _BOX_RECT
{
    int left;
    int right;
    int top;
    int bottom;
    //std::vector<cv::Point2f> landmarkVec;
} BOX_RECT;
 
 
typedef struct __detect_result_t
{
    char name[OBJ_NAME_MAX_SIZE];
    BOX_RECT box;
    std::vector<float> landmark;//用于保存人脸关键点。
    float prop;
} detect_result_t;
 
typedef struct _detect_result_group_t
{
    int id;
    int count;
    detect_result_t results[OBJ_NUMB_MAX_SIZE];
} detect_result_group_t;
 
int post_process_std(uint8_t *input0, uint8_t *input1, uint8_t *input2, int model_in_h, int model_in_w,
                 float conf_threshold, float nms_threshold, float scale_w, float scale_h,
                 std::vector<uint32_t> &qnt_zps, std::vector<float> &qnt_scales,
                 detect_result_group_t *group);

int post_process_fp_std(float *input0, float *input1, float *input2, int model_in_h, int model_in_w,
                 float conf_threshold, float nms_threshold,
                 detect_result_group_t *group);
 
 
int rknn_GetTop_std(
    float *pfProb,
    float *pfMaxProb,
    uint32_t *pMaxClass,
    uint32_t outputCount,
    uint32_t topNum);
 
int loadLabelName(const char *locationFilename, char *label[], int classNum);
 
#endif //_RKNN_ZERO_COPY_DEMO_POSTPROCESS_H_
