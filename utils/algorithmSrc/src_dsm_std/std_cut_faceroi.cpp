#include <iostream>
#include <string>
#include <opencv2/opencv.hpp>
#include "std_cut_faceroi.h"
#include "std_face_angle.h"
#include "std_run.h"

extern dsm_std::DSMInfo alarm_stdinfo;
// �и���ת��roiͼƬ
cv::Mat dsm_std::Tcut::cut_rotate_roi(cv::Mat input_img, cv::Rect center, float angle_vec)
{
	center = Tface::safeROI(input_img, center);
	// cout << "center.height " << center.height << endl;
	// cout << "center.width " << center.width << endl;
	// cout << "center.x " << center.x << endl;
	cv::Point zxin;
	zxin.x = center.x;
	zxin.y = center.y;
	cv::Mat make = cv::Mat::zeros(input_img.size(), CV_8UC1);
	cv::RotatedRect rRect(zxin, cv::Size(center.width, center.height), angle_vec);
	cv::Point2f rRectPoint[4];
	rRect.points(rRectPoint);

	for (int i = 0; i < 4; i++)
	{
		cv::Point exit1, exit2;
		exit1.x = 0;
		exit1.y = 0;
		exit2.x = 0;
		exit2.y = 0;
		if (rRectPoint[(i + 1) % 4].x > input_img.cols)
		{
			exit1.x = input_img.cols;
		}
		else
		{
			exit1.x = rRectPoint[(i + 1) % 4].x;
		}
		if (rRectPoint[(i + 1) % 4].y > input_img.rows)
		{
			exit1.y = input_img.rows;
		}
		else
		{
			exit1.y = rRectPoint[(i + 1) % 4].y;
		}

		if (rRectPoint[i].x > input_img.cols)
		{
			exit2.x = input_img.cols;
		}
		else
		{
			exit2.x = rRectPoint[i].x;
		}
		if (rRectPoint[i].y > input_img.rows)
		{
			exit2.y = input_img.rows;
		}
		else
		{
			exit2.y = rRectPoint[i].y;
		}

		// line(make, exit2, exit1, Scalar(255, 0, 255));
		// line(input_img, rRectPoint[i], rRectPoint[(i + 1) % 4], Scalar(0, 255, 255));
	}
	//��ɫ���
	//cout << "ROI����:  x " << zxin.x <<"  y: " << zxin.y << endl;
	floodFill(make, zxin, 255, nullptr); //  255
	cv::Mat makeImg;
	cv::Mat grayimg;
	cv::cvtColor(input_img, grayimg, cv::COLOR_BGR2GRAY);
	cv::bitwise_and(grayimg, make, makeImg);

	cv::Rect boundRect; //������Ӿ���
	boundRect = rRect.boundingRect();
	boundRect = Tface::safeROI(makeImg, boundRect);
	//���Ŀ��
	cv::rectangle(makeImg, boundRect.tl(), boundRect.br(), cv::Scalar(255, 0, 0), 3, 8);
	cv::Mat result_roi = makeImg(boundRect).clone();
	return result_roi;
}

cv::Mat dsm_std::Tcut::cut_eye_L(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX)
{
	int temp_leftright = angle_vec[0];
	int temp_updown = angle_vec[1];
	//���ݱ�׼������ͥ���ۡ��ı������֡������и�
	int ratio_wight = angle_vec[4] * 80;
	int ratio_height = angle_vec[4] * 60;
	cv::Rect roi;
	roi.x = landmarks[0] - (landmarks[2] - landmarks[0]) * 0.36; // landmarks[2] - landmarks[0] Ϊ1ͥ
	roi.y = landmarks[1] - (landmarks[2] - landmarks[0]) * 0.2;
	roi.width = (landmarks[2] - landmarks[0]) * 0.72;
	roi.height = (landmarks[2] - landmarks[0]) * 0.4;

	//�޶�roi����  �޽Ƕ�ʶ�����ʹ��
	//Rect face_box = Tface::safeROI(input_img, roi);
	//Mat eyel_roi = input_img(face_box).clone();
	//����2
	cv::Mat eyel_roi;
	cv::Rect center;
	center.x = landmarks[0];
	center.y = landmarks[1];
	center.width = roi.width;
	center.height = roi.height;
	// //��֤���ַ�����ʶ��Ч����Ӱ��
	// //�Ƕ�����  �������Ը���ϸ������
	// if (temp_leftright < -17)
	// {
	// 	roi.x -= 5;
	// 	roi.y += 5;
	// }

	if (DiffX < 0)
	{
		float DIffX_ = std::abs(DiffX);
		roi.x -= 0.5 * DIffX_;
		roi.y -= 0.3 * DIffX_;
		roi.width += 1.0 * DIffX_;
		roi.height += 0.6 * DIffX_;
	}
	roi = Tface::safeROI(input_img, roi);
	eyel_roi = input_img(roi).clone();
	// rectangle(input_img, roi.tl(), roi.br(), Scalar(0, 255, 0), 1, 8);
	return eyel_roi;
}

cv::Mat dsm_std::Tcut::cut_eye_R(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX)
{
	//���۵Ŀ��߱�
	int temp_leftright = angle_vec[0];
	int ratio_wight = angle_vec[4] * 50;
	int ratio_height = angle_vec[4] * 40;
	cv::Rect roi;
	roi.x = landmarks[2] - (landmarks[2] - landmarks[0]) * 0.36; // landmarks[2] - landmarks[0] Ϊ1ͥ
	roi.y = landmarks[3] - (landmarks[2] - landmarks[0]) * 0.2;
	roi.width = (landmarks[2] - landmarks[0]) * 0.72;
	roi.height = (landmarks[2] - landmarks[0]) * 0.4;

	//�޶�roi����  �޽Ƕ�ʶ�����ʹ��
	//Rect face_box = Tface::safeROI(input_img, roi);
	//Mat eyer_roi = input_img(face_box).clone();
	cv::Mat eyer_roi;
	cv::Rect center;
	center.x = landmarks[2];
	center.y = landmarks[3];
	center.width = roi.width;
	center.height = roi.height;
	//��֤���ַ�����ʶ��Ч����Ӱ��
	//�Ƕ�����  �������Ը���ϸ������

	if (DiffX > 0)
	{
		float DIffX_ = std::abs(DiffX);
		roi.x -= 0.5 * DIffX_;
		roi.y -= 0.3 * DIffX_;
		roi.width += 1.0 * DIffX_;
		roi.height += 0.6 * DIffX_;
	}
	roi = Tface::safeROI(input_img, roi);
	eyer_roi = input_img(roi).clone();
	// rectangle(input_img, roi.tl(), roi.br(), Scalar(0, 255, 0), 1, 8);

	return eyer_roi;
}

cv::Mat dsm_std::Tcut::cut_mouth(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks)
{
	//��͵����ĵ�
	int meanx_mouth = (landmarks[6] + landmarks[8]) * 0.5;
	int meany_mouth = landmarks[7] > landmarks[9] ? landmarks[7] : landmarks[9];
	//int meany_mouth = (landmarks[7] + landmarks[9]) * 0.5;
	int ratio_wight = 2;
	int ratio_height = 0;
	if (angle_vec[4] < 30)
	{
		ratio_wight = angle_vec[4] * 50;
		ratio_height = angle_vec[4] * 40;
		// ratio_wight = angle_vec[4] * 100 * 2.5;
		// ratio_height = angle_vec[4] * 40 * 1.2;
	}
	else
	{
		// ratio_wight = angle_vec[4] * 30;
		// ratio_height = angle_vec[4] * 40;
		ratio_wight = angle_vec[4] * 60;
		ratio_height = angle_vec[4] * 50;
	}
	int mouth_x = landmarks[6] - ratio_wight;
	int mouth_y = landmarks[5] + ratio_height;
	cv::Rect roi;
	roi.x = mouth_x - ratio_height;  // ����ϵ��
	roi.y = mouth_y - ratio_height;
	roi.width = (landmarks[8] - landmarks[6]) + (ratio_wight *4);
	if (meany_mouth - landmarks[5] <= 12)
	{
		// roi.height = (meany_mouth - landmarks[5]) * 2.3 + 12;   //+  ratio_height   +12
		roi.height = (meany_mouth - landmarks[5]) * 2 + 12;   //+  ratio_height
	}
	else
	{
		// roi.height = (meany_mouth - landmarks[5] - 3) * 2.3;   //+  ratio_height
		roi.height = (meany_mouth - landmarks[5] - 3) * 2;
	}

	roi = Tface::safeROI(input_img, roi);
	//cout << "roi3.height " << roi.height << endl;
	//cout << "roi3.width " << roi.width << endl;
	//cout << "roi3.x " << roi.x << endl;
	//cout << "roi3.y " << roi.y << endl;

	//�޶�roi����   ˮƽ�����޽Ƕ�ʱʹ��
	//Rect face_box = Tface::safeROI(input_img, roi);
	//Mat eyel_roi = input_img(face_box).clone();
	//rectangle(input_img, face_box, Scalar(155, 0, 160), 1);
	//namedWindow("����ROI", WINDOW_NORMAL);
	//imshow("����ROI", input_img);
	//Mat mouth_roi;
	cv::Mat mouth_roi(300, 300, CV_8UC3, cv::Scalar::all(255));
	cv::Rect center;
	center.x = meanx_mouth;
	center.y = meany_mouth;
	center.width = roi.width;
	center.height = roi.height;


	mouth_roi = input_img(roi).clone();
	return mouth_roi;

	//���������̬��ƫ�Ƶ���
	if (abs(angle_vec[2]) <= 28)  //25
	{
		//����һ  ��ͼ
		mouth_roi = input_img(roi).clone();
		rectangle(input_img, roi.tl(), roi.br(), cv::Scalar(0, 255, 0), 1, 8);
	}
	else
	{
		//������  ��ת���ͼ
		/*putText(input_img, "error", Point(220, 20), FONT_HERSHEY_SIMPLEX, 0.75, Scalar(0, 255, 0), 1);*/

		mouth_roi = cut_rotate_roi(input_img, center, angle_vec[2]);

		//cout << "center1.height " << center.height << endl;
		//cout << "center1.width " << center.width << endl;
		//cout << "center1.x " << center.x << endl;
	}

	if (mouth_roi.rows == 0)
	{
		return mouth_roi;
	}

	return mouth_roi;
}

cv::Mat dsm_std::Tcut::cut_phone_l(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX)
{
	/*
		�٣���ͷ�������м�Ľ�ȡ��ʽ
	*/
	/*Add*/
	//����ƫ��
	//cv::Point EyeMiddle;
	//EyeMiddle.x = (landmarks[0] + landmarks[2]) / 2;
	//EyeMiddle.y = (landmarks[1] + landmarks[3]) / 2;
	//int BoxXMiddle = face_box.x + (face_box.width / 2);
	//float DiffX = BoxXMiddle - EyeMiddle.x;
	//if ((DiffX < 8) && (DiffX > -8))
	//{
	//	DiffX *= 0.3;
	//}
	//else
	//{
	//	DiffX *= 0.5;
	//}

	cv::Mat phone_image_l;
	int left = face_box.x;
	int top = face_box.y;
	int right = face_box.x + face_box.width;
	int bottom = face_box.y + face_box.height;
	int temp_leftright = angle_vec[0];
	int temp_updown = angle_vec[1];
	//uhd 
	face_box = Tface::safeROI(input_img, face_box);
	cv::Mat face_image(input_img, face_box);
	double bias = Tcut::compareMats(face_image, input_img);

	//��ȡ�������ߵ�phoneroi 
	//���
	cv::Rect left_eye_roi;
	left_eye_roi.x = landmarks[0] - (landmarks[2] - landmarks[0]) * 0.36;  // landmarks[2] - landmarks[0] Ϊ1ͥ
	left_eye_roi.y = landmarks[1] - (landmarks[2] - landmarks[0]) * 0.2;
	left_eye_roi.width = (landmarks[2] - landmarks[0]) * 0.72;
	left_eye_roi.height = (landmarks[2] - landmarks[0]) * 0.4;

	//Rect phone_roi_l(Point(left_eye_roi.x - 130 * bias, left_eye_roi.y + 5 * bias), Point(landmarks[0] - 10, face_box.y + face_box.height + 45 * bias));	//2023.8.7�޸�
	cv::Rect phone_roi_l(cv::Point(left_eye_roi.x - 195, left_eye_roi.y + 5 * bias), cv::Point(landmarks[0] - 10, face_box.y + face_box.height + 70));	//2023.8.7�޸�
	//2023.8.7�޸�
	//if (temp_leftright > 6 && temp_leftright <= 10)
	//{
	//	phone_roi_l.x -= 20;
	//	phone_roi_l.width += 30;
	//}
	//else if (temp_leftright >= 10 && temp_leftright <= 15)
	//{
	//	phone_roi_l.x -= 35;
	//	phone_roi_l.width += 60;
	//}
	//else if (temp_leftright > 15)
	//{
	//	phone_roi_l.x -= 40;
	//	phone_roi_l.width += 70;
	//}

	//if (temp_leftright >= -15 && temp_leftright <= -10)
	//{
	//	phone_roi_l.x -= 20;
	//	phone_roi_l.width += 20;
	//}
	//else if (temp_leftright >= -20 && temp_leftright <= -15)
	//{
	//	phone_roi_l.x -= 20;
	//	phone_roi_l.width += 20;
	//}
	//else if (temp_leftright >= -25 && temp_leftright <= -20)
	//{
	//	phone_roi_l.x -= 20;
	//	phone_roi_l.width += 20;
	//}
	/*ƫ����ƫ��*/
	if (DiffX > 0)
	{	
		float DIffX_ = std::abs(DiffX);
		//��ת
		if (DiffX > 6 && DiffX <= 10)
		{
			phone_roi_l.x -= 1.0 * DIffX_;
			phone_roi_l.width += 2.5 * DIffX_;
		}
		else if (DiffX > 10)
		{
			phone_roi_l.x -= 0.8 * DIffX_;
			phone_roi_l.width += 1.5 * DIffX_;
		}
	}
	else
	{
		float DIffX_ = std::abs(DiffX);
		//��ת
		if (DiffX < -6 && DiffX >= -10)
		{
			phone_roi_l.x -= 1.5 * DIffX_;
			phone_roi_l.width += 1.5 * DIffX_;
		}
		else if (DiffX < -10)
		{
			phone_roi_l.x -= 1.5 * DIffX_;
			phone_roi_l.width += 1.5 * DIffX_;
		}
	}

	/*�����ƫ��*/
	int RollAngle = angle_vec[2];
	if (RollAngle > 0)
	{
		float RollAngle_ = float(RollAngle);
		//����
		phone_roi_l.x -= (2.0 * RollAngle_);
	}
	else
	{
		//����
		float RollAngle_ = std::abs(float(RollAngle));
		phone_roi_l.x += (2.5 * RollAngle_);

	}

	//cv::rectangle(input_img, phone_roi_l, cv::Scalar(0, 0, 255));
	phone_roi_l = Tface::safeROI(input_img, phone_roi_l);

	phone_image_l = input_img(phone_roi_l).clone();

	return phone_image_l;

	/*
		�ڣ���ͷ������A���Ľ�ȡ��ʽ
	*/
	int NoseX = landmarks[4];
}

cv::Mat dsm_std::Tcut::cut_phone_r(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX)
{
	/*Add*/
	//����ƫ��
	//cv::Point EyeMiddle;
	//EyeMiddle.x = (landmarks[0] + landmarks[2]) / 2;
	//EyeMiddle.y = (landmarks[1] + landmarks[3]) / 2;
	//int BoxXMiddle = face_box.x + (face_box.width / 2);
	//float DiffX = BoxXMiddle - EyeMiddle.x;
	//if ((DiffX < 8) && (DiffX > -8))
	//{
	//	DiffX *= 0.3;
	//}
	//else
	//{
	//	DiffX *= 0.5;
	//}

	cv::Mat phone_image_r;
	int left = face_box.x;
	int top = face_box.y;
	int right = face_box.x + face_box.width;
	int bottom = face_box.y + face_box.height;
	int temp_leftright = angle_vec[0];
	int temp_updown = angle_vec[1];
	//uhd 
	face_box = Tface::safeROI(input_img, face_box);
	cv::Mat face_image(input_img, face_box);
	double bias = Tcut::compareMats(face_image, input_img);

	//��ȡ�������ߵ�phoneroi
	//�ұ�
	cv::Rect right_eye_roi;
	right_eye_roi.x = landmarks[2] - (landmarks[2] - landmarks[0]) * 0.36;  // landmarks[2] - landmarks[0] Ϊ1ͥ
	right_eye_roi.y = landmarks[3] - (landmarks[2] - landmarks[0]) * 0.2;
	right_eye_roi.width = (landmarks[2] - landmarks[0]) * 0.72;
	right_eye_roi.height = (landmarks[2] - landmarks[0]) * 0.4;

	//Rect phone_roi_r(Point(landmarks[2] + 10, right_eye_roi.y + 5 * bias), Point(right_eye_roi.x + right_eye_roi.width + 130 * bias, face_box.y + face_box.height + 45 * bias));
	cv::Rect phone_roi_r(cv::Point(landmarks[2] + 10, right_eye_roi.y + 5 * bias), cv::Point(right_eye_roi.x + right_eye_roi.width + 195, face_box.y + face_box.height + 70));
	//����ת��ı�ROI
	//if (temp_leftright < -17)
	//{
	//	phone_roi_r.x -= 10;
	//	phone_roi_r.width += 50;
	//}
	//if (temp_leftright < -13 && temp_leftright >= -17)
	//{
	//	phone_roi_r.x -= 10;
	//	phone_roi_r.width += 30;
	//}
	//if (temp_leftright <= -5 && temp_leftright >= -13)
	//{
	//	phone_roi_r.x -= 10;
	//	phone_roi_r.width += 30;
	//
	//}
	//if (temp_leftright < 0 && temp_leftright > -5)
	//{
	//	//phone_roi_r.x -= 20;
	//	phone_roi_r.width += 30;
	//}
	//if (temp_leftright > 10 && temp_leftright < 20)
	//{
	//	//phone_roi_r.x -= 10;
	//	phone_roi_r.height -= 10;
	//}
	//if (temp_leftright >= 20)
	//{
	//	phone_roi_r.height -= 20;
	//}

	/*ƫ����ƫ��*/
	if (DiffX > 0)
	{
		float DIffX_ = DiffX;
		//��ת
		if (DiffX > 6 && DiffX <= 10)
		{
			phone_roi_r.x -= 1.5 * DIffX_;
			phone_roi_r.width += 3 * DIffX_;
		}
		else if (DiffX > 10)
		{
			phone_roi_r.x -= 1.0 * DIffX_;
			phone_roi_r.width += 3 * DIffX_;
		}
	}
	else
	{
		float DIffX_ = std::abs(DiffX);
		//��ת
		if (DiffX < -6 && DiffX >= -10)
		{
			phone_roi_r.x -= 1.5 * DIffX_;
			phone_roi_r.width += 1.5 * DIffX_;
		}
		else if (DiffX < -10)
		{
			phone_roi_r.x -= 1.5 * DIffX_;
			phone_roi_r.width += 1.5 * DIffX_;
		}
	}

	/*�����ƫ��*/
	int RollAngle = angle_vec[2];
	if (RollAngle > 0)
	{
		float RollAngle_ = float(RollAngle);
		//����
		phone_roi_r.x -= (2.5 * RollAngle_);
	}
	else
	{
		//����
		float RollAngle_ = std::abs(float(RollAngle));
		phone_roi_r.x += (2.0 * RollAngle_);

	}

	//cv::rectangle(input_img, phone_roi_r, cv::Scalar(0, 0, 255));

	phone_roi_r = Tface::safeROI(input_img, phone_roi_r);
	phone_image_r = input_img(phone_roi_r).clone();

	cv::flip(phone_image_r, phone_image_r, 1);

	return phone_image_r;

}

cv::Mat dsm_std::Tcut::cut_hat(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, float DiffX)
{
	int left = face_box.x;
	int top = face_box.y;
	int right = face_box.x + face_box.width;
	int bottom = face_box.y + face_box.height;
	//uhd 
	face_box = Tface::safeROI(input_img, face_box);
	cv::Mat face_image(input_img, face_box);
	double bias = Tcut::compareMats(face_image, input_img);
	//std::cout << "bias = " << bias << std::endl;
	//Rect hat_roi = Rect(Point(left - 40 * bias, top - 140 * bias), Point(right + 40 * bias, bottom - 200 * bias));
	cv::Rect hat_roi = cv::Rect(
		cv::Point(left - 40 * bias, top - 80 * bias), 
		cv::Point(right + 40 * bias, top));
	//hat_roi = Tface::safeROI(input_img, hat_roi);
	hat_roi = Tface::safeROI(input_img, hat_roi);
	//cv::rectangle(input_img, hat_roi, cv::Scalar(255, 0, 0));
	cv::Mat hat_img = input_img(hat_roi).clone();
	//cv::putText(hat_img, std::to_string(bias), cv::Point(50, 50), 1, 1, cv::Scalar(0, 255, 0), 1);

	return  hat_img;

}

cv::Mat dsm_std::Tcut::cut_belt(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec)
{
	cv::Mat belt_img(300, 300, CV_8UC3, cv::Scalar::all(0));
	std::vector<cv::Point> belt_roi;
	if (!input_img.empty())
	{
		std::vector<cv::Point> points;
		points.clear();
		int face_height = face_box.height + 25; //+50像素
		int face_x = face_box.x *1.6;
		int face_y = face_box.y;
		//根据人脸远近进行微调,暂时以 50 作为标准距离
		int distance = 90 - int(angle_vec[4] * 100);
		int stand_dis = 50;
		int dis_bias = stand_dis - distance;
		if (dis_bias > 0)
		{
			dis_bias = dis_bias * 1;
		}
		else
		{
			dis_bias = 0;
		}
		// std::cout << "beltroi.y+distance = " << dis_bias << std::endl;
		face_height = face_height + dis_bias;
		BELT_INFO belt_info_ = SkewParam::GetBeltInfo();
		// belt_info_.five_point[0] = cv::Point(belt_info_.five_point[0].x, face_y + face_height);
		// belt_info_.five_point[1] = cv::Point(belt_info_.five_point[1].x, face_y + face_height + 50);  //50*0.5625=26
		// belt_info_.five_point[2] = cv::Point(belt_info_.five_point[1].x, face_y + face_height + 150); //150*0.5625=84
		// belt_info_.five_point[3] = cv::Point(belt_info_.five_point[3].x, face_y + face_height + 150);
		// belt_info_.five_point[4] = cv::Point(belt_info_.five_point[4].x, face_y + face_height);

		belt_info_.five_point[0] = cv::Point(belt_info_.five_point[0].x, face_y + belt_info_.five_point[0].y);
		belt_info_.five_point[1] = cv::Point(belt_info_.five_point[1].x, face_y + belt_info_.five_point[1].y);  //50*0.5625=26
		belt_info_.five_point[2] = cv::Point(belt_info_.five_point[2].x, face_y + belt_info_.five_point[2].y); //150*0.5625=84
		belt_info_.five_point[3] = cv::Point(belt_info_.five_point[3].x, face_y + belt_info_.five_point[3].y);
		belt_info_.five_point[4] = cv::Point(belt_info_.five_point[4].x, face_y + belt_info_.five_point[4].y);
		
		for (size_t i = 0; i < 5; i++)  //150*85
		{
			// std::cout<<"======================================6666belt_info_.five_point:  "<< belt_info_.five_point[i] << std::endl;
			points.push_back(belt_info_.five_point[i]);
			// alarm_stdinfo.belt_point[i] = cv::Point(1280-points[i].y*2,  belt_info_.five_point[i].x*2-280);
			alarm_stdinfo.belt_point[i] = cv::Point(belt_info_.five_point[i].x,  belt_info_.five_point[i].y);
			// std::cout<<"=======================================================================7777belt_info_.belt_point[i]:  "<< alarm_stdinfo.belt_point[i] << std::endl;
		}

		// alarm_stdinfo.belt_point[0] = cv::Point(1280-belt_info_.five_point[0].y*2,  belt_info_.five_point[0].x*2-280);
		// alarm_stdinfo.belt_point[1] = cv::Point(1280-belt_info_.five_point[1].y*2,  belt_info_.five_point[1].x*2-280);
		// alarm_stdinfo.belt_point[2] = cv::Point(1280-belt_info_.five_point[2].y*2,  belt_info_.five_point[2].x*2-280);
		// alarm_stdinfo.belt_point[3] = cv::Point(1280-belt_info_.five_point[3].y*2,  belt_info_.five_point[3].x*2-280);
		// alarm_stdinfo.belt_point[4] = cv::Point(1280-belt_info_.five_point[4].y*2,  belt_info_.five_point[4].x*2-280);

		// points.push_back(cv::Point(602, 191));
		// points.push_back(cv::Point(77, 194));
		// points.push_back(cv::Point(82, 610));
		// points.push_back(cv::Point(265, 626));
		// points.push_back(cv::Point(518, 495));

		// ????mask???
		cv::Mat mask(input_img.size(), CV_8UC3, cv::Scalar::all(0));
		cv::fillConvexPoly(mask, points, cv::Scalar::all(255));
		cv::Mat result;
		cv::bitwise_and(input_img, mask, result);

		// Rect r1(Point(points[3].x, points[0].y), Point(points[1].x, points[2].y));
		//左舵安全带位置
		int left_x, right_x, top_y, bottom_y;
		// std::cout<<"1==========================points[1].x:  "<< points[1].x<< std::endl;
		// std::cout<<"1==========================points[2].x:  "<< points[2].x<< std::endl;
		// std::cout<<"1==========================points[3].x:  "<< points[3].x<< std::endl;
		// std::cout<<"1==========================points[4].x:  "<< points[4].x<< std::endl;

		// std::cout<<"1==========================points[0].y:  "<< points[0].y<< std::endl;
		// std::cout<<"1==========================points[2].y:  "<< points[2].y<< std::endl;
		// std::cout<<"1==========================points[3].y:  "<< points[3].y<< std::endl;
		// std::cout<<"1==========================points[4].y:  "<< points[4].y<< std::endl;
		if (belt_info_.left_right_button == 0)
		{
			left_x = MIN(points[1].x, points[2].x);
			right_x = MAX(points[3].x, points[4].x);
			top_y = MIN(points[0].y, points[4].y);
			bottom_y = MAX(points[2].y, points[3].y);
		}
		//右舵安全带位置
		else
		{
			left_x = MIN(points[4].x, points[3].x);
			right_x = MAX(points[1].x, points[2].x);
			top_y = MIN(points[0].y, points[4].y);
			bottom_y = MIN(points[2].y, points[3].y);
		}
		cv::Rect r1(cv::Point(left_x, top_y), cv::Point(right_x, bottom_y));
		r1 = Tface::safeROI(result, r1);

		// std::cout<<"==========================top_y:  "<< top_y<< std::endl;
		// std::cout<<"==========================bottom_y:  "<< bottom_y<< std::endl;
		// std::cout<<"==========================r1x:  "<< r1.x<< std::endl;
		// std::cout<<"==========================r1y:  "<< r1.y<< std::endl;
		// std::cout<<"==========================r1.width:  "<< r1.width<< std::endl;
		// std::cout<<"==========================r1.heigt:  "<< r1.height<< std::endl;
		// std::cout<<"=======================================================================result:  "<< result.size() << std::endl;
		belt_img = result(r1).clone();
		// std::cout<<"=======================================================================belt_img:  "<< belt_img.size() << std::endl;
		if (belt_info_.left_right_button == 1)  //翻转成左舵，适应分类模型
		{
			cv::flip(belt_img, belt_img, 1);
		}
		// cv::Scalar color = cv::Scalar(0, 255, 0);
		// cv::polylines(input_img, belt_info.five_point, true, color, 2, cv::LINE_AA);
		return belt_img;
	}
	else
	{
		printf("安全带cutroi尚未变更为4点: **************************************************** cut_belt is empty *in line %d  \n", __LINE__);
		return input_img;
	}
}


cv::Mat dsm_std::Tcut::cut_smoke(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX)
{
	int center_x = (landmarks[6] + landmarks[8]) / 2;
	int center_y = landmarks[7] > landmarks[9] ? landmarks[7] : landmarks[9];
	int ratio_wight = 0;
	int ratio_height = 0;

	// ratio_wight = angle_vec[4] * 100 * 2.5;
	// ratio_height = angle_vec[4] * 40 * 1.1;
	ratio_wight = angle_vec[4] * 100 * 2.5;
	ratio_height = angle_vec[4] * 40 * 1.2;

	int mouth_x = landmarks[6] - ratio_wight;
	int mouth_y = landmarks[5] - ratio_height;
	cv::Rect roi;
	roi.x = mouth_x;   // 乘以系数
	roi.y = mouth_y;
	roi.width = (landmarks[8] - landmarks[6]) + (ratio_wight + ratio_wight);
	roi.height = (center_y - landmarks[5]) * 3.35; //+  ratio_height
	// roi.height = (center_y - landmarks[5]) * 2.75; //+  ratio_height

	//添加角度控制
	//[0]左右朝向
	// roi.x += angle_vec[0];
	roi.x -= (DiffX * 1.2);	/*2024.10.10*/
	//[1]俯仰朝向
	if (angle_vec[1] < -5)
	{
		roi.y -= angle_vec[1] * 1;  // 0.8
		roi.height -= angle_vec[1];
	}

	/*2024.10.12Add*/
	face_box = Tface::safeROI(input_img, face_box);
	cv::Mat face_image(input_img, face_box);
	double bias = Tcut::compareMats(face_image, input_img);
	cv::Rect TempRoi;
	TempRoi.x = landmarks[4] - 70 * bias;
	TempRoi.y = landmarks[5] - 20 * bias;
	TempRoi.width = 148 * bias;
	TempRoi.height = 126 * bias;  //0328  126  108
	roi.x -= (DiffX * 1.2);

	TempRoi = Tface::safeROI(input_img, TempRoi);
	//cv::rectangle(input_img, TempRoi, cv::Scalar(0, 255, 0), 1, 8, 0);
	cv::Mat smoke_img = input_img(TempRoi).clone();
	return smoke_img;
}

double dsm_std::Tcut::compareMats(cv::Mat mat1, cv::Mat mat2)
{
	int face_ares = mat1.rows * mat1.cols;
	// cout << "face_ares ======= " << face_ares << endl;
	int frame_ares = mat2.rows * mat2.cols;
	// cout << "frame_ares ====== " << frame_ares << endl;

	double result = ((double)face_ares / (double)frame_ares) * 10 + 0.56;   //0401
	// double result = ((double)face_ares / (double)frame_ares) * 10 + 0.36;  //旧版本
	// double result = ((double)face_ares / (double)frame_ares) * 10 + 0.34;  //0328
	// cout << "result ===== " << result << endl;

	return result;
}

float dsm_std::Tcut::GetXDiff(std::vector<int> landmarks, cv::Rect face_box)
{
	cv::Point EyeMiddle;
	EyeMiddle.x = (landmarks[0] + landmarks[2]) / 2;
	EyeMiddle.y = (landmarks[1] + landmarks[3]) / 2;
	int BoxXMiddle = face_box.x + (face_box.width / 2);
	float DiffX = BoxXMiddle - EyeMiddle.x;
	if ((DiffX < 8) && (DiffX > -8))
	{
		DiffX *= 0.3;
	}
	else
	{
		DiffX *= 0.5;
	}

	return DiffX;
}