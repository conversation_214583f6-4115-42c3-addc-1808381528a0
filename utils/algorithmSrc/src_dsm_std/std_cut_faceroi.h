#ifndef STD_CUT_FACEROI_H_
#define CUT_FACEROI_H_
 
#include <string>
#include <opencv2/opencv.hpp>
namespace dsm_std {
	class Tcut
	{
	public:
		static cv::<PERSON> cut_eye_L(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX);
		static cv::<PERSON> cut_eye_R(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX);
		static cv::<PERSON> cut_mouth(cv::Mat input_img, std::vector<float> angle_vec, std::vector<int> landmarks);
		static cv::Mat cut_smoke(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX);
		static cv::Mat cut_phone_r(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX);
		static cv::Mat cut_phone_l(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, std::vector<int> landmarks, float DiffX);
		static cv::Mat cut_hat(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec, float DiffX);
		static cv::Mat cut_belt(cv::Mat input_img, cv::Rect face_box, std::vector<float> angle_vec);
		
		static cv::Mat cut_rotate_roi(cv::Mat input_img, cv::Rect center, float angle_vec);  //test
		static double compareMats(cv::Mat mat1, cv::Mat mat2);
		static float GetXDiff(std::vector<int> landmarks, cv::Rect face_box);
	};
}


#endif // CUT_FACEROI_H_

