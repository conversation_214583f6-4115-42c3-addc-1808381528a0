#ifndef STD_FACE_ANGLE_H_
#define STD_FACE_ANGLE_H_
#include <string>
#include <opencv2/opencv.hpp>


namespace dsm_std {
	class Tface
	{
	public:
		static std::vector<float> face_angle(cv::Mat input_img, cv::Rect face_box, std::vector<int> landmarks);
		static float get_pitch_arctan(std::vector<int> landmark);
		static float get_yaw_arctan(std::vector<int> landmark, float& cab_pos, float angle_pitch, float angle_roll);
		static float get_roll_arctan(std::vector<int> landmark);
		static float get_face_distance(cv::Mat input_img, cv::Rect face_box, std::vector<int> landmark);
		static cv::Rect safeROI(const cv::Mat& input_img, cv::Rect& roi_box);
		static void draw_face(cv::Mat input_img, cv::Rect roi, cv::Rect face_box, std::vector<float> angle_vec);
		//������๦��
	private:

	};
}
#endif // FACE_ANGLE_H_  face_angle
