#ifndef STD_YOLOV5S_H_
#define STD_YOLOV5S_H_
#include <string>
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include "std_run.h"
#include "dsmstdShareDefine.h"
#ifdef DSM_USING_RKNN
#include "rknn_api.h"
#endif


namespace dsm_std {

	class YOLO5S
	{
	public:
    	YOLO5S(Net_config config);
	    face_message detect_face(cv::Mat& frame, cv::Mat beltimg); //, DSMInfo& almuv_info
	private:
        const float anchors[3][6] = { {4,5,  8,10,  13,16}, {23,29,  43,55,  73,105},{146,217,  231,300,  335,433} };
        const float stride[3] = { 8.0, 16.0, 32.0 };
        const int inpWidth = 640;  //640  21
        const int inpHeight = 640;
        float confThreshold;
        float nmsThreshold;
        float objThreshold;
        int imgCount = 0;

		char netname[20];
        static int invade_face(int idx, cv::Rect box, std::vector< std::vector<int>> landmarks);
        void drawPred(float conf, int left, int top, int right, int bottom, cv::Mat& frame, std::vector<int> landmark);
        void sigmoid(cv::Mat* out, int length);


	};

#ifdef DSM_USING_RKNN
    extern rknn_context yolov5_ctx;
    extern rknn_input_output_num  io_num;
#else
    Net net;
#endif

}
#endif // YOLOV5S_H_

