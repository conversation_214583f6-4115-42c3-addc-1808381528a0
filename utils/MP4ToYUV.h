//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/4.
//

#ifndef VIS_G3_SOFTWARE_MP4TOYUV_H
#define VIS_G3_SOFTWARE_MP4TOYUV_H
#include <iostream>
#include <opencv2/opencv.hpp>


class MP4ToYUV {
public:

    bool openMp4File(const char *filePath);

    bool readOneFrame(cv::Mat &frame);

    void release();

private:
    cv::VideoCapture cap;

};


#endif //VIS_G3_SOFTWARE_MP4TOYUV_H
