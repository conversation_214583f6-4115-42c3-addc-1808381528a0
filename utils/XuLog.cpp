//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/28.
//

#include <iomanip>
#include <unistd.h>
#include <dirent.h>
#include <yaml-cpp/node/node.h>
#include <yaml-cpp/node/parse.h>

#include "XuLog.h"
#include "XuString.h"
#include "XuFile.h"
#include "XuTimeUtil.h"
#include "XuShell.h"


namespace vis {


    
    /* 根据文件名对两个文件进行排序（升序） */
    bool doLogFileAscending(const std::string fileNmaeA, const std::string fileNmaeB) {
        bool ret = false;
        /* 取出序号这个字段 */
        std::string timeStrA = XuString::getInstance().split(fileNmaeA, "_")[0];
        std::string timeStrB = XuString::getInstance().split(fileNmaeB, "_")[0];
        /* 比较一下时间  旧的在前面 */
        ret = std::atoi(timeStrA.c_str()) < std::atoi(timeStrB.c_str());
        return ret;


    }

    XuLog::XuLog() {
        /* 读取一下配置文件 */
        if(XuFile::getInstance().fileExists(logConfigFilePath.c_str())){
            YAML::Node curConfig = YAML::LoadFile(logConfigFilePath);
            if (curConfig.IsDefined()) {
                /* 看看log文件路径需不需要修改 */
                if (curConfig["path"].IsDefined()) {
                    logFileRootPath = curConfig["path"].as<std::string>();
                    curLogFilePath =logFileRootPath+"curlog";
                }
                /* 看看日志文件数量阈值需不需要修改 */
                if (curConfig["max_file_sum"].IsDefined()) {
                    max_log_file_list_size = curConfig["max_file_sum"].as<int>();

                }
                /* 看看日志文件大小阈值需不需要修改 */
                if (curConfig["max_file_size"].IsDefined()) {
                    max_log_file_size = curConfig["max_file_size"].as<int>();
                }
                /* 看看需要保存到日志文件的级别需不需要修改 */
                if (curConfig["log_level"].IsDefined()) {
                    curSaveLogLevel = static_cast<LOG_LEVEL>(curConfig["log_level"].as<int>());
                }

            }

        }
        /* 全部通道都设置一下回调 */
        channelLock.lock();
        for(int i = 0; i < MAX_LOG_CHANNEL; i ++){
            channel[i].setCallback(*this);
        }
        channelLock.unlock();

        /* 创建一下目录 */
        XuFile::getInstance().mkpath(logFileRootPath);
        openLogFile();
        if (inited) {
            saveThread.start(*this);
        }

    }

    XuLog::~XuLog() {
        if (!logFd) {

        } else {
            fclose(logFd);
        }
    }

    XuLog &XuLog::getInstance() {
        static XuLog _instance;
        return _instance;
    }


    void XuLog::run() {
        /* 设置一下线程名 */
        pthread_setname_np(pthread_self(), "VisLogSave");
        uint64_t lastSaveToFileTime = 0;
        while (inited) {
            /* 如果收到需要立马同步的信号或者时间间隔达到阈值，就同步缓存到文件 */
            if(needSyncToFile || static_cast<uint64_t>(XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec()) - lastSaveToFileTime >= MAX_SYNC_CACHE_TO_FILE_INTERVAL){
                /* 写到文件 */
                writeCacheToFile();
                /* 取消掉需要立马同步的标志 */
                if(needSyncToFile){
                    needSyncToFile = false;
                }
                /* 保存一下时间 */
                lastSaveToFileTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();

            }
            /* 等100毫秒 */
            usleep(100 * 1000);
        }
        pthread_setname_np(pthread_self(), "Finish");
    }


    void XuLog::putToCache(const std::string &fullContent) {
        if (inited) {
            /* 把日志内容放到缓存里 */
            mLock.lock();
            logCache.append(fullContent);
            mLock.unlock();

            /* 判断下缓存的大小是不是超过了阈值，超过了就需要立马同步到文件 */
            if (logCache.size() >= MAX_LOG_CACHE_SIZE) {
                needSyncToFile = true;
            }
        }


    }

    void XuLog::openLogFile() {
        /* 打开文件 */
        logFd = fopen(curLogFilePath.c_str(), "a");
        if (!logFd) {
            printf("open file %s failed! \n", curLogFilePath.c_str());
            inited = false;
        } else {
            inited = true;
        }
    }

    void XuLog::writeCacheToFile() {
        mLock.lock();
        /* 把缓冲区的内容写到文件里 */
        fwrite(logCache.data(), logCache.size(), 1, logFd);
        fflush(logFd);
        /* 清除缓冲区的数据 */
        logCache.clear();
        mLock.unlock();
        /* 看看文件大小是否到了阈值，到了阈值就需要创建新的文件 */
//        printf("-----------------%lld    %d \n",XuFile::getInstance().getFileLength(curLogFilePath.c_str()),max_log_file_size);
        if (XuFile::getInstance().getFileLength(curLogFilePath.c_str()) >= max_log_file_size) {
            /* 这里增加了同步到物理内存的操作，会极大影响性能，所以日志文件必须遵守只记录重要的事情 */
            fsync(fileno(logFd));
            /* 先关闭旧的文件 */
            fclose(logFd);
            /* 检查下文件个数是否超过阈值，如果超过先删掉一个 */
            std::vector<std::string> files = getAllLogFile();

            if (files.size() >= static_cast<std::size_t>(max_log_file_list_size)) {
                /* 对文件根据文件名排下序号 */
                std::sort(files.begin(), files.end(), doLogFileAscending);
                /* 删除最旧的那个 */
                std::string filePath = logFileRootPath + files[0];
                remove(filePath.c_str());
            }
            /* 把这个文件重名一下，用序号[_vispect.log]这种格式 */
            std::string newLogFileName;
            /* 获取log文件对应的序号 */
            int curNum = 0;
            if (!files.empty()) {
                /* 对文件根据文件名排下序号 */
                std::sort(files.begin(), files.end(), doLogFileAscending);
                /* 获取最大的序号 */
                curNum = std::atoi(XuString::getInstance().split(files[files.size() - 1], "_")[0].c_str()) + 1;
                /* 看看文件数是不是超过了阈值 */
                if(files.size() >= MAX_FILE_NAME_NUM){
                    /* 超过阈值了,删除最旧的那个文件 */
                    std::string cmdStr = "rm -f ";
                    cmdStr.append(files[0]);
                    cmdStr.append(" && sync");
                    XuShell::getInstance().runShellWithTimeout(cmdStr.c_str());
                }
            }
            /* 保存新的log文件 */

            char strTemp[100] = {0x00};
            XuString::getInstance().getStrFormTime(strTemp, sizeof(strTemp), "%Y-%m-%d %H:%M:%S", false);
            newLogFileName.append(logFileRootPath);
            newLogFileName.append(std::to_string(curNum));
            newLogFileName.append(strTemp);
            newLogFileName.append("_vispect.log");
            rename(curLogFilePath.c_str(), newLogFileName.c_str());
            /* 再打开一个新的文件 */
            openLogFile();
        }
    }

    std::vector<std::string> XuLog::getAllLogFile() {
        DIR *dir;
        std::vector<std::string> result;
        dir = opendir(logFileRootPath.c_str());
        if (dir != nullptr) {
            struct dirent *ptr;
            while ((ptr = readdir(dir)) != nullptr) {
                if (strcmp(ptr->d_name,".") != 0 && strcmp(ptr->d_name,"..") != 0 && strstr(ptr->d_name, ".log") != nullptr) {
                    result.push_back(ptr->d_name);
                } else { ; //not to do
                }

            }
        } else {
        }
        closedir(dir);
        return result;
    }

    void XuLog::resetLogFileName(std::vector<std::string> &files) {
        for(std::size_t i = 0; i < files.size(); i ++){
            std::string oldFilePathTemp = logFileRootPath + files[i];
            std::string newFilePathTemp = logFileRootPath + std::to_string(i) + "_vispect.log";
            rename(oldFilePathTemp.c_str(), newFilePathTemp.c_str());
        }
    }

    void XuLog::showAndSaveLog(const std::string &tag, const std::string &logContent,XuLog::LOG_LEVEL loglevel) {
        std::string fulllog;
        /* 添加时间 */
        char timeStr[25] = {0x00};
        XuString::getInstance().getStrFormTime(timeStr, 25, "%Y-%m-%d %H:%M:%S", false);
        fulllog.append(timeStr);
        /* 添加级别 */
        switch (loglevel) {
            case LOG_LEVEL_FATAL:{
                fulllog.append("  [FATAL]  ");
            }
                break;
            case LOG_LEVEL_CRITICAL:{
                fulllog.append("  [CRITICAL]  ");
            }
                break;
            case LOG_LEVEL_ERROR:{
                fulllog.append("  [ERROR]  ");
            }
                break;
            case LOG_LEVEL_WARNING:{
                fulllog.append("  [WARNING]  ");
            }
                break;
            case LOG_LEVEL_NOTICE:{
                fulllog.append("  [NOTICE]  ");
            }
                break;
            case LOG_LEVEL_INFORMATION:{
                fulllog.append("  [INFORMATION]  ");
            }
                break;
            case LOG_LEVEL_DEBUG:{
                fulllog.append("  [DEBUG]  ");
            }
                break;
            case LOG_LEVEL_TRACE:{
                fulllog.append("  [TRACE]  ");
            }
                break;
            default:{
                fulllog.append("  [UNKNOW]  ");
            }
                break;
        }

        /* 添加线程名 */
        char thName[100] = {0x00};
        pthread_getname_np(pthread_self(), thName, sizeof(thName));
        fulllog.append("[");
        fulllog.append(thName);
        fulllog.append("]  ");
        /* 添加TAG */
        fulllog.append(tag + ":  ");
        /* 添加Log内容 */
        fulllog.append(logContent + "\n");
        /* 输出到控制台 */
        printf("%s \n", fulllog.c_str());

        /* 输出到日志文件 */
        if (curSaveLogLevel <= loglevel) {
            putToCache(fulllog);
        }
    }

//    void XuLog::fatal(const std::string &tag, const std::string &logContent) {
//        showAndSaveLog(tag,logContent,LOG_LEVEL_FATAL);
//    }
//
//    void XuLog::critical(const std::string &tag, const std::string &logContent) {
//        showAndSaveLog(tag,logContent,LOG_LEVEL_CRITICAL);
//    }
//
//    void XuLog::error(const std::string &tag, const std::string &logContent) {
//        showAndSaveLog(tag,logContent,LOG_LEVEL_ERROR);
//    }
//
//    void XuLog::warning(const std::string &tag, const std::string &logContent) {
//        showAndSaveLog(tag,logContent,LOG_LEVEL_WARNING);
//    }
//
//    void XuLog::notice(const std::string &tag, const std::string &logContent) {
//        showAndSaveLog(tag,logContent,LOG_LEVEL_NOTICE);
//    }
//
//    void XuLog::information(const std::string &tag, const std::string &logContent) {
//        showAndSaveLog(tag,logContent,LOG_LEVEL_INFORMATION);
//    }
//
//    void XuLog::debug(const std::string &tag, const std::string &logContent) {
//        try{
//        showAndSaveLog(tag,logContent,LOG_LEVEL_DEBUG);
//        } catch (...) {
//            system("touch savelogFailed");
//        }
//    }
//
//    void XuLog::trace(const std::string &tag, const std::string &logContent) {
//        showAndSaveLog(tag,logContent,LOG_LEVEL_TRACE);
//    }

    const std::string &XuLog::getLogFileRootPath() const {
        return logFileRootPath;
    }

    void XuLog::onLogMakeFinish(const int loglevel, const char *logContent) {
        /* 输出到日志文件 */
        if (curSaveLogLevel >= loglevel) {
            putToCache(logContent);
        }
    }

    XuLogStream &XuLog::log(const int loglevel, const char *tag) {
        XuLogStream *ret = nullptr;
        /* 遍历找出可用的通道 */
        channelLock.lock();
        for(int i = 0; i < MAX_LOG_CHANNEL; i ++){
            if(!channel[i].getIsUsing()){
                /* 找到未使用的，先把指针拿出来 */
                ret = &channel[i];
                /* 设置成已使用 */
                ret->toBeUse();
                /* 设置日志的级别 */
                ret->setLogLevel(loglevel);
                break;
            }
        }
        channelLock.unlock();

        /* 找到通道了就写入内容 */
        if(ret != nullptr){
            /* 添加时间 */
            char strTemp[100] = {0x00};
            XuString::getInstance().getStrFormTime(strTemp, sizeof(strTemp), "%Y-%m-%d %H:%M:%S", false);
            *ret << strTemp;
            /* 添加级别 */
            switch (loglevel) {
                case LOG_LEVEL_FATAL:{
                    *ret << "  [FATAL]  ";
                }
                    break;
                case LOG_LEVEL_CRITICAL:{
                    *ret << "  [CRITICAL]  ";
                }
                    break;
                case LOG_LEVEL_ERROR:{
                    *ret << "  [ERROR]  ";
                }
                    break;
                case LOG_LEVEL_WARNING:{
                    *ret << "  [WARNING]  ";
                }
                    break;
                case LOG_LEVEL_NOTICE:{
                    *ret << "  [NOTICE]  ";
                }
                    break;
                case LOG_LEVEL_INFORMATION:{
                    *ret << "  [INFORMATION]  ";
                }
                    break;
                case LOG_LEVEL_DEBUG:{
                    *ret << "  [DEBUG]  ";
                }
                    break;
                case LOG_LEVEL_TRACE:{
                    *ret << "  [TRACE]  ";
                }
                    break;
                default:{
                    *ret << "  [UNKNOW]  ";
                }
                    break;
            }

            /* 添加线程名 */
            pthread_getname_np(pthread_self(), strTemp, sizeof(strTemp));
            *ret << "[" << strTemp << "]  ";
            /* 添加TAG */
            *ret << tag << ":  ";
        }

        return *ret;
    }


} // vis