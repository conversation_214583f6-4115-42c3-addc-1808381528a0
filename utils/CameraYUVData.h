//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/24.
//

#ifndef VIS_G3_SOFTWARE_CAMERAYUVDATA_H
#define VIS_G3_SOFTWARE_CAMERAYUVDATA_H


#include <cstdint>
#include <Poco/RWLock.h>

/**
 * 从相机拿出来的YUV数据
 * */
class CameraYUVData {
public:
    int getCameraId() const;

    void setCameraId(int cameraId);

    int getYuvFormat() const;

    void setYuvFormat(int yuvFormat);

    int getDataLen() const;

    void setDataLen(int dataLen);

    uint8_t getTimestamp() const;

    void setTimestamp(uint8_t timestamp);

    void setYUVData(int len, const uint8_t *yuvBuf);



    int getYUVData(uint8_t *buf,const int bufLen);



    int getWidth() const;

    void setWidth(int width);

    int getHeight() const;

    void setHeight(int height);


private:
    /* 数据来源的相机的ID */
    int cameraId = -1;
    /* 图像的宽 */
    int width;
    /* 图像的高 */
    int height;
    /* YVU数据的缓冲区 */
    uint8_t *curYUVData;
    /* yuv缓冲区的长度 */
    int yuvCacheLen = 0;
    /* YUV数据的格式 */
    int yuvFormat;
    /* YUV数据的长度 */
    int dataLen = 0;
    /* 时间戳    单位：us */
    uint8_t timestamp;
    /* 读写YUV数据使用的读写锁 */
    Poco::RWLock yuvDataLock;




};


#endif //VIS_G3_SOFTWARE_CAMERAYUVDATA_H
