//
// Created by xwf on 2021/4/14.
//

#ifndef VISPECT_SRC_COMMON_TOOLS_FORMATLOG_H
#define VISPECT_SRC_COMMON_TOOLS_FORMATLOG_H

#include <iomanip>
#include <iostream>
#include <sstream>
#include <string>
#include "ErrorTable.h"

#include <Poco/Logger.h>

namespace vis {
    /**
     * 格式化类
     */
    class FormatLog {
    public:
        /**
         * 递归时的退出函数
         * @tparam T 最后一个参数的类型
         * @param t 最后一个参数
         */
        template<typename T>
        inline void format(std::stringstream &ss, const T &t) {
            ss << t;
        }

        /**
         * 把所有的参数作为ostream的<<重载符的参数
         * @tparam T 必须有的一个类型
         * @tparam TS 多个类型
         * @param t 将会作为<<的参数
         * @param ts 递归时使用的参数
         */
        template<typename T, typename... TS>
        inline void format(std::stringstream &ss, const T &t, const TS &...ts) {
            ss << t;
            format(ss, ts...);
        }

        /**
         * 把参数经过转换放入到format函数中处理
         * @tparam F 函数名的类型
         * @tparam L 行号的类型
         * @tparam T 第一个参数的类型
         * @tparam TS 一系列参数的类型
         * @param function 函数名
         * @param line 行号
         * @param t 第一个参数
         * @param ts 一系列参数
         * @return 处理完成的字符串
         */
        template<typename F, typename L, typename T, typename... TS>
        inline std::string formatParam(const F &function, const L &line, const T &t, const TS &...ts) {
            std::stringstream ss;
            format(ss, "[", function, "][", line, "]", t, ts...);
            return ss.str();
        }

        template<typename F, typename L, typename I, typename T, typename... TS>
        inline std::string formatHexParam(const F &function, const L &line, I info, const T *data, int len) {
            std::stringstream ss;

            // 避免空指针
            if (nullptr != data) {
                ss << "[" << function << "][" << line << "]" << info;
                ss << std::hex;
                ss << std::uppercase;
                for (int i = 0; i < len; ++i) {
                    ss << "0x" << (int) data[i] << " ";
                }
            } else {
                Poco::Logger::root().error(
                        std::string("[") + __func__ + "][" + std::to_string(__LINE__) + "]参数错误，" + function +
                        "函数传入了一个空指针");
            }

            return ss.str();
        }
    };
} // namespace vis

#endif // VISPECT_SRC_COMMON_TOOLS_FORMATLOG_H
