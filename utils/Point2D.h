//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/30.
//

#ifndef VIS_G3_SOFTWARE_POINT2D_H
#define VIS_G3_SOFTWARE_POINT2D_H

namespace vis {

    class Point2D {

    public:
        float x;
        float y;

        Point2D() {
            x = 0;
            y = 0;
        }

        Point2D(float x, float y) {
            this->x = x;
            this->y = y;
        }

        friend Point2D operator+(Point2D a, Point2D b) {
            Point2D p;
            p.x = a.x + b.x;
            p.y = a.y + b.y;
            return p;
        };

        friend Point2D operator-(Point2D a, Point2D b) {
            Point2D p;
            p.x = a.x - b.x;
            p.y = a.y - b.y;
            return p;
        };

        friend float operator*(Point2D a, Point2D b) {
            float p = a.x * b.x + a.y * b.y;
            return p;
        };

        friend Point2D operator*(Point2D a, float value) {
            Point2D p;
            p.x = a.x * value;
            p.y = a.y * value;
            return p;
        };

        friend float xlJi(Point2D a, Point2D b) {
            float p = ((a.x) * (b.y)) - ((a.y) * (b.x));
            return p;
        };

    };

} // vis

#endif //VIS_G3_SOFTWARE_POINT2D_H
