//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/1.
//

#include <cstdlib>
#include <sys/time.h>
#include <chrono>
#include <cstdio>
#include <sstream>
#include <iomanip>

#include "XuString.h"

#include "XuTimeUtil.h"
#include "G3_Configuration.h"
#include "XuShell.h"
#include <boost/date_time/local_time/local_time.hpp>

XuTimeUtil &XuTimeUtil::getInstance() {
    static XuTimeUtil instance;
    return instance;
}

uint64_t XuTimeUtil::currentTimeMillis() {
    struct timeval time;
    /* 获取时间，理论到us */
    gettimeofday(&time, nullptr);
    uint64_t sec = time.tv_sec;
    sec *= 1000;
    return sec + time.tv_usec / 1000;

}

__suseconds_t XuTimeUtil::currentTimeMicro() {
    struct timeval time;
    /* 获取时间，理论到us */
    gettimeofday(&time, nullptr);
    return time.tv_usec;
}

XuTimeUtil::XuTime<PERSON><PERSON>() {

}

XuTimeUtil::~XuTimeUtil() {

}

__time_t XuTimeUtil::currentTimeSecond() {
    struct timeval time;
    /* 获取时间，理论到us */
    gettimeofday(&time, nullptr);
    return time.tv_sec;
}

bool
XuTimeUtil::setGPSTimeToSystemTime(int year, int mon, int day, int hour, int min, int sec, std::string localTimeZone) {

    /* 先获取系统对应的UTC时间 */
    time_t cur_time = time(nullptr);
    struct tm local;
    gmtime_r(&cur_time, &local);


    /* 比较下年月日时分是否有不同  有不同则需要更新 */
    if (std::abs(local.tm_year - (year - 1900)) != 0 || std::abs(local.tm_mon - (mon - 1)) != 0
        || std::abs(local.tm_mday - day) != 0 || std::abs(local.tm_hour - hour) != 0
        || std::abs(local.tm_min - min) != 0) {

        struct tm *p = new struct tm();

        struct timeval tv;

        struct timezone tz;

        /* 由于mktime是会受时区影响的 而GPS给过来的时间固定是UTC时间  所以保险起见先把时区设置为UTC */
        setenv("TZ", "UTC", 1);
        /* 此方法的主要目的是为了获取时区信息 */
        gettimeofday(&tv, &tz);

        /* 把GPS过来的时间设置到tm里面 */
        p->tm_year = year - 1900;
        p->tm_mon = mon - 1;
        p->tm_mday = day;
        p->tm_hour = hour;
        p->tm_min = min;
        p->tm_sec = sec;

        /* 转成UTC时间  单位：秒 */
        time_t utc_t = mktime(p);

        delete (p);

        /* 把秒数设置给timeval */
        tv.tv_sec = utc_t;
        tv.tv_usec = 0;
        /* 这里设置系统时间 */
        if (settimeofday(&tv, &tz) < 0) {
            return false;
        }
        /* 系统时间设置成功后   同步给硬件时钟  因为硬件时间是UTC时间  所以在这里同步 */
        XuShell::getInstance().runShellWithTimeout("export TZ=:UTC && hwclock -w");

//    setenv("TZ","Europe/London",1);

        /* 在这里把时区设置回当地时区 */
        setenv("TZ", localTimeZone.c_str(), 1);


//        cur_time=time(nullptr);
//        localtime_r( &cur_time, &local );


        return true;

    } else {
        return false;
    }

}

bool XuTimeUtil::setTimeToSystemTime(int year, int mon, int day, int hour, int min, int sec, std::string timeZone,
                                     std::string localTimeZone) {

    bool ret = false;
    try {
        /* 先把时区设置成客户端的时区 */
        setenv("TZ", timeZone.c_str(), 1);
        /* 获取在这个时区下设备当前的TM格式的时间 */
        time_t curtimer = time(nullptr);
        struct tm *local = localtime(&curtimer);

        /* 比较下年月日时分是否有不同  有不同则需要更新 */
        if (std::abs(local->tm_year - (year - 1900)) != 0 || std::abs(local->tm_mon - (mon - 1)) != 0
            || std::abs(local->tm_mday - day) != 0 || std::abs(local->tm_hour - hour) != 0
            || std::abs(local->tm_min - min) != 0 || std::abs(local->tm_sec - sec) != 0) {
            /* 把收到的TM格式的时间转成时间戳 */
            local->tm_year = year - 1900;
            local->tm_mon = mon - 1;
            local->tm_mday = day;
            local->tm_hour = hour;
            local->tm_min = min;
            local->tm_sec = sec;
            local->tm_isdst = local->tm_isdst;
            time_t time_ml = mktime(local);
            /* 把时间戳设置给系统 */
            struct timeval tv;
            tv.tv_sec = time_ml;
            tv.tv_usec = 0;
            if (settimeofday(&tv, nullptr) < 0) {
                ret = false;
            }else{
                /* 系统时间设置成功后   同步给硬件时钟  因为硬件时间是UTC时间  所以在先把时区改成UTC时区  再同步 */
                XuShell::getInstance().runShellWithTimeout("export TZ=:UTC && hwclock -w");
                /* 在这里把时区设置回当地时区 */
                setenv("TZ", localTimeZone.c_str(), 1);
                ret = true;
            }

        } else {
            ret = false;
        }
    } catch (...) {
        printf("setTimeToSystemTime has error! %s \n", strerror(errno));
        ret = false;
    }
    return ret;
}

bool XuTimeUtil::getUTCTime_BCD(uint8_t *bcdTime) {
    bool ret = false;
    /* 先获取系统对应的UTC时间 */
    time_t cur_time = time(nullptr);
    struct tm local;
    gmtime_r(&cur_time, &local);
    std::string bdcStr = "";
    std::stringstream ss;
    ss << std::setw(4) << std::setfill('0') << (local.tm_year + 1900);
    ss << std::setw(2) << std::setfill('0') << (local.tm_mon + 1);
    ss << std::setw(2) << std::setfill('0') << (local.tm_mday);
    ss << std::setw(2) << std::setfill('0') << (local.tm_hour);
    ss << std::setw(2) << std::setfill('0') << (local.tm_min);
    ss << std::setw(2) << std::setfill('0') << (local.tm_sec);
    ss << std::setw(2) << std::setfill('0') << (0);
    ss >> bdcStr;
    printf("*********bdcStr=%s*********\n", bdcStr.c_str());
    ret = XuString::getInstance().string2Bcd(bdcStr, bcdTime) > 0;

    return ret;
}


time_t XuTimeUtil::bcdToTimestamp_Sec(uint8_t *bcdTime, const std::string timezone) {
    time_t ret = 0;
    try {
        setenv("TZ",timezone.c_str(),1);
        tzset();
        time_t curtimer = time(nullptr);
        struct tm *local = localtime(&curtimer);
        /* 把收到的TM格式的时间转成时间戳 */
        local->tm_year = (((bcdTime[1] >> 4) * 10) + (bcdTime[1] & 0x0F) + 2000) - 1900;
        local->tm_mon = ((bcdTime[2] >> 4) * 10) + (bcdTime[2] & 0x0F) - 1;
        local->tm_mday = ((bcdTime[3] >> 4) * 10) + (bcdTime[3] & 0x0F);
        local->tm_hour = ((bcdTime[4] >> 4) * 10) + (bcdTime[4] & 0x0F);
        local->tm_min = ((bcdTime[5] >> 4) * 10) + (bcdTime[5] & 0x0F);
        local->tm_sec = ((bcdTime[6] >> 4) * 10) + (bcdTime[6] & 0x0F);
        time_t time_ml = mktime(local);
        ret = time_ml;
    } catch (...) {
        printf("bcdToTimestamp_Sec has error! %s \n", strerror(errno));
    }
    return ret;
}

time_t XuTimeUtil::getUTCTimestamp_Sec(const uint32_t timestamp, const std::string timezone) {
    // Convert the timestamp to a Boost POSIX time object
    boost::posix_time::ptime localTime = boost::posix_time::from_time_t(timestamp);


    // Get the local time zone
    boost::local_time::time_zone_ptr localTimeZone(new boost::local_time::posix_time_zone(timezone));

    // Create a local date-time object using the local time zone
    boost::local_time::local_date_time localDateTime(localTime, localTimeZone);

    // Convert the local date-time to UTC time
    boost::posix_time::ptime utcTime = localDateTime.utc_time();

    // Convert the UTC time to a Boost POSIX time object
    boost::posix_time::ptime epoch(boost::gregorian::date(1970, 1, 1));
    boost::posix_time::time_duration timeFromEpoch = utcTime - epoch;

    // Return the UTC timestamp in seconds
    return timeFromEpoch.total_seconds();
}

uint64_t XuTimeUtil::getTimeFromSysStartUp_Millis() {
    uint64_t ret = 0;

    double uptime_seconds;
    std::ifstream uptimeFile("/proc/uptime");
    if (uptimeFile >> uptime_seconds) {
//        std::cout << "System has been running for "
//                  << uptime_seconds << " seconds." << std::endl;
        /* 转成毫秒级别 */
        ret = uptime_seconds * 1000;
    } else {
        std::cerr << "getTimeFromSysStartUp_Millis Failed to read /proc/uptime" << std::endl;
    }
    uptimeFile.close();
    return ret;
}

uint64_t XuTimeUtil::getTimeFromSysStartUp_Sec() {
    uint64_t ret = 0;
    std::ifstream uptimeFile("/proc/uptime");
    if (uptimeFile >> ret) {
//        std::cout << "System has been running for "
//                  << ret << " seconds." << std::endl;
    } else {
        std::cerr << "getTimeFromSysStartUp_Sec Failed to read /proc/uptime" << std::endl;
    }
    uptimeFile.close();
    return ret;
}
