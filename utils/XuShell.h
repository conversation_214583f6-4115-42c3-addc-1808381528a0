//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/22.
//

#ifndef VIS_G3_SOFTWARE_XUSHELL_H
#define VIS_G3_SOFTWARE_XUSHELL_H

#include <string>

class XuShell {
public:
    /* 单例 */
    static XuShell &getInstance();

    /**
     * 运行SHELL命令
     *
     * @param cmd ： 命令内容
     * @param msec : 超时时间（单位ms）
     * @return  结果
     */
    std::string runShellWithTimeout(std::string cmd, int msec = 30000);

    /**
     * 关闭G3软件
     *
     * @param tag ：用来表明杀死的原因的标签
     *
     */
    void killallG3sofeward(std::string tag);


    /**
     * reboot 设备
     *
     * @param tag ：用来表明重启原因的标签
     *
     */
    void rebootG3sofeward(std::string tag);

    /**
     * 启动重置应用
     */
    void startResetApp();

    /**
     * 停止重置应用
     */
    void stopResetApp();

private:

};


#endif //VIS_G3_SOFTWARE_XUSHELL_H
