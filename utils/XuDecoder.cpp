//
// Created by Administrator on 2025/8/7.
//
#include <cstring>
#include <cstdio>
#include "rkmedia_api.h"
#include "XuTimeUtil.h"
#include "XuDecoder.h"

int XuDecoder::initDecoder(int width, int height, int vDecchn) {
    int ret = -1;
    RK_U32 u32DispWidth = 720;
    RK_U32 u32DispHeight = 1280;
    RK_BOOL bIsHardware = RK_TRUE;



    RK_MPI_SYS_Init();

    // VDEC
    VDEC_CHN_ATTR_S stVdecAttr;
    stVdecAttr.enCodecType = RK_CODEC_TYPE_H264;
    stVdecAttr.enMode = VIDEO_MODE_STREAM;
    stVdecAttr.enDecodecMode = VIDEO_DECODEC_HADRWARE;

    curVDecChn = vDecchn;

    ret = RK_MPI_VDEC_CreateChn(cur<PERSON><PERSON><PERSON><PERSON>, &stVdecAttr);
    if (ret) {
        printf("Create Vdec[%d] failed! ret=%d\n",curVD<PERSON><PERSON>hn, ret);
        return -1;
    }

    return ret;
}

int XuDecoder::toDecodeH264(const CameraH264Data *camerah264Data) {
    int ret = -1;
    MEDIA_BUFFER mb = RK_MPI_MB_CreateBuffer(camerah264Data->getDataLen(), RK_FALSE, 0);
    memcpy(RK_MPI_MB_GetPtr(mb),camerah264Data->getCurH264Data(),camerah264Data->getDataLen());
    RK_MPI_MB_SetSize(mb, camerah264Data->getDataLen());
    ret = RK_MPI_SYS_SendMediaBuffer(RK_ID_VDEC, curVDecChn, mb);
//    printf("#Send packet(%p, %zuBytes) to VDEC[0].ret=%d \n", RK_MPI_MB_GetPtr(mb),
//           RK_MPI_MB_GetSize(mb),ret);
    RK_MPI_MB_ReleaseBuffer(mb);
    return ret;
}

int XuDecoder::getYUVData(uint8_t *yuvBuf) {
    int ret = -1;


    MEDIA_BUFFER mb = NULL;

    MPP_CHN_S VdecChn, VoChn;
    VdecChn.enModId = RK_ID_VDEC;
    VdecChn.s32DevId = 0;
    VdecChn.s32ChnId = curVDecChn;

    mb = RK_MPI_SYS_GetMediaBuffer(RK_ID_VDEC, curVDecChn, 40);

    MB_IMAGE_INFO_S stImageInfo = {0};
    ret = RK_MPI_MB_GetImageInfo(mb, &stImageInfo);
    if (ret) {
        printf("Get image info failed! ret = %d\n", ret);
        RK_MPI_MB_ReleaseBuffer(mb);
        return NULL;
    }

//    printf("Get Frame:ptr:%p, fd:%d, size:%zu, mode:%d, channel:%d, "
//           "timestamp:%lld, ImgInfo:<wxh %dx%d, fmt 0x%x>, ret: \n",
//           RK_MPI_MB_GetPtr(mb), RK_MPI_MB_GetFD(mb), RK_MPI_MB_GetSize(mb),
//           RK_MPI_MB_GetModeID(mb), RK_MPI_MB_GetChannelID(mb),
//           RK_MPI_MB_GetTimestamp(mb), stImageInfo.u32Width,
//           stImageInfo.u32Height, stImageInfo.enImgType,ret);

    memcpy(yuvBuf,RK_MPI_MB_GetPtr(mb),RK_MPI_MB_GetSize(mb));
    ret = RK_MPI_MB_GetSize(mb);

    RK_MPI_MB_ReleaseBuffer(mb);

    return ret;
}

int XuDecoder::releaseDecoder() {
    int ret = -1;
    ret = RK_MPI_VDEC_DestroyChn(curVDecChn);
    if (ret) {
        printf("ERROR: Destroy VDEC[%d] error! ret=%d\n", curVDecChn, ret);

    }
    return ret;
}
