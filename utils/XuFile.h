//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/15.
//

#ifndef VIS_ADOS_I7_XUFILE_H
#define VIS_ADOS_I7_XUFILE_H


#include <vector>
#include <string>
#include <cstdint>


class XuFile {
public:

    /* 升级的文件的加密类型-----不加密 */
    const static int UPGRADE_FILE_ENCRYPTION_TYPE_NORMAL = 0;
    /* 升级的文件的加密类型-----不加密 */
    const static int UPGRADE_FILE_ENCRYPTION_TYPE_INTERVAL_INVERT = 1;

    /* 单例 */
    static XuFile &getInstance();

    /**
     * 获取目录下的文件（只限获取表层目录的文件，子目录不算，而且必须是标准文件才行）
     *
     * @param path：目录路径
     * @param filter:文件名需要包含的关键字（传入nullptr时代表所有文件名都行）
     *
     * @return 文件名列表
     * */
    std::vector<std::string> getDirAllFile_Harsh(const char *path, const char *filter);

    /**
     * 获取目录下的文件（只限获取表层目录的文件，子目录不算，不是标准文件也许）
     *
     * @param path：目录路径
     * @param filter:文件名需要包含的关键字（传入nullptr时代表所有文件名都行）
     *
     * @return 文件名列表
     * */
    std::vector<std::string> getDirAllFile(const char *path, const char *filter);

    /**
     * 获取目录下的文件（包含子目录，不是普通文件也算）
     *
     * @param path：目录路径
     * @param filter:文件名需要包含的关键字（传入nullptr时代表所有文件名都行）
     *
     * @return 文件名列表
     * */
    std::vector<std::string> getDirAllFile_Full(const char *path, const char *filter);

    /**
     * 获取目录下所有文件的的总长度(包括子目录的文件)
     *
     * @param path ：目录路径
     *
     * @return 长度
     * */
    uint64_t getAllFileLength(const char *path);

    /**
     * 删除指定目录下的文件（只搜索表层目录下的文件）
     *
     * @param path：目录
     * @param count：删除多少个？
     * @param noZero : 长度为0的文件不算，必须不为0才会被计算到删除的文件数里
     * */
    void removeOldMp4File(const char *path, int count, const bool noZero = false);

    /**
     * 删除指定目录下的MP4文件（搜索所有文件，包括子目录）
     *
     * @param path：目录
     * @param count：删除多少个？
     * @param noZero : 长度为0的文件不算，必须不为0才会被计算到删除的文件数里
     * */
    void removeOldMp4File_Full(const char *path, const int count, const bool noZero = false);

    /**
     * 删除指定目录下的JPG文件（搜索所有文件，包括子目录）
     *
     * @param path：目录
     * @param count：删除多少个？
     * @param noZero : 长度为0的文件不算，必须不为0才会被计算到删除的文件数里
     * */
    void removeOldJPGFile_Full(const char *path, const int count, const bool noZero = false);


    /**
     * 创建目录（包含子目录）
     *
     * @param path：目录
     * @param mode：权限  默认0755
     * */
    int mkpath(std::string path, mode_t mode = 0755);

    /**
     * 文件是否存在
     *
     * @param path：文件路径
     *
     * @return 是否存在
     * */
    bool fileExists(const char *path);

    /**
     * 分区是否被挂载（只用于RV1126  其他芯片没使用过）
     *
     * @param path：挂载目录
     *
     * @return 是否被挂载
     * */
    bool hasMount(const char *path);

    /**
     * 保存字符串到文件
     *
     * @param filePath ： 文件路径
     * @param strContent ： 字符串内容
     * @param isReplace ： 是否以覆盖的方式  如果否 则在尾部添加
     *
     * @return 写入字符串的长度
     *
     * */
    int saveStrToFile(const char *filePath, std::string strContent, bool isReplace);

    /**
     * 读取某个文件的内容（从0开始读到buf长度或者读完）
     *
     * @param filePath ： 文件路径
     * @param buf ： 存放读取出来的数据的内容
     * @param bufLen ： 存放读取出来的数据的内容的长度
     *
     * @return 读取出来的数据的长度
     *
     * */
    int readFile(const char *filePath, uint8_t *buf, const int bufLen);

    /**
     * 保存二进制到文件
     *
     * @param filePath ： 文件路径
     * @param buf ： 需要保存的数据
     * @param bufLen ： 需要保存的数据的长度
     *
     * @return 保存的文件的长度
     *
     * */
    int writeFile(const char *filePath, uint8_t *buf, const int bufLen);

    /**
     * 移动文件到另一个地方
     *
     * @param srcFilePath ： 源文件路径
     * @param desFilePath ： 目标文件路径
     * @param offset ： 从原文件的哪个位置开始复制
     *
     * @return 保存的文件的长度
     *
     * */
    int moveFille(const char *srcFilePath, const char *desFilePath, const int offset);

    /**
     * 对升级文件进行解密
     *
     * @param filePath ： 源文件路径
     * @param decPath ： 解密后文件的存放路径
     * @param fileHead ： 文件的头部
     *
     * @return 解密的结果 0:成功   其他:失败
     *
     * */
    int doDecryptForUpgrade(const char *srcPath, const char *decPath, uint8_t *fileHead);

    /**
     * 获取Disk的总容量
     *
     * @param path ： 路径
     *
     * @return 总容量
     *
     * */
    uint64_t getDiskTotalSize(const char *path);

    /**
     * 获取Disk的剩余容量
     *
     * @param path ： 路径
     *
     * @return 剩余容量
     *
     * */
    uint64_t getDiskFreeSize(const char *path);

    /**
     * 检查MP4文件是否正常
     *
     * @param mp4Path : MP4文件的路径
     * @return true：正常  false：已损坏
     */
    bool checkMp4File(const char *mp4Path);

    /**
     * 通过shell命令的方式删除一个文件
     *
     * @param filePath ：文件路径
     * @return 结果  0：成功  其他：失败
     */
    int removeAFileByShell(const char *filePath);

    /**
     * 获取文件的长度
     *
     * @param path ：文件路径
     *
     * @return 长度
     * */
    long long getFileLength(const char *path);

    /**
     * 获取文件的MD5值
     *
     * @param filePath ： 文件路径
     * @param md5Buf ： 存放MD5的内存
     * @return MD5的长度
     */
    int getMD5FromFile(const char *filePath, uint8_t *md5Buf);

    /**
     * 获取byte数组的MD5值
     *
     * @param bytes ： byte数组
     * @param bytesLen ： byte数组的长度
     * @param md5Buf ： 存放MD5的内存
     * @return MD5的长度
     */
    int getMD5OfFromtes(uint8_t *bytes, int bytesLen, uint8_t *md5Buf);

    /**
     * 用AES-256-CBC的方式加密一个文件
     * @param inputFile ： 输入文件（源文件）地址
     * @param outputFile ： 输出文件（加密后的文件）地址
     * @param key ： 密钥
     * @param iv ： 初始向量
     * @return  0：成功  其他：失败
     */
    int encryptFile_AES_256_CBC(const std::string &inputFile, const std::string &outputFile, const uint8_t *key, const uint8_t *iv);


    /**
     * 用AES-256-CBC的方式解密一个文件
     * @param inputFile ： 输入文件（源文件）地址
     * @param outputFile ： 输出文件（解密后的文件）地址
     * @param key ： 密钥
     * @param iv ： 初始向量
     * @return  0：成功  其他：失败
     */
    int decryptFile_AES_256_CBC(const std::string& inputFile, const std::string& outputFile, const uint8_t *key, const uint8_t *iv);

    /**
     * 删除文件
     *
     * @param filePath ： 文件路径
     */
    void deleteFile(const std::string& filePath);


    int PKCS7_PAD(uint8_t *buf,int len,int blockSize);

private:
    /* 带有完整路径的文件信息 */
    struct FullFileInfo{
        std::string fileName;
        std::string filPath;
    };


};


#endif //VIS_ADOS_I7_XUFILE_H
