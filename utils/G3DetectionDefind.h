//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/30.
//

#ifndef VIS_G3_SOFTWARE_G3DETECTIONDEFIND_H
#define VIS_G3_SOFTWARE_G3DETECTIONDEFIND_H

#include <opencv2/core/types.hpp>
#include "G3_Configuration.h"

enum EVENTLIST : int8_t {
    /* 不做报警 */
    EVENT_UNKNOW = -1,
    /* 侧边-左侧向前的一级行人报警 */
    EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1 = 0,
    /* 侧边-左侧向后的一级行人报警 */
    EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1,
    /* 侧边-右侧向前的一级行人报警 */
    EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1,
    /* 侧边-右侧向后的一级行人报警 */
    EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1,
    /* 侧边-左侧向前的二级行人报警 */
    EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2,
    /* 侧边-左侧向后的二级行人报警 */
    EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2,
    /* 侧边-右侧向前的二级行人报警 */
    EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2,
    /* 侧边-右侧向后的二级行人报警 */
    EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2,
    /* 侧边-左侧向前镜头遮挡报警 */
    EVENT_BSD_LEFT_FORWARD_CAMERA_COVER,
    /* 侧边-左侧向后镜头遮挡报警 */
    EVENT_BSD_LEFT_BACKWARD_CAMERA_COVER,
    /* 侧边-右侧向前镜头遮挡报警 */
    EVENT_BSD_RIGHT_FORWARD_CAMERA_COVER,
    /* 侧边-右侧向后镜头遮挡报警 */
    EVENT_BSD_RIGHT_BACKWARD_CAMERA_COVER,
    /* 侧边-正前向一级行人报警 */
    EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1,
    /* 侧边-正后向一级行人报警 */
    EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1,
    /* 侧边-正前向二级行人报警 */
    EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2,
    /* 侧边-正后向二级行人报警 */
    EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2,
    /* 侧边-正前向镜头遮挡报警 */
    EVENT_PDW_FORWARD_CAMERA_COVER,
    /* 侧边-正后向镜头遮挡报警 */
    EVENT_PDW_BACKWARD_CAMERA_COVER,
    /* 侧边-左侧向前的一级车辆报警 */
    EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1,
    /* 侧边-左侧向后的一级车辆报警 */
    EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1,
    /* 侧边-右侧向前的一级车辆报警 */
    EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1,
    /* 侧边-右侧向后的一级车辆报警 */
    EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1,
    /* 侧边-正前方的一级车辆报警 */
    EVENT_BSD_FORWARD_VEHICLE_LEVEL1,
    /* 侧边-正后方的一级车辆报警 */
    EVENT_BSD_BACKWARD_VEHICLE_LEVEL1,

    /* 侧边-左侧向前的二级车辆报警 */
    EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2,
    /* 侧边-左侧向后的二级车辆报警 */
    EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2,
    /* 侧边-右侧向前的二级车辆报警 */
    EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2,
    /* 侧边-右侧向后的二级车辆报警 */
    EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2,
    /* 侧边-正前方的二级车辆报警 */
    EVENT_BSD_FORWARD_VEHICLE_LEVEL2,
    /* 侧边-正后方的二级车辆报警 */
    EVENT_BSD_BACKWARD_VEHICLE_LEVEL2,
    /* DSM- DSM报警（具体报警类型在其他地方） */
    EVENT_DSM,
    /* SBST- 前镜头行人一级报警*/
    EVENT_SBST_FORWARD_PDW_LEVEL1,
    /* SBST- 前镜头行人二级报警*/
    EVENT_SBST_FORWARD_PDW_LEVEL2,
    /* SBST- 前镜头车辆一级报警*/
    EVENT_SBST_FORWARD_FCW_LEVEL1,
    /* SBST- 前镜头车辆二级报警*/
    EVENT_SBST_FORWARD_FCW_LEVEL2,
    /* SBST- 前镜头压线-左边虚线 */
    EVENT_SBST_FORWARD_LDW_LEFT_DASHED,
    /* SBST- 前镜头压线-左边实线 */
    EVENT_SBST_FORWARD_LDW_LEFT_SOLID,
    /* SBST- 前镜头压线-右边虚线 */
    EVENT_SBST_FORWARD_LDW_RIGHT_DASHED,
    /* SBST- 前镜头压线-右边虚线 */
    EVENT_SBST_FORWARD_LDW_RIGHT_SOLID,

    /* ADAS车辆TTC报警 */
    EVENT_ADAS_VEHICLE_TTC,
    /* ADAS车辆headway报警 */
    EVENT_ADAS_VEHICLE_HMW,

    /* ADAS行人的headway报警 */
    EVENT_ADAS_PEDESTRIAN_HMW,

    /* ADAS压左实线报警 */
    EVENT_ADAS_LDW_LEFT_SOLID,
    /* ADAS压左虚线报警 */
    EVENT_ADAS_LDW_LEFT_DASH,
    /* ADAS压左实线报警 */
    EVENT_ADAS_LDW_RIGHT_SOLID,
    /* ADAS压左虚线报警 */
    EVENT_ADAS_LDW_RIGHT_DASH,

    /* R151的报警区域1的行人报警 */
    EVENT_BSD_R151_AREA1_PEDESTRIAN,
    /* R151的报警区域2的行人报警 */
    EVENT_BSD_R151_AREA2_PEDESTRIAN,
    /* R151的报警区域3的行人报警 */
    EVENT_BSD_R151_AREA3_PEDESTRIAN,
    /* R151的报警区域4的行人报警 */
    EVENT_BSD_R151_AREA4_PEDESTRIAN,
    /* R151的报警区域1的车辆报警 */
    EVENT_BSD_R151_AREA1_VEHICLE,
    /* R151的报警区域2的车辆报警 */
    EVENT_BSD_R151_AREA2_VEHICLE,
    /* R151的镜头遮挡报警 */
    EVENT_BSD_R151_CAMERA_COVER,

    /* R158的报警区域1的行人报警 */
    EVENT_BSD_R158_AREA1_PEDESTRIAN,
    /* R158的报警区域2的行人报警 */
    EVENT_BSD_R158_AREA2_PEDESTRIAN,
    /* R158的报警区域3的行人报警 */
    EVENT_BSD_R158_AREA3_PEDESTRIAN,
    /* R158的报警区域1的车辆报警 */
    EVENT_BSD_R158_AREA1_VEHICLE,
    /* R158的报警区域2的车辆报警 */
    EVENT_BSD_R158_AREA2_VEHICLE,
    /* R158的镜头遮挡报警 */
    EVENT_BSD_R158_CAMERA_COVER,

    /* R159的报警区域1的行人报警 */
    EVENT_BSD_R159_AREA1_PEDESTRIAN,
    /* R159的报警区域2的行人报警 */
    EVENT_BSD_R159_AREA2_PEDESTRIAN,
    /* R159的报警区域3的行人报警 */
    EVENT_BSD_R159_AREA3_PEDESTRIAN,
    /* R159的报警区域1的车辆报警 */
    EVENT_BSD_R159_AREA1_VEHICLE,
    /* R159的报警区域2的车辆报警 */
    EVENT_BSD_R159_AREA2_VEHICLE,
    /* R159的镜头遮挡报警 */
    EVENT_BSD_R159_CAMERA_COVER,

    /* ADAS防溜车提醒 */
    EVENT_ADAS_VEHICLE_VB,
    /* ADAS前车启动 */
    EVENT_ADAS_VEHICLE_GO,




};

struct AlarmEventInfo_BSD {
    EVENTLIST eventCode = EVENT_UNKNOW;
    int32_t xDistance = 0;
    int32_t yDistance = 0;
    uint32_t mRealWidth = 0;
    uint32_t mRealHeight = 0;
    uint32_t alarmPedestrianId = 0xFFFFFFFF;
    uint32_t alarmVehicleId = 0xFFFFFFFF;
};

struct AlarmEventInfo_DSM {
    //口罩报警状态
    bool respirator_alarm = false;
    //闭眼报警状态
    bool eye_alarm = false;
    //哈欠报警状态
    bool mouth_alarm = false;
    //左顾右盼报警状态
    bool lookaround_alarm = false;
    //离岗报警状态
    bool facemissing_alarm = false;
    //遮挡报警状态
    bool camcover_alarm = false;
    //抽烟报警状态
    bool smoking_alarm = false;
    //打电话报警状态
    bool phone_alarm = false;
    //疲劳程度
    uint8_t fatigue_rank = false;
    //安全带报警状态
    bool seatbelt_alarm = false;
    //盲点报警状态
    bool blindspot_alarm = false;
    //帽子报警状态
    bool hat_alarm = false;

};

struct AlarmEventInfo_SBST_Forward {
    EVENTLIST eventCode = EVENT_UNKNOW;
    int32_t xDistance = 0;
    int32_t yDistance = 0;
    uint32_t mRealWidth = 0;
    uint32_t mRealHeight = 0;
    uint32_t alarmPedestrianId = 0xFFFFFFFF;
    uint32_t alarmVehicleId = 0xFFFFFFFF;
    float headway = -1.0; //1.2s
    float ttc = -1.0; //1.2s
};

/* 报警事件的状态（主要是485协议需要） */
enum EVENTSTATUS{
    /* 不可用 */
    EVENTSTATUS_UNAVAILABLE = 0x00,
    /* 开始标志 */
    EVENTSTATUS_START = 0x01,
    /* 结束标志 */
    EVENTSTATUS_STOP = 0x02,
};

/* 道路识别类型（主要是485协议需要） */
enum TRAFFICTYPE{
    /* 限速标志 */
    TRAFFICTYPE_SPEEDLIMIT = 0x01,
    /* 限高标志 */
    TRAFFICTYPE_HEIGHTLIMIT = 0x02,
    /* 限重标志 */
    EVENTSTATUS_WEIGHTLIMIT = 0x03,
};

struct AlarmEventInfo_ADAS {
    /* 事件状态 0x00：不可用   0x01：开始标志   0x02：结束标志 */
    EVENTSTATUS eventStatus = EVENTSTATUS_UNAVAILABLE;
    EVENTLIST eventCode = EVENT_UNKNOW;
    float headway = -1.0; // 1.2s
    float ttc = -1.0;   //1.2s
    uint8_t trafficType = 0;


};

struct AlarmEventInfo_R151 {
    EVENTLIST eventCode = EVENT_UNKNOW;
    uint32_t alarmPedestrianId = 0xFFFFFFFF;
    uint32_t alarmVehicleId = 0xFFFFFFFF;
};

struct AlarmEventInfo_R158 {
    EVENTLIST eventCode = EVENT_UNKNOW;
    uint32_t alarmPedestrianId = 0xFFFFFFFF;
    uint32_t alarmVehicleId = 0xFFFFFFFF;
};

struct AlarmEventInfo_R159 {
    EVENTLIST eventCode = EVENT_UNKNOW;
    uint32_t alarmPedestrianId = 0xFFFFFFFF;
    uint32_t alarmVehicleId = 0xFFFFFFFF;
};

/**
 *  整理好的报警事件信息 所有外设需要的报警信息都打包在里面了
 */
struct AlarmEventInfo {
    /* 车速 */
    float speed = -1.0;
    /* 侧边算法的报警信息 */
    AlarmEventInfo_BSD bsdAlarmInfo;
    /* DSM的报警信息 */
    AlarmEventInfo_DSM dsmAlarmInfo;
    /* 新加坡前向镜头的报警信息 */
    AlarmEventInfo_SBST_Forward sbstForwardAlarmInfo;
    /* ADAS的报警信息 */
    AlarmEventInfo_ADAS adasAlarmInfo;
    /* GPS信息 */
    GPSInfo curGpsInfo;
    /* 报警开始的时间 */
    uint64_t alarmTime = 0;
    /* R151的报警信息 */
    AlarmEventInfo_R151 r151AlarmInfo;
    /* R158的报警信息 */
    AlarmEventInfo_R158 r158AlarmInfo;
    /* R159的报警信息 */
    AlarmEventInfo_R159 r159AlarmInfo;
    /* 是否是一个影子报警 */
    bool isShadow = false;


};

/* 侧边算法的识别信息中的行人标志定义 */
struct BSDDetectionInfo_Pedestrian_Status {
    bool left_level2: 1;
    bool right_level2: 1;
    bool forward_level2: 1;
    bool backward_level2: 1;
    bool left_level1: 1;
    bool right_level1: 1;
    bool forward_level1: 1;
    bool backward_level1: 1;
};

/* 侧边算法的识别信息中的车辆标志定义 */
struct BSDDetectionInfo_Vehicle_Status {
    bool left_level2: 1;
    bool right_level2: 1;
    bool forward_level2: 1;
    bool backward_level2: 1;
    bool left_level1: 1;
    bool right_level1: 1;
    bool forward_level1: 1;
    bool backward_level1: 1;
};

/* 侧边行人算法的识别信息中的摄像头遮挡状态定义 */
struct BSDDetectionInfo_Camera_Cover_Status {
    bool left: 1;
    bool right: 1;
    bool forward: 1;
    bool backward: 1;
    uint reserved: 4;
};


/* 侧边行人算法的识别信息中的摄像头Bypass状态定义 */
struct BSDDetectionInfo_Bypass_Status {
    bool left: 1;
    bool right: 1;
    bool forward: 1;
    bool backward: 1;
    uint reserved: 4;
};

/* 侧边行人算法的识别信息中的摄像头Bypass状态定义 */
struct BSDDetectionInfo_Bypass_Status_By_Camera {
    bool camera1: 1;
    bool camera2: 1;
    uint reserved: 6;
};


/* 侧边行人算法的识别信息 */
struct BSDDetectionInfo {
    BSDDetectionInfo_Pedestrian_Status pedestrianStatus = {false, false, false, false, false, false, false, false};
    BSDDetectionInfo_Camera_Cover_Status cameraCoverStatus = {false, false, false, false, 0};
    BSDDetectionInfo_Bypass_Status bypassStatus = {false, false, false, false, 0};
    BSDDetectionInfo_Vehicle_Status vehicleStatus = {false, false, false, false, false, false, false, false};
    void reset(){
        pedestrianStatus = {false, false, false, false, false, false, false, false};
        cameraCoverStatus = {false, false, false, false, 0};
        bypassStatus = {false, false, false, false, 0};
        vehicleStatus = {false, false, false, false, false, false, false, false};
    };
};

/* R151侧边行人算法的识别信息 */
struct R151_BSDDetectionInfo {
    /* 0为镜头安装在车辆左侧，  1为镜头安装在车辆右侧 */
    int installPosition = 0;
    /* 区域1是否有人 */
    bool hasPedestrianInAlarmArea1 = false;
    /* 区域1是否有车 */
    bool hasVehicleInAlarmArea1 = false;
    /* 区域1是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea1 = false;
    /* 区域2是否有人 */
    bool hasPedestrianInAlarmArea2 = false;
    /* 区域2是否有车 */
    bool hasVehicleInAlarmArea2 = false;
    /* 区域2是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea2 = false;
    /* 区域3是否有人 */
    bool hasPedestrianInAlarmArea3 = false;
    /* 区域3是否有车 */
    bool hasVehicleInAlarmArea3 = false;
    /* 区域3是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea3 = false;
    /* 区域4是否有人 */
    bool hasPedestrianInAlarmArea4 = false;
    /* 区域4是否有车 */
    bool hasVehicleInAlarmArea4 = false;
    /* 区域4是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea4 = false;

    void reset(){
        installPosition = 0;
        hasPedestrianInAlarmArea1 = false;
        hasVehicleInAlarmArea1 = false;
        hasCameraCoverInAlarmArea1 = false;
        hasPedestrianInAlarmArea2 = false;
        hasVehicleInAlarmArea2 = false;
        hasCameraCoverInAlarmArea2 = false;
        hasPedestrianInAlarmArea3 = false;
        hasVehicleInAlarmArea3 = false;
        hasCameraCoverInAlarmArea3 = false;
        hasPedestrianInAlarmArea4 = false;
        hasVehicleInAlarmArea4 = false;
        hasCameraCoverInAlarmArea4 = false;
    };

};

/* R158识别信息 */
struct R158_BSDDetectionInfo {
    /* 区域1是否有人 */
    bool hasPedestrianInAlarmArea1 = false;
    /* 区域1是否有车 */
    bool hasVehicleInAlarmArea1 = false;
    /* 区域1是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea1 = false;
    /* 区域2是否有人 */
    bool hasPedestrianInAlarmArea2 = false;
    /* 区域2是否有车 */
    bool hasVehicleInAlarmArea2 = false;
    /* 区域2是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea2 = false;

    /* 区域3是否有人 */
    bool hasPedestrianInAlarmArea3 = false;
    /* 区域3是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea3 = false;

    void reset(){
        hasPedestrianInAlarmArea1 = false;
        hasVehicleInAlarmArea1 = false;
        hasCameraCoverInAlarmArea1 = false;
        hasPedestrianInAlarmArea2 = false;
        hasVehicleInAlarmArea2 = false;
        hasCameraCoverInAlarmArea2 = false;
        hasPedestrianInAlarmArea3 = false;
        hasCameraCoverInAlarmArea3 = false;
    };
};


/* 安保模式识别信息 */
struct SecurityMode_DetectionInfo {
    /* 区域1是否有人 */
    bool hasPedestrianInAlarmArea1 = false;
    /* 区域1是否有车 */
    bool hasVehicleInAlarmArea1 = false;
    /* 区域2是否有人 */
    bool hasPedestrianInAlarmArea2 = false;
    /* 区域2是否有车 */
    bool hasVehicleInAlarmArea2 = false;

    void reset(){
        hasPedestrianInAlarmArea1 = false;
        hasVehicleInAlarmArea1 = false;
        hasPedestrianInAlarmArea2 = false;
        hasVehicleInAlarmArea2 = false;
    };
};

/* R159识别信息 */
struct R159_BSDDetectionInfo {
    /* 区域1是否有人 */
    bool hasPedestrianInAlarmArea1 = false;
    /* 区域1是否有车 */
    bool hasVehicleInAlarmArea1 = false;
    /* 区域1是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea1 = false;
    /* 区域2是否有人 */
    bool hasPedestrianInAlarmArea2 = false;
    /* 区域2是否有车 */
    bool hasVehicleInAlarmArea2 = false;
    /* 区域2是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea2 = false;

    /* 区域3是否有人 */
    bool hasPedestrianInAlarmArea3 = false;
    /* 区域3是否有镜头遮挡 */
    bool hasCameraCoverInAlarmArea3 = false;

    void reset(){
        hasPedestrianInAlarmArea1 = false;
        hasVehicleInAlarmArea1 = false;
        hasCameraCoverInAlarmArea1 = false;
        hasPedestrianInAlarmArea2 = false;
        hasVehicleInAlarmArea2 = false;
        hasCameraCoverInAlarmArea2 = false;
        hasPedestrianInAlarmArea3 = false;
        hasCameraCoverInAlarmArea3 = false;
    };

};

/* ADAS算法的识别信息 */
struct ADASDetectionInfo_alarmStatus {
    /* 前车状态：0---安全；1---危险 */
    bool hasVehicleAlarm: 1;
    /* 行人状态：0---安全；1---危险 */
    bool hasPedestrian: 1;
    /* 左骑线：0---无；1---正在骑线 */
    bool hasLanelineAlarm_left: 1;
    /* 右骑线：0---无；1---正在骑线 */
    bool hasLanelineAlarm_right: 1;
    /* 左线类型：0----实线；1----虚线 */
    bool lanelineType_left: 1;
    /* 右线类型：0----实线；1----虚线 */
    bool lanelineType_right: 1;
    /* 预留 */
    int reserved: 2;
};

/* ADAS算法的识别信息 */
struct ADASDetectionInfo {
    /* 车距监控时间	uint8_t	单位 0.1s。0-80,0XFF表示没检测到车 */
    uint8_t vehicleCollisionTime = 0xFF;
    /* 车距监控距离	uint16_t	单位0.01m。0-30000,其它值表示没检测到车 */
    uint16_t vehicleYDistance = 0xFFFF;
    /* 左车道线距离	int16_t	单位mm。-5000~5000,其它值表示没检测到线 */
    int16_t lineXDistance_left = 0x7FFF;
    /* 右车道线距离	int16_t	单位mm。-5000~5000,其它值表示没检测到线 */
    int16_t lineXDistance_right = 0x7FFF;
    /* 行人碰撞时间	uint8_t	单位 0.1s。0-80,0XFF表示没检测到人 */
    uint8_t pedestrianCollisionTime = 0xFF;
    /* 行人距离	uint16_t	单位0.01m。0-5000,其它值表示没检测到人 */
    uint16_t pedestrianYDistance = 0xFFFF;
    /* 状态 */
    ADASDetectionInfo_alarmStatus alarmStatus = {false, false, false, false, false, false, 0};
    /* 速度上限 0-0xfe（254）；0xff为无效值 */
    uint8_t speedLimit_max = 0xFF;
    /* 速度下限0-0xfe（254）；0xff为无效值 */
    uint8_t speedLimit_min = 0xFF;

    /* 重置一下结构体的值 */
    void reset(){
        vehicleCollisionTime = 0xFF;
        vehicleYDistance = 0xFFFF;
        lineXDistance_left = 0x7FFF;
        lineXDistance_right = 0x7FFF;
        pedestrianCollisionTime = 0xFF;
        pedestrianYDistance = 0xFFFF;
        alarmStatus = {false, false, false, false, false, false, 0};
        speedLimit_max = 0xFF;
        speedLimit_min = 0xFF;
    }
};

/* DSM安全带算法的识别信息 */
struct G3DSMSeatbeltDetectionInfo {
    //安全带报警状态 类别3
    bool seatbelt_alarm = false;
    //安全带标记点，五个int型的坐标XY
    std::vector<cv::Point2i> belt_point;
};

/* DSM人脸算法的识别信息 */
struct G3DSMFaceDetectionInfo {
    //人脸是否是相同驾驶员
    int isSameDriver;

    //人脸得分
    float face_score = 0;

    //口罩报警状态
    bool respirator_alarm = false;

    //闭眼报警状态
    bool eye_alarm = false;

    //哈欠报警状态
    bool mouth_alarm = false;

    //左顾右盼报警状态
    bool lookaround_alarm = false;

    //离岗报警状态
    bool facemissing_alarm = false;

    //遮挡报警状态
    bool camcover_alarm = false;

    //抽烟报警状态
    bool smoking_alarm = false;

    //打电话报警状态
    bool phone_alarm = false;

    //疲劳程度
    int fatigue_rank = 0;

    //盲点检测报警状态 类别4
    bool Blindspot_alarm = false;

    //头盔报警状态
    bool hat_alarm;

    //安全带报警状态 类别
    bool seatbelt_alarm;

    //人脸坐标 左上和右下两个点
    std::vector<cv::Point2i> face_point;

    //人脸五官特征点，五个点XY
    std::vector<cv::Point2i> five_point;

    //人脸角度
    int face_angle = 0;

};

/* 条码类型 */
enum BARCODE_TYPE : uint8_t {
    /* 条码类型---QRCode格式的二维码 */
    BARCODE_TYPE_QRCODE = 0,
    /* 条码类型---Code128格式的一维码 */
    BARCODE_TYPE_CODE128,

};

/* 条码信息 */
struct BarCodeInfo{
    /* 条码类型 */
    BARCODE_TYPE barcodeType = BARCODE_TYPE_QRCODE;
    /* 该条码信息是否可用 */
    bool isCodeInfoAvailable = false;
    /* 关键信息 */
    std::string code_key_info;
    /* 完整信息 */
    std::string code_complete_info;
    /* 条码的框的位置 */
    int mTop = 0;
    int mBottom = 0;
    int mLeft = 0;
    int mRight = 0;
};



/* 算法的识别信息 */
struct G3DetectionResult {
    /* 识别算法类型 */
    CameraType curCameraType;
    /* 侧边行人识别信息 */
    BSDDetectionInfo bsdDetectionInfo;
    /* ADAS识别信息 */
    ADASDetectionInfo adasDetectionInfo;
    /* 车速 */
    float speed = -1.0;
    /* 以镜头划分的bypass信息 */
    BSDDetectionInfo_Bypass_Status_By_Camera bypassStatusByCamera = {};
    /* 对应的报警决策类型 */
    ALARM_DECISION_TYPE alarmDecisionType;
    /* 语义分割使用的maskbuf的宽 */
    uint16_t maskBufWidth = 160;
    /* 语义分割使用的maskbuf的高 */
    uint16_t maskBufHeight = 90;
    /* 语义分割使用的maskbuf的长度 */
    uint32_t maskBufLen = 0;
    /* 语义分割使用的maskbuf   默认是160 x 90分辨率的 */
    unsigned char maskBuf[160 * 90];
    /* 倒车信号 */
    bool isReverse = false;
    /* 镜头遮挡 */
    bool isCameraCover = false;
    /* 照明灯的类型  0：可见光照明灯  1：红外照明灯  2:照明和红外一起开 */
    int lampType = 0;
    /* 照明灯的控制状态 */
    bool isLampOpen = false;
    /* R151认证的识别信息 */
    R151_BSDDetectionInfo r151BsdDetectionInfo;
    /* R158认证的识别信息 */
    R158_BSDDetectionInfo r158BsdDetectionInfo;
    /* R158认证的识别信息 */
    R159_BSDDetectionInfo r159BsdDetectionInfo;
    /* 当前算法使用的正式识别阈值 */
    float classThreshold = 0;
    /* 镜头是否全黑 */
    bool cameraFullBlack = false;
    /* 人脸数组 */
    std::vector<G3DSMFaceDetectionInfo> faceList;
    /* 安全带 */
    std::vector<G3DSMSeatbeltDetectionInfo> seatbeltList;
    /* 这一帧识别所使用的时间。 单位：ms  0xFFFF表示无效*/
    uint16_t detectUseTime = 0xFFFF;
    /* 安保模式的识别信息 */
    SecurityMode_DetectionInfo securityModeDetectionInfo;
};




#endif //VIS_G3_SOFTWARE_G3DETECTIONDEFIND_H
