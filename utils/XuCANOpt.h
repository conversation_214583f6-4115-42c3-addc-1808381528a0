//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/30.
//

#ifndef VIS_G3_SOFTWARE_XUCANOPT_H
#define VIS_G3_SOFTWARE_XUCANOPT_H


#include <stdio.h>
#include <string>
#include <sys/ioctl.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <linux/socket.h>
#include <linux/can.h>
#include <linux/can/error.h>
#include <linux/can/raw.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <pthread.h>


class XuCANOpt {

public:
    /**
     * idmask 掩盖的位，1为掩盖，未掩盖的位置必须与idcode匹配
     * idcode 关心的位，如果对应位没有被掩盖，则id必须与idcode位对应，没有对应的id则会被过滤
     * 例子：
     * idmask   =   0'b 1111 0000
     * idcode   =   0'b 0000 1010
     * id       =   0'b xxxx 1010
     */
    struct can_idmask_s {
        uint32_t idmask;
        uint32_t idcode;
    };


    int initCANBus(int bitRate, const std::string ifName);

    /**
     * 读取一次CANBus的数据
     *
     * */
    int recvCANData(can_frame &frame, timeval tv);

    /**
     * 发送一包can数据
     * @param frame 要发送的参数据,如果发送失败，该参数会被改为出错的详细信息
     * @param timeout 发送阻塞最大的时间，如果套接字没有设置不阻塞的话，则该函数可能会一直阻塞
     * @return 参考int CanOpt::sendCanFrame(can_frame &frame, struct timeval &tv)
     */
    int sendCanFrame(can_frame &frame, const uint &timeout);

    /**
     * 发送一包can数据
     * @param frame 要发送的参数据,如果发送失败，该参数会被改为出错的详细信息
     * @param timeout 发送超时，但并不是一定有效，也有可能阻塞
     * @return 成功返回0或发送字节数，发送失败返回-1（一般返回-1就是总线挂掉了），发送未完成返回-2
     */
    int sendCanFrame(can_frame &frame, struct timeval &tv);

    /**
     * 重新发送can寄存器中的数据，这是针对rv1126的，其他的can驱动不支持该操作
     */
    void resend();

    /**
     * 检查can数据是否发送成功，会尝试重发
     * @param frame 需要检查的can数据
     * @param timeout 超时时间，默认一秒，但这个时间并不是非常准确
     * @return 成功或失败
     */
    bool isSendFinish(struct can_frame &frame, struct timeval &tv);

    /**
     * 打印一包can数据
     * @param frame can数据
     */
    void printCanFrame(const can_frame &frame);

    /**
     * @param frame 错误包
     * @return 是否是致命的错误
     */
    bool isFatalError(const can_frame &frame);

    /**
     * @param frame can数据包
     * @return 总线是否关闭了
     */
    bool isBusOff(const can_frame &frame);

    /**
     * @return 两个can数据包是否相等
     */
    bool equal(const can_frame &frameA, const can_frame &frameB);

    /**
     * 复制can数据包
     * @param to 目标
     * @param from 源
     */
    void copyFrame(struct can_frame &to, const struct can_frame &from);

    /**
     * 设置id掩码，底层的过滤
     * @param mask 掩码
     * @return 成功返回0，失败返回负数
     */
    int setIdMask(const can_idmask_s &mask);


    /**
     * 释放已经打开了的资源
     */
    void closeCAN();


private:
    int init(const std::string ifName);

    int canfd_revc = -1;
    int canfd_send = -1;

    struct sockaddr_can addr;
    std::string curIfName;
    // 最大的can id
    const uint32_t MAX_CAN_ID = 0x1fffffff;
    const uint16_t SIOC_GET_ERR_STAT = (SIOCDEVPRIVATE + 1);
    const uint16_t SIOC_DO_RESEND = (SIOCDEVPRIVATE + 2);
    const uint16_t SIOC_SET_IDMASK = (SIOCDEVPRIVATE + 3);


};


#endif //VIS_G3_SOFTWARE_XUCANOPT_H
