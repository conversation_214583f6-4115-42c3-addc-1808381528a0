//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/25.
//

#ifndef MRV440SOFTWARE_XUHTTPUTIL_H
#define MRV440SOFTWARE_XUHTTPUTIL_H
#include <iostream>
#include <curl/curl.h>
#include <vector>
#include <mutex>

class XuHttpUtil {
    struct XuHttpParam{
        std::string keyName;
        std::string value;
    };


public:
    XuHttpUtil();
    ~XuHttpUtil();

    /* 单例 */
    static XuHttpUtil &getInstance();


    /**
     * 向NDS注册一个账号
     *
     * @param uuid ： 设备的UUID
     * @param tag ：TAG
     * @param key ： key
     * @param strResponse ： HTTP的请求结果
     *
     * @return 0:成功   其他：失败
     */
    int registerToNDS(const std::string &uuid, const std::string &tag, const std::string &key, std::string &strResponse);
    /**
     *  向NDS登录账号
     *
     * @param userName : 用户名
     * @param password ： 密码
     * @param strResponse ： HTTP的请求结果
     *
     * @return 0:成功   其他：失败
     */
    int loginToNDS(const std::string &userName, const std::string &password, std::string &strResponse);
    /**
     * 想NDS请求最新的MQTT的证书
     *
     * @param uuid ： 设备的UUID
     * @param deviceType ： 类型
     * @param mqttCertificate ： 证书内容（目前不用填）
     * @param strResponse ： HTTP的请求结果
     * @return 0:成功   其他：失败
     */
    int getNDSMqttCertificate(const std::string &uuid, const std::string &deviceType, const std::string &mqttCertificate, std::string &strResponse);


    /**
     * 使用post的方式把报警视频或图片上传到NDS
     *
     * @param url ： 上传地址
     * @param path ： 文件路径
     * @param strResponse ： HTTP的请求结果
     * @return 0:成功   其他：失败
     */
    int postAttchmentFileToNDS(const std::string &url, const std::string &path, std::string &strResponse);

    /**
     * 使用post的方式把DVR文件上传到NDS
     *
     * @param url ： 上传地址
     * @param path ： 文件路径
     * @param strResponse ： HTTP的请求结果
     * @return 0:成功   其他：失败
     */
    int postDVRFileToNDS(const std::string &url, const std::string &path, std::string &strResponse);

    /**
     * 使用post的方式把DVR文件上传到NDS
     *
     * @param url ： 上传地址
     * @param path ： 文件路径
     * @param strResponse ： HTTP的请求结果
     * @return 0:成功   其他：失败
     */
    int downlaodFile(const std::string &url, const std::string &path);

    /**
     *
     * @param host
     * @param port
     */
    void setNDSHttpUrlBase(const std::string host,const int port);



private:



    /* 下载线程用的互斥锁 */
    std::mutex downloadLock;


    /*NDS的HTTPS的地址*/
    std::string ndsRootRul = "https://portal.safetyshieldvue.com:443";
//    /*NDS的HTTPS的地址 测试地址*/
//    const std::string ndsRootRul = "http://ssg-dev.nds.asia:10011";


    /**
     * https的post请求
     * @param url ： URL链接
     * @param paramList ： 参数列表
     * @param res ： 服务器返回结果的内容
     *
     * @return 0:成功   其他：失败
     */
    int post_https(const std::string &url,std::vector<XuHttpParam> paramList,std::string &strResponse);

    /**
     * http的post请求
     * @param url ： URL链接
     * @param paramList ： 参数列表
     * @param res ： 服务器返回结果的内容
     *
     * @return 0:成功   其他：失败
     */
    int post_http(const std::string &url,std::vector<XuHttpParam> paramList,std::string &strResponse);

    /**
     * https的get请求
     * @param url ： URL链接
     * @param res ： 服务器返回结果的内容
     *
     * @return 0:成功   其他：失败
     */
    int get_https(const std::string &url,std::string &strResponse);


};


#endif //MRV440SOFTWARE_XUHTTPUTIL_H
