#pragma once

#include <fstream>
#include <mutex>
#include <vector>

#include "LogChannel.h"

namespace vis
{
    /**
     * @brief 这个是日志类，一般不被直接使用，一般通过调用此头文件的gDebug()等接口使用
     * 可以手动初始化，也可以修改通过配置表初始化，如果不进行初始化，则默认的打印等级就是debug级
     */
    class Log
    {
    public:
        enum logLevel
        {
            lv_none = -1,   // 不输出打印
            lv_fatal,       // 致命错误信息，用于记录导致程序崩溃的事件
            lv_critical,    // 严重信息，用于记录严重错误事件
            lv_error,       // 错误信息，用于记录错误事件
            lv_warning,     // 警告信息，用于记录可能导致问题的事件
            lv_notice,      // 注意信息，用于记录需要注意的事件
            lv_information, // 一般信息，用于记录程序运行中的重要事件
            lv_debug,       // 调试信息，用于诊断程序中的问题
            lv_trace,       // 微型调试信息，用于跟踪代码执行路径
        };

        // 一些初始化相关的接口，优先读取mConfigFile指向的配置表进行初始化
        // 后续可以通过下面的接口修改，一旦使用下面的接口修改，则不会使用配置表进行初始化
        static void setLogLevel(const logLevel &lv);
        static void setLogFileAttrib(const std::string &path, const std::size_t &size,
                                     const std::size_t &cnt);

    private:
        explicit Log(const logLevel &lv);
        LogChannel getLogChannel();
        static void setLogFileCnt(const std::size_t &cnt);
        // 设置初始化标志
        static void setInitialized();
        static inline bool needOpen()
        {
            // 日志文件路径存在、日志文件最大的大小大于0、日志文件最大的个数大于0、日志文件未打开，就打开或创建日志文件
            return (mLogFileSize > 0U) && (mLogCnt > 0U)
                   && (!mLogPath.empty() && (!mLogFileStream.is_open()));
        }

        friend LogChannel getLogChn(const vis::Log::logLevel &lv);
        friend void logHex(const Log::logLevel &lv, const std::string &str, const void *const data,
                           const std::size_t &len);

        // 日志的配置文件
        static const std::string mConfigFile;
        // 创建日志目录
        static void mkLogDir(const std::string &path);
        // 打开日志文件
        static void openLogFile();
        // 关闭日志文件
        static void closeLogFile();
        // 设置没有日志文件输出，写或创建日志文件出错将会调用这个方法
        static void setNoLogFile();
        // 日志文件名最大的序号
        static const std::size_t mMaxNumber;
        // 标志是否已经经过了初始化，这个标志的置位应该调用setInitialized函数来置位，不应该使用其他的方式置位
        static bool mInitialized;
        // log的等级，默认是debug等级
        static logLevel mLogLevel;
        // 单个日志文件的大小
        static std::size_t mLogFileSize;
        // 日志文件的存放位置
        static std::string mLogPath;
        // 日志文件的个数
        static std::size_t mLogCnt;
        // 日志文件输出流
        static std::ofstream mLogFileStream;
        // 一个无效的文件输出流
        static std::ofstream mEmptyLogStream;
        // 因为日志类是所有线程都可以使用的，所以必须要加线程同步锁
        static std::mutex mLock;
        // 给空日志流用的锁，日常保持释放的状态
        static std::mutex mEmptyLock;
        // 确保日志文件在关闭期间日志信息不会丢失
        static std::mutex mLogFileLock;
        // 一个无效的日志文件锁
        static std::mutex mEmptyLogFileLock;
        // 构造函数锁，避免构造函数同时多次进入
        static std::mutex mConstructorLock;

        // 当前要打印的日志等级
        logLevel mCurLogLv = lv_none;

        // 负责日志的初始化
        void init();
        // 得到一个无效的日志流
        LogChannel none();
        void appendLogHead(LogChannel &channel);
        static std::string getProgramName();
        static void moveLogFile();
        static std::size_t logFileNumber(const std::string &filename);
        static std::vector<std::string> getLogFileList();
        static std::string makeLogFilename(const std::size_t &number);
        static std::string getLogFile(const std::string &filename);
        static std::string getLogFile(const std::size_t &number);
        static void rollLogFile();
    };

    LogChannel gFatal();
    LogChannel gCritical();
    LogChannel gError();
    LogChannel gWarning();
    LogChannel gNotice();
    LogChannel gInformation();
    LogChannel gDebug();
    LogChannel gTrace();

    void gFatalHex(const std::string &str, const void *const data, const std::size_t &len);
    void gCriticalHex(const std::string &str, const void *const data, const std::size_t &len);
    void gErrorHex(const std::string &str, const void *const data, const std::size_t &len);
    void gWarningHex(const std::string &str, const void *const data, const std::size_t &len);
    void gNoticeHex(const std::string &str, const void *const data, const std::size_t &len);
    void gInformationHex(const std::string &str, const void *const data, const std::size_t &len);
    void gDebugHex(const std::string &str, const void *const data, const std::size_t &len);
    void gTraceHex(const std::string &str, const void *const data, const std::size_t &len);
} // namespace vis