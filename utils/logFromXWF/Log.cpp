#include <iomanip>
#include <iostream>

#include <boost/filesystem.hpp>
#include <boost/format.hpp>
#include <syslog.h>
#include <yaml-cpp/yaml.h>

#include "Log.h"

namespace vis
{
    // 日志的配置文件
    const std::string Log::mConfigFile = {"./config/log.yml"};
    // 日志文件名最大的序号，这个数字不到最大的数字999，为的是给出缓冲空间
    const std::size_t Log::mMaxNumber = 900U;
    // 标志是否已经经过了初始化，这个标志的置位应该调用setInitialized函数来置位，不应该使用其他的方式置位
    bool Log::mInitialized = false;
    // log的等级，默认是debug等级
    Log::logLevel Log::mLogLevel = Log::lv_debug;
    // 单个日志文件的大小
    std::size_t Log::mLogFileSize = 0U;
    // 日志文件的存放位置
    std::string Log::mLogPath = {};
    // 日志文件的个数
    std::size_t Log::mLogCnt = 0U;
    // 日志文件输出流
    std::ofstream Log::mLogFileStream = {};
    std::ofstream Log::mEmptyLogStream = {};
    // 因为日志类是所有线程都可以使用的，所以必须要加线程同步锁
    std::mutex Log::mLock = {};
    // 给空日志流用的锁，日常保持释放的状态
    std::mutex Log::mEmptyLock = {};
    // 确保日志文件在关闭期间日志信息不会丢失
    std::mutex Log::mLogFileLock = {};
    // 一个无效的日志文件锁
    std::mutex Log::mEmptyLogFileLock = {};
    // 构造函数锁，避免构造函数同时多次进入
    std::mutex Log::mConstructorLock = {};

    /**
     * @brief 设置日志等级
     * @param lv 日志等级，默认为不打印
     */
    void Log::setLogLevel(const logLevel &lv)
    {
        mLogLevel = lv;
        setInitialized();
    }

    /**
     * @brief 设置日志文件的属性
     * @param path 日志文件的保存位置，必填项，不得为空，为空则不存日志文件
     * @param size 单个日志文件的大小(单位：KByte)，必须大于0，否则不存日志文件
     * @param cnt 日志文件最大留存个数，必须大于0，否则不存文件，当日志存储个数
     * 超过了最大个数的时候，则会删除最旧的日志文件，其最大值建议小于mMaxNumber,
     * 如果这个参数不小于mMaxNumber，则实际生效的值是mMaxNumber-1
     */
    void Log::setLogFileAttrib(const std::string &path, const size_t &size, const size_t &cnt)
    {
        mLogPath = path;
        mLogFileSize = size * 1024U * 1024U;
        setLogFileCnt(cnt);

        // 标志已经初始化过
        setInitialized();
    }

    /**
     * @brief 构造函数
     */
    Log::Log(const logLevel &lv)
    {
        std::lock_guard<std::mutex> lock(mConstructorLock);

        // 如果没有初始化，则进行初始化
        if (!mInitialized)
        {
            init();
        }

        mCurLogLv = lv;

        // 打开日志文件
        if (needOpen())
        {
            openLogFile();
        }

        // 如果文件的大小超过了最大的大小
        if ((mCurLogLv <= mLogLevel) && mLogFileStream.is_open()
            && (mLogFileStream.tellp() > mLogFileSize))
        {
            // 移动log文件
            moveLogFile();
            // 打开新的日志文件
            openLogFile();
        }
    }

    /**
     * @brief 给出一个无效的打印流，这个是在该等级时不不需要打印时返回的
     * @return 无效的打印流
     */
    LogChannel Log::none()
    {
        LogChannel channel(mEmptyLogStream, mEmptyLogStream, mEmptyLock, mEmptyLogFileLock);

        // 无效的日志流不需要锁
        mEmptyLock.unlock();
        mEmptyLogFileLock.unlock();

        // 因为这个流是不需要打印的，所以每次返回的都是一个已经关闭了的流
        mEmptyLogStream.close();

        return channel;
    }

    /**
     * @brief 打开日志文件
     */
    void Log::openLogFile()
    {
        // 需要打开的时候才打开，避免多余的打开操作
        if (needOpen())
        {
            try
            {
                std::lock_guard<std::mutex> lock(mLock);

                // 打开日志之前，先保证路径存在
                mkLogDir(mLogPath);

                // 如果日志文件没有打开，则打开日志文件
                if (!mLogFileStream.is_open())
                {
                    std::string file = getLogFile(0U);
                    mLogFileStream.open(file, std::ios::app);
                    mLogFileStream.setf(std::ios::unitbuf);
                    std::cout << "以追加模式打开文件" << file << std::endl;
                }
            }
            catch (...)
            {
                mLogFileStream.close();
                setNoLogFile();
            }
        }

        mLogFileLock.unlock();
    }

    /**
     * @brief 关闭日志文件
     */
    void Log::closeLogFile()
    {
        // 获得锁之后关闭文件，确保文件在打开之前，无法进行文件的写操作
        mLogFileLock.lock();

        // 刷新缓存，避免部分日志没有保存到日志
        if (mLogFileStream.is_open())
        {
            mLogFileStream.flush();
        }

        mLogFileStream.close();
    }

    /**
     * @brief 设置没有日志文件输出，写或创建日志文件出错将会调用这个方法
     */
    void Log::setNoLogFile()
    {
        // 设置为不需要输出日志文件
        mLogPath.clear();
        mLogFileSize = 0U;
        mLogCnt = 0U;

        closeLogFile();

        syslog(LOG_DEBUG, "设置不输出日志文件");
    }

    /**
     * @brief 往日志流中追加日志头
     * @param channel 日志流对象
     */
    void Log::appendLogHead(LogChannel &channel)
    {
        const std::chrono::system_clock::time_point today = std::chrono::system_clock::now();
        const std::time_t tt = std::chrono::system_clock::to_time_t(today);
        const std::tm tm = *std::localtime(&tt);
        std::stringstream ss;
        std::array<char, 100> thName{};
        char lvChar = {};

        // 追加事件
        ss << std::put_time(&tm, "[%Y-%m-%d %H:%M:%S]");
        // 追加日志等级标志
        switch (mCurLogLv)
        {
        case lv_fatal:
            lvChar = 'F';
            break;
        case lv_critical:
            lvChar = 'C';
            break;
        case lv_error:
            lvChar = 'E';
            break;
        case lv_warning:
            lvChar = 'W';
            break;
        case lv_notice:
            lvChar = 'N';
            break;
        case lv_information:
            lvChar = 'I';
            break;
        case lv_debug:
            lvChar = 'D';
            break;
        case lv_trace:
            lvChar = 'T';
            break;
        default:
            lvChar = '-';
            break;
        }
        ss << "[" << lvChar << "]";

        // 追加线程名
        pthread_getname_np(pthread_self(), thName.data(), thName.size() - 1);
        ss << "[" << thName.data() << "]";

        // 追加到日志流
        channel << ss.str();
    }

    /**
     * @brief 获得程序的名字
     * @return 程序名
     */
    std::string Log::getProgramName()
    {
        std::string name{};

        boost::filesystem::path program_path;
        program_path = boost::filesystem::read_symlink("/proc/self/exe");
        name = program_path.filename().string();

        if (name.empty())
        {
            name = "unknown";
        }

        return name;
    }

    /**
     * @brief 重命名文件，一般是在日志文件存储大小到了指定大小之后移动的
     */
    void Log::moveLogFile()
    {
        std::string newName;
        std::string curName;
        std::size_t number = 0U;
        std::vector<std::string> fileList = getLogFileList();
        std::vector<std::string>::iterator it;
        std::lock_guard<std::mutex> lock(mLock);

        // 先关闭日志文件
        closeLogFile();

        // 获得当前正在使用的文件名
        curName = makeLogFilename(0U);
        // 要删除没有序号的日志文件
        it = std::find(fileList.begin(), fileList.end(), curName);
        if (it != fileList.end())
        {
            fileList.erase(it);
        }

        // 如果找到列表非空的话
        if (!fileList.empty())
        {
            // 升序排序
            std::sort(fileList.begin(), fileList.end());

            // 如果日志文件的个数超过了mLogCnt个，则删除最旧的日志文件，知道日志文件的个数不超过mLogCnt
            while (fileList.size() >= mLogCnt)
            {
                std::vector<std::string>::iterator oldFile = fileList.begin();
                // 文件存在则删除文件
                if (boost::filesystem::exists(getLogFile(*oldFile)))
                {
                    // 删除最旧的日志文件
                    std::remove(getLogFile(*oldFile).c_str());
                    std::cout << "删除日志文件" << getLogFile(*oldFile) << std::endl;
                }
                else
                {
                    std::cout << getLogFile(*oldFile) << "文件不存在\n";
                }
                fileList.erase(oldFile);
            }

            // 得到日志文件名中最大的序号
            number = logFileNumber(fileList.back());

            // 如果最大的序号大于mMaxNumber，则轮转日志文件
            std::cout << "序号" << number << "过大，开始轮转日志文件\n";
            if (number > mMaxNumber)
            {
                rollLogFile();
                // 轮转日志文件之后文件名全都变了，需要更新日志文件列表
                fileList = getLogFileList();
            }
        }

        // 得到新文件名
        if (!fileList.empty())
        {
            // 得到最大的序号
            number = logFileNumber(fileList.back());
        }
        ++number;
        newName = getLogFile(number);

        try
        {
            // 重命名文件
            curName = getLogFile(0U);
            if (boost::filesystem::exists(curName))
            {
                boost::filesystem::rename(curName, newName);
                std::cout << "移动" << curName << "到" << newName << std::endl;
                // 如果最大的序号已经超过了指定值，则重新排序序号
                if (number > mMaxNumber)
                {
                    rollLogFile();
                }
            }
        }
        catch (...)
        {
            std::string str = "移动" + curName + "到" + newName + "失败\n";
            setNoLogFile();
            syslog(LOG_ERR, "%s", str.c_str());
            std::cerr << str;
        }
    }

    /**
     * @brief 解析文件名带的序号
     * @param filename 文件名
     * @return 序号
     */
    std::size_t Log::logFileNumber(const std::string &filename)
    {
        std::size_t number = 0U;
        std::string str;
        boost::filesystem::path name(filename);

        // 删除后缀后再得到后缀
        str = name.stem().extension().string();

        // 偏移跳过后缀的.符号
        if (!str.empty())
        {
            str = str.substr(1);
        }

        // 如果有内容，则转成整型
        if (!str.empty())
        {
            // 把字符串转成整型
            number = std::stoul(str);
        }
        else
        {
            number = 0;
        }

        return number;
    }

    /**
     * @brief 根据指定的序号生成日志文件的名字，由这个函数生成的名字，可以通过logFileNumber得到序号
     * @param number 序号，为0时则是构造没有序号的日志文件名。范围：[0,999]
     * @return 文件名
     */
    std::string Log::makeLogFilename(const std::size_t &number)
    {
        std::string programName = getProgramName();

        if ((number > 0U) && (number <= 999U))
        {
            boost::format f("%03d");
            std::string sn = (f % number).str();
            programName += "." + sn;
        }
        else if (0U == number)
        {
            // 为0的时候就是单纯的程序名
        }
        else
        {
            std::cerr << "number的值是" << number << "无法生成名字\n";
            programName.clear();
        }

        if (!programName.empty())
        {
            programName += ".log";
        }

        return programName;
    }

    /**
     * @brief 获得日志文件
     * @param filename 日志文件的名字
     * @return 全路径的日志文件，失败则返回空字符串
     */
    std::string Log::getLogFile(const std::string &filename)
    {
        std::string str;

        // 如果路径名不为空且文件名不为空
        if ((!mLogPath.empty()) && (!filename.empty()))
        {
            str = mLogPath + "/" + filename;
        }

        return str;
    }

    /**
     * @brief 根据序号获得日志文件
     * @param number 日志文件的序号
     * @return 全路径的日志文件，失败则返回空字符串
     */
    std::string Log::getLogFile(const std::size_t &number)
    {
        return getLogFile(makeLogFilename(number));
    }

    /**
     * @brief 翻滚所有的日志文件，使日志文件从1开始标序号
     */
    void Log::rollLogFile()
    {
        std::vector<std::string> fileList = getLogFileList();

        std::cout << "轮转日志文件\n";

        // 遍历删除列表中没有序号的日志文件
        for (std::vector<std::string>::iterator it = fileList.begin(); it != fileList.end(); ++it)
        {
            if (logFileNumber(*it) == 0U)
            {
                std::cout << "排除文件" << *it << std::endl;
                fileList.erase(it);
                break;
            }
        }

        if (!fileList.empty())
        {
            std::size_t number = 1U;

            // 升序排序
            std::sort(fileList.begin(), fileList.end());

            try
            {
                // 遍历所有带序号的文件，并重命名为序号从1开始的文件名
                for (const std::string &str : fileList)
                {
                    std::string name = getLogFile(number);
                    if (boost::filesystem::exists(getLogFile(str)))
                    {
                        std::cout << "移动" << getLogFile(str) << "到" << name << std::endl;
                        std::rename(getLogFile(str).c_str(), name.c_str());
                        ++number;
                    }
                    else
                    {
                        std::cout << getLogFile(str) << "不存在\n";
                    }
                }
            }
            catch (...)
            {
                syslog(LOG_ERR, "轮转日志文件失败\n");
                std::cerr << "轮转日志文件失败\n";
            }
        }
    }

    /**
     * @brief 获得已存在的日志文件中的最大的序号
     * @return 最大的序号
     */
    std::vector<std::string> Log::getLogFileList()
    {
        std::vector<std::string> fileList;

        // 如果mLogPath是一个目录
        if ((!mLogPath.empty()) && boost::filesystem::is_directory(mLogPath))
        {
            boost::filesystem::directory_iterator dirIter{mLogPath};

            // 遍历整个目录
            for (const boost::filesystem::directory_entry &f : dirIter)
            {
                const std::string programName = getProgramName();
                std::string::size_type index = {};
                std::string name = f.path().filename().string();

                // 查找程序名对应的日志文件
                index = name.find(programName);
                if (index != std::string::npos)
                {
                    // 找到对应的文件之后放到list中
                    fileList.push_back(name);
                }
            }
        }

        return fileList;
    }

    /**
     * @brief 获得一个日志流，如果当前的日志等级比配置的日志等级小，则返回一个无效的日志流
     * @return 日志流
     */
    LogChannel Log::getLogChannel()
    {
        LogChannel non = none();
        LogChannel *channel = &non;
        LogChannel errChn{mLogFileStream, std::cerr, mLock, mLogFileLock};
        LogChannel normalChn{mLogFileStream, std::cout, mLock, mLogFileLock};

        // 如果当前的等级不小于配置的等级，则给出指定的日志流
        if (mCurLogLv <= mLogLevel)
        {
            if (mCurLogLv <= lv_warning)
            {
                channel = &errChn;
            }
            else
            {
                channel = &normalChn;
            }
            appendLogHead(*channel);
        }

        return *channel;
    }

    /**
     * @brief 设置日志文件的个数
     * @param cnt 日志文件最大留存个数，必须大于0，否则不存日志文件，当日志存储个数
     * 超过了最大个数的时候，则会删除最旧的日志文件，其最大值建议小于mMaxNumber,
     * 如果这个参数不小于mMaxNumber，则实际生效的值是mMaxNumber-1
     */
    void Log::setLogFileCnt(const std::size_t &cnt)
    {
        if (cnt < mMaxNumber)
        {
            mLogCnt = cnt;
        }
        else
        {
            mLogCnt = mMaxNumber - 1;
        }
    }

    /**
     * @brief 置为已初始化标志
     */
    void Log::setInitialized()
    {
        mInitialized = true;
    }

    /**
     * @brief 初始化日志类
     */
    void Log::init()
    {
        try
        {
            // 日志文件存在且能打开才进行初始化
            if ((!mConfigFile.empty()) && boost::filesystem::exists(mConfigFile))
            {
                std::ifstream ifs(mConfigFile);

                if (ifs.is_open())
                {
                    YAML::Node root = YAML::Load(ifs);
                    YAML::Node fileNode = root["file"];

                    // 获得日志打印等级
                    int lv = root["level"].as<int>();
                    if ((lv >= lv_none) && (lv <= lv_trace))
                    {
                        mLogLevel = static_cast<logLevel>(lv);
                    }
                    else
                    {
                        mLogLevel = lv_none;
                    }

                    // 获得日志文件节点的属性
                    if (fileNode.IsDefined())
                    {
                        size_t size = fileNode["fileSize"].as<size_t>();
                        size_t cnt = fileNode["count"].as<size_t>();
                        std::string path = fileNode["path"].as<std::string>();

                        setLogFileAttrib(path, size, cnt);
                    }
                }
            }
        }
        catch (...)
        {
            setInitialized();
            syslog(LOG_ERR, "日志初始化出错\n");
            std::cerr << "日志初始化出错\n";
        }
    }

    /**
     * @brief 创建日志的目录
     * @param path 日志所在的路径
     */
    void Log::mkLogDir(const std::string &path)
    {
        boost::filesystem::path dir(path);

        // 如果目录不存在，则创建目录
        if ((!path.empty()) && (!boost::filesystem::exists(dir)))
        {
            // 创建目录
            if (!boost::filesystem::create_directories(dir))
            {
                // 创建失败则抛出异常
                throw std::invalid_argument("创建目录" + path + "失败");
            }
            else
            {
                std::cout << "创建日志目录: " << path << std::endl;
            }
        }
        else
        {
            // 检查dir是否是一个路径
            if (!boost::filesystem::is_directory(dir))
            {
                throw std::invalid_argument(path + "已存在，且不是一个目录，无法创建" + path
                                            + "目录");
            }
        }
    }

    /**
     * @brief 获得指定等级的日志流对象
     * @param lv 日志等级
     * @return 对应的日志流对象
     */
    vis::LogChannel getLogChn(const vis::Log::logLevel &lv)
    {
        Log l(lv);

        return l.getLogChannel();
    }

    /**
     * @brief 通过日志流打印十六进制数据
     * @param lv 日志等级
     * @param str 开始的时候打的字符串
     * @param data 十六进制的指针
     * @param len data的长度
     */
    void logHex(const Log::logLevel &lv, const std::string &str, const void *const data,
                const size_t &len)
    {
        LogChannel chn = getLogChn(lv);

        // 输出流如果无效则没必要浪费性能进行输出
        if (chn.isValid())
        {
            const uint8_t *pData = static_cast<const uint8_t *>(data);
            std::stringstream ss;

            ss << str << ": ";

            // 格式化每一个字节为
            for (size_t i = 0U; i < len; i++)
            {
                ss << (boost::format("%02x") % static_cast<unsigned int>(*pData)) << " ";
                ++pData;
            }

            chn << ss.str() << std::endl;
        }
    }

    /**
     * @brief 返回对应的日志输出流对象
     * @return 日志输出流
     */
    LogChannel gFatal()
    {
        return getLogChn(Log::lv_fatal);
    }

    /**
     * @brief 返回对应的日志输出流对象
     * @return 日志输出流
     */
    LogChannel gCritical()
    {
        return getLogChn(Log::lv_critical);
    }

    /**
     * @brief 返回对应的日志输出流对象
     * @return 日志输出流
     */
    LogChannel gError()
    {
        return getLogChn(Log::lv_error);
    }

    /**
     * @brief 返回对应的日志输出流对象
     * @return 日志输出流
     */
    LogChannel gWarning()
    {
        return getLogChn(Log::lv_warning);
    }

    /**
     * @brief 返回对应的日志输出流对象
     * @return 日志输出流
     */
    LogChannel gNotice()
    {
        return getLogChn(Log::lv_notice);
    }

    /**
     * @brief 返回对应的日志输出流对象
     * @return 日志输出流
     */
    LogChannel gInformation()
    {
        return getLogChn(Log::lv_information);
    }

    /**
     * @brief 返回对应的日志输出流对象
     * @return 日志输出流
     */
    LogChannel gDebug()
    {
        return getLogChn(Log::lv_debug);
    }

    /**
     * @brief 返回对应的日志输出流对象
     * @return 日志输出流
     */
    LogChannel gTrace()
    {
        return getLogChn(Log::lv_trace);
    }

    /**
     * @brief 通过特定日志等级的日志流打印十六进制数据
     * @param str 开始的时候打的字符串
     * @param data 十六进制的指针
     * @param len data的长度
     */
    void gFatalHex(const std::string &str, const void *const data, const std::size_t &len)
    {
        logHex(Log::lv_fatal, str, data, len);
    }

    /**
     * @brief 通过特定日志等级的日志流打印十六进制数据
     * @param str 开始的时候打的字符串
     * @param data 十六进制的指针
     * @param len data的长度
     */
    void gCriticalHex(const std::string &str, const void *const data, const std::size_t &len)
    {
        logHex(Log::lv_critical, str, data, len);
    }

    /**
     * @brief 通过特定日志等级的日志流打印十六进制数据
     * @param str 开始的时候打的字符串
     * @param data 十六进制的指针
     * @param len data的长度
     */
    void gErrorHex(const std::string &str, const void *const data, const std::size_t &len)
    {
        logHex(Log::lv_error, str, data, len);
    }

    /**
     * @brief 通过特定日志等级的日志流打印十六进制数据
     * @param str 开始的时候打的字符串
     * @param data 十六进制的指针
     * @param len data的长度
     */
    void gWarningHex(const std::string &str, const void *const data, const std::size_t &len)
    {
        logHex(Log::lv_warning, str, data, len);
    }

    /**
     * @brief 通过特定日志等级的日志流打印十六进制数据
     * @param str 开始的时候打的字符串
     * @param data 十六进制的指针
     * @param len data的长度
     */
    void gNoticeHex(const std::string &str, const void *const data, const std::size_t &len)
    {
        logHex(Log::lv_notice, str, data, len);
    }

    /**
     * @brief 通过特定日志等级的日志流打印十六进制数据
     * @param str 开始的时候打的字符串
     * @param data 十六进制的指针
     * @param len data的长度
     */
    void gInformationHex(const std::string &str, const void *const data, const std::size_t &len)
    {
        logHex(Log::lv_information, str, data, len);
    }

    /**
     * @brief 通过特定日志等级的日志流打印十六进制数据
     * @param str 开始的时候打的字符串
     * @param data 十六进制的指针
     * @param len data的长度
     */
    void gDebugHex(const std::string &str, const void *const data, const std::size_t &len)
    {
        logHex(Log::lv_debug, str, data, len);
    }

    /**
     * @brief 通过特定日志等级的日志流打印十六进制数据
     * @param str 开始的时候打的字符串
     * @param data 十六进制的指针
     * @param len data的长度
     */
    void gTraceHex(const std::string &str, const void *const data, const std::size_t &len)
    {
        logHex(Log::lv_trace, str, data, len);
    }
} // namespace vis