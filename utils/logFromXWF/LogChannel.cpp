#include "LogChannel.h"

namespace vis
{
    /**
     * @brief 构造函数
     * @param ofs 一个文件输出流对象
     * @param os 一个输出到终端的对象
     */
    LogChannel::LogChannel(std::ofstream &ofs, const std::ostream &os, std::mutex &lock,
                           std::mutex &writeLock)
        : std::ostream(os.rdbuf()), mOfs(ofs), mLock(lock), mWriteLock(writeLock)
    {
    }

    /**
     * @brief 初始化构造函数
     * @param chn 另一个对象
     */
    LogChannel::LogChannel(const LogChannel &chn)
        : std::ostream(chn.rdbuf()), mOfs(chn.mOfs), mLock(chn.mLock), mWriteLock(chn.mWriteLock)
    {
    }

    /**
     * @brief 判断当前的输出流是否有效
     * @return 有效返回true，无效返回false
     */
    bool LogChannel::isValid() const
    {
        return (this->good() || mOfs.is_open());
    }

    /**
     * @brief 这个函数是为了兼容std::endl的输入
     * @param fun 这个就是std::endl本身
     */
    LogChannel LogChannel::put(std::ostream &(*fun)(std::ostream &))
    {
        std::lock_guard<std::mutex> lock(mLock);
        std::lock_guard<std::mutex> writeLock(mWriteLock);

        if (this->good())
        {
            (void)fun(*this);
        }

        if (mOfs.is_open())
        {
            (void)fun(mOfs);
        }

        return *this;
    }

    /**
     * @brief 这是为了兼容std::endl
     * @param os 日志流
     * @param fun std::endl本身
     * @return 日志流
     */
    LogChannel operator<<(LogChannel &&os, std::ostream &(*fun)(std::ostream &))
    {
        return os.put(fun);
    }

    /**
     * @brief 重载<<操作，这个为了兼容std::endl
     * @param os 日志流
     * @param fun std::endl本身
     * @return 日志流
    */
    LogChannel operator<<(LogChannel &os, std::ostream &(*fun)(std::ostream &))
    {
        return os.put(fun);
    }
} // namespace vis