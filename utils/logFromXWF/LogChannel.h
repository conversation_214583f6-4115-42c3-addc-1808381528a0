#pragma once

#include <fstream>
#include <mutex>
#include <ostream>

namespace vis
{
    class LogChannel : public std::ostream
    {
    public:
        explicit LogChannel(std::ofstream &ofs, const std::ostream &os, std::mutex &lock,
                            std::mutex &writeLock);
        LogChannel(const LogChannel &chn);
        bool isValid() const;

    private:
        template <typename T>
        friend LogChannel operator<<(LogChannel &&os, const T &value);
        template <typename T>
        friend LogChannel operator<<(LogChannel &os, const T &value);
        friend LogChannel operator<<(LogChannel &&os, std::ostream &(*fun)(std::ostream &));
        friend LogChannel operator<<(LogChannel &os, std::ostream &(*fun)(std::ostream &));

        // 这两个put函数是为了处理锁的逻辑而存在的，为的是统一重载<<的行为
        LogChannel put(std::ostream &(*fun)(std::ostream &));
        template <typename T>
        LogChannel put(const T &val)
        {
            std::lock_guard<std::mutex> lock(mLock);
            std::lock_guard<std::mutex> writeLock(mWriteLock);

            if (this->good())
            {
                static_cast<std::ostream &>(*this) << val;
            }

            if (mOfs.is_open())
            {
                mOfs << val;
            }

            return *this;
        }

        std::ofstream &mOfs;
        std::mutex &mLock;
        std::mutex &mWriteLock;
    };

    /**
     * @brief 重载<<操作
     * @tparam T 输出给流的对象的类型
     * @param os 日志流
     * @param value 输出给流的对象
     * @return 日志流
     */
    template <typename T>
    LogChannel operator<<(LogChannel &&os, const T &value)
    {
        return os.put(value);
    }

    /**
     * @brief 重载<<操作
     * @tparam T 输出给流的对象的类型
     * @param os 日志流
     * @param value 输出给流的对象
     * @return 日志流
     */
    template <typename T>
    LogChannel operator<<(LogChannel &os, const T &value)
    {
        return os.put(value);
    }

} // namespace vis