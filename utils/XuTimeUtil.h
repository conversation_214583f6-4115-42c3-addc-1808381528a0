//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/1.
//

#ifndef VIS_ADOS_I7_XUTIMEUTIL_H
#define VIS_ADOS_I7_XUTIMEUTIL_H


#include <cstdint>
#include <string>


class XuTimeUtil {
public:
    XuTimeUtil();

    ~XuTimeUtil();

    /* 单例 */
    static XuTimeUtil &getInstance();

    /**
     * 获取毫秒级别的时间戳
     * **/
    uint64_t currentTimeMillis();

    /**
     * 获取秒级别的时间戳
     * **/
    __time_t currentTimeSecond();

    /**
     * 获取微秒级别的时间戳
     * **/
    __suseconds_t currentTimeMicro();

    /**
     * 设置GPS时间去到系统时间
     *
     * @param year ： 年
     * @param mon ： 月
     * @param day ： 日
     * @param hour ： 时
     * @param min ： 分
     * @param sec ： 秒
     * @param localTimeZone ： 系统本地的时区
     *
     * */
    bool setGPSTimeToSystemTime(int year, int mon, int day, int hour, int min, int sec, std::string localTimeZone);

    /**
     * 设置时间去到系统时间
     *
     * @param year ： 年
     * @param mon ： 月
     * @param day ： 日
     * @param hour ： 时
     * @param min ： 分
     * @param sec ： 秒
     * @param timeZone ： 设置过来的时间所在的时区
     * @param localTimeZone ： 系统本地的时区
     *
     * */
    bool setTimeToSystemTime(int year, int mon, int day, int hour, int min, int sec, std::string timeZone,
                             std::string localTimeZone);

    /**
     * 获取BCD格式的UTC时间
     * @param bcdTime：存放时间的内存
     * @return 是否成功
     */
    bool getUTCTime_BCD(uint8_t bcdTime[8]);

    /**
     *根据BCD格式的时间和时区，计算在那个时区的那个时间下，UTC时间是多少，并返回秒级的时间戳
     *
     * @param bcdTime ： BCD格式的时间
     * @param timezone ： 时区
     * @return 秒级时间戳
     */
    time_t bcdToTimestamp_Sec(uint8_t *bcdTime,const std::string timezone);

    /**
     *根据秒级别的时间戳和时区，计算在那个时区的那个时间下，UTC时间是多少，并返回秒级的时间戳（需要计算夏令时和冬令时）
     *
     * @param timestamp ： 秒级别的时间戳
     * @param timezone ： 时区
     * @return 秒级时间戳
     */
    time_t getUTCTimestamp_Sec(const uint32_t timestamp,const std::string timezone);

    /**
     * 获取从系统启动到现在位置的时间(毫秒级别)
     *
     * @return 系统从启动到现在为止过了多少毫秒
     */
    uint64_t getTimeFromSysStartUp_Millis();

    /**
     * 获取从系统启动到现在位置的时间(秒级别)
     *
     * @return 系统从启动到现在为止过了多少秒
     */
    uint64_t getTimeFromSysStartUp_Sec();




};


#endif //VIS_ADOS_I7_XUTIMEUTIL_H
