/*******************************************************************************
 * Copyright (c) 2014, 2017 IBM Corp.
 *
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * and Eclipse Distribution License v1.0 which accompany this distribution.
 *
 * The Eclipse Public License is available at
 *    http://www.eclipse.org/legal/epl-v10.html
 * and the Eclipse Distribution License is available at
 *   http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * Contributors:
 *    <PERSON> - initial API and implementation and/or initial documentation
 *    <PERSON> - return codes from linux_read
 *******************************************************************************/

#include "MQTTLinux.h"

void TimerInit(Timer* timer)
{
	timer->end_time = (struct timeval){0, 0};
}

char <PERSON>r<PERSON>sExpired(Timer* timer)
{
	struct timeval now, res;
	gettimeofday(&now, NULL);
	timersub(&timer->end_time, &now, &res);
	return res.tv_sec < 0 || (res.tv_sec == 0 && res.tv_usec <= 0);
}


void TimerCountdownMS(Timer* timer, unsigned int timeout)
{
	struct timeval now;
	gettimeofday(&now, NULL);
	struct timeval interval = {timeout / 1000, (timeout % 1000) * 1000};
	timeradd(&now, &interval, &timer->end_time);
}


void TimerCountdown(Timer* timer, unsigned int timeout)
{
	struct timeval now;
	gettimeofday(&now, NULL);
	struct timeval interval = {timeout, 0};
	timeradd(&now, &interval, &timer->end_time);
}


int TimerLeftMS(Timer* timer)
{
	struct timeval now, res;
	gettimeofday(&now, NULL);
	timersub(&timer->end_time, &now, &res);
	//printf("left %d ms\n", (res.tv_sec < 0) ? 0 : res.tv_sec * 1000 + res.tv_usec / 1000);
	return (res.tv_sec < 0) ? 0 : res.tv_sec * 1000 + res.tv_usec / 1000;
}

/**
 * 从MQTT读数据
 *
 * @param n : Network对象的指针
 * @param buffer ： 存放数据的指针
 * @param len ： 指针长度
 * @param timeout_ms : 超时时间
 * @return 读出的长度
 */
int linux_read(Network* n, unsigned char* buffer, int len, int timeout_ms)
{
	struct timeval interval = {timeout_ms / 1000, (timeout_ms % 1000) * 1000};
	if (interval.tv_sec < 0 || (interval.tv_sec == 0 && interval.tv_usec <= 0))
	{
		interval.tv_sec = 0;
		interval.tv_usec = 100;
	}

	setsockopt(n->my_socket, SOL_SOCKET, SO_RCVTIMEO, (char *)&interval, sizeof(struct timeval));
    setsockopt(n->ssl, SOL_SOCKET, SO_RCVTIMEO, (char *)&interval, sizeof(struct timeval));
	int bytes = 0;
	while (bytes < len)
	{
        int rc = -1;
        /* 是不是使用SSL决定了从哪个描述符读数据 */
        if(n->useSSL==1){
            rc = SSL_read(n->ssl, buffer+bytes, (len - bytes));
        }else{
            rc = read(n->my_socket, buffer+bytes, (len - bytes));
        }

		if (rc == -1)
		{
			if (errno != EAGAIN && errno != EWOULDBLOCK)
			  bytes = -1;
			break;
		}
		else if (rc == 0)
		{
			bytes = 0;
			break;
		}
		else
			bytes += rc;
	}
	return bytes;
}

/**
 * 写数据到mqtt
 *
 * @param n : Network对象的指针
 * @param buffer ： 存放数据的指针
 * @param len ： 数据长度
 * @param timeout_ms : 超时时间
 * @return 写出的长度
 */
int linux_write(Network* n, unsigned char* buffer, int len, int timeout_ms)
{
	struct timeval tv;

	tv.tv_sec = 0;  /* 30 Secs Timeout */
	tv.tv_usec = timeout_ms * 1000;  // Not init'ing this can cause strange errors

	setsockopt(n->my_socket, SOL_SOCKET, SO_SNDTIMEO, (char *)&tv,sizeof(struct timeval));
    setsockopt(n->ssl, SOL_SOCKET, SO_SNDTIMEO, (char *)&tv,sizeof(struct timeval));
	int	rc = -1;
    /* 是不是使用SSL决定了从哪个描述符写数据 */
    if(n->useSSL == 1){
        rc = SSL_write(n->ssl, buffer, len);
        }else{
        rc = write(n->my_socket, buffer, len);
    }

	return rc;
}

/**
 * 初始化Network对象
 *
 * @param n : Network对象的指针
 */
void NetworkInit(Network* n)
{
	n->my_socket = 0;
	n->mqttread = linux_read;
	n->mqttwrite = linux_write;
    n->useSSL = 0;
}

/**
 * 原版的不带SSL的连接函数（这里改动了一点就是给useSSL赋值了）
 *
 * @param n : Network对象的指针
 * @param addr ： MQTT服务器的IP地址
 * @param port ： MQTT服务器的IP地址
 * @return 0：成功  其他：失败
 */
int NetworkConnect(Network* n, char* addr, int port)
{
	int type = SOCK_STREAM;
	struct sockaddr_in address;
	int rc = -1;
	sa_family_t family = AF_INET;
	struct addrinfo *result = NULL;
	struct addrinfo hints = {0, AF_UNSPEC, SOCK_STREAM, IPPROTO_TCP, 0, NULL, NULL, NULL};

	if ((rc = getaddrinfo(addr, NULL, &hints, &result)) == 0)
	{
		struct addrinfo* res = result;

		/* prefer ip4 addresses */
		while (res)
		{
			if (res->ai_family == AF_INET)
			{
				result = res;
				break;
			}
			res = res->ai_next;
		}

		if (result->ai_family == AF_INET)
		{
			address.sin_port = htons(port);
			address.sin_family = family = AF_INET;
			address.sin_addr = ((struct sockaddr_in*)(result->ai_addr))->sin_addr;
		}
		else
			rc = -1;

		freeaddrinfo(result);
	}

	if (rc == 0)
	{
		n->my_socket = socket(family, type, 0);
        n->useSSL = 0;
		if (n->my_socket != -1){
            rc = connect(n->my_socket, (struct sockaddr*)&address, sizeof(address));
        }

	}

	return rc;
}

/**
 * 打印证书内容
 *
 * @param ssl ： SSL的描述符
 */
void ShowCerts(SSL * ssl)
{
    X509 *cert;
    char *line;
    cert = SSL_get_peer_certificate(ssl);
    if (cert != NULL) {
        printf("数字证书信息:\n");
        line = X509_NAME_oneline(X509_get_subject_name(cert), 0, 0);
        printf("证书: %s\n", line);
        free(line);
        line = X509_NAME_oneline(X509_get_issuer_name(cert), 0, 0);
        printf("颁发者: %s\n", line);
        free(line);
        // 获取证书有效期起止时间（ASN1_TIME 格式）
        const ASN1_TIME *not_before = X509_get0_notBefore(cert);
        printf("生效时间: %s\n", not_before->data);
        const ASN1_TIME *not_after = X509_get0_notAfter(cert);
        printf("过期时间: %s\n", not_after->data);

        X509_free(cert);
    } else {
        printf("无证书信息！\n");
    }





}

/**
 * 连接Network（带SSL的socket）
 *
 * @param n : Network对象的指针
 * @param addr ： MQTT服务器的IP地址
 * @param port ： MQTT服务器的IP地址
 * @param crtFilePath ： 证书路径
 * @return 0：成功  其他：失败
 */
int NetworkConnectBySSL(Network* n, const char* addr, const char* port, const char* crtFilePath)
{

    SSL_CTX* ssl_context;
    SSL *ssl;


    /*SSL初始化*/
    SSL_library_init();
    OpenSSL_add_all_algorithms();
    SSL_load_error_strings();

    /* 创建SSL的上下文 */
    ssl_context = SSL_CTX_new(TLS_client_method());
    /* 设置不校验证书，因为服务器现在的证书都是过期的，且MRV330又改回了不校验证书，所以就这样吧 */
    SSL_CTX_set_verify(ssl_context, SSL_VERIFY_NONE, NULL);

    /* 设置用来进行校验的证书 */
    if (SSL_CTX_use_certificate_file(ssl_context, crtFilePath, SSL_FILETYPE_PEM) != 1) {
        SSL_CTX_free(ssl_context);
        printf("Failed to load client certificate from %s \n", crtFilePath);
    }
    SSL_CTX_set_default_verify_paths(ssl_context);
    /* 创建Socket并连接到服务器 */
    int socketFd = -1;
    if ((socketFd = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        printf("create socket failed! %s \n", strerror(errno));
        return -1;
    }
    /* 解析链接地址和消息 */
    struct addrinfo hints = {};
    struct addrinfo* serverInfo;
    hints.ai_family = AF_INET;
    hints.ai_socktype = SOCK_STREAM;
    int result = getaddrinfo(addr, port, &hints, &serverInfo);
    if (result != 0) {
        printf("Failed to get addr info= %s    addr=%s  port=%s \n", gai_strerror(result),addr,port);
        return -1;
    }
    /* 连接服务端socket */
    if (connect(socketFd, serverInfo->ai_addr, serverInfo->ai_addrlen) != 0) {
        printf("Connect socket failed! %s \n", strerror(errno));
        return -1;
    }
    /* 让socket走SSL */
    ssl = SSL_new(ssl_context);
    SSL_set_fd(ssl, socketFd);
    if (SSL_connect(ssl) == -1) {
        ERR_print_errors_fp(stderr);
        return -1;
    } else {
        printf("Connected with %s encryption \n", SSL_get_cipher(ssl));
        /* 打印一下证书的内容 */
        ShowCerts(ssl);
        /* 检查一下证书 */
        /* 把对应的描述符什么的存起来 */
        n->my_socket = socketFd;
        n->ssl = ssl;
        n->useSSL = 1;
    }
    return 0;
}

/**
 * 连接Network（不带SSL的socket）
 *
 * @param n : Network对象的指针
 * @param addr ： MQTT服务器的IP地址
 * @param port ： MQTT服务器的IP地址
 * @return 0：成功  其他：失败
 */
int NetworkConnectNotSSL(Network* n, const char* addr, const char* port)
{
    n->useSSL = 0;
    /* 创建Socket并连接到服务器 */
    int socketFd = -1;
    if ((socketFd = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        printf("create socket failed! %s \n", strerror(errno));
        return -1;
    }
    /* 解析链接地址和消息 */
    struct addrinfo hints = {};
    struct addrinfo* serverInfo;
    hints.ai_family = AF_INET;
    hints.ai_socktype = SOCK_STREAM;
    int result = getaddrinfo(addr, port, &hints, &serverInfo);
    if (result != 0) {
        printf("Failed to get addr info= %s    addr=%s  port=%s \n", gai_strerror(result),addr,port);
        return -1;
    }
    /* 连接服务端socket */
    if (connect(socketFd, serverInfo->ai_addr, serverInfo->ai_addrlen) != 0) {
        printf("Connect socket failed! %s \n", strerror(errno));
        return -1;
    }else{
        n->my_socket = socketFd;
        return 0;
    }
}

/**
 * 断开连接
 *
 * @param n : Network对象的指针
 */
void NetworkDisconnect(Network* n)
{
	close(n->my_socket);
    if(n->useSSL == 1){
        SSL_shutdown(n->ssl);
      }

}
