/*******************************************************************************
 * Copyright (c) 2014 IBM Corp.
 *
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * and Eclipse Distribution License v1.0 which accompany this distribution.
 *
 * The Eclipse Public License is available at
 *    http://www.eclipse.org/legal/epl-v10.html
 * and the Eclipse Distribution License is available at
 *   http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * Contributors:
 *    <PERSON> - initial API and implementation and/or initial documentation
 *******************************************************************************/

#if !defined(__MQTT_LINUX_)
#define __MQTT_LINUX_

#if defined(WIN32_DLL) || defined(WIN64_DLL)
  #define DLLImport __declspec(dllimport)
  #define DLLExport __declspec(dllexport)
#elif defined(LINUX_SO)
  #define DLLImport extern
  #define DLLExport  __attribute__ ((visibility ("default")))
#else
  #define DLLImport
  #define DLLExport
#endif

#include <sys/types.h>
#include <sys/socket.h>
#include <sys/param.h>
#include <sys/time.h>
#include <sys/select.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <stdio.h>
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>

#include <stdlib.h>
#include <string.h>
#include <signal.h>

#include <openssl/ssl.h>
#include <openssl/err.h>
#include <openssl/evp.h>

typedef struct Timer
{
	struct timeval end_time;
} Timer;

void TimerInit(Timer*);
char TimerIsExpired(Timer*);
void TimerCountdownMS(Timer*, unsigned int);
void TimerCountdown(Timer*, unsigned int);
int TimerLeftMS(Timer*);

typedef struct Network
{
    /* socket的描述符 */
	int my_socket;
    /* 读取MQTT消息的函数 */
	int (*mqttread) (struct Network*, unsigned char*, int, int);
    /* 发送MQTT消息的函数 */
	int (*mqttwrite) (struct Network*, unsigned char*, int, int);
    /* 使用SSL时的描述符 */
    SSL *ssl;
    /* 是否使用SSL  0：否  1：是 */
    int useSSL;
} Network;

int linux_read(Network*, unsigned char*, int, int);
int linux_write(Network*, unsigned char*, int, int);
/* 初始化Network这个结构体 */
DLLExport void NetworkInit(Network*);
/* 连接Network（普通socket） */
DLLExport int NetworkConnect(Network*, char*, int);
/* 连接Network（带SSL的socket） */
DLLExport int NetworkConnectBySSL(Network*, const char*, const char*, const char*);
/* 连接Network（不带SSL的socket） */
DLLExport int NetworkConnectNotSSL(Network*, const char*, const char*);
/* 断开Network的连接 */
DLLExport void NetworkDisconnect(Network*);
/* 打印证书的内容 */
DLLExport void ShowCerts(SSL *);


#endif
