//
// Created by xwf on 2021/3/26.
//

#ifndef VISPECT_ERRORTABLE_H
#define VISPECT_ERRORTABLE_H

#include <string>

namespace vis {
    // 错误码
    enum et_code {
        et_success = 0,
        et_openfile,
        et_createGpioIndex,
        et_gpioUsed,
        et_param,
        et_createFile,
        et_gpioAttrib,
        et_setGpioAttrib,
        et_pthreadRunning,
        et_cmdlineParam,
        et_writeFile,
        et_pthreadCreate,
        et_fileOpened,
        et_getUartAttr,
        et_tcflush,
        et_tcsetattr,
        et_getBaudrate,
        et_select,
        et_timeout,
        et_fileMiss,
        et_socketOpened,
        et_socket,
        et_bind,
        et_setsockopt,
        et_connect,
        et_getsockopt,
        et_configFile,
        et_loadYamlFile,
        et_yamlParam,
        et_createDirectories,
        et_yamlthrow,
        et_pocothrow,
        et_recvfrom,
        et_sendto,
        et_loggerInit,
        et_initGpio,
        et_tcgetattr,
        et_busy,
        et_unknow, // 新的错误码从此行的上一行加入，加入后需在et_string数组中加入错误码的含义
    };

    // 错误信息
    extern const char *et_string[];

    extern std::string et_getErrStr(int errNo);

} // namespace vis

#endif // VISPECT_ERRORTABLE_H
