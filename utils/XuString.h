//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/8.
//

#ifndef VIS_ADOS_I7_XUSTRING_H
#define VIS_ADOS_I7_XUSTRING_H

#include <cstdint>
#include <string>
#include <cstring>
#include <vector>

class XuString {

public:
    /* 单例 */
    static XuString &getInstance();

    /**
     * 将str首尾的特殊字符去掉
     * @param str:需要进行处理的字符串
     *
     **/
    void trim(std::string &str);

    /**
     * 将str以pattern为分割符分割出来
     * @param str:需要进行分割的字符串
     * @param pattern:分割符
     *
     * @return 分割好的字符串数组
     **/
    std::vector<std::string> split(std::string str, std::string pattern);

    /**
      * 将str转成int
      * @param str:int的str形式
      * @param len：需要转换的str的长度
      *
      * @return 转换好的结果
      **/
    int Strtoint(const char *str, int len);

    /**
    * 将str转成longlong
    * @param digit:longlong的str形式
    * @param minus:是正或者负
    * @param len：需要转换的str的长度
    *
    * @return 转换好的结果
     **/
    long long int Strtointcode(const char *digit, bool minus, int len);

    /**
     * 获取指定格式的时间字符串
     *
     * @param sz：用来存获取到的时间字符串的指针
     * @param nsize：用来存获取到的时间字符串的指针的长度
     * @param format：时间字符串的格式   (录制视频文件一般使用%Y%m%d%H%M%S)
     * @param format：是否使用标准时间（格林威治时间）
     *
     * @return 指定格式的时间字符串的长度
     * */
    int getStrFormTime(char *sz, std::size_t nsize, const char *format, bool bgmt);

    /**
    * 将秒级时间戳转成指定格式的时间字符串
    *
    * @param timestamp : 秒级的时间戳
    * @param sz：用来存获取到的时间字符串的指针
    * @param nsize：用来存获取到的时间字符串的指针的长度
    * @param format：时间字符串的格式   (录制视频文件一般使用%Y%m%d%H%M%S)
    * @param format：是否使用标准时间（格林威治时间）
    *
    * @return 指定格式的时间字符串的长度
    * */
    int getStrFormTimestamp(uint64_t timestamp, char *sz, std::size_t nsize, const char *format, bool bgmt);

    /**
     * BCD字节数组===>String
     *
     * @param bytes
     * @return 十进制字符串
     */
    std::string bcd2String(const uint8_t *buf, const int len);

    /**
    * 字符串==>BCD字节数组
    *
    * @param str ： BCD字符串
    * @param buf : 存放BCD数据的指针
    *
    * @return BCD字节数组的长度
    */
    int string2Bcd(std::string str, uint8_t *buf);


    uint8_t ascII2Bcd(uint8_t asc);


    /**
     *  将string类型的byte数组转成byte数组
     * @param str : 需要转换成byte数组的str  必须得是%02x格式得，每个byte之间不能有空格
     * @param buf ： 存放转换出来得byte数组得内存
     * @param bufLen ： 存放转换出来得byte数组得内存的长度
     *
     * @return 转出来得byte数组的长度
     */
    int stringToByteArray(std::string str, uint8_t *buf, const int bufLen);


    /**
     *  把byte数组转成16进制的string(string是%02x格式的,每个byte之间没空格)
     * @param buf : 需要转成string的byte数组
     * @param bufLen : 需要转成string的byte数组的长度
     *
     * @return 转换出来的string
     */
    std::string byteArrayToString(const uint8_t * const buf, const int bufLen);


    /**
     *  比对两个byte数组是否相等
     *
     * @param array1 : 需要比对的byte数组1
     * @param arrayLen1 : 需要比对的byte数组1的长度
     * @param array2 : 需要比对的byte数组2
     * @param arrayLen2 : 需要比对的byte数组2的长度
     *
     * @return 是否相同
     */
    bool cmpTwoByteArray(const uint8_t *array1, const int arrayLen1, const uint8_t *array2, const int arrayLen2);

    /**
     * 字符串中的某些字符串替换
     *
     * @param resource_str : 原字符串
     * @param sub_str ： 需要被替换的字符串
     * @param new_str ： 替换字符串
     * @return    替换完成后的字符串
     */
    std::string replaceString(std::string resource_str, std::string sub_str, std::string new_str);

    /**
     * AES-128-ECB 解密+BASE64解密
     *
     * @param myKey ： key
     * @param src  ： 需要进行解密的字符串
     * @return  解密完成后的字符串
     */
    std::string AES_128_ECB_AND_BASE64_Decrypt(uint8_t *myKey, std::string src);

    /**
     * AES(ECB)加密+BASE64加密
     *
     * @param myKey ： key
     * @param src  ： 需要进行加密的字符串
     * @return  加密完成后的字符串
     */
    std::string AES_ECB_128_AND_BASE64_Encrypt(uint8_t *myKey, std::string src);

    /**
     * AES(ECB-128)加密
     *
     * @param myKey ： key
     * @param src  ： 需要进行加密的数据
     * @return  加密完成后的字符串
     */
    std::string AES_ECB_128_Encrypt(uint8_t *myKey, std::string src);

    /**
     * AES(ECB)解密
     *
     * @param myKey ： key
     * @param src  ： 需要进行解密的数据
     * @return  解密完成后的字符串
     */
    std::string AES_ECB_128_Decrypt(uint8_t *myKey, std::string src);

    /**
     * 把float转成特殊位数小数的字符串
     *
     * @param val ： 原始float
     * @param fixed ： 保留小数点后多少位
     * @return  转换完成后的字符串
     */
    std::string formatFloatValue(float val, int fixed);

    /**
     * 获取当前CPU的序列号
     *
     * @return  序列号
     */
    std::string getCPUSerialNumber();

};


#endif //VIS_ADOS_I7_XUSTRING_H
