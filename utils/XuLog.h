//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/28.
//

#ifndef VIS_G3_SOFTWARE_XULOG_H
#define VIS_G3_SOFTWARE_XULOG_H

#include <string>
#include <Poco/Thread.h>
#include <mutex>
#include "XuLogStream.h"

namespace vis {
    /**
     * 日志类
     *
     */
    class XuLog : public XuLogChanneCallback , public Poco::Runnable{
    public:

        /* 日志级别 */
        enum LOG_LEVEL{
            LOG_LEVEL_FATAL = 0,
            LOG_LEVEL_CRITICAL,
            LOG_LEVEL_ERROR,
            LOG_LEVEL_WARNING,
            LOG_LEVEL_NOTICE,
            LOG_LEVEL_INFORMATION,
            LOG_LEVEL_DEBUG,
            LOG_LEVEL_TRACE,
        };

        XuLog();

        ~XuLog();


        /* 单例 */
        static XuLog &getInstance();

        XuLogStream &log(const int loglevel,const char *tag);

        void onLogMakeFinish(const int loglevel, const char *logContent) override;


//
//        /**
//         * 打印fatal级别的log
//         *
//         * @param tag ： 日志的TAG
//         * @param logContent ： 日志的原始内容
//         */
//        void fatal(const std::string &tag,const std::string &logContent);
//        /**
//         * 打印critical级别的log
//         *
//         * @param tag ： 日志的TAG
//         * @param logContent ： 日志的原始内容
//         */
//        void critical(const std::string &tag,const std::string &logContent);
//        /**
//         * 打印error级别的log
//         *
//         * @param tag ： 日志的TAG
//         * @param logContent ： 日志的原始内容
//         */
//        void error(const std::string &tag,const std::string &logContent);
//        /**
//         * 打印warning级别的log
//         *
//         * @param tag ： 日志的TAG
//         * @param logContent ： 日志的原始内容
//         */
//        void warning(const std::string &tag,const std::string &logContent);
//        /**
//         * 打印notice级别的log
//         *
//         * @param tag ： 日志的TAG
//         * @param logContent ： 日志的原始内容
//         */
//        void notice(const std::string &tag,const std::string &logContent);
//        /**
//         * 打印information级别的log
//         *
//         * @param tag ： 日志的TAG
//         * @param logContent ： 日志的原始内容
//         */
//        void information(const std::string &tag,const std::string &logContent);
//        /**
//         * 打印debug级别的log
//         *
//         * @param tag ： 日志的TAG
//         * @param logContent ： 日志的原始内容
//         */
//        void debug(const std::string &tag,const std::string &logContent);
//        /**
//         * 打印trace级别的log
//         *
//         * @param tag ： 日志的TAG
//         * @param logContent ： 日志的原始内容
//         */
//        void trace(const std::string &tag,const std::string &logContent);

        /**
         * 获取log目录下的所有log文件（只限获取表层目录的文件，子目录不算）
         *
         * @return 文件名列表
         * */
        std::vector<std::string> getAllLogFile();

        const std::string &getLogFileRootPath() const;

    private:







        /* 配置文件的路径 */
        const std::string logConfigFilePath = "/userdata/xulog.yaml";

        /* 当前需要保存的日志的级别 */
        LOG_LEVEL curSaveLogLevel = LOG_LEVEL_WARNING;
        /* 临时存放log内容的缓冲区 */
        std::string logCache;
        /* 存放log内容的缓冲区的最大小  目标超过了这个大小  那么就进行一次强制同步到文件的操作 */
        const std::size_t MAX_LOG_CACHE_SIZE = 1024*1024*2;
        /* 同步锁 */
        std::mutex mLock;
        /* 是否初始化了 */
        bool inited = false;
        /* 日志文件的描述符 */
        FILE *logFd;
        /* 日志文件的大小限制 */
        long long max_log_file_size = 1024*1024*5;
        /* 日志文件的个数限制 */
        int max_log_file_list_size = 30;
        /* 日志文件的存放位置 */
        std::string logFileRootPath = "/userdata/media/vispectLog/";
        /* 当前写入的日志文件的路径 */
        std::string curLogFilePath =logFileRootPath+"curlog";
        /* 文件名中的序号的最大值 超过这个就重新从0开始计算 */
        const std::size_t MAX_FILE_NAME_NUM = 99999;
        /* 同步缓存的内容到日志文件里的时间间隔 单位：s */
        const uint64_t MAX_SYNC_CACHE_TO_FILE_INTERVAL = 2;
        /* 是否需要立马同步缓存的内容到日志文件里 */
        bool needSyncToFile = false;
        /* 使用的线程类 */
        Poco::Thread saveThread;


        /* 同时允许存在多少个通道去放日志内容 */
        const static int MAX_LOG_CHANNEL = 10;
        /* 读写通道信息的互斥锁 */
        std::mutex channelLock;
        /* 日志通道的列表 */
        XuLogStream channel[MAX_LOG_CHANNEL];


        void run() override;

        /**
         * 打开当前写入的日志文件
         */
        void openLogFile();

        /**
         * 把日志内容写到缓存里
         *
         * @param fullContent ： 日志内容（带了时间几倍和线程名那些的）
         */
        void putToCache(const std::string &fullContent);

        /**
         * 把缓存的日志内容写入到日志文件中
         */
        void writeCacheToFile();

        /**
         * 重置日志文件的文件名，让序号从0开始
         *
         * @param files ： 日志文件的文件名列表
         */
        void resetLogFileName(std::vector<std::string> &files);

        /**
         * 格式化日志内容并打印打控制台，同时根据log级别保存到log文件里
         *
         * @param tag ： 日志的TAG
         * @param logContent ： 日志的原始内容
         * @param loglevel ： 日志级别
         */
        void showAndSaveLog(const std::string &tag, const std::string &logContent,LOG_LEVEL loglevel);







    };

} // vis

#endif //VIS_G3_SOFTWARE_XULOG_H
