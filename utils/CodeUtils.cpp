//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/9/27.
//

#include <cassert>
#include "CodeUtils.h"

CodeUtils &CodeUtils::getInstance() {
    static CodeUtils _instance;
    return _instance;
}

uint16_t CodeUtils::BbToUint16(uint8_t *bytes) {
    uint16_t _uint16 = bytes[1] & 0xFF;
    _uint16 |= (bytes[0] << 8) & 0xFF00;
    return _uint16;
}

int16_t CodeUtils::BbToint16(uint8_t *bytes) {
    int16_t _int16 = bytes[1] & 0xFF;
    _int16 |= (bytes[0] << 8) & 0xFF00;
    return _int16;
}

uint32_t CodeUtils::BbToUint32(uint8_t *bytes) {
    uint32_t _uint32 = bytes[3] & 0xFF;
    _uint32 |= (bytes[2] << 8) & 0xFF00;
    _uint32 |= (bytes[1] << 16) & 0xFF0000;
    _uint32 |= (bytes[0] << 24) & 0xFF000000;
    return _uint32;
}

int32_t CodeUtils::BbToint32(uint8_t *bytes) {
    int32_t _int32 = bytes[3] & 0xFF;
    _int32 |= (bytes[2] << 8) & 0xFF00;
    _int32 |= (bytes[1] << 16) & 0xFF0000;
    _int32 |= (bytes[0] << 24) & 0xFF000000;
    return _int32;
}

uint64_t CodeUtils::BbToUint64(uint8_t *bytes)
{
    uint64_t _uint64 = bytes[7] & 0xFF;
    _uint64 |= (static_cast<uint64_t>(bytes[6]) << 8) & 0xFF00;
    _uint64 |= (static_cast<uint64_t>(bytes[5]) << 16) & 0xFF0000;
    _uint64 |= (static_cast<uint64_t>(bytes[4]) << 24) & 0xFF000000;
    _uint64 |= (static_cast<uint64_t>(bytes[3]) << 32) & 0xFF00000000;
    _uint64 |= (static_cast<uint64_t>(bytes[2]) << 40) & 0xFF0000000000;
    _uint64 |= (static_cast<uint64_t>(bytes[1]) << 48) & 0xFF000000000000;
    _uint64 |= (static_cast<uint64_t>(bytes[0]) << 56) & 0xFF00000000000000;
    return _uint64;
}
int64_t CodeUtils::BbToint64(uint8_t *bytes)
{
    int64_t intValue = 0;
    for (std::size_t i = 0; i < sizeof(int64_t); i++) {
        intValue = (intValue << 8) | static_cast<uint8_t>(bytes[i]);
    }
    return intValue;
}
int32_t CodeUtils::uint16ToBb(uint16_t uint16, uint8_t *buf) {
    buf[1] = uint16 & 0xFF;
    buf[0] = (uint16 & 0xFF00) >> 8;
    return 2;
}

int32_t CodeUtils::int16ToBb(int16_t int16, uint8_t *buf) {
    buf[1] = int16 & 0xFF;
    buf[0] = (int16 & 0xFF00) >> 8;
    return 2;
}

int32_t CodeUtils::uint32ToBb(uint32_t uint32, uint8_t *buf) {
    buf[3] = uint32 & 0xFF;
    buf[2] = (uint32 & 0xFF00) >> 8;
    buf[1] = (uint32 & 0xFF0000) >> 16;
    buf[0] = (uint32 & 0xFF000000) >> 24;
    return 4;
}

int32_t CodeUtils::int32ToBb(int32_t int32, uint8_t *buf) {
    buf[3] = int32 & 0xFF;
    buf[2] = (int32 & 0xFF00) >> 8;
    buf[1] = (int32 & 0xFF0000) >> 16;
    buf[0] = (int32 & 0xFF000000) >> 24;
    return 4;
}

int32_t CodeUtils::uint64ToBb(uint64_t uint64, uint8_t *buf) {
    buf[7] = uint64 & 0xFF;
    buf[6] = (uint64 & 0xFF00) >> 8;
    buf[5] = (uint64 & 0xFF0000) >> 16;
    buf[4] = (uint64 & 0xFF000000) >> 24;
    buf[3] = (uint64 & 0xFF00000000) >> 32;
    buf[2] = (uint64 & 0xFF0000000000) >> 40;
    buf[1] = (uint64 & 0xFF000000000000) >> 48;
    buf[0] = (uint64 & 0xFF00000000000000) >> 56;
    return 8;
}

int32_t CodeUtils::int64ToBb(int64_t int64, uint8_t *buf) {
    buf[7] = int64 & 0xFF;
    buf[6] = (int64 & 0xFF00) >> 8;
    buf[5] = (int64 & 0xFF0000) >> 16;
    buf[4] = (int64 & 0xFF000000) >> 24;
    buf[3] = (int64 & 0xFF00000000) >> 32;
    buf[2] = (int64 & 0xFF0000000000) >> 40;
    buf[1] = (int64 & 0xFF000000000000) >> 48;
    buf[0] = (int64 & 0xFF00000000000000) >> 56;
    return 8;
}

uint16_t
CodeUtils::findCharsInChars(const char *haystack, size_t hlen, const char *needle, size_t nlen, uint16_t *results) {
    int indexCount = 0;
    const char *cur;
    const char *last;
    assert(haystack);
    assert(needle);
    assert(nlen > 1);
    last = haystack + hlen - nlen;
    int index = 0;
    for (cur = haystack; cur <= last; ++cur) {
        if (cur[0] == needle[0] && memcmp(cur, needle, nlen) == 0) {
            results[indexCount] = index;
            indexCount++;
        }
        index++;
    }
    return indexCount;

}

uint16_t CodeUtils::generateCrc16(uint8_t *buf, int len) {
    uint16_t crc = 0xFFFF;
    for (int i = 0; i < len; i++) {
        crc ^= (buf[i] & 0xFF);
        crc = crc & 0xFFFF;
        for (int j = 0; j < 8; j++) {
            if ((crc & 0x0001) != 0) {
                crc = (crc >> 1) ^ 0x8408;
            } else {
                crc = (crc >> 1);
            }
            crc = crc & 0xFFFF;
        }
    }
    return crc;
}

int
CodeUtils::doEscape4SendFromG3PCConfigure(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf) {
    int ret = -1;
    int index = 0;
    for (int i = 0; i < startIndex; i++) {
        decBuf[index] = srcBuf[i];
        index++;
    }

    for (int i = startIndex; i <= endIndex; i++) {
        if (srcBuf[i] == 0x5A) {
            decBuf[index] = 0x5C;
            index++;
            decBuf[index] = 0x01;
            index++;
        } else if (srcBuf[i] == 0x5B) {
            decBuf[index] = 0x5C;
            index++;
            decBuf[index] = 0x02;
            index++;
        } else if (srcBuf[i] == 0x5C) {
            decBuf[index] = 0x5C;
            index++;
            decBuf[index] = 0x03;
            index++;
        } else {
            decBuf[index] = srcBuf[i];
            index++;
        }
    }
    for (int i = endIndex + 1; i < srcLen; i++) {
        decBuf[index] = srcBuf[i];
        index++;
    }
    ret = index;
    return ret;
}

int CodeUtils::doEscape4ReceiveFromG3PCConfigure(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex,
                                                 uint8_t *decBuf) {
    int ret = -1;
    int bufIndex = 0;
    /* 先把不需要转义的写进去 */
    for (int i = 0; i < startIndex; i++) {
        decBuf[bufIndex] = srcBuf[i];
        bufIndex++;
    }
    /* 再把需要转义的地方写进去 */
    for (int i = startIndex; i <= endIndex; i++) {
        /* 收到5C了  下面就判断是01还是02还是03了 以此决定转成哪个*/
        if (srcBuf[i] == 0x5C) {
            /* 5F01 需要转成5E */
            if (srcBuf[i + 1] == 0x01) {
                decBuf[bufIndex] = 0x5A;
                bufIndex++;
                i++;
            } else if (srcBuf[i + 1] == 0x02) {
                decBuf[bufIndex] = 0x5B;
                bufIndex++;
                i++;
            } else if (srcBuf[i + 1] == 0x03) {
                decBuf[bufIndex] = 0x5C;
                bufIndex++;
                i++;
            } else {
                decBuf[bufIndex] = srcBuf[i];
                bufIndex++;
            }
        } else {
            /* 不需要转义的  直接写进去 */
            decBuf[bufIndex] = srcBuf[i];
            bufIndex++;
        }

    }
    /* 不需要转义的  直接写进去 */
    for (int i = endIndex + 1; i < srcLen; i++) {
        decBuf[bufIndex] = srcBuf[i];
        bufIndex++;
    }

    ret = bufIndex;
    return ret;
}

uint16_t CodeUtils::bBToUint16(const uint8_t *bytes) {
    uint16_t _uint16 = bytes[0] & 0xFF;
    _uint16 |= (bytes[1] << 8) & 0xFF00;
    return _uint16;
}

int16_t CodeUtils::bBToint16(uint8_t *bytes) {
    int16_t _int16 = bytes[0] & 0xFF;
    _int16 |= (bytes[1] << 8) & 0xFF00;
    return _int16;
}

int32_t CodeUtils::uint16TobB(uint16_t uint16, uint8_t *buf) {
    buf[0] = uint16 & 0xFF;
    buf[1] = (uint16 & 0xFF00) >> 8;
    return 2;
}

int32_t CodeUtils::int16TobB(int16_t int16, uint8_t *buf) {
    buf[0] = int16 & 0xFF;
    buf[1] = (int16 & 0xFF00) >> 8;
    return 2;
}

int CodeUtils::doEscape4SendFromRS485(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf) {
    int ret = -1;
    int index = 0;
    for (int i = 0; i < startIndex; i++) {
        decBuf[index] = srcBuf[i];
        index++;
    }

    for (int i = startIndex; i <= endIndex; i++) {
        if (srcBuf[i] == 0x5E) {
            decBuf[index] = 0x5F;
            index++;
            decBuf[index] = 0x01;
            index++;
        } else if (srcBuf[i] == 0x5F) {
            decBuf[index] = 0x5F;
            index++;
            decBuf[index] = 0x02;
            index++;
        } else {
            decBuf[index] = srcBuf[i];
            index++;
        }
    }
    for (int i = endIndex + 1; i < srcLen; i++) {
        decBuf[index] = srcBuf[i];
        index++;
    }
    ret = index;
    return ret;
}

int CodeUtils::doEscape4ReceiveFromRS485(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf) {
    int ret = -1;
    int bufIndex = 0;
    /* 先把不需要转义的写进去 */
    for (int i = 0; i < startIndex; i++) {
        decBuf[bufIndex] = srcBuf[i];
        bufIndex++;
    }
    /* 再把需要转义的地方写进去 */
    for (int i = startIndex; i <= endIndex; i++) {
        /* 收到5C了  下面就判断是01还是02还是03了 以此决定转成哪个*/
        if (srcBuf[i] == 0x5F) {
            /* 5F01 需要转成5E */
            if (srcBuf[i + 1] == 0x01) {
                decBuf[bufIndex] = 0x5E;
                bufIndex++;
                i++;
            } else if (srcBuf[i + 1] == 0x02) {
                decBuf[bufIndex] = 0x5F;
                bufIndex++;
                i++;
            } else {
                decBuf[bufIndex] = srcBuf[i];
                bufIndex++;
            }
        } else {
            /* 不需要转义的  直接写进去 */
            decBuf[bufIndex] = srcBuf[i];
            bufIndex++;
        }

    }
    /* 不需要转义的  直接写进去 */
    for (int i = endIndex + 1; i < srcLen; i++) {
        decBuf[bufIndex] = srcBuf[i];
        bufIndex++;
    }

    ret = bufIndex;
    return ret;
}

int CodeUtils::doEscape4SendFromMCUUart(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf) {
    int ret = -1;
    int index = 0;
    for (int i = 0; i < startIndex; i++) {
        decBuf[index] = srcBuf[i];
        index++;
    }

    for (int i = startIndex; i <= endIndex; i++) {
        if (srcBuf[i] == 0x5D) {
            decBuf[index] = 0x5F;
            index++;
            decBuf[index] = 0x01;
            index++;
        } else if (srcBuf[i] == 0x5E) {
            decBuf[index] = 0x5F;
            index++;
            decBuf[index] = 0x02;
            index++;
        } else if (srcBuf[i] == 0x5F) {
            decBuf[index] = 0x5F;
            index++;
            decBuf[index] = 0x03;
            index++;
        } else {
            decBuf[index] = srcBuf[i];
            index++;
        }
    }
    for (int i = endIndex + 1; i < srcLen; i++) {
        decBuf[index] = srcBuf[i];
        index++;
    }
    ret = index;
    return ret;
}

int CodeUtils::doEscape4ReceiveFromMCUUart(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf) {
    int ret = -1;
    int bufIndex = 0;
    /* 先把不需要转义的写进去 */
    for (int i = 0; i < startIndex; i++) {
        decBuf[bufIndex] = srcBuf[i];
        bufIndex++;
    }
    /* 再把需要转义的地方写进去 */
    for (int i = startIndex; i <= endIndex; i++) {
        /* 收到5C了  下面就判断是01还是02还是03了 以此决定转成哪个*/
        if (srcBuf[i] == 0x5F) {
            /* 5F01 需要转成5E */
            if (srcBuf[i + 1] == 0x01) {
                decBuf[bufIndex] = 0x5D;
                bufIndex++;
                i++;
            } else if (srcBuf[i + 1] == 0x02) {
                decBuf[bufIndex] = 0x5E;
                bufIndex++;
                i++;
            } else if (srcBuf[i + 1] == 0x03) {
                decBuf[bufIndex] = 0x5F;
                bufIndex++;
                i++;
            } else {
                decBuf[bufIndex] = srcBuf[i];
                bufIndex++;
            }
        } else {
            /* 不需要转义的  直接写进去 */
            decBuf[bufIndex] = srcBuf[i];
            bufIndex++;
        }

    }
    /* 不需要转义的  直接写进去 */
    for (int i = endIndex + 1; i < srcLen; i++) {
        decBuf[bufIndex] = srcBuf[i];
        bufIndex++;
    }

    ret = bufIndex;
    return ret;
}

uint16_t CodeUtils::generateCrc16_MODBUS(uint8_t *buf, int len) {
    uint16_t crc = 0xFFFF;
    for (int i = 0; i < len; i++) {
        crc ^= (buf[i] & 0xFF);
        crc = crc & 0xFFFF;
        for (int j = 0; j < 8; j++) {
            if ((crc & 0x0001) != 0) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = (crc >> 1);
            }
            crc = crc & 0xFFFF;
        }
    }
    return crc;
}

bool CodeUtils::bytesCMP(uint8_t *bytes1, uint8_t *bytes2, int len) {
    bool ret = true;
    for(int i = 0; i < len; i ++){
        ret = (bytes1[i] == bytes2[i]) && ret;
    }
    return ret;
}







