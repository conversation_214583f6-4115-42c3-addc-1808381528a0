
#include "term.h"
#include <stdio.h>
#include <string.h>
#include <termios.h>
#include <unistd.h>

#define debug(str...)                       \
    printf("[%s][%d]", __func__, __LINE__); \
    printf(str);                            \
    fflush(stdout)

#define perr(str...)                        \
    printf("[%s][%d]", __func__, __LINE__); \
    printf(str);                            \
    printf(" : %s\n", strerror(errno));     \
    fflush(stdout);                         \
    _exit(errno)

int term_errno = 0;
char term_err_buff[1024] = {0};

int term_set_raw(int fd) {
    struct termios tio;
    tcgetattr(fd, &tio);

    /* BSD raw mode */
    cfmakeraw(&tio);

    return tcsetattr(fd, TCSANOW, &tio);
}

int term_set_baudrate(int fd, int baudrate) {
    int rval, r;
    speed_t spd;
    struct termios tio;

    rval = 0;

    do { /* dummy */
        tcgetattr(fd, &tio);
        spd = baudrate;
        r = cfsetospeed(&tio, spd);
        if (r < 0) {
            term_errno = TERM_ESETOSPEED;
            rval = -1;
            break;
        }
        /* ispeed = 0, means same as ospeed (see POSIX) */
        cfsetispeed(&tio, B0);

        return tcsetattr(fd, TCSANOW, &tio);
    } while (0);

    return rval;
}

int term_set_parity(int fd, enum parity_e parity) {
    int rval;
    struct termios tiop;

    rval = 0;

    do { /* dummy */
        tcgetattr(fd, &tiop);
        switch (parity) {
            case P_EVEN:
                tiop.c_cflag &= ~(PARODD | CMSPAR);
                tiop.c_cflag |= PARENB;
                break;
            case P_ODD:
                tiop.c_cflag &= ~CMSPAR;
                tiop.c_cflag |= PARENB | PARODD;
                break;
            case P_MARK:
                tiop.c_cflag |= PARENB | PARODD | CMSPAR;
                break;
            case P_SPACE:
                tiop.c_cflag &= ~PARODD;
                tiop.c_cflag |= PARENB | CMSPAR;
                break;
            case P_NONE:
                tiop.c_cflag &= ~(PARENB | PARODD | CMSPAR);
                break;
            default:
                term_errno = TERM_EPARITY;
                rval = -1;
                break;
        }
        if (rval < 0)
            break;
        return tcsetattr(fd, TCSANOW, &tiop);

    } while (0);

    return rval;
}

int term_set_databits(int fd, int databits) {
    int rval;
    struct termios tiop;

    rval = 0;

    do { /* dummy */
        tcgetattr(fd, &tiop);

        switch (databits) {
            case 5:
                tiop.c_cflag = (tiop.c_cflag & ~CSIZE) | CS5;
                break;
            case 6:
                tiop.c_cflag = (tiop.c_cflag & ~CSIZE) | CS6;
                break;
            case 7:
                tiop.c_cflag = (tiop.c_cflag & ~CSIZE) | CS7;
                break;
            case 8:
                tiop.c_cflag = (tiop.c_cflag & ~CSIZE) | CS8;
                break;
            default:
                term_errno = TERM_EDATABITS;
                rval = -1;
                break;
        }
        if (rval < 0)
            break;

        return tcsetattr(fd, TCSANOW, &tiop);

    } while (0);

    return rval;
}

int term_set_stopbits(int fd, int stopbits) {
    int rval;
    struct termios tiop;

    rval = 0;

    do { /* dummy */
        tcgetattr(fd, &tiop);

        switch (stopbits) {
            case 1:
                tiop.c_cflag &= ~CSTOPB;
                break;
            case 2:
                tiop.c_cflag |= CSTOPB;
                break;
            default:
                term_errno = TERM_ESTOPBITS;
                rval = -1;
                break;
        }
        if (rval < 0)
            break;
        return tcsetattr(fd, TCSANOW, &tiop);

    } while (0);

    return rval;
}

int term_set_flowcntrl(int fd, enum flowcntrl_e flowcntl) {
    int rval;
    struct termios tiop;

    rval = 0;

    do { /* dummy */
        tcgetattr(fd, &tiop);

        switch (flowcntl) {
            case FC_RTSCTS:
                tiop.c_cflag |= CRTSCTS;
                tiop.c_iflag &= ~(IXON | IXOFF | IXANY);
                break;
            case FC_XONXOFF:
                tiop.c_cflag &= ~(CRTSCTS);
                tiop.c_iflag |= IXON | IXOFF;
                break;
            case FC_NONE:
                tiop.c_cflag &= ~(CRTSCTS);
                tiop.c_iflag &= ~(IXON | IXOFF | IXANY);
                break;
            default:
                term_errno = TERM_EFLOW;
                rval = -1;
                break;
        }
        if (rval < 0)
            break;
        return tcsetattr(fd, TCSANOW, &tiop);
    } while (0);

    return rval;
}

int term_set_local(int fd, int local) {
    int rval;
    struct termios tiop;

    rval = 0;

    do { /* dummy */

        tcgetattr(fd, &tiop);

        if (local)
            tiop.c_cflag |= CLOCAL;
        else
            tiop.c_cflag &= ~CLOCAL;

        return tcsetattr(fd, TCSANOW, &tiop);

    } while (0);

    return rval;
}

int term_set_hupcl(int fd, int on) {
    int rval;
    struct termios tiop;

    rval = 0;

    do { /* dummy */
        tcgetattr(fd, &tiop);

        if (on)
            tiop.c_cflag |= HUPCL;
        else
            tiop.c_cflag &= ~HUPCL;

        return tcsetattr(fd, TCSANOW, &tiop);
    } while (0);

    return rval;
}

int term_set_CR(int fd, int on) {
    int rval;
    struct termios tiop;

    rval = 0;

    do { /* dummy */
        tcgetattr(fd, &tiop);

        if (on)
            tiop.c_cflag |= ~IGNCR;
        else
            tiop.c_cflag &= IGNCR;

        return tcsetattr(fd, TCSANOW, &tiop);
    } while (0);

    return rval;
}

int
term_set(int fd, int raw, int baud, enum parity_e parity, int databits, int stopbits, enum flowcntrl_e fc, int local,
         int hup_close) {
    int rval = 0, r;

    do { /* dummy */

        if (raw) {
            r = term_set_raw(fd);
            if (r < 0) {
                rval = -1;
                debug("set fail\n");
                break;
            }
        }

        r = term_set_baudrate(fd, baud);
        if (r < 0) {
            rval = -1;
            debug("set fail\n");
            break;
        }

        r = term_set_parity(fd, parity);
        if (r < 0) {
            rval = -1;
            debug("set fail\n");
            break;
        }

        r = term_set_databits(fd, databits);
        if (r < 0) {
            rval = -1;
            debug("set fail\n");
            break;
        }

        r = term_set_stopbits(fd, stopbits);
        if (r < 0) {
            rval = -1;
            debug("set fail\n");
            break;
        }

        r = term_set_flowcntrl(fd, fc);
        if (r < 0) {
            rval = -1;
            debug("set fail\n");
            break;
        }

        r = term_set_local(fd, local);
        if (r < 0) {
            rval = -1;
            debug("set fail\n");
            break;
        }

        r = term_set_hupcl(fd, hup_close);
        if (r < 0) {
            rval = -1;
            debug("set fail\n");
            break;
        }

    } while (0);

    return rval;
}

static const char *const term_err_str[] = {[TERM_EOK] = "No error",
        [TERM_ENOINIT] = "Framework is uninitialized",
        [TERM_EFULL] = "Framework is full",
        [TERM_ENOTFOUND] = "Filedes not in the framework",
        [TERM_EEXISTS] = "Filedes already in the framework",
        [TERM_EATEXIT] = "Cannot install atexit handler",
        [TERM_EISATTY] = "Filedes is not a tty",
        [TERM_EFLUSH] = "Cannot flush the device",
        [TERM_EGETATTR] = "Cannot get the device attributes",
        [TERM_ESETATTR] = "Cannot set the device attributes",
        [TERM_EBAUD] = "Invalid baud rate",
        [TERM_ESETOSPEED] = "Cannot set the output speed",
        [TERM_ESETISPEED] = "Cannot set the input speed",
        [TERM_EGETSPEED] = "Cannot decode speed",
        [TERM_EPARITY] = "Invalid parity mode",
        [TERM_EDATABITS] = "Invalid number of databits",
        [TERM_ESTOPBITS] = "Invalid number of stopbits",
        [TERM_EFLOW] = "Invalid flowcontrol mode",
        [TERM_EDTRDOWN] = "Cannot lower DTR",
        [TERM_EDTRUP] = "Cannot raise DTR",
        [TERM_EMCTL] = "Cannot get mctl status",
        [TERM_EDRAIN] = "Cannot drain the device",
        [TERM_EBREAK] = "Cannot send break sequence",
        [TERM_ERTSDOWN] = "Cannot lower RTS",
        [TERM_ERTSUP] = "Cannot raise RTS"};

const char *term_strerror(int terrnum, int errnum) {
    const char *rval;

    switch (terrnum) {
        case TERM_EFLUSH:
        case TERM_EGETATTR:
        case TERM_ESETATTR:
        case TERM_ESETOSPEED:
        case TERM_ESETISPEED:
        case TERM_EDRAIN:
        case TERM_EBREAK:
            snprintf(term_err_buff, sizeof(term_err_buff), "%s: %s", term_err_str[terrnum], strerror(errnum));
            rval = term_err_buff;
            break;
        case TERM_EOK:
        case TERM_ENOINIT:
        case TERM_EFULL:
        case TERM_ENOTFOUND:
        case TERM_EEXISTS:
        case TERM_EATEXIT:
        case TERM_EISATTY:
        case TERM_EBAUD:
        case TERM_EPARITY:
        case TERM_EDATABITS:
        case TERM_ESTOPBITS:
        case TERM_EFLOW:
        case TERM_EDTRDOWN:
        case TERM_EDTRUP:
        case TERM_EMCTL:
        case TERM_ERTSDOWN:
        case TERM_ERTSUP:
            snprintf(term_err_buff, sizeof(term_err_buff), "%s", term_err_str[terrnum]);
            rval = term_err_buff;
            break;
        default:
            rval = nullptr;
            break;
    }

    return rval;
}