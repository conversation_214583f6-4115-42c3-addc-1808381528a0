//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/9/27.
//
#ifndef VISPECT_CODEUTILS_H
#define VISPECT_CODEUTILS_H

#include <cstdint>
#include <cstring>

class CodeUtils {
public:
    /* 单例 */
    static CodeUtils &getInstance();

    /* 转成无符号的short （大端）*/
    uint16_t BbToUint16(uint8_t *bytes);

    /* 转成无符号的short （小端）*/
    uint16_t bBToUint16(const uint8_t *bytes);

    /* 转成有符号的short （大端）*/
    int16_t BbToint16(uint8_t *bytes);


    /* 转成有符号的short （小端）*/
    int16_t bBToint16(uint8_t *bytes);

    /* 转成无符号的int （大端）*/
    uint32_t BbToUint32(uint8_t *bytes);

    /* 转成有符号的int （大端）*/
    int32_t BbToint32(uint8_t *bytes);
    /* 转成无符号的long （大端）*/
    uint64_t BbToUint64(uint8_t *bytes);
    /* 转成有符号的long （大端）*/
    int64_t BbToint64(uint8_t *bytes);

    /* 无符号的的short转成char[2] （大端）*/
    int32_t uint16ToBb(uint16_t uint16, uint8_t *buf);

    /* 无符号的的short转成char[2] （小端）*/
    int32_t uint16TobB(uint16_t uint16, uint8_t *buf);

    /* 有符号的的short转成char[2] （大端）*/
    int32_t int16ToBb(int16_t int16, uint8_t *buf);

    /* 有符号的的short转成char[2] （小端）*/
    int32_t int16TobB(int16_t int16, uint8_t *buf);

    /* 无符号的的int转成char[4] （大端）*/
    int32_t uint32ToBb(uint32_t uint32, uint8_t *buf);

    /* 有符号的的int转成char[4] （大端）*/
    int32_t int32ToBb(int32_t int32, uint8_t *buf);

    /* 无符号的的int转成char[8] （大端）*/
    int32_t uint64ToBb(uint64_t uint64, uint8_t *buf);

    /* 有符号的的int转成char[8] （大端）*/
    int32_t int64ToBb(int64_t int64, uint8_t *buf);


    /**
     * 在一个char数组里找到目标数组所有出现的下标
     *
     * @param haystack ： 原数组
     *
     *
     *
     *
     * */
    uint16_t findCharsInChars(const char *haystack, size_t hlen, const char *needle, size_t nlen, uint16_t *results);


    /**
     * CRC16校验
     *
     * @param buf ：源数据
     * @param len ：需要校验的数据长度
     *
     * @return 结果
     */
    uint16_t generateCrc16(uint8_t *buf, int len);

    /**
     * CRC16校验(CRC-16/MODBUS)
     *
     * @param buf ：源数据
     * @param len ：需要校验的数据长度
     *
     * @return 结果
     */
    uint16_t generateCrc16_MODBUS(uint8_t *buf, int len);

    /**
     * PC端通过以太网配置G3的协议数据的发送前转义
	 * 0x5A <====> 0x5C01
	 * 0x5B <====> 0x5C02
	 * 0x5C <====> 0x5C03
     *
     * @param srcBuf ：源数据
     * @param startIndex ：源数据需要转义的开始位置
     * @param endIndex ： 源数据需要转义的结束位置
     * @param decBuf ： 转义完成的数据的存在地址
     *
     * @return 转义完成后数据的长度
     */
    int doEscape4SendFromG3PCConfigure(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf);

    /**
     * PC端通过以太网配置G3的协议数据的接收后转义
     * 0x5C01<====>0x5A
	 * 0x5C02<====>0x5B
	 * 0x5C03<====>0x5C
     *
     *
     * @param srcBuf ：源数据
     * @param startIndex ：源数据需要转义的开始位置
     * @param endIndex ： 源数据需要转义的结束位置
     * @param decBuf ： 转义完成的数据的存在地址
     *
     * @return 转义完成后数据的长度
     */
    int doEscape4ReceiveFromG3PCConfigure(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf);

    /**
     * RS485的发送前转义
     *
     * 0x5E <====> 0x5F01
     * 0x5F <====> 0x5F02
     *
     * @param srcBuf ：源数据
     * @param startIndex ：源数据需要转义的开始位置
     * @param endIndex ： 源数据需要转义的结束位置
     * @param decBuf ： 转义完成的数据的存在地址
     *
     * @return 转义完成后数据的长度
     */
    int doEscape4SendFromRS485(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf);

    /**
     * RS485的接收后转义
     *
     * 0x5F01 <====> 0x5E
     * 0x5F02 <====> 0x5F
     *
     *
     * @param srcBuf ：源数据
     * @param startIndex ：源数据需要转义的开始位置
     * @param endIndex ： 源数据需要转义的结束位置
     * @param decBuf ： 转义完成的数据的存在地址
     *
     * @return 转义完成后数据的长度
     */
    int doEscape4ReceiveFromRS485(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf);

    /**
     * 跟MCU的UART的发送前转义
     *
     * 0x5D <====> 0x5F01
     * 0x5E <====> 0x5F02
     * 0x5F <====> 0x5F03
     *
     * @param srcBuf ：源数据
     * @param startIndex ：源数据需要转义的开始位置
     * @param endIndex ： 源数据需要转义的结束位置
     * @param decBuf ： 转义完成的数据的存在地址
     *
     * @return 转义完成后数据的长度
     */
    int doEscape4SendFromMCUUart(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf);

    /**
     * 跟MCU的UART的接收后转义
     *
     * 0x5F01 <====> 0x5D
     * 0x5F02 <====> 0x5E
     * 0x5F03 <====> 0x5F
     *
     *
     * @param srcBuf ：源数据
     * @param startIndex ：源数据需要转义的开始位置
     * @param endIndex ： 源数据需要转义的结束位置
     * @param decBuf ： 转义完成的数据的存在地址
     *
     * @return 转义完成后数据的长度
     */
    int doEscape4ReceiveFromMCUUart(uint8_t *srcBuf, int srcLen, int startIndex, int endIndex, uint8_t *decBuf);

    bool bytesCMP(uint8_t *bytes1,uint8_t *bytes2,int len);


};

#endif // VISPECT_CODEUTILS_H
