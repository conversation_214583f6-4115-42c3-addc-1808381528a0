#ifndef _TERM_H
#define _TERM_H

enum flowcntrl_e {
    FC_NONE = 0,
    FC_RTSCTS,
    FC_XONXOFF,
    FC_OTHER,
    FC_ERROR
};

enum parity_e {
    P_NONE = 0,
    P_EVEN,
    P_ODD,
    P_MARK,
    P_SPACE,
    P_ERROR
};

enum term_errno_e {
    TERM_EOK = 0,
    TERM_ENOINIT,
    TERM_EFULL,
    TERM_ENOTFOUND,
    TERM_EEXISTS,
    TERM_EATEXIT,
    TERM_EISATTY,
    TERM_EFLUSH,   /* see errno */
    TERM_EGETATTR, /* see errno */
    TERM_ESETATTR, /* see errno */
    TERM_EBAUD,
    TERM_ESETOSPEED,
    TERM_ESETISPEED,
    TERM_EGETSPEED,
    TERM_EPARITY,
    TERM_EDATABITS,
    TERM_ESTOPBITS,
    TERM_EFLOW,
    TERM_EDTRDOWN,
    TERM_EDTRUP,
    TERM_EMCTL,
    TERM_EDRAIN, /* see errno */
    TERM_EBREAK,
    TERM_ERTSDOWN,
    TERM_ERTSUP
};

extern int term_errno;

int
term_set(int fd, int raw, int baud, enum parity_e parity, int databits, int stopbits, enum flowcntrl_e fc, int local,
         int hup_close);

const char *term_strerror(int terrnum, int errnum);

int term_set_baudrate(int fd, int baudrate);

int term_set_CR(int fd, int on);

#endif