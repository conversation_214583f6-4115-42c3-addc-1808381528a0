//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/8.
//

#include "DetectDataCallback.h"

void DetectDataCallback::onGetObjectInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) {
    printf("DetectDataCallback::onGetObjectInfo  \n");
}

int DetectDataCallback::getYUVData(int cameraId, uint8_t *buf,Vehicle_RealtimeStatus &vehicleRealtimeStatus, bool *needClean) {
    printf("DetectDataCallback::getYUVData  \n");
    return 0;
}

void DetectDataCallback::onGetDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
    printf("DetectDataCallback::onGetDetectionEvent  \n");
}

void DetectDataCallback::onGetDSMInfo(G3DetectionResult &detectionInfo) {
    printf("DetectDataCallback::onGetDSMInfo  \n");
}

void DetectDataCallback::onGetObjectInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) {
    printf("DetectDataCallback::onGetObjectInfo_GES  \n");
}

void DetectDataCallback::onGetObjectInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) {
    printf("DetectDataCallback::onGetObjectInfo_Adas  \n");
}

void DetectDataCallback::onGetDetectInof_CameraStatus(G3DetectionResult &detectionInfo) {
    printf("DetectDataCallback::onGetDetectInof_CameraStatus  \n");
}

void DetectDataCallback::onGetDetectInfo_BarCode(G3DetectionResult &detectionInfo,
                                                 std::vector<BarCodeInfo> &barCodeInfoList) {
    printf("DetectDataCallback::onGetDetectInfo_BarCode  \n");
}


