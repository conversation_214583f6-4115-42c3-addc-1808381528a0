//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/14.
//

#ifndef VIS_G3_SOFTWARE_VISALARMDECISION_MT_FACE_H
#define VIS_G3_SOFTWARE_VISALARMDECISION_MT_FACE_H
#include "VisAlarmDecision.h"
#include "myparams.h"
#include "putcnText.h"
#include "resnet18.h"
#include "get_alarm.h"
#include "yolov5s.h"
#include "dsmShareDefine.h"

namespace vis {
    /**
     * 地铁人脸的报警决策
     */
    class VisAlarmDecision_MT_Face : public VisAlarmDecision{
    public:
        VisAlarmDecision_MT_Face();

        /**
         * 解析 地铁DSM算法 识别出来的结果的方法
         * @param face_roi_info ： 铁DSM算法 识别出来的结果
         * @param frame : 人脸的图片
         * @param vehicleRealtimeStatus ：车辆实时状态
         */
        void parse_MT_DSM_OD(face_message &face_roi_info, cv::Mat &frame,Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF) override;


    private:

        /* 报警事件信息 */
        AlarmEventInfo curAlarmEventInfo;
        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;

    };

} // vis

#endif //VIS_G3_SOFTWARE_VISALARMDECISION_MT_FACE_H
