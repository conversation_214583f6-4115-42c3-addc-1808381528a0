//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/7.
//

#ifndef VIS_G3_SOFTWARE_VISALARMDECISION_H
#define VIS_G3_SOFTWARE_VISALARMDECISION_H
#include "transit.h"
#include "DetectDataCallback.h"
#include "std_transit.h"


namespace vis {
    /**
     * 所有报警决策的基类
     */
    class VisAlarmDecision {
    public:

        /**
         * 初始化
         *
         * @param cameraId ： 当前对应的相机ID
         * @param classThreshold : 配置过来的算法识别阈值中的正式阈值（主要跟影子报警相关）
         * @param detectDataCallback ： 结果回调
         * @return ：0成功   其他：失败
         */
        int init(int cameraId, float classThreshold, DetectDataCallback *detectDataCallback);


        /* 当前的报警决策类型 */
        ALARM_DECISION_TYPE curAlarmDecisionType = ALARM_DECISION_TYPE_UNKNOW;

        /* 相机ID */
        int curCameraId = -1;
        /* 结果回调 */
        DetectDataCallback *curDetectDataCallback = nullptr;
        /* 这个镜头是否被bypassle */
        bool isbypass = false;
        /* 这个镜头是否被遮挡了 */
        bool cameraCover = false;

        /* 该报警决策对应的镜头类型 */
        CameraType curCameraType;

        /* 当前目标检测所使用的正式识别阈值  这里默认值直接用0了，在不需要影子的时候就只判断一次 */
        float curClassThreshold = 0;





        /**
         * 这个镜头是否被bypass（bypass状态下不响喇叭，其他照常）
         *
         * @return 是否处于bypass状态
         */
        bool isCameraBypass() const;

        /**
         * 设置镜头的bypass状态
         *
         * @param cameraBypass ： bypass状态
         */
        void setCameraBypass(bool cameraBypass);

        /**
         * 这个镜头是否被遮挡了
         *
         * @return 是否被遮挡
         */
        bool isCameraCover() const;

        /**
         * 设置镜头的遮挡状态
         *
         * @param cameraCover ： 是否被遮挡
         */
        void setCameraCover(bool cameraCover);

        /**
         * 是否初始化了
         *
         * @return 是否初始化了
         */
        bool isInited() const;

        /**
         * 检查下报警开关，看看这个镜头是否可以报警  （经过JOE哥哥确定废弃）
         *
         * @return 是否可以报警
         */
        bool checkCameraAlarmSwitch();


        /**
         * 解析 英国工程机械-BSD相机-目标检测识 别出来的结果的方法
         *
         * @param curObjectInfo ： 识别信息(需要进行报警检测的)
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param curObjectInfo_notCheck : 识别信息(不需要进行报警检测，直接输出出去的)
         * @param cameraCovered : 是否相机被遮挡
         * @param imgBrightness : 画面当前的亮度
         * @param isFullblack ： 画面是否全黑
         * @param detectTime : 识别一帧图像花费的时间（单位：ms）
         */
       virtual void parse_UK_BSD_OD(td::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,td::objectInfo_t &curObjectInfo_notCheck,bool cameraCovered,float imgBrightness = -1,bool isFullblack = false,uint16_t detectTime = 0xFFFF);

        /**
         * 解析 新加坡巴士-160度相机-语义分割 识别出来的结果的方法
         *
         * @param curObjectInfo ： 识别信息
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param detectTime : 识别一帧图像花费的时间（单位：ms）
         */
        virtual void parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF);

        /**
         * 解析 地铁DSM算法 识别出来的结果的方法
         * @param face_roi_info ： 地铁DSM算法 识别出来的结果
         * @param frame : 人脸的图片
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param detectTime : 识别一帧图像花费的时间（单位：ms）
         */
        virtual void parse_MT_DSM_OD(face_message &face_roi_info, cv::Mat &frame,Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF);

        /**
         * 解析 地铁手势算法 识别出来的结果的方法
         * @param objectInfo_ ： 地铁手势算法 识别出来的结果
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param detectTime : 识别一帧图像花费的时间（单位：ms）
         */
        virtual void parse_MT_GES_OD(gt::objectInfo_ &curObjectInfo,Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF);

        /**
         * 解析 新加坡MUV的DSM算法 识别出来的结果的方法
         * @param almuv_info ： 报警信息
         * @param state_of : 是否有人脸（1=有人脸，0=无）
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param cameraCovered : 是否相机被遮挡
         * @param imgBrightness : 画面当前的亮度
         * @param isFullblack ： 画面是否全黑
         * @param detectTime : 识别一帧图像花费的时间（单位：ms）
         */
        virtual void parse_MUV_DSM_OD(dsm_muv::DSMInfo &almuv_info, const int state_of, Vehicle_RealtimeStatus &vehicleRealtimeStatus, bool cameraCovered, float imgBrightness = -1, bool isFullblack = false,uint16_t detectTime = 0xFFFF);

        /**
         * 解析 DVR算法 识别出来的结果的方法
         *
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param cameraCovered : 是否相机被遮挡
         * @param imgBrightness : 画面当前的亮度
         * @param isFullblack ： 画面是否全黑
         * @param detectTime : 识别一帧图像花费的时间（单位：ms）
         */
        virtual void parse_DVR_DETECTINFO(Vehicle_RealtimeStatus &vehicleRealtimeStatus, bool cameraCovered, float imgBrightness = -1, bool isFullblack = false,uint16_t detectTime = 0xFFFF);

        /**
         * 获取下最终使用的报警区域的的点（包括了转换为降效后的点）
         *
         * @param alarmAreaPointList ： 正常的报警区域的点的数组
         * @param pointListLen ： 正常的报警区域的点的数组的长度
         *
         * @return 转换成功的点   -1=失败
         */
        int getUltimateAlarmAreaPointList(VISPoint *alarmAreaPointList, const int pointListLen);

        /**
         * 整理一下td的算法框（把参与报警决策和不参与报警决策的框放到一起，同时根据是否降效进行缩放）
         *
         * @param dest ： 存放整理好的数据的内存
         * @param alarmObjectInfos ： 参与报警决策的框
         * @param notCheckObjectInfos ： 不参与报警决策的框
         */
        void organizeObjectInfoToShow(td::objectInfo_t &dest, std::vector<td::object_t> &alarmObjectInfos, std::vector<td::object_t> &notCheckObjectInfos);


        /**
         * 解析 标准版的DSM算法 识别出来的结果的方法
         * @param stdDSMinfo ： 识别信息
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param cameraCovered : 是否相机被遮挡
         * @param imgBrightness : 画面当前的亮度
         * @param isFullblack ： 画面是否全黑
         * @param detectTime : 识别一帧图像花费的时间（单位：ms）
         */
        virtual void parse_STD_DSM_OD(dsm_std::DSMInfo &stdDSMinfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus, bool cameraCovered, float imgBrightness = -1, bool isFullblack = false,uint16_t detectTime = 0xFFFF);


    private:

        /* 是否初始化了 */
        bool inited = false;

        /**
         * 检查是否需要降低算法效果
         *
         * @param cameraId ： 镜头ID
         *
         * @return 是否需要降低算法效果
         */
        bool needReduceDetectionEffect(const int cameraId);

        /**
         * 方大框的大小和并进行移动
         *
         * @param objectInfos ： 原来的框的数组
         *
         * @return 操作了多少个框   -1=失败
         */
        int enlargeObjectRect(std::vector<td::object_t> &objectInfos);

    };

} // vis

#endif //VIS_G3_SOFTWARE_VISALARMDECISION_H
