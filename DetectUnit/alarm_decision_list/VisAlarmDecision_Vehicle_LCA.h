//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/24.
//

#ifndef VIS_G3_SOFTWARE_VISALARMDECISION_VEHICLE_LCA_H
#define VIS_G3_SOFTWARE_VISALARMDECISION_VEHICLE_LCA_H

#include "VisAlarmDecision.h"

namespace vis {
    /**
     * 车（区域）_并线辅助 的报警决策
     */
    class VisAlarmDecision_Vehicle_LCA : public VisAlarmDecision{
    public:
        VisAlarmDecision_Vehicle_LCA();

        /**
         * 解析 英国工程机械-BSD相机-目标检测识 别出来的结果的方法
         *
         * @param curObjectInfo ： 识别信息(需要进行报警检测的)
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param curObjectInfo_notCheck : 识别信息(不需要进行报警检测，直接输出出去的)
         * @param cameraCovered : 是否相机被遮挡
         * @param imgBrightness : 画面当前的亮度
         * @param isFullblack ： 画面是否全黑
         */
        void parse_UK_BSD_OD(td::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,td::objectInfo_t &curObjectInfo_notCheck,bool cameraCovered,float imgBrightness = -1,bool isFullblack = false,uint16_t detectTime = 0xFFFF) override;

        /**
         * 解析 新加坡巴士-160度相机-语义分割 识别出来的结果的方法
         *
         * @param curObjectInfo ： 识别信息
         * @param vehicleRealtimeStatus ：车辆实时状态
         */
        void parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF) override;


    private:
        /* 整理好的左边区域的报警事件信息 所有外设需要的报警信息都打包在里面了 */
        AlarmEventInfo alarmEventInfo_left;
        /* 整理好的右边区域的报警事件信息 所有外设需要的报警信息都打包在里面了 */
        AlarmEventInfo alarmEventInfo_right;
        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;
        /* 整理出来的这个报警决策需要传出去的框的列表 */
        std::vector<td::object_t> curAlarmDecisionObjects;


        /* 左边报警区域1的顶点 */
        VISPoint alarmAreaPointList_left_level1[6];
        /* 右边报警区域1的顶点 */
        VISPoint alarmAreaPointList_right_level1[6];
        /* 左边报警区域2的顶点 */
        VISPoint alarmAreaPointList_left_level2[6];
        /* 右边报警区域2的顶点 */
        VISPoint alarmAreaPointList_right_level2[6];

        /* 左边车辆报警区域1内是否有车 */
        bool hasVehicleInLeftAlarmAreaLevel1 = false;
        /* 左边车辆报警区域2内是否有车 */
        bool hasVehicleInLeftAlarmAreaLevel2 = false;
        /* 右边车辆报警区域1内是否有车 */
        bool hasVehicleInRightAlarmAreaLevel1 = false;
        /* 右边车辆报警区域2内是否有车 */
        bool hasVehicleInRightAlarmAreaLevel2 = false;


        /* 当前左边车辆报警区域1的车辆信息（在车辆报警区域1内距离镜头最近的车辆） */
        td::object_t curAlarmVehicleInfo_Left_level1;
        /* 当前左边车辆报警区域2的车辆信息（在车辆报警区域2内距离镜头最近的车辆） */
        td::object_t curAlarmVehicleInfo_Left_level2;
        /* 当前右边车辆报警区域1的车辆信息（在车辆报警区域1内距离镜头最近的车辆） */
        td::object_t curAlarmVehicleInfo_Right_level1;
        /* 当前右边车辆报警区域2的车辆信息（在车辆报警区域2内距离镜头最近的车辆） */
        td::object_t curAlarmVehicleInfo_Right_level2;

        /* 上次左边一级区域报警的行人ID */
        uint32_t lastAlaramId_left_level1 = 0xFFFFFFFF;
        /* 上次左边二级区域报警的行人ID */
        uint32_t lastAlaramId_left_level2 = 0xFFFFFFFF;
        /* 上次右边一级区域报警的行人ID */
        uint32_t lastAlaramId_right_level1 = 0xFFFFFFFF;
        /* 上次右边二级区域报警的行人ID */
        uint32_t lastAlaramId_right_level2 = 0xFFFFFFFF;


        /**
         * 获取报警区域
         *
         * @param vehicleRealtimeStatus : 当前车况
         */
        void getAlarmArea(Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 把vector格式的point列表转成数组
         *
         * @param pointsVector ： vector格式的point列表
         * @param pointArrays ： 用来存放结果的数组
         * @param len : 用来存放结果的数组的长度
         *
         * @return 0：成功  其他：失败
         */
        int pointVectorPointsToPointArrays(std::vector<VISPoint> pointsVector,VISPoint *pointArrays,const int len);

        /**
         * 判断车辆的框是否在一级报警区域内
         *
         * @param vehicleInfo ： 车辆的框的信息
         * @return 是否在报警区域内
         */
        bool isVehicleInAlarmAreaLevel1(td::object_ &vehicleInfo);

        /**
         * 判断车辆的框是否在二级报警区域内
         *
         * @param vehicleInfo ： 车辆的框的信息
         * @return 是否在报警区域内
         */
        bool isVehicleInAlarmAreaLevel2(td::object_ &vehicleInfo);

        /**
         * 判断是否有一级报警
         *
         * @param vehicleRealtimeStatus ： 车况
         */
        void parseEvent_level1(Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 判断是否有二级报警
         *
         * @param vehicleRealtimeStatus ： 车况
         */
        void parseEvent_level2(Vehicle_RealtimeStatus &vehicleRealtimeStatus);

    };

} // vis

#endif //VIS_G3_SOFTWARE_VISALARMDECISION_VEHICLE_LCA_H
