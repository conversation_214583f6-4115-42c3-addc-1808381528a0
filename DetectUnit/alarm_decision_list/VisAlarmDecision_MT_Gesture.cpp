//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/17.
//

#include "VisAlarmDecision_MT_Gesture.h"

namespace vis {

    VisAlarmDecision_MT_Gesture::VisAlarmDecision_MT_Gesture() {
        curAlarmDecisionType = ALARM_DECISION_TYPE_GESTURE_METRO;
        detectionResult.alarmDecisionType = curAlarmDecisionType;

    }

    void VisAlarmDecision_MT_Gesture::parse_MT_GES_OD(gt::objectInfo_ &curObjectInfo,
                                                      Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {
        detectionResult.curCameraType.copy(curCameraType);
        /* 设置全局的相机遮挡状态 */
        detectionResult.isCameraCover = false;
        /* 存一下正式的识别阈值 */
        detectionResult.classThreshold = curClassThreshold;
        /* 车速 */
        detectionResult.speed = vehicleRealtimeStatus.speed;
        /* 算法的耗时 */
        detectionResult.detectUseTime =  detectTime;
        /* 直接把识别信息发出去 */
        curDetectDataCallback->onGetObjectInfo_GES(curObjectInfo, detectionResult);

    }


} // vis