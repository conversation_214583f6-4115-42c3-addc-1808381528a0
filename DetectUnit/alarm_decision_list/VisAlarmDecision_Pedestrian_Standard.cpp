//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/21.
//

#include "VisAlarmDecision_Pedestrian_Standard.h"
#include "XuTimeUtil.h"

namespace vis {
    VisAlarmDecision_Pedestrian_Standard::VisAlarmDecision_Pedestrian_Standard() {
        curAlarmDecisionType = ALARM_DECISION_TYPE_PEDSTRIAN_TTC;
        detectionResult.alarmDecisionType = curAlarmDecisionType;
    }

    void VisAlarmDecision_Pedestrian_Standard::parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo,
                                                                                    Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {
        /* 是语义分割的，那么先把maskbuf复制过去 */
        detectionResult.maskBufWidth = DAS_MASK_WIDTH;
        detectionResult.maskBufHeight = DAS_MASK_HEIGHT;
        detectionResult.maskBufLen = sizeof(curObjectInfo.maskBuf);
        (void) memcpy(detectionResult.maskBuf, curObjectInfo.maskBuf, detectionResult.maskBufLen);
        detectionResult.curCameraType.copy(curCameraType);
        detectionResult.isReverse = vehicleRealtimeStatus.reverse;
        /* ADAS识别信息初始化一下 */
        detectionResult.adasDetectionInfo.reset();
        /* 设置一下全局的相机遮挡 */
        detectionResult.isCameraCover = curObjectInfo.alarmInfo.m_camera_covered_alarm;
        /* 存一下正式的识别阈值 */
        detectionResult.classThreshold = curClassThreshold;
        /* 先清理一下框 */
        curAlarmDecisionObjects.clear();
        /* 先重置下headway */
        curAlarmInfo.headwayTime = 999999.00;
        /* 算法的耗时 */
        detectionResult.detectUseTime =  detectTime;
        /* 塞一下报警决策类型进去 */
        detectionResult.alarmDecisionType = curAlarmDecisionType;
        detectionResult.speed = vehicleRealtimeStatus.speed;

        /* 遍历拿到的object信息，把人跟车的框分别拿出去解析 */
        if (!curObjectInfo.objects.empty()) {
            for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {

                /* 把行人挑出来，给行人信息的识别类去识别 */
                if ((strcmp(curObjectInfo.objects[i].label, "person") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "cyclist") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "motorcyclist") == 0)) {
                    /* 判断下是否是前方行人且headway比之前的小，是的话把信息存起来（取headway最小的那个） */
                    if (curObjectInfo.objects[i].mIsAheadObj &&
                        curObjectInfo.objects[i].mHeadway < curAlarmInfo.headwayTime) {
                        curAlarmInfo.ttcTime = curObjectInfo.objects[i].mTTC;
                        curAlarmInfo.headwayTime = curObjectInfo.objects[i].mHeadway;
                        curAlarmInfo.mYDistance = curObjectInfo.objects[i].mYDistance;

                        detectionResult.adasDetectionInfo.pedestrianCollisionTime = curObjectInfo.objects[i].mHeadway;
                        detectionResult.adasDetectionInfo.pedestrianYDistance = curObjectInfo.objects[i].mYDistance;
                    }
                    /* 是对应的框，直接添加进来 */
                    curAlarmDecisionObjects.push_back(curObjectInfo.objects[i]);
                }

            }
        }

//                /* 看看是否需要判断一下报警开关 */
//                if(checkCameraAlarmSwitch()) {
        /* 判断一下headway */
        checkPedestrianHeadway(curObjectInfo, vehicleRealtimeStatus);
//                }



        /* 清理掉旧的框  把整理过的框塞进去 */
        das::objectInfo_t curPedestrianObjectInfo = curObjectInfo;
        curPedestrianObjectInfo.objects.clear();
        curPedestrianObjectInfo.objects.insert(curPedestrianObjectInfo.objects.begin(), curAlarmDecisionObjects.begin(),
                                            curAlarmDecisionObjects.end());

        /* 把识别信息发送出去 */
        curDetectDataCallback->onGetObjectInfo_Adas(curPedestrianObjectInfo, detectionResult);
    }

    void VisAlarmDecision_Pedestrian_Standard::checkPedestrianHeadway(das::objectInfo_t &curObjectInfo,
                                                                      Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 先看下是否有headway的报警状态 */
        if (curObjectInfo.alarmInfo.m_fcw_pedestrian_alarm_level1) {
            /* 先保存下现在处于报警状态 */
            detectionResult.adasDetectionInfo.alarmStatus.hasPedestrian = true;
            /* 现在有报警，那么判断下之前是不是也有报警 */
            if (curAlarmInfo.hasHeadwayAlarm) {
                /* 之前也有headway报警，那么现在不用理 */
                ; //not to do

            } else {
                /* 之前没有headway报警，现在有，那么需要发一个开始headway报警的事件 */
                alarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_PEDESTRIAN_HMW;
                alarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_START;
                alarmEventInfo.adasAlarmInfo.headway = curAlarmInfo.headwayTime;
                /* 设置下当前的报警状态 */
                curAlarmInfo.hasHeadwayAlarm = true;
                alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType, EVENT_ADAS_PEDESTRIAN_HMW, alarmEventInfo);
                printf("send pdw start! \n");
            }
        } else {
            /* 先保存下现在处于安全状态 */
            detectionResult.adasDetectionInfo.alarmStatus.hasPedestrian = false;

            /* 现在没有报警，那么判断下之前是不是有报警 */
            if (curAlarmInfo.hasHeadwayAlarm) {
                /* 之前有headway报警，那么现在需要发送一个停止headway报警的事件 */
                alarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_PEDESTRIAN_HMW;
                alarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_STOP;
                alarmEventInfo.adasAlarmInfo.headway = curAlarmInfo.headwayTime;
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasHeadwayAlarm = false;
                alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType, EVENT_ADAS_PEDESTRIAN_HMW, alarmEventInfo);

                printf("send pdw stop! \n");

            } else {
                /* 之前也没有headway报警，那么现在不需要理 */
                ; //not to do
            }
        }


    }
} // vis