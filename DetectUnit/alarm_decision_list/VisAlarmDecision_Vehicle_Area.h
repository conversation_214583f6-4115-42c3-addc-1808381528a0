//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/12.
//

#ifndef VIS_G3_SOFTWARE_VISALARMDECISION_VEHICLE_AREA_H
#define VIS_G3_SOFTWARE_VISALARMDECISION_VEHICLE_AREA_H

#include "VisAlarmDecision.h"
namespace vis {
    /**
     * 车（区域）的报警决策
     */
    class VisAlarmDecision_Vehicle_Area : public VisAlarmDecision{

    public:
        VisAlarmDecision_Vehicle_Area();

        /**
         * 解析 英国工程机械-BSD相机-目标检测识 别出来的结果的方法
         *
         * @param curObjectInfo ： 识别信息(需要进行报警检测的)
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param curObjectInfo_notCheck : 识别信息(不需要进行报警检测，直接输出出去的)
         * @param cameraCovered : 是否相机被遮挡
         * @param imgBrightness : 画面当前的亮度
         * @param isFullblack ： 画面是否全黑
         */
        void parse_UK_BSD_OD(td::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,td::objectInfo_t &curObjectInfo_notCheck,bool cameraCovered,float imgBrightness = -1,bool isFullblack = false,uint16_t detectTime = 0xFFFF) override;

        /**
         * 解析 新加坡巴士-160度相机-语义分割 识别出来的结果的方法
         *
         * @param curObjectInfo ： 识别信息
         * @param vehicleRealtimeStatus ：车辆实时状态
         */
        void parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF) override;


        /**
         * 获取报警区域
         *
         * @param vehicleRealtimeStatus : 当前车况
         */
        void getAlarmArea(Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 判断车辆的框是否在一级报警区域内
         *
         * @param vehicleInfo ： 车辆的框的信息
         * @return 是否在报警区域内
         */
        bool isVehicleInAlarmAreaLevel1(td::object_ &vehicleInfo);

        /**
         * 判断车辆的框是否在二级报警区域内
         *
         * @param vehicleInfo ： 车辆的框的信息
         * @return 是否在报警区域内
         */
        bool isVehicleInAlarmAreaLevel2(td::object_ &vehicleInfo);


        /**
         * 把vector格式的point列表转成数组
         *
         * @param pointsVector ： vector格式的point列表
         * @param pointArrays ： 用来存放结果的数组
         * @param len : 用来存放结果的数组的长度
         *
         * @return 0：成功  其他：失败
         */
        int pointVectorPointsToPointArrays(std::vector<VISPoint> pointsVector,VISPoint *pointArrays,const int len);

        /**
         * 判断是否有一级报警
         *
         * @param curAlaramId ： 当前可能用于报警判断的车辆ID
         * @param curAlaramId ： 当前可能用于报警判断的车辆ID（影子）
         * @param vehicleRealtimeStatus ： 车况
         */
        void parseEvent_level1(uint32_t curAlaramId,uint32_t curAlaramId_shadow,Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 判断是否有二级报警
         *
         * @param curAlaramId ： 当前可能用于报警判断的车辆ID
         * @param curAlaramId ： 当前可能用于报警判断的车辆ID（影子）
         * @param vehicleRealtimeStatus ： 车况
         */
        void parseEvent_level2(uint32_t curAlaramId,uint32_t curAlaramId_shadow,Vehicle_RealtimeStatus &vehicleRealtimeStatus);


    private:

        /* 车辆报警区域1内是否有车 */
        bool hasVehicleInAlarmAreaLevel1 = false;
        /* 车辆报警区域2内是否有车 */
        bool hasVehicleInAlarmAreaLevel2 = false;
        /* 当前车辆报警区域1的车辆信息（在车辆报警区域1内距离镜头最近的车辆） */
        td::object_t curAlarmVehicleInfo_level1;
        /* 当前车辆报警区域2的车辆信息（在车辆报警区域2内距离镜头最近的车辆） */
        td::object_t curAlarmVehicleInfo_level2;
        /* 上次报警的车辆ID */
        uint32_t lastAlaramId_level1 = 0xFFFFFFFF;
        uint32_t lastAlaramId_level2 = 0xFFFFFFFF;
        /* 整理好的报警事件信息 所有外设需要的报警信息都打包在里面了 */
        AlarmEventInfo alarmEventInfo;
        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;
        /* 整理出来的这个报警决策需要传出去的框的列表 */
        std::vector<td::object_t> curAlarmDecisionObjects;
        /* 报警区域1的顶点 */
        VISPoint alarmAreaPointList_level1[6];
        /* 报警区域2的顶点 */
        VISPoint alarmAreaPointList_level2[6];







        /* 车辆报警区域1内是否有车_影子 */
        bool hasVehicleInAlarmAreaLevel1_shadow = false;
        /* 车辆报警区域2内是否有车_影子 */
        bool hasVehicleInAlarmAreaLevel2_shadow = false;
        /* 当前车辆报警区域1的车辆信息_影子（在车辆报警区域1内距离镜头最近的车辆） */
        td::object_t curAlarmVehicleInfo_level1_shadow;
        /* 当前车辆报警区域2的车辆信息_影子（在车辆报警区域2内距离镜头最近的车辆） */
        td::object_t curAlarmVehicleInfo_level2_shadow;
        /* 上次报警的车辆ID_影子 */
        uint32_t lastAlaramId_level1_shadow = 0xFFFFFFFF;
        uint32_t lastAlaramId_level2_shadow = 0xFFFFFFFF;
        /* 整理好的报警事件信息 所有外设需要的报警信息都打包在里面了_影子 */
        AlarmEventInfo alarmEventInfo_shadow;










    };

} // vis

#endif //VIS_G3_SOFTWARE_VISALARMDECISION_VEHICLE_AREA_H
