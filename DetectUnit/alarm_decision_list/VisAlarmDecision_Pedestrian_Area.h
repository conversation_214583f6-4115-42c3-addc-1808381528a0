//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/10.
//

#ifndef VIS_G3_SOFTWARE_VISALARMDECISION_PEDESTRIAN_AREA_H
#define VIS_G3_SOFTWARE_VISALARMDECISION_PEDESTRIAN_AREA_H

#include "VisAlarmDecision.h"
namespace vis {

    /**
     * 人（区域）的报警决策
     */
    class VisAlarmDecision_Pedestrian_Area : public VisAlarmDecision{
    public:
        VisAlarmDecision_Pedestrian_Area();

        /**
         * 解析 英国工程机械-BSD相机-目标检测识 别出来的结果的方法
         *
         * @param curObjectInfo ： 识别信息(需要进行报警检测的)
         * @param vehicleRealtimeStatus ：车辆实时状态
         * @param curObjectInfo_notCheck : 识别信息(不需要进行报警检测，直接输出出去的)
         * @param cameraCovered : 是否相机被遮挡
         * @param imgBrightness : 画面当前的亮度
         * @param isFullblack ： 画面是否全黑
         */
        void parse_UK_BSD_OD(td::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,td::objectInfo_t &curObjectInfo_notCheck,bool cameraCovered,float imgBrightness = -1,bool isFullblack = false,uint16_t detectTime = 0xFFFF) override;

        /**
         * 解析 新加坡巴士-160度相机-语义分割 识别出来的结果的方法
         *
         * @param curObjectInfo ： 识别信息
         * @param vehicleRealtimeStatus ：车辆实时状态
         */
        void parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF) override;





    private:

        /* 报警区域扩大1米的时候  在画面中应该扩大的像素点距离 */
        const float ALARM_EXIT_AREA_DIST = 1.35;

        /* 报警区域1内是否有人 */
        bool hasPedestrianInAlarmAreaLevel1 = false;
        /* 报警区域2内是否有人 */
        bool hasPedestrianInAlarmAreaLevel2 = false;

        /* 上一包结果中在报警区域1内的行人ID */
        std::vector<uint32_t> lastInAlarm1IdList;
        /* 上一包结果中在报警区域2内的行人ID */
        std::vector<uint32_t> lastInAlarm2IdList;

        /* 上次报警的行人ID */
        uint32_t lastAlaramId_level1 = -1;
        uint32_t lastAlaramId_level2 = -1;

        /* 上次报警的行人Y距离 */
        int lastAlaramYDistance_level1 = -1;
        int lastAlaramYDistance_level2 = -1;


        /* 报警事件信息 */
        AlarmEventInfo curAlarmEventInfo;
        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;
        /* 整理出来的这个报警决策需要传出去的框的列表 */
        std::vector<td::object_t> curAlarmDecisionObjects;

        /* 报警区域1的顶点 */
        VISPoint alarmAreaPointList_level1[6];
        /* 报警区域1的退出区域的顶点 */
        VISPoint alarmAreaPointList_level1_exit[6];

        /* 报警区域2的顶点 */
        VISPoint alarmAreaPointList_level2[6];
        /* 报警区域2的退出区域的顶点 */
        VISPoint alarmAreaPointList_level2_exit[6];




        /* 报警区域1内是否有人_影子 */
        bool hasPedestrianInAlarmAreaLevel1_shadow = false;
        /* 报警区域2内是否有人_影子 */
        bool hasPedestrianInAlarmAreaLevel2_shadow = false;

        /* 上一包结果中在报警区域1内的行人ID_影子 */
        std::vector<uint32_t> lastInAlarm1IdList_shadow;
        /* 上一包结果中在报警区域2内的行人ID_影子 */
        std::vector<uint32_t> lastInAlarm2IdList_shadow;

        /* 上次报警的行人ID_影子 */
        uint32_t lastAlaramId_level1_shadow = 0xFFFFFFFF;
        uint32_t lastAlaramId_level2_shadow = 0xFFFFFFFF;

        /* 上次报警的行人Y距离_影子 */
        int lastAlaramYDistance_level1_shadow = -1;
        int lastAlaramYDistance_level2_shadow = -1;

        /* 报警事件信息影子 */
        AlarmEventInfo curAlarmEventInfo_shadow;


        /* 是否可以识别行人 */
        bool canDetectPedestrian = true;


        /**
         * 获取报警区域
         *
         * @param vehicleRealtimeStatus : 当前车况
         */
        void getAlarmArea(Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 判断行人的框是否在一级报警区域内
         *
         * @param pedestrianInfo ： 行人的框的信息
         * @return 是否在报警区域内
         */
        bool isPedestrianInAlarmAreaLevel1(td::object_ &pedestrianInfo);

        /**
         * 判断行人的框是否在二级报警区域内
         *
         * @param pedestrianInfo ： 行人的框的信息
         * @return 是否在报警区域内
         */
        bool isPedestrianInAlarmAreaLevel2(td::object_ &pedestrianInfo);

        /**
         * 判断是否有一级报警
         *
         * @param curAlaramId ： 当前可能用于报警判断的行人ID
         * @param curPedestrianTrend ： 当前可能用于报警判断的行人趋势
         * @param curAlaramId ： 当前可能用于报警判断的行人ID(影子)
         * @param curPedestrianTrend ： 当前可能用于报警判断的行人趋势(影子)
         * @param vehicleRealtimeStatus ： 车况
         */
        void parseEvent_level1(const uint32_t curAlaramId, const uint32_t curPedestrianTrend,const uint32_t curAlaramId_shadow, const uint32_t curPedestrianTrend_shadow,Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 判断是否有二级报警
         *
         * @param curAlaramId ： 当前可能用于报警判断的行人ID
         * @param curPedestrianTrend ： 当前可能用于报警判断的行人趋势
         * @param curAlaramId ： 当前可能用于报警判断的行人ID(影子)
         * @param curPedestrianTrend ： 当前可能用于报警判断的行人趋势(影子)
         * @param vehicleRealtimeStatus ： 车况
         */
        void parseEvent_level2(const uint32_t curAlaramId, const uint32_t curPedestrianTrend,const uint32_t curAlaramId_shadow, const uint32_t curPedestrianTrend_shadow,Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 把vector格式的point列表转成数组
         *
         * @param pointsVector ： vector格式的point列表
         * @param pointArrays ： 用来存放结果的数组
         * @param len : 用来存放结果的数组的长度
         *
         * @return 0：成功  其他：失败
         */
        int pointVectorPointsToPointArrays(std::vector<VISPoint> pointsVector,VISPoint *pointArrays,const int len);

        /**
         * 判断一下行人是否在可行使区域内
         *
         * @param pedestrianInfo ： 行人的框信息
         * @param stationlineInfo ： 黄色实线的框信息
         * @param vehicleRealtimeStatus : 车况信息
         *
         * @return 是否在可行驶区域内
         */
        bool checkPedestrianInsideRoad(das::object_t pedestrianInfo,das::object_t stationlineInfo,Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         * 判断一下行人的headway是否低于阈值
         *
         * @return 是否低于阈值
         */
        bool checkHeadway(das::object_t pedestrianInfo);



        /**
         * 判断行人的框是否在一级报警区域内(新加坡测试版本)
         *
         * @param pedestrianInfo ： 行人的框的信息
         * @return 是否在报警区域内
         */
        bool isPedestrianInAlarmAreaLevel1_sgp(td::object_ &pedestrianInfo);

        /**
         * 判断行人的框是否在二级报警区域内(新加坡测试版本)
         *
         * @param pedestrianInfo ： 行人的框的信息
         * @return 是否在报警区域内
         */
        bool isPedestrianInAlarmAreaLevel2_sgp(td::object_ &pedestrianInfo);







    };

} // vis

#endif //VIS_G3_SOFTWARE_VISALARMDECISION_PEDESTRIAN_AREA_H
