//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/18.
//

#ifndef VIS_G3_SOFTWARE_VISALARMDECISION_VEHICLE_STANDARD_H
#define VIS_G3_SOFTWARE_VISALARMDECISION_VEHICLE_STANDARD_H
#include "VisAlarmDecision.h"
namespace vis {
    /**
     * 车辆（TTC)的报警决策
     */
    class VisAlarmDecision_Vehicle_Standard : public VisAlarmDecision{
    public:
        /* 车辆（TTC)的报警决策中的报警信息 */
        struct AlarmDecisionVehicleStandardAlarmInfo{
            /* 是否有TTC报警 */
            bool hasTTCAlarm = false;
            /* 对应的TTC报警时间 */
            float ttcTime = 999999.00;
            /* 是否有headway报警 */
            bool hasHeadwayAlarm = false;
            /* 对应的headway报警时间 */
            float headwayTime = 999999.00;

        };

        VisAlarmDecision_Vehicle_Standard();

        /**
        * 解析 新加坡巴士-160度相机-语义分割 识别出来的结果的方法
        *
        * @param curObjectInfo ： 识别信息
        * @param vehicleRealtimeStatus ：车辆实时状态
        */
        void parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF) override;

    private:
        /* 整理好的报警事件信息 所有外设需要的报警信息都打包在里面了 */
        AlarmEventInfo alarmEventInfo;
        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;
        /* 整理出来的这个报警决策需要传出去的框的列表 */
        std::vector<das::object_t> curAlarmDecisionObjects;

        /* 当前的TTC信息 */
        AlarmDecisionVehicleStandardAlarmInfo curAlarmInfo;

        /**
         *检查下是否有TTC报警
         *
         * @param curObjectInfo : 识别信息
         * @param vehicleRealtimeStatus ： 车况
         */
        void checkVehicleTTC(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         *检查下是否有headway报警
         *
         * @param curObjectInfo : 识别信息
         * @param vehicleRealtimeStatus ： 车况
         */
        void checkVehicleHeadway(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         *检查下是否有防溜车报警
         *
         * @param curObjectInfo : 识别信息
         * @param vehicleRealtimeStatus ： 车况
         */
        void checkVB(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         *检查下是否有前车启动
         *
         * @param curObjectInfo : 识别信息
         * @param vehicleRealtimeStatus ： 车况
         */
        void checkGo(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus);


    };

} // vis

#endif //VIS_G3_SOFTWARE_VISALARMDECISION_VEHICLE_STANDARD_H
