//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/18.
//

#include "VisAlarmDecision_Vehicle_Standard.h"
#include "XuTimeUtil.h"
#include "XuString.h"

namespace vis {
    VisAlarmDecision_Vehicle_Standard::VisAlarmDecision_Vehicle_Standard() {
        curAlarmDecisionType = ALARM_DECISION_TYPE_VEHICLE_TTC;
        detectionResult.alarmDecisionType = curAlarmDecisionType;
        detectionResult.curCameraType.copy(curCameraType);
    }

    void VisAlarmDecision_Vehicle_Standard::parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo,
                                                                                 Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {
        /* 是语义分割的，那么先把maskbuf复制过去 */
        detectionResult.maskBufWidth = DAS_MASK_WIDTH;
        detectionResult.maskBufHeight = DAS_MASK_HEIGHT;
        detectionResult.maskBufLen = sizeof(curObjectInfo.maskB<PERSON>);
        (void) memcpy(detectionResult.maskBuf,curObjectInfo.maskBuf,detectionResult.maskBufLen);
        detectionResult.curCameraType.copy(curCameraType);
        detectionResult.isReverse = vehicleRealtimeStatus.reverse;
        /* ADAS识别信息初始化一下 */
        detectionResult.adasDetectionInfo.reset();
        /* 设置一下全局的相机遮挡 */
        detectionResult.isCameraCover = curObjectInfo.alarmInfo.m_camera_covered_alarm;
        /* 存一下正式的识别阈值 */
        detectionResult.classThreshold = curClassThreshold;
        /* 先清理一下框 */
        curAlarmDecisionObjects.clear();
        /* 算法的耗时 */
        detectionResult.detectUseTime =  detectTime;
        /* 塞一下报警决策类型进去 */
        detectionResult.alarmDecisionType = curAlarmDecisionType;
        detectionResult.speed = vehicleRealtimeStatus.speed;
        /* 遍历拿到的object信息，把人跟车的框分别拿出去解析 */
        if (!curObjectInfo.objects.empty()) {
            for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {



//                printf("p1=(%d,%d)  p2=(%d,%d)   \n",curObjectInfo.objects[i].mLeft,curObjectInfo.objects[i].mTop,curObjectInfo.objects[i].mRight,curObjectInfo.objects[i].mBottom);//

                /* 把车辆挑出来，给车辆信息的识别类去识别 */
                if ((strcmp(curObjectInfo.objects[i].label, "car") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "bus") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "truck") == 0)) {


//                    printf("i=%d  lable=%s \n",i,XuString::getInstance().byteArrayToString(
//                            reinterpret_cast<const uint8_t *>(curObjectInfo.objects[i].label),
//                            sizeof(curObjectInfo.objects[i].label)).c_str());//

                    /* 先判断一下TTC和headway是不是负的，是的话就设置为99999 */
                    if(curObjectInfo.objects[i].mTTC < 0){
                        curObjectInfo.objects[i].mTTC = 99999.99;
                    }
                    if(curObjectInfo.objects[i].mHeadway < 0){
                        curObjectInfo.objects[i].mHeadway = 99999.99;
                    }


                    /* 判断下是否是前车，是的话把信息存起来(梁工说前车只有一台) */
                    if(curObjectInfo.objects[i].mIsAheadObj){
                        curAlarmInfo.ttcTime = curObjectInfo.objects[i].mTTC;
                        curAlarmInfo.headwayTime = curObjectInfo.objects[i].mHeadway;
                        detectionResult.adasDetectionInfo.vehicleCollisionTime = curObjectInfo.objects[i].mHeadway;
                        detectionResult.adasDetectionInfo.vehicleYDistance = curObjectInfo.objects[i].mYDistance;
                    }
                    /* 是对应的框，直接添加进来 */
                    curAlarmDecisionObjects.push_back(curObjectInfo.objects[i]);

//                    printf("i=%d label=%s  p1=(%d,%d)  p2=(%d,%d)   \n",i,curObjectInfo.objects[i].label,curObjectInfo.objects[i].mLeft,curObjectInfo.objects[i].mTop,curObjectInfo.objects[i].mRight,curObjectInfo.objects[i].mBottom);

                }
            }
        }

//        /* 看看是否需要判断一下报警开关 */
//        if(checkCameraAlarmSwitch()) {
            /* 判断一下TTC */
            checkVehicleTTC(curObjectInfo, vehicleRealtimeStatus);
            /* 判断一下headway */
            checkVehicleHeadway(curObjectInfo, vehicleRealtimeStatus);
            /* 判断一下虚拟保险杠 */
            checkVB(curObjectInfo, vehicleRealtimeStatus);
            /* 判断一下前车启动 */
            checkGo(curObjectInfo, vehicleRealtimeStatus);
//        }


        /* 清理掉旧的框  把整理过的框塞进去 */
        das::objectInfo_t curVehicleObjectInfo = curObjectInfo;
        curVehicleObjectInfo.objects.clear();
        curVehicleObjectInfo.objects.insert(curVehicleObjectInfo.objects.begin(), curAlarmDecisionObjects.begin(),
                                            curAlarmDecisionObjects.end());
        /* 把识别信息发送出去 */
        curDetectDataCallback->onGetObjectInfo_Adas(curVehicleObjectInfo, detectionResult);

    }

    void VisAlarmDecision_Vehicle_Standard::checkVehicleTTC(das::objectInfo_t &curObjectInfo,
                                                            Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 先看下是否有TTC的报警状态 */
        if(curObjectInfo.alarmInfo.m_fcw_vehicle_ttc_alarm_level1){
            /* 先保存下现在处于报警状态 */
            detectionResult.adasDetectionInfo.alarmStatus.hasVehicleAlarm = true;
           /* 现在有报警，那么判断下之前是不是也有报警 */
           if(curAlarmInfo.hasTTCAlarm){
               /* 之前也有TTC报警，那么现在不用理 */
               ; //not to do

           }else{
               /* 之前没有TTC报警，现在有，那么需要发一个开始TTC报警的事件 */
               alarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_VEHICLE_TTC;
               alarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_START;
               alarmEventInfo.adasAlarmInfo.ttc = curAlarmInfo.ttcTime;
               alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
               curDetectDataCallback->onGetDetectionEvent(curCameraType,EVENT_ADAS_VEHICLE_TTC,alarmEventInfo);
               /* 重置下当前的报警状态 */
               curAlarmInfo.hasTTCAlarm = true;
           }
        }else{
            /* 先保存下现在处于安全状态 */
            detectionResult.adasDetectionInfo.alarmStatus.hasVehicleAlarm = false;

            /* 现在没有报警，那么判断下之前是不是有报警 */
            if(curAlarmInfo.hasTTCAlarm){
                /* 之前有TTC报警，那么现在需要发送一个停止TTC报警的事件 */
                alarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_VEHICLE_TTC;
                alarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_STOP;
                alarmEventInfo.adasAlarmInfo.ttc = curAlarmInfo.ttcTime;
                alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType,EVENT_ADAS_VEHICLE_TTC,alarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasTTCAlarm = false;

            }else{
                /* 之前也没有TTC报警，那么现在不需要理 */
                ; //not to do
            }
        }


    }

    void VisAlarmDecision_Vehicle_Standard::checkVehicleHeadway(das::objectInfo_t &curObjectInfo,
                                                                Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 先看下是否有headway的报警状态 */
        if(curObjectInfo.alarmInfo.m_fcw_vehicle_headway_alarm_level1){
            /* 先保存下现在处于报警状态 */
            detectionResult.adasDetectionInfo.alarmStatus.hasVehicleAlarm = true;
            /* 现在有报警，那么判断下之前是不是也有报警 */
            if(curAlarmInfo.hasHeadwayAlarm){
                /* 之前也有headway报警，那么现在不用理 */
                ; //not to do

            }else{
                /* 之前没有headway报警，现在有，那么需要发一个开始headway报警的事件 */
                alarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_VEHICLE_HMW;
                alarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_START;
                alarmEventInfo.adasAlarmInfo.headway = curAlarmInfo.headwayTime;
                alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType,EVENT_ADAS_VEHICLE_HMW,alarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasHeadwayAlarm = true;
            }
        }else{
            /* 先保存下现在处于安全状态 */
            detectionResult.adasDetectionInfo.alarmStatus.hasVehicleAlarm = false;

            /* 现在没有报警，那么判断下之前是不是有报警 */
            if(curAlarmInfo.hasHeadwayAlarm){
                /* 之前有headway报警，那么现在需要发送一个停止headway报警的事件 */
                alarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_VEHICLE_HMW;
                alarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_STOP;
                alarmEventInfo.adasAlarmInfo.headway = curAlarmInfo.headwayTime;
                alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType,EVENT_ADAS_VEHICLE_HMW,alarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasHeadwayAlarm = false;

            }else{
                /* 之前也没有headway报警，那么现在不需要理 */
                ; //not to do
            }
        }


    }

    void VisAlarmDecision_Vehicle_Standard::checkVB(das::objectInfo_t &curObjectInfo,
                                                    Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 先看下是否有VB的报警状态 */
        if(curObjectInfo.alarmInfo.m_fcw_virtual_bumper_alarm){
            detectionResult.adasDetectionInfo.alarmStatus.hasVehicleAlarm = true;
            alarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_VEHICLE_VB;
            alarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_UNAVAILABLE;
            alarmEventInfo.adasAlarmInfo.headway = curAlarmInfo.headwayTime;
            alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
            curDetectDataCallback->onGetDetectionEvent(curCameraType,EVENT_ADAS_VEHICLE_VB,alarmEventInfo);
        }

    }

    void VisAlarmDecision_Vehicle_Standard::checkGo(das::objectInfo_t &curObjectInfo,
                                                    Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 先看下是否有Go的报警状态 */
        if(curObjectInfo.alarmInfo.m_fcw_front_vehicle_moving_alarm){
            detectionResult.adasDetectionInfo.alarmStatus.hasVehicleAlarm = true;
            alarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_VEHICLE_GO;
            alarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_UNAVAILABLE;
            alarmEventInfo.adasAlarmInfo.headway = curAlarmInfo.headwayTime;
            alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
            curDetectDataCallback->onGetDetectionEvent(curCameraType,EVENT_ADAS_VEHICLE_GO,alarmEventInfo);
        }

    }

} // vis