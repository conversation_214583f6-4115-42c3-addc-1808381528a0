//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/24.
//

#include "VisAlarmDecision_Vehicle_LCA.h"
#include "XuTimeUtil.h"

namespace vis {
    VisAlarmDecision_Vehicle_LCA::VisAlarmDecision_Vehicle_LCA() {
        curAlarmDecisionType = ALARM_DECISION_TYPE_LANE_CHANGE_ASSISTANCE;
        detectionResult.alarmDecisionType = curAlarmDecisionType;
    }

    void VisAlarmDecision_Vehicle_LCA::parse_UK_BSD_OD(td::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,td::objectInfo_t &curObjectInfo_notCheck,bool cameraCovered,float imgBrightness, bool isFullblack,uint16_t detectTime) {
        hasVehicleInLeftAlarmAreaLevel1 = false;
        hasVehicleInLeftAlarmAreaLevel2 = false;
        hasVehicleInRightAlarmAreaLevel1 = false;
        hasVehicleInRightAlarmAreaLevel2 = false;
        curAlarmVehicleInfo_Left_level1.mYDistance = 999999;
        curAlarmVehicleInfo_Left_level2.mYDistance = 999999;
        curAlarmVehicleInfo_Right_level1.mYDistance = 999999;
        curAlarmVehicleInfo_Right_level2.mYDistance = 999999;
        /* 由于结构体里面没有裸指针，故直接用={}重置就好了 */
        alarmEventInfo_left = {};
        alarmEventInfo_right = {};
        detectionResult.bsdDetectionInfo.reset();
        detectionResult.curCameraType.copy(curCameraType);
        /* 看看是不是有遮挡 */
        cameraCover = cameraCovered;
        detectionResult.isCameraCover = cameraCover;
        detectionResult.cameraFullBlack = isFullblack;
        isbypass = false;

        detectionResult.bsdDetectionInfo.cameraCoverStatus.forward = cameraCover;
        detectionResult.bsdDetectionInfo.bypassStatus.forward = isbypass;
        detectionResult.speed = vehicleRealtimeStatus.speed;
        detectionResult.isReverse = vehicleRealtimeStatus.reverse;
        /* 存一下正式的识别阈值 */
        detectionResult.classThreshold = curClassThreshold;
        /* 先清理一下框 */
        curAlarmDecisionObjects.clear();
        /* 算法的耗时 */
        detectionResult.detectUseTime =  detectTime;
        /* 遍历拿到的object信息，把人跟车的框分别拿出去解析 */
        if (!curObjectInfo.objects.empty()) {

            /* 获取一下报警区域 */
            getAlarmArea(vehicleRealtimeStatus);


            for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {
                /* 把车辆挑出来，给行人信息的识别类去识别 */
                if ((strcmp(curObjectInfo.objects[i].label, "car") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "bus") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "truck") == 0)) {

                    /* 先把框的点整理  避免超出屏幕的框 */
                    curObjectInfo.objects[i].mLeft = (curObjectInfo.objects[i].mLeft < 0) ? 0
                                                                                          : ((curObjectInfo.objects[i].mLeft >
                                                                                              1280) ? 1280
                                                                                                    : curObjectInfo.objects[i].mLeft);
                    curObjectInfo.objects[i].mTop = (curObjectInfo.objects[i].mTop < 0) ? 0
                                                                                        : ((curObjectInfo.objects[i].mTop >
                                                                                            720) ? 720
                                                                                                 : curObjectInfo.objects[i].mTop);
                    curObjectInfo.objects[i].mRight = (curObjectInfo.objects[i].mRight < 0) ? 0
                                                                                            : ((curObjectInfo.objects[i].mRight >
                                                                                                1280) ? 1280
                                                                                                      : curObjectInfo.objects[i].mRight);
                    curObjectInfo.objects[i].mBottom = (curObjectInfo.objects[i].mBottom < 0) ? 0
                                                                                              : ((curObjectInfo.objects[i].mBottom >
                                                                                                  720) ? 720

                                                                                                       : curObjectInfo.objects[i].mBottom);

                    /* 判断下是否在二级区域内 */
                    if (isVehicleInAlarmAreaLevel2(curObjectInfo.objects[i])) {
                        /* 把label改一下 */
                        std::string newlabel;
                        newlabel.append("A2-");
                        newlabel.append(curObjectInfo.objects[i].label);
                        memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                        memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());
                    }

                    /* 判断下是否在一级区域内 */
                    if (isVehicleInAlarmAreaLevel1(curObjectInfo.objects[i])) {
                        /* 把label改一下 */
                        std::string newlabel;
                        newlabel.append("A1-");
                        newlabel.append(curObjectInfo.objects[i].label);
                        memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                        memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());
                    }
                    /* 是对应的框，直接添加进来 */
                    curAlarmDecisionObjects.push_back(curObjectInfo.objects[i]);
                }
                /* motorcyclist需要特殊处理 */

            }
        }
        /* 判断是否在需要报警的车速区间内,在的话需要判断报警 */
        if (vehicleRealtimeStatus.speed >=
            G3_Configuration::getInstance().getSbstSidewardVehicleMinAlramSpeedLevel1() &&
            vehicleRealtimeStatus.speed <=
            G3_Configuration::getInstance().getSbstSidewardVehicleMaxAlramSpeedLevel1()) {
            /* 看看是否需要判断一下报警开关 */
//            if(checkCameraAlarmSwitch()) {
                parseEvent_level1(vehicleRealtimeStatus);
//            }

        }


        /* 判断是否在需要报警的车速区间内,在的话需要判断报警 */
        if (vehicleRealtimeStatus.speed >=
            G3_Configuration::getInstance().getSbstSidewardVehicleMinAlramSpeedLevel2() &&
            vehicleRealtimeStatus.speed <=
            G3_Configuration::getInstance().getSbstSidewardVehicleMaxAlramSpeedLevel2()) {
            /* 看看是否需要判断一下报警开关 */
//            if(checkCameraAlarmSwitch()) {
                parseEvent_level2(vehicleRealtimeStatus);
//            }

        }
        detectionResult.alarmDecisionType = curAlarmDecisionType;
        detectionResult.speed = vehicleRealtimeStatus.speed;
        /* 清理掉旧的框  把整理过的框塞进去 */
        td::objectInfo_t curVehicleObjectInfo = curObjectInfo;
        curVehicleObjectInfo.objects.clear();
        /* 先插入需要检测的框 */
        curVehicleObjectInfo.objects.insert(curVehicleObjectInfo.objects.begin(), curAlarmDecisionObjects.begin(),
                                            curAlarmDecisionObjects.end());
        /* 再插入不需要检测的框 */
        curVehicleObjectInfo.objects.insert(curVehicleObjectInfo.objects.end(), curObjectInfo_notCheck.objects.begin(),
                                            curObjectInfo_notCheck.objects.end());
        /* 把识别信息发送出去 */
        curDetectDataCallback->onGetObjectInfo_BSD(curVehicleObjectInfo, detectionResult);
    }

    void VisAlarmDecision_Vehicle_LCA::getAlarmArea(Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 设置报警区域1的点 */
        pointVectorPointsToPointArrays(
                G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Left(),
                alarmAreaPointList_left_level1,
                6);
        pointVectorPointsToPointArrays(
                G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel1Right(),
                alarmAreaPointList_right_level1,
                6);

        /* 设置报警区域2的点 */
        pointVectorPointsToPointArrays(
                G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Left(),
                alarmAreaPointList_left_level2,
                6);

        pointVectorPointsToPointArrays(
                G3_Configuration::getInstance().getSbstVehicleAlramAreaLevel2Right(),
                alarmAreaPointList_right_level2,
                6);

        /* 获取下最终使用的报警区域的的点（是不是降效都在里面实现了） */
        getUltimateAlarmAreaPointList(alarmAreaPointList_left_level1, 6);
        getUltimateAlarmAreaPointList(alarmAreaPointList_left_level2, 6);
        getUltimateAlarmAreaPointList(alarmAreaPointList_right_level1, 6);
        getUltimateAlarmAreaPointList(alarmAreaPointList_right_level2, 6);

    }

    int VisAlarmDecision_Vehicle_LCA::pointVectorPointsToPointArrays(std::vector<VISPoint> pointsVector,
                                                                     VISPoint *pointArrays, const int len) {
        int ret = -1;
        if (pointsVector.size() == static_cast<std::size_t>(len)) {
            for (std::size_t i = 0; i < pointsVector.size(); i++) {
                pointArrays[i].x = pointsVector[i].x;
                pointArrays[i].y = pointsVector[i].y;
            }
            ret = 0;
        } else { ; // not to do
        }
        return ret;
    }

    bool VisAlarmDecision_Vehicle_LCA::isVehicleInAlarmAreaLevel1(td::object_ &vehicleInfo) {



        VISPoint vehiclePOints[4];



        vehiclePOints[0].x = vehicleInfo.mLeft;
        vehiclePOints[0].y = vehicleInfo.mTop;

        vehiclePOints[1].x = vehicleInfo.mRight;
        vehiclePOints[1].y = vehicleInfo.mTop;

        vehiclePOints[2].x = vehicleInfo.mRight;
        vehiclePOints[2].y = vehicleInfo.mBottom;

        vehiclePOints[3].x = vehicleInfo.mLeft;
        vehiclePOints[3].y = vehicleInfo.mBottom;

        /* 判断下是否在左边的一级报警区域内 */
        bool isInLeft = false;
        if (alarmAreaPointList_left_level1[0].x != 0 || alarmAreaPointList_left_level1[0].y != 0 ||
                alarmAreaPointList_left_level1[1].x != 0 || alarmAreaPointList_left_level1[1].y != 0) {

            isInLeft = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(vehiclePOints, 4,
                                                                                                 alarmAreaPointList_left_level1,
                                                                                                 6);
            /* 如果在左边的一级报警区域内那么看看是否需要更新信息 */
            if(isInLeft){
                /* 记录下当前有在左边一级报警区域的车 */
                hasVehicleInLeftAlarmAreaLevel1 = true;
                /* 离得更近的话就用这个离得近的 */
                if (curAlarmVehicleInfo_Left_level1.mYDistance >= vehicleInfo.mYDistance) {
                    alarmEventInfo_left.bsdAlarmInfo.yDistance = vehicleInfo.mYDistance;
                    alarmEventInfo_left.bsdAlarmInfo.xDistance = vehicleInfo.mXDistance;
                    alarmEventInfo_left.bsdAlarmInfo.mRealWidth = vehicleInfo.mRealWidth;
                    alarmEventInfo_left.bsdAlarmInfo.mRealHeight = vehicleInfo.mRealHeight;
                    curAlarmVehicleInfo_Left_level1 = vehicleInfo;
                }
            }


        }





        /* 判断下是否在右边的报警区域内 */
        bool isInRight = false;
        if (alarmAreaPointList_right_level1[0].x != 0 || alarmAreaPointList_right_level1[0].y != 0 ||
                alarmAreaPointList_right_level1[1].x != 0 || alarmAreaPointList_right_level1[1].y != 0) {

            isInRight = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(vehiclePOints, 4,
                                                                                                  alarmAreaPointList_right_level1,
                                                                                                  6);
            /* 如果在右边的一级报警区域内那么看看是否需要更新信息 */
            if(isInRight){
                /* 记录下当前有在右边一级报警区域的车 */
                hasVehicleInRightAlarmAreaLevel1 = true;
                /* 离得更近的话就用这个离得近的 */
                if (curAlarmVehicleInfo_Right_level1.mYDistance >= vehicleInfo.mYDistance) {
                    alarmEventInfo_right.bsdAlarmInfo.yDistance = vehicleInfo.mYDistance;
                    alarmEventInfo_right.bsdAlarmInfo.xDistance = vehicleInfo.mXDistance;
                    alarmEventInfo_right.bsdAlarmInfo.mRealWidth = vehicleInfo.mRealWidth;
                    alarmEventInfo_right.bsdAlarmInfo.mRealHeight = vehicleInfo.mRealHeight;
                    curAlarmVehicleInfo_Right_level1 = vehicleInfo;
                }
            }
        }



        return isInLeft | isInRight;
    }

    bool VisAlarmDecision_Vehicle_LCA::isVehicleInAlarmAreaLevel2(td::object_ &vehicleInfo) {

        VISPoint vehiclePOints[4];



        vehiclePOints[0].x = vehicleInfo.mLeft;
        vehiclePOints[0].y = vehicleInfo.mTop;

        vehiclePOints[1].x = vehicleInfo.mRight;
        vehiclePOints[1].y = vehicleInfo.mTop;

        vehiclePOints[2].x = vehicleInfo.mRight;
        vehiclePOints[2].y = vehicleInfo.mBottom;

        vehiclePOints[3].x = vehicleInfo.mLeft;
        vehiclePOints[3].y = vehicleInfo.mBottom;



        /* 判断下是否在左边的报警区域内 */
        bool isInLeft = false;
        /* 判断下左边二级区域是否有设置（前面两个点不为0）  有设置才需要去判断 */
        if (alarmAreaPointList_left_level2[0].x != 0 || alarmAreaPointList_left_level2[0].y != 0 ||
            alarmAreaPointList_left_level2[1].x != 0 || alarmAreaPointList_left_level2[1].y != 0) {

            isInLeft = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(vehiclePOints, 4,
                                                                                                 alarmAreaPointList_left_level2,
                                                                                                 6);

            /* 如果在左边的二级报警区域内那么看看是否需要更新信息 */
            if(isInLeft){
                /* 记录下当前有在左边二级报警区域的车 */
                hasVehicleInLeftAlarmAreaLevel2 = true;
                /* 离得更近的话就用这个离得近的 */
                if (curAlarmVehicleInfo_Left_level2.mYDistance >= vehicleInfo.mYDistance) {
                    alarmEventInfo_left.bsdAlarmInfo.yDistance = vehicleInfo.mYDistance;
                    alarmEventInfo_left.bsdAlarmInfo.xDistance = vehicleInfo.mXDistance;
                    alarmEventInfo_left.bsdAlarmInfo.mRealWidth = vehicleInfo.mRealWidth;
                    alarmEventInfo_left.bsdAlarmInfo.mRealHeight = vehicleInfo.mRealHeight;
                    curAlarmVehicleInfo_Left_level2 = vehicleInfo;
                }
            }

        }




        /* 判断下是否在右边的报警区域内 */
        bool isInRight = false;
        /* 判断下右边二级区域是否有设置（前面两个点不为0）  有设置才需要去判断 */
        if (alarmAreaPointList_right_level2[0].x != 0 || alarmAreaPointList_right_level2[0].y != 0 ||
                alarmAreaPointList_right_level2[1].x != 0 || alarmAreaPointList_right_level2[1].y != 0) {

            isInRight = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(vehiclePOints, 4,
                                                                                                  alarmAreaPointList_right_level2,
                                                                                                  6);
            /* 如果在右边二级报警区域内那么看看是否需要更新信息 */
            if(isInRight){
                /* 记录下当前有在右边二级报警区域的车 */
                hasVehicleInRightAlarmAreaLevel2 = true;
                /* 离得更近的话就用这个离得近的 */
                if (curAlarmVehicleInfo_Right_level2.mYDistance >= vehicleInfo.mYDistance) {
                    alarmEventInfo_right.bsdAlarmInfo.yDistance = vehicleInfo.mYDistance;
                    alarmEventInfo_right.bsdAlarmInfo.xDistance = vehicleInfo.mXDistance;
                    alarmEventInfo_right.bsdAlarmInfo.mRealWidth = vehicleInfo.mRealWidth;
                    alarmEventInfo_right.bsdAlarmInfo.mRealHeight = vehicleInfo.mRealHeight;
                    curAlarmVehicleInfo_Right_level2 = vehicleInfo;
                }
            }


        }



        return isInLeft | isInRight;
    }

    void VisAlarmDecision_Vehicle_LCA::parseEvent_level1(Vehicle_RealtimeStatus &vehicleRealtimeStatus) {

        /* 先检查左边的 */
        if (hasVehicleInLeftAlarmAreaLevel1) {
            alarmEventInfo_left.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1;
            detectionResult.bsdDetectionInfo.vehicleStatus.left_level1 = true;
                /* 看看是否需要启动同ID过滤   */
                if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel1() == 0 &&
                        lastAlaramId_left_level1 == curAlarmVehicleInfo_Left_level1.mId) {
                    printf("lastAlaramId_left_level1=%d    curAlaramId=%d   \n", lastAlaramId_left_level1, curAlarmVehicleInfo_Left_level1.mId);
                    return;
                }
                /* 看看是否需要判断车门信号过滤 */
                if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                    (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                    printf("Doors is opened,did not alarm! \n");
                    return;
                }

                lastAlaramId_left_level1 = curAlarmVehicleInfo_Left_level1.mId;
                alarmEventInfo_left.bsdAlarmInfo.alarmVehicleId = curAlarmVehicleInfo_Left_level1.mId;
                alarmEventInfo_left.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType, alarmEventInfo_left.bsdAlarmInfo.eventCode, alarmEventInfo_left);
        }else{
            lastAlaramId_left_level1 = -1;
        }

        /* 再检查右边的 */
        if (hasVehicleInRightAlarmAreaLevel1) {
            alarmEventInfo_right.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
            detectionResult.bsdDetectionInfo.vehicleStatus.right_level1 = true;
            /* 看看是否需要启动同ID过滤   */
            if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel1() == 0 &&
                lastAlaramId_right_level1 == curAlarmVehicleInfo_Right_level1.mId) {
                printf("lastAlaramId_right_level1=%d    curAlaramId=%d   \n", lastAlaramId_right_level1, curAlarmVehicleInfo_Right_level1.mId);
                return;
            }
            /* 看看是否需要判断车门信号过滤 */
            if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                printf("Doors is opened,did not alarm! \n");
                return;
            }

            lastAlaramId_right_level1 = curAlarmVehicleInfo_Right_level1.mId;
            alarmEventInfo_right.bsdAlarmInfo.alarmVehicleId = curAlarmVehicleInfo_Right_level1.mId;
            alarmEventInfo_right.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
            curDetectDataCallback->onGetDetectionEvent(curCameraType, alarmEventInfo_right.bsdAlarmInfo.eventCode, alarmEventInfo_right);
        }else{
            lastAlaramId_right_level1 = -1;
        }


    }

    void VisAlarmDecision_Vehicle_LCA::parseEvent_level2(Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 先检查左边的 */
        if (hasVehicleInLeftAlarmAreaLevel2) {
            alarmEventInfo_left.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2;
            detectionResult.bsdDetectionInfo.vehicleStatus.left_level2 = true;
            /* 看看是否需要启动同ID过滤   */
            if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel2() == 0 &&
                lastAlaramId_left_level2 == curAlarmVehicleInfo_Left_level2.mId) {
                printf("lastAlaramId_left_level2=%d    curAlaramId=%d   \n", lastAlaramId_left_level2, curAlarmVehicleInfo_Left_level2.mId);
                return;
            }
            /* 看看是否需要判断车门信号过滤 */
            if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                printf("Doors is opened,did not alarm! \n");
                return;
            }

            lastAlaramId_left_level2 = curAlarmVehicleInfo_Left_level2.mId;
            alarmEventInfo_left.bsdAlarmInfo.alarmVehicleId = curAlarmVehicleInfo_Left_level2.mId;
            alarmEventInfo_left.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
            curDetectDataCallback->onGetDetectionEvent(curCameraType, alarmEventInfo_left.bsdAlarmInfo.eventCode, alarmEventInfo_left);
        }else{
            lastAlaramId_left_level2 = -1;
        }

        /* 再检查右边的 */
        if (hasVehicleInRightAlarmAreaLevel2) {
            alarmEventInfo_right.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
            detectionResult.bsdDetectionInfo.vehicleStatus.right_level2 = true;
            /* 看看是否需要启动同ID过滤   */
            if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel2() == 0 &&
                lastAlaramId_right_level2 == curAlarmVehicleInfo_Right_level2.mId) {
                printf("lastAlaramId_right_level2=%d    curAlaramId=%d   \n", lastAlaramId_right_level2, curAlarmVehicleInfo_Right_level2.mId);
                return;
            }
            /* 看看是否需要判断车门信号过滤 */
            if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                printf("Doors is opened,did not alarm! \n");
                return;
            }

            lastAlaramId_right_level2 = curAlarmVehicleInfo_Right_level2.mId;
            alarmEventInfo_right.bsdAlarmInfo.alarmVehicleId = curAlarmVehicleInfo_Right_level2.mId;
            alarmEventInfo_right.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
            curDetectDataCallback->onGetDetectionEvent(curCameraType, alarmEventInfo_right.bsdAlarmInfo.eventCode, alarmEventInfo_right);
        }else{
            lastAlaramId_right_level2 = -1;
        }
    }

    void VisAlarmDecision_Vehicle_LCA::parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo,
                                                                            Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {
        /* 是语义分割的，那么先把maskbuf复制过去 */
        detectionResult.maskBufWidth = DAS_MASK_WIDTH;
        detectionResult.maskBufHeight = DAS_MASK_HEIGHT;
        detectionResult.maskBufLen = sizeof(curObjectInfo.maskBuf);
        (void) memcpy(detectionResult.maskBuf,curObjectInfo.maskBuf,detectionResult.maskBufLen);

        /* 先定义一个td下的objectinfo，用来后续调用parse_UK_BSD_OD进行是否在报警区域内的检测的 */
        td::objectInfo_t tdbjectInfo_check;
        /* 先定义一个td下的objectinfo，用来后续调用parse_UK_BSD_OD，这不需要进行检测，只是输出框 */
        td::objectInfo_t tdbjectInfo_notCheck;
        /* 遍历拿到的object信息，把车辆拿出去解析 */
        if (!curObjectInfo.objects.empty()) {
            /* 先把报警信息复制过去 */
            memcpy(&tdbjectInfo_check.alarmInfo, &curObjectInfo.alarmInfo, sizeof(curObjectInfo.alarmInfo));
            /* 再把故障信息复制过去 */
            memcpy(&tdbjectInfo_check.faultInfo, &curObjectInfo.faultInfo, sizeof(curObjectInfo.faultInfo));

            /* 再把Object信息给复制过去 */
            for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {
                /* 把车辆挑出来，给车辆信息的识别类去识别 */
                if ((strcmp(curObjectInfo.objects[i].label, "car") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "bus") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "truck") == 0)) {
                    /* 判断下是否需要在可行驶区域内的车辆才报警  如果是的话  只有才可行驶区域的车辆才会送去报警决策 */
                    if (G3_Configuration::getInstance().getVehicleAlarmNeedTravelableArea() == 1) {
                        /* 只有在可行驶区域内的才可以去到报警决策 */
                        if (curObjectInfo.objects[i].mIsInsideRoad) {
                            /* 转换一下 */
                            td::object_t objectTemp;
                            memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                                   sizeof(curObjectInfo.objects[i].label));
                            objectTemp.mId = curObjectInfo.objects[i].mId;
                            objectTemp.mTop = curObjectInfo.objects[i].mTop;
                            objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                            objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                            objectTemp.mRight = curObjectInfo.objects[i].mRight;
                            objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                            objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                            objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                            objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                            objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                            objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                            objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                            objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                            objectTemp.mScore = curObjectInfo.objects[i].mScore;
                            /* 最大跟最小得分都设置成跟得分一样 */
                            objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                            objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                            /* 添加进去 */
                            tdbjectInfo_check.objects.push_back(objectTemp);
                        } else {
                            /* 不在可行驶区域内的需要放在另一个地方，该地方不要进行报警判断，但是需要直接输出出去 */
                            /* 转换一下 */
                            td::object_t objectTemp;
                            memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                                   sizeof(curObjectInfo.objects[i].label));
                            objectTemp.mId = curObjectInfo.objects[i].mId;
                            objectTemp.mTop = curObjectInfo.objects[i].mTop;
                            objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                            objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                            objectTemp.mRight = curObjectInfo.objects[i].mRight;
                            objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                            objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                            objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                            objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                            objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                            objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                            objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                            objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                            objectTemp.mScore = curObjectInfo.objects[i].mScore;
                            /* 最大跟最小得分都设置成跟得分一样 */
                            objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                            objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                            /* 添加进去 */
                            tdbjectInfo_notCheck.objects.push_back(objectTemp);
                        }
                    } else {
                        /* 转换一下 */
                        td::object_t objectTemp;
                        memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                               sizeof(curObjectInfo.objects[i].label));
                        objectTemp.mId = curObjectInfo.objects[i].mId;
                        objectTemp.mTop = curObjectInfo.objects[i].mTop;
                        objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                        objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                        objectTemp.mRight = curObjectInfo.objects[i].mRight;
                        objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                        objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                        objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                        objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                        objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                        objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                        objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                        objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                        objectTemp.mScore = curObjectInfo.objects[i].mScore;
                        /* 最大跟最小得分都设置成跟得分一样 */
                        objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                        objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                        /* 添加进去 */
                        tdbjectInfo_check.objects.push_back(objectTemp);
                    }
                }
            }
        }
        /* 去到调用目标检测的报警决策入口 */
        parse_UK_BSD_OD(tdbjectInfo_check, vehicleRealtimeStatus,tdbjectInfo_notCheck,curObjectInfo.alarmInfo.m_camera_covered_alarm,-1,false,detectTime);
    }

} // vis