//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/17.
//

#ifndef VIS_G3_SOFTWARE_VISALARMDECISION_MT_GESTURE_H
#define VIS_G3_SOFTWARE_VISALARMDECISION_MT_GESTURE_H

#include "VisAlarmDecision.h"

namespace vis {
    /**
     * 地铁手势的报警决策
     */
    class VisAlarmDecision_MT_Gesture : public VisAlarmDecision{
    public:
        VisAlarmDecision_MT_Gesture();
        /**
         * 解析 地铁手势算法 识别出来的结果的方法
         * @param objectInfo_ ： 地铁手势算法 识别出来的结果
         * @param vehicleRealtimeStatus ：车辆实时状态
         */
        void parse_MT_GES_OD(gt::objectInfo_ &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF) override;


    private:
        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;

    };

} // vis

#endif //VIS_G3_SOFTWARE_VISALARMDECISION_MT_GESTURE_H
