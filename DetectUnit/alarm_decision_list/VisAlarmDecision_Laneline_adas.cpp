//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/23.
//

#include "VisAlarmDecision_Laneline_adas.h"
#include "XuTimeUtil.h"

namespace vis {
    VisAlarmDecision_Laneline_adas::VisAlarmDecision_Laneline_adas() {
        curAlarmDecisionType = ALARM_DECISION_TYPE_LANELINE;
        detectionResult.alarmDecisionType = curAlarmDecisionType;
    }

    void VisAlarmDecision_Laneline_adas::parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo,
                                                                              Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {
        /* 是语义分割的，那么先把maskbuf复制过去 */
        detectionResult.maskBufWidth = DAS_MASK_WIDTH;
        detectionResult.maskBufHeight = DAS_MASK_HEIGHT;
        detectionResult.maskBufLen = sizeof(curObjectInfo.maskBuf);
        (void) memcpy(detectionResult.maskBuf,curObjectInfo.maskB<PERSON>,detectionResult.maskBufLen);
        detectionResult.curCameraType.copy(curCameraType);
        /* ADAS识别信息初始化一下 */
        detectionResult.adasDetectionInfo.reset();
        detectionResult.isReverse = vehicleRealtimeStatus.reverse;
        /* 设置全局的相机遮挡状态 */
        detectionResult.isCameraCover = curObjectInfo.alarmInfo.m_camera_covered_alarm;
        /* 存一下正式的识别阈值 */
        detectionResult.classThreshold = curClassThreshold;
        /* 算法的耗时 */
        detectionResult.detectUseTime =  detectTime;
        /* 塞一下报警决策类型和速度进去 */
        detectionResult.alarmDecisionType = curAlarmDecisionType;
        detectionResult.speed = vehicleRealtimeStatus.speed;
        /* 先清理一下框 */
        curAlarmDecisionObjects.clear();
        /* 遍历拿到的lanes信息,解析所有的车道线信息 */
        if (!curObjectInfo.lanes.empty()) {
            for (std::size_t i = 0; i < curObjectInfo.lanes.size(); i++) {
                /* 把实线跟虚线区分出来  只识别这两个 */
                if ((strcmp(curObjectInfo.lanes[i].label, "solid") == 0) ||
                    (strcmp(curObjectInfo.lanes[i].label, "dash") == 0)) {
//                    printf("lane i=%d  label=%s A0=%0.2f  A1=%0.2f  A2=%0.2f  A3=%0.2f\n",i,curObjectInfo.lanes[i].label,curObjectInfo.lanes[i].mA0,curObjectInfo.lanes[i].mA1,curObjectInfo.lanes[i].mA2,curObjectInfo.lanes[i].mA3);

                    /* 判断下是否是当前车道线，分左右，是的话把信息存起来 */
                    if(curObjectInfo.lanes[i].mIsLeftLane){
                        detectionResult.adasDetectionInfo.lineXDistance_left = curObjectInfo.lanes[i].mXDistance;
                        curLanelineId_left = curObjectInfo.lanes[i].mId;
                        /* 存一下是实线还是虚线 */
                        detectionResult.adasDetectionInfo.alarmStatus.lanelineType_left = (strcmp(curObjectInfo.lanes[i].label, "dash") == 0) ? 1 : 0;

                    }else if(curObjectInfo.lanes[i].mIsRightLane){
                        detectionResult.adasDetectionInfo.lineXDistance_right = curObjectInfo.lanes[i].mXDistance;
                        curLanelineId_right = curObjectInfo.lanes[i].mId;
                        /* 存一下是实线还是虚线 */
                        detectionResult.adasDetectionInfo.alarmStatus.lanelineType_right = (strcmp(curObjectInfo.lanes[i].label, "dash") == 0) ? 1 : 0;
                    }
                    /* 是对应的线，直接添加进来 */
                    curAlarmDecisionObjects.push_back(curObjectInfo.lanes[i]);
                }

            }
        }

//        printf("left_soild=%d  left_dash=%d  right_soild=%d  right_dash=%d   \n",curObjectInfo.alarmInfo.m_ldw_left_solid_alarm,curObjectInfo.alarmInfo.m_ldw_left_dash_alarm,curObjectInfo.alarmInfo.m_ldw_right_solid_alarm,curObjectInfo.alarmInfo.m_ldw_right_dash_alarm);


        /* 如果没有右边报警的，就看看是不是有左边报警 */
        if(!curAlarmInfo.hasRightSolidAlarm && !curAlarmInfo.hasRightDashAlarm){
            /* 判断一下压左实线 */
            checkLDW_LeftSolid(curObjectInfo, vehicleRealtimeStatus);
            /* 判断一下压左虚线 */
            checkLDW_LeftDash(curObjectInfo, vehicleRealtimeStatus);
        }


        /* 如果没有左边报警的，就看看是不是有右边报警 */
        if(!curAlarmInfo.hasLeftSolidAlarm && !curAlarmInfo.hasLeftDashAlarm){
            /* 判断一下压右实线 */
            checkLDW_RightSolid(curObjectInfo, vehicleRealtimeStatus);
            /* 判断一下压右虚线 */
            checkLDW_RightDash(curObjectInfo, vehicleRealtimeStatus);
        }



        /* 清理掉旧的框  把整理过的框塞进去 */
        curObjectInfo.lanes.clear();
        curObjectInfo.lanes.insert(curObjectInfo.lanes.begin(),curAlarmDecisionObjects.begin(),curAlarmDecisionObjects.end());
        /* 把识别信息发送出去 */
        curDetectDataCallback->onGetObjectInfo_Adas(curObjectInfo, detectionResult);
    }

    void VisAlarmDecision_Laneline_adas::checkLDW_LeftSolid(das::objectInfo_t &curObjectInfo,
                                                  Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 判断下左边实线的报警 */
        if(curObjectInfo.alarmInfo.m_ldw_left_solid_alarm){
            /* 现在有左边实线报警，那么需要判断下上次是否有左边实线报警 */
            if(curAlarmInfo.hasLeftSolidAlarm ){
                /* 之前也有左边实线报警，那么现在不用理 */
                ; //not to do
            }else{
                /* 上次没有左车道线报警，那么就需要发一条开始报警 */
                curAlarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_LDW_LEFT_SOLID;
                curAlarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_START;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType,curAlarmEventInfo.adasAlarmInfo.eventCode,curAlarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasLeftSolidAlarm = true;
                printf("***************send left solid start \n");

                lastAlarmTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
            }
        }else{
            /* 现在没有左边实线报警，那么需要判断下上次是否有左边实线报警 */
            if(curAlarmInfo.hasLeftSolidAlarm){
                /* 之前有左边实线报警，那么需要发一个停止左边实线报警的事件 */
                curAlarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_LDW_LEFT_SOLID;
                curAlarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_STOP;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType,curAlarmEventInfo.adasAlarmInfo.eventCode,curAlarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasLeftSolidAlarm = false;
                printf("***************send left solid stop \n");

            }else{
                /* 之前没有左边实线报警，那么现在不用理  */
                ; //not to do
            }
        }

    }

    void VisAlarmDecision_Laneline_adas::checkLDW_LeftDash(das::objectInfo_t &curObjectInfo,
                                                           Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 判断下左边虚线的报警 */
        if(curObjectInfo.alarmInfo.m_ldw_left_dash_alarm){
            /* 现在有左边虚线报警，那么需要判断下上次是否有左边虚线报警 */
            if(curAlarmInfo.hasLeftDashAlarm){
                /* 之前也有左边虚线报警，那么现在不用理 */
                ; //not to do
            }else{
                /* 上次没有左车道线报警，那么现在需要发一条开始 */
                curAlarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_LDW_LEFT_DASH;
                curAlarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_START;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType,curAlarmEventInfo.adasAlarmInfo.eventCode,curAlarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasLeftDashAlarm = true;
                printf("***************send left dash start \n");
                lastAlarmTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
            }
        }else{
            /* 现在没有左边虚线报警，那么需要判断下上次是否有左边虚线报警 */
            if(curAlarmInfo.hasLeftDashAlarm){
                /* 之前有左边虚线报警，那么需要发一个停止左边虚线报警的事件 */
                curAlarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_LDW_LEFT_DASH;
                curAlarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_STOP;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType,curAlarmEventInfo.adasAlarmInfo.eventCode,curAlarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasLeftDashAlarm = false;

                printf("***************send left dash stop \n");

            }else{
                /* 之前没有左边虚线报警，那么现在不用理  */
                ; //not to do
            }
        }
    }

    void VisAlarmDecision_Laneline_adas::checkLDW_RightSolid(das::objectInfo_t &curObjectInfo,
                                                             Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        /* 判断下右边实线的报警 */
        if(curObjectInfo.alarmInfo.m_ldw_right_solid_alarm){
            /* 现在有右边实线报警，那么需要判断下上次是否有右边实线报警 */
            if(curAlarmInfo.hasRightSolidAlarm){
                /* 之前也有右边实线报警，那么现在不用理 */
                ; //not to do
            }else{
                /* 上次没有车道线报警，那么现在需要发一条开始 */
                curAlarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_LDW_RIGHT_SOLID;
                curAlarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_START;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType, curAlarmEventInfo.adasAlarmInfo.eventCode,
                                                           curAlarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasRightSolidAlarm = true;
                printf("***************send right solid start \n");
                lastAlarmTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
            }


        }else{
            /* 现在没有右边实线报警，那么需要判断下上次是否有右边实线报警 */
            if(curAlarmInfo.hasRightSolidAlarm){
                /* 之前有右边实线报警，那么需要发一个停止右边实线报警的事件 */
                curAlarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_LDW_RIGHT_SOLID;
                curAlarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_STOP;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType,curAlarmEventInfo.adasAlarmInfo.eventCode,curAlarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasRightSolidAlarm = false;
                printf("***************send right solid stop \n");
            }else{
                /* 之前没有右边实线报警，那么现在不用理  */
                ; //not to do
            }
        }


    }

    void VisAlarmDecision_Laneline_adas::checkLDW_RightDash(das::objectInfo_t &curObjectInfo,
                                                            Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
         /* 判断下右边虚线的报警 */
        if(curObjectInfo.alarmInfo.m_ldw_right_dash_alarm){
            /* 现在有右边虚线报警，那么需要判断下上次是否有右边虚线报警 */
            if(curAlarmInfo.hasRightDashAlarm){
                /* 之前也有右边虚线报警，那么现在不用理 */
                ; //not to do
            }else{
                /* 上次没有车道线报警，那么现在需要发一条开始 */
                curAlarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_LDW_RIGHT_DASH;
                curAlarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_START;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType, curAlarmEventInfo.adasAlarmInfo.eventCode,
                                                           curAlarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasRightDashAlarm = true;
                printf("***************send right dash start \n");
                lastAlarmTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
            }


        }else{
            /* 现在没有右边虚线报警，那么需要判断下上次是否有右边虚线报警 */
            if(curAlarmInfo.hasRightDashAlarm){
                /* 之前有右边虚线报警，那么需要发一个停止右边虚线报警的事件 */
                curAlarmEventInfo.adasAlarmInfo.eventCode = EVENT_ADAS_LDW_RIGHT_DASH;
                curAlarmEventInfo.adasAlarmInfo.eventStatus = EVENTSTATUS_STOP;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType,curAlarmEventInfo.adasAlarmInfo.eventCode,curAlarmEventInfo);
                /* 重置下当前的报警状态 */
                curAlarmInfo.hasRightDashAlarm = false;
                printf("***************send right dash stop \n");

            }else{
                /* 之前没有右边虚线报警，那么现在不用理  */
                ; //not to do
            }
        }

    }
} // vis