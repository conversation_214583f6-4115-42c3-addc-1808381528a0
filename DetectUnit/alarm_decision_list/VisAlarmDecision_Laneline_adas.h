//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/23.
//

#ifndef VIS_G3_SOFTWARE_VISALARMDECISION_LANELINE_ADAS_H
#define VIS_G3_SOFTWARE_VISALARMDECISION_LANELINE_ADAS_H

#include "VisAlarmDecision.h"

namespace vis {
    /**
     * 车道线(ADAS) 的报警决策
     */
    class VisAlarmDecision_Laneline_adas : public VisAlarmDecision{
    public:
        /* 车道线(ADAS) 的报警决策中的报警信息 */
        struct AlarmDecisionLDWADASAlarmInfo{
            /* 是否有左边压虚线报警 */
            bool hasLeftDashAlarm = false;
            /* 是否有左边压实线报警 */
            bool hasLeftSolidAlarm = false;
            /* 是否有右边压虚线报警 */
            bool hasRightDashAlarm = false;
            /* 是否有右边压实线报警 */
            bool hasRightSolidAlarm = false;

        };



        VisAlarmDecision_Laneline_adas();

        void parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime = 0xFFFF) override;

    private:
        /* 报警事件信息 */
        AlarmEventInfo curAlarmEventInfo;
        /* 识别出来的信息整理好的结果 */
        G3DetectionResult detectionResult;
        /* 整理出来的这个报警决策需要传出去的框的列表 */
        std::vector<das::lane_> curAlarmDecisionObjects;
        /* 当前的LDW报警信息 */
        AlarmDecisionLDWADASAlarmInfo curAlarmInfo;
//        /* 车道线报警冷却时间 单位：s */
//        const uint64_t ALARM_SLEEP_TIME = 5;
        /* 上次车道线报警的时间 单位：s */
        uint64_t lastAlarmTime = 0;

        /* 当前车道的车道线的ID */
        int curLanelineId_left = -1;
        int curLanelineId_right = -1;




        /**
         *检查下是否有压左边实线报警
         *
         * @param curObjectInfo : 识别信息
         * @param vehicleRealtimeStatus ： 车况
         */
        void checkLDW_LeftSolid(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus);
        /**
         *检查下是否有压左边虚线报警
         *
         * @param curObjectInfo : 识别信息
         * @param vehicleRealtimeStatus ： 车况
         */
        void checkLDW_LeftDash(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus);
        /**
         *检查下是否有压右边实线报警
         *
         * @param curObjectInfo : 识别信息
         * @param vehicleRealtimeStatus ： 车况
         */
        void checkLDW_RightSolid(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus);
        /**
         *检查下是否有压右边虚线报警
         *
         * @param curObjectInfo : 识别信息
         * @param vehicleRealtimeStatus ： 车况
         */
        void checkLDW_RightDash(das::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus);

    };

} // vis

#endif //VIS_G3_SOFTWARE_VISALARMDECISION_LANELINE_ADAS_H
