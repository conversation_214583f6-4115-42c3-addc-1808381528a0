//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/12.
//

#include "VisAlarmDecision_Vehicle_Area.h"
#include "XuTimeUtil.h"

namespace vis {
    VisAlarmDecision_Vehicle_Area::VisAlarmDecision_Vehicle_Area() {
        curAlarmDecisionType = ALARM_DECISION_TYPE_VEHICLE_AREA;
        detectionResult.alarmDecisionType = curAlarmDecisionType;
    }

    void VisAlarmDecision_Vehicle_Area::parse_UK_BSD_OD(td::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,td::objectInfo_t &curObjectInfo_notCheck,bool cameraCovered,float imgBrightness, bool isFullblack,uint16_t detectTime) {

        hasVehicleInAlarmAreaLevel1 = false;
        hasVehicleInAlarmAreaLevel2 = false;
        curAlarmVehicleInfo_level1.mYDistance = 999999;
        curAlarmVehicleInfo_level2.mYDistance = 999999;
        /* 由于结构体里面没有裸指针，故直接用={}重置就好了 */
        alarmEventInfo = {};
        hasVehicleInAlarmAreaLevel1_shadow = false;
        hasVehicleInAlarmAreaLevel2_shadow = false;
        curAlarmVehicleInfo_level1_shadow.mYDistance = 999999;
        curAlarmVehicleInfo_level2_shadow.mYDistance = 999999;
        /* 由于结构体里面没有裸指针，故直接用={}重置就好了 */
        alarmEventInfo_shadow = {};

        detectionResult.bsdDetectionInfo.reset();
        detectionResult.curCameraType.copy(curCameraType);
        /* 看看是不是有遮挡 */
        cameraCover = cameraCovered;
        detectionResult.isCameraCover = cameraCover;
        detectionResult.cameraFullBlack = isFullblack;
        isbypass = false;

        detectionResult.bsdDetectionInfo.cameraCoverStatus.forward = cameraCover;
        detectionResult.bsdDetectionInfo.bypassStatus.forward = isbypass;
        detectionResult.speed = vehicleRealtimeStatus.speed;
        detectionResult.isReverse = vehicleRealtimeStatus.reverse;
        /* 存一下正式的识别阈值 */
        detectionResult.classThreshold = curClassThreshold;
        /* 获取一下报警区域 */
        getAlarmArea(vehicleRealtimeStatus);
        /* 先清理一下框 */
        curAlarmDecisionObjects.clear();
        /* 算法的耗时 */
        detectionResult.detectUseTime =  detectTime;
        detectionResult.alarmDecisionType = curAlarmDecisionType;
        detectionResult.speed = vehicleRealtimeStatus.speed;
        /* 遍历拿到的object信息，把人跟车的框分别拿出去解析 */
        if (!curObjectInfo.objects.empty()) {
            for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {
                /* 把车辆挑出来，给行人信息的识别类去识别 */
                if ((strcmp(curObjectInfo.objects[i].label, "car") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "bus") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "truck") == 0)) {

                    /* 先把框的点整理  避免超出屏幕的框 */
                    curObjectInfo.objects[i].mLeft = (curObjectInfo.objects[i].mLeft < 0) ? 0
                                                                                          : ((curObjectInfo.objects[i].mLeft >
                                                                                              1280) ? 1280
                                                                                                    : curObjectInfo.objects[i].mLeft);
                    curObjectInfo.objects[i].mTop = (curObjectInfo.objects[i].mTop < 0) ? 0
                                                                                        : ((curObjectInfo.objects[i].mTop >
                                                                                            720) ? 720
                                                                                                 : curObjectInfo.objects[i].mTop);
                    curObjectInfo.objects[i].mRight = (curObjectInfo.objects[i].mRight < 0) ? 0
                                                                                            : ((curObjectInfo.objects[i].mRight >
                                                                                                1280) ? 1280
                                                                                                      : curObjectInfo.objects[i].mRight);
                    curObjectInfo.objects[i].mBottom = (curObjectInfo.objects[i].mBottom < 0) ? 0
                                                                                              : ((curObjectInfo.objects[i].mBottom >
                                                                                                  720) ? 720

                                                                                                       : curObjectInfo.objects[i].mBottom);


//                    printf("id=%d  mScore=%f  mMaxScore=%f   mMinScore=%f curClassThreshold=%f \n",curObjectInfo.objects[i].mId,curObjectInfo.objects[i].mScore,curObjectInfo.objects[i].mMaxScore,curObjectInfo.objects[i].mMinScore,curClassThreshold);

                    /* 根据得分，看看是不是需不需走影子报警的流程 */
                    if ((curObjectInfo.objects[i].mMaxScore >= curClassThreshold) &&
                        (curObjectInfo.objects[i].mMinScore >= (curClassThreshold / 2.0f))) {
                        /* 大于等于正式阈值，那么就不是影子报警了 */
                        /* 判断下是否在一级区域内 */
                        if (isVehicleInAlarmAreaLevel1(curObjectInfo.objects[i])) {
                            /* 把label改一下 */
                            std::string newlabel;
                            newlabel.append("A1-");
                            newlabel.append(curObjectInfo.objects[i].label);
                            memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                            memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());

                            hasVehicleInAlarmAreaLevel1 = true;

                            /* 离得更近的话就用这个离得近的 */
                            if (curAlarmVehicleInfo_level1.mYDistance >= curObjectInfo.objects[i].mYDistance) {
                                alarmEventInfo.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                                alarmEventInfo.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                                alarmEventInfo.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                alarmEventInfo.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                curAlarmVehicleInfo_level1 = curObjectInfo.objects[i];
                            }
                        } else {
                            /* 没在一级区域内那么判断下是否在二级区域内 */
                            if (isVehicleInAlarmAreaLevel2(curObjectInfo.objects[i])) {
                                /* 把label改一下 */
                                std::string newlabel;
                                newlabel.append("A2-");
                                newlabel.append(curObjectInfo.objects[i].label);
                                memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                                memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());


                                hasVehicleInAlarmAreaLevel2 = true;

                                /* 离得更近的话就用这个离得近的 */
                                if (curAlarmVehicleInfo_level2.mYDistance >= curObjectInfo.objects[i].mYDistance) {
                                    alarmEventInfo.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                                    alarmEventInfo.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                                    alarmEventInfo.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                    alarmEventInfo.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                    curAlarmVehicleInfo_level2 = curObjectInfo.objects[i];
                                }
                            }

                        }
                    } else {
                        /* 小于正式阈值，是一个影子报警了 */
                        /* 判断下是否在一级区域内 */
                        if (isVehicleInAlarmAreaLevel1(curObjectInfo.objects[i])) {
                            /* 把label改一下 */
                            std::string newlabel;
                            newlabel.append("A1-");
                            newlabel.append(curObjectInfo.objects[i].label);
                            memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                            memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());

                            hasVehicleInAlarmAreaLevel1_shadow = true;

                            /* 离得更近的话就用这个离得近的 */
                            if (curAlarmVehicleInfo_level1_shadow.mYDistance >= curObjectInfo.objects[i].mYDistance) {
                                alarmEventInfo_shadow.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                                alarmEventInfo_shadow.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                                alarmEventInfo_shadow.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                alarmEventInfo_shadow.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                curAlarmVehicleInfo_level1_shadow = curObjectInfo.objects[i];
                            }

                        } else {
                            /* 没在一级区域内那么判断下是否在二级区域内 */
                            if (isVehicleInAlarmAreaLevel2(curObjectInfo.objects[i])) {
                                /* 把label改一下 */
                                std::string newlabel;
                                newlabel.append("A2-");
                                newlabel.append(curObjectInfo.objects[i].label);
                                memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                                memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());


                                hasVehicleInAlarmAreaLevel2_shadow = true;

                                /* 离得更近的话就用这个离得近的 */
                                if (curAlarmVehicleInfo_level2_shadow.mYDistance >=
                                    curObjectInfo.objects[i].mYDistance) {
                                    alarmEventInfo_shadow.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                                    alarmEventInfo_shadow.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                                    alarmEventInfo_shadow.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                    alarmEventInfo_shadow.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                    curAlarmVehicleInfo_level2_shadow = curObjectInfo.objects[i];
                                }
                            }

                        }


                    }


                    /* 是对应的框，直接添加进来 */
                    curAlarmDecisionObjects.push_back(curObjectInfo.objects[i]);
                }
                /* motorcyclist需要特殊处理 */

            }
        }


        parseEvent_level1(curAlarmVehicleInfo_level1.mId, curAlarmVehicleInfo_level1_shadow.mId, vehicleRealtimeStatus);


        parseEvent_level2(curAlarmVehicleInfo_level2.mId, curAlarmVehicleInfo_level2_shadow.mId,
                          vehicleRealtimeStatus);


        /* 清理掉旧的框  把整理过的框塞进去 */
        td::objectInfo_t curVehicleObjectInfo = curObjectInfo;
        curVehicleObjectInfo.objects.clear();

        /* 整理识别框（同时也进行降效那些） */
        organizeObjectInfoToShow(curVehicleObjectInfo, curAlarmDecisionObjects, curObjectInfo_notCheck.objects);

        /* 把识别信息发送出去 */
        curDetectDataCallback->onGetObjectInfo_BSD(curVehicleObjectInfo, detectionResult);
    }

    bool VisAlarmDecision_Vehicle_Area::isVehicleInAlarmAreaLevel1(td::object_ &vehicleInfo) {
        /* 判断下一级区域是否有设置（前面两个点不为0） */
        if (alarmAreaPointList_level1[0].x == 0 && alarmAreaPointList_level1[0].y == 0 &&
            alarmAreaPointList_level1[1].x == 0 && alarmAreaPointList_level1[1].y == 0) {
            return false;
        }


        VISPoint vehiclePOints[4];
        bool isIn = false;

        vehiclePOints[0].x = vehicleInfo.mLeft;
        vehiclePOints[0].y = vehicleInfo.mTop;

        vehiclePOints[1].x = vehicleInfo.mRight;
        vehiclePOints[1].y = vehicleInfo.mTop;

        vehiclePOints[2].x = vehicleInfo.mRight;
        vehiclePOints[2].y = vehicleInfo.mBottom;

        vehiclePOints[3].x = vehicleInfo.mLeft;
        vehiclePOints[3].y = vehicleInfo.mBottom;

        /* 判断下是否在报警区域内 */
        isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(vehiclePOints, 4,
                                                                                         alarmAreaPointList_level1, 6);

        return isIn;
    }

    void VisAlarmDecision_Vehicle_Area::getAlarmArea(Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        switch (curCameraId) {
            case CAMERA_ID_1: {
                bool needChange = G3_Configuration::getInstance().getVehicleAlarmNeedChangeByTurnSignCamera0() == 1;
                int minSpeed = G3_Configuration::getInstance().getSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera0();
                int maxSpeed = G3_Configuration::getInstance().getSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera0();
                /* 如果需要变化报警区域  打了转向灯  且到达了制定速度 就需要变化报警区域   (两边同时打的这种情况不需要变化区域)  */
                if (needChange && vehicleRealtimeStatus.speed >= minSpeed && vehicleRealtimeStatus.speed <= maxSpeed &&
                    (vehicleRealtimeStatus.turnR || vehicleRealtimeStatus.turnL) &&
                    !(vehicleRealtimeStatus.turnR && vehicleRealtimeStatus.turnL)) {

                    if (vehicleRealtimeStatus.turnL) {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera1TurnleftAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera1TurnleftAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);

                    } else {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera1TurnrightAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera1TurnrightAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);
                    }

                } else {
                    /* 设置报警区域1的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera0VehicleAlramAreaLevel1(),
                            alarmAreaPointList_level1,
                            6);

                    /* 设置报警区域2的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera0VehicleAlramAreaLevel2(),
                            alarmAreaPointList_level2,
                            6);
                }


            }
                break;

            case CAMERA_ID_2: {
                bool needChange = G3_Configuration::getInstance().getVehicleAlarmNeedChangeByTurnSignCamera1() == 1;
                int minSpeed = G3_Configuration::getInstance().getSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera1();
                int maxSpeed = G3_Configuration::getInstance().getSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera1();
                /* 如果需要变化报警区域  打了转向灯  且到达了制定速度 就需要变化报警区域   (两边同时打的这种情况不需要变化区域)  */
                if (needChange && vehicleRealtimeStatus.speed >= minSpeed && vehicleRealtimeStatus.speed <= maxSpeed &&
                    (vehicleRealtimeStatus.turnR || vehicleRealtimeStatus.turnL) &&
                    !(vehicleRealtimeStatus.turnR && vehicleRealtimeStatus.turnL)) {

                    if (vehicleRealtimeStatus.turnL) {

                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera2TurnleftAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera2TurnleftAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);

                    } else {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera2TurnrightAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera2TurnrightAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);
                    }


                } else {
                    /* 设置报警区域1的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera1VehicleAlramAreaLevel1(),
                            alarmAreaPointList_level1,
                            6);

                    /* 设置报警区域2的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera1VehicleAlramAreaLevel2(),
                            alarmAreaPointList_level2,
                            6);
                }
            }
                break;

            case CAMERA_ID_3: {
                bool needChange = G3_Configuration::getInstance().getVehicleAlarmNeedChangeByTurnSignCamera2() == 1;
                int minSpeed = G3_Configuration::getInstance().getSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera2();
                int maxSpeed = G3_Configuration::getInstance().getSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera2();
                /* 如果需要变化报警区域  打了转向灯  且到达了制定速度 就需要变化报警区域   (两边同时打的这种情况不需要变化区域)  */
                if (needChange && vehicleRealtimeStatus.speed >= minSpeed && vehicleRealtimeStatus.speed <= maxSpeed &&
                    (vehicleRealtimeStatus.turnR || vehicleRealtimeStatus.turnL) &&
                    !(vehicleRealtimeStatus.turnR && vehicleRealtimeStatus.turnL)) {

                    if (vehicleRealtimeStatus.turnL) {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera3TurnleftAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera3TurnleftAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);

                    } else {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera3TurnrightAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera3TurnrightAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);
                    }

                } else {
                    /* 设置报警区域1的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera2VehicleAlramAreaLevel1(),
                            alarmAreaPointList_level1,
                            6);

                    /* 设置报警区域2的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera2VehicleAlramAreaLevel2(),
                            alarmAreaPointList_level2,
                            6);
                }
            }
                break;

            case CAMERA_ID_4: {
                bool needChange = G3_Configuration::getInstance().getVehicleAlarmNeedChangeByTurnSignCamera3() == 1;
                int minSpeed = G3_Configuration::getInstance().getSbstChangeVehicleAlramAreaByTurnlightMinSpeedCamera3();
                int maxSpeed = G3_Configuration::getInstance().getSbstChangeVehicleAlramAreaByTurnlightMaxSpeedCamera3();
                /* 如果需要变化报警区域  打了转向灯  且到达了制定速度 就需要变化报警区域   (两边同时打的这种情况不需要变化区域)  */
                if (needChange && vehicleRealtimeStatus.speed >= minSpeed && vehicleRealtimeStatus.speed <= maxSpeed &&
                    (vehicleRealtimeStatus.turnR || vehicleRealtimeStatus.turnL) &&
                    !(vehicleRealtimeStatus.turnR && vehicleRealtimeStatus.turnL)) {

                    if (vehicleRealtimeStatus.turnL) {

                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera4TurnleftAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera4TurnleftAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);

                    } else {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera4TurnrightAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstVehicleAlramAreaCamera4TurnrightAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);
                    }


                } else {
                    /* 设置报警区域1的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera3VehicleAlramAreaLevel1(),
                            alarmAreaPointList_level1,
                            6);

                    /* 设置报警区域2的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera3VehicleAlramAreaLevel2(),
                            alarmAreaPointList_level2,
                            6);
                }
            }
                break;


        }
        /* 获取下最终使用的报警区域的的点（是不是降效都在里面实现了） */
        getUltimateAlarmAreaPointList(alarmAreaPointList_level1, 6);
        getUltimateAlarmAreaPointList(alarmAreaPointList_level2, 6);
    }

    int VisAlarmDecision_Vehicle_Area::pointVectorPointsToPointArrays(std::vector<VISPoint> pointsVector,
                                                                      VISPoint *pointArrays, const int len) {
        int ret = -1;
        if (pointsVector.size() == static_cast<std::size_t>(len)) {
            for (std::size_t i = 0; i < pointsVector.size(); i++) {
                pointArrays[i].x = pointsVector[i].x;
                pointArrays[i].y = pointsVector[i].y;
            }
            ret = 0;
        } else { ; // not to do
        }
        return ret;
    }

    bool VisAlarmDecision_Vehicle_Area::isVehicleInAlarmAreaLevel2(td::object_ &vehicleInfo) {
        /* 判断下二级区域是否有设置（前面两个点不为0） */
        if (alarmAreaPointList_level2[0].x == 0 && alarmAreaPointList_level2[0].y == 0 &&
            alarmAreaPointList_level2[1].x == 0 && alarmAreaPointList_level2[1].y == 0) {
            return false;
        }

        VISPoint vehiclePOints[4];
        bool isIn = false;

        vehiclePOints[0].x = vehicleInfo.mLeft;
        vehiclePOints[0].y = vehicleInfo.mTop;

        vehiclePOints[1].x = vehicleInfo.mRight;
        vehiclePOints[1].y = vehicleInfo.mTop;

        vehiclePOints[2].x = vehicleInfo.mRight;
        vehiclePOints[2].y = vehicleInfo.mBottom;

        vehiclePOints[3].x = vehicleInfo.mLeft;
        vehiclePOints[3].y = vehicleInfo.mBottom;

        /* 判断下是否在报警区域内 */
        isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(vehiclePOints, 4,
                                                                                         alarmAreaPointList_level2, 6);

        return isIn;
    }

    void VisAlarmDecision_Vehicle_Area::parseEvent_level1(uint32_t curAlaramId, uint32_t curAlaramId_shadow,
                                                          Vehicle_RealtimeStatus &vehicleRealtimeStatus) {

        if (hasVehicleInAlarmAreaLevel1) {
            bool needToSendAlarmEvent = true;
            int eventCode = EVENT_UNKNOW;

            /* 根据安装装位置确定这个报警应该转成什么类型的 */
            switch (curCameraType.installPosition) {
                case INSTALL_POSITION_LEFT: {
                    /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_RIGHT: {
                    /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_FRONT:
                case INSTALL_POSITION_BACK: {
                    /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.forward_level1 = true;
                            eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.backward_level1 = true;
                            eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD: {
                            if (curCameraType.installPosition == INSTALL_POSITION_FRONT) {
                                alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL1;
                                detectionResult.bsdDetectionInfo.vehicleStatus.forward_level1 = true;
                                eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL1;
                            } else {
                                alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL1;
                                detectionResult.bsdDetectionInfo.vehicleStatus.backward_level1 = true;
                                eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL1;
                            }
                        }
                            break;
                    }
                }
                    break;

            }


            /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
            if (needToSendAlarmEvent) {

                /* 判断是否在需要报警的车速区间内,不在的话就不需要判断报警 */
                if (vehicleRealtimeStatus.speed <
                    G3_Configuration::getInstance().getSbstSidewardVehicleMinAlramSpeedLevel1() &&
                    vehicleRealtimeStatus.speed >
                    G3_Configuration::getInstance().getSbstSidewardVehicleMaxAlramSpeedLevel1()) {

                    return;
                }

                /* 看看是否需要启动同ID过滤   */
                if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel1() == 0 &&
                    lastAlaramId_level1 == curAlaramId) {
                    printf("lastAlaramId_level1=%d    curAlaramId=%d   \n", lastAlaramId_level1, curAlaramId);
                    return;
                }
                /* 看看是否需要判断车门信号过滤 */
                if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                    (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                    printf("Doors is opened,did not alarm! \n");
                    return;
                }

                lastAlaramId_level1 = curAlaramId;
                alarmEventInfo.bsdAlarmInfo.alarmVehicleId = curAlaramId;
                alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                alarmEventInfo.isShadow = false;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            }
        } else {
            lastAlaramId_level1 = -1;
        }




        /* 判断一下影子的报警 */
        if (hasVehicleInAlarmAreaLevel1_shadow) {
            bool needToSendAlarmEvent = true;
            int eventCode = EVENT_UNKNOW;

            /* 根据安装装位置确定这个报警应该转成什么类型的 */
            switch (curCameraType.installPosition) {
                case INSTALL_POSITION_LEFT: {
                    /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_RIGHT: {
                    /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_FRONT:
                case INSTALL_POSITION_BACK: {
                    /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD: {
                            if (curCameraType.installPosition == INSTALL_POSITION_FRONT) {
                                alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL1;
                                eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL1;
                            } else {
                                alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL1;
                                eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL1;
                            }
                        }
                            break;
                    }
                }
                    break;

            }


            /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
            if (needToSendAlarmEvent) {

                /* 判断是否在需要报警的车速区间内,不在的话就不需要判断报警 */
                if (vehicleRealtimeStatus.speed <
                    G3_Configuration::getInstance().getSbstSidewardVehicleMinAlramSpeedLevel1() &&
                    vehicleRealtimeStatus.speed >
                    G3_Configuration::getInstance().getSbstSidewardVehicleMaxAlramSpeedLevel1()) {

                    return;
                }

                /* 看看是否需要启动同ID过滤   */
                if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel1() == 0 &&
                    lastAlaramId_level1_shadow == curAlaramId_shadow) {
                    printf("lastAlaramId_level1_shadow=%d    curAlaramId_shadow=%d   \n", lastAlaramId_level1_shadow,
                           curAlaramId_shadow);
                    return;
                }
                /* 看看是否需要判断车门信号过滤 */
                if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                    (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                    printf("Doors is opened,did not alarm! \n");
                    return;
                }

                lastAlaramId_level1_shadow = curAlaramId_shadow;
                alarmEventInfo_shadow.bsdAlarmInfo.alarmVehicleId = curAlaramId_shadow;
                alarmEventInfo_shadow.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                alarmEventInfo_shadow.isShadow = true;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, alarmEventInfo_shadow);
            }
        } else {
            lastAlaramId_level1_shadow = -1;
        }


    }

    void VisAlarmDecision_Vehicle_Area::parseEvent_level2(uint32_t curAlaramId, uint32_t curAlaramId_shadow,
                                                          Vehicle_RealtimeStatus &vehicleRealtimeStatus) {


        if (hasVehicleInAlarmAreaLevel2) {
            bool needToSendAlarmEvent = true;
            int eventCode = EVENT_UNKNOW;

            /* 根据安装装位置确定这个报警应该转成什么类型的 */
            switch (curCameraType.installPosition) {
                case INSTALL_POSITION_LEFT: {
                    /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;

                    }
                }
                    break;


                case INSTALL_POSITION_RIGHT: {
                    /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_FRONT:
                case INSTALL_POSITION_BACK: {
                    /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.forward_level2 = true;
                            eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.backward_level2 = true;
                            eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                            detectionResult.bsdDetectionInfo.vehicleStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_DOWNWARD: {
                            if (curCameraType.installPosition == INSTALL_POSITION_FRONT) {
                                alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL2;
                                detectionResult.bsdDetectionInfo.vehicleStatus.forward_level2 = true;
                                eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL2;

                            } else {
                                alarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL2;
                                detectionResult.bsdDetectionInfo.vehicleStatus.backward_level2 = true;
                                eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL2;
                            }

                        }
                            break;

                    }
                }
                    break;
            }


            /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
            if (needToSendAlarmEvent) {

                /* 判断是否在需要报警的车速区间内,不在的话就不需要判断报警 */
                if (vehicleRealtimeStatus.speed <
                    G3_Configuration::getInstance().getSbstSidewardVehicleMinAlramSpeedLevel2() &&
                    vehicleRealtimeStatus.speed >
                    G3_Configuration::getInstance().getSbstSidewardVehicleMaxAlramSpeedLevel2()) {

                    return;
                }

                /* 看看是否需要启动同ID过滤   */
                if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel2() == 0 &&
                    lastAlaramId_level2 == curAlaramId) {
                    printf("lastAlaramId_level2=%d    curAlaramId=%d   \n", lastAlaramId_level2, curAlaramId);
                    return;
                }
                /* 看看是否需要判断车门信号过滤 */
                if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                    (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                    printf("Doors is opened,did not alarm! \n");
                    return;
                }

                lastAlaramId_level2 = curAlaramId;
                alarmEventInfo.bsdAlarmInfo.alarmVehicleId = curAlaramId;
                alarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                alarmEventInfo.isShadow = false;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            }
        } else {
            lastAlaramId_level2 = -1;
        }


        /* 判断一下影子 */
        if (hasVehicleInAlarmAreaLevel2_shadow) {
            bool needToSendAlarmEvent = true;
            int eventCode = EVENT_UNKNOW;

            /* 根据安装装位置确定这个报警应该转成什么类型的 */
            switch (curCameraType.installPosition) {
                case INSTALL_POSITION_LEFT: {
                    /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;

                    }
                }
                    break;


                case INSTALL_POSITION_RIGHT: {
                    /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_FRONT:
                case INSTALL_POSITION_BACK: {
                    /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_RIGHT: {
                            alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_DOWNWARD: {
                            if (curCameraType.installPosition == INSTALL_POSITION_FRONT) {
                                alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL2;
                                eventCode = EVENT_BSD_FORWARD_VEHICLE_LEVEL2;

                            } else {
                                alarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL2;
                                eventCode = EVENT_BSD_BACKWARD_VEHICLE_LEVEL2;
                            }

                        }
                            break;

                    }
                }
                    break;
            }


            /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
            if (needToSendAlarmEvent) {

                /* 判断是否在需要报警的车速区间内,不在的话就不需要判断报警 */
                if (vehicleRealtimeStatus.speed <
                    G3_Configuration::getInstance().getSbstSidewardVehicleMinAlramSpeedLevel2() &&
                    vehicleRealtimeStatus.speed >
                    G3_Configuration::getInstance().getSbstSidewardVehicleMaxAlramSpeedLevel2()) {

                    return;
                }

                /* 看看是否需要启动同ID过滤   */
                if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel2() == 0 &&
                    lastAlaramId_level2_shadow == curAlaramId_shadow) {
                    printf("lastAlaramId_level2_shadow=%d    curAlaramId_shadow=%d   \n", lastAlaramId_level2_shadow,
                           curAlaramId_shadow);
                    return;
                }
                /* 看看是否需要判断车门信号过滤 */
                if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                    (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                    printf("Doors is opened,did not alarm! \n");
                    return;
                }

                lastAlaramId_level2_shadow = curAlaramId_shadow;
                alarmEventInfo_shadow.bsdAlarmInfo.alarmVehicleId = curAlaramId_shadow;
                alarmEventInfo_shadow.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                alarmEventInfo_shadow.isShadow = true;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, alarmEventInfo_shadow);
            }
        } else {
            lastAlaramId_level2_shadow = -1;
        }


    }

    void VisAlarmDecision_Vehicle_Area::parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo,
                                                                             Vehicle_RealtimeStatus &vehicleRealtimeStatus,
                                                                             uint16_t detectTime) {
        /* 是语义分割的，那么先把maskbuf复制过去 */
        detectionResult.maskBufWidth = DAS_MASK_WIDTH;
        detectionResult.maskBufHeight = DAS_MASK_HEIGHT;
        detectionResult.maskBufLen = sizeof(curObjectInfo.maskBuf);
        (void) memcpy(detectionResult.maskBuf, curObjectInfo.maskBuf, detectionResult.maskBufLen);

        /* 先定义一个td下的objectinfo，用来后续调用parse_UK_BSD_OD进行是否在报警区域内的检测的 */
        td::objectInfo_t tdbjectInfo_check;
        /* 先定义一个td下的objectinfo，用来后续调用parse_UK_BSD_OD，这不需要进行检测，只是输出框 */
        td::objectInfo_t tdbjectInfo_notCheck;
        /* 遍历拿到的object信息，把车辆拿出去解析 */
        if (!curObjectInfo.objects.empty()) {
            /* 先把报警信息复制过去 */
            memcpy(&tdbjectInfo_check.alarmInfo, &curObjectInfo.alarmInfo, sizeof(curObjectInfo.alarmInfo));
            /* 再把故障信息复制过去 */
            memcpy(&tdbjectInfo_check.faultInfo, &curObjectInfo.faultInfo, sizeof(curObjectInfo.faultInfo));

            /* 再把Object信息给复制过去 */
            for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {
                /* 把车辆挑出来，给车辆信息的识别类去识别 */
                if ((strcmp(curObjectInfo.objects[i].label, "car") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "bus") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "truck") == 0)) {
                    /* 判断下是否需要在可行驶区域内的车辆才报警  如果是的话  只有才可行驶区域的车辆才会送去报警决策 */
                    if (G3_Configuration::getInstance().getVehicleAlarmNeedTravelableArea() == 1) {
                        /* 只有在可行驶区域内的才可以去到报警决策 */
                        if (curObjectInfo.objects[i].mIsInsideRoad) {
                            /* 转换一下 */
                            td::object_t objectTemp;
                            memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                                   sizeof(curObjectInfo.objects[i].label));
                            objectTemp.mId = curObjectInfo.objects[i].mId;
                            objectTemp.mTop = curObjectInfo.objects[i].mTop;
                            objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                            objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                            objectTemp.mRight = curObjectInfo.objects[i].mRight;
                            objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                            objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                            objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                            objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                            objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                            objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                            objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                            objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                            objectTemp.mScore = curObjectInfo.objects[i].mScore;
                            /* 最大跟最小得分都设置成跟得分一样 */
                            objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                            objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                            /* 添加进去 */
                            tdbjectInfo_check.objects.push_back(objectTemp);
                        } else {
                            /* 不在可行驶区域内的需要放在另一个地方，该地方不要进行报警判断，但是需要直接输出出去 */
                            /* 转换一下 */
                            td::object_t objectTemp;
                            memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                                   sizeof(curObjectInfo.objects[i].label));
                            objectTemp.mId = curObjectInfo.objects[i].mId;
                            objectTemp.mTop = curObjectInfo.objects[i].mTop;
                            objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                            objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                            objectTemp.mRight = curObjectInfo.objects[i].mRight;
                            objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                            objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                            objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                            objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                            objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                            objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                            objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                            objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                            objectTemp.mScore = curObjectInfo.objects[i].mScore;
                            /* 最大跟最小得分都设置成跟得分一样 */
                            objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                            objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                            /* 添加进去 */
                            tdbjectInfo_notCheck.objects.push_back(objectTemp);
                        }
                    } else {
                        /* 转换一下 */
                        td::object_t objectTemp;
                        memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                               sizeof(curObjectInfo.objects[i].label));
                        objectTemp.mId = curObjectInfo.objects[i].mId;
                        objectTemp.mTop = curObjectInfo.objects[i].mTop;
                        objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                        objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                        objectTemp.mRight = curObjectInfo.objects[i].mRight;
                        objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                        objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                        objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                        objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                        objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                        objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                        objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                        objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                        objectTemp.mScore = curObjectInfo.objects[i].mScore;
                        /* 最大跟最小得分都设置成跟得分一样 */
                        objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                        objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                        /* 添加进去 */
                        tdbjectInfo_check.objects.push_back(objectTemp);
                    }
                }
            }

        }

        /* 去到调用目标检测的报警决策入口 */
        parse_UK_BSD_OD(tdbjectInfo_check, vehicleRealtimeStatus, tdbjectInfo_notCheck,
                        curObjectInfo.alarmInfo.m_camera_covered_alarm, -1, false, detectTime);


    }
} // vis