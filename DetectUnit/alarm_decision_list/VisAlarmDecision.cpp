//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/7.
//

#include "VisAlarmDecision.h"

namespace vis {

    bool VisAlarmDecision::isCameraBypass() const {
        return isbypass;
    }

    void VisAlarmDecision::setCameraBypass(bool cameraBypass) {
        isbypass = cameraBypass;
    }

    int VisAlarmDecision::init(int cameraId, float classThreshold, DetectDataCallback *detectDataCallback) {
        curCameraId = cameraId;
        curClassThreshold = classThreshold;
        curDetectDataCallback = detectDataCallback;
        G3_Configuration::getInstance().getCameraTypeInfoOfCamera(curCameraType, cameraId);
        inited = true;
        return 0;
    }

    bool VisAlarmDecision::isCameraCover() const {
        return cameraCover;
    }

    void VisAlarmDecision::setCameraCover(bool cameraCover) {
        VisAlarmDecision::cameraCover = cameraCover;
    }

    bool VisAlarmDecision::isInited() const {
        return inited;
    }

    void
    VisAlarmDecision::parse_UK_BSD_OD(td::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,td::objectInfo_t &curObjectInfo_notCheck,bool cameraCovered,float imgBrightness, bool isFullblack,uint16_t detectTime) {
        printf("VisAlarmDecision::parse_UK_BSD_OD  \n");

    }

    void VisAlarmDecision::parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo,
                                                                Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {
        printf("VisAlarmDecision::parse_SBST_160_SEMANTIC_SEGMENTATION  \n");
    }

    void VisAlarmDecision::parse_MT_DSM_OD(face_message &face_roi_info, cv::Mat &frame,
                                           Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {
        printf("VisAlarmDecision::parse_MT_DSM_OD  \n");

    }

    void VisAlarmDecision::parse_MT_GES_OD(gt::objectInfo_ &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {
        printf("VisAlarmDecision::parse_MT_GES_OD  \n");
    }
    void VisAlarmDecision::parse_MUV_DSM_OD(dsm_muv::DSMInfo &almuv_info, const int state_of, Vehicle_RealtimeStatus &vehicleRealtimeStatus, bool cameraCovered, float imgBrightness, bool isFullblack,uint16_t detectTime) {
        printf("VisAlarmDecision::parse_MUV_DSM_OD  \n");
    }

    bool VisAlarmDecision::checkCameraAlarmSwitch() {
        bool ret = false;
        /* 先判断下是否需要依据镜头报警开关 */
        if(G3_Configuration::getInstance().getCameraAlarmNeedCameraAlarmSwitch() == 1){
            switch (curCameraId) {
                case CAMERA_ID_1:{
                    /* 先看看所有开关是否开着并且可以报镜头1的报警 */
                    if(G3_Configuration::getInstance().getCameraAlarmSwitch1().camera1AlarmEnable && G3_Configuration::getInstance().getCameraAlarmSwitchStatus().cameraAlarmSwitch1){
                        ret = true;
                    }else if(G3_Configuration::getInstance().getCameraAlarmSwitch2().camera1AlarmEnable && G3_Configuration::getInstance().getCameraAlarmSwitchStatus().cameraAlarmSwitch2){
                        ret = true;
                    }else if(G3_Configuration::getInstance().getCameraAlarmSwitch3().camera1AlarmEnable && G3_Configuration::getInstance().getCameraAlarmSwitchStatus().cameraAlarmSwitch3){
                        ret = true;
                    }
                }
                    break;
                case CAMERA_ID_2:{
                    /* 先看看所有开关是否开着并且可以报镜头2的报警 */
                    if(G3_Configuration::getInstance().getCameraAlarmSwitch1().camera2AlarmEnable && G3_Configuration::getInstance().getCameraAlarmSwitchStatus().cameraAlarmSwitch1){
                        ret = true;
                    }else if(G3_Configuration::getInstance().getCameraAlarmSwitch2().camera2AlarmEnable && G3_Configuration::getInstance().getCameraAlarmSwitchStatus().cameraAlarmSwitch2){
                        ret = true;
                    }else if(G3_Configuration::getInstance().getCameraAlarmSwitch3().camera2AlarmEnable && G3_Configuration::getInstance().getCameraAlarmSwitchStatus().cameraAlarmSwitch3){
                        ret = true;
                    }
                }
                    break;
            }
        }else{
            ret = true;
        }

        return ret;
    }

    void VisAlarmDecision::parse_DVR_DETECTINFO(Vehicle_RealtimeStatus &vehicleRealtimeStatus, bool cameraCovered, float imgBrightness, bool isFullblack,uint16_t detectTime) {
        printf("VisAlarmDecision::parse_DVR_DETECTINFO  \n");
    }

    bool VisAlarmDecision::needReduceDetectionEffect(const int cameraId) {
        bool ret = false;
        /* 获取下当前的降效信息 */
        CameraReduceEffectInfo reduceEffectInfo = G3_Configuration::getInstance().getCurCameraReduceEffectInfo();
        /* 获取对应镜头的降效结果 */
        switch (cameraId) {
            case CAMERA_ID_1:{
                ret = reduceEffectInfo.camera1;
            }
                break;

            case CAMERA_ID_2:{
                ret = reduceEffectInfo.camera2;
            }
                break;

            case CAMERA_ID_3:{
                ret = reduceEffectInfo.camera3;
            }
                break;

            case CAMERA_ID_4:{
                ret = reduceEffectInfo.camera4;
            }
                break;
        }
        return ret;
    }

    int VisAlarmDecision::getUltimateAlarmAreaPointList(VISPoint *alarmAreaPointList, const int pointListLen) {
        int ret = -1;
        /* 先判断一下长度是否合法 */
        if(pointListLen > 0){
            /* 长度合法，那么判断一下对应的镜头是否需要降效 */
            if(needReduceDetectionEffect(curCameraId)){
                /* 先获取图像中心点的坐标 */
                double centorX = MRV220_IMG_WIDTH / 2;
                double centorY = MRV220_IMG_HEIGHT / 2;
                /* 遍历数组，一个个转换 */
                for(std::size_t i = 0; i < pointListLen; i++){
                    VISPoint temp = {alarmAreaPointList[i].x, alarmAreaPointList[i].y};
                    /* 根据图像的缩放比例缩放此点跟图像中心点的距离 */
                    alarmAreaPointList[i].x = ((temp.x - centorX) * G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorX;
                    alarmAreaPointList[i].y = ((temp.y - centorY) * G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorY;
                }

            }else{
                /* 不需要降效，那么不处理 */
            }
            ret = pointListLen;
        }else{
            /* 长度不合法，直接不理，返回失败 */
            ; //not to do
        }
        return ret;
    }

    void VisAlarmDecision::organizeObjectInfoToShow(td::objectInfo_t &dest, std::vector<td::object_t> &alarmObjectInfos, std::vector<td::object_t> &notCheckObjectInfos) {
        /* 先塞到一起 */

        /* 先塞参与报警决策的那些框 */
        dest.objects.insert(dest.objects.begin(), alarmObjectInfos.begin(), alarmObjectInfos.end());
        /* 再塞那些不参与报警决策的框 */
        dest.objects.insert(dest.objects.end(),notCheckObjectInfos.begin(),notCheckObjectInfos.end());

        /* 然后看看是不是需要降效 */
        if(needReduceDetectionEffect(curCameraId)){
            /* 需要降效，那么把所有框进行方大 */
            enlargeObjectRect(dest.objects);
        }else{
            /* 不需要降效，直接不理了 */
            ; //not to do
        }
    }

    int VisAlarmDecision::enlargeObjectRect(std::vector<td::object_t> &objectInfos) {
        int ret = -1;
        /* 先看看是不是有数据 */
        if(!objectInfos.empty()){
            /* 有数据了，那么先计算出图像的中心点 */
            double centorX = MRV220_IMG_WIDTH / 2;
            double centorY = MRV220_IMG_HEIGHT / 2;
            /* 遍历出一个个框，开始准备改 */
            for(std::size_t i = 0; i < objectInfos.size(); i ++){
                /* 先获取现在的框的两个点的坐标 */
                int leftX = objectInfos[i].mLeft;
                int leftY = objectInfos[i].mTop;
                int rightX = objectInfos[i].mRight;
                int rightY = objectInfos[i].mBottom;
                /* 求出距离图像中心的距离然后进行缩放 */
                objectInfos[i].mLeft = ((leftX - centorX) / G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorX;
                objectInfos[i].mTop = ((leftY - centorY) / G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorY;
                objectInfos[i].mRight = ((rightX - centorX) / G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorX;
                objectInfos[i].mBottom = ((rightY - centorY) / G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorY;
            }
            ret = objectInfos.size();
        }else{
            /* 没有数据，直接不用理 */
            ; //not to do
        }
        return ret;
    }

    void VisAlarmDecision::parse_STD_DSM_OD(dsm_std::DSMInfo &stdDSMinfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,
                                            bool cameraCovered, float imgBrightness, bool isFullblack,
                                            uint16_t detectTime) {
        printf("VisAlarmDecision::parse_STD_DSM_OD  \n");

    }

} // vis