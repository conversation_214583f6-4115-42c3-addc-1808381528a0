//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/14.
//

#include "VisAlarmDecision_MT_Face.h"
#include "XuTimeUtil.h"

using namespace std;
using namespace cv;

extern DSMInfo alarm_info;
extern face_message face_params;
extern globals globalparams;
extern alarm_timer respirator_timer, mouth_timer, eye_timer, yaw_timer, camcover_timer, leave_timer;

namespace vis {
    VisAlarmDecision_MT_Face::VisAlarmDecision_MT_Face() {
        curAlarmDecisionType = ALARM_DECISION_TYPE_MT_DSM_FACE;
        detectionResult.alarmDecisionType = curAlarmDecisionType;
        G3DSMFaceDetectionInfo faceDetectionInfo;
        detectionResult.faceList.push_back(faceDetectionInfo);



    }

    void VisAlarmDecision_MT_Face::parse_MT_DSM_OD(face_message &face_roi_info,
                                                   cv::Mat &frame,Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {

        try {

            const int input_width = 1280;
            const int input_height = 720;
            float ratio = (float) input_height / (float) 640; //缩放比例
            int x_offset = (input_width - input_height) * 0.5;
            int y_offset = 0;

            detectionResult.curCameraType.copy(curCameraType);
            /* 设置全局的相机遮挡状态 */
            detectionResult.isCameraCover = false;
            /* 存一下正式的识别阈值 */
            detectionResult.classThreshold = curClassThreshold;
            /* 算法的耗时 */
            detectionResult.detectUseTime =  detectTime;
            /* 车速 */
            detectionResult.speed = vehicleRealtimeStatus.speed;

            if (vehicleRealtimeStatus.speed < globalparams.respirator_speed)
            {
                init_global_params(respirator_timer);
            }
            if (vehicleRealtimeStatus.speed < globalparams.eye_speed)
            {
                init_global_params(eye_timer);
            }
            if (vehicleRealtimeStatus.speed < globalparams.mouth_speed)
            {
                init_global_params(mouth_timer);
            }
            if (vehicleRealtimeStatus.speed < globalparams.lookaround_speed)
            {
                init_global_params(yaw_timer);
            }

            /* 有人脸去走检测 */
            if (face_roi_info.state == 1) {

                //[1] 口罩状态识别，优先级仅次于人脸识别
                if ((globalparams.respirator_button == true) &&
                    (vehicleRealtimeStatus.speed > globalparams.respirator_speed)) {
                    int send_sign = 0; //有返回值时为异常状态
                    send_sign = Talarm::alarm_respirator(face_roi_info);
                    if (send_sign == 1) {
                        alarm_info.respirator_alarm = true;
                    }
//                    else
//                    {
//                        alarm_info.respirator_alarm = false;
//                    }

                }

                //[2]闭眼报警逻辑
                if ((globalparams.eye_button == true) && (vehicleRealtimeStatus.speed > globalparams.eye_speed)) {
                    int send_sign = 0; //有返回值时为异常状态
                    send_sign = Talarm::alarm_eye(face_roi_info);
                    if (eye_timer.alarm_state == true) {
//                         cout << "存在闭眼行为:（状态码1）" << eye_timer.alarm_state << endl;
                        //发送报警信息，并置零恢复状态
                        //eye_timer.alarm_state = false;
                    }
                    if (send_sign == 1) {
                        alarm_info.eye_alarm = true;
                    }
                }

                //[3]哈切报警逻辑
                if ((globalparams.mouth_button == true) && (vehicleRealtimeStatus.speed > globalparams.mouth_speed) &&
                    (alarm_info.respirator_alarm == false)) {
                    int send_sign = 0; //有返回值时为异常状态
                    send_sign = Talarm::alarm_mouth(face_roi_info);
                    //                    if (mouth_timer.alarm_state == true) {
//                        cout << "存在打哈欠行为:（状态码1）" << mouth_timer.alarm_state << endl;
//                        printf("++++++++++++++++++++++存在打哈欠行为:（状态码1）++++++++++++++++\n");
//                        //发送报警信息，并置零恢复状态
//                        //mouth_timer.alarm_state = false;
//                    }
                    if (send_sign == 1) {
                        alarm_info.mouth_alarm = true;

                    }

                }
                //[4]左顾右盼的报警逻辑
                if ((globalparams.lookaround_button == true) &&
                    (vehicleRealtimeStatus.speed > globalparams.lookaround_speed)) {
                    int send_sign = 0;
                    send_sign = Talarm::alarm_lookaround(face_roi_info);
//                    if (yaw_timer.alarm_state == true) {
////                        cout << "存在左顾右盼行为:（状态码1）" << eye_timer.alarm_state << endl;
//                         //发送报警信息，并置零恢复状态
//                        //yaw_timer.alarm_state = false;
//                    }
//                        alarm_info.lookaround_alarm = yaw_timer.alarm_state;

                    if (send_sign == 1) {
                        alarm_info.lookaround_alarm = true;

                    }
                }

                //[8] 抽烟识别报警
                //[9] 电话识别报警
                alarm_info.face_point[0] = Point2i(x_offset + alarm_info.face_point[0].x * ratio,
                                                   y_offset + alarm_info.face_point[0].y * ratio);
                alarm_info.face_point[1] = Point2i(x_offset + alarm_info.face_point[1].x * ratio,
                                                   y_offset + alarm_info.face_point[1].y * ratio);
                alarm_info.five_point[0] = Point2i(x_offset + alarm_info.five_point[0].x * ratio,
                                                   y_offset + alarm_info.five_point[0].y * ratio);
                alarm_info.five_point[1] = Point2i(x_offset + alarm_info.five_point[1].x * ratio,
                                                   y_offset + alarm_info.five_point[1].y * ratio);
                alarm_info.five_point[2] = Point2i(x_offset + alarm_info.five_point[2].x * ratio,
                                                   y_offset + alarm_info.five_point[2].y * ratio);
                alarm_info.five_point[3] = Point2i(x_offset + alarm_info.five_point[3].x * ratio,
                                                   y_offset + alarm_info.five_point[3].y * ratio);
                alarm_info.five_point[4] = Point2i(x_offset + alarm_info.five_point[4].x * ratio,
                                                   y_offset + alarm_info.five_point[4].y * ratio);


            } // state = 4  /2/0
            else {
//                    printf("alarm_facemissing facemissing_button:%d    :%d\n",globalparams.facemissing_button,globalparams.facemissing_speed);
                /* 没有人脸 */
                alarm_info.face_score = 0.0;
                alarm_info.face_point.clear();
                alarm_info.five_point.clear();
                /* 清掉报警 */
                alarm_info.respirator_alarm = false;
                alarm_info.eye_alarm = false;
                alarm_info.mouth_alarm = false;
                alarm_info.lookaround_alarm = false;
                alarm_info.facemissing_alarm = false;
                alarm_info.camcover_alarm = false;
                alarm_info.smoking_alarm = false;
                alarm_info.phone_alarm = false;
                alarm_info.fatigue_rank = 0;
                //状态清空
                if (respirator_timer.freeze_state == false) {
                    respirator_timer.ltime_idx.clear();
                    respirator_timer.recog_star_time = 0;
                    respirator_timer.freeze_state = false;
                }

                if (eye_timer.freeze_state == false) {
                    eye_timer.ltime_idx.clear();
                    eye_timer.recog_star_time = 0;
                    eye_timer.freeze_state = false;
                }

                if (mouth_timer.freeze_state == false) {
                    mouth_timer.ltime_idx.clear();
                    mouth_timer.recog_star_time = 0;
                    mouth_timer.freeze_state = false;
                }

                if (yaw_timer.freeze_state == false) {
                    yaw_timer.ltime_idx.clear();
                    yaw_timer.recog_star_time = 0;
                    yaw_timer.freeze_state = false;
                }


            }

            int camcover_sign = 0;
            //[5] 遮挡识别报警  CAMCOVERD
            if ((globalparams.camcover_button == true) &&
                (vehicleRealtimeStatus.speed > globalparams.camcover_speed))  //&& (face_roi_info.state == 2))
            {

                camcover_sign = Talarm::alarm_camcover(face_roi_info, frame);
//                    if (camcover_timer.alarm_state == true)
//                    {
//                        cout << "存在闭眼行为:（状态码1）" << camcover_timer.alarm_state << endl;
//                        //发送报警信息，并置零恢复状态
//                        //leave_timer.alarm_state = false; //可能需要生成报警事件：持续时间  频次  分布等信息
//                    }
                //alarm_info.camcover_alarm = camcover_timer.alarm_state;
                if (camcover_sign == 1) {
                    alarm_info.camcover_alarm = true;
                }
            }
            //[6]离岗识别报警
            if ((globalparams.facemissing_button == true) && (vehicleRealtimeStatus.speed > globalparams.facemissing_speed)&&(camcover_sign != 1)&&(face_params.state != 4)) {
                int send_sign = 0;

                send_sign = Talarm::alarm_facemissing(face_roi_info, frame);
                if (leave_timer.alarm_state == true) {
//                        cout << "存在离岗行为:（状态码1）" << leave_timer.alarm_state << endl;
                    //发送报警信息，并置零恢复状态
                    //leave_timer.alarm_state = false; //可能需要生成报警事件：持续时间  频次  分布等信息
                }
//                    alarm_info.facemissing_alarm = leave_timer.alarm_state;
                if (send_sign == 1) {
                    alarm_info.facemissing_alarm = true;

                }
            }

            //[7] 手势模块独立
            alarm_info.fatigue_rank = risk_level(alarm_info);


            /* callback 出去识别信息 */
            if (alarm_info.eye_alarm || alarm_info.mouth_alarm || alarm_info.lookaround_alarm ||
                alarm_info.facemissing_alarm
                || alarm_info.camcover_alarm || alarm_info.smoking_alarm || alarm_info.phone_alarm) {
                /* callback 出去报警报警信息 */
                curAlarmEventInfo.dsmAlarmInfo.respirator_alarm = alarm_info.respirator_alarm;
                curAlarmEventInfo.dsmAlarmInfo.eye_alarm = alarm_info.eye_alarm;
                curAlarmEventInfo.dsmAlarmInfo.mouth_alarm = alarm_info.mouth_alarm;
                curAlarmEventInfo.dsmAlarmInfo.lookaround_alarm = alarm_info.lookaround_alarm;
                curAlarmEventInfo.dsmAlarmInfo.facemissing_alarm = alarm_info.facemissing_alarm;
                curAlarmEventInfo.dsmAlarmInfo.camcover_alarm = alarm_info.camcover_alarm;
                curAlarmEventInfo.dsmAlarmInfo.smoking_alarm = alarm_info.smoking_alarm;
                curAlarmEventInfo.dsmAlarmInfo.phone_alarm = alarm_info.phone_alarm;
                curAlarmEventInfo.dsmAlarmInfo.fatigue_rank = alarm_info.fatigue_rank;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curDetectDataCallback->onGetDetectionEvent(curCameraType, EVENT_DSM, curAlarmEventInfo);


                // string eye_log = to_string(abs(etime)) + "_Alarm";
                // printLog::Write(eye_log);
            }



            /* 识别信息重新封装下再发出去 */
            detectionResult.faceList[0].face_score = alarm_info.face_score;
            detectionResult.faceList[0].respirator_alarm = alarm_info.respirator_alarm;
            detectionResult.faceList[0].eye_alarm = alarm_info.eye_alarm;
            detectionResult.faceList[0].mouth_alarm = alarm_info.mouth_alarm;
            detectionResult.faceList[0].lookaround_alarm = alarm_info.lookaround_alarm;
            detectionResult.faceList[0].facemissing_alarm = alarm_info.facemissing_alarm;
            detectionResult.faceList[0].camcover_alarm = alarm_info.camcover_alarm;
            detectionResult.faceList[0].smoking_alarm = alarm_info.smoking_alarm;
            detectionResult.faceList[0].phone_alarm = alarm_info.phone_alarm;
            detectionResult.faceList[0].fatigue_rank = alarm_info.fatigue_rank;
            detectionResult.faceList[0].five_point = alarm_info.five_point;
            detectionResult.faceList[0].face_point = alarm_info.face_point;

            curDetectDataCallback->onGetDSMInfo(detectionResult);

            if (alarm_info.eye_alarm == true) {
                // string eye_log = "eye_sss";
                // printLog::Write(eye_log);

                alarm_info.eye_alarm = false;
            }
            if (alarm_info.mouth_alarm == true) {
                alarm_info.mouth_alarm = false;
            }
            if (alarm_info.respirator_alarm == true) {
                alarm_info.respirator_alarm = false;
            }
            // yaw_timer 111
            if (alarm_info.lookaround_alarm == true) {
                alarm_info.lookaround_alarm = false;
            }
            if (alarm_info.camcover_alarm == true) {
                alarm_info.camcover_alarm = false;
            }
            if (alarm_info.facemissing_alarm == true) {
                alarm_info.facemissing_alarm = false;
            }

        } catch (...) {
            printf("VisAlarmDecision_MT_Face::parse_MT_DSM_OD has Exception! \n");
        }
    }
} // vis