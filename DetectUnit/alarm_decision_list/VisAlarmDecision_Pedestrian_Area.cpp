//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/10.
//

#include "VisAlarmDecision_Pedestrian_Area.h"
#include "XuTimeUtil.h"

namespace vis {
    VisAlarmDecision_Pedestrian_Area::VisAlarmDecision_Pedestrian_Area() {
        curAlarmDecisionType = ALARM_DECISION_TYPE_PEDESTRIAN_AREA;
        detectionResult.alarmDecisionType = curAlarmDecisionType;
    }

    void VisAlarmDecision_Pedestrian_Area::parse_UK_BSD_OD(td::objectInfo_t &curObjectInfo, Vehicle_RealtimeStatus &vehicleRealtimeStatus,td::objectInfo_t &curObjectInfo_notCheck,bool cameraCovered,float imgBrightness, bool isFullblack,uint16_t detectTime) {



        /* 根据是否进入安保模式确定是否需要进行识别 */
        if (vehicleRealtimeStatus.SecurityModeStatus) {
            canDetectPedestrian = false;
        } else {
            canDetectPedestrian = true;
        }

        /* 由于结构体里面没有裸指针，故直接用={}重置就好了 */
        curAlarmEventInfo = {};
        curAlarmEventInfo_shadow = {};
        detectionResult.bsdDetectionInfo.reset();
        detectionResult.curCameraType.copy(curCameraType);
        detectionResult.isReverse = vehicleRealtimeStatus.reverse;
        /* 看看是不是有遮挡或者被bypass */
        cameraCover = cameraCovered;
        detectionResult.isCameraCover = cameraCover;
        detectionResult.cameraFullBlack = isFullblack;
        isbypass = false;
        /* 存一下正式的识别阈值 */
        detectionResult.classThreshold = curClassThreshold;
        /* 算法的耗时 */
        detectionResult.detectUseTime =  detectTime;
        /* 报警决策类型和车速 */
        detectionResult.speed = vehicleRealtimeStatus.speed;
        detectionResult.alarmDecisionType = curAlarmDecisionType;
        switch (curCameraType.installPosition) {
            case INSTALL_POSITION_FRONT: {
                detectionResult.bsdDetectionInfo.cameraCoverStatus.forward = cameraCover;
                detectionResult.bsdDetectionInfo.bypassStatus.forward = isbypass;
            }
                break;
            case INSTALL_POSITION_BACK: {
                detectionResult.bsdDetectionInfo.cameraCoverStatus.backward = cameraCover;
                detectionResult.bsdDetectionInfo.bypassStatus.backward = isbypass;
            }
                break;
            case INSTALL_POSITION_LEFT: {
                detectionResult.bsdDetectionInfo.cameraCoverStatus.left = cameraCover;
                detectionResult.bsdDetectionInfo.bypassStatus.left = isbypass;
            }
                break;
            case INSTALL_POSITION_RIGHT: {
                detectionResult.bsdDetectionInfo.cameraCoverStatus.right = cameraCover;
                detectionResult.bsdDetectionInfo.bypassStatus.right = isbypass;
            }
                break;
        }

        hasPedestrianInAlarmAreaLevel1 = false;
        hasPedestrianInAlarmAreaLevel2 = false;
        lastAlaramYDistance_level1 = 999999;
        lastAlaramYDistance_level2 = 999999;

        int curAlarmLeve1PedestrianId = -1;
        int curAlarmLeve2PedestrianId = -1;

        int curAlarmLeve1PedestrianTrend = 0;
        int curAlarmLeve2PedestrianTrend = 0;

        /* 定义一下当前在报警区域内的行人ID的列表 */
        std::vector<uint32_t> curAlarmArea1IdList;
        std::vector<uint32_t> curAlarmArea2IdList;



        /* 影子相关的变量 */
        hasPedestrianInAlarmAreaLevel1_shadow = false;
        hasPedestrianInAlarmAreaLevel2_shadow = false;
        lastAlaramYDistance_level1_shadow = 999999;
        lastAlaramYDistance_level2_shadow = 999999;

        int curAlarmLeve1PedestrianId_shadow = -1;
        int curAlarmLeve2PedestrianId_shadow = -1;
        int curAlarmLeve1PedestrianTrend_shadow = 0;
        int curAlarmLeve2PedestrianTrend_shadow = 0;
        std::vector<uint32_t> curAlarmArea1IdList_shadow;
        std::vector<uint32_t> curAlarmArea2IdList_shadow;

        /* 获取一下报警区域 */
        getAlarmArea(vehicleRealtimeStatus);

        /* 先清理一下框 */
        curAlarmDecisionObjects.clear();

        /* 看看是否允许进行识别 */
        if(canDetectPedestrian){
            /* 允许识别就正常识别 */
            if (!curObjectInfo.objects.empty()) {
                for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {
                    if (strcmp("person", curObjectInfo.objects[i].label) == 0
                        || strcmp("cyclist", curObjectInfo.objects[i].label) == 0
                        || strcmp("motorcyclist", curObjectInfo.objects[i].label) == 0) {
                        /* 先把框的点整理  避免超出屏幕的框 */
                        curObjectInfo.objects[i].mLeft = (curObjectInfo.objects[i].mLeft < 0) ? 0
                                                                                              : ((curObjectInfo.objects[i].mLeft >
                                                                                                  1280) ? 1280
                                                                                                        : curObjectInfo.objects[i].mLeft);
                        curObjectInfo.objects[i].mTop = (curObjectInfo.objects[i].mTop < 0) ? 0
                                                                                            : ((curObjectInfo.objects[i].mTop >
                                                                                                720) ? 720
                                                                                                     : curObjectInfo.objects[i].mTop);
                        curObjectInfo.objects[i].mRight = (curObjectInfo.objects[i].mRight < 0) ? 0
                                                                                                : ((curObjectInfo.objects[i].mRight >
                                                                                                    1280) ? 1280
                                                                                                          : curObjectInfo.objects[i].mRight);
                        curObjectInfo.objects[i].mBottom = (curObjectInfo.objects[i].mBottom < 0) ? 0
                                                                                                  : ((curObjectInfo.objects[i].mBottom >
                                                                                                      720) ? 720

                                                                                                           : curObjectInfo.objects[i].mBottom);

//                    printf("i=%d  mScore=%f  mMaxScore=%f   mMinScore=%f curClassThreshold=%f \n",i,curObjectInfo.objects[i].mScore,curObjectInfo.objects[i].mMaxScore,curObjectInfo.objects[i].mMinScore,curClassThreshold);


                        /* 根据得分，看看是不是需不需走影子报警的流程 */
                        if((curObjectInfo.objects[i].mMaxScore >= curClassThreshold) && (curObjectInfo.objects[i].mMinScore >= (curClassThreshold / 2.0f))){
                            /* 大于等于正式阈值，不是影子 */

                            /* 判断下是否在一级区域内 */
                            if (isPedestrianInAlarmAreaLevel1(curObjectInfo.objects[i])) {
                                /* 把label改一下 */
                                std::string newlabel;
                                newlabel.append("A1-");
                                newlabel.append(curObjectInfo.objects[i].label);
                                memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                                memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());

                                hasPedestrianInAlarmAreaLevel1 = true;
                                /* 报警区域1内的行人的ID存起来 */
                                curAlarmArea1IdList.push_back(curObjectInfo.objects[i].mId);


                                /* 离得更近的话就用这个离得近的 */
                                if (lastAlaramYDistance_level1 >= curObjectInfo.objects[i].mYDistance) {
                                    curAlarmEventInfo.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                                    curAlarmEventInfo.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                                    curAlarmEventInfo.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                    curAlarmEventInfo.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                    lastAlaramYDistance_level1 = curObjectInfo.objects[i].mYDistance;
                                    curAlarmLeve1PedestrianId = curObjectInfo.objects[i].mId;
                                    curAlarmLeve1PedestrianTrend = curObjectInfo.objects[i].mTrend;

                                }
                            } else {
                                /* 如果没在1级区域那么判断下是否在二级区域内 */
                                if (isPedestrianInAlarmAreaLevel2(curObjectInfo.objects[i])) {
                                    /* 把label改一下 */
                                    std::string newlabel;
                                    newlabel.append("A2-");
                                    newlabel.append(curObjectInfo.objects[i].label);
                                    memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                                    memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());

                                    hasPedestrianInAlarmAreaLevel2 = true;
                                    /* 报警区域2内的行人的ID存起来 */
                                    curAlarmArea2IdList.push_back(curObjectInfo.objects[i].mId);

                                    /* 离得更近的话就用这个离得近的 */
                                    if (lastAlaramYDistance_level2 >= curObjectInfo.objects[i].mYDistance) {
                                        curAlarmEventInfo.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                                        curAlarmEventInfo.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                                        curAlarmEventInfo.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                        curAlarmEventInfo.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                        lastAlaramYDistance_level2 = curObjectInfo.objects[i].mYDistance;
                                        curAlarmLeve2PedestrianId = curObjectInfo.objects[i].mId;
                                        curAlarmLeve2PedestrianTrend = curObjectInfo.objects[i].mTrend;
                                    }
                                }
                            }

                        }else{
                            /* 小于正式阈值，是个影子 */

                            /* 判断下是否在一级区域内 */
                            if (isPedestrianInAlarmAreaLevel1(curObjectInfo.objects[i])) {
                                /* 把label改一下 */
                                std::string newlabel;
                                newlabel.append("A1-");
                                newlabel.append(curObjectInfo.objects[i].label);
                                memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                                memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());

                                hasPedestrianInAlarmAreaLevel1_shadow = true;
                                /* 报警区域1内的行人的ID存起来 */
                                curAlarmArea1IdList_shadow.push_back(curObjectInfo.objects[i].mId);


                                /* 离得更近的话就用这个离得近的 */
                                if (lastAlaramYDistance_level1_shadow >= curObjectInfo.objects[i].mYDistance) {
                                    curAlarmEventInfo_shadow.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                                    curAlarmEventInfo_shadow.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                                    curAlarmEventInfo_shadow.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                    curAlarmEventInfo_shadow.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                    lastAlaramYDistance_level1_shadow = curObjectInfo.objects[i].mYDistance;
                                    curAlarmLeve1PedestrianId_shadow = curObjectInfo.objects[i].mId;
                                    curAlarmLeve1PedestrianTrend_shadow = curObjectInfo.objects[i].mTrend;

                                }
                            } else {
                                /* 如果没在1级区域那么判断下是否在二级区域内 */
                                if (isPedestrianInAlarmAreaLevel2(curObjectInfo.objects[i])) {
                                    /* 把label改一下 */
                                    std::string newlabel;
                                    newlabel.append("A2-");
                                    newlabel.append(curObjectInfo.objects[i].label);
                                    memset(curObjectInfo.objects[i].label, 0x00, sizeof(curObjectInfo.objects[i].label));
                                    memcpy(curObjectInfo.objects[i].label, newlabel.c_str(), newlabel.size());

                                    hasPedestrianInAlarmAreaLevel2_shadow = true;
                                    /* 报警区域2内的行人的ID存起来 */
                                    curAlarmArea2IdList_shadow.push_back(curObjectInfo.objects[i].mId);

                                    /* 离得更近的话就用这个离得近的 */
                                    if (lastAlaramYDistance_level2_shadow >= curObjectInfo.objects[i].mYDistance) {
                                        curAlarmEventInfo_shadow.bsdAlarmInfo.yDistance = curObjectInfo.objects[i].mYDistance;
                                        curAlarmEventInfo_shadow.bsdAlarmInfo.xDistance = curObjectInfo.objects[i].mXDistance;
                                        curAlarmEventInfo_shadow.bsdAlarmInfo.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                        curAlarmEventInfo_shadow.bsdAlarmInfo.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                        lastAlaramYDistance_level2_shadow = curObjectInfo.objects[i].mYDistance;
                                        curAlarmLeve2PedestrianId_shadow = curObjectInfo.objects[i].mId;
                                        curAlarmLeve2PedestrianTrend_shadow = curObjectInfo.objects[i].mTrend;
                                    }
                                }
                            }
                        }
                        /* 是对应的框，直接添加进来 */
                        curAlarmDecisionObjects.push_back(curObjectInfo.objects[i]);
                    }
                }
            }

            /* 更新一下上一次报警行人ID */
            lastInAlarm1IdList.clear();
            lastInAlarm1IdList.insert(lastInAlarm1IdList.begin(), curAlarmArea1IdList.begin(), curAlarmArea1IdList.end());
            lastInAlarm2IdList.clear();
            lastInAlarm2IdList.insert(lastInAlarm2IdList.begin(), curAlarmArea2IdList.begin(), curAlarmArea2IdList.end());

            /* 更新一下上一次报警行人ID(影子) */
            lastInAlarm1IdList_shadow.clear();
            lastInAlarm1IdList_shadow.insert(lastInAlarm1IdList_shadow.begin(), curAlarmArea1IdList_shadow.begin(), curAlarmArea1IdList_shadow.end());
            lastInAlarm2IdList_shadow.clear();
            lastInAlarm2IdList_shadow.insert(lastInAlarm2IdList_shadow.begin(), curAlarmArea2IdList_shadow.begin(), curAlarmArea2IdList_shadow.end());



                /* 看看是否需要判断一下报警开关 */
//            if(checkCameraAlarmSwitch()) {
                parseEvent_level1(curAlarmLeve1PedestrianId, curAlarmLeve1PedestrianTrend,curAlarmLeve1PedestrianId_shadow, curAlarmLeve1PedestrianTrend_shadow, vehicleRealtimeStatus);
//            }



                /* 看看是否需要判断一下报警开关 */
//            if(checkCameraAlarmSwitch()) {
                parseEvent_level2(curAlarmLeve2PedestrianId, curAlarmLeve2PedestrianTrend,curAlarmLeve2PedestrianId_shadow, curAlarmLeve2PedestrianTrend_shadow, vehicleRealtimeStatus);
//            }




            /* 清理掉旧的框  把整理过的框塞进去 */
            td::objectInfo_t curPedestrianObjectInfo = curObjectInfo;
            curPedestrianObjectInfo.objects.clear();

            /* 整理识别框（同时也进行降效那些） */
            organizeObjectInfoToShow(curPedestrianObjectInfo,curAlarmDecisionObjects,curObjectInfo_notCheck.objects);

            /* 作为G4-GJ中的G3部分就需要根据亮度判断是否开灯 */
            if(G3_Configuration::getInstance().getG3DeviceWorkingMode() ==  G3WORKMODE_G4GJ_UNIT_G3 || G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4GJ_G4_UNIT){
                /* 亮度低于10就开灯 */
                if(imgBrightness >= 0 && imgBrightness < 10){
                    detectionResult.lampType = 0;
                    detectionResult.isLampOpen = true;
//                printf("open lamp! \n");
                }

            }


            /* 把识别信息发出去 */
            curDetectDataCallback->onGetObjectInfo_BSD(curPedestrianObjectInfo, detectionResult);
        }else{
            /* 不允许识别，那么就直接发个空的 */
            td::objectInfo_t curPedestrianObjectInfo = {};
            /* 把识别信息发出去 */
            curDetectDataCallback->onGetObjectInfo_BSD(curPedestrianObjectInfo, detectionResult);
        }

    }

    void VisAlarmDecision_Pedestrian_Area::getAlarmArea(Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        switch (curCameraId) {
            case CAMERA_ID_1: {
                bool needChange = G3_Configuration::getInstance().getPedestrianAlarmNeedChangeByTurnSignCamera0() == 1;
                int minSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMinSpeed();
                int maxSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMaxSpeed();
                /* 如果需要变化报警区域  打了转向灯  且到达了制定速度 就需要变化报警区域   (两边同时打的这种情况不需要变化区域)  */
                if (needChange && vehicleRealtimeStatus.speed >= minSpeed && vehicleRealtimeStatus.speed <= maxSpeed &&
                    (vehicleRealtimeStatus.turnR || vehicleRealtimeStatus.turnL) &&
                    !(vehicleRealtimeStatus.turnR && vehicleRealtimeStatus.turnL)) {
                    if (vehicleRealtimeStatus.turnL) {

                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera1TurnleftAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera1TurnleftAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);

                    } else {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera1TurnrightAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera1TurnrightAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);
                    }

                } else {
                    /* 设置报警区域1的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1(),
                            alarmAreaPointList_level1,
                            6);

                    /* 设置报警区域2的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2(),
                            alarmAreaPointList_level2,
                            6);
                }


            }
                break;

            case CAMERA_ID_2: {
                bool needChange = G3_Configuration::getInstance().getPedestrianAlarmNeedChangeByTurnSignCamera1() == 1;
                int minSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMinSpeed();
                int maxSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMaxSpeed();
                /* 如果需要变化报警区域  打了转向灯  且到达了制定速度 就需要变化报警区域   (两边同时打的这种情况不需要变化区域)  */
                if (needChange && vehicleRealtimeStatus.speed >= minSpeed && vehicleRealtimeStatus.speed <= maxSpeed &&
                    (vehicleRealtimeStatus.turnR || vehicleRealtimeStatus.turnL) &&
                    !(vehicleRealtimeStatus.turnR && vehicleRealtimeStatus.turnL)) {

                    if (vehicleRealtimeStatus.turnL) {

                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera2TurnleftAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera2TurnleftAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);

                    } else {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera2TurnrightAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera2TurnrightAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);
                    }


                } else {
                    /* 设置报警区域1的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1(),
                            alarmAreaPointList_level1,
                            6);

                    /* 设置报警区域2的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2(),
                            alarmAreaPointList_level2,
                            6);
                }
            }
                break;

            case CAMERA_ID_3: {
                bool needChange = G3_Configuration::getInstance().getPedestrianAlarmNeedChangeByTurnSignCamera2() == 1;
                int minSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMinSpeed();
                int maxSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMaxSpeed();
                /* 如果需要变化报警区域  打了转向灯  且到达了制定速度 就需要变化报警区域   (两边同时打的这种情况不需要变化区域)  */
                if (needChange && vehicleRealtimeStatus.speed >= minSpeed && vehicleRealtimeStatus.speed <= maxSpeed &&
                    (vehicleRealtimeStatus.turnR || vehicleRealtimeStatus.turnL) &&
                    !(vehicleRealtimeStatus.turnR && vehicleRealtimeStatus.turnL)) {
                    if (vehicleRealtimeStatus.turnL) {

                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera3TurnleftAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera3TurnleftAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);

                    } else {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera3TurnrightAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera3TurnrightAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);
                    }

                } else {
                    /* 设置报警区域1的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera2PedestrianAlramAreaLevel1(),
                            alarmAreaPointList_level1,
                            6);

                    /* 设置报警区域2的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera2PedestrianAlramAreaLevel2(),
                            alarmAreaPointList_level2,
                            6);
                }


            }
                break;

            case CAMERA_ID_4: {
                bool needChange = G3_Configuration::getInstance().getPedestrianAlarmNeedChangeByTurnSignCamera3() == 1;
                int minSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMinSpeed();
                int maxSpeed = G3_Configuration::getInstance().getSbstChangePedestrianAlramAreaByTurnlightMaxSpeed();
                /* 如果需要变化报警区域  打了转向灯  且到达了制定速度 就需要变化报警区域   (两边同时打的这种情况不需要变化区域)  */
                if (needChange && vehicleRealtimeStatus.speed >= minSpeed && vehicleRealtimeStatus.speed <= maxSpeed &&
                    (vehicleRealtimeStatus.turnR || vehicleRealtimeStatus.turnL) &&
                    !(vehicleRealtimeStatus.turnR && vehicleRealtimeStatus.turnL)) {

                    if (vehicleRealtimeStatus.turnL) {

                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera4TurnleftAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera4TurnleftAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);

                    } else {
                        /* 设置报警区域1的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera4TurnrightAngle1Level1(),
                                alarmAreaPointList_level1,
                                6);

                        /* 设置报警区域2的点 */
                        pointVectorPointsToPointArrays(
                                G3_Configuration::getInstance().getSbstPedestrianAlramAreaCamera4TurnrightAngle1Level2(),
                                alarmAreaPointList_level2,
                                6);
                    }


                } else {
                    /* 设置报警区域1的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera3PedestrianAlramAreaLevel1(),
                            alarmAreaPointList_level1,
                            6);

                    /* 设置报警区域2的点 */
                    pointVectorPointsToPointArrays(
                            G3_Configuration::getInstance().getCamera3PedestrianAlramAreaLevel2(),
                            alarmAreaPointList_level2,
                            6);
                }
            }
                break;



        }
        /* 获取下最终使用的报警区域的的点（是不是降效都在里面实现了） */
        getUltimateAlarmAreaPointList(alarmAreaPointList_level1, 6);
        getUltimateAlarmAreaPointList(alarmAreaPointList_level2, 6);

    }

    bool VisAlarmDecision_Pedestrian_Area::isPedestrianInAlarmAreaLevel1(td::object_ &pedestrianInfo) {
        /* 判断下一级区域是否有设置（前面两个点不为0） */
        if (alarmAreaPointList_level1[0].x == 0 && alarmAreaPointList_level1[0].y == 0 &&
            alarmAreaPointList_level1[1].x == 0 && alarmAreaPointList_level1[1].y == 0) {
            return false;
        }
        VISPoint pedestrianPOints[4];
        bool isIn = false;

        pedestrianPOints[0].x = pedestrianInfo.mLeft;
        pedestrianPOints[0].y = pedestrianInfo.mTop;

        pedestrianPOints[1].x = pedestrianInfo.mRight;
        pedestrianPOints[1].y = pedestrianInfo.mTop;

        pedestrianPOints[2].x = pedestrianInfo.mRight;
        pedestrianPOints[2].y = pedestrianInfo.mBottom;

        pedestrianPOints[3].x = pedestrianInfo.mLeft;
        pedestrianPOints[3].y = pedestrianInfo.mBottom;

        /* 判断下是否在报警区域内 */
        isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                         alarmAreaPointList_level1, 6);
        /* 如果没在报警区域内容  那么判断下上次的识别结果中 他是否在报警区域内容 */
        if (!isIn) {
            bool isLastIn = false;
            for (std::size_t i = 0; i < lastInAlarm1IdList.size(); i++) {
                if (lastInAlarm1IdList[i] == pedestrianInfo.mId) {
                    isLastIn = true;
                    break;
                }
            }
            /* 如果上次有在报警区域  那么这次得不在退出区域才能算退出了 */
            if (isLastIn) {

                /* 设置报警区域1的退出区域的点 */
                XuCalculationTool::getInstance().polygonIsometricScaling2(alarmAreaPointList_level1,
                                                                          alarmAreaPointList_level1_exit,
                                                                          ALARM_EXIT_AREA_DIST);
                bool inInExit = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(
                        pedestrianPOints,
                        4,
                        alarmAreaPointList_level1_exit,
                        6);
                isIn = inInExit;
            }
        }

        return isIn;
    }

    bool VisAlarmDecision_Pedestrian_Area::isPedestrianInAlarmAreaLevel2(td::object_ &pedestrianInfo) {
        /* 判断下二级区域是否有设置（前面两个点不为0） */
        if (alarmAreaPointList_level2[0].x == 0 && alarmAreaPointList_level2[0].y == 0 &&
            alarmAreaPointList_level2[1].x == 0 && alarmAreaPointList_level2[1].y == 0) {
            return false;
        }


        VISPoint pedestrianPOints[4];
        bool isIn = false;

        pedestrianPOints[0].x = pedestrianInfo.mLeft;
        pedestrianPOints[0].y = pedestrianInfo.mTop;

        pedestrianPOints[1].x = pedestrianInfo.mRight;
        pedestrianPOints[1].y = pedestrianInfo.mTop;

        pedestrianPOints[2].x = pedestrianInfo.mRight;
        pedestrianPOints[2].y = pedestrianInfo.mBottom;

        pedestrianPOints[3].x = pedestrianInfo.mLeft;
        pedestrianPOints[3].y = pedestrianInfo.mBottom;


        /* 判断下是否在报警区域内 */
        isIn = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(pedestrianPOints, 4,
                                                                                         alarmAreaPointList_level2, 6);

        /* 如果没在报警区域内容  那么判断下上次的识别结果中 他是否在报警区域内容 */
        if (!isIn) {
            bool isLastIn = false;
            for (std::size_t i = 0; i < lastInAlarm2IdList.size(); i++) {
                if (lastInAlarm2IdList[i] == pedestrianInfo.mId) {
                    isLastIn = true;
                    break;
                }
            }
            /* 如果上次有在报警区域  那么这次得不在退出区域才能算退出了 */
            if (isLastIn) {
                /* 设置报警区域2的退出区域的点 */
                XuCalculationTool::getInstance().polygonIsometricScaling2(alarmAreaPointList_level2,
                                                                          alarmAreaPointList_level2_exit,
                                                                          ALARM_EXIT_AREA_DIST);
                bool inInExit = XuCalculationTool::getInstance().isPolygon1AndPolygon2HaveOverlappingArea(
                        pedestrianPOints,
                        4,
                        alarmAreaPointList_level2_exit,
                        6);
                isIn = inInExit;
            }
        }
        return isIn;
    }

    void VisAlarmDecision_Pedestrian_Area::parseEvent_level1(const uint32_t curAlaramId, const uint32_t curPedestrianTrend,const uint32_t curAlaramId_shadow, const uint32_t curPedestrianTrend_shadow,Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
//        printf("hasPedestrianInAlarmAreaLevel1=%d     hasPedestrianInAlarmAreaLevel1_shadow=%d    curAlaramId_shadow=%d    curPedestrianTrend_shadow=%d \n", hasPedestrianInAlarmAreaLevel1, hasPedestrianInAlarmAreaLevel1_shadow,curAlaramId_shadow,curPedestrianTrend_shadow);
        if (hasPedestrianInAlarmAreaLevel1) {
            bool needToSendAlarmEvent = true;
            int eventCode = EVENT_UNKNOW;

            /* 根据安装装位置确定这个报警应该转成什么类型的 */
            switch (curCameraType.installPosition) {
                case INSTALL_POSITION_LEFT: {
                    /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_RIGHT: {
                    /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_FRONT:
                case INSTALL_POSITION_BACK: {
                    /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.forward_level1 = true;
                            eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level1 = true;
                            eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level1 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level1 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_DOWNWARD: {
                            if (curCameraType.installPosition == INSTALL_POSITION_FRONT) {
                                curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                                detectionResult.bsdDetectionInfo.pedestrianStatus.forward_level1 = true;
                                eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                            } else {
                                curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                                detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level1 = true;
                                eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                            }
                        }
                            break;
                    }
                }
                    break;

            }



            /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
            if (needToSendAlarmEvent) {

                /* 判断是否在需要报警的车速区间内,不在就不需要报警 */
                if (vehicleRealtimeStatus.speed < G3_Configuration::getInstance().getBsdMinAlramSpeedLevel1() &&
                    vehicleRealtimeStatus.speed > G3_Configuration::getInstance().getBsdMaxAlramSpeedLevel1()) {
                    return;
                }

                /* 看看是否需要启动同ID过滤   */
                if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel1() == 0 &&
                    lastAlaramId_level1 == curAlaramId) {
//                    printf("lastAlaramId_level1=%d    curAlaramId=%d   \n", lastAlaramId_level1, curAlaramId);
                    return;
                }
                /* 看看是否需要启动趋势过滤 */

                /* 如果打开了趋势  没识别出趋势  直接不报 */
                if (((G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_static) ||
                     (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_far_to_near) ||
                     (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_near_to_far)) &&
                    curPedestrianTrend == TD_TREND_DEFAULT) {
                    printf("curPedestrianTrend == TD_TREND_DEFAULT, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要过滤掉禁止的行人 */
                if (curPedestrianTrend == TD_TREND_STATIC &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_static)) {
                    printf("curPedestrianTrend == TD_TREND_STATIC, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要过滤掉靠近相机的行人 */
                if (curPedestrianTrend == TD_TREND_FAR_TO_NEAR &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_far_to_near)) {
                    printf("curPedestrianTrend == TD_TREND_FAR_TO_NEAR, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要过滤掉远离相机的行人 */
                if (curPedestrianTrend == TD_TREND_NEAR_TO_FAR &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_near_to_far)) {
                    printf("curPedestrianTrend == TD_TREND_NEAR_TO_FAR, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要判断车门信号过滤 */
                if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                    (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                    printf("Doors is opened,did not alarm! \n");
                    return;
                }

                lastAlaramId_level1 = curAlaramId;
                curAlarmEventInfo.bsdAlarmInfo.alarmPedestrianId = curAlaramId;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curAlarmEventInfo.isShadow = false;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, curAlarmEventInfo);

//                printf("*******************curDetectDataCallback->onGetDetectionEvent  eventCode=%d  alarmPedestrianId = %d\n",
//                       eventCode, curAlaramId);
            }
        }else{
            /* 区域1没有人了，那么就清理一下区域1的上一次报警的行人ID */
            lastAlaramId_level1 = -1;
        }

        /* 判断下影子 */
        if (hasPedestrianInAlarmAreaLevel1_shadow) {
            bool needToSendAlarmEvent = true;
            int eventCode = EVENT_UNKNOW;

            /* 根据安装装位置确定这个报警应该转成什么类型的 */
            switch (curCameraType.installPosition) {
                case INSTALL_POSITION_LEFT: {
                    /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_RIGHT: {
                    /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_FRONT:
                case INSTALL_POSITION_BACK: {
                    /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1;
                        }
                            break;

                        case CAMERA_ORIENTATION_DOWNWARD: {
                            if (curCameraType.installPosition == INSTALL_POSITION_FRONT) {
                                curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                                eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1;
                            } else {
                                curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                                eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1;
                            }
                        }
                            break;
                    }
                }
                    break;

            }


            /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
            if (needToSendAlarmEvent) {

                /* 判断是否在需要报警的车速区间内,不在就不需要报警 */
                if (vehicleRealtimeStatus.speed < G3_Configuration::getInstance().getBsdMinAlramSpeedLevel1() &&
                    vehicleRealtimeStatus.speed > G3_Configuration::getInstance().getBsdMaxAlramSpeedLevel1()) {
                    return;
                }

                /* 看看是否需要启动同ID过滤   */
                if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel1() == 0 &&
                    lastAlaramId_level1_shadow == curAlaramId_shadow) {
                    return;
                }
                /* 看看是否需要启动趋势过滤 */

                /* 如果打开了趋势  没识别出趋势  直接不报 */
                if (((G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_static) ||
                     (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_far_to_near) ||
                     (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_near_to_far)) &&
                    curPedestrianTrend_shadow == TD_TREND_DEFAULT) {
                    printf("curPedestrianTrend == TD_TREND_DEFAULT, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要过滤掉禁止的行人 */
                if (curPedestrianTrend_shadow == TD_TREND_STATIC &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_static)) {
                    printf("curPedestrianTrend == TD_TREND_STATIC, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要过滤掉靠近相机的行人 */
                if (curPedestrianTrend_shadow == TD_TREND_FAR_TO_NEAR &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_far_to_near)) {
                    printf("curPedestrianTrend == TD_TREND_FAR_TO_NEAR, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要过滤掉远离相机的行人 */
                if (curPedestrianTrend_shadow == TD_TREND_NEAR_TO_FAR &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel1().trend_near_to_far)) {
                    printf("curPedestrianTrend == TD_TREND_NEAR_TO_FAR, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要判断车门信号过滤 */
                if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                    (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                    printf("Doors is opened,did not alarm! \n");
                    return;
                }

                lastAlaramId_level1_shadow = curAlaramId_shadow;
                curAlarmEventInfo_shadow.bsdAlarmInfo.alarmPedestrianId = curAlaramId_shadow;
                curAlarmEventInfo_shadow.bsdAlarmInfo.alarmPedestrianId = curAlaramId_shadow;
                curAlarmEventInfo_shadow.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curAlarmEventInfo_shadow.isShadow = true;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, curAlarmEventInfo_shadow);
//              printf("********************curDetectDataCallback->onGetDetectionEvent  eventCode_shadow=%d  alarmPedestrianId_shadow = %d\n",eventCode, curAlaramId_shadow);

            }
        }else{
            /* 区域1没有人了，那么就清理一下区域1的上一次报警的行人ID */
            lastAlaramId_level1_shadow = -1;
        }





    }

    void VisAlarmDecision_Pedestrian_Area::parseEvent_level2(const uint32_t curAlaramId, const uint32_t curPedestrianTrend,const uint32_t curAlaramId_shadow, const uint32_t curPedestrianTrend_shadow,Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        if (hasPedestrianInAlarmAreaLevel2) {
            bool needToSendAlarmEvent = true;
            int eventCode = EVENT_UNKNOW;

            /* 根据安装装位置确定这个报警应该转成什么类型的 */
            switch (curCameraType.installPosition) {
                case INSTALL_POSITION_LEFT: {
                    /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_RIGHT: {
                    /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;


                    }
                }
                    break;

                case INSTALL_POSITION_FRONT:
                case INSTALL_POSITION_BACK: {
                    /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.forward_level2 = true;
                            eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level2 = true;
                            eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.left_level2 = true;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                            detectionResult.bsdDetectionInfo.pedestrianStatus.right_level2 = true;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD: {
                            if (curCameraType.installPosition == INSTALL_POSITION_FRONT) {
                                curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                                detectionResult.bsdDetectionInfo.pedestrianStatus.forward_level2 = true;
                                eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                            } else {
                                curAlarmEventInfo.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                                detectionResult.bsdDetectionInfo.pedestrianStatus.backward_level2 = true;
                                eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                            }
                        }
                            break;
                    }
                }
                    break;

            }
            /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
            if (needToSendAlarmEvent) {

                /* 判断是否在需要报警的车速区间内,不在就不需要报警 */
                if (vehicleRealtimeStatus.speed < G3_Configuration::getInstance().getBsdMinAlramSpeedLevel2() &&
                    vehicleRealtimeStatus.speed > G3_Configuration::getInstance().getBsdMaxAlramSpeedLevel2()) {
                    return;
                }

                /* 看看是否需要启动同ID过滤   */
                if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel2() == 0 &&
                    lastAlaramId_level2 == curAlaramId) {
                    printf("lastAlaramId_level2=%d    curAlaramId=%d   \n", lastAlaramId_level2, curAlaramId);
                    return;
                }
                /* 看看是否需要启动趋势过滤 */
                /* 如果打开了趋势  如果没识别出趋势  直接不报 */
                if (((G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_static) ||
                     (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_far_to_near) ||
                     (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_near_to_far)) &&
                    curPedestrianTrend == TD_TREND_DEFAULT) {
                    printf("curPedestrianTrend == TD_TREND_DEFAULT, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要过滤掉禁止的行人 */
                if (curPedestrianTrend == TD_TREND_STATIC &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_static)) {
                    return;
                }

                /* 看看是否需要过滤掉靠近相机的行人 */
                if (curPedestrianTrend == TD_TREND_FAR_TO_NEAR &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_far_to_near)) {
                    return;
                }

                /* 看看是否需要过滤掉远离相机的行人 */
                if (curPedestrianTrend == TD_TREND_NEAR_TO_FAR &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_near_to_far)) {
                    return;
                }



                /* 看看是否需要判断车门信号过滤 */
                if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                    (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                    printf("Doors is opened,did not alarm! \n");
                    return;
                }


                lastAlaramId_level2 = curAlaramId;
                curAlarmEventInfo.bsdAlarmInfo.alarmPedestrianId = curAlaramId;
                curAlarmEventInfo.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curAlarmEventInfo.isShadow = false;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, curAlarmEventInfo);

//                printf("*******************curDetectDataCallback->onGetDetectionEvent  eventCode=%d  curAlaramId=%d \n",
//                       eventCode, curAlaramId);
            }


        }else{
            /* 区域2没有人了，那么就清理一下区域2的上一次报警的行人ID */
            lastAlaramId_level2 = -1;
        }

        if (hasPedestrianInAlarmAreaLevel2_shadow) {
            bool needToSendAlarmEvent = true;
            int eventCode = EVENT_UNKNOW;

            /* 根据安装装位置确定这个报警应该转成什么类型的 */
            switch (curCameraType.installPosition) {
                case INSTALL_POSITION_LEFT: {
                    /* 确定安装位置是左边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                    }
                }
                    break;


                case INSTALL_POSITION_RIGHT: {
                    /* 确定安装位置是右边  那么看看朝向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD:
                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;


                    }
                }
                    break;

                case INSTALL_POSITION_FRONT:
                case INSTALL_POSITION_BACK: {
                    /* 确定安装位置是前面或者后面  那么看看朝向  朝哪个方向就算哪个方向  确定到底应该是哪个报警 */
                    switch (curCameraType.cameraOrientation) {
                        case CAMERA_ORIENTATION_FORWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_BACKWARD: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_LEFT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;

                        case CAMERA_ORIENTATION_RIGHT: {
                            curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                            eventCode = EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2;
                        }
                            break;
                        case CAMERA_ORIENTATION_DOWNWARD: {
                            if (curCameraType.installPosition == INSTALL_POSITION_FRONT) {
                                curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                                eventCode = EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2;
                            } else {
                                curAlarmEventInfo_shadow.bsdAlarmInfo.eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                                eventCode = EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2;
                            }
                        }
                            break;
                    }
                }
                    break;

            }
            /* 看看是否有对应的报警类型  有才能发送（主要防止错误启动） */
            if (needToSendAlarmEvent) {

                /* 判断是否在需要报警的车速区间内,不在就不需要报警 */
                if (vehicleRealtimeStatus.speed < G3_Configuration::getInstance().getBsdMinAlramSpeedLevel2() &&
                    vehicleRealtimeStatus.speed > G3_Configuration::getInstance().getBsdMaxAlramSpeedLevel2()) {
                    return;
                }

                /* 看看是否需要启动同ID过滤   */
                if (G3_Configuration::getInstance().getBsdSameIdAlramTypeLevel2() == 0 &&
                    lastAlaramId_level2_shadow == curAlaramId_shadow) {
                    printf("lastAlaramId_level2_shadow=%d    curAlaramId_shadow=%d   \n", lastAlaramId_level2_shadow, curAlaramId_shadow);
                    return;
                }
                /* 看看是否需要启动趋势过滤 */
                /* 如果打开了趋势  如果没识别出趋势  直接不报 */
                if (((G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_static) ||
                     (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_far_to_near) ||
                     (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_near_to_far)) &&
                    curPedestrianTrend_shadow == TD_TREND_DEFAULT) {
                    printf("curPedestrianTrend == TD_TREND_DEFAULT, did not need send alarm. \n");
                    return;
                }

                /* 看看是否需要过滤掉禁止的行人 */
                if (curPedestrianTrend_shadow == TD_TREND_STATIC &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_static)) {
                    return;
                }

                /* 看看是否需要过滤掉靠近相机的行人 */
                if (curPedestrianTrend_shadow == TD_TREND_FAR_TO_NEAR &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_far_to_near)) {
                    return;
                }

                /* 看看是否需要过滤掉远离相机的行人 */
                if (curPedestrianTrend_shadow == TD_TREND_NEAR_TO_FAR &&
                    (G3_Configuration::getInstance().getBsdAlarmTrendShieldingLevel2().trend_near_to_far)) {
                    return;
                }



                /* 看看是否需要判断车门信号过滤 */
                if ((G3_Configuration::getInstance().getPedestrianAlarmNeedDoorSign() == 1) &&
                    (vehicleRealtimeStatus.backDoor || vehicleRealtimeStatus.frontDoor)) {
                    printf("Doors is opened,did not alarm! \n");
                    return;
                }


                lastAlaramId_level2_shadow = curAlaramId_shadow;
                curAlarmEventInfo_shadow.bsdAlarmInfo.alarmPedestrianId = curAlaramId_shadow;
                curAlarmEventInfo_shadow.alarmTime = XuTimeUtil::getInstance().currentTimeMillis();
                curAlarmEventInfo_shadow.isShadow = true;
                curDetectDataCallback->onGetDetectionEvent(curCameraType, eventCode, curAlarmEventInfo_shadow);
            }


        }else{
            /* 区域2没有人了，那么就清理一下区域2的上一次报警的行人ID */
            lastAlaramId_level2_shadow = -1;
        }














    }

    void VisAlarmDecision_Pedestrian_Area::parse_SBST_160_SEMANTIC_SEGMENTATION(das::objectInfo_t &curObjectInfo,
                                                                                Vehicle_RealtimeStatus &vehicleRealtimeStatus,uint16_t detectTime) {
        /* 是语义分割的，那么先把maskbuf复制过去 */
        detectionResult.maskBufWidth = DAS_MASK_WIDTH;
        detectionResult.maskBufHeight = DAS_MASK_HEIGHT;
        detectionResult.maskBufLen = sizeof(curObjectInfo.maskBuf);
        (void) memcpy(detectionResult.maskBuf, curObjectInfo.maskBuf, detectionResult.maskBufLen);

        /* 先定义一个td下的objectinfo，用来后续调用parse_UK_BSD_OD进行是否在报警区域内的检测的 */
        td::objectInfo_t tdbjectInfo_check;
        /* 定义一个td下的objectinfo，用来后续调用parse_UK_BSD_OD，这不需要进行检测，只是输出框 */
        td::objectInfo_t tdbjectInfo_notCheck;
        /* 定义一个新加坡用的黄色实线对象，用来做公交站的行人过滤 */
        das::object_t stationline;

        /* 遍历拿到的object信息，把人拿出去解析 */
        if (!curObjectInfo.objects.empty()) {
            /* 先把报警信息复制过去 */
            memcpy(&tdbjectInfo_check.alarmInfo, &curObjectInfo.alarmInfo, sizeof(curObjectInfo.alarmInfo));
            /* 再把故障信息复制过去 */
            memcpy(&tdbjectInfo_check.faultInfo, &curObjectInfo.faultInfo, sizeof(curObjectInfo.faultInfo));

            /* 先找下黄色实线，因为他是判断是否在可行驶区域内的基础条件之一 */
            for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {
                /* 不是行人，那么需要看下是不是黄色实线 */
                if((strcmp(curObjectInfo.objects[i].label, "stationline") == 0)){
                    stationline = curObjectInfo.objects[i];
                }
            }



            /* 再把Object信息给复制过去 */
            for (std::size_t i = 0; i < curObjectInfo.objects.size(); i++) {
                /* 把行人挑出来，给行人信息的识别类去识别 */
                if ((strcmp(curObjectInfo.objects[i].label, "person") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "cyclist") == 0) ||
                    (strcmp(curObjectInfo.objects[i].label, "motorcyclist") == 0) ||
                            (strcmp(curObjectInfo.objects[i].label, "stationline") == 0)) {

                    if(strcmp(curObjectInfo.objects[i].label, "stationline") == 0){
                        /* 转换一下 */
                        td::object_t objectTemp;
                        memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                               sizeof(curObjectInfo.objects[i].label));
                        objectTemp.mId = curObjectInfo.objects[i].mId;
                        objectTemp.mTop = curObjectInfo.objects[i].mTop;
                        objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                        objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                        objectTemp.mRight = curObjectInfo.objects[i].mRight;
                        objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                        objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                        objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                        objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                        objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                        objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                        objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                        objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                        objectTemp.mScore = curObjectInfo.objects[i].mScore;
                        /* 最大跟最小得分都设置成跟得分一样 */
                        objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                        objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                        /* 添加进去 */
                        tdbjectInfo_notCheck.objects.push_back(objectTemp);
                        continue;

                    }


                    /* 判断下是否需要在可行驶区域内的人才报警  如果是的话  只有才可行驶区域的人才会送去报警决策 */
                    if (G3_Configuration::getInstance().getPedestrianAlarmNeedTravelableArea() == 1) {
                        /* 只有在可行驶区域内容的才可以去到报警决策 */
                        if (checkPedestrianInsideRoad(curObjectInfo.objects[i],stationline,vehicleRealtimeStatus)) {
                            /* 转换一下 */
                            td::object_t objectTemp;
                            memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                                   sizeof(curObjectInfo.objects[i].label));
                            objectTemp.mId = curObjectInfo.objects[i].mId;
                            objectTemp.mTop = curObjectInfo.objects[i].mTop;
                            objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                            objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                            objectTemp.mRight = curObjectInfo.objects[i].mRight;
                            objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                            objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                            objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                            objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                            objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                            objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                            objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                            objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                            objectTemp.mScore = curObjectInfo.objects[i].mScore;
                            /* 最大跟最小得分都设置成跟得分一样 */
                            objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                            objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                            /* 添加进去 */
                            tdbjectInfo_check.objects.push_back(objectTemp);
                        } else {
                            /* 不在可行驶区域内的需要放在另一个地方，该地方不要进行报警判断，但是需要直接输出出去 */
                            /* 转换一下 */
                            td::object_t objectTemp;
                            memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                                   sizeof(curObjectInfo.objects[i].label));
                            objectTemp.mId = curObjectInfo.objects[i].mId;
                            objectTemp.mTop = curObjectInfo.objects[i].mTop;
                            objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                            objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                            objectTemp.mRight = curObjectInfo.objects[i].mRight;
                            objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                            objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                            objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                            objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                            objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                            objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                            objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                            objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                            objectTemp.mScore = curObjectInfo.objects[i].mScore;
                            /* 最大跟最小得分都设置成跟得分一样 */
                            objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                            objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                            /* 添加进去 */
                            tdbjectInfo_notCheck.objects.push_back(objectTemp);
                        }
                    } else {
                        if(curCameraType.algType == ALGORITHM_TYPE_ADAS_NORMAL){
                            if(checkHeadway(curObjectInfo.objects[i])){
                                /* 转换一下 */
                                td::object_t objectTemp;
                                memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                                       sizeof(curObjectInfo.objects[i].label));
                                objectTemp.mId = curObjectInfo.objects[i].mId;
                                objectTemp.mTop = curObjectInfo.objects[i].mTop;
                                objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                                objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                                objectTemp.mRight = curObjectInfo.objects[i].mRight;
                                objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                                objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                                objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                                objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                                objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                                objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                                objectTemp.mScore = curObjectInfo.objects[i].mScore;
                                /* 最大跟最小得分都设置成跟得分一样 */
                                objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                                objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                                /* 添加进去 */
                                tdbjectInfo_check.objects.push_back(objectTemp);
                            }else{
                                /* 不在可行驶区域内的需要放在另一个地方，该地方不要进行报警判断，但是需要直接输出出去 */
                                /* 转换一下 */
                                td::object_t objectTemp;
                                memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                                       sizeof(curObjectInfo.objects[i].label));
                                objectTemp.mId = curObjectInfo.objects[i].mId;
                                objectTemp.mTop = curObjectInfo.objects[i].mTop;
                                objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                                objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                                objectTemp.mRight = curObjectInfo.objects[i].mRight;
                                objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                                objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                                objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                                objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                                objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                                objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                                objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                                objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                                objectTemp.mScore = curObjectInfo.objects[i].mScore;
                                /* 最大跟最小得分都设置成跟得分一样 */
                                objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                                objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                                /* 添加进去 */
                                tdbjectInfo_notCheck.objects.push_back(objectTemp);
                            }

                        }else{
                            /* 转换一下 */
                            td::object_t objectTemp;
                            memcpy(objectTemp.label, curObjectInfo.objects[i].label,
                                   sizeof(curObjectInfo.objects[i].label));
                            objectTemp.mId = curObjectInfo.objects[i].mId;
                            objectTemp.mTop = curObjectInfo.objects[i].mTop;
                            objectTemp.mBottom = curObjectInfo.objects[i].mBottom;
                            objectTemp.mLeft = curObjectInfo.objects[i].mLeft;
                            objectTemp.mRight = curObjectInfo.objects[i].mRight;
                            objectTemp.mXDistance = curObjectInfo.objects[i].mXDistance;
                            objectTemp.mYDistance = curObjectInfo.objects[i].mYDistance;
                            objectTemp.mHeadway = curObjectInfo.objects[i].mHeadway;
                            objectTemp.mRealWidth = curObjectInfo.objects[i].mRealWidth;
                            objectTemp.mRealHeight = curObjectInfo.objects[i].mRealHeight;
                            objectTemp.mXVelocity = curObjectInfo.objects[i].mXVelocity;
                            objectTemp.mYVelocity = curObjectInfo.objects[i].mYVelocity;
                            objectTemp.mTrend = curObjectInfo.objects[i].mTrend;
                            objectTemp.mScore = curObjectInfo.objects[i].mScore;
                            /* 最大跟最小得分都设置成跟得分一样 */
                            objectTemp.mMaxScore = curObjectInfo.objects[i].mScore;
                            objectTemp.mMinScore = curObjectInfo.objects[i].mScore;
                            /* 添加进去 */
                            tdbjectInfo_check.objects.push_back(objectTemp);
                        }


                    }
                }
            }
        }
        /* 去到调用目标检测的报警决策入口 */
        parse_UK_BSD_OD(tdbjectInfo_check, vehicleRealtimeStatus, tdbjectInfo_notCheck,curObjectInfo.alarmInfo.m_camera_covered_alarm,-1,
                        false,detectTime);

    }

    int VisAlarmDecision_Pedestrian_Area::pointVectorPointsToPointArrays(std::vector<VISPoint> pointsVector,
                                                                         VISPoint *pointArrays, const int len) {

        int ret = -1;
        if (pointsVector.size() == static_cast<std::size_t>(len)) {
            for (std::size_t i = 0; i < pointsVector.size(); i++) {
                pointArrays[i].x = pointsVector[i].x;
                pointArrays[i].y = pointsVector[i].y;
            }
            ret = 0;
        } else { ; // not to do
        }
        return ret;
    }

    bool VisAlarmDecision_Pedestrian_Area::isPedestrianInAlarmAreaLevel1_sgp(td::object_ &pedestrianInfo) {
        /* 判断下一级区域是否有设置（前面两个点不为0） */
        if (alarmAreaPointList_level1[0].x == 0 && alarmAreaPointList_level1[0].y == 0 &&
            alarmAreaPointList_level1[1].x == 0 && alarmAreaPointList_level1[1].y == 0) {
            return false;
        }
        VISPoint pedestrianPOints[4];
        bool isIn = false;

        pedestrianPOints[0].x = pedestrianInfo.mLeft;
        pedestrianPOints[0].y = pedestrianInfo.mTop;

        pedestrianPOints[1].x = pedestrianInfo.mRight;
        pedestrianPOints[1].y = pedestrianInfo.mTop;

        pedestrianPOints[2].x = pedestrianInfo.mRight;
        pedestrianPOints[2].y = pedestrianInfo.mBottom;

        pedestrianPOints[3].x = pedestrianInfo.mLeft;
        pedestrianPOints[3].y = pedestrianInfo.mBottom;


        /* 先判断这个框在屏幕中线的左边还是右边 */
        int pedestrianmidpoint = (pedestrianInfo.mRight - pedestrianInfo.mLeft) + pedestrianInfo.mRight;
        bool isLeft = pedestrianmidpoint <= 640;
        if (isLeft) {
            /* 在左边，那么需要判断行人框的右边和上边 */
            bool polygon1HavePointInPolygon = false;
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[0],
                                                                                            alarmAreaPointList_level1,
                                                                                            6);
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[1],
                                                                                            alarmAreaPointList_level1,
                                                                                            6);
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[2],
                                                                                            alarmAreaPointList_level1,
                                                                                            6);

            /* 如果通过点没判断出来  那么再判断两个多边形的边是否有相交的  有一条相交就算 */
            bool hasLineIntersected = false;
            if (!polygon1HavePointInPolygon) {
                VISLine line1, line2;
                line1.startPoint = pedestrianPOints[0];
                line1.endPoint = pedestrianPOints[1];
                line2.startPoint = pedestrianPOints[1];
                line2.endPoint = pedestrianPOints[2];

                VISLine desLine;
                for (int j = 0; j < 6; j++) {
                    desLine.startPoint = alarmAreaPointList_level1[j];
                    if (j == 6 - 1) {
                        desLine.endPoint = alarmAreaPointList_level1[0];
                    } else {
                        desLine.endPoint = alarmAreaPointList_level1[j + 1];
                    }
                    hasLineIntersected |= XuCalculationTool::getInstance().isLineIntersecting(line1, desLine);
                    hasLineIntersected |= XuCalculationTool::getInstance().isLineIntersecting(line2, desLine);
                    /* 找到一条就够用了 */
                    if (hasLineIntersected) {
                        break;
                    }
                }
            }
            isIn = polygon1HavePointInPolygon | hasLineIntersected;

        } else {
            /* 在右边，那么需要判断行人框的左边和上边 */

            bool polygon1HavePointInPolygon = false;
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[3],
                                                                                            alarmAreaPointList_level1,
                                                                                            6);
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[0],
                                                                                            alarmAreaPointList_level1,
                                                                                            6);
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[1],
                                                                                            alarmAreaPointList_level1,
                                                                                            6);

            /* 如果通过点没判断出来  那么再判断两个多边形的边是否有相交的  有一条相交就算 */
            bool hasLineIntersected = false;
            if (!polygon1HavePointInPolygon) {
                VISLine line1, line2;
                line1.startPoint = pedestrianPOints[0];
                line1.endPoint = pedestrianPOints[1];
                line2.startPoint = pedestrianPOints[0];
                line2.endPoint = pedestrianPOints[3];

                VISLine desLine;
                for (int j = 0; j < 6; j++) {
                    desLine.startPoint = alarmAreaPointList_level1[j];
                    if (j == 6 - 1) {
                        desLine.endPoint = alarmAreaPointList_level1[0];
                    } else {
                        desLine.endPoint = alarmAreaPointList_level1[j + 1];
                    }
                    hasLineIntersected |= XuCalculationTool::getInstance().isLineIntersecting(line1, desLine);
                    hasLineIntersected |= XuCalculationTool::getInstance().isLineIntersecting(line2, desLine);
                    /* 找到一条就够用了 */
                    if (hasLineIntersected) {
                        break;
                    }
                }
            }
            isIn = polygon1HavePointInPolygon | hasLineIntersected;
        }





        return isIn;
    }

    bool VisAlarmDecision_Pedestrian_Area::isPedestrianInAlarmAreaLevel2_sgp(td::object_ &pedestrianInfo) {
        /* 判断下一级区域是否有设置（前面两个点不为0） */
        if (alarmAreaPointList_level2[0].x == 0 && alarmAreaPointList_level2[0].y == 0 &&
                alarmAreaPointList_level2[1].x == 0 && alarmAreaPointList_level2[1].y == 0) {
            return false;
        }
        VISPoint pedestrianPOints[4];
        bool isIn = false;

        pedestrianPOints[0].x = pedestrianInfo.mLeft;
        pedestrianPOints[0].y = pedestrianInfo.mTop;

        pedestrianPOints[1].x = pedestrianInfo.mRight;
        pedestrianPOints[1].y = pedestrianInfo.mTop;

        pedestrianPOints[2].x = pedestrianInfo.mRight;
        pedestrianPOints[2].y = pedestrianInfo.mBottom;

        pedestrianPOints[3].x = pedestrianInfo.mLeft;
        pedestrianPOints[3].y = pedestrianInfo.mBottom;


        /* 先判断这个框在屏幕中线的左边还是右边 */
        int pedestrianmidpoint = (pedestrianInfo.mRight - pedestrianInfo.mLeft) + pedestrianInfo.mRight;
        bool isLeft = pedestrianmidpoint <= 640;
        if (isLeft) {
            /* 在左边，那么需要判断行人框的右边和上边 */
            bool polygon1HavePointInPolygon = false;
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[0],
                                                                                            alarmAreaPointList_level2,
                                                                                            6);
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[1],
                                                                                            alarmAreaPointList_level2,
                                                                                            6);
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[2],
                                                                                            alarmAreaPointList_level2,
                                                                                            6);

            /* 如果通过点没判断出来  那么再判断两个多边形的边是否有相交的  有一条相交就算 */
            bool hasLineIntersected = false;
            if (!polygon1HavePointInPolygon) {
                VISLine line1, line2;
                line1.startPoint = pedestrianPOints[0];
                line1.endPoint = pedestrianPOints[1];
                line2.startPoint = pedestrianPOints[1];
                line2.endPoint = pedestrianPOints[2];

                VISLine desLine;
                for (int j = 0; j < 6; j++) {
                    desLine.startPoint = alarmAreaPointList_level2[j];
                    if (j == 6 - 1) {
                        desLine.endPoint = alarmAreaPointList_level2[0];
                    } else {
                        desLine.endPoint = alarmAreaPointList_level2[j + 1];
                    }
                    hasLineIntersected |= XuCalculationTool::getInstance().isLineIntersecting(line1, desLine);
                    hasLineIntersected |= XuCalculationTool::getInstance().isLineIntersecting(line2, desLine);
                    /* 找到一条就够用了 */
                    if (hasLineIntersected) {
                        break;
                    }
                }
            }
            isIn = polygon1HavePointInPolygon | hasLineIntersected;

        } else {
            /* 在右边，那么需要判断行人框的左边和上边 */

            bool polygon1HavePointInPolygon = false;
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[3],
                                                                                            alarmAreaPointList_level2,
                                                                                            6);
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[0],
                                                                                            alarmAreaPointList_level2,
                                                                                            6);
            polygon1HavePointInPolygon |= XuCalculationTool::getInstance().isPointInPolygon(pedestrianPOints[1],
                                                                                            alarmAreaPointList_level2,
                                                                                            6);

            /* 如果通过点没判断出来  那么再判断两个多边形的边是否有相交的  有一条相交就算 */
            bool hasLineIntersected = false;
            if (!polygon1HavePointInPolygon) {
                VISLine line1, line2;
                line1.startPoint = pedestrianPOints[0];
                line1.endPoint = pedestrianPOints[1];
                line2.startPoint = pedestrianPOints[0];
                line2.endPoint = pedestrianPOints[3];

                VISLine desLine;
                for (int j = 0; j < 6; j++) {
                    desLine.startPoint = alarmAreaPointList_level2[j];
                    if (j == 6 - 1) {
                        desLine.endPoint = alarmAreaPointList_level2[0];
                    } else {
                        desLine.endPoint = alarmAreaPointList_level2[j + 1];
                    }
                    hasLineIntersected |= XuCalculationTool::getInstance().isLineIntersecting(line1, desLine);
                    hasLineIntersected |= XuCalculationTool::getInstance().isLineIntersecting(line2, desLine);
                    /* 找到一条就够用了 */
                    if (hasLineIntersected) {
                        break;
                    }
                }
            }
            isIn = polygon1HavePointInPolygon | hasLineIntersected;
        }





        return isIn;
    }

    bool VisAlarmDecision_Pedestrian_Area::checkPedestrianInsideRoad(das::object_t pedestrianInfo,
                                                                     das::object_t stationlineInfo,Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
        bool isInsideRoad = true;
        /* 先从语义分割判断下在不在可行驶区域（靠路肩） */
        if(!pedestrianInfo.mIsInsideRoad){
            isInsideRoad = false;
        }else{
            /* 语义分割判断到是在可行驶区域，那么再用黄线判断下 */
            /* 先判断下是不是装在左侧的镜头，其他位置的镜头先不管 */
            if(curCameraType.installPosition == INSTALL_POSITION_LEFT){
                /* 如果是在左侧了，那么还需要判断下是否打了左转灯 */
                if(vehicleRealtimeStatus.turnL){
                    /* 打了左转灯了，那么需要判断下车速是否小于10 */
                    if(abs(vehicleRealtimeStatus.speed - 10) > 0.000001){
                        /* 车速小于10了，那么看看是否识别出黄线了 */
                        if((strcmp(stationlineInfo.label, "stationline") == 0)){
                            /* 识别出黄线了，那么行人的车框的底变的Y小于黄线的框的底边，那么就算不在可行驶区域内  （原点在画面的左上角） */
                            if(pedestrianInfo.mBottom < stationlineInfo.mBottom){
                                isInsideRoad = false;
                            }else{
                                ; // not to do
                            }

                        }else{
                            ; // not to do
                        }

                    }else{
                        ; // not to do
                    }

                }else{
                    ; // not to do
                }

            }else{
                ; //not to do
            }



        }





        return isInsideRoad;
    }

    bool VisAlarmDecision_Pedestrian_Area::checkHeadway(das::object_t pedestrianInfo) {
        bool ret = ((pedestrianInfo.mHeadway > 0) && (pedestrianInfo.mHeadway * 10) <= G3_Configuration::getInstance().getAdasHeadwayThresholdLevel1());
        return ret;
    }

} // vis