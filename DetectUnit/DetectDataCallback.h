//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/8.
//

#ifndef VIS_G3_SOFTWARE_DETECTDATACALLBACK_H
#define VIS_G3_SOFTWARE_DETECTDATACALLBACK_H


#include <cstdint>
#include <vector>
#include <string>
#include "CameraYUVData.h"
#include "G3DetectionDefind.h"
#include  "tdShareDefine.h"
#include  "gtShareDefine.h"
#include  "dasShareDefine.h"
#include  "myparams.h"


class DetectDataCallback {

public:


    /**
     * 从缓冲区获取一帧YUV数据
     *
     * @param cameraId : 相机ID
     * @param cameraYuvData ： 用来存放YUV的指针
     * @param vehicleRealtimeStatus ： 用来车辆状态数据的对象
     * @param needClean : 是否需要清空识别信息（三选二专用）
     * @return 结果     0：成功  其他：失败
     * */
    virtual int getYUVData(int cameraId, uint8_t *buf,Vehicle_RealtimeStatus &vehicleRealtimeStatus, bool *needClean);

    /**
     * 拿到一组识别结果(侧边行人)
     *
     * @param objectInfo ： 识别信息数组
     * @param detectionInfo ： 封装好的识别结果
     *
     * @return 无
     * */
    virtual void onGetObjectInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo);

    /**
     * 获取到一个算法识别出来的报警事件
     *
     * @param curCameraType ： 相机类型信息
     * @param eventCode ： 事件代码
     *
     * @return 无
     * */
    virtual void onGetDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

    /**
     * 拿到一组识别结果(DSM)
     *
     * @param detectionInfo ： 封装好的识别结果
     *
     * @return 无
     * */
    virtual void onGetDSMInfo(G3DetectionResult &detectionInfo);

    /**
     * 拿到一组识别结果(手势)
     *
     * @param objectInfo ： 识别信息数组
     * @param detectionInfo ： 封装好的识别结果
     *
     * @return 无
     * */
    virtual void onGetObjectInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo);

    /**
     * 拿到一组识别结果(ADAS)
     *
     * @param objectInfo ： 识别信息数组
     * @param detectionInfo ： 封装好的识别结果
     *
     * @return 无
     * */
    virtual void onGetObjectInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo);

    /**
     * 拿到一组识别结果(镜头状态)
     *
     * @param detectionInfo ： 封装好的识别结果
     *
     * @return 无
     * */
    virtual void onGetDetectInof_CameraStatus(G3DetectionResult &detectionInfo);

    /**
     * 拿到一组识别结果(条码)
     *
     * @param detectionInfo ： 封装好的识别结果
     * @param barCodeInfoList : 条码信息列表
     *
     * @return 无
     * */
    virtual void onGetDetectInfo_BarCode(G3DetectionResult &detectionInfo, std::vector<BarCodeInfo> &barCodeInfoList);


};


#endif //VIS_G3_SOFTWARE_DETECTDATACALLBACK_H
