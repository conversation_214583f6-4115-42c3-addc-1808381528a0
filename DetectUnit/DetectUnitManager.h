//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/8.
//

#ifndef VIS_G3_SOFTWARE_DETECTUNITMANAGER_H
#define VIS_G3_SOFTWARE_DETECTUNITMANAGER_H



#include "DetectDataCallback.h"
#include <Poco/ThreadPool.h>
#include <mutex>
#include "Detecter_BSD.h"
#include "Detecter_Gesture.h"
#include "Detecter_Adas.h"
#include "Detecter_SBST_ForwardAndBackword.h"
#include "Detecter_SBST_Sideward.h"
#include "utils/XuTimeUtil.h"
#include "VisDetecter.h"
#include "detecter_list/VisDetecter_UK_BSD_OD.h"
#include "detecter_list/VisDetecter_SBST_160_SS.h"
#include "detecter_list/VisDetecter_MT_DSM.h"
#include "detecter_list/VisDetecter_MT_Gesture.h"
#include "detecter_list/VisDetecter_SBST_160_OD.h"
#include "detecter_list/VisDetecter_ADAS_160_Normal.h"
#include "detecter_list/VisDetecter_DE_BSD_OD.h"
#include "VisDetecter_ADAS_60_Normal.h"
#include "detecter_list/VisDetecter_MUV_DSM.h"
#include "detecter_list/VisDetecter_DVR.h"
#include "detecter_list/VisDetecter_KOM_BSD_OD.h"
#include "detecter_list/VisDetecter_ADAS_160_SBST.h"
#include "detecter_list/VisDetecter_DSM_Normal.h"
#include "detecter_list/VisDetecter_UK_DD_QRCode.h"
#include "detecter_list/VisDetecter_DSM_FB.h"
#include "detecter_list/VisDetecter_FORKLIFT_160_OD.h"
#include <mutex>


namespace vis {

    class DetectUnitManager : public DetectDataCallback {
    public:

        /* 所有识别算法的集合都放在这里了 */
        struct VisDetecterList{
            /* 相机ID */
            CAMERAIDLIST cameraId = CAMERA_ID_UNKNOW;
            /* 各种算法对象的指针 */
            VisDetecter_UK_BSD_OD *detecterUkBsdOd = nullptr;
            VisDetecter_SBST_160_SS *detecterSbst160Ss = nullptr;
            VisDetecter_MT_DSM *detecterMtDsm = nullptr;
            VisDetecter_MT_Gesture *detecterMtGesture = nullptr;
            VisDetecter_SBST_160_OD *detecterSbst160Od = nullptr;
            VisDetecter_ADAS_160_Normal *detecterAdas160Normal = nullptr;
            VisDetecter_DE_BSD_OD *detecterDEBsdOd = nullptr;
            VisDetecter_ADAS_60_Normal *detecterAdas60Normal = nullptr;
            VisDetecter_MUV_DSM *detecterMuvDsm = nullptr;
            VisDetecter_DVR *detecterDvr = nullptr;
            VisDetecter_KOM_BSD_OD *detecterKomBsdOd = nullptr;
            VisDetecter_ADAS_160_SBST *detecterAdas160Sbst = nullptr;
            VisDetecter_DSM_Normal *detecterDsmNormal = nullptr;
            VisDetecter_UK_DD_QRCode *detecterUkDdQrCode = nullptr;
            VisDetecter_DSM_FB *detecterDsmFb = nullptr;
            VisDetecter_FORKLIFT_160_OD *detecterForklift160Od = nullptr;
        };


        DetectUnitManager();

        ~DetectUnitManager();


        /**
         * 初始化
         *
         * @param detectDataCallback ：main的回调接口
         *
         * @return 结果     0：成功  其他：失败
         *
         * */
        int init(DetectDataCallback &detectDataCallback);


        /**
         * 设置YUV数据到缓冲区
         *
         * @param cameraYuvData ： YUV数据封装好的对象
         *
         * */
        void setYUVDataToDetect(CameraYUVData &cameraYuvData);




        /**
        * 设置车速
        *
        * @param speed : 速度  （单位：KM\H）
        * @param baseOf ： 来源    （0：CAN  1：UART  2：GPS  3:以太网口）
        *
        * @return 无
        * */
        void setSpeed(const float speed, const int baseOf);

        /**
         *
         * 设置实时车况
         *
         * @param vehicleRealtimeStatus : 实时车况
         */
        void setVehicleRealtimeStatus(Vehicle_RealtimeStatus &vehicleRealtimeStatus);

        /**
         *
         * 获取实时车况
         *
         * @return  当前算法使用的实时车况
         */
        Vehicle_RealtimeStatus getVehicleRealtimeStatus();

        void start();


        /**
         * 获取当前的运行状态
         *
         * @return 0：一切正常   其他：有故障
         * */
        int getWorkStaus();

        /**
         * 设置是否处于高温状态
         *
         * @param isHighTemperature
         */
        void setIsHighTemperature(bool isHighTemperature);

        /**
         * 设置转向信号
         *
         * @param trunL ： 是否左转
         * @param trunR ： 是否右转
         */
        void setTurnSignal(const bool trunL,const bool trunR);

        /**
         * 设置倒车信号
         *
         * @param reverse ： 倒车信号
         */
        void setReverseSignal(const bool reverse);

        /**
         * 设置车门信号
         *
         * @param door ： 车门信号
         */
        void setDoorSignal(const bool door);


        /**
               * 从缓冲区获取一帧YUV数据
               *
               * @param cameraId : 相机ID
               * @param cameraYuvData ： 用来存放YUV的指针
               * @param vehicleRealtimeStatus ： 用来车辆状态数据的对象
               * @param needClean : 是否需要清空识别信息（三选二专用）
               * @return 结果     0：成功  其他：失败
               * */
        int getYUVData(int cameraId, uint8_t *buf,Vehicle_RealtimeStatus &vehicleRealtimeStatus, bool *needClean) override;

        /**
         * 获取到算法识别结果的回调
         *
         * @param cameraId ： 相机ID
         * @param detectType ： 算法类型
         * @param objectInfo ： 算法识别内容
         *
         * */
        void onGetObjectInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) override;


        /**
         * 获取到一个算法识别出来的报警事件
         *
         * @param cameraId : 相机ID
         * @param detectType ： 算法类型
         * @param eventCode ： 事件代码
         *
         * @return 无
         * */
        void onGetDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) override;

        /**
         * 拿到一组识别结果(DSM)
         *
         * @param detectionInfo ： 封装好的识别结果
         *
         * @return 无
         * */
        virtual void onGetDSMInfo(G3DetectionResult &detectionInfo) override;

        /**
         * 拿到一组识别结果(手势)
         *
         * @param objectInfo ： 识别信息数组
         * @param detectionInfo ： 封装好的识别结果
         *
         * @return 无
         * */
        void onGetObjectInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) override;

        /**
         * 拿到一组识别结果(ADAS)
         *
         * @param objectInfo ： 识别信息数组
         * @param detectionInfo ： 封装好的识别结果
         *
         * @return 无
         * */
        void onGetObjectInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) override;

        /**
         * 拿到一组识别结果(镜头状态)
         *
         * @param detectionInfo ： 封装好的识别结果
         *
         * @return 无
         * */
        void onGetDetectInof_CameraStatus(G3DetectionResult &detectionInfo) override;

        /**
         * 拿到一组识别结果(条码)
         *
         * @param detectionInfo ： 封装好的识别结果
         * @param barCodeInfoList : 条码信息列表
         *
         * @return 无
         * */
        void onGetDetectInfo_BarCode(G3DetectionResult &detectionInfo, std::vector<BarCodeInfo> &barCodeInfoList) override;

    private:

        /* Detect 模块所有线程使用的线程池 */
        Poco::ThreadPool threadPool;

        /* 存放YUV数据的的缓存 */
        CameraYUVData *yuvCache_camera1 = nullptr;

        CameraYUVData *yuvCache_camera2 = nullptr;

        CameraYUVData *yuvCache_camera3 = nullptr;

        CameraYUVData *yuvCache_camera4 = nullptr;



        DetectDataCallback *callback;

        Detecter_BSD detect_bsd_camera1;

        Detecter_BSD detect_bsd_camera2;


        Detecter_Gesture detect_gesture_camera1;

        Detecter_Gesture detect_gesture_camera2;

        Detecter_Adas detect_adas_camera1;

        Detecter_Adas detect_adas_camera2;

        Detecter_SBST_ForwardAndBackword sbstForwardAndBackword_camera1;

        Detecter_SBST_ForwardAndBackword sbstForwardAndBackword_camera2;

        Detecter_SBST_Sideward sbstSideward_camera1;

        Detecter_SBST_Sideward sbstSideward_camera2;


        /* 是不是高温了 */
        bool isHighTemperature = false;

        /* 当前的车况 */
        Vehicle_RealtimeStatus curVehicleStatus = {};


        /* 需要运行的算法列表 */
        std::vector<VisDetecterList> detecterList;


        /* 上一次报R151的镜头遮挡报警的时间 */
        uint64_t lastCameracoverTime;

        /* 识别算法所允许的最大识别时间，单位ms。  如果超过这个时间都没识别出结果，那么就说明有问题。 */
        uint64_t MAX_DETECT_INTERVAL_TIME = 1000;


        /* 当前需要串行执行的规则的下标。 */
        int serialRuleIndex = 0;
        /* 当前规则执行完成的结果(复用一下规则结构体，不要太较真) */
        AlgSerialRuleItem ruleFinishRet = {false, false, false, false,0};
        /* 更新规则相关信息的锁 */
        std::mutex ruleLock;



        /**
       * 通过旧的算法选择启动对应的算法
       */
        void startDetect_Old();

        /**
         * 通过新的算法选择启动对应的算法
         */
        void startDetect_new();

        /**
         * 获取相机对应的算法的工作状态
         *
         * @param cameraId ： 相机ID
         *
         * @return 是否在正常工作
         */
        bool getCameraDetecterWorkingStatus(const int cameraId);

        /**
         * 获取所有算法的工作状态
         *
         * @return 是否在正常工作
         */
        bool getAllDetecterWorkingStatus();

        /**
         * 根据车辆信号判断镜头对应的算法是否应该工作
         *
         * @param cameraId ： 镜头ID
         *
         * @return 是否应该工作
         */
        bool isCameraAlgShouldWorkBySignal(const CAMERAIDLIST cameraId);

        /**
         * 检查下现在的镜头算法工作状态切换的信号规则是否满足
         *
         * @param signalSet ： 镜头算法工作状态切换的信号规则
         *
         * @return  0：不满足   1：满足   2：无效规则
         */
        int checkCameraAlgWorkSwitchSignal(const CameraAlgWorkRule_SignalSet signalSet);

        /**
         * 根据C3的状态确定这个镜头的算法使用应该工作
         *
         * @param cameraId ： 镜头ID
         *
         * @return 是否应该工作
         */
        bool isCameraAlgShouldWorkByC3(const CAMERAIDLIST cameraId);

        /**
         * 尝试执行串行规则的下一条规则
         */
        void changAlgSerialRuleIndex(const CAMERAIDLIST cameraId);

        /**
         * 判断下在算法串行执行的规则下，是否可以执行此算法
         *
         * @return  是否可以执行此算法
         */
        bool checkCanWorkByAlgSerialRule(const CAMERAIDLIST cameraId);

    };

} // namespace vis

#endif //VIS_G3_SOFTWARE_DETECTUNITMANAGER_H
