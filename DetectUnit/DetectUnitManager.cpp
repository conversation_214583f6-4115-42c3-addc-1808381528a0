//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/8.
//

#include "DetectUnitManager.h"
#include "XuFile.h"

namespace vis {

    DetectUnitManager::DetectUnitManager() : threadPool(2, 4) {
        lastCameracoverTime = 0;
    }


    void DetectUnitManager::setYUVDataToDetect(CameraYUVData &cameraYuvData) {


        /* 看看是不是G4-4+1模式 */
        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G45_UNIT){

            switch (cameraYuvData.getCameraId()) {
                case CAMERA_ID_1: {
                    if(curVehicleStatus.c3Switch_c31){
                        switch (G3_Configuration::getInstance().getC3SwitchRuleByC3Id(C3ID_1).cameraForEnable) {
                            case CAMERA_ID_1: {
                                yuvCache_camera1 = &cameraYuvData;
                            }
                                break;
                            case CAMERA_ID_2: {
                                yuvCache_camera2 = &cameraYuvData;
                            }
                                break;

                            case CAMERA_ID_3: {
                                yuvCache_camera3 = &cameraYuvData;
                            }
                                break;
                            case CAMERA_ID_4: {
                                yuvCache_camera4 = &cameraYuvData;
                            }
                                break;

                            default: { ; //not to do
                            }
                                break;
                        }

                    }else{
                        yuvCache_camera1 = &cameraYuvData;
                    }
                }
                    break;
                case CAMERA_ID_2: {
                    if(curVehicleStatus.c3Switch_c32){
                        switch (G3_Configuration::getInstance().getC3SwitchRuleByC3Id(C3ID_2).cameraForEnable) {
                            case CAMERA_ID_1: {
                                yuvCache_camera1 = &cameraYuvData;
                            }
                                break;
                            case CAMERA_ID_2: {
                                yuvCache_camera2 = &cameraYuvData;
                            }
                                break;

                            case CAMERA_ID_3: {
                                yuvCache_camera3 = &cameraYuvData;
                            }
                                break;
                            case CAMERA_ID_4: {
                                yuvCache_camera4 = &cameraYuvData;
                            }
                                break;

                            default: { ; //not to do
                            }
                                break;
                        }
                    }else{
                        yuvCache_camera2 = &cameraYuvData;
                    }
                }
                    break;


                default: { ; //not to do
                }
                    break;

            }



        }else{
            /* 不是G4-4+1模式，直接放 */
            switch (cameraYuvData.getCameraId()) {
                case CAMERA_ID_1: {
                    yuvCache_camera1 = &cameraYuvData;
                }
                    break;
                case CAMERA_ID_2: {
                    yuvCache_camera2 = &cameraYuvData;
                }
                    break;

                case CAMERA_ID_3: {
                    yuvCache_camera3 = &cameraYuvData;
                }
                    break;
                case CAMERA_ID_4: {
                    yuvCache_camera4 = &cameraYuvData;
                }
                    break;

                default: { ; //not to do
                }
                    break;

            }
        }


    }

    int DetectUnitManager::getYUVData(int cameraId, uint8_t *buf, Vehicle_RealtimeStatus &vehicleRealtimeStatus, bool *needClean) {
        int ret = -1;
        /* 先看看是不是满足串行规则 */
        if(checkCanWorkByAlgSerialRule(static_cast<CAMERAIDLIST>(cameraId))){
            // 满足串行规则了，那么就检查下是不是可以移动规则下标了
            changAlgSerialRuleIndex(static_cast<CAMERAIDLIST>(cameraId));
            // 再判断是不是高温了，高温就不给图像了
            if (!isHighTemperature) {
                /* 不是高温就判断下车辆信号的规则 */
                if (isCameraAlgShouldWorkBySignal(static_cast<CAMERAIDLIST>(cameraId))) {
                    /* 如果是否G4-4+1模式且不符合C3规则 */
                    if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G45_UNIT &&
                       !isCameraAlgShouldWorkByC3(static_cast<CAMERAIDLIST>(cameraId))){
                        /* G4-4+1模式且不符合C3规则那么就直接返回失败 */
//                    printf("is G4-4+1 and c3 not! %d \n",vehicleRealtimeStatus.c3Switch_c31);
                        ret = -1;
                        *needClean = true;
                    }else{
                        /* 先设置不用清理识别信息 */
                        *needClean = false;
                        /* 根据相机ID拿对应的图像 */
                        switch (cameraId) {
                            case CAMERA_ID_1: {
                                if (yuvCache_camera1 != nullptr) {
                                    yuvCache_camera1->getYUVData(buf, yuvCache_camera1->getDataLen());
                                    ret = 0;
                                }
                            }
                                break;
                            case CAMERA_ID_2: {
                                if (yuvCache_camera2 != nullptr) {
                                    yuvCache_camera2->getYUVData(buf, yuvCache_camera2->getDataLen());
                                    ret = 0;
                                }
                            }
                                break;
                            case CAMERA_ID_3: {
                                if (yuvCache_camera3 != nullptr) {
                                    yuvCache_camera3->getYUVData(buf, yuvCache_camera3->getDataLen());
                                    ret = 0;
                                }
                            }
                                break;
                            case CAMERA_ID_4: {
                                if (yuvCache_camera4 != nullptr) {
                                    yuvCache_camera4->getYUVData(buf, yuvCache_camera4->getDataLen());
                                    ret = 0;
                                }
                            }
                                break;
                            default: { ; //not to do
                            }
                                break;
                        }
                    }
                } else {
                    /* 不满足车辆信号的规则，不给图像给算法，并且清掉识别信息 */
                    ret = -1;
                    *needClean = true;
                }
            } else {
                /* 高温状态下，不给图像给算法 */
                ; //not to do
            }
        }else{
            // 不允许执行的话就直接什么都不做
        }
        /* 刷新一下车况 */
        vehicleRealtimeStatus = curVehicleStatus;

        return ret;
    }

    int DetectUnitManager::init(DetectDataCallback &detectDataCallback) {
        callback = &detectDataCallback;
        return 0;
    }


    void DetectUnitManager::start() {
        /* 先判断配置表版本  20版本之前都使用旧的选择方式，用以适配徐榕佑那叼毛的旧客户端 */
        if (G3_Configuration::getInstance().getConfigVersionCode() < 20) {
            startDetect_Old();
        } else {
            startDetect_new();
        }
    }

    DetectUnitManager::~DetectUnitManager() {

    }

    int DetectUnitManager::getWorkStaus() {
        bool ret = true;
        /* 如果是旧的配置表和高温状态，直接返回成功，新的配置表才需要真实判断 */
        if (G3_Configuration::getInstance().getConfigVersionCode() >= 20 && !isHighTemperature) {

            /* 看看是不是V6模式 */
            if (G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_V6) {
                /* 是V6模式，那么就判断一下算法1的状态就行了 */
                bool camera1WorkingStatus = getCameraDetecterWorkingStatus(CAMERA_ID_1);
                ret = camera1WorkingStatus;
            } else {
                /* 不是V6模式，需要检查下所有算法的状态 */
                bool camera2WorkingStatus = getAllDetecterWorkingStatus();
                ret = camera2WorkingStatus;
            }


        }
        return (ret ? 0 : -1);
    }

    void DetectUnitManager::setIsHighTemperature(bool isHighTemperature) {
        DetectUnitManager::isHighTemperature = isHighTemperature;
    }


    void DetectUnitManager::setSpeed(const float speed, const int baseOf) {
        if (speed >= 0) {
            curVehicleStatus.speed = speed;
        }
    }

    void DetectUnitManager::onGetObjectInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) {
        callback->onGetObjectInfo_BSD(objectInfo, detectionInfo);
    }

    void
    DetectUnitManager::onGetDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
        /* 所有镜头的遮挡报警公用一个间隔时间，所以在这里进行间隔时间判断 */
        if (eventCode == EVENT_BSD_R151_CAMERA_COVER || eventCode == EVENT_BSD_R158_CAMERA_COVER ||
            eventCode == EVENT_BSD_R159_CAMERA_COVER ||
            eventCode == EVENT_BSD_LEFT_FORWARD_CAMERA_COVER || eventCode == EVENT_BSD_LEFT_BACKWARD_CAMERA_COVER ||
            eventCode == EVENT_BSD_RIGHT_FORWARD_CAMERA_COVER ||
            eventCode == EVENT_BSD_RIGHT_BACKWARD_CAMERA_COVER || eventCode == EVENT_PDW_FORWARD_CAMERA_COVER ||
            eventCode == EVENT_PDW_BACKWARD_CAMERA_COVER ||
            eventCode == EVENT_PDW_BACKWARD_CAMERA_COVER) {
            /* 除了DSM，所有的遮挡报警都需要间隔指定的时间 */
            int intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec() - lastCameracoverTime;
            if (intervalTime >= G3_Configuration::getInstance().getBsdCamcoverAlarmOutputInterval()) {
                lastCameracoverTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Sec();
                callback->onGetDetectionEvent(curCameraType, eventCode, alarmEventInfo);
            } else { ; //not to do

            }

        } else {
            callback->onGetDetectionEvent(curCameraType, eventCode, alarmEventInfo);
        }

    }

    void DetectUnitManager::onGetDSMInfo(G3DetectionResult &detectionInfo) {
        callback->onGetDSMInfo(detectionInfo);
    }

    void DetectUnitManager::onGetObjectInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) {
        callback->onGetObjectInfo_GES(objectInfo, detectionInfo);
    }

    void DetectUnitManager::onGetObjectInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionInfo) {

        callback->onGetObjectInfo_Adas(objectInfo, detectionInfo);


    }


    void DetectUnitManager::setVehicleRealtimeStatus(Vehicle_RealtimeStatus &vehicleRealtimeStatus) {
//        printf("*************setVehicleRealtimeStatus=%f  \n\n",vehicleRealtimeStatus.speed);

/* 先特殊处理一下车速 */
        if (vehicleRealtimeStatus.speed < 0) {
            vehicleRealtimeStatus.speed = curVehicleStatus.speed;
        }


        curVehicleStatus = vehicleRealtimeStatus;
    }

    void DetectUnitManager::startDetect_Old() {
        /* 初始化镜头0对应的算法 */
        std::cout << "运行镜头1算法: "
                  << G3_Configuration::getInstance().getAlgorithmTypeOfCamera(CAMERA_ID_1) << std::endl;
        switch (G3_Configuration::getInstance().getAlgorithmTypeOfCamera(CAMERA_ID_1)) {
            case ALGORITHM_TYPE_NOT: {
                printf("have a unknow camera type! \n");
            }
                break;
            case ALGORITHM_TYPE_SIDE_PEDESTRIAN_AND_VEHICLE: {
                detect_bsd_camera1.init(CAMERA_ID_1, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(detect_bsd_camera1);
                }
            }
                break;

            case ALGORITHM_TYPE_DSM_METRO: {
//                detect_dsm_camera1.init(CAMERA_ID_1, *this);
//                if (threadPool.available() > 0) {
//                    threadPool.start(detect_dsm_camera1);
//                }
            }
                break;

            case ALGORITHM_TYPE_GESTURE_TJ_METRO: {
                detect_gesture_camera1.init(CAMERA_ID_1, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(detect_gesture_camera1);
                }
            }
                break;

            case ALGORITHM_TYPE_ADAS_NORMAL: {
                detect_adas_camera1.init(CAMERA_ID_1, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(detect_adas_camera1);
                }
            }
                break;

            case ALGORITHM_TYPE_SBST_BACKWARD:
            case ALGORITHM_TYPE_SBST_FORWARD: {
                sbstForwardAndBackword_camera1.init(CAMERA_ID_1, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(sbstForwardAndBackword_camera1);
                }
            }
                break;

            case ALGORITHM_TYPE_SBST_SIDEWARD: {
                sbstSideward_camera1.init(CAMERA_ID_1, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(sbstSideward_camera1);
                }
            }
                break;


        }

        /* 加个间隔 避免读配置文件失败 */
        usleep(100 * 1000);

        /* 初始化镜头1对应的算法 */
        std::cout << "运行镜头2算法: "
                  << G3_Configuration::getInstance().getAlgorithmTypeOfCamera(CAMERA_ID_2) << std::endl;
        switch (G3_Configuration::getInstance().getAlgorithmTypeOfCamera(CAMERA_ID_2)) {
            case ALGORITHM_TYPE_NOT: {
                printf("have a unknow camera type! \n");
            }
                break;
            case ALGORITHM_TYPE_SIDE_PEDESTRIAN_AND_VEHICLE: {
                detect_bsd_camera2.init(CAMERA_ID_2, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(detect_bsd_camera2);
                }
            }
                break;

            case ALGORITHM_TYPE_DSM_METRO: {
//                detect_dsm_camera2.init(CAMERA_ID_2, *this);
//                if (threadPool.available() > 0) {
//                    threadPool.start(detect_dsm_camera2);
//                }
            }
                break;

            case ALGORITHM_TYPE_GESTURE_TJ_METRO: {
                detect_gesture_camera2.init(CAMERA_ID_2, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(detect_gesture_camera2);
                }
            }
                break;

            case ALGORITHM_TYPE_ADAS_NORMAL: {

                detect_adas_camera2.init(CAMERA_ID_2, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(detect_adas_camera2);
                }
            }
                break;

            case ALGORITHM_TYPE_SBST_BACKWARD:
            case ALGORITHM_TYPE_SBST_FORWARD: {
                sbstForwardAndBackword_camera2.init(CAMERA_ID_2, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(sbstForwardAndBackword_camera2);
                }
            }
                break;

            case ALGORITHM_TYPE_SBST_SIDEWARD: {
                sbstSideward_camera2.init(CAMERA_ID_2, *this);
                if (threadPool.available() > 0) {
                    threadPool.start(sbstSideward_camera2);
                }
            }
                break;


        }
    }

    void DetectUnitManager::startDetect_new() {
        /* 从配置表获取配置的相机数 */
        std::vector<CameraType> cameraTypeList = G3_Configuration::getInstance().getCameraTypeList();
        /* 一个个添加进去(不能超过定义的相机数) */
        for (std::size_t i = 0; (i < cameraTypeList.size() && i < sizeof(CAMERAIDLIST)); i++) {
            /* 先定义好识别算法的对象 */
            vis::DetectUnitManager::VisDetecterList detecterList_temp;
            /* 设置相机ID */
            detecterList_temp.cameraId = static_cast<CAMERAIDLIST>(cameraTypeList[i].cameraId);
            /* 初始化识别算法的对象 */
            switch (cameraTypeList[i].algType) {

                case ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION: {
                    /* 英国工程机械-BSD相机-目标检测 */
                    detecterList_temp.detecterUkBsdOd = new VisDetecter_UK_BSD_OD();
                    detecterList_temp.detecterUkBsdOd->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterUkBsdOd->addVisAlarmDecision(cameraTypeList[i].adType,
                                                                           G3_Configuration::getInstance().getTdClassThreshold());
                    threadPool.start(*detecterList_temp.detecterUkBsdOd);

                }
                    break;

                case ALGORITHM_TYPE_SBST_160_SEMANTIC_SEGMENTATION: {
                    /* 英国工程机械-160°相机-语义分割 */
                    detecterList_temp.detecterSbst160Ss = new VisDetecter_SBST_160_SS();
                    detecterList_temp.detecterSbst160Ss->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterSbst160Ss->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterSbst160Ss);
                }
                    break;
                case ALGORITHM_TYPE_DSM_METRO: {
                    /* 地铁DSM */
                    detecterList_temp.detecterMtDsm = new VisDetecter_MT_DSM();
                    detecterList_temp.detecterMtDsm->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterMtDsm->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterMtDsm);
                }
                    break;
                case ALGORITHM_TYPE_GESTURE_TJ_METRO: {
                    /* 天津地铁手势 */
                    detecterList_temp.detecterMtGesture = new VisDetecter_MT_Gesture();
                    detecterList_temp.detecterMtGesture->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterMtGesture->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterMtGesture);
                }
                    break;

                case ALGORITHM_TYPE_SBST_160_OBJECT_DETECTION: {
                    /* 新加坡巴士-160度镜头-目标检测 */
                    detecterList_temp.detecterSbst160Od = new VisDetecter_SBST_160_OD();
                    detecterList_temp.detecterSbst160Od->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterSbst160Od->addVisAlarmDecision(cameraTypeList[i].adType,
                                                                             G3_Configuration::getInstance().getSbst160OdClassThreshold());
                    threadPool.start(*detecterList_temp.detecterSbst160Od);
                }
                    break;

                case ALGORITHM_TYPE_ADAS_160_NORMAL: {
                    /* ADAS-160度镜头 */
                    detecterList_temp.detecterAdas160Normal = new VisDetecter_ADAS_160_Normal();
                    detecterList_temp.detecterAdas160Normal->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterAdas160Normal->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterAdas160Normal);
                }
                    break;

                case ALGORITHM_TYPE_DE_BSD_OBJECT_DETECTION: {
                    /* 德国公交车-BSD相机-目标检测 */
                    detecterList_temp.detecterDEBsdOd = new VisDetecter_DE_BSD_OD();
                    detecterList_temp.detecterDEBsdOd->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterDEBsdOd->addVisAlarmDecision(cameraTypeList[i].adType,
                                                                           G3_Configuration::getInstance().getDeBsdOdClassThreshold());
                    threadPool.start(*detecterList_temp.detecterDEBsdOd);
                }
                    break;

                case ALGORITHM_TYPE_ADAS_60_NORMAL: {
                    /* ADAS-60度镜头 */
                    detecterList_temp.detecterAdas60Normal = new VisDetecter_ADAS_60_Normal();
                    detecterList_temp.detecterAdas60Normal->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterAdas60Normal->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterAdas60Normal);
                }
                    break;

                case ALGORITHM_TYPE_DSM_MUV: {
                    /* 新加坡MUV的DSM算法 */
                    detecterList_temp.detecterMuvDsm = new VisDetecter_MUV_DSM();
                    detecterList_temp.detecterMuvDsm->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterMuvDsm->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterMuvDsm);
                }
                    break;

                case ALGORITHM_TYPE_DVR: {
                    /* DVR算法 */
                    detecterList_temp.detecterDvr = new VisDetecter_DVR();
                    detecterList_temp.detecterDvr->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterDvr->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterDvr);
                }
                    break;

                case ALGORITHM_TYPE_KOM_BSD_OBJECT_DETECTION: {
                    /* 小松的CVBS镜头侧边行人目标检测的算法 */
                    detecterList_temp.detecterKomBsdOd = new VisDetecter_KOM_BSD_OD();
                    detecterList_temp.detecterKomBsdOd->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterKomBsdOd->addVisAlarmDecision(cameraTypeList[i].adType,
                                                                            G3_Configuration::getInstance().getKomBsdOdClassThreshold());
                    threadPool.start(*detecterList_temp.detecterKomBsdOd);
                }
                    break;

                case ALGORITHM_TYPE_ADAS_160_SBST: {
                    /* SBST-ADAS-160度镜头 */
                    detecterList_temp.detecterAdas160Sbst = new VisDetecter_ADAS_160_SBST();
                    detecterList_temp.detecterAdas160Sbst->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterAdas160Sbst->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterAdas160Sbst);
                }
                    break;

                case ALGORITHM_TYPE_DSM_NORMAL: {
                    /* 标准DSM算法 */
                    detecterList_temp.detecterDsmNormal = new VisDetecter_DSM_Normal();
                    detecterList_temp.detecterDsmNormal->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterDsmNormal->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterDsmNormal);
                }
                    break;

                case ALGORITHM_TYPE_UK_DD_QRCODE: {
                    /* 英国DD的二维码识别 */
                    detecterList_temp.detecterUkDdQrCode = new VisDetecter_UK_DD_QRCode();
                    detecterList_temp.detecterUkDdQrCode->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterUkDdQrCode->addVisAlarmDecision(cameraTypeList[i].adType,
                                                                              G3_Configuration::getInstance().getTdClassThreshold());
                    threadPool.start(*detecterList_temp.detecterUkDdQrCode);
                }
                    break;

                case ALGORITHM_TYPE_DSM_FB: {
                    /* DSM-识别安全带 */
                    detecterList_temp.detecterDsmFb = new VisDetecter_DSM_FB();
                    detecterList_temp.detecterDsmFb->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterDsmFb->addVisAlarmDecision(cameraTypeList[i].adType, 0);
                    threadPool.start(*detecterList_temp.detecterDsmFb);
                }
                    break;

                case ALGORITHM_TYPE_FORKLIFT_160_OBJECT_DETECTION: {
                    /* 叉车-160度镜头-目标检测 */
                    detecterList_temp.detecterForklift160Od = new VisDetecter_FORKLIFT_160_OD();
                    detecterList_temp.detecterForklift160Od->init(cameraTypeList[i].cameraId, *this);
                    detecterList_temp.detecterForklift160Od->addVisAlarmDecision(cameraTypeList[i].adType,
                                                                                 G3_Configuration::getInstance().getForklift160OdClassThreshold());
                    threadPool.start(*detecterList_temp.detecterForklift160Od);
                }
                    break;

                case ALGORITHM_TYPE_NOT:
                default: {
                    /* 不启动算法，或者不认识的算法，直接下一条 */
                    continue;
                }
                    break;
            }
            /* 停个100ms，防止算法启动失败 */
            usleep(100 * 1000);
            /* 把识别算法的对象放到vector里面保持起来 */
            detecterList.push_back(detecterList_temp);
        }


//        printf("********************1=%d   2=%d  \n", sizeof(detecterList_camera1),sizeof(detecterList_camera2));
//
//        /* 先开启镜头1对应的算法 */
//        CameraType cameraType_camera1;
//        G3_Configuration::getInstance().getCameraTypeInfoOfCamera(cameraType_camera1, CAMERA_ID_1);
//        printf("********************cameraType_camera1.algType=%d  \n", cameraType_camera1.algType);
//
//        switch (cameraType_camera1.algType) {
//            case ALGORITHM_TYPE_NOT: {
//                /* 不启动算法 */
//
//            }
//                break;
//            case ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION: {
//                /* 英国工程机械-BSD相机-目标检测 */
//                detecterList_camera1.detecterUkBsdOd.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterUkBsdOd.addVisAlarmDecision(cameraType_camera1.adType,
//                                                                         G3_Configuration::getInstance().getTdClassThreshold());
//                threadPool.start(detecterList_camera1.detecterUkBsdOd);
//
//            }
//                break;
//
//            case ALGORITHM_TYPE_SBST_160_SEMANTIC_SEGMENTATION: {
//                /* 英国工程机械-160°相机-语义分割 */
//                detecterList_camera1.detecterSbst160Ss.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterSbst160Ss.addVisAlarmDecision(cameraType_camera1.adType, 0);
//                threadPool.start(detecterList_camera1.detecterSbst160Ss);
//            }
//                break;
//            case ALGORITHM_TYPE_DSM_METRO: {
//                /* 地铁DSM */
//                detecterList_camera1.detecterMtDsm.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterMtDsm.addVisAlarmDecision(cameraType_camera1.adType, 0);
//                threadPool.start(detecterList_camera1.detecterMtDsm);
//            }
//                break;
//            case ALGORITHM_TYPE_GESTURE_TJ_METRO: {
//                /* 天津地铁手势 */
//                detecterList_camera1.detecterMtGesture.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterMtGesture.addVisAlarmDecision(cameraType_camera1.adType, 0);
//                threadPool.start(detecterList_camera1.detecterMtGesture);
//            }
//                break;
//
//            case ALGORITHM_TYPE_SBST_160_OBJECT_DETECTION: {
//                /* 新加坡巴士-160度镜头-目标检测 */
//                detecterList_camera1.detecterSbst160Od.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterSbst160Od.addVisAlarmDecision(cameraType_camera1.adType,
//                                                                           G3_Configuration::getInstance().getSbst160OdClassThreshold());
//                threadPool.start(detecterList_camera1.detecterSbst160Od);
//            }
//                break;
//
//            case ALGORITHM_TYPE_ADAS_160_NORMAL: {
//                /* ADAS-160度镜头 */
//                detecterList_camera1.detecterAdas160Normal.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterAdas160Normal.addVisAlarmDecision(cameraType_camera1.adType, 0);
//                threadPool.start(detecterList_camera1.detecterAdas160Normal);
//            }
//                break;
//
//            case ALGORITHM_TYPE_DE_BSD_OBJECT_DETECTION: {
//                /* 德国公交车-BSD相机-目标检测 */
//                detecterList_camera1.detecterDEBsdOd.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterDEBsdOd.addVisAlarmDecision(cameraType_camera1.adType,
//                                                                         G3_Configuration::getInstance().getDeBsdOdClassThreshold());
//                threadPool.start(detecterList_camera1.detecterDEBsdOd);
//            }
//                break;
//
//            case ALGORITHM_TYPE_ADAS_60_NORMAL: {
//                /* ADAS-60度镜头 */
//                detecterList_camera1.detecterAdas60Normal.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterAdas60Normal.addVisAlarmDecision(cameraType_camera1.adType, 0);
//                threadPool.start(detecterList_camera1.detecterAdas60Normal);
//            }
//                break;
//
//            case ALGORITHM_TYPE_DSM_MUV: {
//                /* 新加坡MUV的DSM算法 */
//                detecterList_camera1.detecterMuvDsm.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterMuvDsm.addVisAlarmDecision(cameraType_camera1.adType, 0);
//                threadPool.start(detecterList_camera1.detecterMuvDsm);
//            }
//                break;
//
//            case ALGORITHM_TYPE_DVR: {
//                /* DVR算法 */
//                detecterList_camera1.detecterDvr.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterDvr.addVisAlarmDecision(cameraType_camera1.adType, 0);
//                threadPool.start(detecterList_camera1.detecterDvr);
//            }
//                break;
//
//            case ALGORITHM_TYPE_KOM_BSD_OBJECT_DETECTION: {
//                /* 小松的CVBS镜头侧边行人目标检测的算法 */
//                detecterList_camera1.detecterKomBsdOd.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterKomBsdOd.addVisAlarmDecision(cameraType_camera1.adType,
//                                                                          G3_Configuration::getInstance().getKomBsdOdClassThreshold());
//                threadPool.start(detecterList_camera1.detecterKomBsdOd);
//            }
//                break;
//
//            case ALGORITHM_TYPE_ADAS_160_SBST: {
//                /* SBST-ADAS-160度镜头 */
//                detecterList_camera1.detecterAdas160Sbst.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterAdas160Sbst.addVisAlarmDecision(cameraType_camera1.adType,0);
//                threadPool.start(detecterList_camera1.detecterAdas160Sbst);
//            }
//                break;
//
//            case ALGORITHM_TYPE_DSM_NORMAL: {
//                /* 标准DSM算法 */
//                detecterList_camera1.detecterDsmNormal.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterDsmNormal.addVisAlarmDecision(cameraType_camera1.adType,0);
//                threadPool.start(detecterList_camera1.detecterDsmNormal);
//            }
//                break;
//
//            case ALGORITHM_TYPE_UK_DD_QRCODE: {
//                /* 英国DD的二维码识别 */
//                detecterList_camera1.detecterUkDdQrCode.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterUkDdQrCode.addVisAlarmDecision(cameraType_camera1.adType,
//                                                                         G3_Configuration::getInstance().getTdClassThreshold());
//                threadPool.start(detecterList_camera1.detecterUkDdQrCode);
//            }
//                break;
//            case ALGORITHM_TYPE_DSM_FB: {
//                /* DSM-识别安全带 */
//                detecterList_camera1.detecterDsmFb.init(cameraType_camera1.cameraId, *this);
//                detecterList_camera1.detecterDsmFb.addVisAlarmDecision(cameraType_camera1.adType,0);
//                threadPool.start(detecterList_camera1.detecterDsmFb);
//            }
//                break;
//
//
//
//
//        }
//
//        /* 看看是不是处于V6模式 */
//        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6){
//            /* 不是V6模式，那么就需要启动第二个算法 */
//
//            /* 加个间隔 避免读配置文件失败 */
//            usleep(100 * 1000);
//            /* 再开启镜头2对应的算法 */
//            CameraType cameraType_camera2;
//            G3_Configuration::getInstance().getCameraTypeInfoOfCamera(cameraType_camera2, CAMERA_ID_2);
//            switch (cameraType_camera2.algType) {
//                case ALGORITHM_TYPE_NOT: {
//                    /* 不启动算法 */
//
//                }
//                    break;
//                case ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION: {
//                    /* 英国工程机械-BSD相机-目标检测 */
//                    detecterList_camera2.detecterUkBsdOd.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterUkBsdOd.addVisAlarmDecision(cameraType_camera2.adType,
//                                                                             G3_Configuration::getInstance().getTdClassThreshold());
//                    threadPool.start(detecterList_camera2.detecterUkBsdOd);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_SBST_160_SEMANTIC_SEGMENTATION: {
//                    /* 新加坡巴士-160度相机-语义分割 */
//                    detecterList_camera2.detecterSbst160Ss.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterSbst160Ss.addVisAlarmDecision(cameraType_camera2.adType, 0);
//                    threadPool.start(detecterList_camera2.detecterSbst160Ss);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_DSM_METRO: {
//                    /* 地铁DSM */
//                    detecterList_camera2.detecterMtDsm.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterMtDsm.addVisAlarmDecision(cameraType_camera2.adType, 0);
//                    threadPool.start(detecterList_camera2.detecterMtDsm);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_GESTURE_TJ_METRO: {
//                    /* 天津地铁手势 */
//                    detecterList_camera2.detecterMtGesture.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterMtGesture.addVisAlarmDecision(cameraType_camera2.adType, 0);
//                    threadPool.start(detecterList_camera2.detecterMtGesture);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_SBST_160_OBJECT_DETECTION: {
//                    /* 新加坡-160度镜头-目标检测 */
//                    detecterList_camera2.detecterSbst160Od.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterSbst160Od.addVisAlarmDecision(cameraType_camera2.adType,
//                                                                               G3_Configuration::getInstance().getSbst160OdClassThreshold());
//                    threadPool.start(detecterList_camera2.detecterSbst160Od);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_ADAS_160_NORMAL: {
//                    /* ADAS-160度镜头 */
//                    detecterList_camera2.detecterAdas160Normal.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterAdas160Normal.addVisAlarmDecision(cameraType_camera2.adType, 0);
//                    threadPool.start(detecterList_camera2.detecterAdas160Normal);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_DE_BSD_OBJECT_DETECTION: {
//                    /* 德国公交车-BSD相机-目标检测 */
//                    detecterList_camera2.detecterDEBsdOd.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterDEBsdOd.addVisAlarmDecision(cameraType_camera2.adType,
//                                                                             G3_Configuration::getInstance().getDeBsdOdClassThreshold());
//                    threadPool.start(detecterList_camera2.detecterDEBsdOd);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_ADAS_60_NORMAL: {
//                    /* ADAS-60度镜头 */
//                    detecterList_camera2.detecterAdas60Normal.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterAdas60Normal.addVisAlarmDecision(cameraType_camera2.adType, 0);
//                    threadPool.start(detecterList_camera2.detecterAdas60Normal);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_DSM_MUV: {
//                    /* 新加坡MUV的DSM算法 */
//                    detecterList_camera2.detecterMuvDsm.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterMuvDsm.addVisAlarmDecision(cameraType_camera2.adType, 0);
//                    threadPool.start(detecterList_camera2.detecterMuvDsm);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_DVR: {
//                    /* DVR算法 */
//                    detecterList_camera2.detecterDvr.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterDvr.addVisAlarmDecision(cameraType_camera2.adType, 0);
//                    threadPool.start(detecterList_camera2.detecterDvr);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_KOM_BSD_OBJECT_DETECTION: {
//                    /* 小松的CVBS镜头侧边行人目标检测的算法 */
//                    detecterList_camera2.detecterKomBsdOd.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterKomBsdOd.addVisAlarmDecision(cameraType_camera2.adType,
//                                                                              G3_Configuration::getInstance().getKomBsdOdClassThreshold());
//                    threadPool.start(detecterList_camera2.detecterKomBsdOd);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_ADAS_160_SBST: {
//                    /* SBST-ADAS-160度镜头 */
//                    detecterList_camera2.detecterAdas160Sbst.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterAdas160Sbst.addVisAlarmDecision(cameraType_camera2.adType,0);
//                    threadPool.start(detecterList_camera2.detecterAdas160Sbst);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_DSM_NORMAL: {
//                    /* 标准DSM算法 */
//                    detecterList_camera2.detecterDsmNormal.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterDsmNormal.addVisAlarmDecision(cameraType_camera2.adType,0);
//                    threadPool.start(detecterList_camera2.detecterDsmNormal);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_UK_DD_QRCODE: {
//                    /* 英国DD的二维码识别 */
//                    detecterList_camera2.detecterUkDdQrCode.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterUkDdQrCode.addVisAlarmDecision(cameraType_camera2.adType,
//                                                                                G3_Configuration::getInstance().getTdClassThreshold());
//                    threadPool.start(detecterList_camera2.detecterUkDdQrCode);
//                }
//                    break;
//
//                case ALGORITHM_TYPE_DSM_FB: {
//                    /* 标准DSM算法 */
//                    detecterList_camera2.detecterDsmFb.init(cameraType_camera2.cameraId, *this);
//                    detecterList_camera2.detecterDsmFb.addVisAlarmDecision(cameraType_camera2.adType,0);
//                    threadPool.start(detecterList_camera2.detecterDsmFb);
//                }
//                    break;
//            }
//
////        /* 如果是G4-MT，那么需要跑多一路算法(目前应该定死是信号灯识别) */
////        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G4MT_UNIT){
////            usleep(100*1000);
////            detecterList_camera3.detecterUkBsdOd.init(CAMERA_ID_2,*this);
////            detecterList_camera3.detecterUkBsdOd.addVisAlarmDecision(cameraType_camera2.adType,G3_Configuration::getInstance().getTdClassThreshold());
////            threadPool.start(detecterList_camera3.detecterUkBsdOd);
////        }
//
//        }else{
//            /* 是V6模式。不用启动第二个算法 */
//            ; // not to do
//        }





    }

    Vehicle_RealtimeStatus DetectUnitManager::getVehicleRealtimeStatus() {
        return curVehicleStatus;
    }

    void DetectUnitManager::setTurnSignal(const bool trunL, const bool trunR) {
        curVehicleStatus.turnL |= trunL;
        curVehicleStatus.turnR |= trunR;
    }

    void DetectUnitManager::setReverseSignal(const bool reverse) {
        curVehicleStatus.reverse |= reverse;
    }

    void DetectUnitManager::setDoorSignal(const bool door) {
        curVehicleStatus.backDoor |= door;
        curVehicleStatus.frontDoor |= door;
    }

    bool DetectUnitManager::getCameraDetecterWorkingStatus(const int cameraId) {
        bool isopen = true;
        bool isWorking = true;
        uint64_t intervalTime = 0;
        /* 遍历算法列表 */
        for (std::size_t i = 0; i < detecterList.size(); i++) {
            /* 判断一下是不是与要查询的镜头一致 */
            if (detecterList[i].cameraId == cameraId) {
                /* 根据算法类型确定从哪个指针读状态 */
                switch (G3_Configuration::getInstance().getAlgorithmTypeOfCamera(cameraId)) {
                    case ALGORITHM_TYPE_NOT: {
                        /* 不启动算法 */
                        isopen = true;
                        isWorking = true;

                    }
                        break;
                    case ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION: {
                        /* 英国工程机械-BSD相机-目标检测 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterUkBsdOd->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterUkBsdOd->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;


                    }
                        break;

                    case ALGORITHM_TYPE_SBST_160_SEMANTIC_SEGMENTATION: {
                        /* 英国工程机械-160°相机-语义分割 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterSbst160Ss->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterSbst160Ss->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;

                    }
                        break;
                    case ALGORITHM_TYPE_DSM_METRO: {
                        /* 地铁DSM */

//                /* 直接成功 */
                        isopen = true;
                        isWorking = true;
                    }
                        break;
                    case ALGORITHM_TYPE_GESTURE_TJ_METRO: {
                        /* 天津地铁手势 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterMtGesture->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterMtGesture->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;
                    }
                        break;

                    case ALGORITHM_TYPE_SBST_160_OBJECT_DETECTION: {
                        /* 新加坡巴士-160度镜头-目标检测 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterSbst160Od->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterSbst160Od->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;
                    }
                        break;

                    case ALGORITHM_TYPE_ADAS_160_NORMAL: {
                        /* ADAS-160度镜头 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterAdas160Normal->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterAdas160Normal->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;
                    }
                        break;

                    case ALGORITHM_TYPE_ADAS_160_SBST: {
                        /* SBST-ADAS-160度镜头 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterAdas160Sbst->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterAdas160Sbst->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;
                    }
                        break;

                    case ALGORITHM_TYPE_DE_BSD_OBJECT_DETECTION: {
                        /* 德国公交车-BSD相机-目标检测 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterDEBsdOd->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterDEBsdOd->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;
                    }
                        break;

                    case ALGORITHM_TYPE_ADAS_60_NORMAL: {
                        /* ADAS-60度镜头 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterAdas60Normal->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterAdas60Normal->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;

                    }
                        break;

                    case ALGORITHM_TYPE_DSM_MUV: {
                        /* 新加披的MUV的DSM算法 */

//                /* 直接成功 */
                        isopen = true;
                        isWorking = true;

                    }
                        break;

                    case ALGORITHM_TYPE_KOM_BSD_OBJECT_DETECTION: {
                        /* 小松的CVBS镜头侧边行人目标检测的算法 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterKomBsdOd->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterKomBsdOd->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;

                    }
                        break;

                    case ALGORITHM_TYPE_DSM_NORMAL: {
                        /* 标准DSM算法 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterDsmNormal->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterDsmNormal->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;

                    }
                        break;

                    case ALGORITHM_TYPE_UK_DD_QRCODE: {
                        /* 英国DD的二维码识别 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterUkDdQrCode->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterUkDdQrCode->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;

                    }
                        break;

                    case ALGORITHM_TYPE_DSM_FB: {
                        /* DSM-识别安全带 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterDsmFb->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterDsmFb->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;

                    }
                        break;

                    case ALGORITHM_TYPE_FORKLIFT_160_OBJECT_DETECTION: {
                        /* 叉车-160度镜头-目标检测 */

                        /* 先看看是不是初始化成功了 */
                        isopen = detecterList[i].detecterForklift160Od->detectOpen;
                        /* 判断下识别间隔是不是在允许的范围内 */
                        intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                       detecterList[i].detecterForklift160Od->getLastDetectTime();
                        isWorking = intervalTime < MAX_DETECT_INTERVAL_TIME;

                    }
                        break;
                }
            } else {
                /* 不一致就直接不管 */
                ; //not to do
            }


        }
        return isopen && isWorking;
    }

    void DetectUnitManager::onGetDetectInof_CameraStatus(G3DetectionResult &detectionInfo) {
        callback->onGetDetectInof_CameraStatus(detectionInfo);
    }

    void DetectUnitManager::onGetDetectInfo_BarCode(G3DetectionResult &detectionInfo,
                                                    std::vector<BarCodeInfo> &barCodeInfoList) {
        callback->onGetDetectInfo_BarCode(detectionInfo, barCodeInfoList);
    }

    bool DetectUnitManager::isCameraAlgShouldWorkBySignal(const CAMERAIDLIST cameraId) {
        bool ret = true;
        std::vector<CameraAlgWorkRule> ruleList = G3_Configuration::getInstance().getCameraAlgWorkRuleList();
        /* 遍历规则列表 */
        for (std::size_t i = 0; i < ruleList.size(); i++) {
            /* 先对应的镜头，只认找到的第一个 */
            if (ruleList[i].cameraId == cameraId) {
                /* 找到镜头对应的规则了,看看是不是满足需要工作的规则 */
                int workSignalRet = checkCameraAlgWorkSwitchSignal(ruleList[i].workSignal);
                /* 找到镜头对应的规则了,看看是不是满足需要需要暂停的规则 */
                int pauseSignalRet = checkCameraAlgWorkSwitchSignal(ruleList[i].pauseSignal);
                /* 只有在  （满足工作规则/工作规则无效 + 不满足暂停规则/暂停规则无效） 的情况下，算法才可以工作 */
                ret = (workSignalRet != 0) && (pauseSignalRet != 1);
                /* 退出遍历 */
                break;
            } else { ; //not to do
            }
        }
        return ret;
    }

    int DetectUnitManager::checkCameraAlgWorkSwitchSignal(const CameraAlgWorkRule_SignalSet signalSet) {
        int ret = 2;
        /* 先看看是不是无效值 */
        int intValue = 0;
        memcpy(&intValue, &signalSet, sizeof(signalSet));
        if (intValue != 0) {
            /* 不是无效值，那么就需要一个个信号判断了 */
            bool isSufficient = true;
            /* 看看是不是需要左转 */
            if (signalSet.turnL) {
                isSufficient &= curVehicleStatus.turnL;
            }
            /* 看看是不是需要右转 */
            if (signalSet.turnR) {
                isSufficient &= curVehicleStatus.turnR;
            }
            /* 看看是不是需要车门 */
            if (signalSet.door) {
                isSufficient &= (curVehicleStatus.backDoor || curVehicleStatus.frontDoor);
            }
            /* 看看是不是需要倒车 */
            if (signalSet.reverse) {
                isSufficient &= curVehicleStatus.reverse;
            }
            /* 整理结果 0：不满足   1：满足 */
            ret = isSufficient ? 1 : 0;
        } else {
            /* 是无效值，直接不用往下判断了 */
            ret = 2;
        }
        return ret;
    }

    bool DetectUnitManager::getAllDetecterWorkingStatus() {
        bool isopen = true;
        bool isWorking = true;
        uint64_t intervalTime = 0;
        /* 遍历算法列表 */
        for (std::size_t i = 0; i < detecterList.size(); i++) {
            /* 根据算法类型确定从哪个指针读状态 */
            switch (G3_Configuration::getInstance().getAlgorithmTypeOfCamera(detecterList[i].cameraId)) {
                case ALGORITHM_TYPE_NOT: {
                    /* 不启动算法 */
                    isopen &= true;
                    isWorking &= true;

                }
                    break;
                case ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION: {
                    /* 英国工程机械-BSD相机-目标检测 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterUkBsdOd->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterUkBsdOd->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;


                }
                    break;

                case ALGORITHM_TYPE_SBST_160_SEMANTIC_SEGMENTATION: {
                    /* 英国工程机械-160°相机-语义分割 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterSbst160Ss->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterSbst160Ss->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;

                }
                    break;
                case ALGORITHM_TYPE_DSM_METRO: {
                    /* 地铁DSM */

                    /* 直接成功 */
                    isopen &= true;
                    isWorking &= true;
                }
                    break;
                case ALGORITHM_TYPE_GESTURE_TJ_METRO: {
                    /* 天津地铁手势 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterMtGesture->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterMtGesture->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;
                }
                    break;

                case ALGORITHM_TYPE_SBST_160_OBJECT_DETECTION: {
                    /* 新加坡巴士-160度镜头-目标检测 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterSbst160Od->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterSbst160Od->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;
                }
                    break;

                case ALGORITHM_TYPE_ADAS_160_NORMAL: {
                    /* ADAS-160度镜头 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterAdas160Normal->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterAdas160Normal->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;
                }
                    break;

                case ALGORITHM_TYPE_ADAS_160_SBST: {
                    /* SBST-ADAS-160度镜头 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterAdas160Sbst->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterAdas160Sbst->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;
                }
                    break;

                case ALGORITHM_TYPE_DE_BSD_OBJECT_DETECTION: {
                    /* 德国公交车-BSD相机-目标检测 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterDEBsdOd->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterDEBsdOd->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;
                }
                    break;

                case ALGORITHM_TYPE_ADAS_60_NORMAL: {
                    /* ADAS-60度镜头 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterAdas60Normal->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterAdas60Normal->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;

                }
                    break;

                case ALGORITHM_TYPE_DSM_MUV: {
                    /* 新加披的MUV的DSM算法 */

                    /* 直接成功 */
                    isopen &= true;
                    isWorking &= true;

                }
                    break;

                case ALGORITHM_TYPE_KOM_BSD_OBJECT_DETECTION: {
                    /* 小松的CVBS镜头侧边行人目标检测的算法 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterKomBsdOd->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterKomBsdOd->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;

                }
                    break;

                case ALGORITHM_TYPE_DSM_NORMAL: {
                    /* 标准DSM算法 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterDsmNormal->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterDsmNormal->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;

                }
                    break;

                case ALGORITHM_TYPE_UK_DD_QRCODE: {
                    /* 英国DD的二维码识别 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterUkDdQrCode->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterUkDdQrCode->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;

                }
                    break;

                case ALGORITHM_TYPE_DSM_FB: {
                    /* DSM-识别安全带 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterDsmFb->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterDsmFb->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;

                }
                    break;

                case ALGORITHM_TYPE_FORKLIFT_160_OBJECT_DETECTION: {
                    /* 叉车-160度镜头-目标检测 */

                    /* 先看看是不是初始化成功了 */
                    isopen &= detecterList[i].detecterForklift160Od->detectOpen;
                    /* 判断下识别间隔是不是在允许的范围内 */
                    intervalTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() -
                                   detecterList[i].detecterForklift160Od->getLastDetectTime();
                    isWorking &= intervalTime < MAX_DETECT_INTERVAL_TIME;
                }
                    break;
            }
        }
        return isopen && isWorking;
    }

    bool DetectUnitManager::isCameraAlgShouldWorkByC3(const CAMERAIDLIST cameraId) {
        bool ret = true;
        /* 跟镜头相关的规则列表 */
        C3SwitchRule ruleForCamera;
        /* 镜头应该对应的C3开关状态 */
        std::vector<C3SwitchRule> ruleList = G3_Configuration::getInstance().getC3SwicthRuleList();
        /* 根据镜头ID有不同的处理方式 */
        switch (cameraId) {
            case CAMERA_ID_1:{
                ruleForCamera = G3_Configuration::getInstance().getC3SwitchRuleByC3Id(C3ID_1);
            }
                break;
            case CAMERA_ID_2:{
                ruleForCamera = G3_Configuration::getInstance().getC3SwitchRuleByC3Id(C3ID_2);
            }
                break;
            default:{
                /* 遍历规则列表 */
                for (std::size_t i = 0; i < ruleList.size(); i++) {
                    /* 看看这个规则是不是跟现在这个镜头相关 */
                    if (ruleList[i].cameraForEnable == cameraId) {
                        /* 跟这个镜头相关，那么保存下这条规则 */
                        ruleForCamera = ruleList[i];
                        /* 退出遍历 */
                        break;
                    } else { ; //not to do
                    }
                }
            }
                break;
        }

        /* 遍历完了看下是不是有对应的规则 */
        if(ruleForCamera.c3Id != -1){
            bool cameraC3Status = false;
            /* 从规则理的C3ID里面看看应该判断哪个C3 */
            switch (ruleForCamera.c3Id) {
                case C3ID_1:{
                    cameraC3Status = curVehicleStatus.c3Switch_c31;
                }
                    break;
                case C3ID_2:{
                    cameraC3Status = curVehicleStatus.c3Switch_c32;
                }
                    break;
                case C3ID_3:{
                    cameraC3Status = curVehicleStatus.c3Switch_c33;
                }
                    break;
                case C3ID_4:{
                    cameraC3Status = curVehicleStatus.c3Switch_c34;
                }
                    break;
            }

            //FIXME 可能那些傻逼会设置C3_1开启后还是镜头1这种，需要研究下怎么解决

            /* 根据相机跟挡位判断是否能工作 */
            switch (cameraId) {
                case CAMERA_ID_1:
                case CAMERA_ID_2:{
                    ret = !cameraC3Status;
                }
                    break;
                default:{
                    /* 根据挡位看看是不是能工作 */
                    if((ruleForCamera.cameraForEnable == cameraId && cameraC3Status)){
                        /* 可以工作，就不用理 */
                        ; //not to do
                    }else{
                        //不可以工作
                        ret = false;
                    }
                }
                    break;
            }
        }else{
            /* 没有对应的规则，直接不理 */
            ; //not to do
        }
        return ret;
    }

    bool DetectUnitManager::checkCanWorkByAlgSerialRule(const CAMERAIDLIST cameraId) {
        ruleLock.lock();
        bool canWork = false;
        /* 先看看是不是有算法串行规则 */
        if(G3_Configuration::getInstance().getAlgSerialRule().empty()){
            // 没有串行规则，那么就直接允许执行
            canWork = true;
        }else{
            /* 有算法串行规则，那么获取下当前镜头是否可以执行 */
            switch (cameraId) {
                case CAMERA_ID_1:{
                    canWork = G3_Configuration::getInstance().getAlgSerialRule()[serialRuleIndex].camera1 && (!ruleFinishRet.camera1);
                }
                    break;
                case CAMERA_ID_2:{
                    canWork = G3_Configuration::getInstance().getAlgSerialRule()[serialRuleIndex].camera2 && (!ruleFinishRet.camera2);
                }
                    break;
                case CAMERA_ID_3:{
                    canWork = G3_Configuration::getInstance().getAlgSerialRule()[serialRuleIndex].camera3 && (!ruleFinishRet.camera3);
                }
                    break;
                case CAMERA_ID_4:{
                    canWork = G3_Configuration::getInstance().getAlgSerialRule()[serialRuleIndex].camera4 && (!ruleFinishRet.camera4);
                }
                    break;
            }
        }
        ruleLock.unlock();
//        printf("cameraId=%d   canWork=%d  \n",cameraId,canWork);

        return canWork;
    }

    void DetectUnitManager::changAlgSerialRuleIndex(const CAMERAIDLIST cameraId) {
        ruleLock.lock();
        /* 先看看是不是有算法串行规则 */
        if(!G3_Configuration::getInstance().getAlgSerialRule().empty()){
            /* 有规则，那么记录下执行结果 */
            switch (cameraId) {
                case CAMERA_ID_1:{
                    ruleFinishRet.camera1 = true;
                }
                    break;
                case CAMERA_ID_2:{
                    ruleFinishRet.camera2 = true;
                }
                    break;
                case CAMERA_ID_3:{
                    ruleFinishRet.camera3 = true;
                }
                    break;
                case CAMERA_ID_4:{
                    ruleFinishRet.camera4 = true;
                }
                    break;
            }
            /* 检查下当前完成的结果数是否达到了本条规则需要的完成的 */
            bool isAllFinish = true;
            isAllFinish &= (G3_Configuration::getInstance().getAlgSerialRule()[serialRuleIndex].camera1 ? ruleFinishRet.camera1 : true);
            isAllFinish &= (G3_Configuration::getInstance().getAlgSerialRule()[serialRuleIndex].camera2 ? ruleFinishRet.camera2 : true);
            isAllFinish &= (G3_Configuration::getInstance().getAlgSerialRule()[serialRuleIndex].camera3 ? ruleFinishRet.camera3 : true);
            isAllFinish &= (G3_Configuration::getInstance().getAlgSerialRule()[serialRuleIndex].camera4 ? ruleFinishRet.camera4 : true);
            if(isAllFinish){
                /* 所有结果都完成了，那么就先移动下标 */
                serialRuleIndex = (serialRuleIndex >= G3_Configuration::getInstance().getAlgSerialRule().size() - 1) ? 0 : (serialRuleIndex + 1);
                /* 清空下结果 */
                ruleFinishRet = {false, false, false, false,0};
            }
        }
        ruleLock.unlock();
    }


} // namespace vis
