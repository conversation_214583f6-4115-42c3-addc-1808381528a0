//
// Created by Administrator on 2024/10/11.
//


#include "VisDetecter_DSM_Normal.h"
#include "utils/algorithmSrc/src_dsm_std/dsmstdShareDefine.h"
#include "std_run.h"
#include "std_resnet18.h"
#include "utils/algorithmSrc/src_dsm_std/std_get_alarm.h"
#include "utils/algorithmSrc/src_dsm_std/std_dsmini.h"
#include "XuLog.h"
#include "XuTimeUtil.h"
#include <iostream>

dsm_std::DSMInfo alarm_stdinfo;
dsm_std::globals globalstd;

namespace vis {
    VisDetecter_DSM_Normal::VisDetecter_DSM_Normal() {
        curAlgorithmType = ALGORITHM_TYPE_DSM_NORMAL;
    }

    void VisDetecter_DSM_Normal::doDetecter() {
    //    dsm_std::DSMInfo alarm_stdinfo;
    //    dsm_std::globals globalstd;
       dsm_std::LoadConfigFile("conf.yaml", globalstd);
       dsm_std::init_DSMInfo_parmas(alarm_stdinfo);
       dsm_std::ini::iniReader DSMconfig;
       bool ret = DSMconfig.ReadConfig("./model/DSM.ini"); //?????????????
       if (ret == false)
       {
           printf("ReadConfig is Error,Cfg=%s", "./model/DSM.ini");

       }
    // printf("*****************---**********zwx in line %d  \n", __LINE__);
       std::vector<cv::Point2i> tempbelt_point;
       bool debugconfig_ret = dsm_std::LoadbeltConfigFile("./model/muv_dsm_deconfig.yml");
       tempbelt_point = alarm_stdinfo.belt_point;
       if (debugconfig_ret == false)
       {
           printf("ReadConfig is Error,Cfg=%s", "debug_config.yml");
           // return -1;
       }
       std::cout<<"dsmstd: !!!!!!!----------------------------------------alarm_stdinfo.belt_point =  " <<alarm_stdinfo.belt_point.size()<<std::endl;
       printf("**********XU in line %d  \n", __LINE__);
       // string face_model = DSMconfig.ReadString("path", "yolov5sfacePt", "");
       // string  Resnet_model = DSMconfig.ReadString("path", "Resnet18mpt", "");
       // string labels_txt_file = DSMconfig.ReadString("path", "Resnet18labelPt", "");
       //        string res_weight = DSMconfig.ReadInt("net-resnet18", "res_weight", 0);
       //        string res_height = DSMconfig.ReadInt("net-resnet18", "res_height", 0);
       // dsm_std::Net_config yolov5_nets;  // choice = [yolov5s, yolov5m, yolov5l]
       // yolov5_nets.netname = face_model;
       cv::Mat back_frame;

    //    dsm_std::AlarmA a;
    //    dsm_std::AlarmB b;
    //    dsm_std::AlarmC c;
    //    dsm_std::AlarmD d;
    //    dsm_std::AlarmE e;
    //    dsm_std::AlarmF f;
    //    dsm_std::AlarmG g;
    //    dsm_std::AlarmH h;
    //    dsm_std::AlarmI i;
    //    dsm_std::AlarmBox boxstd(&a, &b, &c, &d, &e,&f, &g, &h,&i);

        // 动态创建 Alarm 对象并存入 vector
        // std::vector<dsm_std::Alarm*> alarms = {
        //     new dsm_std::AlarmA(),
        //     new dsm_std::AlarmB(),
        //     new dsm_std::AlarmC(),
        //     // 可自由增删：new dsm_std::AlarmD(), ...
        // };
    
        // 根据配置文件动态加载不同数量的 Alarm
        std::vector<dsm_std::Alarm*> alarms;
        if (globalstd.eye_button) 
        {
            alarms.push_back(new dsm_std::AlarmA());
        }
        if (globalstd.mouth_button) 
        {
            alarms.push_back(new dsm_std::AlarmB());
        }
        if (globalstd.smoking_button) 
        {
            alarms.push_back(new dsm_std::AlarmC());
        }
        if (globalstd.phone_button) 
        {
            alarms.push_back(new dsm_std::AlarmD());
        }
        if (globalstd.hat_button) 
        {
            alarms.push_back(new dsm_std::AlarmE());
        }
        if (globalstd.seatbelt_button) 
        {
            alarms.push_back(new dsm_std::AlarmF());
        }
        if (globalstd.lookaround_button) 
        {
            alarms.push_back(new dsm_std::AlarmG());
        }
        if (globalstd.Blindspot_button) 
        {
            alarms.push_back(new dsm_std::AlarmH());
        }
        if (globalstd.facemissing_button) 
        {
            alarms.push_back(new dsm_std::AlarmI());
        }
        // 初始化 AlarmBox
        dsm_std::AlarmBox boxstd(alarms);

       dsm_std::Kernelstd kernelstd;
       dsm_std::Net_config yolov5_net = {0.81, 0.52, 0.6, "yolov5s"};
       dsm_std::YOLO5S yolo_model(yolov5_net);

       cv::Mat frame;
       int subway_star = true;
       int state_of = 0;
       const int input_width = 1280;
       const int input_height = 720;


       uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
       cv::Mat yuvImg = cv::Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);
       cv::Mat srcimg = cv::Mat::zeros(input_height, input_width, CV_8UC3);
       alarm_stdinfo.face_point.resize(2);
       alarm_stdinfo.five_point.resize(5);
       int frame_cnt = 0;

        /* ��ǰ��ͷ�Ƿ��ڵ� */
        bool isCamCoverd = false;
        /* ��ǰ��������� */
        float imgBrightness = -1;
        /* ��ǰ�ľ�ͷ�Ƿ�ȫ�� */
        bool isFullBlack = false;
        /* ��ͷ�ڵ���ȫ��ͨ������������Ƿ���Ҫ�����ı��� */
        int camcoverd_cnt = 0;
        int fullblack_cnt = 0;
        
       //��ȡCANЭ������İ�ȫ���źţ��Լ�ת����źź��㷨����ѡ�͡�
       dsm_std::ExternalParam exterIOparam(9,false, false, false, dsm_std::SafeBeltStatus::SAFE_BELT_ERROR); 
    //    int sumtable = globalstd.Blindspot_button + globalstd.eye_button + globalstd.facemissing_button + globalstd.hat_button + globalstd.lookaround_button +
    //    globalstd.mouth_button + globalstd.phone_button + globalstd.seatbelt_button + globalstd.smoking_button;
    //    std::cout << "****  Done sumtable. "  << sumtable << std::endl;
       exterIOparam.table = 9; //����ʵ�������ù���������
    //    printf("**********while (subway_star)  in line %d  \n", __LINE__);

        /* 是否需要清理识别信息 */
        bool needclean = false;

       while (subway_star)
       {
            ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData, curVehicleStatus,&needclean);
           if (ret != 0)
           {
               printf("get yuv data failed!  \n");
               usleep(5 * 1000);
               continue;
           }
           frame_cnt ++;

           yuvImg.data = cameraYuvData;
           uint8_t md5bytes[16] = {};
           // getMD5OfBytes(cameraYuvData,1382400,md5bytes); //��ȡMD5����

           rga_utils.imageTransformation(input_width, input_height, rga_utils.IMG_TYPE_NV21,
                                        cameraYuvData,
                                        input_width, input_height, rga_utils.IMG_TYPE_BGR888, srcimg.data);
            int cut_x = (input_width - input_height) * 0.5;
            int cut_y = 0;
            int cut_width = input_height;
            int cut_height = input_height;
            cv::Mat cutimg ;
            if (globalstd.seatbelt_button) 
            {
                 // 计算ROI宽高（确保正值）
                int beltwidth = alarm_stdinfo.belt_point[2].x - alarm_stdinfo.belt_point[0].x;
                int beltheight = alarm_stdinfo.belt_point[2].y - alarm_stdinfo.belt_point[0].y;
                if(beltwidth <= 0 || beltheight <= 0) {
                 printf("坐标无效，宽度或高度不能为负！");
                 continue;
                }
                cutimg = srcimg(cv::Rect(alarm_stdinfo.belt_point[0].x, alarm_stdinfo.belt_point[0].y, beltwidth, beltheight));
                // imwrite("cutimg.jpg", cutimg);
                
            }
            
            cv::Mat roi = srcimg(cv::Rect(cut_x, cut_y, cut_width, cut_height));
            cv::resize(roi, frame, cv::Size(640, 640));
            // imwrite("frame.jpg", frame);
        //    cv::cvtColor(yuvImg, srcimg_dsmstd, cv::COLOR_YUV2BGR_NV12);
        //    if (srcimg_dsmstd.empty())
        //    {
        //        std::cout << "ERROR srcimg is void " << std::endl;
        //    }
        //    cv::Mat dst(cv::Size(1280, 1280), CV_8UC3, cv::Scalar(0, 0, 0)); // ��������ɫ����ԭ����
        //    cv::Mat paddedSrc; // 
        //    //
        //    int padWidth = (dst.rows - srcimg_dsmstd.rows) / 2;
        //    cv::copyMakeBorder(srcimg_dsmstd, paddedSrc, padWidth, padWidth, 0, 0, cv::BORDER_CONSTANT, cv::Scalar(0, 0, 0));
        //    cv::resize(paddedSrc, frame, cv::Size(640, 640), 0, 0, cv::INTER_LINEAR); // ?????????
           
           if (frame.empty())
           {
               std::cout << "Done processing. " << std::endl;
               break;
           }
           
            std::cout << "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^Input stand_speed::  " << curVehicleStatus.speed << std::endl;
           try
           {
               exterIOparam.turnL = curVehicleStatus.turnL;
               exterIOparam.turnR = curVehicleStatus.turnR;
               exterIOparam.safe_belt = (curVehicleStatus.safebeltStatus == 0xFF) ? dsm_std::SafeBeltStatus::SAFE_BELT_ERROR : (curVehicleStatus.safebeltStatus == 0x00 ? dsm_std::SafeBeltStatus::SAFE_BELT_OFF : dsm_std::SafeBeltStatus::SAFE_BELT_ON);
               exterIOparam.back_gear = curVehicleStatus.reverse;
               int std_speed = curVehicleStatus.speed; // ???????km/h
               uint64_t beforeDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
               // std::cout << "std:aaaaaaaaaataurn_light=" << (curVehicleStatus.turnL || curVehicleStatus.turnR)  << std::endl;

               state_of = kernelstd.runs_detector(cutimg, frame, yolo_model, alarm_stdinfo, boxstd, std_speed, exterIOparam);
               // printf("BBB****************(%d,%d),(%d,%d),(%d,%d),(%d,%d),(%d,%d)*************************  \n",almuv_info.belt_point[0].x,almuv_info.belt_point[0].y,almuv_info.belt_point[1].x,almuv_info.belt_point[1].y,almuv_info.belt_point[2].x,almuv_info.belt_point[2].y,almuv_info.belt_point[3].x,almuv_info.belt_point[3].y,almuv_info.belt_point[4].x,almuv_info.belt_point[4].y);
               curDetectUseTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - beforeDetectTime;
               std::cout<< "*********************************   **********curDetectUseTime:        "<<curDetectUseTime<<std::endl;
               if (state_of == 1 || state_of == 0)
               {
                   std::cout << "_________MYS_DE_0430______"  << std::endl;
                   for (size_t i = 0; i < 2; i++)
                   {
                        if (alarm_stdinfo.face_point[i].x >= 640) 
                        {
                            alarm_stdinfo.face_point[i].x = 639;
                        }
                        if (alarm_stdinfo.face_point[i].x <0)
                        {
                            alarm_stdinfo.face_point[i].x = 0;
                        }
                        if (alarm_stdinfo.face_point[i].y  >= 640)
                        {
                            alarm_stdinfo.face_point[i].y = 639;
                        }

                   }
                   

                   
                   alarm_stdinfo.face_point[0] = cv::Point2i((alarm_stdinfo.face_point[0].x * 1.125 + 280),
                                                          alarm_stdinfo.face_point[0].y * 1.125);
                   alarm_stdinfo.face_point[1] = cv::Point2i((alarm_stdinfo.face_point[1].x * 1.125+ 280),
                                                          alarm_stdinfo.face_point[1].y * 1.125);
                   //关键点转换显示
                   alarm_stdinfo.five_point[0] = cv::Point2i((alarm_stdinfo.five_point[0].x * 1.125+ 280),
                                                          alarm_stdinfo.five_point[0].y * 1.125);
                   alarm_stdinfo.five_point[1] = cv::Point2i((alarm_stdinfo.five_point[1].x * 1.125+ 280),
                                                          alarm_stdinfo.five_point[1].y * 1.125);
                   alarm_stdinfo.five_point[2] = cv::Point2i((alarm_stdinfo.five_point[2].x * 1.125+ 280),
                                                          alarm_stdinfo.five_point[2].y * 1.125);
                   alarm_stdinfo.five_point[3] = cv::Point2i((alarm_stdinfo.five_point[3].x * 1.125+ 280),
                                                          alarm_stdinfo.five_point[3].y * 1.125);
                   alarm_stdinfo.five_point[4] = cv::Point2i((alarm_stdinfo.five_point[4].x * 1.125+ 280),
                                                          alarm_stdinfo.five_point[4].y * 1.125);
               std::cout<<"std:----------------------------------------output [1]box: alarm_stdinfo.face_point =  " <<alarm_stdinfo.face_point.size()<<std::endl;
               std::cout<<"std:----------------------------------------output [2]pos: alarm_stdinfo.five_point =  " <<alarm_stdinfo.five_point.size()<<std::endl;
               std::cout<<"std:----------------------------------------output [3]belt: alarm_stdinfo.belt_point =  " <<alarm_stdinfo.belt_point.size()<<std::endl;
               std::cout<<"std:----------------------------------------output [4]Angle: alarm_stdinfo.face_angle =  " <<alarm_stdinfo.face_angle<<std::endl;
               // 人脸坐标
               std::cout<<"std:----------------------------------------face_uppos= " <<alarm_stdinfo.face_point[0]<<std::endl;
               std::cout<<"std:----------------------------------------face_downpos=  " <<alarm_stdinfo.face_point[1]<<std::endl;
                                                          
               }
               else
               {
                   alarm_stdinfo.face_point.clear();
                   alarm_stdinfo.five_point.clear();
                   alarm_stdinfo.face_angle = 0;
                   
                   alarm_stdinfo.eye_alarm = false;
                   alarm_stdinfo.mouth_alarm = false;
                   alarm_stdinfo.smoking_alarm = false;
                   alarm_stdinfo.phone_alarm = false;
                   alarm_stdinfo.hat_alarm = false;
                   alarm_stdinfo.seatbelt_alarm = false;
                   alarm_stdinfo.Blindspot_alarm = false;
                   alarm_stdinfo.lookaround_alarm = false;
                std::cout << "----------------------------------------------------------------------------报警清空" << std::endl;
               }



               if (frame_cnt % 50 == 3) {
                    /* �ȼ���µ�ǰ֡�Ƿ��ڵ�����ȫ�ڣ����ó����� */
                    bool curIsCamCoverd = false;
                    bool curIsFullBlack = false;
                    /* ����µ�ǰ��ͷ��״̬ */
                    calcCamCoverd(srcimg,imgBrightness,curIsCamCoverd,curIsFullBlack);
                    /* �����������ܵ��ڵ��Ĵ��� */
                    if (curIsCamCoverd){
                        camcoverd_cnt++;
                    }else{
                        camcoverd_cnt = 0;
                    }
                    /* �����������ܵ�ȫ�ڵĴ��� */
                    if (curIsFullBlack){
                        fullblack_cnt++;
                    }else{
                        fullblack_cnt = 0;
                    }
                    /* ��������ܵ��ڵ������ﵽ����ֵ����ô���Ǳ��ڵ���  ��Ŀǰ��ʱʹ��BSD�� */
                    isCamCoverd = (camcoverd_cnt >= G3_Configuration::getInstance().getBsdCamcoverCountThreshold());
                    /* ��������ܵ�ȫ�ڴ����ﵽ����ֵ����ô����ȫ����  ��Ŀǰ��ʱʹ��BSD�� */
                    isFullBlack = (fullblack_cnt > G3_Configuration::getInstance().getBsdCamcoverCountThreshold());

                }

                toAlarmDecision(alarm_stdinfo,state_of,isCamCoverd,imgBrightness,isFullBlack,curDetectUseTime);

           }
           catch (const cv::Exception &ii)
           {
           }


            lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
       }
    }

    void VisDetecter_DSM_Normal::run() {
        std::string pthreadName = "NORMAL_DSM";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        doDetecter();
        pthread_setname_np(pthread_self(), "Finish");
    }

     void VisDetecter_DSM_Normal::toAlarmDecision(dsm_std::DSMInfo &alarm_stdinfo, const int state_of, bool cameraCovered,
                                                  float imgBrightness, bool isFullblack,
                                                  const uint16_t curDetectUseTime) {
         /* ?????�M???????MUV??????????????? */
         if (alarmDecisionList.alarmDecisionMuvFace != nullptr && alarmDecisionList.alarmDecisionMuvFace->isInited())
         {
             /* ???MUV???? */
              dsm_muv::DSMInfo almuv_info;
              almuv_info.isSameDriver = alarm_stdinfo.isSameDriver;
              almuv_info.face_score = alarm_stdinfo.face_score;
              almuv_info.face_point = alarm_stdinfo.face_point;
              almuv_info.five_point = alarm_stdinfo.five_point;
              almuv_info.belt_point = alarm_stdinfo.belt_point;
              almuv_info.face_angle = alarm_stdinfo.face_angle;
              almuv_info.eye_alarm = alarm_stdinfo.eye_alarm;
              almuv_info.smoking_alarm = alarm_stdinfo.smoking_alarm;
              almuv_info.seatbelt_alarm = alarm_stdinfo.seatbelt_alarm;
              almuv_info.Blindspot_alarm = alarm_stdinfo.Blindspot_alarm;
              almuv_info.lookaround_alarm = alarm_stdinfo.lookaround_alarm;
              almuv_info.camcover_alarm = alarm_stdinfo.camcover_alarm;
              almuv_info.phone_alarm = alarm_stdinfo.phone_alarm;
              almuv_info.facemissing_alarm = alarm_stdinfo.facemissing_alarm;
              almuv_info.hat_alarm = alarm_stdinfo.hat_alarm;

              almuv_info.fatigue_rank = alarm_stdinfo.fatigue_rank;
             alarmDecisionList.alarmDecisionMuvFace->parse_MUV_DSM_OD(almuv_info, state_of, curVehicleStatus,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
         }


         /* 看看是不是需要塞到标准DSM人脸 */
         if (alarmDecisionList.alarmDecisionStdFace != nullptr && alarmDecisionList.alarmDecisionStdFace->isInited())
         {
             alarmDecisionList.alarmDecisionStdFace->parse_STD_DSM_OD(alarm_stdinfo, curVehicleStatus,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
         }

     }

    void
    VisDetecter_DSM_Normal::calcCamCoverd(cv::Mat srcimg, float &imgBrightness, bool &isCamCoverd, bool &isFullBlack) {
        //G3�������㷨��ͼƬ��ԭʼͼƬ������ǰ��û��ʱ��ˮӡ�ģ�����

        //ͼ������imgBrightness��G4-GJ��Ŀ�õ������ڸ��ݻ������������ƵƵĿ���
        //isCamCoverd�����ж�����Ƿ��б��ڵ�
        //isFullBlack�����ж�ͼ���Ƿ�ȫ�ڣ�����������ε������ͨ·������ʱ�Ĺ����ϱ���


        //cvNamedWindow("pResizeGrayImg");
        //cvShowImage("pResizeGrayImg", mPpImgs->pResizeGrayImg);
        //cvWaitKey(0);

        //srcimg = imread("C://Users//lyc_s//Desktop//coverImg.bmp");

        float scale = 0.278;
        cv::Mat img_resize_rgb, img_resize_gray, img_canny;
        cv::resize(srcimg, img_resize_rgb, cv::Size(srcimg.cols * scale, srcimg.rows * scale), cv::INTER_NEAREST);

        //imshow("img_resize_rgb", img_resize_rgb);

        float del_percent = 0.05;
        img_resize_rgb = img_resize_rgb(cv::Rect(0, img_resize_rgb.rows * del_percent, img_resize_rgb.cols, img_resize_rgb.rows * (1 - del_percent) - 1));

        cv::cvtColor(img_resize_rgb, img_resize_gray, cv::COLOR_BGR2GRAY);


        //imshow("img_resize_gray", img_resize_gray);
        //waitKey(0);

        blur(img_resize_gray, img_resize_gray, cv::Size(5, 5));
        Canny(img_resize_gray, img_canny, 10, 20, 3);  // Canny(img_resize_gray, img_canny, 30, 60, 3);

        //imshow("canny", img_canny);
        //waitKey(0);

        //Mat element = getStructuringElement(MORPH_RECT, Size(3, 3));
        //erode(img_canny, img_canny, element);

        int white_cnt = 0;

        int total_cnt = img_canny.rows * img_canny.cols * img_canny.channels();

        for (int i = 0; i < total_cnt; i++)
            if (img_canny.data[i] > 200)
                white_cnt++;

        float ratio = (float)white_cnt / total_cnt;


        int sum_gray = 0;
        for (int i = 0; i < total_cnt; i++)
            sum_gray += img_resize_gray.data[i];
        float avg_gray = (float)sum_gray / total_cnt;


        //printf("ratio = %f\n", ratio);
        //imshow("canny", img_canny);
        //waitKey(0);

        //return ratio < 0.003 ? true : false;

        //get result
        imgBrightness = avg_gray;
        isCamCoverd = ((ratio < 0.005) || (avg_gray < 22)) ? true : false;
        isFullBlack = (white_cnt < 5 && avg_gray < 30) ? true : false;
    }


} // vis