//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/7.
//

#include "VisDetecter.h"
#include "VisAlarmDecision_Pedestrian_Area.h"
#include "XuFile.h"


namespace vis {



    bool VisDetecter::isDetectOpen() const {
        return detectOpen;
    }

    uint64_t VisDetecter::getLastDetectTime() const {
        return lastDetectTime;
    }


    void VisDetecter::addVisAlarmDecision(CameraType_AlarmDecisionType &adType, float classThreshold) {
        /* 先看看需不要添加区域行人的报警决策 */
        if (adType.pedestrian_area) {
            alarmDecisionList.alarmDecisionPedestrianArea = new VisAlarmDecision_Pedestrian_Area();
            alarmDecisionList.alarmDecisionPedestrianArea->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加区域车辆的报警决策 */
        if (adType.vehicle_area) {
            alarmDecisionList.alarmDecisionVehicleArea = new VisAlarmDecision_Vehicle_Area();
            alarmDecisionList.alarmDecisionVehicleArea->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加地铁人脸的报警决策 */
        if (adType.mt_dsm_face) {
            alarmDecisionList.alarmDecisionMtFace = new VisAlarmDecision_MT_Face();
            alarmDecisionList.alarmDecisionMtFace->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加地铁手势的报警决策 */
        if (adType.gesture_metro) {
            alarmDecisionList.alarmDecisionMtGesture = new VisAlarmDecision_MT_Gesture();
            alarmDecisionList.alarmDecisionMtGesture->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加车辆（TTC）的报警决策 */
        if (adType.vehicle_ttc) {
            alarmDecisionList.alarmDecisionVehicleStandard = new VisAlarmDecision_Vehicle_Standard();
            alarmDecisionList.alarmDecisionVehicleStandard->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加行人（TTC）的报警决策 */
        if (adType.pedestrian_ttc) {
            alarmDecisionList.alarmDecisionPedestrianStandard = new VisAlarmDecision_Pedestrian_Standard();
            alarmDecisionList.alarmDecisionPedestrianStandard->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加车道线（TTC）的报警决策 */
        if (adType.laneline) {
            alarmDecisionList.alarmDecisionLanelineAdas = new VisAlarmDecision_Laneline_adas();
            alarmDecisionList.alarmDecisionLanelineAdas->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加行人（R151）的报警决策 */
        if (adType.pedestrian_r151) {
            alarmDecisionList.alarmDecisionPedestrianR151 = new VisAlarmDecision_Pedestrian_R151();
            alarmDecisionList.alarmDecisionPedestrianR151->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加车辆（R151）的报警决策 */
        if (adType.vehicle_r151) {
            alarmDecisionList.alarmDecisionVehicleR151 = new VisAlarmDecision_Vehicle_R151();
            alarmDecisionList.alarmDecisionVehicleR151->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加行人（R158）的报警决策 */
        if (adType.pedestrian_r158) {
            alarmDecisionList.alarmDecisionPedestrianR158 = new VisAlarmDecision_Pedestrian_R158();
            alarmDecisionList.alarmDecisionPedestrianR158->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加车辆（R158）的报警决策 */
        if (adType.vehicle_r158) {
            alarmDecisionList.alarmDecisionVehicleR158 = new VisAlarmDecision_Vehicle_R158();
            alarmDecisionList.alarmDecisionVehicleR158->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加行人（R159）的报警决策 */
        if (adType.pedestrian_r159) {
            alarmDecisionList.alarmDecisionPedestrianR159 = new VisAlarmDecision_Pedestrian_R159();
            alarmDecisionList.alarmDecisionPedestrianR159->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加车辆（R159）的报警决策 */
        if (adType.vehicle_r159) {
            alarmDecisionList.alarmDecisionVehicleR159 = new VisAlarmDecision_Vehicle_R159();
            alarmDecisionList.alarmDecisionVehicleR159->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加行人（SBST后向）的报警决策 */
        if (adType.pedestrian_SBST_Backward) {
            alarmDecisionList.alarmDecisionPedestrianSbstBackward = new VisAlarmDecision_Pedestrian_SBST_Backward();
            alarmDecisionList.alarmDecisionPedestrianSbstBackward->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加车辆（SBST后向）的报警决策 */
        if (adType.vehicle_SBST_Backward) {
            alarmDecisionList.alarmDecisionVehicleSbstBackward = new VisAlarmDecision_Vehicle_SBST_Backward();
            alarmDecisionList.alarmDecisionVehicleSbstBackward->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加新加坡MUV人脸的报警决策 */
        if (adType.muv_dsm_face) {
            alarmDecisionList.alarmDecisionMuvFace = new VisAlarmDecision_MUV_Face();
            alarmDecisionList.alarmDecisionMuvFace->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加ADAS的限速标示的报警决策 */
        if (adType.trafficSign) {
            alarmDecisionList.alarmDecisionTsrAdas = new VisAlarmDecision_TSR_ADAS();
            alarmDecisionList.alarmDecisionTsrAdas->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加镜头状态的报警决策 */
        if (adType.camera_status) {
            alarmDecisionList.alarmDecisionCameraStatus = new VisAlarmDecision_CameraStatus();
            alarmDecisionList.alarmDecisionCameraStatus->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加新加坡特殊的ADAS车辆报警决策 */
        if (adType.spg_adas_vehicle) {
            alarmDecisionList.alarmDecisionVehicleSpgAdas = new VisAlarmDecision_Vehicle_SPG_ADAS();
            alarmDecisionList.alarmDecisionVehicleSpgAdas->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加英国DD的定位二维码识别的报警 */
        if (adType.uk_dd_qrcode) {
            alarmDecisionList.alarmDecisionDdQrCode = new VisAlarmDecision_DD_QRCode();
            alarmDecisionList.alarmDecisionDdQrCode->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加人（Security区域）的报警 */
        if (adType.pedestrian_area_security_model) {
            alarmDecisionList.alarmDecisionPedestrianAreaSecurityMode = new VisAlarmDecision_Pedestrian_Area_SecurityMode();
            alarmDecisionList.alarmDecisionPedestrianAreaSecurityMode->init(curCameraId,classThreshold,curDetectDataCallback);
        }
        /* 再看看需不需要添加标准的DSM人脸的报警 */
        if (adType.std_dsm_face) {
            alarmDecisionList.alarmDecisionStdFace = new VisAlarmDecision_STD_Face();
            alarmDecisionList.alarmDecisionStdFace->init(curCameraId,classThreshold,curDetectDataCallback);
        }
    }



    int VisDetecter::init(int cameraId, DetectDataCallback &detectDataCallback) {
        curCameraId = cameraId;
        curDetectDataCallback = &detectDataCallback;
        return 0;
    }

    std::vector<cv::Point> VisDetecter::getIgnoreAreaPoint1() {
        G3_Configuration &conf = G3_Configuration::getInstance();
        std::vector<cv::Point> point;
        std::vector<VISPoint> vPoint;

        /* 根据镜头id获取各自忽略的区域 */
        switch (curCameraId) {
            case CAMERA_ID_1:{
                vPoint = conf.getUndetectedAreaListCamera1()[0];
            }
                break;
            case CAMERA_ID_2:{
                vPoint = conf.getUndetectedAreaListCamera2()[0];
            }
                break;
            case CAMERA_ID_3:{
                vPoint = conf.getUndetectedAreaListCamera3()[0];
            }
                break;
            case CAMERA_ID_4:{
                vPoint = conf.getUndetectedAreaListCamera4()[0];
            }
                break;
        }



        /* 判断下是否需要降效 */
        if(needReduceDetectionEffect(curCameraId)){
            /* 需要降效，那么不识别区域也需要进行对应的位移和缩放 */
            /* 先获取图像中心点的坐标 */
            double centorX = MRV220_IMG_WIDTH / 2;
            double centorY = MRV220_IMG_HEIGHT / 2;
            /* 遍历所有点并进行坐标变换和转换格式 */
            for (std::size_t i = 0; i < vPoint.size(); ++i) {
                cv::Point cp;
                cp.x = ((vPoint[i].x - centorX) * G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorX;
                cp.y = ((vPoint[i].y - centorY) * G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorY;
                point.push_back(cp);
            }
        }else{
            /* 不需要降效，那么就直接转换格式就行了 */
            for (std::size_t i = 0; i < vPoint.size(); ++i) {
                cv::Point cp;
                cp.x = vPoint[i].x;
                cp.y = vPoint[i].y;
                point.push_back(cp);
            }
        }
        return point;
    }

    std::vector<cv::Point> VisDetecter::getIgnoreAreaPoint2() {
        G3_Configuration &conf = G3_Configuration::getInstance();
        std::vector<cv::Point> point;
        std::vector<VISPoint> vPoint;

        /* 根据镜头id获取各自忽略的区域 */
        switch (curCameraId) {
            case CAMERA_ID_1:{
                vPoint = conf.getUndetectedAreaListCamera1()[1];
            }
                break;
            case CAMERA_ID_2:{
                vPoint = conf.getUndetectedAreaListCamera2()[1];
            }
                break;
            case CAMERA_ID_3:{
                vPoint = conf.getUndetectedAreaListCamera3()[1];
            }
                break;
            case CAMERA_ID_4:{
                vPoint = conf.getUndetectedAreaListCamera4()[1];
            }
                break;
        }
        /* 判断下是否需要降效 */
        if(needReduceDetectionEffect(curCameraId)){
            /* 需要降效，那么不识别区域也需要进行对应的位移和缩放 */

            /* 先获取图像中心点的坐标 */
            double centorX = MRV220_IMG_WIDTH / 2;
            double centorY = MRV220_IMG_HEIGHT / 2;
            /* 遍历所有点并进行坐标变换和转换格式 */
            for (std::size_t i = 0; i < vPoint.size(); ++i) {
                cv::Point cp;
                /* 根据图像的缩放比例缩放此点跟图像中心点的距离 */
                cp.x = ((vPoint[i].x - centorX) * G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorX;
                cp.y = ((vPoint[i].y - centorY) * G3_Configuration::getInstance().getReduceEffectImgZoomRate()) + centorY;
                point.push_back(cp);
            }
        }else{
            /* 不需要降效，那么就直接转换格式就行了 */
            for (std::size_t i = 0; i < vPoint.size(); ++i) {
                cv::Point cp;
                cp.x = vPoint[i].x;
                cp.y = vPoint[i].y;
                point.push_back(cp);
            }
        }
        return point;
    }

    VisDetecter::CameraRealImgSize VisDetecter::getCameraRealImgSize(const int cameraId) {
        VisDetecter::CameraRealImgSize ret;
        /* 遍历一下获取输入类型 */
        switch (G3_Configuration::getInstance().getCameraInputSignalType(cameraId)) {
            case CAMERA_INPUT_TYPE_AHD_720P:
            case CAMERA_INPUT_TYPE_AHD_1080P:{
                /* 1080P和720P都是输出720P的图像 */
                ret.startX = 0;
                ret.startY = 0;
                ret.width = 1280;
                ret.height = 720;
            }
                break;
            case CAMERA_INPUT_TYPE_CVBS_D1_NTSC:{
                /* CVBS NTSC 分辨率为 720*480  且在图像正中间 */
                ret.startX = 280;
                ret.startY = 120;
                ret.width = 720;
                ret.height = 480;
            }
                break;
            case CAMERA_INPUT_TYPE_CVBS_D1_PAL:{
                /* CVBS PAL 分辨率为 720*576  且在图像正中间 */
                ret.startX = 280;
                ret.startY = 72;
                ret.width = 720;
                ret.height = 576;
            }
                break;
            default:{
                /* 其他分辨率就默认是720P的 */
                ret.startX = 0;
                ret.startY = 0;
                ret.width = 1280;
                ret.height = 720;
            }
                break;
        }
        return ret;
    }

    bool VisDetecter::needReduceDetectionEffect(const int cameraId) {
        bool ret = false;
        /* 获取下当前的降效信息 */
        CameraReduceEffectInfo reduceEffectInfo = G3_Configuration::getInstance().getCurCameraReduceEffectInfo();
        /* 获取对应镜头的降效结果 */
        switch (cameraId) {
            case CAMERA_ID_1:{
                ret = reduceEffectInfo.camera1;
            }
                break;

            case CAMERA_ID_2:{
                ret = reduceEffectInfo.camera2;
            }
                break;

            case CAMERA_ID_3:{
                ret = reduceEffectInfo.camera3;
            }
                break;

            case CAMERA_ID_4:{
                ret = reduceEffectInfo.camera4;
            }
                break;
        }
        return ret;
    }

    int VisDetecter::reduceEffectiveImg(uint8_t *imgData, const int imgDataLen, uint8_t *outBuf) {
        int ret = -1;
        /* 先看看降效画面的内存开了没有 */
        if(reduceEffectImg == nullptr){
            int brgBufLen = MRV220_IMG_WIDTH*MRV220_IMG_HEIGHT*3;
            /* 没有开辟，那么再检查下存放临时缩放图像的内存开了没有，没有就开辟 */
            if(scaleImgTempBuf == nullptr){
                scaleImgTempBuf = (uint8_t *) malloc(brgBufLen);
            }
            /* 开始开辟读入黑色图像 */
            reduceEffectImg = (uint8_t *) malloc(brgBufLen);
            /* 读黑色的图片到存放临时缩放图像的内存 */
            XuFile::getInstance().readFile(BLACK_YUV_FILE_PATHA,scaleImgTempBuf,MRV220_IMG_WIDTH*MRV220_IMG_HEIGHT*3/2);
            /* 转成BGR888存到降效时使用的黑色背景图像 */
            rgaUtils.imageTransformation(MRV220_IMG_WIDTH, MRV220_IMG_HEIGHT, rgaUtils.IMG_TYPE_NV21,
                                         scaleImgTempBuf,
                                         MRV220_IMG_WIDTH, MRV220_IMG_HEIGHT, rgaUtils.IMG_TYPE_BGR888, reduceEffectImg);
        }
        /* 把有效图像缩放一半 */
        rgaUtils.imageTransformation(MRV220_IMG_WIDTH, MRV220_IMG_HEIGHT, XuRGAUtils::IMG_TYPE_NV21,
                                     imgData, (MRV220_IMG_WIDTH * G3_Configuration::getInstance().getReduceEffectImgZoomRate()), (MRV220_IMG_HEIGHT * G3_Configuration::getInstance().getReduceEffectImgZoomRate()), XuRGAUtils::IMG_TYPE_BGR888, scaleImgTempBuf);
        /* 把缩放后的有效图像合成到降效画面的正中间 */
        ret = rgaUtils.synthesisImg_center((MRV220_IMG_WIDTH * G3_Configuration::getInstance().getReduceEffectImgZoomRate()), (MRV220_IMG_HEIGHT * G3_Configuration::getInstance().getReduceEffectImgZoomRate()), XuRGAUtils::IMG_TYPE_BGR888,
                                     scaleImgTempBuf, MRV220_IMG_WIDTH, MRV220_IMG_HEIGHT, XuRGAUtils::IMG_TYPE_BGR888, reduceEffectImg);
        /* 合成好了，把图像复制出去 */
        memcpy(outBuf,reduceEffectImg, ret);

        return ret;
    }

    int VisDetecter::getUltimateImg(uint8_t *nv21Data, const int nv21DataLen, uint8_t *outBuf) {
        int ret = -1;
        /* 先看看YUV数据长度是否合法 */
        if(nv21DataLen >= MRV220_IMG_WIDTH*MRV220_IMG_HEIGHT*3/2){
            /* 看看是不是需要降效 */
            if(needReduceDetectionEffect(curCameraId)){
                /* 需要降效，那么就获取降效后的图片 */
                ret = reduceEffectiveImg(nv21Data,nv21DataLen,outBuf);
            }else{
                /* 不用降效就直接转格式就行了 */
                ret = rgaUtils.imageTransformation(MRV220_IMG_WIDTH, MRV220_IMG_HEIGHT, rgaUtils.IMG_TYPE_NV21,
                                             nv21Data,
                                             MRV220_IMG_WIDTH, MRV220_IMG_HEIGHT, rgaUtils.IMG_TYPE_BGR888, outBuf);
            }
        }else{
            /* YUV数据长度不合法，直接不处理，返回失败 */
            ; //not to do

        }
        return ret;
    }


} // vis