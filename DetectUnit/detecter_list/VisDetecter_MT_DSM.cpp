//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/14.
//

#include "VisDetecter_MT_DSM.h"
#include "DSMGlobalVariable.h"
#include "XuTimeUtil.h"
using namespace std;
using namespace cv;

extern DSMInfo alarm_info;
extern face_message face_params;
extern globals globalparams;
extern alarm_timer respirator_timer, mouth_timer, eye_timer, yaw_timer, camcover_timer, leave_timer;

namespace vis {
    VisDetecter_MT_DSM::VisDetecter_MT_DSM() {
        curAlgorithmType = ALGORITHM_TYPE_DSM_METRO;
    }

    void VisDetecter_MT_DSM::run() {
        std::string pthreadName = "MT_DSM";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        doDetecter();
        pthread_setname_np(pthread_self(), "Finish");
    }

    void VisDetecter_MT_DSM::doDetecter() {
        LoadConfigFile("conf.yaml", globalparams);


        init_DSMInfo_parmas(alarm_info);
        init_global_params(respirator_timer);
        init_global_params(eye_timer);
        init_global_params(mouth_timer);
        init_global_params(yaw_timer);
        init_global_params(camcover_timer);
        init_global_params(leave_timer);
        //Net_config yolo_nets = {0.51, 0.3, 0.6, "last"};  // choice = [yolov5s, yolov5m, yolov5l]
        Net_config yolo_nets = {0.3, 0.3, 0.3, "yolov5s"};  // choice = [yolov5s, yolov5m, yolov5l]
        //定义一个网络模型的对象yolo_model
        YOLO yolo_model(yolo_nets);
        detectOpen = true;
        // 读取一张图片测试其效果
        //string imgpath = "s_l.jpg";
        //Mat srcimg = imread(imgpath);
        //读取视频测试
        static const string kWinName = "Deep learning object detection in OpenCV";
        Mat frame;

        int subway_star = true; //从地铁获取停车信号，停止DSM功能转为红绿灯识别

        const int input_width = 1280;
        const int input_height = 720;
//        float ratio = (float )input_height / (float )640; //缩放比例
//        int x_offset = (input_width - input_height) * 0.5;
//        int y_offset = 0;
        uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
        Mat yuvImg = Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);
        Mat srcimg = Mat::zeros(input_height, input_width, CV_8UC3);

        alarm_info.face_point.resize(2);
        alarm_info.five_point.resize(5);
        /* 是否需要清理识别信息 */
        bool needclean = false;
        XuYUVDataOpt yuvDataOpt;
        face_message face_roi_info;
        while (subway_star) {
            int ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData,curVehicleStatus,&needclean);
            if(!needclean){
                if (ret != 0) {
                    printf("get yuv data failed!  \n");
                    usleep(5 * 1000);
                    continue;
                }
//            printf("get yuv data success, cameraId=%d size=%d  \n", curCameraId,ret);

                rga_utils.imageTransformation(input_width, input_height, rga_utils.IMG_TYPE_NV21,
                                              cameraYuvData,
                                              input_width, input_height, rga_utils.IMG_TYPE_BGR888, srcimg.data);
                int cut_x = (input_width - input_height) * 0.5;
                int cut_y = 0;
                int cut_width = input_height;
                int cut_height = input_height;
                Mat roi = srcimg(Rect(cut_x, cut_y, cut_width, cut_height));
                resize(roi, frame, Size(640, 640));
                if (frame.empty()) {
                    cout << "Done processing. " << endl;
                    break;
                }
                try {
                    //识别人脸特征
                    uint64_t beforeDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                    face_roi_info = yolo_model.detect_face(frame);
                    curDetectUseTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - beforeDetectTime;
                    /* 塞给决策算法 */
                    toAlarmDecision(face_roi_info,frame,curDetectUseTime);
                } catch (const cv::Exception &ii) {

                }

                lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
            }else{
                face_roi_info = {};
                curDetectUseTime = 40;
                toAlarmDecision(face_roi_info,frame,curDetectUseTime);
                lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
            }

        }



    }

    void VisDetecter_MT_DSM::toAlarmDecision(face_message &face_roi_info, Mat &frame,const uint16_t curDetectUseTime) {
        /* 看看需不需要塞到地铁人脸的报警决策里 */
        if(alarmDecisionList.alarmDecisionMtFace != nullptr && alarmDecisionList.alarmDecisionMtFace->isInited()){
            alarmDecisionList.alarmDecisionMtFace->parse_MT_DSM_OD(face_roi_info,frame,curVehicleStatus,curDetectUseTime);
        }

    }


} // vis