//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/3.
//

#include "VisDetecter_ADAS_160_Normal.h"
#include "dasDetect.h"
#include "XuTimeUtil.h"
#include "XuString.h"

namespace vis {
    using namespace cv;
    using namespace std;

    VisDetecter_ADAS_160_Normal::VisDetecter_ADAS_160_Normal() {
        curAlgorithmType = ALGORITHM_TYPE_ADAS_160_NORMAL;
    }

    void VisDetecter_ADAS_160_Normal::run() {
        std::string pthreadName = "ADAS_160_";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        doDetecter();
        pthread_setname_np(pthread_self(), "Finish");
    }

    void VisDetecter_ADAS_160_Normal::
    doDetecter() {
        int ret = -1;

        das::Detect *detect = new das::Detect();

        detect->setAlgMode(YOLOV5DS_ADAS_160_MODE);  //设置算法模式，很重要！！！不然容易出错

        const int input_width = 1280;
        const int input_height = 720;

        das::objectInfo_t object_info;

        object_info.objects.clear();

        Mat srcimg = Mat::zeros(input_height, input_width, CV_8UC3);

        std::vector<cv::Point> ignoreArea1;
        std::vector<cv::Point> ignoreArea2;
        ignoreArea1 = getIgnoreAreaPoint1();
        ignoreArea2 = getIgnoreAreaPoint2();
        detect->init(input_width, input_height,ignoreArea1,ignoreArea2);
        detectOpen = true;
        printf("=================================================ADAS_160_Normal init finish!  version=%s \n",detect->getModelVersion());
        /* 把算法版本号存起来 */
        switch (curCameraId) {
            case CAMERA_ID_1:{
                setenv("G3_CAM1_ALG_VERSION",detect->getModelVersion(), 1);
            }
                break;
            case CAMERA_ID_2:{
                setenv("G3_CAM2_ALG_VERSION",detect->getModelVersion(), 1);
            }
                break;
            case CAMERA_ID_3:{
                setenv("G3_CAM3_ALG_VERSION",detect->getModelVersion(), 1);
            }
                break;
            case CAMERA_ID_4:{
                setenv("G3_CAM4_ALG_VERSION",detect->getModelVersion(), 1);
            }
                break;
        }


        uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
        Mat yuvImg = Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);

        int frame_cnt = 0;
        bool isCamCoverd = false;
//        bool isFullBlack = false;
        float imgBrightness = -1;
        /* 镜头遮挡状态的持续累加计数 */
        int camcoverd_cnt = 0;
        /* 镜头全黑状态的持续累加计数 */
        int fullblack_cnt = 0;
        /* 是否需要清理识别信息 */
        bool needclean = false;
        while (1) {
            frame_cnt++;

//            double BeginTime_alg = (double) cv::getTickCount();

            ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData,curVehicleStatus,&needclean);
            if(!needclean){
                if (ret != 0) {
                    usleep(5 * 1000);
                    continue;
                }
//            printf("get yuv data success, cameraId=%d size=%d  \n", cameraYuvData.getCameraId(),cameraYuvData.getDataLen());

                xuRgaUtils.imageTransformation(1280, 720, xuRgaUtils.IMG_TYPE_NV21, cameraYuvData, 1280,
                                               720,
                                               xuRgaUtils.IMG_TYPE_BGR888, srcimg.data);
//            yuvImg.data = cameraYuvData.getCurYuvData();
//            cvtColor(yuvImg, srcimg, COLOR_YUV2BGR_NV12);





                // 设置一下报警开关
                detect->setAlarmOnOff((G3_Configuration::getInstance().getAdasPdwSwitch() == 1),
                                      (G3_Configuration::getInstance().getAdasFcwSwitch() == 1),
                                      (G3_Configuration::getInstance().getAdasLdwSwitch() == 1),
                                      (G3_Configuration::getInstance().getAdasVbSwitch() == 1),
                                      (G3_Configuration::getInstance().getAdasGoSwitch() == 1));
//            srcimg = imread("/userdata/xryimg.jpg");
                uint64_t beforeDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                detect->detect(srcimg, curVehicleStatus.speed/(float)3.6);
                curDetectUseTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - beforeDetectTime;
                object_info.objects.clear();
                object_info.traffics.clear();
                object_info.lanes.clear();

                object_info = detect->getResult();
                detect->executeAlarmStrategy(object_info, curVehicleStatus);

                if (frame_cnt % 50 == 3){
                    /* 先检测下当前帧是否遮挡或者全黑，并拿出亮度 */
                    bool curIsCamCoverd = false;
                    bool curIsFullBlack = false;
                    das::CalcCamCoverd(srcimg, imgBrightness, curIsCamCoverd, curIsFullBlack);  //rv1126上约20-30ms，暂定G3取需要的isCamCoverd及imgBrightness直接从这个函数中取，目前没有从objinfo中取
                    /* 计算下连续总的遮挡的次数 */
                    if (curIsCamCoverd){
                        camcoverd_cnt++;
                    }else{
                        camcoverd_cnt = 0;
                    }
                    /* 计算下连续总的全黑的次数 */
                    if (curIsFullBlack){
                        fullblack_cnt++;
                    }else{
                        fullblack_cnt = 0;
                    }
                    /* 如果连续总的遮挡次数达到了阈值，那么就是被遮挡了 */
                    isCamCoverd = (camcoverd_cnt >= G3_Configuration::getInstance().getBsdCamcoverCountThreshold());
//                /* 如果连续总的全黑次数达到了阈值，那么就是全黑了 */
//                isFullBlack = (fullblack_cnt > G3_Configuration::getInstance().getBsdCamcoverCountThreshold());

                }
                object_info.alarmInfo.m_camera_covered_alarm = isCamCoverd;

                lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();


                /* 这里交给报警决策判断一下 */
                toAlarmDecision(object_info,curDetectUseTime);
            }else{
                /* 需要清理掉旧的识别信息，那么就发个空的object去给报警决策 */
                object_info.objects.clear();
                object_info.lanes.clear();
                object_info.traffics.clear();
                object_info.alarmInfo = {};
                object_info.faultInfo = {};
                curDetectUseTime = 40;
                toAlarmDecision(object_info, curDetectUseTime);
                /* 记录下这次完成识别的时间 */
                lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                usleep(curDetectUseTime * 1000);
            }




        }


        delete detect;

        return;
    }

    void VisDetecter_ADAS_160_Normal::toAlarmDecision(das::objectInfo_t &object_info,const uint16_t curDetectUseTime) {
        /* 看看需不需要塞到行人(区域)的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianArea != nullptr && alarmDecisionList.alarmDecisionPedestrianArea->isInited()){
            alarmDecisionList.alarmDecisionPedestrianArea->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到车辆（区域）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleArea != nullptr && alarmDecisionList.alarmDecisionVehicleArea->isInited()){
            alarmDecisionList.alarmDecisionVehicleArea->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到行人(TTC)的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianStandard != nullptr && alarmDecisionList.alarmDecisionPedestrianStandard->isInited()){
            alarmDecisionList.alarmDecisionPedestrianStandard->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }
        /* 看看需不需要塞到车辆(TTC)的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleStandard != nullptr && alarmDecisionList.alarmDecisionVehicleStandard->isInited()){
            alarmDecisionList.alarmDecisionVehicleStandard->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到车道线(TTC)的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionLanelineAdas != nullptr && alarmDecisionList.alarmDecisionLanelineAdas->isInited()){
            alarmDecisionList.alarmDecisionLanelineAdas->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到行人（SBST后向）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianSbstBackward != nullptr && alarmDecisionList.alarmDecisionPedestrianSbstBackward->isInited()){
            alarmDecisionList.alarmDecisionPedestrianSbstBackward->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到车辆（SBST后向）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleSbstBackward != nullptr && alarmDecisionList.alarmDecisionVehicleSbstBackward->isInited()){
            alarmDecisionList.alarmDecisionVehicleSbstBackward->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到行人（R159）的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianR159 != nullptr && alarmDecisionList.alarmDecisionPedestrianR159->isInited()){
            alarmDecisionList.alarmDecisionPedestrianR159->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

    }
} // vis