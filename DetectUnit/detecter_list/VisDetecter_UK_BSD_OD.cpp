//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/7.
//

#include "VisDetecter_UK_BSD_OD.h"
#include "tdDetect.h"

#include "XuTimeUtil.h"

namespace vis {


    using namespace cv;
    using namespace std;

    int sum = 0;
    void VisDetecter_UK_BSD_OD::doDetecter() {



        int ret = -1;
        std::vector<cv::Point> ignoreArea1;
        std::vector<cv::Point> ignoreArea2;

        td::Detect *detect = new td::Detect();
        // 使用默认模式
        detect->setG4RunMode(NORMAL_MODE);  // NORMAL_MODE-0   KOM_MODE-1

        const int input_width = 1280;
        const int input_height = 720;

        td::objectInfo_t object_info;
        object_info.alarmInfo = {};
        object_info.faultInfo = {};
        object_info.objects.clear();

        Mat srcimg = Mat::zeros(input_height, input_width, CV_8UC3);
        Mat camcoverImg = Mat::zeros(input_height, input_width, CV_8UC3);
        /* 获取不识别区域 */
        ignoreArea1 = getIgnoreAreaPoint1();
        ignoreArea2 = getIgnoreAreaPoint2();
        /* 获取图像真实大小 */
        CameraRealImgSize cameraRealImgSize = getCameraRealImgSize(curCameraId);
        td::ROI_RECT roiRect;
        roiRect.x = cameraRealImgSize.startX;
        roiRect.y = cameraRealImgSize.startY;
        roiRect.width = cameraRealImgSize.width;
        roiRect.height = cameraRealImgSize.height;

        detect->init(input_width, input_height, roiRect,ignoreArea1, ignoreArea2,ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION);
        detectOpen = true;
        printf("=================================================UK_BSD init finish!  version=%s \n",detect->getModelVersion());
        /* 把算法版本号存起来 */
        switch (curCameraId) {
            case CAMERA_ID_1:{
                setenv("G3_CAM1_ALG_VERSION",detect->getModelVersion(), 1);
            }
                break;
            case CAMERA_ID_2:{
                setenv("G3_CAM2_ALG_VERSION",detect->getModelVersion(), 1);
            }
                break;
            case CAMERA_ID_3:{
                setenv("G3_CAM3_ALG_VERSION",detect->getModelVersion(), 1);
            }
                break;
            case CAMERA_ID_4:{
                setenv("G3_CAM4_ALG_VERSION",detect->getModelVersion(), 1);
            }
                break;
        }


        uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
        Mat yuvImg = Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);
        int frame_cnt = 0;

        /* 当前镜头是否被遮挡 */
        bool isCamCoverd = false;
        /* 当前画面的亮度 */
        float imgBrightness = -1;
        /* 当前的镜头是否全黑 */
        bool isFullBlack = false;

        /* 镜头遮挡和全黑通过计算次数看是否需要报警的变量 */
        int camcoverd_cnt = 0;
        int fullblack_cnt = 0;
        /* 是否需要清理识别信息 */
        bool needclean = false;

        while (1) {
            frame_cnt++;
            ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData, curVehicleStatus,&needclean);
            if(!needclean){
                if (ret != 0) {
                    usleep(5 * 1000);
                    continue;
                }


//            double BeginTime_alg = (double) cv::getTickCount();
                /* 获取最终给到算法的BRG888图像（是不是降效都在里面实现了） */
                getUltimateImg(cameraYuvData,(input_width * input_height * 3 / 2),srcimg.data);



                uint64_t beforeDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                detect->detect(srcimg);
                curDetectUseTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - beforeDetectTime;
                object_info = detect->getResult();




                if (frame_cnt % 50 == 3) {
                    /* 先检测下当前帧是否遮挡或者全黑，并拿出亮度 */
                    bool curIsCamCoverd = false;
                    bool curIsFullBlack = false;
                    /* 这里需要判断是否降效，来确定是不是可以直接使用srcimg */
                    if(needReduceDetectionEffect(curCameraId)){
                        /* 算法需要降效果，那么就使用没缩放后的图像进行镜头遮挡的判断 */
                        yuvImg.data = cameraYuvData;
                        cvtColor(yuvImg, camcoverImg, COLOR_YUV2BGR_NV12);
                        td::CalcCamCoverd(camcoverImg,imgBrightness,curIsCamCoverd,curIsFullBlack);
                    }else{
                        /* 算法不需要降效，那么就直接使用srcimg */
                        td::CalcCamCoverd(srcimg,imgBrightness,curIsCamCoverd,curIsFullBlack);
                    }
                    /* 计算下连续总的遮挡的次数 */
                    if (curIsCamCoverd){
                        camcoverd_cnt++;
                    }else{
                        camcoverd_cnt = 0;
                    }
                    /* 计算下连续总的全黑的次数 */
                    if (curIsFullBlack){
                        fullblack_cnt++;
                    }else{
                        fullblack_cnt = 0;
                    }
                    /* 如果连续总的遮挡次数达到了阈值，那么就是被遮挡了 */
                    isCamCoverd = (camcoverd_cnt >= G3_Configuration::getInstance().getBsdCamcoverCountThreshold());
                    /* 如果连续总的全黑次数达到了阈值，那么就是全黑了 */
                    isFullBlack = (fullblack_cnt > G3_Configuration::getInstance().getBsdCamcoverCountThreshold());

                }
                /* 这里交给报警决策判断一下 */
                toAlarmDecision(object_info,isCamCoverd,imgBrightness,isFullBlack,curDetectUseTime);

                /* 记录下这次完成识别的时间 */
                lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
            }else{
                /* 需要清理掉旧的识别信息，那么就发个空的object去给报警决策 */
                object_info.objects.clear();
                object_info.alarmInfo = {};
                object_info.faultInfo = {};
                curDetectUseTime = 40;
                toAlarmDecision(object_info, false,-1,false,curDetectUseTime);
                /* 记录下这次完成识别的时间 */
                lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                usleep(curDetectUseTime * 1000);
            }
        }

        delete detect;

        return;
    }


    VisDetecter_UK_BSD_OD::VisDetecter_UK_BSD_OD() {
        curAlgorithmType = ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION;
    }

    void VisDetecter_UK_BSD_OD::run() {
        std::string pthreadName = "UK_BSD_OD";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        doDetecter();
        pthread_setname_np(pthread_self(), "Finish");
    }

    void VisDetecter_UK_BSD_OD::toAlarmDecision(td::objectInfo_t &object_info,bool cameraCovered,float imgBrightness, bool isFullblack,const uint16_t curDetectUseTime) {
        td::objectInfo_t object_info_notcheck;
        /* 看看需不需要塞到行人识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianArea != nullptr && alarmDecisionList.alarmDecisionPedestrianArea->isInited()){
            alarmDecisionList.alarmDecisionPedestrianArea->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }
        /* 看看需不需要塞到车辆的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleArea != nullptr && alarmDecisionList.alarmDecisionVehicleArea->isInited()){
            alarmDecisionList.alarmDecisionVehicleArea->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }
        /* 看看需不需要塞到行人（R151）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianR151 != nullptr && alarmDecisionList.alarmDecisionPedestrianR151->isInited()){
            alarmDecisionList.alarmDecisionPedestrianR151->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }
        /* 看看需不需要塞到车辆（R151）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleR151 != nullptr && alarmDecisionList.alarmDecisionVehicleR151->isInited()){
            alarmDecisionList.alarmDecisionVehicleR151->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }
        /* 看看需不需要塞到行人（R158）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianR158 != nullptr && alarmDecisionList.alarmDecisionPedestrianR158->isInited()){
            alarmDecisionList.alarmDecisionPedestrianR158->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }
        /* 看看需不需要塞到车辆（R158）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleR158 != nullptr && alarmDecisionList.alarmDecisionVehicleR158->isInited()){
            alarmDecisionList.alarmDecisionVehicleR158->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }
        /* 看看需不需要塞到行人（R159）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianR159 != nullptr && alarmDecisionList.alarmDecisionPedestrianR159->isInited()){
            alarmDecisionList.alarmDecisionPedestrianR159->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }

        /* 看看需不需要塞到车辆（R159）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleR159 != nullptr && alarmDecisionList.alarmDecisionVehicleR159->isInited()){
            alarmDecisionList.alarmDecisionVehicleR159->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }

        /* 看看需不需要塞到行人（SBST后向）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianSbstBackward != nullptr && alarmDecisionList.alarmDecisionPedestrianSbstBackward->isInited()){
            alarmDecisionList.alarmDecisionPedestrianSbstBackward->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }

        /* 看看需不需要塞到车辆（SBST后向）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleSbstBackward != nullptr && alarmDecisionList.alarmDecisionVehicleSbstBackward->isInited()){
            alarmDecisionList.alarmDecisionVehicleSbstBackward->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }

        /* 看看需不需要塞到人（Security区域）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianAreaSecurityMode != nullptr && alarmDecisionList.alarmDecisionPedestrianAreaSecurityMode->isInited()){
            alarmDecisionList.alarmDecisionPedestrianAreaSecurityMode->parse_UK_BSD_OD(object_info,curVehicleStatus,object_info_notcheck,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }
    }


} // vis