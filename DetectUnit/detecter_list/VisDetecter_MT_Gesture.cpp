//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/17.
//

#include "VisDetecter_MT_Gesture.h"
#include "XuTimeUtil.h"


using namespace cv;
using namespace std;

namespace vis {
    VisDetecter_MT_Gesture::VisDetecter_MT_Gesture() {
        curAlgorithmType = ALGORITHM_TYPE_GESTURE_TJ_METRO;
    }

    void VisDetecter_MT_Gesture::run() {
        std::string pthreadName = "MT_Gesture";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        doDetecter();
        pthread_setname_np(pthread_self(), "Finish");
    }

    void VisDetecter_MT_Gesture::doDetecter() {
        int ret = -1;

        gt::Detect *detect = new gt::Detect();

        const int input_width = 1280;
        const int input_height = 720;

        gt::objectInfo_t object_info;
        object_info.alarmInfo = 0;
        object_info.faultInfo = 0;
        object_info.objects.clear();

        Mat srcimg = Mat::zeros(input_height, input_width, CV_8UC3);

        detect->init(input_width, input_height);
        detectOpen = true;
        uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
        Mat yuvImg = Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);
        /* 是否需要清理识别信息 */
        bool needclean = false;

        while (1) {

            ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData,curVehicleStatus,&needclean);
            if(!needclean){
                if (ret != 0) {
                    usleep(5 * 1000);
                    continue;
                }
                rga_utils.imageTransformation(input_width, input_height, rga_utils.IMG_TYPE_NV21,
                                              cameraYuvData, input_width, input_height,
                                              rga_utils.IMG_TYPE_BGR888, srcimg.data);
                uint64_t beforeDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                detect->detect(srcimg);
                curDetectUseTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - beforeDetectTime;
                object_info = detect->getResult();

                /* 塞给决策算法 */
                toAlarmDecision(object_info,curDetectUseTime);
                lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
            }else{
                /* 需要清理掉旧的识别信息，那么就发个空的object去给报警决策 */
                object_info.objects.clear();
                object_info.alarmInfo = {};
                object_info.faultInfo = {};
                curDetectUseTime = 40;
                toAlarmDecision(object_info,curDetectUseTime);
                lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
            }


        }

        delete detect;



        return;
    }

    void VisDetecter_MT_Gesture::toAlarmDecision(gt::objectInfo_ &curObjectInfo,const uint16_t curDetectUseTime) {
        /* 看看需不需要塞到地铁人脸的报警决策里 */
        if(alarmDecisionList.alarmDecisionMtGesture != nullptr && alarmDecisionList.alarmDecisionMtGesture->isInited()){
            alarmDecisionList.alarmDecisionMtGesture->parse_MT_GES_OD(curObjectInfo,curVehicleStatus,curDetectUseTime);
        }
    }



} // vis