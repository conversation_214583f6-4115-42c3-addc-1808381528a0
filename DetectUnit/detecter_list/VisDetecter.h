//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/7.
//

#ifndef VIS_G3_SOFTWARE_VISDETECTER_H
#define VIS_G3_SOFTWARE_VISDETECTER_H


#include "DetectDataCallback.h"
#include "XuRGAUtils.h"
#include "alarm_decision_list/VisAlarmDecision.h"
#include "alarm_decision_list/VisAlarmDecision_Pedestrian_Area.h"
#include "alarm_decision_list/VisAlarmDecision_Vehicle_Area.h"
#include "alarm_decision_list/VisAlarmDecision_MT_Face.h"
#include "alarm_decision_list/VisAlarmDecision_MT_Gesture.h"
#include "alarm_decision_list/VisAlarmDecision_Vehicle_Standard.h"
#include "alarm_decision_list/VisAlarmDecision_Pedestrian_Standard.h"
#include "alarm_decision_list/VisAlarmDecision_Laneline_adas.h"
#include "alarm_decision_list/VisAlarmDecision_Pedestrian_R151.h"
#include "alarm_decision_list/VisAlarmDecision_Vehicle_R151.h"
#include "alarm_decision_list/VisAlarmDecision_Pedestrian_R158.h"
#include "alarm_decision_list/VisAlarmDecision_Vehicle_R158.h"
#include "alarm_decision_list/VisAlarmDecision_Pedestrian_R159.h"
#include "alarm_decision_list/VisAlarmDecision_Vehicle_R159.h"
#include "VisAlarmDecision_Pedestrian_SBST_Backward.h"
#include "VisAlarmDecision_Vehicle_SBST_Backward.h"
#include "VisAlarmDecision_MUV_Face.h"
#include "VisAlarmDecision_TSR_ADAS.h"
#include "VisAlarmDecision_CameraStatus.h"
#include "VisAlarmDecision_Vehicle_SPG_ADAS.h"
#include "VisAlarmDecision_DD_QRCode.h"
#include "VisAlarmDecision_Pedestrian_Area_SecurityMode.h"
#include "VisAlarmDecision_STD_Face.h"

namespace vis {

    /**
     * 所有识别算法的基类
     */
    class VisDetecter{
    public:
        /* 所有报警决策的类型都在这里了 */
        struct VisAlarmDecisionList{
            VisAlarmDecision_Pedestrian_Area *alarmDecisionPedestrianArea = nullptr;
            VisAlarmDecision_Vehicle_Area *alarmDecisionVehicleArea = nullptr;
            VisAlarmDecision_MT_Face *alarmDecisionMtFace = nullptr;
            VisAlarmDecision_MT_Gesture *alarmDecisionMtGesture = nullptr;
            VisAlarmDecision_Vehicle_Standard *alarmDecisionVehicleStandard = nullptr;
            VisAlarmDecision_Pedestrian_Standard *alarmDecisionPedestrianStandard = nullptr;
            VisAlarmDecision_Laneline_adas *alarmDecisionLanelineAdas = nullptr;
            VisAlarmDecision_Pedestrian_R151 *alarmDecisionPedestrianR151 = nullptr;
            VisAlarmDecision_Vehicle_R151 *alarmDecisionVehicleR151 = nullptr;
            VisAlarmDecision_Pedestrian_R158 *alarmDecisionPedestrianR158 = nullptr;
            VisAlarmDecision_Vehicle_R158 *alarmDecisionVehicleR158 = nullptr;
            VisAlarmDecision_Pedestrian_R159 *alarmDecisionPedestrianR159 = nullptr;
            VisAlarmDecision_Vehicle_R159 *alarmDecisionVehicleR159 = nullptr;
            VisAlarmDecision_Pedestrian_SBST_Backward *alarmDecisionPedestrianSbstBackward = nullptr;
            VisAlarmDecision_Vehicle_SBST_Backward *alarmDecisionVehicleSbstBackward = nullptr;
            VisAlarmDecision_MUV_Face *alarmDecisionMuvFace = nullptr;
            VisAlarmDecision_TSR_ADAS *alarmDecisionTsrAdas = nullptr;
            VisAlarmDecision_CameraStatus *alarmDecisionCameraStatus = nullptr;
            VisAlarmDecision_Vehicle_SPG_ADAS *alarmDecisionVehicleSpgAdas = nullptr;
            VisAlarmDecision_DD_QRCode *alarmDecisionDdQrCode = nullptr;
            VisAlarmDecision_Pedestrian_Area_SecurityMode *alarmDecisionPedestrianAreaSecurityMode = nullptr;
            VisAlarmDecision_STD_Face *alarmDecisionStdFace = nullptr;
        };

        /* 图像的真实大小（用于判断那些加了黑边的图像中真实图像的位置和大小） */
        struct CameraRealImgSize{
            int startX = 0;
            int startY = 0;
            int width = 1280;
            int height = 720;
        };


        /* 当前的识别算法类型 */
        ALGORITHM_TYPE curAlgorithmType;




        /* 算法是否打开 */
        bool detectOpen = false;

        /* 上一帧图像解析成功的时间 */
        uint64_t lastDetectTime = 0;
        /* 当前的车况 */
        Vehicle_RealtimeStatus curVehicleStatus = {};

        /* 回调函数 */
        DetectDataCallback *curDetectDataCallback;
        /* 当前相机ID */
        int curCameraId = -1;


        /* 报警决策列表 */
        VisAlarmDecisionList alarmDecisionList;

        /* 上一帧图像识别的耗时时间 */
        uint16_t curDetectUseTime = 0xFFFF;



        /**
         * 初始化
         *
         * @param cameraId : 算法对应的相机ID
         * @param detectUnitManager ：DetectUnitManager的对象
         *
         * @return 结果     0：成功  其他：失败
         *
         * */
        int init(int cameraId, DetectDataCallback &detectDataCallback);

        /**
         * 检查算法是否已经启动
         *
         * @return 是否已启动
         */
        bool isDetectOpen() const;

        /**
         * 获取最后一次获得识别结果的时间
         *
         * @return  最后一次获得识别结果的时间
         */
        uint64_t getLastDetectTime() const;

        /**
         * 给算法添加对应的报警决策
         *
         * @param adType ：报警决策类型类型
         * @param classThreshold classThreshold : 配置过来的算法识别阈值中的正式阈值（主要跟影子报警相关）
         */
        void addVisAlarmDecision(CameraType_AlarmDecisionType &adType, float classThreshold);

        /**
         * 获得忽略报警区域1
         * @return 忽略区域1的所有点
         */
        std::vector<cv::Point> getIgnoreAreaPoint1();
        /**
         * 获得忽略报警区域2
         * @return 忽略区域2的所有点
         */
        std::vector<cv::Point> getIgnoreAreaPoint2();

        /**
         * 获取镜头图像的真实大小信息
         *
         * @param cameraId ： 镜头ID
         * @return 真实大小信息
         */
        CameraRealImgSize getCameraRealImgSize(const int cameraId);

        /**
         * 检查是否需要降低算法效果
         *
         * @param cameraId ： 镜头ID
         *
         * @return 是否需要降低算法效果
         */
        bool needReduceDetectionEffect(const int cameraId);

        /**
         * 获取最终给到算法的BGR888的图像（是不是降效都在里面实现了）
         *
         * @param nv21Data ： 原始的NV21格式图像数据
         * @param nv21DataLen ： 原始的NV21格式图像数据的长度
         * @param outBuf ： 存放BGR888图像数据的内存
         *
         * @return BGR888图像数据的长度  -1=失败
         */
        int getUltimateImg(uint8_t *nv21Data, const int nv21DataLen, uint8_t *outBuf);


    private:

        /* 降效时使用的黑色背景图像 */
        uint8_t *reduceEffectImg = nullptr;
        /* 降效时临时的缩放后图像 */
        uint8_t *scaleImgTempBuf = nullptr;

        /* RGA工具类 */
        XuRGAUtils rgaUtils;


        /***
         * 识别函数在这里写  由子类基础实现
         */
        virtual void doDetecter() = 0;


        /**
         * 获取降效之后的BGR888的图像
         *
         * @param imgData ： 原始的NV21格式图像数据
         * @param imgDataLen ： 原始的NV21格式图像数据的长度
         * @param outBuf ： 存放BGR888图像数据的内存
         * @return BGR888图像数据的长度  -1=失败
         */
        int reduceEffectiveImg(uint8_t *imgData, const int imgDataLen, uint8_t *outBuf);

    };

} // vis

#endif //VIS_G3_SOFTWARE_VISDETECTER_H
