//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/14.
//

#ifndef VIS_G3_SOFTWARE_VISDETECTER_MT_DSM_H
#define VIS_G3_SOFTWARE_VISDETECTER_MT_DSM_H

#include "VisDetecter.h"
#include <Poco/Runnable.h>
#include "myparams.h"
#include "putcnText.h"
#include "resnet18.h"
#include "get_alarm.h"
#include "yolov5s.h"
#include "dsmShareDefine.h"
namespace vis {
    /**
     * 地铁DSM
     */
    class VisDetecter_MT_DSM : public VisDetecter , public Poco::Runnable {
    public:
        VisDetecter_MT_DSM();


        void run() override;


        /*  */
        void doDetecter() override;

        /**
         * 把识别结果给到报警决策
         *
         * @param face_roi_info ： 识别结果
         * @param frame : 人脸图片
         * @param curDetectUseTime : 这一帧图像算法的识别耗时
         */
        void toAlarmDecision(face_message &face_roi_info,cv::Mat &frame,const uint16_t curDetectUseTime = 0xFFFF);



    private:
        /* RGA工具类 */
        XuRGAUtils rga_utils;

    };

} // vis

#endif //VIS_G3_SOFTWARE_VISDETECTER_MT_DSM_H
