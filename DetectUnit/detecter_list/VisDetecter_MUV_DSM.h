//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/20.
//

#ifndef VIS_G3_SOFTWARE_VISDETECTER_MUV_DSM_H
#define VIS_G3_SOFTWARE_VISDETECTER_MUV_DSM_H

#include "VisDetecter.h"
#include <Poco/Runnable.h>

namespace vis
{

    /**
     * 新加坡的DSM算法
     */
    class VisDetecter_MUV_DSM : public VisDetecter, public Poco::Runnable
    {
    public:
        VisDetecter_MUV_DSM();

        void run() override;

        /*  */
        void doDetecter() override;

        /**
         * 把识别结果给到报警决策
         *
         * @param almuv_info ： 识别结果
         * @param state_of : 是否有人脸（1=有人脸，0=无）
         * @param cameraCovered : 是否有镜头遮挡
         * @param imgBrightness : 画面识别出来的亮度
         * @param isFullblack ： 画面是否全黑
         * @param curDetectUseTime : 这一帧图像算法的识别耗时
         */
        void toAlarmDecision(dsm_muv::DSMInfo &almuv_info,const int state_of,bool cameraCovered,float imgBrightness = -1,bool isFullblack = false,const uint16_t curDetectUseTime = 0xFFFF);

    private:
        /* RGA工具 */
        XuRGAUtils rga_utils;

        CameraType curCameraType;

        AlarmEventInfo curAlarmEventInfo;

        /* 实时车况 */
        Vehicle_RealtimeStatus curVehicleStatus = {};

        /**
         * 镜头状态检测
         *
         * @param srcimg ： 镜头画面
         * @param imgBrightness ： 画面亮度的结果存放的地址
         * @param isCamCoverd ： 是否遮挡的结果存放的地址
         * @param isFullBlack ： 是否全黑的结果存放的地址
         */
        void calcCamCoverd(cv::Mat srcimg, float& imgBrightness, bool& isCamCoverd, bool& isFullBlack);

    };

} // vis

#endif // VIS_G3_SOFTWARE_VISDETECTER_MUV_DSM_H
