//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/7/3.
//

#ifndef VIS_G3_SOFTWARE_VISDETECTER_ADAS_160_NORMAL_H
#define VIS_G3_SOFTWARE_VISDETECTER_ADAS_160_NORMAL_H

#include "VisDetecter.h"
#include "XuRGAUtils.h"
#include <Poco/Runnable.h>
namespace vis {

    /**
     * ADAS-160度镜头
     */
    class VisDetecter_ADAS_160_Normal : public VisDetecter , public Poco::Runnable {
    public:
        VisDetecter_ADAS_160_Normal();

        void run() override;

        /**
         * 拿图像进程识别的主任务
         *
         */
        void doDetecter() override;

        /**
         * 把识别结果给到报警决策
         *
         * @param object_info ： 识别结果
         * @param curDetectUseTime : 这一帧图像算法的识别耗时
         */
        void toAlarmDecision(das::objectInfo_t &object_info,const uint16_t curDetectUseTime = 0xFFFF);

    private:
        /* RGA工具类 */
        XuRGAUtils xuRgaUtils;

        int alaramSum = 0;
        int MAXALARMSUM = 55;
        bool hwalarmStatus = true;
        bool ttcalarmStatus = false;

    };


} // vis

#endif //VIS_G3_SOFTWARE_VISDETECTER_ADAS_160_NORMAL_H
