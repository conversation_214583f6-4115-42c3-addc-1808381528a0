//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/5/30.
//

#ifndef VIS_G3_SOFTWARE_VISDETECTER_SBST_160_OD_H
#define VIS_G3_SOFTWARE_VISDETECTER_SBST_160_OD_H

#include "VisDetecter.h"
#include "XuRGAUtils.h"
#include "alarm_decision_list/VisAlarmDecision.h"
#include <Poco/Runnable.h>


namespace vis {
    /**
     * 新加坡巴士-160度相机-目标检测
     */
    class VisDetecter_SBST_160_OD : public VisDetecter , public Poco::Runnable{
    public:
        VisDetecter_SBST_160_OD();



        void run() override;


        /**
         * 算法识别
         */
        void doDetecter() override;

        /**
         * 把识别结果给到报警决策
         *
         * @param object_info ： 识别结果
         * @param cameraCovered : 是否有镜头遮挡
         * @param imgBrightness : 画面识别出来的亮度
         * @param isFullblack ： 画面是否全黑
         * @param curDetectUseTime : 这一帧图像算法的识别耗时
         */
        void toAlarmDecision(td::objectInfo_t &object_info,bool cameraCovered,float imgBrightness = -1,bool isFullblack = false,const uint16_t curDetectUseTime = 0xFFFF);
    private:






    };

} // vis

#endif //VIS_G3_SOFTWARE_VISDETECTER_SBST_160_OD_H
