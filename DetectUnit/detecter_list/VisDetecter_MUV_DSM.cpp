//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/20.
//
#include "utils/algorithmSrc/src_dsm_muv/yolov5s.h"

#include "VisDetecter_MUV_DSM.h"
#include "utils/algorithmSrc/src_dsm_muv/dsmShareDefine.h"
#include "run.h"
#include "resnet18_muv.h"
#include "utils/algorithmSrc/src_dsm_muv/get_alarm.h"
#include "utils/algorithmSrc/src_dsm_muv/dsm_ini.h"
#include "XuLog.h"
#include "XuTimeUtil.h"
#include <iostream>
// #include "XuTimeUtil.h"

dsm_muv::DSMInfo almuv_info;
dsm_muv::globals globalmuvs;

namespace vis
{
    VisDetecter_MUV_DSM::VisDetecter_MUV_DSM()
    {
        curAlgorithmType = ALGORITHM_TYPE_DSM_MUV;
    }

    void VisDetecter_MUV_DSM::doDetecter()
    {

        LoadConfigFile("conf.yaml", globalmuvs);
        dsm_muv::init_DSMInfo_parmas(almuv_info);
        dsm_muv::ini::iniReader DSMconfig;
        bool ret = DSMconfig.ReadConfig("./model/DSM.ini"); //???��????????
        if (ret == false)
        {
            printf("ReadConfig is Error,Cfg=%s", "./model/DSM.ini");
            //            return -1;
        }
        printf("**********XU in line %d  \n", __LINE__);
        std::vector<cv::Point2i> tempbelt_point;
        bool debugconfig_ret = dsm_muv::LoadDebugConfigFile("./model/muv_dsm_deconfig.yml");
        tempbelt_point = almuv_info.belt_point;
        if (debugconfig_ret == false)
        {
            printf("ReadConfig is Error,Cfg=%s", "debug_config.yml");
            // return -1;
        }
        std::cout<<"muv: ----------------------------------------almuv_info.belt_point =  " <<almuv_info.belt_point.size()<<std::endl;
        printf("**********XU in line %d  \n", __LINE__);
        // string face_model = DSMconfig.ReadString("path", "yolov5sfacePt", "");
        // string  Resnet_model = DSMconfig.ReadString("path", "Resnet18mpt", "");
        // string labels_txt_file = DSMconfig.ReadString("path", "Resnet18labelPt", "");
        //        string res_weight = DSMconfig.ReadInt("net-resnet18", "res_weight", 0);
        //        string res_height = DSMconfig.ReadInt("net-resnet18", "res_height", 0);
        // dsm_muv::Net_config yolov5_nets;  // choice = [yolov5s, yolov5m, yolov5l]
        // yolov5_nets.netname = face_model;
        cv::Mat back_frame;

        dsm_muv::AlarmA a;
        dsm_muv::AlarmB b;
        dsm_muv::AlarmC c;
        dsm_muv::AlarmD d;
        dsm_muv::AlarmE e;
        dsm_muv::AlarmBox boxctt(&a, &b, &c, &d, &e);

        // std::vector<std::vector<int>> superposition(5, std::vector<int>(0));

        dsm_muv::Net_config yolov5_nets = {0.81, 0.52, 0.6, "yolov5s"};
        dsm_muv::YOLO yolo_model(yolov5_nets);
        // ????Kernel??????
        dsm_muv::Kernel kernel;
        // ??????????????��??
        // std::string imgpath = "s_l.jpg";
        // cv::Mat srcimg = imread(imgpath);
        // ??????????
        // static const std::string kWinName = "Deep learning object detection in OpenCV";
        cv::Mat frame;
        int subway_star = true; // ?????????????????DSM??????????????

        const int input_width = 1280;
        const int input_height = 720;
        float ratio = (float)input_width / (float)640; // ???????
        // int x_offset = (input_width - input_height) * 0.5;
        // int y_offset = 0;

        uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
        cv::Mat yuvImg = cv::Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);
        cv::Mat srcimg_muv;
        almuv_info.face_point.resize(2);
        almuv_info.five_point.resize(5);
        int state_of = 0;
        XuYUVDataOpt yuvDataOpt;

        /* 当前镜头是否被遮挡 */
        bool isCamCoverd = false;
        /* 当前画面的亮度 */
        float imgBrightness = -1;
        /* 当前的镜头是否全黑 */
        bool isFullBlack = false;
        /* 镜头遮挡和全黑通过计算次数看是否需要报警的变量 */
        int camcoverd_cnt = 0;
        int fullblack_cnt = 0;
        /* 是否需要清理识别信息 */
        bool needclean = false;
        /* 帧数 */
        int frame_cnt = 0;

        while (subway_star)
        {
            int ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData, curVehicleStatus,&needclean);
            if(!needclean){
                if (ret != 0)
                {
                    printf("get yuv data failed!  \n");
                    usleep(5 * 1000);
                    continue;
                }
                frame_cnt ++;

                yuvImg.data = cameraYuvData;
                cv::cvtColor(yuvImg, srcimg_muv, cv::COLOR_YUV2BGR_NV12);
//            imwrite("MUV_input1.jpg", srcimg_muv);
                if (srcimg_muv.empty())
                {
                    std::cout << "ERROR srcimg is void " << std::endl;
                }

                // ??????????1280*1280  ???resize 640*640
                cv::Mat dst(cv::Size(1280, 1280), CV_8UC3, cv::Scalar(0, 0, 0)); // ?????????????(0, 0, 0)
                // ????????????????(0)
                cv::Mat paddedSrc; // ??????????????????????????
                // ??????????????

                int padWidth = (dst.rows - srcimg_muv.rows) / 2;
                cv::copyMakeBorder(srcimg_muv, paddedSrc, padWidth, padWidth, 0, 0, cv::BORDER_CONSTANT, cv::Scalar(0, 0, 0));
                cv::resize(paddedSrc, paddedSrc, cv::Size(640, 640), 0, 0, cv::INTER_LINEAR); // ?????????
                cv::transpose(paddedSrc, paddedSrc);                                          // ?????
                cv::flip(paddedSrc, frame, 0);
                // imwrite("MUV_input.jpg", frame);
                // Stop the program if reached end of video
                if (frame.empty())
                {
                    std::cout << "Done processing. " << std::endl;
                    break;
                }

                try
                {

                    // 初始化安全带变量 第一、第二参数参数左右转向灯 第三是安全带
                    dsm_muv::ExternalParam exterIOparam(false, false, false, dsm_muv::SafeBeltStatus::SAFE_BELT_ERROR); // 创建实例并初始化所有成员
                    exterIOparam.turnL = curVehicleStatus.turnL;
                    exterIOparam.turnR = curVehicleStatus.turnR;
                    exterIOparam.safe_belt = (curVehicleStatus.safebeltStatus == 0xFF) ? dsm_muv::SafeBeltStatus::SAFE_BELT_ERROR : (curVehicleStatus.safebeltStatus == 0x00 ? dsm_muv::SafeBeltStatus::SAFE_BELT_OFF : dsm_muv::SafeBeltStatus::SAFE_BELT_ON);
                    exterIOparam.back_gear = curVehicleStatus.reverse;
                    int muv_speed = curVehicleStatus.speed; // ?????��km/h
                    uint64_t start_ter = dsm_muv::getTimesmuv();
                    std::cout << "muv:aaaaaaaaaataurn_light=" << (curVehicleStatus.turnL || curVehicleStatus.turnR)  << std::endl;
                    uint64_t beforeDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                    state_of = kernel.run_detector(frame, yolo_model, almuv_info, boxctt, muv_speed, exterIOparam);
                    curDetectUseTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - beforeDetectTime;
                    // printf("BBB****************(%d,%d),(%d,%d),(%d,%d),(%d,%d),(%d,%d)*************************  \n",almuv_info.belt_point[0].x,almuv_info.belt_point[0].y,almuv_info.belt_point[1].x,almuv_info.belt_point[1].y,almuv_info.belt_point[2].x,almuv_info.belt_point[2].y,almuv_info.belt_point[3].x,almuv_info.belt_point[3].y,almuv_info.belt_point[4].x,almuv_info.belt_point[4].y);



                    uint64_t end_ter = dsm_muv::getTimesmuv();
                    std::cout << "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ Run_detector-Time :  " << int(end_ter-start_ter) << std::endl;
                    if (state_of == 1 || state_of == 0)
                    {
                        std::cout << "_________END_END_坐标转换______"  << std::endl;
                        almuv_info.face_point[0] = cv::Point2i((640 - almuv_info.face_point[0].y) * ratio,
                                                               almuv_info.face_point[0].x * ratio - 280);
                        almuv_info.face_point[1] = cv::Point2i((640 - almuv_info.face_point[1].y) * ratio,
                                                               almuv_info.face_point[1].x * ratio - 280);
                        //人脸坐标转换
                        almuv_info.five_point[0] = cv::Point2i((640 - almuv_info.five_point[0].y) * ratio,
                                                               almuv_info.five_point[0].x * ratio - 280);
                        almuv_info.five_point[1] = cv::Point2i((640 - almuv_info.five_point[1].y) * ratio,
                                                               almuv_info.five_point[1].x * ratio - 280);
                        almuv_info.five_point[2] = cv::Point2i((640 - almuv_info.five_point[2].y) * ratio,
                                                               almuv_info.five_point[2].x * ratio - 280);
                        almuv_info.five_point[3] = cv::Point2i((640 - almuv_info.five_point[3].y) * ratio,
                                                               almuv_info.five_point[3].x * ratio - 280);
                        almuv_info.five_point[4] = cv::Point2i((640 - almuv_info.five_point[4].y) * ratio,
                                                               almuv_info.five_point[4].x * ratio - 280);
                        // 安全带坐标转换
                        // almuv_info.belt_point[0] =  cv::Point2i();


                    }
                    else
                    {
                        almuv_info.face_point.clear();
                        almuv_info.five_point.clear();
                        almuv_info.face_angle = 0;
                        std::cout << "???????????????????????????" << std::endl;
                        almuv_info.eye_alarm = false;
                        almuv_info.smoking_alarm = false;
                        almuv_info.seatbelt_alarm = false;
                        almuv_info.Blindspot_alarm = false;
                        almuv_info.lookaround_alarm = false;
                    }

                    std::cout << "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^Input muv_speed::  " << muv_speed << std::endl;
                    std::cout<<"muv:----------------------------------------output [1]box: almuv_info.face_point =  " <<almuv_info.face_point.size()<<std::endl;
                    std::cout<<"muv:----------------------------------------output [2]pos: almuv_info.five_point =  " <<almuv_info.five_point.size()<<std::endl;
                    std::cout<<"muv:----------------------------------------output [3]belt: almuv_info.belt_point =  " <<almuv_info.belt_point.size()<<std::endl;
                    std::cout<<"muv:----------------------------------------output [4]Angle: almuv_info.face_angle =  " <<almuv_info.face_angle<<std::endl;
                    // ???????  ?��????
                    // ??????????

                }
                catch (const cv::Exception &ii)
                {
                }

                // ??????????????????????????????
                // std::cout<<"muv:----------------------------------------almuv_info.face_angle =  " <<almuv_info.face_angle<<std::endl;
                // std::cout<<"muv:----------------------------------------output [1]belt: almuv_info.belt_point =  " <<almuv_info.belt_point[0]<<std::endl;
                // std::cout<<"muv:----------------------------------------output [2]belt: almuv_info.belt_point =  " <<almuv_info.belt_point[1]<<std::endl;
                // std::cout<<"muv:----------------------------------------output [3]belt: almuv_info.belt_point =  " <<almuv_info.belt_point[2]<<std::endl;
                // std::cout<<"muv:----------------------------------------output [4]belt: almuv_info.belt_point =  " <<almuv_info.belt_point[3]<<std::endl;
                // std::cout<<"muv:----------------------------------------output [5]belt: almuv_info.belt_point =  " <<almuv_info.belt_point[4]<<std::endl;

                if (frame_cnt % 50 == 3) {
                    /* 先检测下当前帧是否遮挡或者全黑，并拿出亮度 */
                    bool curIsCamCoverd = false;
                    bool curIsFullBlack = false;
                    /* 检测下当前镜头的状态 */
                    calcCamCoverd(srcimg_muv,imgBrightness,curIsCamCoverd,curIsFullBlack);
                    /* 计算下连续总的遮挡的次数 */
                    if (curIsCamCoverd){
                        camcoverd_cnt++;
                    }else{
                        camcoverd_cnt = 0;
                    }
                    /* 计算下连续总的全黑的次数 */
                    if (curIsFullBlack){
                        fullblack_cnt++;
                    }else{
                        fullblack_cnt = 0;
                    }
                    /* 如果连续总的遮挡次数达到了阈值，那么就是被遮挡了  （目前暂时使用BSD） */
                    isCamCoverd = (camcoverd_cnt >= G3_Configuration::getInstance().getBsdCamcoverCountThreshold());
                    /* 如果连续总的全黑次数达到了阈值，那么就是全黑了  （目前暂时使用BSD） */
                    isFullBlack = (fullblack_cnt > G3_Configuration::getInstance().getBsdCamcoverCountThreshold());

                }

                toAlarmDecision(almuv_info,state_of,isCamCoverd,imgBrightness,isFullBlack,curDetectUseTime);
                printf("**********XUXU in line %d  \n", __LINE__);
                if (almuv_info.eye_alarm == true)
                {
                    std::cout << "muv:- - - - - - - - - - - - - - - - - - e sleep????. " << std::endl;
                    almuv_info.eye_alarm = false;
                }
                if (almuv_info.smoking_alarm == true)
                {
                    std::cout << "muv:- - - - - - - - - - - - - - - - - - e smoke????. " << std::endl;
                    almuv_info.smoking_alarm = false;
                }
                if (almuv_info.seatbelt_alarm == true)
                {
                    std::cout << "muv:- - - - - - - - - - - - - - - - - - e belt????. " << std::endl;
                    almuv_info.seatbelt_alarm = false;
                }
                if (almuv_info.Blindspot_alarm == true)
                {
                    std::cout << "muv:- - - - - - - - - - - - - - - - - - e blindspot????. " << std::endl;
                    almuv_info.Blindspot_alarm = false;
                }
                if (almuv_info.lookaround_alarm == true)
                {
                    std::cout << "muv:- - - - - - - - - - - - - - - - - - e ?????. " << std::endl;
                    almuv_info.lookaround_alarm = false;
                }
                printf("**********XUXU in line %d  \n", __LINE__);
            }else{
                almuv_info = {};
                curDetectUseTime = 40;
                toAlarmDecision(almuv_info,0, false,-1,false,curDetectUseTime);
            }



        }
    }

    void VisDetecter_MUV_DSM::run()
    {
        std::string pthreadName = "MUV_DSM";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        doDetecter();
        pthread_setname_np(pthread_self(), "Finish");
    }

    void VisDetecter_MUV_DSM::toAlarmDecision(dsm_muv::DSMInfo &almuv_info, const int state_of, bool cameraCovered,
                                              float imgBrightness, bool isFullblack,const uint16_t curDetectUseTime) {
        /* ?????�M????????????MUV??????????????? */
        if (alarmDecisionList.alarmDecisionMuvFace != nullptr && alarmDecisionList.alarmDecisionMuvFace->isInited())
        {
            alarmDecisionList.alarmDecisionMuvFace->parse_MUV_DSM_OD(almuv_info, state_of, curVehicleStatus,cameraCovered,imgBrightness,isFullblack,curDetectUseTime);
        }
    }

    void
    VisDetecter_MUV_DSM::calcCamCoverd(cv::Mat srcimg, float &imgBrightness, bool &isCamCoverd, bool &isFullBlack) {
//G3给过来算法的图片是原始图片，叠加前，没有时间水印的！！！

        //图像亮度imgBrightness是G4-GJ项目用到，用于根据环境亮度来控制灯的开关
        //isCamCoverd用于判断相机是否有被遮挡
        //isFullBlack用于判断图像是否全黑（用于相机被拔掉或相机通路出问题时的故障上报）


        //cvNamedWindow("pResizeGrayImg");
        //cvShowImage("pResizeGrayImg", mPpImgs->pResizeGrayImg);
        //cvWaitKey(0);

        //srcimg = imread("C://Users//lyc_s//Desktop//coverImg.bmp");

        float scale = 0.278;
        cv::Mat img_resize_rgb, img_resize_gray, img_canny;
        cv::resize(srcimg, img_resize_rgb, cv::Size(srcimg.cols * scale, srcimg.rows * scale), cv::INTER_NEAREST);

        //imshow("img_resize_rgb", img_resize_rgb);

        float del_percent = 0.05;
        img_resize_rgb = img_resize_rgb(cv::Rect(0, img_resize_rgb.rows * del_percent, img_resize_rgb.cols, img_resize_rgb.rows * (1 - del_percent) - 1));

        cv::cvtColor(img_resize_rgb, img_resize_gray, cv::COLOR_BGR2GRAY);


        //imshow("img_resize_gray", img_resize_gray);
        //waitKey(0);

        blur(img_resize_gray, img_resize_gray, cv::Size(5, 5));
        Canny(img_resize_gray, img_canny, 10, 20, 3);  // Canny(img_resize_gray, img_canny, 30, 60, 3);

        //imshow("canny", img_canny);
        //waitKey(0);

        //Mat element = getStructuringElement(MORPH_RECT, Size(3, 3));
        //erode(img_canny, img_canny, element);

        int white_cnt = 0;

        int total_cnt = img_canny.rows * img_canny.cols * img_canny.channels();

        for (int i = 0; i < total_cnt; i++)
            if (img_canny.data[i] > 200)
                white_cnt++;

        float ratio = (float)white_cnt / total_cnt;


        int sum_gray = 0;
        for (int i = 0; i < total_cnt; i++)
            sum_gray += img_resize_gray.data[i];
        float avg_gray = (float)sum_gray / total_cnt;


        //printf("ratio = %f\n", ratio);
        //imshow("canny", img_canny);
        //waitKey(0);

        //return ratio < 0.003 ? true : false;

        //get result
        imgBrightness = avg_gray;
        isCamCoverd = ((ratio < 0.005) || (avg_gray < 22)) ? true : false;
        isFullBlack = (white_cnt < 5 && avg_gray < 30) ? true : false;
    }


} // vis