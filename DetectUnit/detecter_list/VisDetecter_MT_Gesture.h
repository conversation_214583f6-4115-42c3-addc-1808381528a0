//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/17.
//

#ifndef VIS_G3_SOFTWARE_VISDETECTER_MT_GESTURE_H
#define VIS_G3_SOFTWARE_VISDETECTER_MT_GESTURE_H
#include "VisDetecter.h"
#include <Poco/Runnable.h>
#include "gtDetect.h"
#include "XuRGAUtils.h"

namespace vis {
    /**
      * 地铁手势
      */
    class VisDetecter_MT_Gesture : public VisDetecter , public Poco::Runnable  {
    public:

        VisDetecter_MT_Gesture();


        void run() override;


        /*  */
        void doDetecter() override;

        /**
         * 把识别结果给到报警决策
         *
         * @param curObjectInfo ： 识别结果
         * @param curDetectUseTime : 这一帧图像算法的识别耗时
         */
        void toAlarmDecision(gt::objectInfo_ &curObjectInfo,const uint16_t curDetectUseTime = 0xFFFF);

    private:
        XuRGAUtils rga_utils;



    };

} // vis

#endif //VIS_G3_SOFTWARE_VISDETECTER_MT_GESTURE_H
