//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/4/12.
//

#ifndef VIS_G3_SOFTWARE_VISDETECTER_SBST_160_SS_H
#define VIS_G3_SOFTWARE_VISDETECTER_SBST_160_SS_H

#include "VisDetecter.h"
#include "XuRGAUtils.h"
#include <Poco/Runnable.h>
namespace vis {
    /**
     * 新加坡巴士-160度相机-语义分割
     */
    class VisDetecter_SBST_160_SS : public VisDetecter , public Poco::Runnable {
    public:
        VisDetecter_SBST_160_SS();

        void run() override;

        /**
         * 拿图像进程识别的主任务
         *
         */
        void doDetecter() override;

        /**
         * 把识别结果给到报警决策
         *
         * @param object_info ： 识别结果
         * @param curDetectUseTime : 这一帧图像算法的识别耗时
         */
        void toAlarmDecision(das::objectInfo_t &object_info,const uint16_t curDetectUseTime = 0xFFFF);

    private:
        /* RGA工具类 */
        XuRGAUtils xuRgaUtils;

    };

} // vis

#endif //VIS_G3_SOFTWARE_VISDETECTER_SBST_160_SS_H
