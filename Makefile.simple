# Makefile for simple image conversion example
# 用于编译简化版图像转换示例程序

CXX = g++
CXXFLAGS = -std=c++11 -Wall -O2
INCLUDES = -I. -Iutils
LIBS = -lpthread

# 目标文件
TARGET = simple_image_conversion
SOURCE = simple_image_conversion.cpp

# 依赖的对象文件
OBJS = utils/XuFile.o

# 默认目标
all: $(TARGET)

# 编译主程序
$(TARGET): $(SOURCE) $(OBJS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^ $(LIBS)

# 编译XuFile.o
utils/XuFile.o: utils/XuFile.cpp utils/XuFile.h
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	rm -f $(TARGET) $(OBJS)

# 运行程序
run: $(TARGET)
	./$(TARGET)

.PHONY: all clean run
