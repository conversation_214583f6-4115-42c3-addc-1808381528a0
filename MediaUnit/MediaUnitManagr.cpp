//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/21.
//


#include <iostream>
#include "MediaUnitManagr.h"
#include "G3_Configuration.h"
#include "XuLog.h"

#define IMAG_WIDTH 1280
#define IMAG_HEIGHT 720

void MediaUnitManagr::start() {





    /* 根据工作模式确定镜头列表长度并启动镜头 */
    switch (G3_Configuration::getInstance().getG3DeviceWorkingMode()) {
        case G3WORKMODE_V6:{
            /* 是V6模式，那么就只启动1个镜头 */
            startUpCamera(1);
        }
            break;
        case G3WORKMODE_G4MINI:{
            /* 是G4-MIMI式，那么就只启动4个镜头 */
            startUpCamera(4);
        }
            break;
        default:{
            /* 其他模式都启动2个镜头 */
            startUpCamera(2);
        }
            break;
    }

    /* 如果需要加密的话，需要启动多媒体文件加密的线程 */
    if(G3_Configuration::getInstance().getMultimediaFilesEncryptEnable() != 0){
        multimediaFilesEncrypt.start(*this);
    }

}

MediaUnitManagr::MediaUnitManagr() : threadPool(6, 14) {

}

void MediaUnitManagr::setInterface(MultimediaDataCallback &mainUnitInterface) {
    curMainUnitInterface = &mainUnitInterface;
}

int MediaUnitManagr::copyH264FrameList(CameraH264Data *src, CameraH264Data *des, int copyLen) {
    int ret = -1;
    for (int i = 0; i < copyLen; i++) {
        des[i].setCurH264Data(src[i].getDataLen(), src[i].getCurH264Data());
        des[i].setTimestamp(src[i].getTimestamp());
        des[i].setCameraId(src[i].getCameraId());
        des[i].setImagWidth(src[i].getImagWidth());
        des[i].setImagHeight(src[i].getImagHeight());
        des[i].setVenChn(src[i].getVenChn());
        des[i].setFrameRate(src[i].getFrameRate());
        des[i].setBitRate(src[i].getBitRate());
    }
    ret = 0;
    return ret;
}


int MediaUnitManagr::onCameraYUVGet(CameraYUVData &cameraData) {
    /* 这里遍历出所有镜头列表找出对应的镜头 */
    for(std::size_t i = 0; i < cameraRunnableList.size(); i ++){
        if(cameraData.getCameraId() == cameraRunnableList[i].cameraId){
            cameraRunnableList[i].h264EncoderRunnable->setCurCameraYuvData(cameraData);
            if(cameraRunnableList[i].jpegEncoder != nullptr){
                cameraRunnableList[i].jpegEncoder->setYUVdata(cameraData);
            }

            break;
        }
    }
    return 0;
}


int MediaUnitManagr::onCameraH264Get(const CameraH264Data &cameraH264Data) {
//    printf("Media onCameraH264Get  cameraId = %d   size = %d  Timestamp=%lld \n",cameraH264Data.getCameraId(),cameraH264Data.getDataLen(),cameraH264Data.getTimestamp());
    /* 先塞给Main  拿去给可能用的地方用 */
    curMainUnitInterface->onCameraH264Get(cameraH264Data);
    /* 这里遍历出所有镜头列表找出对应的镜头 */
    for(std::size_t i = 0; i < cameraRunnableList.size(); i ++){
        if(cameraH264Data.getCameraId() == cameraRunnableList[i].cameraId){
            /* 看看是不是开启录制 */
            if(cameraRunnableList[i].mp4RecordRunnable != nullptr){
                /* 先判断下时间是否小于2022-04-22 11:58 或者 没有插TF  同时工作模式不是V6  */
                if ((static_cast<uint64_t>(XuTimeUtil::getInstance().currentTimeSecond()) <= MIN_TIME_STAMP) || !G3_Configuration::getInstance().isHasTfCard()) {
                    /* 时间和模式都不对，不允许录像 */
                    cameraRunnableList[i].mp4RecordRunnable->setCanRecord(false);
                }else{
                    /* 时间或者模式对了，可以开始录像 */
                    cameraRunnableList[i].mp4RecordRunnable->setCanRecord(true);
                    /* 看看是不是刚开始录制 */
                    if (cameraRunnableList[i].curDVRStartTime == 0) {
                        /* 刚开始的话要先塞入一个开始包 */
                        CameraH264Data createMp4File;
                        createMp4File.setCameraId(cameraH264Data.getCameraId());
                        createMp4File.setImagWidth(cameraH264Data.getImagWidth());
                        createMp4File.setImagHeight(cameraH264Data.getImagHeight());
                        createMp4File.setTimestamp(cameraH264Data.getTimestamp());
                        createMp4File.setDataLen(0);
                        createMp4File.setFramePurpose(CameraH264Data::MUXER_STATE_CREATE);
                        cameraRunnableList[i].curDVRStartTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                        cameraRunnableList[i].mp4RecordRunnable->setCurCamerH264Data(createMp4File);
                    } else {
                        /* 不是刚开始录，那么看看是不是已经录够一个文件的时间了 */
                        uint64_t recodrdTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - cameraRunnableList[i].curDVRStartTime;
                        if (recodrdTime >= DVR_FILE_RECORD_TIME) {
                            /* 已经录够时间了，那么就塞一个结束包和一个开始包 */
                            CameraH264Data stopMp4File;
                            stopMp4File.setCameraId(cameraH264Data.getCameraId());
                            stopMp4File.setImagWidth(cameraH264Data.getImagWidth());
                            stopMp4File.setImagHeight(cameraH264Data.getImagHeight());
                            stopMp4File.setTimestamp(cameraH264Data.getTimestamp());
                            stopMp4File.setDataLen(0);
                            stopMp4File.setFramePurpose(CameraH264Data::MUXER_STATE_STOP);
                            cameraRunnableList[i].mp4RecordRunnable->setCurCamerH264Data(stopMp4File);
                            CameraH264Data createMp4File;
                            createMp4File.setCameraId(cameraH264Data.getCameraId());
                            createMp4File.setImagWidth(cameraH264Data.getImagWidth());
                            createMp4File.setImagHeight(cameraH264Data.getImagHeight());
                            createMp4File.setTimestamp(cameraH264Data.getTimestamp());
                            createMp4File.setDataLen(0);
                            createMp4File.setFramePurpose(CameraH264Data::MUXER_STATE_CREATE);
                            cameraRunnableList[i].curDVRStartTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                            cameraRunnableList[i].mp4RecordRunnable->setCurCamerH264Data(createMp4File);
                        }
                    }
                    /* 填入H264数据 */
                    cameraRunnableList[i].mp4RecordRunnable->setCurCamerH264Data(cameraH264Data);
                }
            }
            /* 找到了就直接停止遍历 */
            break;
        }
    }
    return 0;
}

int MediaUnitManagr::onMp4FileGet(const Mp4FileInfo &mp4FileInfo) {
//    printf("Media onMp4FileGet  path = %s \n", mp4FileInfo.getFilePath());
    curMainUnitInterface->onMp4FileGet(mp4FileInfo);
    return 0;
}

int MediaUnitManagr::getWorkStaus() {
    bool ret = true;
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        bool cameraWorkStatus = true;
        /* 先看看镜头是否打开 */
        cameraWorkStatus &= cameraRunnableList[i].getCameraImagRunnable->isCameraopened();
        /* 再看看镜头工作状态（最后一次获取到图像的时间不会太久） */
        uint64_t curTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
        int64_t intervalTime = curTime - cameraRunnableList[i].getCameraImagRunnable->getLastGetYuvDataTime();
        cameraWorkStatus &= intervalTime < 1000;
        /* 镜头工作状态有问题就打印一下 */
        if(!cameraWorkStatus){
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,"MediaUnitManagr") << "camera working error!  cameraId="<<  cameraRunnableList[i].cameraId << "    curTime="  << curTime << "  gettime=" << cameraRunnableList[i].getCameraImagRunnable->getLastGetYuvDataTime() << XU_LOG_END;
        }
        /* 把这个镜头状态存到全局变量 */
        switch (cameraRunnableList[i].cameraId) {
            case CAMERA_ID_1:{
                G3_Configuration::getInstance().setCamera1Opened(cameraWorkStatus);
                /* 检查一下是不是G4-4+1模式 */
                if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G45_UNIT){
                    /* 是G4-4+1模式,那么就看下是不是有这个镜头的C3规则对应的另一个镜头ID */
                    CAMERAIDLIST c3CameraId = getOtherCameraFromC3Rule(CAMERA_ID_1);
                    /* 根据镜头ID设置给另一个状态 */
                    switch (c3CameraId) {
                        case CAMERA_ID_1:{
                            G3_Configuration::getInstance().setCamera1Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_2:{
                            G3_Configuration::getInstance().setCamera2Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_3:{
                            G3_Configuration::getInstance().setCamera3Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_4:{
                            G3_Configuration::getInstance().setCamera4Opened(cameraWorkStatus);
                        }
                            break;
                    }

                }
            }
                break;
            case CAMERA_ID_2:{
                G3_Configuration::getInstance().setCamera2Opened(cameraWorkStatus);
                /* 检查一下是不是G4-4+1模式 */
                if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G45_UNIT){
                    /* 是G4-4+1模式,那么就看下是不是有这个镜头的C3规则对应的另一个镜头ID */
                    CAMERAIDLIST c3CameraId = getOtherCameraFromC3Rule(CAMERA_ID_1);
                    /* 根据镜头ID设置给另一个状态 */
                    switch (c3CameraId) {
                        case CAMERA_ID_1:{
                            G3_Configuration::getInstance().setCamera1Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_2:{
                            G3_Configuration::getInstance().setCamera2Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_3:{
                            G3_Configuration::getInstance().setCamera3Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_4:{
                            G3_Configuration::getInstance().setCamera4Opened(cameraWorkStatus);
                        }
                            break;
                    }

                }
            }
                break;
            case CAMERA_ID_3:{
                G3_Configuration::getInstance().setCamera3Opened(cameraWorkStatus);
                /* 检查一下是不是G4-4+1模式 */
                if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G45_UNIT){
                    /* 是G4-4+1模式,那么就看下是不是有这个镜头的C3规则对应的另一个镜头ID */
                    CAMERAIDLIST c3CameraId = getOtherCameraFromC3Rule(CAMERA_ID_1);
                    /* 根据镜头ID设置给另一个状态 */
                    switch (c3CameraId) {
                        case CAMERA_ID_1:{
                            G3_Configuration::getInstance().setCamera1Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_2:{
                            G3_Configuration::getInstance().setCamera2Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_3:{
                            G3_Configuration::getInstance().setCamera3Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_4:{
                            G3_Configuration::getInstance().setCamera4Opened(cameraWorkStatus);
                        }
                            break;
                    }

                }
            }
                break;
            case CAMERA_ID_4:{
                G3_Configuration::getInstance().setCamera4Opened(cameraWorkStatus);
                /* 检查一下是不是G4-4+1模式 */
                if(G3_Configuration::getInstance().getG3DeviceWorkingMode() == G3WORKMODE_G45_UNIT){
                    /* 是G4-4+1模式,那么就看下是不是有这个镜头的C3规则对应的另一个镜头ID */
                    CAMERAIDLIST c3CameraId = getOtherCameraFromC3Rule(CAMERA_ID_1);
                    /* 根据镜头ID设置给另一个状态 */
                    switch (c3CameraId) {
                        case CAMERA_ID_1:{
                            G3_Configuration::getInstance().setCamera1Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_2:{
                            G3_Configuration::getInstance().setCamera2Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_3:{
                            G3_Configuration::getInstance().setCamera3Opened(cameraWorkStatus);
                        }
                            break;
                        case CAMERA_ID_4:{
                            G3_Configuration::getInstance().setCamera4Opened(cameraWorkStatus);
                        }
                            break;
                    }

                }
            }
                break;

            default:{
                ; //not to do
            }
                break;
        }
        /* 添加到最终结果 */
        ret &= cameraWorkStatus;
        /* 看下编码是否正常 */
        bool enWorkStatus = true;
        enWorkStatus &= cameraRunnableList[i].h264EncoderRunnable->isEncoderOpened();
        intervalTime = curTime - cameraRunnableList[i].h264EncoderRunnable->getLastGetH264DataTime();
        enWorkStatus &= intervalTime < 1000;
        /* 编码状态有问题就打印一下 */
        if(!enWorkStatus){
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,"MediaUnitManagr") << "en working error! cameraId=" << cameraRunnableList[i].cameraId <<"  curTime=" << curTime << "  gettime=" << cameraRunnableList[i].h264EncoderRunnable->getLastGetH264DataTime() << "   intervalTime=" << intervalTime << XU_LOG_END;
        }
        /* 添加到最终结果 */
        ret &= enWorkStatus;
    }
    return (ret ? 0 : -1);
}



int MediaUnitManagr::onShowYUVGet(CameraYUVData &cameraData) {
    return curMainUnitInterface->onShowYUVGet(cameraData);
}

int MediaUnitManagr::onDetectionYUVGet(CameraYUVData &cameraData) {
    /* 塞给Main  拿去给算法用 */
    return curMainUnitInterface->onCameraYUVGet(cameraData);
}

void MediaUnitManagr::setSpeed(const float speed, const int baseOf) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setSpeed(speed, baseOf);
    }
}

void MediaUnitManagr::setCPUSerial(const char *cpuSerialStr, const int len) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setCPUSerial(cpuSerialStr, len);
    }
}

void MediaUnitManagr::setVoltageStr(float voltage) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setVoltageStr(voltage);
    }
}

void MediaUnitManagr::setStatusUart(int statusUart) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setStatusUart(statusUart);
    }
}

void MediaUnitManagr::setStatusTf(int statusTf) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setStatusTf(statusTf);
    }
}

void MediaUnitManagr::setStatusSensor(int statusSensor) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setStatusSensor(statusSensor);
    }
}

void MediaUnitManagr::setTcpClient(int tcpClient) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setTcpClient(tcpClient);
    }
}

void MediaUnitManagr::setUdpBroadcast(int udpBroadcast) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setUdpBroadcast(udpBroadcast);
    }
}


void MediaUnitManagr::setMcuversion(char *mcuversion, int len) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setMcuversion(mcuversion, len);
    }
}

void MediaUnitManagr::setIoValue110(int ioValue110) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setIoValue110(ioValue110);
    }
}

void MediaUnitManagr::setIoValue111(int ioValue111) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setIoValue111(ioValue111);
    }
}

void MediaUnitManagr::setIoValue113(int ioValue113) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setIoValue113(ioValue113);
    }
}

void MediaUnitManagr::setIoValue115(int ioValue115) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setIoValue115(ioValue115);
    }
}

void MediaUnitManagr::setTemperature_CPU(float temperature) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setTemperature_CPU(temperature);
    }
}

void MediaUnitManagr::setG3SoftwardVersion(const char *version, const int len) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setG3SoftwardVersion(version, len);
    }
}

void MediaUnitManagr::setStatusRs485(int statusRs485) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setStatusRs485(statusRs485);
    }
}

void MediaUnitManagr::setIsptm(bool isptm) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setIsptm(isptm);
    }
}

void MediaUnitManagr::setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        /* 找到对应的镜头 */
        if(detectionResult.curCameraType.cameraId == cameraRunnableList[i].cameraId){
            /* 把识别信息放进去 */
            cameraRunnableList[i].getCameraImagRunnable->setDetectionInfo_BSD(objectInfo, detectionResult);
            if(cameraRunnableList[i].jpegEncoder != nullptr){
                cameraRunnableList[i].jpegEncoder->setDetectionInfo_BSD(objectInfo, detectionResult);
            }
            /* 找到就退出 */
            break;
        }
    }
}

void MediaUnitManagr::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        /* 找到对应的镜头 */
        if(curCameraType.cameraId == cameraRunnableList[i].cameraId){
            /* 看看是不是开启了录制 */
            if(cameraRunnableList[i].mp4RecordRunnable != nullptr){
                /* 开启了录制就可以录报警视频了 */
                cameraRunnableList[i].mp4RecordRunnable->muxerWarMP4(curCameraType.cameraId, alarmEventInfo.alarmTime,ALARM_VIDEO_BEFORETIME, ALARM_VIDEO_AFTERTIME,eventCode);
            }
            /* 看看是不是开启JPEG录制 */
            if(cameraRunnableList[i].jpegEncoder != nullptr){
                /* 开启了录制就可以录报警视频了 */
                cameraRunnableList[i].jpegEncoder->recodeJpeg(alarmEventInfo.alarmTime,1,eventCode);
            }

            /* 找到就退出 */
            break;
        }
    }
}

void MediaUnitManagr::setDSMInfo(G3DetectionResult &detectionResult) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        /* 找到对应的镜头 */
        if(detectionResult.curCameraType.cameraId == cameraRunnableList[i].cameraId){
            /* 把识别信息放进去 */
            cameraRunnableList[i].getCameraImagRunnable->setDSMInfo(detectionResult);
            /* 找到就退出 */
            break;
        }
    }
}

void MediaUnitManagr::setDetectionInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        /* 找到对应的镜头 */
        if(detectionResult.curCameraType.cameraId == cameraRunnableList[i].cameraId){
            /* 把识别信息放进去 */
            cameraRunnableList[i].getCameraImagRunnable->setDetectionInfo_GES(objectInfo, detectionResult);
            /* 找到就退出 */
            break;
        }
    }
}

void MediaUnitManagr::setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        /* 找到对应的镜头 */
        if(detectionResult.curCameraType.cameraId == cameraRunnableList[i].cameraId){
            /* 把识别信息放进去 */
            cameraRunnableList[i].getCameraImagRunnable->setDetectionInfo_Adas(objectInfo, detectionResult);
            if(cameraRunnableList[i].jpegEncoder != nullptr){
                cameraRunnableList[i].jpegEncoder->setDetectionInfo_Adas(objectInfo, detectionResult);
            }
            /* 找到就退出 */
            break;
        }
    }
}

void MediaUnitManagr::setCANWorkStatus(bool isWork) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setCANWorkStatus(isWork);
    }
}

void MediaUnitManagr::setDetectInfo_CameraStatus(G3DetectionResult &detectionResult) {

}

bool MediaUnitManagr::startUpCamera(const int listSize) {
    bool ret = false;
     /* 看看长度是否超过4 */
     if(listSize <= 4 ){
         /* 获取下H264的码流 */
         int curH264BitRate = 1024 * 1024 * 4;
         switch (G3_Configuration::getInstance().getH264BitRateLevel()) {
             case H264_BIT_RATE_LEVEL_LEVEL1: {
                 curH264BitRate = 1024 * 1024 * 1;
             }
                 break;
             case H264_BIT_RATE_LEVEL_LEVEL2: {
                 curH264BitRate = 1024 * 1024 * 2;
             }
                 break;
             case H264_BIT_RATE_LEVEL_LEVEL3: {
                 curH264BitRate = 1024 * 1024 * 3;
             }
                 break;
             case H264_BIT_RATE_LEVEL_LEVEL4: {
                 curH264BitRate = 1024 * 1024 * 4;
             }
                 break;
         }

         /* 一个个镜头操作 */
         for(int i = 0; i < listSize; i ++){
             CameraFuncSet temp;
             int h264EndecodecChannelNum = XuEncoder::H264_ENDECODEC_CHANNEL_1;
             int jpegEndecodecChannelNum = JPEGEncoder::JPEG_ENDECODEC_CHANNEL_1;
             /* 判断镜头ID和H264编码所使用的通道号 */
             switch (i) {
                 case 0:{
                     temp.cameraId = CAMERA_ID_1;
                     h264EndecodecChannelNum = XuEncoder::H264_ENDECODEC_CHANNEL_1;
                     jpegEndecodecChannelNum = JPEGEncoder::JPEG_ENDECODEC_CHANNEL_1;
                 }
                     break;
                 case 1:{
                     temp.cameraId = CAMERA_ID_2;
                     h264EndecodecChannelNum = XuEncoder::H264_ENDECODEC_CHANNEL_2;
                     jpegEndecodecChannelNum = JPEGEncoder::JPEG_ENDECODEC_CHANNEL_2;
                 }
                     break;
                 case 2:{
                     temp.cameraId = CAMERA_ID_3;
                     h264EndecodecChannelNum = XuEncoder::H264_ENDECODEC_CHANNEL_3;
                     jpegEndecodecChannelNum = JPEGEncoder::JPEG_ENDECODEC_CHANNEL_3;
                 }
                     break;
                 case 3:{
                     temp.cameraId = CAMERA_ID_4;
                     h264EndecodecChannelNum = XuEncoder::H264_ENDECODEC_CHANNEL_4;
                     jpegEndecodecChannelNum = JPEGEncoder::JPEG_ENDECODEC_CHANNEL_4;
                 }
                     break;
             }
             /* 启动MP4录制 */
             if(G3_Configuration::getInstance().isHasTfCard()){
                 temp.mp4RecordRunnable = new MP4RecordRunnable();
                 temp.mp4RecordRunnable->init(temp.cameraId, *this);
                 temp.mp4RecordRunnable->setHasTfCard(true);
                 if (threadPool.available() > 0) {
                     threadPool.start(*temp.mp4RecordRunnable);
                 }
             }
             /* 启动JPEG编码 */
             if(G3_Configuration::getInstance().isHasTfCard() && G3_Configuration::getInstance().getAlarmEvidenceTypes().photo){
                 temp.jpegEncoder = new JPEGEncoder();
                 temp.jpegEncoder->initEncoder(temp.cameraId, IMAG_WIDTH, IMAG_HEIGHT,jpegEndecodecChannelNum,*this);
             }
             /* 启动H.264编码 */
             temp.h264EncoderRunnable = new H264EncoderRunnable();
             temp.h264EncoderRunnable->init(temp.cameraId, IMAG_WIDTH, IMAG_HEIGHT, G3_Configuration::getInstance().getCameraFrameRate(), curH264BitRate,
                                            h264EndecodecChannelNum, *this);
             if (threadPool.available() > 0) {
                 threadPool.start(*temp.h264EncoderRunnable);
             }
             /* 启动镜头图像获取线程 */
             temp.getCameraImagRunnable = new CameraImagRunnable();
             temp.getCameraImagRunnable->init(temp.cameraId, IMAG_WIDTH, IMAG_HEIGHT, *this);
             CameraType cameraType;
             G3_Configuration::getInstance().getCameraTypeInfoOfCamera(cameraType, temp.cameraId);
             /* JOE哥哥提出的要求  除了向后的镜头之外，如果镜头选择了R158的算法决策或者DSM（MUV除外），那么这个镜头的画面也需要水平镜像  */
             if (cameraType.cameraOrientation == 2 || cameraType.adType.pedestrian_r158 || cameraType.adType.vehicle_r158 || cameraType.adType.mt_dsm_face || cameraType.adType.std_dsm_face) {
                 temp.getCameraImagRunnable->setNeedMirror(true);
             } else {
                 temp.getCameraImagRunnable->setNeedMirror(false);
             }
             if (threadPool.available() > 0) {
                 threadPool.start(*temp.getCameraImagRunnable);
             }
             /* 添加到列表里 */
             cameraRunnableList.push_back(temp);

         }

     }else{
         /* 长度大于4，不合法，不做任何事情 */
         ; //not to do
     }
    return ret;
}

void MediaUnitManagr::setPhotoresistorValue(float photoresistorValue) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setPhotoresistorValue(photoresistorValue);
    }
}

void MediaUnitManagr::setIRLampSwichStatus(const bool lampSwich) {
    /* 遍历镜头列表 */
    for(std::size_t i= 0; i < cameraRunnableList.size(); i ++){
        cameraRunnableList[i].getCameraImagRunnable->setIRLampSwichStatus(lampSwich);
    }
}

CAMERAIDLIST MediaUnitManagr::getOtherCameraFromC3Rule(CAMERAIDLIST cameraId) {
    CAMERAIDLIST ret = CAMERA_ID_UNKNOW;


    /* 跟镜头相关的规则列表 */
    C3SwitchRule ruleForCamera;
    /* 镜头应该对应的C3开关状态 */
    std::vector<C3SwitchRule> ruleList = G3_Configuration::getInstance().getC3SwicthRuleList();
    /* 根据镜头ID有不同的处理方式 */
    switch (cameraId) {
        case CAMERA_ID_1:{
            ruleForCamera = G3_Configuration::getInstance().getC3SwitchRuleByC3Id(C3ID_1);
        }
            break;
        case CAMERA_ID_2:{
            ruleForCamera = G3_Configuration::getInstance().getC3SwitchRuleByC3Id(C3ID_2);
        }
            break;
        case CAMERA_ID_3:{
            ruleForCamera = G3_Configuration::getInstance().getC3SwitchRuleByC3Id(C3ID_3);
        }
            break;
        case CAMERA_ID_4:{
            ruleForCamera = G3_Configuration::getInstance().getC3SwitchRuleByC3Id(C3ID_4);
        }
            break;
    }

    /* 看下是不是有对应的规则 */
    if(ruleForCamera.c3Id != -1){
        /* 有规则，那么就把打开的时候对应的相机ID传出去 */
        ret = static_cast<CAMERAIDLIST>(ruleForCamera.cameraForEnable);
    }

    return ret;
}

int MediaUnitManagr::onJpgFileGet(const JPGFileInfo &jpgFileInfo) {
    return curMainUnitInterface->onJpgFileGet(jpgFileInfo);
}
