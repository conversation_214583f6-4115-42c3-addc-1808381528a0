//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/13.
//

#include <unistd.h>

#include <XuFile.h>
#include <XuString.h>
#include "WarMp4Recorder.h"
#include "XuH264ToMp4.h"
#include "XuTimeUtil.h"
#include "XuLog.h"
#include "CodeUtils.h"


WarMp4Recorder::WarMp4Recorder() {
    /* 在构造函数这里开辟内存 */
    for(std::size_t i = 0; i < WAR_H264_FRAME_LIST_SIZE; i ++){
        CameraH264Data temp;
        war_frameList.push_back(temp);
    }
}

WarMp4Recorder::~WarMp4Recorder() {

}

int WarMp4Recorder::init(int cameraId,MultimediaDataCallback &mainUnitInterface) {
    curCameraId = cameraId;

    curMainUnitInterface = &mainUnitInterface;

    /* 检查一下目录 */
    checkVideoDir();
    /* 获取TF卡总容量 */
    uint64_t totalSize = XuFile::getInstance().getDiskTotalSize(G3_Configuration::getInstance().getCurFileStorageRoot().c_str());
    /* 根据设备类型确定一路摄像头的报警视频文件能占多大百分比的空间 */
    switch (G3_Configuration::getInstance().getG3DeviceWorkingMode()) {
        case G3WORKMODE_V6:{
            /* V6的话有自己的定义 */
            MAX_WAR_SIZE = totalSize * MAX_ONE_CAMERA_WAR_VIDEO_FILE_SIZE_IN_V6;
        }
            break;
        case G3WORKMODE_G4MINI:{
            /* G4-MINI的话有自己的定义 */
            MAX_WAR_SIZE = totalSize * MAX_ONE_CAMERA_WAR_VIDEO_FILE_SIZE_IN_G4MINI;
        }
            break;
        default:{
            /* 其他模式都是有两路镜头，所以有自己的定义 */
            MAX_WAR_SIZE = totalSize * MAX_ONE_CAMERA_WAR_VIDEO_FILE_SIZE_IN_G3S;
        }
            break;
    }


    warFileFrameCache = (uint8_t *) malloc(1024 * 1024 * 8);





    warMp4RecorderThread.start(*this);


    return 0;
}



void WarMp4Recorder::run() {


    _thNmae.clear();
    _thNmae.append("Muxer ");
    _thNmae.append(std::to_string(curCameraId));
    _thNmae.append(" war");
    if (!_thNmae.empty()) {
        pthread_setname_np(pthread_self(), _thNmae.c_str());
    }

    needStop = false;
    running = true;
    while (!needStop){

        if(needMuxer){
            muxerMp4();
        }
        /* 等待10ms后再检查一下是否需要录制 */
        usleep(10 * 1000);
    }

    pthread_setname_np(pthread_self(), "Finish");

}

void WarMp4Recorder::muxerWarFile(uint64_t timeStamp, uint32_t before, uint32_t last, int type) {
    if (!needMuxer) {
        warTimeStamp = timeStamp;
        beforeTime = before;
        lastTime = last;
        evenCode = type;
        needMuxer = true;
    }
}

void WarMp4Recorder::setCurCamerH264Data(const CameraH264Data &camerH264Data) {
    /* 再写入到报警使用的帧列表里 */
    if (camerH264Data.getFramePurpose() == CameraH264Data::MUXER_STATE_WRITE) {
        camreWarListLock.lock();
        war_frameList[h264ListIndex].setCameraId(camerH264Data.getCameraId());
        war_frameList[h264ListIndex].setImagWidth(camerH264Data.getImagWidth());
        war_frameList[h264ListIndex].setImagHeight(camerH264Data.getImagHeight());
        war_frameList[h264ListIndex].setTimestamp(camerH264Data.getTimestamp());
        war_frameList[h264ListIndex].setFramePurpose(camerH264Data.getFramePurpose());
        war_frameList[h264ListIndex].setCurH264Data(camerH264Data.getDataLen(), camerH264Data.getCurH264Data());
        h264ListIndex++;
        if (h264ListIndex >= WAR_H264_FRAME_LIST_SIZE) {
            h264ListIndex = 0;
        }

//        uint64_t cacheLen = 0;
//        for(int i =0 ; i < WAR_H264_FRAME_LIST_SIZE; i ++){
//            cacheLen += war_frameList[i].getVector().size();
//        }
//        printf("WAR curCameraId=%d  cacheLen=%llu \n",curCameraId,cacheLen);
        camreWarListLock.unlock();
    }
}

std::vector<int> WarMp4Recorder::getWarFrameListOrderedIndexs() {
    std::vector<int> ret;

    /* 先获取当前最后一个存放数据的Index */
    std::size_t curIndex = -1;
    if(h264ListIndex >= war_frameList.size()){
        curIndex = war_frameList.size() - 1;
    }else{
        curIndex = h264ListIndex;
    }
    printf("curIndex=%d \n",curIndex);
    if(curIndex >= 0 && curIndex < war_frameList.size()){
        /* 拿出index+1 ~ size的内容 */
        for(std::size_t i = curIndex; i < war_frameList.size(); i ++){
            ret.push_back(i);
        }
        /* 拿出0 ~ index的内容 */
        for(std::size_t i = 0; i < curIndex; i ++){
            ret.push_back(i);
        }

    }
    return ret;
}

void WarMp4Recorder::setNeedStop(bool needStop) {
    WarMp4Recorder::needStop = needStop;
}

void WarMp4Recorder::muxerMp4() {

    try {

        /* 先等一下，采集到足够的数据 */
        usleep(lastTime * 1000);

        /* 先把内存复制出来一份 锁住读写锁  复制出一份感觉的数据出来*/
        camreWarListLock.lock();

        std::vector <WarFileFrameIndexInfo> warFileFrameIndexList;
        int curIndex = 0;

        /* 先把vector里面的数据排序一下 */
        std::vector <int> orderedIndexs = getWarFrameListOrderedIndexs();

        /* 找到符合条件的第一个I帧 */
        int fristI = 0;
        for (std::size_t i = 0; i < orderedIndexs.size(); i++) {
            CameraH264Data temp = war_frameList[orderedIndexs[i]];

            uint64_t warVideoStartTime = warTimeStamp;
            warVideoStartTime -= beforeTime;

            if (temp.getTimestamp() <= warVideoStartTime) {
                if (temp.getDataLen() > 0 && ((temp.getCurH264Data()[4] & 0x1F) == 5)) {
                    fristI = i;
                }
            } else {
                break;
            }
        }
        /* 找到符合条件的最后一个I帧 */
        int lastI = orderedIndexs.size() - 1;
        for (int i = orderedIndexs.size() - 1; i >= 0; i--) {
            CameraH264Data temp = war_frameList[orderedIndexs[i]];
            uint64_t warVideoStopTime = warTimeStamp;
            warVideoStopTime += lastTime;
            if ((temp.getTimestamp()) >= warVideoStopTime) {
                if (temp.getDataLen() > 0 && ((temp.getCurH264Data()[4] & 0x1F) == 5)) {
                    lastI = i;
                }
            } else {
                break;
            }
        }
        printf("fristI=%d   lastI=%d   warFileFrameIndexList.size=%d warTimeStamp=%llu \n",fristI,lastI,warFileFrameIndexList.size(),warTimeStamp);
        /* 找一个SPS */
        for (std::size_t i = 0; i < orderedIndexs.size(); i++) {
            CameraH264Data temp = war_frameList[orderedIndexs[i]];
            if (temp.getDataLen() > 0 && ((temp.getCurH264Data()[4] & 0x1F) == 7)) {
                memcpy(warFileFrameCache + curIndex, temp.getCurH264Data(), temp.getDataLen());
                WarFileFrameIndexInfo infoTemp;
                infoTemp.index = curIndex;
                infoTemp.len = temp.getDataLen();
                warFileFrameIndexList.push_back(infoTemp);
                curIndex += temp.getDataLen();
                break;
            } else { ;
            }
        }

        /* 找一个PPS */
        for (std::size_t i = 0; i < orderedIndexs.size(); i++) {
            CameraH264Data temp = war_frameList[orderedIndexs[i]];
            if (temp.getDataLen() > 0 && ((temp.getCurH264Data()[4] & 0x1F) == 8)) {
                memcpy(warFileFrameCache + curIndex, temp.getCurH264Data(), temp.getDataLen());
                WarFileFrameIndexInfo infoTemp;
                infoTemp.index = curIndex;
                infoTemp.len = temp.getDataLen();
                warFileFrameIndexList.push_back(infoTemp);
                curIndex += temp.getDataLen();
                break;
            } else { ;
            }
        }


        /* 从I帧开始写入正常的视频帧 */
        for (int i = fristI; i <= lastI; i++) {
            memcpy(warFileFrameCache + curIndex, war_frameList[orderedIndexs[i]].getCurH264Data(), war_frameList[orderedIndexs[i]].getDataLen());
            WarFileFrameIndexInfo infoTemp;
            infoTemp.index = curIndex;
            infoTemp.len = war_frameList[orderedIndexs[i]].getDataLen();
            warFileFrameIndexList.push_back(infoTemp);
            curIndex += infoTemp.len;
        }


        camreWarListLock.unlock();

        /* 当前MP4文件信息 */
        Mp4FileInfo curMp4Fileinfo;
        curMp4Fileinfo.setCameraId(curCameraId);
        /* 填入事件类型 */
        curMp4Fileinfo.setFileType(evenCode);

        /* 先根据mp4视频文件的类型判断应该存到哪里 */
        std::string filePath = G3_Configuration::getInstance().getCurFileStorageRoot();

        std::string alarmFileRoot = G3_Configuration::getInstance().getCurFileStorageRoot();

        /* 根据相机ID确定次级目录 */
        switch (curCameraId) {
            case CAMERA_ID_1: {
                alarmFileRoot.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
                filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
            }
            break;
            case CAMERA_ID_2: {
                alarmFileRoot.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
                filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
            }
                break;
            case CAMERA_ID_3: {
                alarmFileRoot.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
                filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
            }
                break;
            case CAMERA_ID_4: {
                alarmFileRoot.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
                filePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
            }
                break;
        }

        switch (evenCode) {
            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_LEFT_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_LEFT_BACKWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL1:
            case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL1:
            case EVENT_BSD_LEFT_FORWARD_VEHICLE_LEVEL2:
            case EVENT_BSD_LEFT_BACKWARD_VEHICLE_LEVEL2:
            case EVENT_BSD_LEFT_FORWARD_CAMERA_COVER:
            case EVENT_BSD_LEFT_BACKWARD_CAMERA_COVER: {
                filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_LEFT);
            }
                break;
            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_RIGHT_BACKWARD_PEDESTRIAN_LEVEL1:
            case EVENT_BSD_RIGHT_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_RRIGHT_BACKWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL1:
            case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL1:
            case EVENT_BSD_RIGHT_FORWARD_VEHICLE_LEVEL2:
            case EVENT_BSD_RIGHT_BACKWARD_VEHICLE_LEVEL2:
            case EVENT_BSD_RIGHT_FORWARD_CAMERA_COVER:
            case EVENT_BSD_RIGHT_BACKWARD_CAMERA_COVER: {
                filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_RIGHT);
            }
                break;

            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL1:
            case EVENT_PDW_FORWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_FORWARD_VEHICLE_LEVEL1:
            case EVENT_BSD_FORWARD_VEHICLE_LEVEL2:
            case EVENT_PDW_FORWARD_CAMERA_COVER: {
                filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_FORWARD);
            }
                break;

            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL1:
            case EVENT_PDW_BACKWARD_PEDESTRIAN_LEVEL2:
            case EVENT_BSD_BACKWARD_VEHICLE_LEVEL1:
            case EVENT_BSD_BACKWARD_VEHICLE_LEVEL2:
            case EVENT_PDW_BACKWARD_CAMERA_COVER: {
                filePath.append(WAR_VIDEO_FILE_PATH_OF_BSD_BACKWARD);
            }
                break;

            case EVENT_BSD_R151_AREA1_PEDESTRIAN:
            case EVENT_BSD_R151_AREA2_PEDESTRIAN:
            case EVENT_BSD_R151_AREA3_PEDESTRIAN:
            case EVENT_BSD_R151_AREA4_PEDESTRIAN:
            case EVENT_BSD_R151_AREA1_VEHICLE:
            case EVENT_BSD_R151_AREA2_VEHICLE:
            case EVENT_BSD_R151_CAMERA_COVER:{
                filePath.append(WAR_VIDEO_FILE_PATH_OF_R151);
            }
                break;

            case EVENT_BSD_R158_AREA1_PEDESTRIAN:
            case EVENT_BSD_R158_AREA2_PEDESTRIAN:
            case EVENT_BSD_R158_AREA3_PEDESTRIAN:
            case EVENT_BSD_R158_AREA1_VEHICLE:
            case EVENT_BSD_R158_AREA2_VEHICLE:
            case EVENT_BSD_R158_CAMERA_COVER:{
                filePath.append(WAR_VIDEO_FILE_PATH_OF_R158);
            }
                break;

            case EVENT_BSD_R159_AREA1_PEDESTRIAN:
            case EVENT_BSD_R159_AREA2_PEDESTRIAN:
            case EVENT_BSD_R159_AREA3_PEDESTRIAN:
            case EVENT_BSD_R159_AREA1_VEHICLE:
            case EVENT_BSD_R159_AREA2_VEHICLE:
            case EVENT_BSD_R159_CAMERA_COVER:{
                filePath.append(WAR_VIDEO_FILE_PATH_OF_R159);
            }
                break;

            default: {
                filePath.append(WAR_VIDEO_FILE_PATH_OF_UNKNOW_MP4);
            }
                break;
        }


        /* 检查下录制出来的大小是否已经超过了阈值 需要覆盖了 */
        uint64_t dvrSize = XuFile::getInstance().getAllFileLength(alarmFileRoot.c_str());
        printf("dvrSize=%llu   MAX_WAR_SIZE=%llu\n", dvrSize, MAX_WAR_SIZE);
        if (dvrSize >= MAX_WAR_SIZE) {
            /* 大小已经超了  删掉6个文件 */
            XuFile::getInstance().removeOldMp4File_Full(alarmFileRoot.c_str(), 8, true);
        } else {
            ; // not to do
        }
        /* 判断下是不是V6模式 */
        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6){
            /* 不是V6模式  获取指定格式的时间字符串用来放到文件名 */
            char fileName[15] = {0x00};
            int fileNameLen = XuString::getInstance().getStrFormTimestamp((warTimeStamp/1000),fileName, sizeof(fileName), "%Y%m%d%H%M%S", false);
            filePath.append(fileName, fileNameLen);
        }else{
            /* 是V6模式，那么就读序号用来做文件名 */
            uint32_t alarmVideoIndex = getV6AlarmVideoIndexForName();
            filePath.append(std::to_string(alarmVideoIndex));
        }
        /* 填入镜头ID */
        filePath.append("_" + std::to_string(curCameraId));
        /* 填入事件码 */
        filePath.append("_" + std::to_string(evenCode));
        /* 填入状态码  1为被保护  */
        filePath.append("_0");
        /* 填入是否加密  1为被加密 */
        filePath.append("_");
        filePath.append(std::to_string(G3_Configuration::getInstance().getMultimediaFilesEncryptEnable()));
        /* 填入后缀 */
        filePath.append(".mp4");



        std::string tmpWarFilePath = "/tmp/war";
        /* 填入镜头ID */
        tmpWarFilePath.append("_" + std::to_string(curCameraId));
        /* 填入事件码 */
        tmpWarFilePath.append("_" + std::to_string(evenCode));
        /* 填入后缀 */
        tmpWarFilePath.append(".mp4");


        /* 写入到MP4文件中 */
        XuH264ToMp4 mp4Muxer;
        /* 在这里，根据是不是需要加密选择不同的录制路径 */
        if(G3_Configuration::getInstance().getMultimediaFilesEncryptEnable() == 0){
            /* 先创建MP4文件 */
            mp4Muxer.create_and_open_file(filePath.c_str());
        }else{
            /* 录制到临时文件 */
            mp4Muxer.create_and_open_file(tmpWarFilePath.c_str());
        }

        /* 把路径存到MP4信息里 */
        curMp4Fileinfo.setFilePath(filePath.c_str());
        /* 把开始时间跟结束时间跟报警时间写入到MP4信息里面 */
        curMp4Fileinfo.setStartTime(warTimeStamp - beforeTime);
        curMp4Fileinfo.setStopTime(warTimeStamp + lastTime);
        curMp4Fileinfo.setAlarmTime(warTimeStamp);
        curMp4Fileinfo.setTimeStamp(warTimeStamp);
        /* 写个SPS进去 */
        mp4Muxer.write_packet(warFileFrameCache + warFileFrameIndexList[0].index, warFileFrameIndexList[0].len);
        /* 写个PPS进去 */
        mp4Muxer.write_packet(warFileFrameCache + warFileFrameIndexList[1].index, warFileFrameIndexList[1].len);
        /* 写实际数据 */
        for (std::size_t i = 2; i < warFileFrameIndexList.size(); i++) {
            mp4Muxer.write_packet(warFileFrameCache + warFileFrameIndexList[i].index, warFileFrameIndexList[i].len);

        }
        /* 写个MP4的结尾  结束掉MP4录制 */
        mp4Muxer.close_file();





        /* 如果是需要加密的情况下，需要把文件信息存起来给加密线程用,不需要加密的话就直接告诉外面 */
        if(G3_Configuration::getInstance().getMultimediaFilesEncryptEnable() != 0){
            /* 把文件的信息存起来，给加密线程使用 */
            std::string infoFileName = XuString::getInstance().replaceString(tmpWarFilePath,".mp4",".i");
            uint8_t infoBytes[512] = {0x00};
            int len = curMp4Fileinfo.toCode(infoBytes);
            if(len > 0){
                XuFile::getInstance().writeFile(infoFileName.c_str(),infoBytes,len);
            }else{
                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR,"CAMERATest") << "curMp4Fileinfo.toCode failed!" << XU_LOG_END;
            }
        }else{
            /* 把MP4信息抛出去外面 */
            curMainUnitInterface->onMp4FileGet(curMp4Fileinfo);
        }


    } catch (...) {
        printf("record war mp4 file has err:%s \n", strerror(errno));
    }

    needMuxer = false;
}

uint32_t WarMp4Recorder::getV6AlarmVideoIndexForName() {
    //NOTE 由于V6是单镜头的，所以这里就不加锁做同步了
    uint32_t ret = 0;
    uint32_t lastIndex = 0xFFFFFFFF;
    uint8_t readBuf[4] = {0x00};
    /* 先看看文件是否存在 */
    if(XuFile::getInstance().fileExists(V6_ALARM_VIDEO_COUNT_FILE)){
        /* 从文件中读出四个字节 */
        ret = XuFile::getInstance().readFile(V6_ALARM_VIDEO_COUNT_FILE,readBuf, sizeof(readBuf));
        /* 看看是不是读出了四个字节 */
        if(ret == 4){
            /* 把四个字节转成uint32(大端模式) */
            lastIndex = CodeUtils::getInstance().BbToUint32(readBuf);
        }else{
            /* 文件内容没有四个字节，说明出问题了，序号重新开始算 */
            ; // not to do
        }
    }
    /* 看看是不是读不出数据或者需要重新开始计算了 */
    if(lastIndex == 0xFFFFFFFF){
        /* 读不出数据或者需要重新开始计算了，那么就写个0进去 */
        memset(readBuf,0x00, sizeof(readBuf));
        XuFile::getInstance().writeFile(V6_ALARM_VIDEO_COUNT_FILE, readBuf, sizeof(readBuf));
        /* 返回0 */
        ret = 0;
    }else{
        /* 读的到正常的数据，那么先把结果+1，作为此次报警视频可以使用的下标 */
        ret = lastIndex + 1;
        /* 再把这个结果保存到文件 */
        CodeUtils::getInstance().uint32ToBb(ret,readBuf);
        XuFile::getInstance().writeFile(V6_ALARM_VIDEO_COUNT_FILE, readBuf, sizeof(readBuf));

    }
    return ret;
}

void WarMp4Recorder::checkVideoDir() {
    std::string warfilePath = G3_Configuration::getInstance().getCurFileStorageRoot();
    /* 根据镜头创建次级目录 */
    switch (curCameraId) {
        case CAMERA_ID_1:{
            warfilePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1);
        }
            break;
        case CAMERA_ID_2:{
            warfilePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2);
        }
            break;
        case CAMERA_ID_3:{
            warfilePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3);
        }
            break;
        case CAMERA_ID_4:{
            warfilePath.append(WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4);
        }
            break;
    }
    std::string warFilePathTemp = warfilePath;

    warFilePathTemp.append(WAR_VIDEO_FILE_PATH_OF_BSD_LEFT);
    XuFile::getInstance().mkpath(warFilePathTemp);
    warFilePathTemp.clear();
    warFilePathTemp.append(warfilePath);
    warFilePathTemp.append(WAR_VIDEO_FILE_PATH_OF_BSD_RIGHT);
    XuFile::getInstance().mkpath(warFilePathTemp);
    warFilePathTemp.clear();
    warFilePathTemp.append(warfilePath);
    warFilePathTemp.append(WAR_VIDEO_FILE_PATH_OF_BSD_FORWARD);
    XuFile::getInstance().mkpath(warFilePathTemp);
    warFilePathTemp.clear();
    warFilePathTemp.append(warfilePath);
    warFilePathTemp.append(WAR_VIDEO_FILE_PATH_OF_BSD_BACKWARD);
    XuFile::getInstance().mkpath(warFilePathTemp);
    warFilePathTemp.clear();
    warFilePathTemp.append(warfilePath);
    warFilePathTemp.append(WAR_VIDEO_FILE_PATH_OF_R151);
    XuFile::getInstance().mkpath(warFilePathTemp);
    warFilePathTemp.clear();
    warFilePathTemp.append(warfilePath);
    warFilePathTemp.append(WAR_VIDEO_FILE_PATH_OF_R158);
    XuFile::getInstance().mkpath(warFilePathTemp);
    warFilePathTemp.clear();
    warFilePathTemp.append(warfilePath);
    warFilePathTemp.append(WAR_VIDEO_FILE_PATH_OF_R159);
    XuFile::getInstance().mkpath(warFilePathTemp);
    warFilePathTemp.clear();
    warFilePathTemp.append(warfilePath);
    warFilePathTemp.append(WAR_VIDEO_FILE_PATH_OF_UNKNOW_MP4);
    XuFile::getInstance().mkpath(warFilePathTemp);
}

bool WarMp4Recorder::isRunning() const {
    return running;
}

void WarMp4Recorder::setRunning(bool running) {
    WarMp4Recorder::running = running;
}

