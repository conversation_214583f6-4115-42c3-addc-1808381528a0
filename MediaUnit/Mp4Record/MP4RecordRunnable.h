//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/28.
//

#ifndef VIS_G3_SOFTWARE_MP4RECORDRUNNABLE_H
#define VIS_G3_SOFTWARE_MP4RECORDRUNNABLE_H

#include <Poco/Runnable.h>
#include <mutex>
#include <list>
#include "MultimediaDataCallback.h"
#include "XuH264ToMp4.h"
#include "utils/XuFile.h"
#include "utils/XuString.h"
#include "utils/XuTimeUtil.h"
#include <unistd.h>
#include "G3DetectionDefind.h"
#include "WarMp4Recorder.h"









class MP4RecordRunnable : public Poco::Runnable {
public:

    /* 一路镜头的用于录制DVR的h264数据缓存队列的长度   27*5=135 即存5秒的 因为现在有个加密的要求 */
    static const int DVR_H264_FRAME_LIST_SIZE = 135;


    void run() override;

    /**
     * 初始化
     *
     * @param cameraId ： 相机ID
     * @param mainUnitInterface ： 回调信息给外面的接口
     *
     * */
    void init(int cameraId, MultimediaDataCallback &mainUnitInterface);

    /**
     * 设置新的H264数据进来
     *
     * @param camerH264Data ： H264数据封装对象
     *
     * */
    void setCurCamerH264Data(const CameraH264Data &camerH264Data);

    /**
     * 深度拷贝H264数据封装对象的数组
     *
     * @param src ： 源数组
     * @param des ： 目标数组
     * @param copyLen ： 拷贝多长
     *
     * @return 拷贝完成的长度
     * */
    int copyH264FrameList(CameraH264Data src[], CameraH264Data des[], int copyLen);

    /**
     * 生成一个MP4文件
     *
     * @param cameraId : 相机ID
     * @param timeStamp ： 生成的时间点
     * @param before ： 前多少毫秒
     * @param last ： 后多少毫秒
     * @param type ： 视频类型
     *
     * @return 结果 ：   0：成功   其他：失败
     *
     * */
    int muxerWarMP4(int cameraId, uint64_t timeStamp, uint32_t before, uint32_t last, int type);

    /**
     * 使用双休冒泡排序法将H264数据按时间从小到大进行排序
     */
    int bubbleSort(CameraH264Data war_frameList[], int len);

    __time_t getLastsaveMp4DataTime() const;

    int getCurCameraId() const;

    void setCurCameraId(int curCameraId);

    bool isHasTfCard() const;

    void setHasTfCard(bool hasTfCard);

    bool isCanRecord() const;

    void setCanRecord(bool canRecord);

private:
    /* 一路摄像头的DVR录像最大大小 （byte） */
    uint64_t MAX_DVR_SIZE;
    /* 是否需要停止MP4录制 */
    bool needStopMP4Record;
    /* 当前的相机ID */
    int curCameraId;
    /* 回传给Main模块的接口 */
    MultimediaDataCallback *curMainUnitInterface;
    /* DVR用的H264存放队列 */
    CameraH264Data dvr_frameList_camera[DVR_H264_FRAME_LIST_SIZE];
    uint64_t lastCamreDVRListInputIndex = 0;
    std::mutex camreDVRListLock;


    /* 上次写入一帧数据到MP4文件的时间 */
    __time_t lastsaveMP4DataTime = 0;

    /* 是否有TF卡 */
    bool hasTFCard = false;
    /* 是否能录制 */
    bool canRecord = false;

    WarMp4Recorder warFileRecorder;

    /* 临时存放未加密的文件的路径，此路径只在需要加密的时候使用 */
    std::string tmpDVRPath = "";
    /* 实际存放DVR文件的路径 */
    std::string filePath = "";

    const char *V6_DVR_COUNT_FILE = "V6DVRCountFile";

    /**
     * 获取V6模式下的DVR名字中的下标（既G4/G3模式下的时间戳的字段）
     *
     * @return 此次录制的DVR可以使用的下标
     */
    uint32_t getV6DVRIndexForName();

    /**
     * 检查存放视频的目录，如果不存在就创建
     */
    void checkVideoDir();


};


#endif //VIS_G3_SOFTWARE_MP4RECORDRUNNABLE_H
