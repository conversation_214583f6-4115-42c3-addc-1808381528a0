//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/25.
//

#ifndef VIS_G3_SOFTWARE_MP4FILEINFO_H
#define VIS_G3_SOFTWARE_MP4FILEINFO_H

#include <cstdint>
#include <bits/stdint-uintn.h>
#include <string>
#include <ostream>


class Mp4FileInfo {
public:

    void setFilePath(const std::string &filePath);

    int getFileType() const;

    void setFileType(int fileType);

    uint64_t getStartTime() const;

    void setStartTime(uint64_t startTime);

    uint64_t getStopTime() const;

    void setStopTime(uint64_t stopTime);

    uint64_t getAlarmTime() const;

    void setAlarmTime(uint64_t alarmTime);

    const uint8_t &getCameraId() const;

    void setCameraId(const uint8_t &cameraId);

    const uint64_t &getTimeStamp() const;

    void setTimeStamp(const uint64_t &timeStamp);

    const std::string &getFilePath() const;

    /**
     * 把类的内容转成数组
     *
     * @param buf : 存放内容数组的指针
     * @return 内容数组的长度 -1为失败
     */
    const int toCode(uint8_t *buf);

    /**
     * 把数组转成类的内容
     *
     * @param buf : 存放内容数组的指针
     * @return 0：成功  其他：失败
     */
    const int deCode(uint8_t *buf);

    bool operator==(const Mp4FileInfo &rhs) const;

    bool operator!=(const Mp4FileInfo &rhs) const;

    void toString();


private:
    /* mp4文件的路径 */
    std::string filePath;
    /* 文件类型 */
    int fileType = 0;
    /* 开始时间   单位：us */
    uint64_t startTime;
    /* 结束时间   单位：us */
    uint64_t stopTime;
    /* uint64_t   单位：us    此项只有在类型为报警视频时才有效 */
    uint64_t alarmTime;
    /* 相机ID */
    uint8_t cameraId;
    /* 录制的时间  （DVR为开始录制的时间   报警视频为报警的时间） */
    uint64_t timeStamp;

};


#endif //VIS_G3_SOFTWARE_MP4FILEINFO_H
