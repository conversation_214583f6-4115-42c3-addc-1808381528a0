//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/28.
//


#include "MP4RecordRunnable.h"
#include "XuLog.h"
#include "CodeUtils.h"


void MP4RecordRunnable::setCurCamerH264Data(const CameraH264Data &camerH264Data) {
//    printf("cameraId:%d  camerH264Data.getTimestamp():%lld  FramePurpose:%d \n",camerH264Data.getCameraId(),camerH264Data.getTimestamp(),camerH264Data.getFramePurpose());
    /* 先写入到DVR使用的帧列表里 */
    camreDVRListLock.lock();
    /* 看看是不是SPS+PPS+I帧的组合帧 */
    if (camerH264Data.getDataLen() > 5 &&(camerH264Data.getCurH264Data()[4] & 0x1F) == 7) {
        /* 是组合帧，那么就拆开吧（RKMedia塞组合帧会有问题） */
        uint8_t nalHead[] = {00, 00, 00, 01};
        uint16_t result[50];
        int resultLen = CodeUtils::getInstance().findCharsInChars(
                reinterpret_cast<const char *>(camerH264Data.getCurH264Data()), camerH264Data.getDataLen(),
                reinterpret_cast<const char *>(nalHead), 4, result);
        /* 一帧帧分开 */
        for (int i = 0; i < resultLen; i++) {
            /* 先填哪些公用的 */
            dvr_frameList_camera[lastCamreDVRListInputIndex].setCameraId(camerH264Data.getCameraId());
            dvr_frameList_camera[lastCamreDVRListInputIndex].setImagWidth(camerH264Data.getImagWidth());
            dvr_frameList_camera[lastCamreDVRListInputIndex].setImagHeight(camerH264Data.getImagHeight());
            dvr_frameList_camera[lastCamreDVRListInputIndex].setTimestamp(camerH264Data.getTimestamp());
            dvr_frameList_camera[lastCamreDVRListInputIndex].setFramePurpose(camerH264Data.getFramePurpose());
            /* 填入分开后的内 */
            if (i == resultLen - 1) {
                dvr_frameList_camera[lastCamreDVRListInputIndex].setCurH264Data(
                        (camerH264Data.getDataLen() - result[i]),
                        (camerH264Data.getCurH264Data() + result[i]));
            } else {
                dvr_frameList_camera[lastCamreDVRListInputIndex].setCurH264Data(result[i + 1] - result[i],
                                                                                (camerH264Data.getCurH264Data() +
                                                                                 result[i]));
            }

            /* 复制一份给报警视频录制线程 */
            if(warFileRecorder.isRunning()){
                warFileRecorder.setCurCamerH264Data(dvr_frameList_camera[lastCamreDVRListInputIndex]);
            }

            /* 移动下标 */
            lastCamreDVRListInputIndex++;
            /* 如果输入下标不是到了最后一个  就照常写入后移  如果到了最后  那么从第一个重新开始覆盖 */
            if (lastCamreDVRListInputIndex >= DVR_H264_FRAME_LIST_SIZE) {
                lastCamreDVRListInputIndex = 0;
            }
        }

        camreDVRListLock.unlock();

    } else {
        /* 不是组合帧就正常写入就好了 */
        dvr_frameList_camera[lastCamreDVRListInputIndex].setCameraId(camerH264Data.getCameraId());
        dvr_frameList_camera[lastCamreDVRListInputIndex].setImagWidth(camerH264Data.getImagWidth());
        dvr_frameList_camera[lastCamreDVRListInputIndex].setImagHeight(camerH264Data.getImagHeight());
        dvr_frameList_camera[lastCamreDVRListInputIndex].setTimestamp(camerH264Data.getTimestamp());
        dvr_frameList_camera[lastCamreDVRListInputIndex].setFramePurpose(camerH264Data.getFramePurpose());
        dvr_frameList_camera[lastCamreDVRListInputIndex].setCurH264Data(camerH264Data.getDataLen(),
                                                                        camerH264Data.getCurH264Data());
        lastCamreDVRListInputIndex++;
        /* 如果输入下标不是到了最后一个  就照常写入后移  如果到了最后  那么从第一个重新开始覆盖 */
        if (lastCamreDVRListInputIndex >= DVR_H264_FRAME_LIST_SIZE) {
            lastCamreDVRListInputIndex = 0;
        }
        camreDVRListLock.unlock();
        /* 复制一份给报警视频录制线程 */
        if(warFileRecorder.isRunning()){
            warFileRecorder.setCurCamerH264Data(camerH264Data);
        }

    }

}

void MP4RecordRunnable::init(int cameraId, MultimediaDataCallback &mainUnitInterface) {
    curCameraId = cameraId;
    curMainUnitInterface = &mainUnitInterface;
    /* 创建一下存视频文件的目录 */
    checkVideoDir();
    /* 判断下一路DVR录像能使用多少内存空间 */
    /* 获取TF卡总容量 */
    uint64_t totalSize = XuFile::getInstance().getDiskTotalSize(G3_Configuration::getInstance().getCurFileStorageRoot().c_str());
    /* 根据设备类型确定一路摄像头的DVR文件能占多大百分比的空间 */
    switch (G3_Configuration::getInstance().getG3DeviceWorkingMode()) {
        case G3WORKMODE_V6:{
            /* V6的话有不同的定义 */
            MAX_DVR_SIZE = totalSize * MAX_ONE_CAMERA_DVR_FILE_SIZE_IN_V6;
        }
            break;
        case G3WORKMODE_G4MINI:{
            /* G4-MINI的话不同的定义 */
            MAX_DVR_SIZE = totalSize * MAX_ONE_CAMERA_DVR_FILE_SIZE_IN_G4MINI;
        }
            break;
        default:{
            /* 其他模式都是有两路镜头，所以有不同的定义 */
            MAX_DVR_SIZE = totalSize * MAX_ONE_CAMERA_DVR_FILE_SIZE_IN_G3S;
        }
            break;
    }
    needStopMP4Record = false;
    /* 根据配置启动视频录制 */
    if(G3_Configuration::getInstance().getAlarmEvidenceTypes().video){
        warFileRecorder.init(cameraId,mainUnitInterface);
    }



}

void MP4RecordRunnable::run() {
    std::string pthreadName = "MP4Record_";
    pthreadName.append(std::to_string(curCameraId));
    pthread_setname_np(pthread_self(), pthreadName.c_str());

    XuH264ToMp4 mp4Muxer;
    Mp4FileInfo curMp4Fileinfo;

    CameraH264Data tmppFrameList[DVR_H264_FRAME_LIST_SIZE];
    int h264FrameLen = -1;
    /*开始循环读写数据*/
    while (!needStopMP4Record) {

        /* 看看是否有数据可用 有的话就录制到MP4文件里 */
        if (lastCamreDVRListInputIndex > 0) {
            lastsaveMP4DataTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
            /* 先把内存复制出来一份 锁住读写锁  复制出一份干净的数据出来*/
            camreDVRListLock.lock();
            copyH264FrameList(dvr_frameList_camera, tmppFrameList, lastCamreDVRListInputIndex);
            /* 把长度记录一下  然后让接收线程从头开始存 */
            h264FrameLen = lastCamreDVRListInputIndex;
            lastCamreDVRListInputIndex = 0;
            camreDVRListLock.unlock();
            /* 检查下录制出来的大小是否已经超过了阈值 需要覆盖了 */
            std::string dvrPath = G3_Configuration::getInstance().getCurFileStorageRoot();
            /* 保存相机ID */
            curMp4Fileinfo.setCameraId(curCameraId);
            /* 确定DVR目录 */
            switch (curCameraId) {
                case CAMERA_ID_1: {
                    dvrPath.append(DVR_FILE_PATH_OF_CAMERA1);
                }
                    break;

                case CAMERA_ID_2: {
                    dvrPath.append(DVR_FILE_PATH_OF_CAMERA2);
                }
                    break;

                case CAMERA_ID_3: {
                    dvrPath.append(DVR_FILE_PATH_OF_CAMERA3);
                }
                    break;

                case CAMERA_ID_4: {
                    dvrPath.append(DVR_FILE_PATH_OF_CAMERA4);
                }
                    break;
            }
            uint64_t dvrSize = XuFile::getInstance().getAllFileLength(dvrPath.c_str());

            if (dvrSize >= MAX_DVR_SIZE) {
                printf("dvrSize=%llu MAX_DVR_SIZE=%llu dvrPath=%s \n",dvrSize,MAX_DVR_SIZE,dvrPath.c_str());
                /* 大小已经超了  删掉6个文件 */
                XuFile::getInstance().removeOldMp4File(dvrPath.c_str(), 8, true);
            }
            /* 遍历读出来的这些帧率，各自做对应的处理 */
            for (int i = 0; i < h264FrameLen; i++) {
                switch (tmppFrameList[i].getFramePurpose()) {
                    case CameraH264Data::MUXER_STATE_CREATE: {
                        /* 先写好存放路径 */
                        filePath.clear();
                        filePath.append(dvrPath);
                        /* 看看是不是V6模式 */
                        if(G3_Configuration::getInstance().getG3DeviceWorkingMode() != G3WORKMODE_V6){
                            /* 不是V6模式，那么获取指定格式的时间字符串用来当文件名 */
                            char strTime[15] = {0x00};
                            XuString::getInstance().getStrFormTime(strTime, 15, "%Y%m%d%H%M%S", false);
                            /* 添加时间 */
                            filePath.append(strTime);
                        }else{
                            /* 是V6模式，那么就读序号用来做文件名 */
                            uint32_t dvrIndex = getV6DVRIndexForName();
                            /* 添加到文件名 */
                            filePath.append(std::to_string(dvrIndex));
                        }
                        /* 填入镜头ID */
                        filePath.append("_" + std::to_string(curCameraId));
                        /* 填入事件码 */
                        filePath.append("_00");
                        /* 填入状态码  1为被保护  */
                        filePath.append("_0");
                        /* 填入是否加密  1为被加密 */
                        filePath.append("_");
                        filePath.append(std::to_string(G3_Configuration::getInstance().getMultimediaFilesEncryptEnable()));
                        /* 填入后缀 */
                        filePath.append(".mp4");
                        /* 在这里，根据是不是需要加密选择不同的录制路径 */
                        if(G3_Configuration::getInstance().getMultimediaFilesEncryptEnable() == 0){
                            /* 开始录制一个新的MP4文件 */
                            mp4Muxer.create_and_open_file(filePath.c_str());
                        }else{
                            /* 如果需要对多媒体文件进行加密的情况下，临时存放原始文件的路径 */
                            tmpDVRPath.clear();
                            tmpDVRPath.append("/tmp/dvr_"+std::to_string(curCameraId)+"_"+std::to_string(XuTimeUtil::getInstance().currentTimeSecond())+".mp4");
                            /* 开始录制一个新的MP4文件 */
                            mp4Muxer.create_and_open_file(tmpDVRPath.c_str());
                        }

                        /* 把名字和开始时间放到MP4的信息里 */
                        curMp4Fileinfo.setFilePath(filePath.c_str());
                        curMp4Fileinfo.setStartTime(XuTimeUtil::getInstance().currentTimeMillis());
                        curMp4Fileinfo.setTimeStamp(XuTimeUtil::getInstance().currentTimeMillis());



                    }
                        break;
                    case CameraH264Data::MUXER_STATE_WRITE: {
                        /* 写入H264视频数据 */
                        mp4Muxer.write_packet(tmppFrameList[i].getCurH264Data(), tmppFrameList[i].getDataLen());
//                        printf("mp4Muxer.write_packet  \n");

                    }
                        break;
                    case CameraH264Data::MUXER_STATE_STOP: {
                        /* 写个结尾 */
                        mp4Muxer.close_file();
                        /* 把结束时间放到MP4信息里 */
                        curMp4Fileinfo.setStopTime(XuTimeUtil::getInstance().currentTimeMillis());
                        curMp4Fileinfo.setFileType(EVENT_UNKNOW);

                        /* 如果是不需要加密的情况，那么就直接通知外面，如果是需要加密的情况，就要进行加密操作，这里只需要存好一些信息给加密用就行 */
                        if(G3_Configuration::getInstance().getMultimediaFilesEncryptEnable() == 0){
                            /* 录制结束了  通知一下外面 */
                            curMainUnitInterface->onMp4FileGet(curMp4Fileinfo);
                        }else{
                            /* 把文件的信息存起来，给加密线程使用 */
                            std::string dvrInfoFileName = XuString::getInstance().replaceString(tmpDVRPath,".mp4",".i");
                            uint8_t dvrInfoBytes[512] = {0x00};
                            int len = curMp4Fileinfo.toCode(dvrInfoBytes);
                            if(len > 0){
                                XuFile::getInstance().writeFile(dvrInfoFileName.c_str(),dvrInfoBytes,len);
                            }else{
                                vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,"CAMERATest") << "curMp4Fileinfo.toCode failed!" << XU_LOG_END;
                            }
                        }
                    }
                        break;
                    default: {
                    }
                        break;
                }
//
//                /* 回收一下内存 */
//                tmppFrameList[i].relese();
            }
        } else { ;
        }
        usleep(1000 * 10);
    }
    pthread_setname_np(pthread_self(), "Finish");
}

int MP4RecordRunnable::copyH264FrameList(CameraH264Data *src, CameraH264Data *des, int copyLen) {
    int ret = 0;
//    printf("copyH264FrameList src:%d  des:%d   copyLen:%d  \n",sizeof(src),sizeof(des),copyLen);
    for (int i = 0; i < copyLen; i++) {
        if (src[i].getTimestamp() != 0) {
            des[i].setCameraId(src[i].getCameraId());
            des[i].setImagWidth(src[i].getImagWidth());
            des[i].setImagHeight(src[i].getImagHeight());
            des[i].setTimestamp(src[i].getTimestamp());
            des[i].setFramePurpose(src[i].getFramePurpose());
            des[i].setCurH264Data(src[i].getDataLen(), src[i].getCurH264Data());
        }
        ret++;
    }
    return ret;
}

int MP4RecordRunnable::muxerWarMP4(int cameraId, uint64_t timeStamp, uint32_t before, uint32_t last, int type) {
//    printf("============================muxerWarMP4  cameraId:%d  timeStamp:%lld    before:%d   last:%d   type=%d canRecord:%d \n", cameraId, timeStamp,
//           before, last, type,canRecord);
    int ret = -1;
    if (canRecord && warFileRecorder.isRunning()) {
        warFileRecorder.muxerWarFile(timeStamp, before, last, type);
        ret = 0;
    }

    return ret;
}

int MP4RecordRunnable::bubbleSort(CameraH264Data *war_frameList, int len) {
    int ret = -1;
    int sentryI = 0;
    int sentryJ = len - 1;
    while (sentryI < sentryJ) {
        for (int i = sentryI; i < sentryJ; i++) {
            if (war_frameList[i].getTimestamp() > war_frameList[i + 1].getTimestamp()) {
                CameraH264Data temp = war_frameList[i];
                war_frameList[i] = war_frameList[i + 1];
                war_frameList[i + 1] = temp;
            }
        }
        sentryJ--;
        for (int i = sentryJ; i > sentryI; i--) {
            if (war_frameList[i].getTimestamp() < war_frameList[i - 1].getTimestamp()) {
                CameraH264Data temp = war_frameList[i];
                war_frameList[i] = war_frameList[i - 1];
                war_frameList[i - 1] = temp;
            }
        }
        sentryI++;
    }
    ret = 0;
    return ret;
}

__time_t MP4RecordRunnable::getLastsaveMp4DataTime() const {
    return lastsaveMP4DataTime;
}

int MP4RecordRunnable::getCurCameraId() const {
    return curCameraId;
}

void MP4RecordRunnable::setCurCameraId(int curCameraId) {
    MP4RecordRunnable::curCameraId = curCameraId;
}

bool MP4RecordRunnable::isHasTfCard() const {
    return hasTFCard;
}

void MP4RecordRunnable::setHasTfCard(bool hasTfCard) {
    hasTFCard = hasTfCard;
}

bool MP4RecordRunnable::isCanRecord() const {
    return canRecord;
}

void MP4RecordRunnable::setCanRecord(bool canRecord) {
    MP4RecordRunnable::canRecord = canRecord;
}

uint32_t MP4RecordRunnable::getV6DVRIndexForName() {
    //NOTE 由于V6是单镜头的，所以这里就不加锁做同步了
    uint32_t ret = 0;
    uint32_t lastIndex = 0xFFFFFFFF;
    uint8_t readBuf[4] = {0x00};
    /* 先看看文件是否存在 */
    if(XuFile::getInstance().fileExists(V6_DVR_COUNT_FILE)){
        /* 从文件中读出四个字节 */
        ret = XuFile::getInstance().readFile(V6_DVR_COUNT_FILE,readBuf, sizeof(readBuf));
        /* 看看是不是读出了四个字节 */
        if(ret == 4){
            /* 把四个字节转成uint32(大端模式) */
            lastIndex = CodeUtils::getInstance().BbToUint32(readBuf);
        }else{
            /* 文件内容没有四个字节，说明出问题了，序号重新开始算 */
            ; // not to do
        }
    }
    /* 看看是不是读不出数据或者需要重新开始计算了 */
    if(lastIndex == 0xFFFFFFFF){
        /* 读不出数据或者需要重新开始计算了，那么就写个0进去 */
        memset(readBuf,0x00, sizeof(readBuf));
        XuFile::getInstance().writeFile(V6_DVR_COUNT_FILE, readBuf, sizeof(readBuf));
        /* 返回0 */
        ret = 0;
    }else{
        /* 读的到正常的数据，那么先把结果+1，作为此次DVR可以使用的下标 */
        ret = lastIndex + 1;
        /* 再把这个结果保存到文件 */
        CodeUtils::getInstance().uint32ToBb(ret,readBuf);
        XuFile::getInstance().writeFile(V6_DVR_COUNT_FILE, readBuf, sizeof(readBuf));

    }
    return ret;
}

void MP4RecordRunnable::checkVideoDir() {
    std::string dirPath = G3_Configuration::getInstance().getCurFileStorageRoot();
    /* 根据镜头判断DVR文件的存放路径 */
    switch (curCameraId) {
        case CAMERA_ID_1:{
            dirPath.append(DVR_FILE_PATH_OF_CAMERA1);
        }
            break;
        case CAMERA_ID_2:{
            dirPath.append(DVR_FILE_PATH_OF_CAMERA2);
        }
            break;
        case CAMERA_ID_3:{
            dirPath.append(DVR_FILE_PATH_OF_CAMERA3);
        }
            break;
        case CAMERA_ID_4:{
            dirPath.append(DVR_FILE_PATH_OF_CAMERA4);
        }
            break;
    }
    /* 尝试创建路径 */
    XuFile::getInstance().mkpath(dirPath);
    /* 创建未定义视频的目录 */
    dirPath.clear();
    dirPath.append(G3_Configuration::getInstance().getCurFileStorageRoot());
    dirPath.append(UNKNOW_VIDEO_FILE_PATH);
    XuFile::getInstance().mkpath(dirPath);
}


