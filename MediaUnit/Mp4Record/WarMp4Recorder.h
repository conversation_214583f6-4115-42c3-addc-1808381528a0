//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/7/13.
//

#ifndef VIS_G3_SOFTWARE_WARMP4RECORDER_H
#define VIS_G3_SOFTWARE_WARMP4RECORDER_H

#include <Poco/Runnable.h>
#include <Poco/ThreadPool.h>
#include <mutex>
#include <list>
#include "CameraH264Data.h"
#include "MultimediaDataCallback.h"
#include "G3DetectionDefind.h"



/* 镜头1的报警视频的根目录 */
#define WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA1 "alarmVideo/camera1/"
/* 镜头2的报警视频的根目录 */
#define WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA2 "alarmVideo/camera2/"
/* 镜头3的报警视频的根目录 */
#define WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA3 "alarmVideo/camera3/"
/* 镜头4的报警视频的根目录 */
#define WAR_VIDEO_FILE_ROOT_PATH_OF_CAMERA4 "alarmVideo/camera4/"

/* 镜头的侧边报警左边的报警视频存放目录 */
#define WAR_VIDEO_FILE_PATH_OF_BSD_LEFT "BSD/Left/"
/* 镜头的侧边报警右边的报警视频存放目录 */
#define WAR_VIDEO_FILE_PATH_OF_BSD_RIGHT "BSD/Right/"
/* 镜头的侧边报警前边的报警视频存放目录 */
#define WAR_VIDEO_FILE_PATH_OF_BSD_FORWARD "BSD/Forward/"
/* 镜头的侧边报警后边的报警视频存放目录 */
#define WAR_VIDEO_FILE_PATH_OF_BSD_BACKWARD "BSD/Backward/"
/* 镜头的R151算法的报警视频存放目录 */
#define WAR_VIDEO_FILE_PATH_OF_R151 "R151/"
/* 镜头的R158算法的报警视频存放目录 */
#define WAR_VIDEO_FILE_PATH_OF_R158 "R158/"
/* 镜头的R159算法的报警视频存放目录 */
#define WAR_VIDEO_FILE_PATH_OF_R159 "R159/"
/* 镜头的未知算法类型的报警视频存放目录 */
#define WAR_VIDEO_FILE_PATH_OF_UNKNOW_MP4 "UNKNOW/"


class WarMp4Recorder : public Poco::Runnable {
public:

    struct WarFileFrameIndexInfo{
        int index;
        int len;
    };
    WarMp4Recorder();

    ~WarMp4Recorder();

    int init(int cameraId,MultimediaDataCallback &mainUnitInterface);


    void muxerWarFile(uint64_t timeStamp, uint32_t before, uint32_t last, int type);



    void setCurCamerH264Data(const CameraH264Data &camerH264Data);

    void setNeedStop(bool needStop);

    void muxerMp4();


    void run() override;

    bool isRunning() const;

    void setRunning(bool running);


private:

    /* 报警视频录制线程是否已启动 */
    bool running = false;

    /**
     * 把现在的H264数据缓存按从早到晚排序，然后把下标记录下来
     *
     * @return  排序完后的下标
     */
    std::vector<int> getWarFrameListOrderedIndexs();



    /* 一路摄像头的报警视频最大大小 （byte） */
    uint64_t MAX_WAR_SIZE = 1024 * 1024 * 1024 * 1;

    /* 是否需要停止 */
    bool needStop = true;
    /* 是否需要录制 */
    bool needMuxer;

    // 使用的线程类
    Poco::Thread warMp4RecorderThread;
    std::string _thNmae;

    MultimediaDataCallback *curMainUnitInterface;

    int curCameraId;
    uint64_t warTimeStamp;
    uint64_t beforeTime;
    uint64_t lastTime;
    int evenCode;


    /* 一路镜头的用于录制报警视频的h264数据缓存队列的长度   27*15=405 即存15秒的 */
    const std::size_t WAR_H264_FRAME_LIST_SIZE = 405;
    /* 报警用的H264存放队列 */
    std::vector<CameraH264Data> war_frameList;
    std::mutex camreWarListLock;
    /* c */
    std::size_t h264ListIndex = 0;




    uint8_t *warFileFrameCache = nullptr;

    const char *V6_ALARM_VIDEO_COUNT_FILE = "V6AlarmVideoCountFile";
    /**
     * 获取V6模式下的报警视频文件名字中的下标（既G4/G3模式下的时间戳的字段）
     *
     * @return 此次录制的报警视频文件可以使用的下标
     */
    uint32_t getV6AlarmVideoIndexForName();

    /**
     * 检查存放视频的目录，如果不存在就创建
     */
    void checkVideoDir();


};


#endif //VIS_G3_SOFTWARE_WARMP4RECORDER_H
