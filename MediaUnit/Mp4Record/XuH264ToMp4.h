//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/21.
//

#ifndef VIS_ADOS_I7_H264TOMP4_H
#define VIS_ADOS_I7_H264TOMP4_H

#include <cstring>
#include <cstdio>
#include <cstdint>
#include <mp4v2/mp4v2.h>

class XuH264ToMp4 {


public:
    XuH264ToMp4();

    ~XuH264ToMp4();

    /**
     * 创建MP4文件（开始MP4生成）
     * @param fileName：文件路径
     *
     * @return 结果  0：成功  其他：失败
     *
     * */
    int create_and_open_file(const char *fileName);

    /**
     * 停止当前的MP4生成
     *
     * @return 结果  0：成功  其他：失败
     *
     * */
    int close_file();

    /**
     * 写入一包H264数据
     *
     * @param h264Data：一帧h264数据
     * @param len：这一帧h264数据的长度
     *
     * @return 结果
     *
     * */
    bool write_packet(uint8_t *h264Data, int len);


private:

    unsigned char naluType;

    MP4FileHandle pHandle = nullptr;
    MP4TrackId videoId;
    int width = 1280;
    int height = 720;
    int frameRate = 25;
    int timeScale = 90000;  //此处存有疑虑
    int addStream = 1;




};


#endif //VIS_ADOS_I7_H264TOMP4_H
