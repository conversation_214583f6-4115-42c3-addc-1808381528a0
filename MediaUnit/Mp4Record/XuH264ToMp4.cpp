//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/21.
//



#include "XuH264ToMp4.h"



#include <stdio.h>
#include <string.h>

XuH264ToMp4::XuH264ToMp4() {

}

XuH264ToMp4::~XuH264ToMp4() {}

int XuH264ToMp4::create_and_open_file(const char *fileName) {
    int ret;
    pHandle = MP4Create(fileName, 0);
    if (pHandle == MP4_INVALID_FILE_HANDLE) {
        ret = -1;
    } else {
        if (MP4SetTimeScale(pHandle, timeScale)) {
            ret = 0;
        } else {
            ret = -1;
        }
    }
    return ret;

}

int XuH264ToMp4::close_file() {
    int ret = -1;
    if (pHandle != nullptr) {
        MP4Close(pHandle, 0);
        pHandle = nullptr;
        addStream = 1;
        ret = 0;
    } else {
        ret = -1;
    }
    return ret;
}

bool XuH264ToMp4::write_packet(uint8_t *h264Data, int len) {

    bool ret = false;
    if (len > 0) {
        naluType = h264Data[4] & 0x1F;
        switch (naluType) {
            case 0x07: {// SPS
//            printf("------------------------------------\n");
//            printf("sps(%d)\n", len);
                if (addStream) {
                    videoId = MP4AddH264VideoTrack
                            (pHandle,
                             timeScale,              // 一秒钟多少timescale
                             timeScale / frameRate,    // 每个帧有多少个timescale
                             width,                  // width
                             height,                 // height
                             h264Data[5],               // sps[1] AVCProfileIndication
                             h264Data[6],               // sps[2] profile_compat
                             h264Data[7],               // sps[3] AVCLevelIndication
                             3);                     // 4 bytes length before each NAL unit
                    if (videoId == MP4_INVALID_TRACK_ID) {
                        return false;
                    }

                    MP4SetVideoProfileLevel(pHandle, 0x7F);

                    addStream = 0;
                }

                MP4AddH264SequenceParameterSet(pHandle, videoId, h264Data + 4, len - 4);
                ret = true;

            }
                break;

            case 0x08: { // PPS
//            printf("pps(%d)\n", len-4);
                MP4AddH264PictureParameterSet(pHandle, videoId, h264Data + 4, len - 4);
                ret = true;

            }
                break;

            default: {
//            printf("slice(%d)\n", len-4);

                int nalLen = len - 4;
                h264Data[0] = ((nalLen) >> 24) & 0xFF;
                h264Data[1] = ((nalLen) >> 16) & 0xFF;
                h264Data[2] = ((nalLen) >> 8) & 0xFF;
                h264Data[3] = ((nalLen) >> 0) & 0xFF;
                ret = MP4WriteSample(pHandle, videoId, h264Data, len, MP4_INVALID_DURATION, 0, 1);
            }
                break;
        }
    }
    return ret;


}



