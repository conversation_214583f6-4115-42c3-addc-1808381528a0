//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/24.
//

#ifndef VIS_G3_SOFTWARE_MULTIMEDIADATACALLBACK_H
#define VIS_G3_SOFTWARE_MULTIMEDIADATACALLBACK_H

#include "CameraYUVData.h"
#include "H264Encodec/CameraH264Data.h"
#include "Mp4Record/Mp4FileInfo.h"
#include "JPGFileInfo.h"

class MultimediaDataCallback {
public:
    MultimediaDataCallback();

    /**
     * 当获取到YUV数据时的回调
     *
     * @param cameraData ： YUV数据的封装对象
     *
     * @return 结果 ：    0：成功  其他：失败
     * */
    virtual int onCameraYUVGet(CameraYUVData &cameraData);

    /**
     * 当获取到H264数据时的回调
      *
      * @param cameraH264Data ： H264数据的封装对象
      *
      * @return 结果 ：    0：成功  其他：失败
      * */
    virtual int onCameraH264Get(const CameraH264Data &cameraH264Data);

    /**
     * 当有新生产的MP4文件时的回调
     *
     * @param mp4FileInfo ： MP4文件的信息
     *
     * @return 结果 ：    0：成功  其他：失败
     * */
    virtual int onMp4FileGet(const Mp4FileInfo &mp4FileInfo);

    /**
     * 当有用于显示的YUV数据的回调
     *
     * @param cameraData ： YUV数据的封装对象
     *
     * @return 结果 ：    0：成功  其他：失败
     * */
    virtual int onShowYUVGet(CameraYUVData &cameraData);

    /**
     * 当有用于算法的YUV数据的回调
     *
     * @param cameraData ： YUV数据的封装对象
     *
     * @return 结果 ：    0：成功  其他：失败
     * */
    virtual int onDetectionYUVGet(CameraYUVData &cameraData);

    /**
     * 当有新生产的JPG文件时的回调
     *
     * @param jpgFileInfo ： jpg文件的信息
     *
     * @return 结果 ：    0：成功  其他：失败
     * */
    virtual int onJpgFileGet(const JPGFileInfo &jpgFileInfo);


};


#endif //VIS_G3_SOFTWARE_MULTIMEDIADATACALLBACK_H
