//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/22.
//

#ifndef G3SOFTWARE_XUCAMERAUTILS_H
#define G3SOFTWARE_XUCAMERAUTILS_H

#include <assert.h>
#include <fcntl.h>
#include <getopt.h>
#include <pthread.h>
#include <unistd.h>
#include <cstdint>
#include "rkmedia_api.h"

#include <linux/videodev2.h>
#include <sys/mman.h>
#include <cstring>
#include "CamCap.h"
#include "G3_Configuration.h"

#include <Poco/Runnable.h>
#include <Poco/ThreadPool.h>


class XuCameraUtils : public Poco::Runnable {

public:
    /**
     * 打开相机并初始化
     *
     * @param cameraId ： 相机ID
     * @param width ： 宽
     * @param height ： 高
     *
     * @return 结果 ：   0：成功   其他：失败
     *
     * */
    int openCamera(int cameraId, int width, int height);


    /**
     * 获取相机的YUV图像数据
     *
     * @param yuvBuf ： 存放YUV图像的内存
     * @param len : 存放YUV图像的内存的长度
     *
     * @return 结果 ：   获取到的图像数据的大小
     *
     * */
    int getCameraYUV(uint8_t *yuvBuf, const int len);
    /**
     * 关闭相机（打开之后一定要关闭）
     *
     * */
    void closeCamera();

    void start();

    void run() override;


private:

    int cvbsDataMix(uint8_t *cvbs, uint8_t  curCameraInputSignalType, uint8_t *decBuf, int width, int height);

    /**
     * 获取相机的YUV图像数据
     *
     * @param yuvBuf ： 存放YUV图像的内存
     * @param len : 存放YUV图像的内存的长度
     *
     * @return 结果 ：   获取到的图像数据的大小
     *
     * */
    int getCameraImag(uint8_t *yuvBuf, const int len);


    /* 当前使用的相机ID */
    int curCameraId = -1;
    /* ISPP视频节点 */
    const RK_CHAR *DEVICENAME_BYPASS = "rkispp_m_bypass";
    const RK_CHAR *DEVICENAME_SCALE0 = "rkispp_scale0";
    const RK_CHAR *DEVICENAME_SCALE1 = "rkispp_scale1";
    const RK_CHAR *DEVICENAME_SCALE2 = "rkispp_scale2";




    /* V4L2打开相机的工具类 */
    CamCap cap;


    bool needToStopCamera = true;
    /* 当前的相机输入信号类型 */
    uint8_t  curCameraInputSignalType = CAMERA_INPUT_TYPE_NONE;




    /* 使用的线程类 */
    Poco::Thread cameraUtilsThread;
    /* 使用的线程类 */
    Poco::Thread camCapThread;
    /* 用来存放拿到的YUV数据的指针 */
    uint8_t *yudData_ahd;

    XuRGAUtils rgaUtils;

    /* 用来带黑边的图像的指针 */
    uint8_t *yudData_cvbs;

    /* 黑色的图像 */
    uint8_t *yudData_black;

    /* 外面获取一帧YUV数据的图像应该有多大的内存 */
    int yuvBufLen = -1;

    /* 请求图像的宽高 */
    int imgWidth = 1280;
    int imgHeight = 720;

    std::mutex cpyYUVLock;

    /* 镜头是否有连接 */
    bool cameraIsConnected = false;


};


#endif //G3SOFTWARE_XUCAMERAUTILS_H
