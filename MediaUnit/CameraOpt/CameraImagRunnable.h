//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/25.
//

#ifndef VIS_G3_SOFTWARE_CAMERAIMAGRUNNABLE_H
#define VIS_G3_SOFTWARE_CAMERAIMAGRUNNABLE_H

#include <Poco/Runnable.h>
#include <XuYUVDataOpt.h>
#include "MultimediaDataCallback.h"
#include "utils/XuTimeUtil.h"
#include <XuString.h>
#include "G3_Configuration.h"
#include "G3DetectionDefind.h"
#include "tdShareDefine.h"
#include "gtShareDefine.h"
#include "dasShareDefine.h"
#include "myparams.h"
#include <mutex>
#include "XuRGAUtils.h"
#include "DEKOMAVTPCameraCallback.h"
#include "XuDecoder.h"

class CameraImagRunnable : public Poco::Runnable, public DEKOMAVTPCameraCallback{
public:
    /* 把原始的BSD识别信息跟整理好的识别信息放到一起，用以区分算法决策类型 */
    struct BSDDetectionInfoAll{
        td::objectInfo_t curobjectInfo;
        G3DetectionResult detectionResult;
    };


    /* 把原始的ADAS识别信息跟整理好的识别信息放到一起，用以区分算法决策类型 */
    struct ADASDetectionInfoAll{
        das::objectInfo_t curobjectInfo;
        G3DetectionResult detectionResult;
    };



    void run() override;

    void init(int cameraId, int width, int height, MultimediaDataCallback &mainUnitInterface);

    bool isCameraopened() const;

    uint64_t getLastGetYuvDataTime() const;

    /**
     * 在YUV上画识别框
     *
     * @param cameraYuvData : YUV数据
     * @param len : YUV数据的长度
     * */
    void drawYUV(uint8_t *yuvdata, int len);

    void drawTestInfo(uint8_t *yuvdata, int len);

    void drawTestInfoByV6(uint8_t *yuvdata, int len);

    void drawTestInfoByG4MINI(uint8_t *yuvdata, int len);

    /**
     * 在YUV上画BSD的识别信息
     *
     * @param cameraYuvData : YUV数据
     * @param len : YUV数据的长度
     * */
    void drawBSDInfo(uint8_t *yuvdata, int len);

    /**
     * 在YUV上画DSM的识别信息
     *
     * @param cameraYuvData : YUV数据
     * @param len : YUV数据的长度
     * */
    void drawDSMInfo(uint8_t *yuvdata, int len);

    /**
     * 在YUV上画手势的识别信息
     *
     * @param cameraYuvData : YUV数据
     * @param len : YUV数据的长度
     * */
    void drawGESInfo(uint8_t *yuvdata, int len);

    /**
     * 在YUV上画ADAS的识别信息
     *
     * @param cameraYuvData : YUV数据
     * @param len : YUV数据的长度
     * */
    void drawADASInfo(uint8_t *yuvdata, int len);


     /**
      * 在YUV上画maskbuf的信息
      *
      * @param maskbuf : maskbuf数据
      * @param maskbufLen : maskbuf数据的长度
      * @param yuvdata : YUV数据
      * @param yuvlen : YUV数据的长度
      */
    void drawMaskBuf(uint8_t *maskbuf,const int maskbufLen,uint8_t *yuvdata, const int yuvlen);

    /**
     * 设置从识别模块拿到的识别信息(这里使用copy函数，测试下vector深拷贝的效果)
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     *
     * */
    void setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);


    /**
     * 设置从识别模块拿到的报警事件
     *
     * @param cameraId : 相机ID
     * @param detectType : 识别算法类型
     * @param eventCode ： 事件码
     *
     * */
    void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);

    /**
     * 设置从识别模块拿到的识别信息(DSM)
     *
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDSMInfo(G3DetectionResult &detectionResult);

    /**
     * 设置从识别模块拿到的识别信息
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectionInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

    /**
     * 设置从识别模块拿到的识别信息(Adas)
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

    /**
    * 设置车速
    *
    * @param speed : 速度  （单位：KM\H）
    * @param baseOf ： 来源    （0：CAN  1：UART  2：GPS）
    *
    * @return 无
    * */
    void setSpeed(const float speed, const int baseOf);


    int getStatusUart() const;

    void setStatusUart(int statusUart);

    int getStatusRs485() const;

    void setStatusRs485(int statusRs485);

    const char *getVoltageStr() const;

    void setVoltageStr(float voltage);

    const char *getTemperatureCpuStr() const;

    void setTemperature_CPU(float temperature);

    int getStatusTf() const;

    void setStatusTf(int statusTf);

    int getStatusSensor() const;

    void setStatusSensor(int statusSensor);

    int getTcpClient() const;

    void setTcpClient(int tcpClient);

    int getUdpBroadcast() const;

    void setUdpBroadcast(int udpBroadcast);

    void setMcuversion(char *version, int len);

    const char *getCpuSerialStr() const;

    void setCPUSerial(const char *cpuSerial, const int len);

    void setIoValue110(int ioValue110);

    void setIoValue111(int ioValue111);

    void setIoValue113(int ioValue113);

    void setIoValue115(int ioValue115);

    void setG3SoftwardVersion(const char *version, const int len);

    bool isIsptm() const;

    void setIsptm(bool isptm);

    void setPhotoresistorValue(float photoresistorValue);
    /**
     * 设置红外灯的开关状态
     *
     * @param lampSwich ： 红外灯的开关  ture：打开  false：关闭
     */
    void setIRLampSwichStatus(const bool lampSwich);

    /**
     * 把VISPoint的vector转成YUVDataOpt_Point数组
     * @param visPointList ： VISPoint的vector
     * @param yuvDataOpt_PointList ： YUVDataOpt_Point数组
     * @param len ： YUVDataOpt_Point数组长度
     *
     * @return 0：成功  其他：失败
     */
    int visPointListToYUVDataOpt_PointList(const std::vector<VISPoint> &visPointList,YUVDataOpt_Point *yuvDataOpt_PointList,const int len);

    void setCANWorkStatus(bool isWork);

    bool isNeedMirror() const;

    void setNeedMirror(bool needMirror);

    int onDEKOMIPCH264Get(const CameraH264Data &cameraH264Data) override;

private:
    /* 小松的CVBS镜头的分辨率 */
    const int KOM_CAMERA_IMG_WIDTH = 720;
    const int KOM_CAMERA_IMG_HEIGHT = 576;



    /* 是否需要停止线程 */
    bool needStop = true;
    /* 数据来源的相机的ID */
    int curCameraId = 0;
    /* 相机对应的安装信息和算法类型 */
    CameraType curCameraType = {};
    /* 图像的宽 */
    int imagWidth;
    /* 图像的高 */
    int imagHeight;
    MultimediaDataCallback *curMainUnitInterface;
    /* 当前获取到的YUV数据 */
    CameraYUVData curCameraYuvData;
    /* 当前用来绘画的的YUV数据 */
    CameraYUVData drawYuvData;
    /* 相机是是否已打开 */
    bool cameraopened = false;
    /* 上一帧YUV数据的获取时间 */
    uint64_t lastGetYUVDataTime = 0xFFFFFFFFFFFFFFFF;


    /* 在相机的YUV数据上绘画的工具类 */
    XuYUVDataOpt yuvDataOpt;
    /* 存放时间字符串的内存地址 */
    char timeStr[25];

    int camera1Type = -1;
    int camera2Type = -1;

    /* 行人报警区域 */
    YUVDataOpt_Point pedestrianAlarmAreaList_level1[6];
    YUVDataOpt_Point pedestrianAlarmAreaList_level2[6];
    /* 车辆报警区域 */
    YUVDataOpt_Point vehicleAlarmAreaList_level1[6];
    YUVDataOpt_Point vehicleAlarmAreaList_level2[6];
    /* 不识别区域 */
    YUVDataOpt_Point notdetectionArea1[6];
    YUVDataOpt_Point notdetectionArea2[6];
    /* R151的四个行人报警区域 */
    YUVDataOpt_Point r151PedestrianAlarmArea1[6];
    YUVDataOpt_Point r151PedestrianAlarmArea2[6];
    YUVDataOpt_Point r151PedestrianAlarmArea3[6];
    YUVDataOpt_Point r151PedestrianAlarmArea4[6];




    /* 镜头的图像识别出来的BSD识别信息 */
    std::vector<BSDDetectionInfoAll> curObjectInfo_BSD;



    /* 镜头的图像识别出来的BSD识别信息 */
    std::vector<ADASDetectionInfoAll> curObjectInfo_ADAS;

    /* 操作识别出来的BSD识别信息的互斥锁 */
    std::mutex objectInfo_BSD_lock;

    /* 镜头的图像识别出来的DSM人脸识别信息 */
    G3DSMFaceDetectionInfo dsmFaceDetectionInfo;
    /* 镜头的图像识别出来的DSM安全带识别信息 */
    G3DSMSeatbeltDetectionInfo dsmSeatbeltDetectionInfo;
    /* 操作识别出来的DSM识别信息的互斥锁 */
    std::mutex dsmInfolock;


    /* 镜头的图像识别出来的手势识别信息 */
    gt::objectInfo_t curObjectInfo_Ges;
    /* 操作识别出来的手势识别信息的互斥锁 */
    std::mutex objectInfo_Ges_lock;



    /* 操作识别出来的Adas识别信息的互斥锁 */
    std::mutex objectInfo_Adas_lock;

    float curSpeed = 10;
    int curSpeedBaseOf = 1;

    /* 存放速度字符串的内存地址 */
    char speedStr[7];


    /* 产测模式下需要得信息 */
    bool isptm = false;
    std::string testInfostr;
    int status_uart = 0;
    int status_rs485 = 0;
    char voltageStr[6] = {0x00};
    bool voltageGood = false;
    int ioValue_110 = -1;
    int ioValue_111 = -1;
    int ioValue_113 = -1;
    int ioValue_115 = -1;
    char temperature_cpuStr[6] = {0x00};
    bool temperatureCPUGood = false;
    int status_tf = 1;
    int status_sensor = 1;
    int tcpClient = 0;
    int udpBroadcast = 0;
    char g3SoftwardVersion[10] = {0x00};
    char mcuversion[15] = {0x00};
    char cpuSerialStr[25] = {0x00};
    bool canWorkStatus = false;
    float photoresistorValue = 0;
    bool cameraIRLampSwichEnable = false;

    XuRGAUtils xuRgaUtils;

    cv::Mat tmp_mask_small;
    cv::Mat showimg;
    cv::Mat addImg;
    cv::Mat mask;

    /* YUV图像是否需要镜像 */
    bool needMirror = false;

    /* H264解码工具 */
    XuDecoder decoder;

    FILE *h264Test = nullptr;


    /**
        * 通过V4L2拿出镜头的画面
        */
    void getImageFromCamera();
    /**
     * 通过MP4文件中读出画面
     *
     * @param mp4FilePath ： MP4文件路径
     */
    void getImageFromMp4File(const char *mp4FilePath);
    /**
    * 通过以太网从德国小松的IPC里面获取画面
    */
    void getImageFromDEKOMIPC();


};


#endif //VIS_G3_SOFTWARE_CAMERAIMAGRUNNABLE_H
