//
// Created by xwf on 2021/12/10.
//

#include <fcntl.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>


#include "CamCap.h"
#include "XuTimeUtil.h"
#include "G3_Configuration.h"
#include "XuLog.h"
/**
 * @param deviceNode video节点
 */
CamCap::CamCap(const std::string &deviceNode) {
    setDevice(deviceNode);
}

/**
 * 设置视频节点
 * @param deviceNode video口二道门
 * @return 成功返回文件描述符，失败返回负数
 */
int CamCap::setDevice(const std::string &deviceNode) {
    _deviceNode = deviceNode;
    _cameraInfo.videoFd = open(deviceNode.c_str(), O_RDWR);
    if (_cameraInfo.videoFd < 0) {
        return _cameraInfo.videoFd;
    }

    // 获取指定节点的能力
    memset(&_cameraInfo.cap, 0, sizeof(_cameraInfo.cap));
    if (ioctl(_cameraInfo.videoFd, VIDIOC_QUERYCAP, &_cameraInfo.cap) < 0) {
        return -1;
    }

    return _cameraInfo.videoFd;
}

/**
 * @return 当前节点是否是摄像头
 */
bool CamCap::isCamera() {
    bool ret = false;

    ret = (_cameraInfo.cap.capabilities & (V4L2_CAP_VIDEO_CAPTURE_MPLANE | V4L2_CAP_VIDEO_CAPTURE))
          != 0;

    return ret;
}

/**
 * @return 当前节点是否是多平面摄像头
 */
bool CamCap::isMplaneCamera() {
    return (_cameraInfo.cap.capabilities & V4L2_CAP_VIDEO_CAPTURE_MPLANE) != 0;
}

/**
 * 获取摄像头支持的格式
 * @param fmt 获取结果
 * @return 成功返回0，失败返回-1
 */
int CamCap::getFmt(v4l2_format &fmt) {
    memset(&fmt, 0, sizeof(fmt));

    if (isMplaneCamera()) {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    } else if (isCamera()) {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    } else {
        return -1;
    }

    if (ioctl(_cameraInfo.videoFd, VIDIOC_G_FMT, &fmt)) {
        return -1;
    }

    return 0;
}



/**
 * 设置摄像头格式
 * @param width 摄像头的宽度
 * @param height 摄像头的高度
 * @param fourcc 图像格式，比如V4L2_PIX_FMT_UYVY
 * @return 成功返回0，失败返回负数
 */
int CamCap::setFmt(uint32_t width, uint32_t height, uint32_t fourcc, uint8_t cameraInputSignalType) {

    int ret = 0;
    struct v4l2_format fmt = {0};

    ret = getFmt(fmt);
    if (ret < 0) {

        return ret;
    }

    _width = width;
    _height = height;
    _fourcc = fourcc;
    curCameraInputSignalType = cameraInputSignalType;

    if (isMplaneCamera()) {
        fmt.fmt.pix_mp.width = _width;
        fmt.fmt.pix_mp.height = _height;
        if (fourcc > 0) {
            fmt.fmt.pix_mp.pixelformat = fourcc;
        }
    } else if (isCamera()) {
        fmt.fmt.pix.width = _width;
        fmt.fmt.pix.height = _height;
        if (fourcc > 0) {
            fmt.fmt.pix.pixelformat = fourcc;
        }
    }
    if (ioctl(_cameraInfo.videoFd, VIDIOC_S_FMT, &fmt) < 0) {
        printf("++++++++++++++++++++++ioctl  \n");
        return -1;
    }
    return 0;
}

/**
 * 设置摄像头格式
 * @param fourcc 图像格式，比如V4L2_PIX_FMT_UYVY
 * @return 成功返回0，失败返回负数
 */
int CamCap::setFmt(uint32_t fourcc) {
    int ret = 0;
    struct v4l2_format fmt = {0};

    ret = getFmt(fmt);
    if (ret < 0) {
        return ret;
    }

    _fourcc = fourcc;

    if (isMplaneCamera()) {
        fmt.fmt.pix_mp.pixelformat = fourcc;
    } else if (isCamera()) {
        fmt.fmt.pix.pixelformat = fourcc;
    }

    if (ioctl(_cameraInfo.videoFd, VIDIOC_S_FMT, &fmt) < 0) {
        return -1;
    }

    return 0;
}

/**
 * 内存映射，默认映射3个缓存
 * @return 成功返回0，失败返回-1
 */
int CamCap::memMap() {
    struct v4l2_requestbuffers req = {0};
    struct v4l2_buffer buf = {0};
    struct v4l2_format fmt = {0};

    // 默认3个缓冲
    _cameraInfo.meminfo.resize(6);

    if (isMplaneCamera()) {
        req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    } else if (isCamera()) {
        req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    } else {
        return -1;
    }
    req.count = _cameraInfo.meminfo.size();
    req.memory = V4L2_MEMORY_MMAP;

    // 申请缓存
    if (ioctl(_cameraInfo.videoFd, VIDIOC_REQBUFS, &req) < 0) {
        return -1;
    }

    printf("***********req.count=%d  \n",req.count);

    if (getFmt(fmt) < 0) {
        return -1;
    }

    // 初始化获取每个缓存
    for (std::size_t memIndex = 0; memIndex < req.count; ++memIndex) {
        memset(&buf, 0, sizeof(buf));

        if (isMplaneCamera()) {
            buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
            // 获取到多平面的平面个数
            buf.length = fmt.fmt.pix_mp.num_planes;
            _cameraInfo.meminfo[memIndex].plane.resize(fmt.fmt.pix_mp.num_planes);
            _cameraInfo.meminfo[memIndex].memPoint.resize(fmt.fmt.pix_mp.num_planes);
            buf.m.planes = _cameraInfo.meminfo[memIndex].plane.data();
        } else {
            buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
            _cameraInfo.meminfo[memIndex].memPoint.resize(1);
        }

        buf.index = memIndex;
        buf.memory = V4L2_MEMORY_MMAP;

        // 获取单个缓存信息
        if (ioctl(_cameraInfo.videoFd, VIDIOC_QUERYBUF, &buf) < 0) {
            return -1;
        }

        // 如果是多平面缓存的话
        if (isMplaneCamera()) {
            // 映射每个平面
            for (std::size_t planeIndex = 0; planeIndex < _cameraInfo.meminfo[memIndex].plane.size();
                 ++planeIndex) {
                buf.m.planes = _cameraInfo.meminfo[memIndex].plane.data() + planeIndex;
                // 内存映射
                _cameraInfo.meminfo[memIndex].memPoint[planeIndex].start =
                        mmap(nullptr, buf.m.planes[planeIndex].length, PROT_READ | PROT_WRITE, MAP_SHARED,
                             _cameraInfo.videoFd, buf.m.planes[planeIndex].m.mem_offset);
                if (nullptr == _cameraInfo.meminfo[memIndex].memPoint[planeIndex].start) {
                    return -1;
                }
                // 映射的内存大小
                _cameraInfo.meminfo[memIndex].memPoint[planeIndex].length =
                        buf.m.planes[planeIndex].length;

                // printf(" map buffer index: %d, meminfo: %p, len: %x, offset: %x\n", memIndex,
                //        _cameraInfo.meminfo[memIndex].memPoint[planeIndex].start,
                //        _cameraInfo.meminfo[memIndex].memPoint[planeIndex].length,
                //        buf.m.planes[planeIndex].m.mem_offset);
            }
        } else // 如果是单平面
        {
            _cameraInfo.meminfo[memIndex].memPoint[0].start =
                    mmap(nullptr, buf.length, PROT_WRITE | PROT_READ, MAP_SHARED, _cameraInfo.videoFd,
                         buf.m.offset);
            if (nullptr == _cameraInfo.meminfo[memIndex].memPoint[0].start) {
                return -1;
            }
            _cameraInfo.meminfo[memIndex].memPoint[0].length = buf.length;
        }
    }

    return 0;
}

/**
 * 设置摄像头格式，慎用
 * @param fmt 需要设置的格式
 * @return 成功返回0，失败返回-1
 */
int CamCap::setFmt(struct v4l2_format fmt) {
    if (ioctl(_cameraInfo.videoFd, VIDIOC_S_FMT, &fmt) < 0) {
        return -1;
    }
    return 0;
}

/**
 * 获取摄像头格式
 * @param width 摄像头的宽度
 * @param height 摄像头的高度
 * @param fource 图像的格式
 * @return 成功返回0，失败返回负数
 */
int CamCap::getFmt(uint32_t &width, uint32_t &height, uint32_t &fource) {
    v4l2_format fmt = {0};
    int ret = 0;

    ret = getFmt(fmt);
    if (ret < 0) {
        return ret;
    }

    if (isMplaneCamera()) {
        width = fmt.fmt.pix_mp.width;
        height = fmt.fmt.pix_mp.height;
        fource = fmt.fmt.pix_mp.pixelformat;
    } else if (isCamera()) {
        width = fmt.fmt.pix.width;
        height = fmt.fmt.pix.height;
        fource = fmt.fmt.pix.pixelformat;
    } else {
        return -1;
    }

    return 0;
}

/**
 * 获取摄像头格式
 * @param width 摄像头的宽度
 * @param height 摄像头的高度
 * @return 成功返回0，失败返回负数
 */
int CamCap::getFmt(uint32_t &width, uint32_t &height) {
    uint32_t fource = 0;

    return getFmt(width, height, fource);
}

/**
 * 获取摄像头格式
 * @param fource 图像的格式
 * @return 成功返回0，失败返回负数
 */
int CamCap::getFmt(uint32_t &fource) {
    uint32_t h;
    uint32_t w;

    return getFmt(w, h, fource);
}

/**
 * 开启摄像头的流传输
 * @return 成功返回0，失败返回-1
 */
int CamCap::streamOn() {
    enum v4l2_buf_type type;
    struct v4l2_buffer buf = {0};

    if (memMap() < 0) {
        return -1;
    }

    if (isMplaneCamera()) {
        type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    } else if (isCamera()) {
        type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    } else {
        return -1;
    }

    if (ioctl(_cameraInfo.videoFd, VIDIOC_STREAMON, &type) < 0) {
        return -1;
    }

    // buf入队列
    for (std::size_t memIndex = 0; memIndex < _cameraInfo.meminfo.size(); ++memIndex) {
        if (isMplaneCamera()) {
            for (std::size_t planeIndex = 0; planeIndex < _cameraInfo.meminfo[memIndex].plane.size();
                 planeIndex++) {
                memset(&buf, 0, sizeof(buf));
                buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
                buf.memory = V4L2_MEMORY_MMAP;
                buf.index = memIndex;
                buf.length = _cameraInfo.meminfo[memIndex].memPoint.size();
                buf.m.planes = _cameraInfo.meminfo[memIndex].plane.data();
                if (ioctl(_cameraInfo.videoFd, VIDIOC_QBUF, &buf) < 0) {
                    return -1;
                }
            }
        } else {
            memset(&buf, 0, sizeof(buf));
            buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
            buf.memory = V4L2_MEMORY_MMAP;
            buf.index = memIndex;
            if (ioctl(_cameraInfo.videoFd, VIDIOC_QBUF, &buf) < 0) {
                return -1;
            }
        }
    }

    return 0;
}

/**
 * 停止流的传输
 * @return 成功返回0，失败返回-1
 */
int CamCap::streamOff() {
    enum v4l2_buf_type type;
    struct v4l2_requestbuffers req = {0};

    if (memUnmap() < 0) {
        return -1;
    }

    if (isMplaneCamera()) {
        type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    } else if (isCamera()) {
        type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    } else {
        return -1;
    }

    if (ioctl(_cameraInfo.videoFd, VIDIOC_STREAMOFF, &type) < 0) {
        return -1;
    }

    // 回收内存申请
    if (isMplaneCamera()) {
        req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    } else if (isCamera()) {
        req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    } else {
        return -1;
    }
    req.count = 0;
    req.memory = V4L2_MEMORY_MMAP;

    // 释放缓存
    if (ioctl(_cameraInfo.videoFd, VIDIOC_REQBUFS, &req) < 0) {
        return -1;
    }

    return 0;
}

/**
 * 获取图像数据
 * @param data 获取到的结果
 * @param dataLen data的长度
 * @return 获取到的图像的长度，或失败返回-1
 */



int CamCap::getFmtData(CamCapBufIndex &bufInfo) {


    struct v4l2_plane lane = {0};
    int ret = -1;


    if (isMplaneCamera()) {

        bufInfo.buffer.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
        bufInfo.buffer.length = 1;       // 默认取第0个plane
        bufInfo.buffer.m.planes = &lane; // &lane;

    } else if (isCamera()) {
        bufInfo.buffer.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    }

    bufInfo.buffer.memory = V4L2_MEMORY_MMAP;

    // 出队列   这里有时会阻塞
    if (ioctl(_cameraInfo.videoFd, VIDIOC_DQBUF, &bufInfo.buffer)) {
        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,"CAMERATest") << "ioctl  VIDIOC_DQBUF failed  " << _deviceNode << XU_LOG_END;

        ret = -1;
    }else {
        if (isMplaneCamera()) {

            ret = lane.bytesused;
            bufInfo.yuvDataLen = ret;
            bufInfo.yuvData = _cameraInfo.meminfo[bufInfo.buffer.index].memPoint[bufInfo.buffer.length -1].start;


        } else {
            ret = bufInfo.buffer.bytesused;
            bufInfo.yuvDataLen = ret;
            bufInfo.yuvData = _cameraInfo.meminfo[bufInfo.buffer.index].memPoint[0].start;

        }
    }




//    // 入队列
//    if (ioctl(_cameraInfo.videoFd, VIDIOC_QBUF, &buf)) {
//        return -1;
//    }


    return ret;
}

CamCap::~CamCap() {
    streamOff();
    closeCam();
}

/**
 * 获取摄像头图像
 * @param data 图像结果
 * @param dataLen data的长度
 * @param msec 等待的时间
 * @return 获取到的图像的长度，或超时返回0，失败返回-1
 */
int CamCap::getVideoData(CamCapBufIndex &bufInfo, uint msec) {
    fd_set rFds;
    timeval tv;
    int ret = -1;
    if (msec > 0) {
        /* 有设置超时时间就要用select */
        tv.tv_sec = msec / 1000;
        tv.tv_usec = (msec % 1000) * 1000;
        FD_ZERO(&rFds);
        FD_SET(_cameraInfo.videoFd, &rFds);
        ret = select(_cameraInfo.videoFd + 1, &rFds, nullptr, nullptr, &tv);
        // timeout or fail
        if (ret > 0) {
            if (FD_ISSET(_cameraInfo.videoFd, &rFds)) {
                ret = getFmtData(bufInfo);
            }else{
                ret = -1;
            }
        }
    }else{
        /* 没设置超时时间，直接获取就行了 */
        ret = getFmtData(bufInfo);
    }

    /* 如果ret是-1，那么就重新开一下 */
    if(ret <= 0){
        reStart();
    }


    return ret;
}

/**
 * 关闭摄像头
 * @return 0
 */
int CamCap::closeCam() {
    return close(_cameraInfo.videoFd);
}

/**
 * 重置摄像头，当摄像头出现问题的时候应该要调用该方法重置
 * @return 成功返回0，失败返回负数
 */
int CamCap::reset(uint8_t cameraInputSignalType) {
    int ret = 0;

    ret = streamOff();
    ret |= closeCam();
    ret |= setDevice(_deviceNode);
    ret |= setFmt(_width, _height, _fourcc,cameraInputSignalType);
    ret |= streamOn();

    return ret;
}

/**
 * 接触内存映射
 * @return 0
 */
int CamCap::memUnmap() {
    // 解除内存映射
    for (std::size_t memIndex = 0; memIndex < _cameraInfo.meminfo.size(); ++memIndex) {
        for (std::size_t planeIndex = 0; planeIndex < _cameraInfo.meminfo[memIndex].memPoint.size(); ++planeIndex) {
            munmap(_cameraInfo.meminfo[memIndex].memPoint[planeIndex].start,
                   _cameraInfo.meminfo[memIndex].memPoint[planeIndex].length);
        }
    }
    return 0;
}

int CamCap::closeDevices() {
    printf("i am close %d*****************\n",_cameraInfo.videoFd);
    close(_cameraInfo.videoFd);
    return 0;
}

//int CamCap::cvbsDataMix() {
//    int ret = -1;
//    int dataLen = 0;
//    for (int i = 0; i < (actualHeight + (actualHeight / 2)); i+=2) {
//        /* 先把奇数行复制过去 */
//        memcpy(cvbsData_full + (actualWidth * i),cvbsData_Odd+(actualWidth*(i/2)),actualWidth);
//        /* 再把偶数行复制过去 */
//        memcpy(cvbsData_full + (actualWidth * (i+1)),cvbsData_even+(actualWidth*(i/2)),actualWidth);
//        /* 累计下数据 */
//        dataLen += (actualWidth * 2);
//    }
//    ret = dataLen;
//
//    return ret;
//}

//uint64_t lastwTime = 0;
//int wsum = 0;
//#include "XuTimeUtil.h"

void CamCap::run() {
    std::string threadName = "CamCap_";
    threadName.append(_deviceNode);
    pthread_setname_np(pthread_self(), threadName.c_str());
    needStopCap = false;

    CamCapBufIndex bufInfo1;

    int ret = -1;
    while (!needStopCap){
        switch (curCameraInputSignalType) {
            case CAMERA_INPUT_TYPE_AHD_720P:{
                /* 拿出图像信息 */
                ret = getVideoData(bufInfo1,1000);
                /* 能拿到锁就说明没在copy，可以接着用 */
                if(ret > 0 && cpyYUVLock.try_lock()){

                    if(!bufInfo_ahd.isrelse){
                        releaseBufToCam(bufInfo_ahd);
                    }
                    bufInfo_ahd.yuvData = bufInfo1.yuvData;
                    bufInfo_ahd.yuvDataLen = bufInfo1.yuvDataLen;
                    bufInfo_ahd.buffer = bufInfo1.buffer;
                    bufInfo_ahd.isrelse = false;
                    cpyYUVLock.unlock();
                }else{
                    /* 拿不到锁，说明旧的还没cpy走，直接从小塞回去 */
                    releaseBufToCam(bufInfo1);
                }
            }
                break;

            case CAMERA_INPUT_TYPE_AHD_1080P:{
                /* 拿出图像信息 */
                ret = getVideoData(bufInfo1,1000);
                /* 能拿到锁就说明没在copy，可以接着用 */
                if(ret > 0 && cpyYUVLock.try_lock()){

                    if(!bufInfo_ahd.isrelse){
                        releaseBufToCam(bufInfo_ahd);
                    }
                    bufInfo_ahd.yuvData = bufInfo1.yuvData;
                    bufInfo_ahd.yuvDataLen = bufInfo1.yuvDataLen;
                    bufInfo_ahd.buffer = bufInfo1.buffer;
                    bufInfo_ahd.isrelse = false;
                    cpyYUVLock.unlock();
                }else{
                    /* 拿不到锁，说明旧的还没cpy走，直接从小塞回去 */
                    releaseBufToCam(bufInfo1);
                }
            }
                break;


            case CAMERA_INPUT_TYPE_CVBS_D1_PAL:
            case CAMERA_INPUT_TYPE_CVBS_D1_NTSC:{
                /* 连拿图像 */
                int ret_cvbs = getVideoData(bufInfo1,1000);
                /* 能拿到锁就说明没在copy，可以接着用 */
                if(ret_cvbs > 0 && cpyYUVLock.try_lock()){
                    /* 先看看还了没有，没有就先还 */
                    if(!bufInfo_cvbs.isrelse){
                        releaseBufToCam(bufInfo_cvbs);
                    }
                    /* 指针存起来 */
                    bufInfo_cvbs.yuvData = bufInfo1.yuvData;
                    bufInfo_cvbs.yuvDataLen = bufInfo1.yuvDataLen;
                    bufInfo_cvbs.buffer = bufInfo1.buffer;
                    bufInfo_cvbs.isrelse = false;
                    cpyYUVLock.unlock();
                }else{
                    /* 拿不到锁，说明旧的还没cpy走，直接塞回去 */
                    releaseBufToCam(bufInfo1);
                }
            }
                break;
        }
    }
    pthread_setname_np(pthread_self(), "Finish");

}

int CamCap::releaseBufToCam(CamCapBufIndex &bufInfo) {
    int ret = -1;


        // 入队列
    // 出队列   这里有时会阻塞
//    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,"CAMERATest") << "start ioctl  VIDIOC_QBUF " << _deviceNode << XU_LOG_END;
    if (ioctl(_cameraInfo.videoFd, VIDIOC_QBUF, &bufInfo.buffer)) {
//        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG,"CAMERATest") << "start ioctl  VIDIOC_QBUF  failed " << _deviceNode << XU_LOG_END;
        ret = -1;
    }else{
        bufInfo.isrelse = true;
        ret = 0;
    }

    return ret;
}

int CamCap::getCamData(uint8_t *buf, int bufLen) {
        int ret = -1;
        /* 没有手动设置输入信号类型，就按照自动识别的出来操作 */
        switch (curCameraInputSignalType) {
            case CAMERA_INPUT_TYPE_AHD_720P:{
                /* 先看看是不是有数据 */
                if(bufInfo_ahd.yuvDataLen > 0){
                    /* 加锁 */
                    cpyYUVLock.lock();
                    /* 把内容通过RGA复制出来 */
                    ret = rgaUtils.imageTransformation(_width,_height, XuRGAUtils::IMG_TYPE_UYVY_422,
                                                       static_cast<uint8_t *>(bufInfo_ahd.yuvData), _width, _height,XuRGAUtils::IMG_TYPE_NV21, buf);
                    /* 解锁 */
                    cpyYUVLock.unlock();
                }

            }
                break;

            case CAMERA_INPUT_TYPE_CVBS_D1_PAL:
            case CAMERA_INPUT_TYPE_CVBS_D1_NTSC:{
                /* 先看看是不是有数据 */
                if(bufInfo_cvbs.yuvDataLen > 0 ){
                    /* 加锁 */
                    cpyYUVLock.lock();
                    /* 把CVBS的内容通过RGA复制出来 */
                    ret = rgaUtils.imageTransformation(_width,_height, XuRGAUtils::IMG_TYPE_UYVY_422,
                                                        static_cast<uint8_t *>(bufInfo_cvbs.yuvData), _width, _height,XuRGAUtils::IMG_TYPE_UYVY_422, buf);
                    /* 解锁 */
                    cpyYUVLock.unlock();
                }
            }
                break;

            case CAMERA_INPUT_TYPE_AHD_1080P:{
                /* 先看看是不是有数据 */
                if(bufInfo_ahd.yuvDataLen > 0){
                    /* 加锁 */
                    cpyYUVLock.lock();


                    /* 把内容通过RGA复制出来，同时缩放成720P */
                    ret = rgaUtils.imageTransformation(_width,_height, XuRGAUtils::IMG_TYPE_UYVY_422,
                                                       static_cast<uint8_t *>(bufInfo_ahd.yuvData), 1280, 720,XuRGAUtils::IMG_TYPE_NV21, buf);


                    /* 解锁 */
                    cpyYUVLock.unlock();
                }

            }
                break;

        }
    return ret;
}

int CamCap::reStart() {
    streamOff();
    closeDevices();
    bufInfo_ahd.yuvDataLen = -1;
    bufInfo_ahd.isrelse = true;
    bufInfo_cvbs.yuvDataLen = -1;
    bufInfo_cvbs.isrelse = true;
    sleep(1);
    setDevice(_deviceNode);
    setFmt(_width,_height,_fourcc,curCameraInputSignalType);
    streamOn();
    return 0;
}

