//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/25.
//


#include "CameraImagRunnable.h"
#include "XuCameraUtils.h"
#include "Point2D.h"
#include "XuFile.h"
#include "CodeUtils.h"
#include "MP4ToYUV.h"
#include "XuFile.h"
#include "XuLog.h"
#include "XuTimeUtil.h"
#include "DEKOMAVTPCameraImageGet.h"
#include "DEKOMAVTPCameraOpt.h"
#include "XuMemGeter.h"


void CameraImagRunnable::init(int cameraId, int width, int height, MultimediaDataCallback &mainUnitInterface) {
    curMainUnitInterface = &mainUnitInterface;
    curCameraId = cameraId;
    G3_Configuration::getInstance().getCameraTypeInfoOfCamera(curCameraType, curCameraId);
    imagWidth = width;
    imagHeight = height;
    curCameraYuvData.setCameraId(cameraId);
    curCameraYuvData.setWidth(width);
    curCameraYuvData.setHeight(height);

    drawYuvData.setCameraId(cameraId);
    drawYuvData.setWidth(width);
    drawYuvData.setHeight(height);


    mask = cv::Mat(DAS_MASK_HEIGHT, DAS_MASK_WIDTH, CV_8UC1);
    addImg = cv::Mat(imagHeight, imagWidth, CV_8UC3);
    showimg = cv::Mat(imagHeight, imagWidth, CV_8UC3);
    tmp_mask_small = cv::Mat(90, 160, CV_8UC3);

    needStop = false;
}

void CameraImagRunnable::run() {
    /* 由于memcpy性能的波动，有时候memcpy1382400个字节的时间要20ms，这样就会导致丢帧，所以改成了传递指针，这样的话逻辑就比较复杂，兼容性和可读性也差，但是没办法，瑞芯微的人不肯修复这个问题。 */


    /* 设置一下线程名 */
    std::string pthreadName = "CameraImag_";
    pthreadName.append(std::to_string(curCameraId));
    pthread_setname_np(pthread_self(), pthreadName.c_str());


    /* 判断一下需要从哪里获取镜头图像 */
    switch (G3_Configuration::getInstance().getCameraImageSource()) {
        case CAMERA_IMAGE_SOURCE_ANALOG_SIGNAL_CAMERA:{
            /* 从模拟信号镜头获取图像 */
            getImageFromCamera();
        }
            break;
        case CAMERA_IMAGE_SOURCE_MP4FILE:{
            /* 从MP4文件获取图像 */
            std::string mp4FilePath;
            switch (curCameraId) {
                case CAMERA_ID_1:{
                    mp4FilePath = "/userdata/media/camera1.mp4";
                }
                    break;
                case CAMERA_ID_2:{
                    mp4FilePath = "/userdata/media/camera2.mp4";
                }
                    break;
                case CAMERA_ID_3:{
                    mp4FilePath = "/userdata/media/camera3.mp4";
                }
                    break;
                case CAMERA_ID_4:{
                    mp4FilePath = "/userdata/media/camera4.mp4";
                }
                    break;
            }
            /* 读出图像 */
            if(!mp4FilePath.empty()){
                /* 从MP4文件里面读 */
                getImageFromMp4File(mp4FilePath.c_str());
            }
        }
            break;
        case CAMERA_IMAGE_SOURCE_DE_KOM_AVTP_IPC:{
            getImageFromDEKOMIPC();
        }
            break;
    }
    pthread_setname_np(pthread_self(), "Finish");
}

bool CameraImagRunnable::isCameraopened() const {
    return cameraopened;
}

uint64_t CameraImagRunnable::getLastGetYuvDataTime() const {
    return lastGetYUVDataTime;
}

void CameraImagRunnable::drawYUV(uint8_t *yuvdata, int len) {

    /* 如果镜头对应的报警决策里面有R158、R159的报警决策，那么统一在UI模块进行绘画 */
    if (curCameraType.adType.pedestrian_r158 || curCameraType.adType.vehicle_r158 ||
        curCameraType.adType.pedestrian_r159 || curCameraType.adType.vehicle_r159) {
        return;
    }

    /* 看看是否需要叠加时间水印 */
    bool canAddTimeOsd = false;
    switch (curCameraId) {
        case CAMERA_ID_1:{
            canAddTimeOsd = G3_Configuration::getInstance().getCameraTimeOsdAdd().camera1;
        }
            break;
        case CAMERA_ID_2:{
            canAddTimeOsd = G3_Configuration::getInstance().getCameraTimeOsdAdd().camera2;
        }
            break;
        case CAMERA_ID_3:{
            canAddTimeOsd = G3_Configuration::getInstance().getCameraTimeOsdAdd().camera3;
        }
            break;
        case CAMERA_ID_4:{
            canAddTimeOsd = G3_Configuration::getInstance().getCameraTimeOsdAdd().camera4;
        }
            break;
    }

    /* 需要叠加时间水印的时候才叠加 */
    if(canAddTimeOsd){
        /* 叠加时间戳 R151的时间戳要特殊处理 */
        if (G3_Configuration::getInstance().getG3DisplayMode() == G3_DISPALY_MODE_R151) {
            XuString::getInstance().getStrFormTime(timeStr, 25, "%d-%m-%Y %H:%M:%S", false);
            yuvDataOpt.drawToNV21(50, 50, 900, 700, -1, -1, -1, -1, timeStr, imagWidth,
                                  imagHeight, yuvdata,
                                  XuYUVDataOpt::COLOR_WHITE,
                                  XuYUVDataOpt::TYPE_ONLY_TEXT);
        } else {
            XuString::getInstance().getStrFormTime(timeStr, 25, "%Y-%m-%d %H:%M:%S", false);
            yuvDataOpt.drawToNV21(50, 50, 900, 700, -1, -1, -1, -1, timeStr, imagWidth,
                                  imagHeight, yuvdata,
                                  XuYUVDataOpt::COLOR_WHITE,
                                  XuYUVDataOpt::TYPE_ONLY_TEXT);
        }

    }


    /* 画报警区域 */
    if (G3_Configuration::getInstance().getBsdAddDetectionInfo()) {
        /* 先画个速度 */
        sprintf(speedStr, "%.2lf", curSpeed);
        int speedColor = XuYUVDataOpt::COLOR_WHITE;
        switch (curSpeedBaseOf) {
            case SPEEDFROM_ETH: {
                speedColor = XuYUVDataOpt::COLOR_YELLOW;
            }
                break;
            case SPEEDFROM_GPS: {
                speedColor = XuYUVDataOpt::COLOR_LIGHT_PURPLE;
            }
                break;

        }
        yuvDataOpt.drawToNV21(1000, 70, 1200, 700, -1, -1, -1, -1, speedStr, imagWidth,
                              imagHeight, yuvdata,
                              speedColor,
                              XuYUVDataOpt::TYPE_ONLY_TEXT);

        yuvDataOpt.drawToNV21(1100, 70, 1200, 700, -1, -1, -1, -1, "KM/H", imagWidth,
                              imagHeight, yuvdata,
                              speedColor,
                              XuYUVDataOpt::TYPE_ONLY_TEXT);

        CameraType cameraType_camera;
        G3_Configuration::getInstance().getCameraTypeInfoOfCamera(cameraType_camera, curCameraId);
        switch (cameraType_camera.algType) {
            case ALGORITHM_TYPE_NOT: {
                /* 不启动算法 */

            }
                break;
            case ALGORITHM_TYPE_SBST_160_OBJECT_DETECTION:
                /* 新加坡-160度相机-目标检测 */
            case ALGORITHM_TYPE_UK_BSD_OBJECT_DETECTION:
                /* 英国工程机械-BSD相机-目标检测 */
            case ALGORITHM_TYPE_DE_BSD_OBJECT_DETECTION:
                /* 德国公交车-BSD相机-目标检测 */
            case ALGORITHM_TYPE_KOM_BSD_OBJECT_DETECTION:
                /* 小松-BSD相机-目标检测 */
            case ALGORITHM_TYPE_FORKLIFT_160_OBJECT_DETECTION:{
                /* 叉车-160度镜头-目标检测 */
                drawBSDInfo(yuvdata, len);
            }
                break;

            case ALGORITHM_TYPE_SBST_160_SEMANTIC_SEGMENTATION: {
                /* 新加坡巴士-160度相机-语义分割 */
                drawBSDInfo(yuvdata, len);
            }
                break;

            case ALGORITHM_TYPE_DSM_METRO:
            case ALGORITHM_TYPE_DSM_MUV:
            case ALGORITHM_TYPE_DSM_NORMAL:
            case ALGORITHM_TYPE_DSM_FB: {
                /* 地铁DSM和MUV的DSM还有标准的DSM*/
                drawDSMInfo(yuvdata, len);

            }
                break;

            case ALGORITHM_TYPE_GESTURE_TJ_METRO: {
                /* 天津地铁手势 */
                drawGESInfo(yuvdata, len);
            }
                break;

            case ALGORITHM_TYPE_ADAS_160_NORMAL:
            case ALGORITHM_TYPE_ADAS_60_NORMAL:
            case ALGORITHM_TYPE_ADAS_160_SBST:{
                /* ADAS-160度镜头 */
                drawADASInfo(yuvdata, len);
            }
                break;

            case ALGORITHM_TYPE_UK_DD_QRCODE:{
                /* 英国DD的二维码识别 */
                drawADASInfo(yuvdata, len);
            }
                break;
        }


    }



    /* 叠加下测试信息 */
    if (isptm) {
        switch (G3_Configuration::getInstance().getG3DeviceWorkingMode()) {
            case G3WORKMODE_V6:{
                /* V6模式下有自己的画法 */
                drawTestInfoByV6(yuvdata, len);
            }
                break;
            case G3WORKMODE_G4MINI:{
                /* G4MINI模式下有自己的画法 */
                drawTestInfoByG4MINI(yuvdata, len);
            }
                break;

            default:{
                /* 正常画 */
                drawTestInfo(yuvdata, len);
            }
                break;
        }


    }


}


void CameraImagRunnable::setSpeed(const float speed, const int baseOf) {
    if(speed >= 0){
        curSpeed = speed;
        curSpeedBaseOf = baseOf;
    }

}

void CameraImagRunnable::drawTestInfo(uint8_t *yuvdata, int len) {


    testInfostr.append("UART:    ");
    testInfostr.append(((status_uart == 1) ? "YES" : ((status_uart == 0) ? "NO" : "UNKNOW")));

    yuvDataOpt.drawToNV21(50, 200, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("RS485:   ");
    testInfostr.append(((status_rs485 == 1) ? "YES" : ((status_rs485 == 0) ? "NO" : "UNKNOW")));

    yuvDataOpt.drawToNV21(50, 250, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("RS232:   ");
    testInfostr.append("NO");
    yuvDataOpt.drawToNV21(50, 300, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("Voltage: ");
    testInfostr.append(voltageGood ? "YES" : "NO");
    yuvDataOpt.drawToNV21(50, 350, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();



    testInfostr.append("IO_110:  ");
    testInfostr.append((ioValue_110 == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(50, 400, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("IO_111:  ");
    testInfostr.append(((ioValue_111 == 1) ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(50, 450, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("IO_113:  ");
    testInfostr.append((ioValue_113 == 1) ? "YES" : "NO");
    yuvDataOpt.drawToNV21(50, 500, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("IO_115:  ");
    testInfostr.append((ioValue_115 == 1) ? "YES" : "NO");
    yuvDataOpt.drawToNV21(50, 550, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("CANWorkStatus:  ");
    testInfostr.append(canWorkStatus ? "YES" : "NO");
    yuvDataOpt.drawToNV21(50, 600, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("CPUTemperature: ");
    testInfostr.append(temperatureCPUGood ? "YES" : "NO");
    yuvDataOpt.drawToNV21(500, 200, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("TFCard:         ");
    testInfostr.append((status_tf == 1 ? "YES" : (status_tf == 2 ? "NO" : "Error")));
    yuvDataOpt.drawToNV21(500, 250, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("G-sensor:       ");
    testInfostr.append((status_sensor == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(500, 300, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("TCP:            ");
    testInfostr.append((tcpClient == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(500, 350, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("UDP Broadcast:  ");
    testInfostr.append((udpBroadcast == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(500, 400, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("CPU:           ");
    testInfostr.append(cpuSerialStr);
    yuvDataOpt.drawToNV21(500, 450, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("SoftwareVer:    ");
    testInfostr.append(g3SoftwardVersion);
    yuvDataOpt.drawToNV21(500, 500, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("MCUVersion:     ");
    testInfostr.append(mcuversion);
    yuvDataOpt.drawToNV21(500, 550, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    /* 画固件版本号 */
    testInfostr.append("FirmwareVer:    ");
    testInfostr.append(getenv("G3_FIRMWARE_VERSION"));


    yuvDataOpt.drawToNV21(500, 600, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

}

int CameraImagRunnable::getStatusUart() const {
    return status_uart;
}

void CameraImagRunnable::setStatusUart(int statusUart) {
    status_uart = statusUart;
}


const char *CameraImagRunnable::getVoltageStr() const {
    return voltageStr;
}


const char *CameraImagRunnable::getTemperatureCpuStr() const {
    return temperature_cpuStr;
}

int CameraImagRunnable::getStatusTf() const {
    return status_tf;
}

void CameraImagRunnable::setStatusTf(int statusTf) {
    status_tf = statusTf;
}

int CameraImagRunnable::getStatusSensor() const {
    return status_sensor;
}

void CameraImagRunnable::setStatusSensor(int statusSensor) {
    status_sensor = statusSensor;
}

int CameraImagRunnable::getTcpClient() const {
    return tcpClient;
}

void CameraImagRunnable::setTcpClient(int tcpClient) {
    CameraImagRunnable::tcpClient = tcpClient;
}

int CameraImagRunnable::getUdpBroadcast() const {
    return udpBroadcast;
}

void CameraImagRunnable::setUdpBroadcast(int udpBroadcast) {
    CameraImagRunnable::udpBroadcast = udpBroadcast;
}

void CameraImagRunnable::setMcuversion(char *version, int len) {
    if (len > 0) {
        memcpy(mcuversion, version, len);
    }

}

const char *CameraImagRunnable::getCpuSerialStr() const {
    return cpuSerialStr;
}

void CameraImagRunnable::setCPUSerial(const char *cpuSerial, const int len) {
    if (len > 0) {
        memcpy(cpuSerialStr, cpuSerial, len);
    }
}

void CameraImagRunnable::setVoltageStr(float voltage) {
    sprintf(static_cast<char *>(voltageStr), "%.02f", voltage);
    if (voltage < 14 && voltage > 12) {
        voltageGood = true;
    } else {
        voltageGood = false;
    }


}

void CameraImagRunnable::setIoValue110(int ioValue110) {
    ioValue_110 = ioValue110;
}

void CameraImagRunnable::setIoValue111(int ioValue111) {
    ioValue_111 = ioValue111;
}

void CameraImagRunnable::setIoValue113(int ioValue113) {
    ioValue_113 = ioValue113;
}

void CameraImagRunnable::setIoValue115(int ioValue115) {
    ioValue_115 = ioValue115;
}

void CameraImagRunnable::setTemperature_CPU(float temperature) {
    sprintf(temperature_cpuStr, "%.2f", temperature);
    if (temperature < 95) {
        temperatureCPUGood = true;
    } else {
        temperatureCPUGood = false;
    }
}

void CameraImagRunnable::setG3SoftwardVersion(const char *version, const int len) {
    if (len > 0) {
        memcpy(g3SoftwardVersion, version, len);
    }

}

int CameraImagRunnable::getStatusRs485() const {
    return status_rs485;
}

void CameraImagRunnable::setStatusRs485(int statusRs485) {
    status_rs485 = statusRs485;
}

bool CameraImagRunnable::isIsptm() const {
    return isptm;
}

void CameraImagRunnable::setIsptm(bool isptm) {
    CameraImagRunnable::isptm = isptm;
}

void CameraImagRunnable::setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
    objectInfo_BSD_lock.lock();
    if (!curObjectInfo_BSD.empty()) {
        /* 先看看是否在vector里面了 */
        bool isInVectot = false;
        for (int i = 0; i < static_cast<int>(curObjectInfo_BSD.size()); i++) {
            if (curObjectInfo_BSD[i].detectionResult.alarmDecisionType == detectionResult.alarmDecisionType) {
                isInVectot = true;
                curObjectInfo_BSD[i].curobjectInfo.objects.clear();
                copy(objectInfo.objects.begin(), objectInfo.objects.end(),
                     inserter(curObjectInfo_BSD[i].curobjectInfo.objects,
                              curObjectInfo_BSD[i].curobjectInfo.objects.begin()));
            }
        }
        /* 如果没在vector里面就加一个 */
        if (!isInVectot) {
            BSDDetectionInfoAll temp;

            copy(temp.curobjectInfo.objects.begin(), temp.curobjectInfo.objects.end(),
                 inserter(objectInfo.objects, objectInfo.objects.begin()));
            /* 由于结构体里面没有裸指针，故直接用=就好了 */
            temp.detectionResult = detectionResult;
            curObjectInfo_BSD.push_back(temp);
        }


    } else {
        BSDDetectionInfoAll temp;
        copy(temp.curobjectInfo.objects.begin(), temp.curobjectInfo.objects.end(),
             inserter(objectInfo.objects, objectInfo.objects.begin()));
        /* 由于结构体里面没有裸指针，故直接用=就好了 */
        temp.detectionResult = detectionResult;
        curObjectInfo_BSD.push_back(temp);
    }
    objectInfo_BSD_lock.unlock();
}

void CameraImagRunnable::setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo) {

}

void CameraImagRunnable::setDSMInfo(G3DetectionResult &detectionResult) {
    dsmInfolock.lock();
    /* 设置一下人脸的信息 */
    dsmFaceDetectionInfo.face_point.clear();
    for (int i = 0; i < (int) detectionResult.faceList[0].face_point.size(); i++) {
        dsmFaceDetectionInfo.face_point.push_back(detectionResult.faceList[0].face_point[i]);
    }
    dsmFaceDetectionInfo.five_point.clear();
    for (int i = 0; i < (int) detectionResult.faceList[0].five_point.size(); i++) {
        dsmFaceDetectionInfo.five_point.push_back(detectionResult.faceList[0].five_point[i]);
    }
    dsmFaceDetectionInfo.face_score = detectionResult.faceList[0].face_score;
    dsmFaceDetectionInfo.isSameDriver = detectionResult.faceList[0].isSameDriver;
    dsmFaceDetectionInfo.respirator_alarm = detectionResult.faceList[0].respirator_alarm;
    dsmFaceDetectionInfo.eye_alarm = detectionResult.faceList[0].eye_alarm;
    dsmFaceDetectionInfo.mouth_alarm = detectionResult.faceList[0].mouth_alarm;
    dsmFaceDetectionInfo.lookaround_alarm = detectionResult.faceList[0].lookaround_alarm;
    dsmFaceDetectionInfo.facemissing_alarm = detectionResult.faceList[0].facemissing_alarm;
    dsmFaceDetectionInfo.camcover_alarm = detectionResult.faceList[0].camcover_alarm;
    dsmFaceDetectionInfo.smoking_alarm = detectionResult.faceList[0].smoking_alarm;
    dsmFaceDetectionInfo.phone_alarm = detectionResult.faceList[0].phone_alarm;
    dsmFaceDetectionInfo.fatigue_rank = detectionResult.faceList[0].fatigue_rank;
    dsmFaceDetectionInfo.face_angle = detectionResult.faceList[0].face_angle;
    dsmFaceDetectionInfo.Blindspot_alarm = detectionResult.faceList[0].Blindspot_alarm;
    dsmFaceDetectionInfo.hat_alarm = detectionResult.faceList[0].hat_alarm;

    /* 设置一下安全带的信息 */
    if(!detectionResult.seatbeltList.empty()){
        dsmSeatbeltDetectionInfo.seatbelt_alarm = detectionResult.seatbeltList[0].seatbelt_alarm;
        dsmSeatbeltDetectionInfo.belt_point.clear();
        for (int i = 0; i < (int) detectionResult.seatbeltList[0].belt_point.size(); i++) {
            dsmSeatbeltDetectionInfo.belt_point.push_back(detectionResult.seatbeltList[0].belt_point[i]);
        }
    }

    dsmInfolock.unlock();
}

void CameraImagRunnable::setDetectionInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {
    objectInfo_Ges_lock.lock();
    curObjectInfo_Ges.objects.clear();
    for (int i = 0; i < (int) objectInfo.objects.size(); i++) {
        curObjectInfo_Ges.objects.push_back(objectInfo.objects[i]);

    }
    objectInfo_Ges_lock.unlock();
}

void CameraImagRunnable::setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult) {


    objectInfo_Adas_lock.lock();

    switch (detectionResult.alarmDecisionType) {
        case ALARM_DECISION_TYPE_PEDSTRIAN_TTC:
        case ALARM_DECISION_TYPE_VEHICLE_TTC:
        case ALARM_DECISION_TYPE_SPG_ADAS_VEHICLE:{
            /* 整理一下TTC人跟车的信息 */
            if (!curObjectInfo_ADAS.empty()) {
                /* 先看看是否在vector里面了 */
                bool isInVectot = false;
                for (std::size_t i = 0; i < curObjectInfo_ADAS.size(); i++) {
                    if (curObjectInfo_ADAS[i].detectionResult.alarmDecisionType == detectionResult.alarmDecisionType &&
                        curObjectInfo_ADAS[i].detectionResult.curCameraType.cameraId ==
                        detectionResult.curCameraType.cameraId) {
                        isInVectot = true;
                        curObjectInfo_ADAS[i].curobjectInfo.objects.clear();
                        copy(objectInfo.objects.begin(), objectInfo.objects.end(),
                             inserter(curObjectInfo_ADAS[i].curobjectInfo.objects,
                                      curObjectInfo_ADAS[i].curobjectInfo.objects.begin()));
                    }
                }
                /* 如果没在vector里面就加一个 */
                if (!isInVectot) {
                    ADASDetectionInfoAll temp;
                    copy(temp.curobjectInfo.objects.begin(), temp.curobjectInfo.objects.end(),
                         inserter(objectInfo.objects, objectInfo.objects.begin()));
                    /* 由于结构体里面没有裸指针，故直接用=就好了 */
                    temp.detectionResult = detectionResult;
                    curObjectInfo_ADAS.push_back(temp);
                }
            } else {
                ADASDetectionInfoAll temp;
                copy(temp.curobjectInfo.objects.begin(), temp.curobjectInfo.objects.end(),
                     inserter(objectInfo.objects, objectInfo.objects.begin()));
                /* 由于结构体里面没有裸指针，故直接用=就好了 */
                temp.detectionResult = detectionResult;
                curObjectInfo_ADAS.push_back(temp);
            }
        }
            break;

        case ALARM_DECISION_TYPE_LANELINE:{
              /* 整理一下车道线的信息 */
            if (!curObjectInfo_ADAS.empty()) {
                /* 先看看是否在vector里面了 */
                bool isInVectot = false;
                for (std::size_t i = 0; i < curObjectInfo_ADAS.size(); i++) {
                    if (curObjectInfo_ADAS[i].detectionResult.alarmDecisionType == detectionResult.alarmDecisionType &&
                        curObjectInfo_ADAS[i].detectionResult.curCameraType.cameraId ==
                        detectionResult.curCameraType.cameraId) {
                        isInVectot = true;
                        curObjectInfo_ADAS[i].curobjectInfo.lanes.clear();
                        copy(objectInfo.lanes.begin(), objectInfo.lanes.end(),
                             inserter(curObjectInfo_ADAS[i].curobjectInfo.lanes,
                                      curObjectInfo_ADAS[i].curobjectInfo.lanes.begin()));
                    }
                }
                /* 如果没在vector里面就加一个 */
                if (!isInVectot) {
                    ADASDetectionInfoAll temp;
                    copy(temp.curobjectInfo.lanes.begin(), temp.curobjectInfo.lanes.end(),
                         inserter(objectInfo.lanes, objectInfo.lanes.begin()));
                    /* 由于结构体里面没有裸指针，故直接用=就好了 */
                    temp.detectionResult = detectionResult;
                    curObjectInfo_ADAS.push_back(temp);
                }
            } else {
                ADASDetectionInfoAll temp;
                copy(temp.curobjectInfo.lanes.begin(), temp.curobjectInfo.lanes.end(),
                     inserter(objectInfo.lanes, objectInfo.lanes.begin()));
                /* 由于结构体里面没有裸指针，故直接用=就好了 */
                temp.detectionResult = detectionResult;
                curObjectInfo_ADAS.push_back(temp);
            }

        }
            break;

        case ALARM_DECISION_TYPE_TRAFFIC_SIGN:{
            /* 整理一下交通标志的信息 */
            if (!curObjectInfo_ADAS.empty()) {
                /* 先看看是否在vector里面了 */
                bool isInVectot = false;
                for (std::size_t i = 0; i < curObjectInfo_ADAS.size(); i++) {
                    if (curObjectInfo_ADAS[i].detectionResult.alarmDecisionType == detectionResult.alarmDecisionType &&
                        curObjectInfo_ADAS[i].detectionResult.curCameraType.cameraId ==
                        detectionResult.curCameraType.cameraId) {
                        isInVectot = true;
                        curObjectInfo_ADAS[i].curobjectInfo.traffics.clear();
                        copy(objectInfo.traffics.begin(), objectInfo.traffics.end(),
                             inserter(curObjectInfo_ADAS[i].curobjectInfo.traffics,
                                      curObjectInfo_ADAS[i].curobjectInfo.traffics.begin()));
                    }
                }
                /* 如果没在vector里面就加一个 */
                if (!isInVectot) {
                    ADASDetectionInfoAll temp;
                    copy(temp.curobjectInfo.traffics.begin(), temp.curobjectInfo.traffics.end(),
                         inserter(objectInfo.traffics, objectInfo.traffics.begin()));
                    /* 由于结构体里面没有裸指针，故直接用=就好了 */
                    temp.detectionResult = detectionResult;
                    curObjectInfo_ADAS.push_back(temp);
                }
            } else {
                ADASDetectionInfoAll temp;
                copy(temp.curobjectInfo.traffics.begin(), temp.curobjectInfo.traffics.end(),
                     inserter(objectInfo.traffics, objectInfo.traffics.begin()));
                /* 由于结构体里面没有裸指针，故直接用=就好了 */
                temp.detectionResult = detectionResult;
                curObjectInfo_ADAS.push_back(temp);
            }
        }
            break;

        default:{
            ; // not to do
        }
            break;
    }

    objectInfo_Adas_lock.unlock();

    }







void CameraImagRunnable::drawBSDInfo(uint8_t *yuvdata, int len) {

    /* 画报警区域 */
    switch (curCameraId) {
        case CAMERA_ID_1: {
            /* 先获取行人报警区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel1(),
                                               pedestrianAlarmAreaList_level1, 6);
            /* 再获取行人报警区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera0PedestrianAlramAreaLevel2(),
                                               pedestrianAlarmAreaList_level2, 6);
            /* 再获取车辆报警区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera0VehicleAlramAreaLevel1(),
                                               vehicleAlarmAreaList_level1, 6);
            /* 再获取车辆报警区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera0VehicleAlramAreaLevel2(),
                                               vehicleAlarmAreaList_level2, 6);
            /* 再获取不识别区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getUndetectedAreaListCamera1()[0],
                                               notdetectionArea1, 6);
            /* 再获取不识别区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getUndetectedAreaListCamera1()[1],
                                               notdetectionArea2, 6);
        }
            break;
        case CAMERA_ID_2: {
            /* 先获取行人报警区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel1(),
                                               pedestrianAlarmAreaList_level1, 6);
            /* 再获取行人报警区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera1PedestrianAlramAreaLevel2(),
                                               pedestrianAlarmAreaList_level2, 6);
            /* 再获取车辆报警区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera1VehicleAlramAreaLevel1(),
                                               vehicleAlarmAreaList_level1, 6);
            /* 再获取车辆报警区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera1VehicleAlramAreaLevel2(),
                                               vehicleAlarmAreaList_level2, 6);
            /* 再获取不识别区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getUndetectedAreaListCamera2()[0],
                                               notdetectionArea1, 6);
            /* 再获取不识别区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getUndetectedAreaListCamera2()[1],
                                               notdetectionArea2, 6);
        }
            break;
        case CAMERA_ID_3: {
            /* 先获取行人报警区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera2PedestrianAlramAreaLevel1(),
                                               pedestrianAlarmAreaList_level1, 6);
            /* 再获取行人报警区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera2PedestrianAlramAreaLevel2(),
                                               pedestrianAlarmAreaList_level2, 6);
            /* 再获取车辆报警区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera2VehicleAlramAreaLevel1(),
                                               vehicleAlarmAreaList_level1, 6);
            /* 再获取车辆报警区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera2VehicleAlramAreaLevel2(),
                                               vehicleAlarmAreaList_level2, 6);
            /* 再获取不识别区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getUndetectedAreaListCamera3()[0],
                                               notdetectionArea1, 6);
            /* 再获取不识别区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getUndetectedAreaListCamera3()[1],
                                               notdetectionArea2, 6);
        }
            break;

        case CAMERA_ID_4: {
            /* 先获取行人报警区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera3PedestrianAlramAreaLevel1(),
                                               pedestrianAlarmAreaList_level1, 6);
            /* 再获取行人报警区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera3PedestrianAlramAreaLevel2(),
                                               pedestrianAlarmAreaList_level2, 6);
            /* 再获取车辆报警区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera3VehicleAlramAreaLevel1(),
                                               vehicleAlarmAreaList_level1, 6);
            /* 再获取车辆报警区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getCamera3VehicleAlramAreaLevel2(),
                                               vehicleAlarmAreaList_level2, 6);
            /* 再获取不识别区域1 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getUndetectedAreaListCamera4()[0],
                                               notdetectionArea1, 6);
            /* 再获取不识别区域2 */
            visPointListToYUVDataOpt_PointList(G3_Configuration::getInstance().getUndetectedAreaListCamera4()[1],
                                               notdetectionArea2, 6);
        }
            break;


    }
    /* 画行人报警区域1 */
    yuvDataOpt.drawToNV21(pedestrianAlarmAreaList_level1, 6, "P-A1", imagWidth, imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_RED, XuYUVDataOpt::TYPE_POLYGON);
    /* 画行人报警区域2 */
    yuvDataOpt.drawToNV21(pedestrianAlarmAreaList_level2, 6, "P-A2", imagWidth, imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW, XuYUVDataOpt::TYPE_POLYGON);

    /* 画车辆报警区域1 */
    yuvDataOpt.drawToNV21(vehicleAlarmAreaList_level1, 6, "V-A1", imagWidth, imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_RED, XuYUVDataOpt::TYPE_POLYGON_DASHEDLINE);
    /* 画车辆报警区域2 */
    yuvDataOpt.drawToNV21(vehicleAlarmAreaList_level2, 6, "V-A2", imagWidth, imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW, XuYUVDataOpt::TYPE_POLYGON_DASHEDLINE);

    /* 画不识别区域1 */
    yuvDataOpt.drawToNV21(notdetectionArea1, 6, "UD-A1", imagWidth, imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_LIGHT_BLUE, XuYUVDataOpt::TYPE_POLYGON);
    /* 画不识别区域2 */
    yuvDataOpt.drawToNV21(notdetectionArea2, 6, "UD-A2", imagWidth, imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_LIGHT_BLUE, XuYUVDataOpt::TYPE_POLYGON);



    /* 如果需要在画面上叠加识别信息就进行叠加 */
    objectInfo_BSD_lock.lock();
    YUVDataOpt_Point objectList[2];

    for (int i = 0; i < static_cast<int>(curObjectInfo_BSD.size()); i++) {
        if (!curObjectInfo_BSD[i].curobjectInfo.objects.empty()) {
            for (int j = 0; j < static_cast<int>(curObjectInfo_BSD[i].curobjectInfo.objects.size()); j++) {
                /* 整理一下框 免得超出屏幕导致崩溃 */
                if (curObjectInfo_BSD[i].curobjectInfo.objects[j].mLeft < 0 ||
                    curObjectInfo_BSD[i].curobjectInfo.objects[j].mRight > imagWidth ||
                    curObjectInfo_BSD[i].curobjectInfo.objects[j].mTop < 0 ||
                    curObjectInfo_BSD[i].curobjectInfo.objects[j].mBottom > imagHeight) {
                    if (curObjectInfo_BSD[i].curobjectInfo.objects[j].mLeft < 0) {
                        curObjectInfo_BSD[i].curobjectInfo.objects[j].mLeft = 0;
                    }
                    if (curObjectInfo_BSD[i].curobjectInfo.objects[j].mRight > imagWidth) {
                        curObjectInfo_BSD[i].curobjectInfo.objects[j].mLeft = imagWidth;
                    }
                    if (curObjectInfo_BSD[i].curobjectInfo.objects[j].mTop < 0) {
                        curObjectInfo_BSD[i].curobjectInfo.objects[j].mTop = 0;
                    }
                    if (curObjectInfo_BSD[i].curobjectInfo.objects[j].mBottom > imagHeight) {
                        curObjectInfo_BSD[i].curobjectInfo.objects[j].mBottom = imagHeight;
                    }
                }


                /* 把框画到图像上  不同的类型用不同的颜色 */
                int color = XuYUVDataOpt::COLOR_GREEN;
                if (strcmp(curObjectInfo_BSD[i].curobjectInfo.objects[j].label, "person") == 0 ||
                    strcmp(curObjectInfo_BSD[i].curobjectInfo.objects[j].label, "cyclist") == 0 ||
                    strcmp(curObjectInfo_BSD[i].curobjectInfo.objects[j].label, "motorcyclist") == 0) {
                    /* 是行人、单车、摩托车则用深绿色框 */
                    color = XuYUVDataOpt::COLOR_DARK_GREEN;
                } else if (strcmp(curObjectInfo_BSD[i].curobjectInfo.objects[j].label, "rider") == 0) {
                    /* 骑自行车、摩托的人则用紫色框（现已废弃） */
                    color = XuYUVDataOpt::COLOR_PURPLE;
                } else if (strcmp(curObjectInfo_BSD[i].curobjectInfo.objects[j].label, "car") == 0) {
                    /* 小轿车则用绿色框 */
                    color = XuYUVDataOpt::COLOR_GREEN;
                } else if (strcmp(curObjectInfo_BSD[i].curobjectInfo.objects[j].label, "bus") == 0) {
                    /* 公交车则用黄色框 */
                    color = XuYUVDataOpt::COLOR_YELLOW;
                } else if (strcmp(curObjectInfo_BSD[i].curobjectInfo.objects[j].label, "truck") == 0) {
                    /* 货车则用蓝色框 */
                    color = XuYUVDataOpt::COLOR_BLUE;
                } else if (strstr(curObjectInfo_BSD[i].curobjectInfo.objects[j].label, "A1-")) {
                    /* 一级报警区域内的则用红色框 */
                    color = XuYUVDataOpt::COLOR_RED;
                } else if (strstr(curObjectInfo_BSD[i].curobjectInfo.objects[j].label, "A2-")) {
                    /* 二级报警区域内的则用红色框 */
                    color = XuYUVDataOpt::COLOR_RED;
                }
                /* 把对角线的坐标填进去 */
                objectList[0].x = curObjectInfo_BSD[i].curobjectInfo.objects[j].mLeft;
                objectList[0].y = curObjectInfo_BSD[i].curobjectInfo.objects[j].mTop;
                objectList[1].x = curObjectInfo_BSD[i].curobjectInfo.objects[j].mRight;
                objectList[1].y = curObjectInfo_BSD[i].curobjectInfo.objects[j].mBottom;
                /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
                if (needMirror) {
                    objectList[0].x = (curObjectInfo_BSD[i].curobjectInfo.objects[j].mLeft >= 640) ? (640 -
                                                                                                      (curObjectInfo_BSD[i].curobjectInfo.objects[j].mLeft -
                                                                                                       640)) : (640 +
                                                                                                                (640 -
                                                                                                                 curObjectInfo_BSD[i].curobjectInfo.objects[j].mLeft));
                    objectList[1].x = (curObjectInfo_BSD[i].curobjectInfo.objects[j].mRight >= 640) ? (640 -
                                                                                                       (curObjectInfo_BSD[i].curobjectInfo.objects[j].mRight -
                                                                                                        640)) : (640 +
                                                                                                                 (640 -
                                                                                                                  curObjectInfo_BSD[i].curobjectInfo.objects[j].mRight));
                }
                /* 开始画 */
                yuvDataOpt.drawToNV21(objectList, 2, curObjectInfo_BSD[i].curobjectInfo.objects[j].label, imagWidth,
                                      imagHeight, yuvdata,
                                      color,
                                      XuYUVDataOpt::TYPE_RECTANGLE);

                /* 顺便把分数也给画上去 */
                std::string scoreStr = std::to_string(curObjectInfo_BSD[i].curobjectInfo.objects[j].mScore);

                yuvDataOpt.drawToNV21(objectList[0].x, objectList[0].y + 50, -1, -1, -1, -1, -1, -1,
                                      scoreStr.substr(0, scoreStr.find(".") + 3).c_str(), imagWidth,
                                      imagHeight, yuvdata,
                                      color,
                                      XuYUVDataOpt::TYPE_ONLY_TEXT);
            }

        }








//
//        /* 画语义分割的画面 */
//        memcpy(mask.data, curObjectInfo_BSD[i].detectionResult.maskBuf, curObjectInfo_BSD[i].detectionResult.maskBufLen);
//
//        xuRgaUtils.imageTransformation(1280, 720, xuRgaUtils.IMG_TYPE_NV21, yuvdata, 1280, 720,xuRgaUtils.IMG_TYPE_BGR888, showimg.data);
//
//        cv::Vec3b a;
//        std::vector<cv::Vec3b> color;
//        a[0] = 255, a[1] = 255, a[2] = 255;
//        color.push_back(a);
//        a[0] = 0, a[1] = 255, a[2] = 0;
//        color.push_back(a);
//        a[0] = 255, a[1] = 0, a[2] = 0;
//        color.push_back(a);
//        a[0] = 0, a[1] = 0, a[2] = 255;
//        color.push_back(a);
//        a[0] = 255, a[1] = 255, a[2] = 0;
//        color.push_back(a);
//        a[0] = 255, a[1] = 0, a[2] = 255;
//        color.push_back(a);
//        a[0] = 0, a[1] = 255, a[2] = 255;
//        color.push_back(a);
//        a[0] = 128, a[1] = 128, a[2] = 0;
//        color.push_back(a);
//        a[0] = 128, a[1] = 0, a[2] = 128;
//        color.push_back(a);
//        a[0] = 0, a[1] = 128, a[2] = 128;
//        color.push_back(a);
//
//
//        xuRgaUtils.imageTransformation(1280, 720, xuRgaUtils.IMG_TYPE_BGR888, addImg.data, 160, 90,
//                                       xuRgaUtils.IMG_TYPE_BGR888, tmp_mask_small.data);
//
//
////        cv::resize(addImg, tmp_mask_small, mask.size());
//        for (int i = 0; i < mask.rows; i++) {
//            for (int j = 0; j < mask.cols; j++) {
//                int val = mask.at<uchar>(i, j);
//
//                if (val <= 0) {
//                    continue;
//                }
//
//
//                if (val > color.size()) {
//                    tmp_mask_small.at<cv::Vec3b>(i, j) = color[0];
//                } else {
//                    tmp_mask_small.at<cv::Vec3b>(i, j) = color[val];
//                }
//
//
//            }
//        }
//
//        cv::Mat tmp_mask_big = cv::Mat(720, 1280, CV_8UC3);
//        xuRgaUtils.imageTransformation(DAS_MASK_WIDTH, DAS_MASK_HEIGHT, xuRgaUtils.IMG_TYPE_BGR888, tmp_mask_small.data, 1280, 720,
//                                       xuRgaUtils.IMG_TYPE_BGR888, tmp_mask_big.data);
////        cv::resize(tmp_mask_small, tmp_mask_big, addImg.size(), cv::INTER_CUBIC);
//
//
//        /* 用RGA合并一下 两路ADAS这里会抛异常 */
//        xuRgaUtils.synthesisImg_Add(1280, 720, tmp_mask_big.data, 1280, 720, showimg.data);
//
//        xuRgaUtils.imageTransformation(1280, 720, xuRgaUtils.IMG_TYPE_BGR888, showimg.data, 1280, 720,
//                                       xuRgaUtils.IMG_TYPE_NV21, yuvdata);
//
//        printf("mask.size=%d \n", curObjectInfo_BSD[i].detectionResult.maskBufLen);





    }


    objectInfo_BSD_lock.unlock();
}

void CameraImagRunnable::drawDSMInfo(uint8_t *yuvdata, int len) {
    /* 如果需要在画面上叠加识别信息就进行叠加 */
    dsmInfolock.lock();
    if ((dsmFaceDetectionInfo.face_score > 0.0) && (dsmFaceDetectionInfo.face_point.size() == 2) &&
        (dsmFaceDetectionInfo.five_point.size() == 5)) {
        YUVDataOpt_Point objectList[2];
        if (dsmFaceDetectionInfo.face_point[0].x < 0 || dsmFaceDetectionInfo.face_point[0].x > imagWidth
            || dsmFaceDetectionInfo.face_point[1].x < 0 || dsmFaceDetectionInfo.face_point[1].x > imagWidth
            || dsmFaceDetectionInfo.face_point[0].y < 0 || dsmFaceDetectionInfo.face_point[0].y > imagHeight
            || dsmFaceDetectionInfo.face_point[1].y < 0 || dsmFaceDetectionInfo.face_point[1].y > imagHeight) {
            if (dsmFaceDetectionInfo.face_point[0].x < 0) {
                dsmFaceDetectionInfo.face_point[0].x = 0;
            }

            if (dsmFaceDetectionInfo.face_point[0].x > imagWidth) {
                dsmFaceDetectionInfo.face_point[0].x = imagWidth;
            }

            if (dsmFaceDetectionInfo.face_point[1].x < 0) {
                dsmFaceDetectionInfo.face_point[1].x = 0;
            }

            if (dsmFaceDetectionInfo.face_point[1].x > imagWidth) {
                dsmFaceDetectionInfo.face_point[1].x = imagWidth;
            }
            if (dsmFaceDetectionInfo.face_point[0].y < 0) {
                dsmFaceDetectionInfo.face_point[0].y = 0;
            }
            if (dsmFaceDetectionInfo.face_point[0].y > imagHeight) {
                dsmFaceDetectionInfo.face_point[0].y = imagHeight;
            }

            if (dsmFaceDetectionInfo.face_point[1].y < 0) {
                dsmFaceDetectionInfo.face_point[1].y = 0;
            }
            if (dsmFaceDetectionInfo.face_point[1].y > imagHeight) {
                dsmFaceDetectionInfo.face_point[1].y = imagHeight;
            }
        }
        /* 把框画到图像上 */
        int color = XuYUVDataOpt::COLOR_WHITE;
        objectList[0].x = dsmFaceDetectionInfo.face_point[0].x;
        objectList[0].y = dsmFaceDetectionInfo.face_point[0].y;
        objectList[1].x = dsmFaceDetectionInfo.face_point[1].x;
        objectList[1].y = dsmFaceDetectionInfo.face_point[1].y;
        /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
        if (needMirror) {
            objectList[0].x = (dsmFaceDetectionInfo.face_point[0].x >= 640) ? (640 - (dsmFaceDetectionInfo.face_point[0].x - 640)) : (
                    640 + (640 - dsmFaceDetectionInfo.face_point[0].x));
            objectList[1].x = (dsmFaceDetectionInfo.face_point[1].x >= 640) ? (640 - (dsmFaceDetectionInfo.face_point[1].x - 640)) : (
                    640 + (640 - dsmFaceDetectionInfo.face_point[1].x));
        }


        yuvDataOpt.drawToNV21(objectList, 2, std::to_string(dsmFaceDetectionInfo.face_angle).c_str(), imagWidth, imagHeight, yuvdata, color,
                              XuYUVDataOpt::TYPE_RECTANGLE);

        /* 画特征点 */
        color = XuYUVDataOpt::COLOR_GREEN;
        objectList[0].x = dsmFaceDetectionInfo.five_point[0].x - 1;
        objectList[0].y = dsmFaceDetectionInfo.five_point[0].y - 1;
        objectList[1].x = dsmFaceDetectionInfo.five_point[0].x + 1;
        objectList[1].y = dsmFaceDetectionInfo.five_point[0].y + 1;
        /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
        if (needMirror) {
            objectList[0].x = ((dsmFaceDetectionInfo.five_point[0].x - 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[0].x - 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[0].x - 1)));
            objectList[1].x = ((dsmFaceDetectionInfo.five_point[0].x + 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[0].x + 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[0].x + 1)));
        }
        if (objectList[0].x < 0 || objectList[0].x > imagWidth
            || objectList[1].x < 0 || objectList[1].x > imagWidth
            || objectList[0].y < 0 || objectList[0].y > imagHeight
            || objectList[1].y < 0 || objectList[1].y > imagHeight) {

        } else {
            yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth, imagHeight, yuvdata, color,
                                  XuYUVDataOpt::TYPE_RECTANGLE);
        }

        objectList[0].x = dsmFaceDetectionInfo.five_point[1].x - 1;
        objectList[0].y = dsmFaceDetectionInfo.five_point[1].y - 1;
        objectList[1].x = dsmFaceDetectionInfo.five_point[1].x + 1;
        objectList[1].y = dsmFaceDetectionInfo.five_point[1].y + 1;

        /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
        if (needMirror) {
            objectList[0].x = ((dsmFaceDetectionInfo.five_point[1].x - 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[1].x - 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[1].x - 1)));
            objectList[1].x = ((dsmFaceDetectionInfo.five_point[1].x + 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[1].x + 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[1].x + 1)));
        }
        if (objectList[0].x < 0 || objectList[0].x > imagWidth
            || objectList[1].x < 0 || objectList[1].x > imagWidth
            || objectList[0].y < 0 || objectList[0].y > imagHeight
            || objectList[1].y < 0 || objectList[1].y > imagHeight) {

        } else {
            yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth, imagHeight, yuvdata, color,
                                  XuYUVDataOpt::TYPE_RECTANGLE);
        }
        objectList[0].x = dsmFaceDetectionInfo.five_point[2].x - 1;
        objectList[0].y = dsmFaceDetectionInfo.five_point[2].y - 1;
        objectList[1].x = dsmFaceDetectionInfo.five_point[2].x + 1;
        objectList[1].y = dsmFaceDetectionInfo.five_point[2].y + 1;
        /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
        if (needMirror) {
            objectList[0].x = ((dsmFaceDetectionInfo.five_point[2].x - 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[2].x - 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[2].x - 1)));
            objectList[1].x = ((dsmFaceDetectionInfo.five_point[2].x + 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[2].x + 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[2].x + 1)));
        }
        if (objectList[0].x < 0 || objectList[0].x > imagWidth
            || objectList[1].x < 0 || objectList[1].x > imagWidth
            || objectList[0].y < 0 || objectList[0].y > imagHeight
            || objectList[1].y < 0 || objectList[1].y > imagHeight) {

        } else {
            yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth, imagHeight, yuvdata, color,
                                  XuYUVDataOpt::TYPE_RECTANGLE);
        }
        objectList[0].x = dsmFaceDetectionInfo.five_point[3].x - 1;
        objectList[0].y = dsmFaceDetectionInfo.five_point[3].y - 1;
        objectList[1].x = dsmFaceDetectionInfo.five_point[3].x + 1;
        objectList[1].y = dsmFaceDetectionInfo.five_point[3].y + 1;
        /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
        if (needMirror) {
            objectList[0].x = ((dsmFaceDetectionInfo.five_point[3].x - 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[3].x - 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[3].x - 1)));
            objectList[1].x = ((dsmFaceDetectionInfo.five_point[3].x + 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[3].x + 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[3].x + 1)));
        }
        if (objectList[0].x < 0 || objectList[0].x > imagWidth
            || objectList[1].x < 0 || objectList[1].x > imagWidth
            || objectList[0].y < 0 || objectList[0].y > imagHeight
            || objectList[1].y < 0 || objectList[1].y > imagHeight) {

        } else {
            yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth, imagHeight, yuvdata, color,
                                  XuYUVDataOpt::TYPE_RECTANGLE);
        }
        objectList[0].x = dsmFaceDetectionInfo.five_point[4].x - 1;
        objectList[0].y = dsmFaceDetectionInfo.five_point[4].y - 1;
        objectList[1].x = dsmFaceDetectionInfo.five_point[4].x + 1;
        objectList[1].y = dsmFaceDetectionInfo.five_point[4].y + 1;
        /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
        if (needMirror) {
            objectList[0].x = ((dsmFaceDetectionInfo.five_point[4].x - 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[4].x - 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[4].x - 1)));
            objectList[1].x = ((dsmFaceDetectionInfo.five_point[4].x + 1) >= 640) ? (640 -
                                                                            ((dsmFaceDetectionInfo.five_point[4].x + 1) - 640))
                                                                         : (640 +
                                                                            (640 - (dsmFaceDetectionInfo.five_point[4].x + 1)));
        }
        if (objectList[0].x < 0 || objectList[0].x > imagWidth
            || objectList[1].x < 0 || objectList[1].x > imagWidth
            || objectList[0].y < 0 || objectList[0].y > imagHeight
            || objectList[1].y < 0 || objectList[1].y > imagHeight) {

        } else {
            yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth, imagHeight, yuvdata, color,
                                  XuYUVDataOpt::TYPE_RECTANGLE);
        }
    } else { ; //not to do
    }


    /* 画安全带的点 */
    if(!dsmSeatbeltDetectionInfo.belt_point.empty()){
        YUVDataOpt_Point objectList[6];
        int color = XuYUVDataOpt::COLOR_WHITE;
        color = XuYUVDataOpt::COLOR_RED;
        objectList[0].x = dsmSeatbeltDetectionInfo.belt_point[0].x;
        objectList[0].y = dsmSeatbeltDetectionInfo.belt_point[0].y;
        objectList[1].x = dsmSeatbeltDetectionInfo.belt_point[1].x;
        objectList[1].y = dsmSeatbeltDetectionInfo.belt_point[1].y;
        objectList[2].x = dsmSeatbeltDetectionInfo.belt_point[2].x;
        objectList[2].y = dsmSeatbeltDetectionInfo.belt_point[2].y;
        objectList[3].x = dsmSeatbeltDetectionInfo.belt_point[3].x;
        objectList[3].y = dsmSeatbeltDetectionInfo.belt_point[3].y;
        objectList[4].x = dsmSeatbeltDetectionInfo.belt_point[4].x;
        objectList[4].y = dsmSeatbeltDetectionInfo.belt_point[4].y;
        /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
        if (needMirror) {
            objectList[0].x = ((objectList[0].x) >= 640) ? (640 -((objectList[0].x) - 640)) : (640 + (640 - (objectList[0].x)));
            objectList[1].x = ((objectList[1].x) >= 640) ? (640 - ((objectList[1].x) - 640)) : (640 + (640 - (objectList[1].x)));
            objectList[2].x = ((objectList[2].x) >= 640) ? (640 -((objectList[2].x) - 640)) : (640 + (640 - (objectList[2].x)));
            objectList[3].x = ((objectList[3].x) >= 640) ? (640 - ((objectList[3].x) - 640)) : (640 + (640 - (objectList[3].x)));
            objectList[4].x = ((objectList[4].x) >= 640) ? (640 -((objectList[4].x) - 640)) : (640 + (640 - (objectList[4].x)));
        }


        if (objectList[0].x < 0 || objectList[0].x > imagWidth
            || objectList[1].x < 0 || objectList[1].x > imagWidth
            || objectList[0].y < 0 || objectList[0].y > imagHeight
            || objectList[1].y < 0 || objectList[1].y > imagHeight
            || objectList[2].x < 0 || objectList[2].x > imagWidth
            || objectList[3].x < 0 || objectList[3].x > imagWidth
            || objectList[2].y < 0 || objectList[2].y > imagHeight
            || objectList[3].y < 0 || objectList[3].y > imagHeight
            || objectList[4].x < 0 || objectList[4].x > imagWidth
            || objectList[4].y < 0 || objectList[4].y > imagHeight) {

        } else {
            yuvDataOpt.drawToNV21(objectList, 5, "", imagWidth, imagHeight, yuvdata, color,
                                  XuYUVDataOpt::TYPE_POLYGON);
        }


    }


    /* 画报警类型 */
    std::string alarmText = "";
    if (dsmFaceDetectionInfo.respirator_alarm) {
        alarmText.append("respirator");
    }
    if (dsmFaceDetectionInfo.eye_alarm) {
        alarmText.append("-eye");
    }
    if (dsmFaceDetectionInfo.mouth_alarm) {
        alarmText.append("-mouth");
    }
    if (dsmFaceDetectionInfo.lookaround_alarm) {
        alarmText.append("-lookaround");
    }
    if (dsmFaceDetectionInfo.facemissing_alarm) {
        alarmText.append("-facemissing");
    }
    if (dsmFaceDetectionInfo.camcover_alarm) {
        alarmText.append("-camcover");
    }
    if (dsmFaceDetectionInfo.smoking_alarm) {
        alarmText.append("-smoking");
    }
    if (dsmFaceDetectionInfo.phone_alarm) {
        alarmText.append("-phone");
    }
    if (dsmFaceDetectionInfo.Blindspot_alarm) {
        alarmText.append("-Blindspot_alarm");
    }
    if (dsmSeatbeltDetectionInfo.seatbelt_alarm) {
        alarmText.append("-seatbelt_alarm");
    }


    alarmText.append("-fatiguerank=");
    alarmText.append(std::to_string(dsmFaceDetectionInfo.fatigue_rank));
    yuvDataOpt.drawToNV21(50, 600, 900, 700, -1, -1, -1, -1, alarmText.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_WHITE,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);

    dsmInfolock.unlock();
}

void CameraImagRunnable::drawGESInfo(uint8_t *yuvdata, int len) {
    /* 如果需要在画面上叠加识别信息就进行叠加 */
    objectInfo_Ges_lock.lock();
    YUVDataOpt_Point objectList[2];
    for (int i = 0; i < (int) curObjectInfo_Ges.objects.size(); i++) {
        if (curObjectInfo_Ges.objects[i].mLeft < 0 ||
            curObjectInfo_Ges.objects[i].mRight > imagWidth ||
            curObjectInfo_Ges.objects[i].mTop < 0 ||
            curObjectInfo_Ges.objects[i].mBottom > imagHeight) {
            if (curObjectInfo_Ges.objects[i].mLeft < 0) {
                curObjectInfo_Ges.objects[i].mLeft = 0;
            }
            if (curObjectInfo_Ges.objects[i].mRight > imagWidth) {
                curObjectInfo_Ges.objects[i].mLeft = imagWidth;
            }
            if (curObjectInfo_Ges.objects[i].mTop < 0) {
                curObjectInfo_Ges.objects[i].mTop = 0;
            }
            if (curObjectInfo_Ges.objects[i].mBottom > imagHeight) {
                curObjectInfo_Ges.objects[i].mBottom = imagHeight;
            }
        }

        /* 把框画到图像上*/
        int color = XuYUVDataOpt::COLOR_RED;
        objectList[0].x = curObjectInfo_Ges.objects[i].mLeft;
        objectList[0].y = curObjectInfo_Ges.objects[i].mTop;
        objectList[1].x = curObjectInfo_Ges.objects[i].mRight;
        objectList[1].y = curObjectInfo_Ges.objects[i].mBottom;
        /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
        if (needMirror) {
            objectList[0].x = (curObjectInfo_Ges.objects[i].mLeft >= 640) ? (640 -
                                                                             (curObjectInfo_Ges.objects[i].mLeft - 640))
                                                                          : (640 + (640 -
                                                                                    (curObjectInfo_Ges.objects[i].mLeft -
                                                                                     1)));
            objectList[1].x = (curObjectInfo_Ges.objects[i].mRight >= 640) ? (640 -
                                                                              (curObjectInfo_Ges.objects[i].mRight -
                                                                               640)) : (640 + (640 -
                                                                                               curObjectInfo_Ges.objects[i].mRight));
        }


        yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth, imagHeight, yuvdata, color,
                              XuYUVDataOpt::TYPE_RECTANGLE);

    }
    objectInfo_Ges_lock.unlock();

}

void CameraImagRunnable::drawADASInfo(uint8_t *yuvdata, int len) {

    /* 如果需要在画面上叠加识别信息就进行叠加 */
    objectInfo_Adas_lock.lock();


    YUVDataOpt_Point objectList[2];
//    printf("curObjectInfo_ADAS.size()=%d \n",curObjectInfo_ADAS.size());
    /* 画识别信息 */
    for (int i = 0; i < static_cast<int>(curObjectInfo_ADAS.size()); i++) {
//        printf("objectInfo.objects.size=%d   alarmDecisionType=%d \n",curObjectInfo_ADAS[i].curobjectInfo.objects.size(),curObjectInfo_ADAS[i].detectionResult.alarmDecisionType);
        /* 画识别框 */
        for (int j = 0; j < static_cast<int>(curObjectInfo_ADAS[i].curobjectInfo.objects.size()); j++) {


            if (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mLeft < 0 ||
                curObjectInfo_ADAS[i].curobjectInfo.objects[j].mRight > imagWidth ||
                curObjectInfo_ADAS[i].curobjectInfo.objects[j].mTop < 0 ||
                curObjectInfo_ADAS[i].curobjectInfo.objects[j].mBottom > imagHeight) {
                if (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mLeft < 0) {
                    curObjectInfo_ADAS[i].curobjectInfo.objects[j].mLeft = 0;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mRight > imagWidth) {
                    curObjectInfo_ADAS[i].curobjectInfo.objects[j].mLeft = imagWidth;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mTop < 0) {
                    curObjectInfo_ADAS[i].curobjectInfo.objects[j].mTop = 0;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mBottom > imagHeight) {
                    curObjectInfo_ADAS[i].curobjectInfo.objects[j].mBottom = imagHeight;
                }
            }
//            printf("==========================%u,%u  %u,%u====================\n",curObjectInfo.objects[i].mLeft,curObjectInfo.objects[i].mTop,curObjectInfo.objects[i].mRight,curObjectInfo.objects[i].mBottom);




            /* 把框画到图像上  不同的类型用不同的颜色 */
            int color = XuYUVDataOpt::COLOR_WHITE;
            if (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mIsAheadObj) {
                color = XuYUVDataOpt::COLOR_RED;
            } else {
                color = XuYUVDataOpt::COLOR_GREEN;
            }


            objectList[0].x = curObjectInfo_ADAS[i].curobjectInfo.objects[j].mLeft;
            objectList[0].y = curObjectInfo_ADAS[i].curobjectInfo.objects[j].mTop;
            objectList[1].x = curObjectInfo_ADAS[i].curobjectInfo.objects[j].mRight;
            objectList[1].y = curObjectInfo_ADAS[i].curobjectInfo.objects[j].mBottom;
            /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
            if (needMirror) {
                objectList[0].x = (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mLeft >= 640) ? (640 -
                                                                                                   (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mLeft -
                                                                                                    640)) : (640 +
                                                                                                             (640 -
                                                                                                              curObjectInfo_ADAS[i].curobjectInfo.objects[j].mLeft));
                objectList[1].x = (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mRight >= 640) ? (640 -
                                                                                                    (curObjectInfo_ADAS[i].curobjectInfo.objects[j].mRight -
                                                                                                     640)) : (640 +
                                                                                                              (640 -
                                                                                                               curObjectInfo_ADAS[i].curobjectInfo.objects[j].mRight));
            }


            std::string textContent;
            textContent.append(curObjectInfo_ADAS[i].curobjectInfo.objects[j].label);
            textContent.append(" ");
            textContent.append(std::to_string(curObjectInfo_ADAS[i].curobjectInfo.objects[j].mYDistance));
            textContent.append(" ");
            textContent.append(
                    XuString::getInstance().formatFloatValue(curObjectInfo_ADAS[i].curobjectInfo.objects[j].mHeadway,
                                                             2));
            yuvDataOpt.drawToNV21(objectList, 2, textContent.c_str(), imagWidth, imagHeight, yuvdata,
                                  color,
                                  XuYUVDataOpt::TYPE_RECTANGLE);
        }

        /* 画一下天地线 */
        objectList[0].x = 0;
        objectList[0].y = G3_Configuration::getInstance().getAdasHorizonY();
        objectList[1].x = imagWidth;
        objectList[1].y = G3_Configuration::getInstance().getAdasHorizonY();
        yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth, imagHeight, yuvdata,
                              XuYUVDataOpt::COLOR_RED,
                              XuYUVDataOpt::TYPE_DASHED_LINE);

        /* 画线  不需要镜像的是才画线 */
        if (!needMirror) {
            for (int j = 0; j < static_cast<int>(curObjectInfo_ADAS[i].curobjectInfo.lanes.size()); j++) {
                if (curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAX < 0) {
                    curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAX = 0;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAX > imagWidth) {
                    curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAX = imagWidth;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBX < 0) {
                    curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBX = 0;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBX > imagWidth) {
                    curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBX = imagWidth;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAY < 0) {
                    curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAY = 0;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAY > imagHeight) {
                    curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAY = imagHeight;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBY < 0) {
                    curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBY = 0;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBY > imagHeight) {
                    curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBY = imagHeight;
                }

                objectList[0].x = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAX;
                objectList[0].y = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAY;
                objectList[1].x = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBX;
                objectList[1].y = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBY;


//            printf("==========================A=%d,%d  B=%d,%d====================\n",curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAX,curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAY,curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBX,curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBY);
                yuvDataOpt.drawAdasLanelineToNV21(objectList[0], objectList[1],
                                                  curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mA0,
                                                  curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mA1,
                                                  curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mA2,
                                                  curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mA3, imagWidth,
                                                  imagHeight, yuvdata, XuYUVDataOpt::COLOR_RED);
                int color = XuYUVDataOpt::COLOR_RED;
                objectList[0].x = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAX - 3;
                objectList[0].y = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAY - 3;
                objectList[1].x = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAX + 3;
                objectList[1].y = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointAY + 3;

                if (objectList[0].x < 0) {
                    objectList[0].x = 0;
                }
                if (objectList[0].x > imagWidth) {
                    objectList[0].x = imagWidth;
                }
                if (objectList[1].x < 0) {
                    objectList[1].x = 0;
                }
                if (objectList[1].x > imagWidth) {
                    objectList[1].x = imagWidth;
                }
                if (objectList[0].y < 0) {
                    objectList[0].y = 0;
                }
                if (objectList[0].y > imagHeight) {
                    objectList[0].y = imagHeight;
                }
                if (objectList[1].y < 0) {
                    objectList[1].y = 0;
                }
                if (objectList[1].y > imagHeight) {
                    objectList[1].y = imagHeight;
                }


                yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth, imagHeight, yuvdata,
                                      color,
                                      XuYUVDataOpt::TYPE_RECTANGLE);
                color = XuYUVDataOpt::COLOR_BLUE;
                objectList[0].x = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBX - 3;
                objectList[0].y = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBY - 3;
                objectList[1].x = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBX + 3;
                objectList[1].y = curObjectInfo_ADAS[i].curobjectInfo.lanes[j].mEndpointBY + 3;
                if (objectList[0].x < 0) {
                    objectList[0].x = 0;
                }
                if (objectList[0].x > imagWidth) {
                    objectList[0].x = imagWidth;
                }
                if (objectList[1].x < 0) {
                    objectList[1].x = 0;
                }
                if (objectList[1].x > imagWidth) {
                    objectList[1].x = imagWidth;
                }
                if (objectList[0].y < 0) {
                    objectList[0].y = 0;
                }
                if (objectList[0].y > imagHeight) {
                    objectList[0].y = imagHeight;
                }
                if (objectList[1].y < 0) {
                    objectList[1].y = 0;
                }
                if (objectList[1].y > imagHeight) {
                    objectList[1].y = imagHeight;
                }
                yuvDataOpt.drawToNV21(objectList, 2, "", imagWidth, imagHeight, yuvdata,
                                      color,
                                      XuYUVDataOpt::TYPE_RECTANGLE);
            }
        }

        /* 画交通标志 */
        for (int j = 0; j < static_cast<int>(curObjectInfo_ADAS[i].curobjectInfo.traffics.size()); j++) {


            if (curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mLeft < 0 ||
                curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mRight > imagWidth ||
                curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mTop < 0 ||
                curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mBottom > imagHeight) {
                if (curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mLeft < 0) {
                    curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mLeft = 0;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mRight > imagWidth) {
                    curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mLeft = imagWidth;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mTop < 0) {
                    curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mTop = 0;
                }
                if (curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mBottom > imagHeight) {
                    curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mBottom = imagHeight;
                }
            }
//            printf("==========================%u,%u  %u,%u====================\n",curObjectInfo.traffics[i].mLeft,curObjectInfo.traffics[i].mTop,curObjectInfo.traffics[i].mRight,curObjectInfo.traffics[i].mBottom);




            /* 把框画到图像上  不同的类型用不同的颜色 */
            int color = XuYUVDataOpt::COLOR_WHITE;
            if (strstr(curObjectInfo_ADAS[i].curobjectInfo.traffics[j].label, "Road-")) {
                color = XuYUVDataOpt::COLOR_RED;
            } else {
                color = XuYUVDataOpt::COLOR_GREEN;
            }


            objectList[0].x = curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mLeft;
            objectList[0].y = curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mTop;
            objectList[1].x = curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mRight;
            objectList[1].y = curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mBottom;
            /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
            if (needMirror) {
                objectList[0].x = (curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mLeft >= 640) ? (640 -
                                                                                                   (curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mLeft -
                                                                                                    640)) : (640 +
                                                                                                             (640 -
                                                                                                              curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mLeft));
                objectList[1].x = (curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mRight >= 640) ? (640 -
                                                                                                    (curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mRight -
                                                                                                     640)) : (640 +
                                                                                                              (640 -
                                                                                                               curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mRight));
            }


            std::string textContent;
            textContent.append(curObjectInfo_ADAS[i].curobjectInfo.traffics[j].label);
            textContent.append(" ");
            textContent.append(std::to_string(curObjectInfo_ADAS[i].curobjectInfo.traffics[j].mSpeedLimit));

            yuvDataOpt.drawToNV21(objectList, 2, textContent.c_str(), imagWidth, imagHeight, yuvdata,
                                  color,
                                  XuYUVDataOpt::TYPE_RECTANGLE);
        }



    }

    objectInfo_Adas_lock.unlock();
}

void CameraImagRunnable::drawMaskBuf(uint8_t *maskbuf, const int maskbufLen, uint8_t *yuvdata, const int yuvlen) {
    /* 画语义分割的画面 */
    memcpy(mask.data, maskbuf, DAS_MASK_HEIGHT * DAS_MASK_WIDTH);

    xuRgaUtils.imageTransformation(imagWidth, imagHeight, xuRgaUtils.IMG_TYPE_NV21, yuvdata, imagWidth, imagHeight,
                                   xuRgaUtils.IMG_TYPE_BGR888, showimg.data);

    cv::Vec3b a;
    std::vector<cv::Vec3b> color;
    a[0] = 255, a[1] = 255, a[2] = 255;
    color.push_back(a);
    a[0] = 0, a[1] = 255, a[2] = 0;
    color.push_back(a);
    a[0] = 255, a[1] = 0, a[2] = 0;
    color.push_back(a);
    a[0] = 0, a[1] = 0, a[2] = 255;
    color.push_back(a);
    a[0] = 255, a[1] = 255, a[2] = 0;
    color.push_back(a);
    a[0] = 255, a[1] = 0, a[2] = 255;
    color.push_back(a);
    a[0] = 0, a[1] = 255, a[2] = 255;
    color.push_back(a);
    a[0] = 128, a[1] = 128, a[2] = 0;
    color.push_back(a);
    a[0] = 128, a[1] = 0, a[2] = 128;
    color.push_back(a);
    a[0] = 0, a[1] = 128, a[2] = 128;
    color.push_back(a);


    xuRgaUtils.imageTransformation(imagWidth, imagHeight, xuRgaUtils.IMG_TYPE_BGR888, addImg.data, 160, 90,
                                   xuRgaUtils.IMG_TYPE_BGR888, tmp_mask_small.data);


//        cv::resize(addImg, tmp_mask_small, mask.size());
    for (int i = 0; i < mask.rows; i++) {
        for (int j = 0; j < mask.cols; j++) {
            int val = mask.at<uchar>(i, j);

            if (val <= 0) {
                continue;
            }


            if (val > static_cast<int>(color.size())) {
                tmp_mask_small.at<cv::Vec3b>(i, j) = color[0];
            } else {
                tmp_mask_small.at<cv::Vec3b>(i, j) = color[val];
            }
        }
    }

    cv::Mat tmp_mask_big = cv::Mat(imagHeight, imagWidth, CV_8UC3);
    xuRgaUtils.imageTransformation(160, 90, xuRgaUtils.IMG_TYPE_BGR888, tmp_mask_small.data, imagWidth, imagHeight,
                                   xuRgaUtils.IMG_TYPE_BGR888, tmp_mask_big.data);
//        cv::resize(tmp_mask_small, tmp_mask_big, addImg.size(), cv::INTER_CUBIC);


    /* 用RGA合并一下 两路ADAS这里会抛异常 */
    xuRgaUtils.synthesisImg_Add(imagWidth, imagHeight, tmp_mask_big.data, imagWidth, imagHeight, showimg.data);

    xuRgaUtils.imageTransformation(imagWidth, imagHeight, xuRgaUtils.IMG_TYPE_BGR888, showimg.data, imagWidth, imagHeight,
                                   xuRgaUtils.IMG_TYPE_NV21, yuvdata);
}

int CameraImagRunnable::visPointListToYUVDataOpt_PointList(const std::vector<VISPoint> &visPointList,
                                                           YUVDataOpt_Point *yuvDataOpt_PointList, const int len) {
    int ret = -1;
    if (len > 0 && !visPointList.empty()) {
        if (visPointList.size() >= static_cast<std::size_t>(len)) {
            for (int i = 0; i < len; i++) {
                /* 如果需要镜像的话，X坐标需要根据画面中线翻转 */
                if (needMirror) {
                    yuvDataOpt_PointList[i].x = (visPointList[i].x >= 640) ? (640 - (visPointList[i].x - 640)) : (640 +
                                                                                                                  (640 -
                                                                                                                   visPointList[i].x));
                } else {
                    yuvDataOpt_PointList[i].x = visPointList[i].x;
                }

                yuvDataOpt_PointList[i].y = visPointList[i].y;
                ret = 0;
            }

        }
    }
    return ret;
}

void CameraImagRunnable::setCANWorkStatus(bool isWork) {
    canWorkStatus = isWork;
}

bool CameraImagRunnable::isNeedMirror() const {
    return needMirror;
}

void CameraImagRunnable::setNeedMirror(bool needMirror) {
    CameraImagRunnable::needMirror = needMirror;
}

void CameraImagRunnable::getImageFromCamera() {
    int ret = -1;
    XuCameraUtils camera;
    /* 反复打开相机，一定要打开成功才能进行接下来的操作 */
    while ((ret = camera.openCamera(curCameraId, imagWidth, imagHeight)) != 0) {
        sleep(1);
    }
    camera.start();
    cameraopened = true;
    /* 计算下一帧图像要多少的内存空间，用来提前开辟内存。这里是以NV21格式计算的 */
    int camrea_yuv_buffLen = imagWidth * imagHeight * 3 / 2;
    /* 用来从V4LS拿图像的内存（主要给算法用） */
    uint8_t *camrea_yuv_buff;
    camrea_yuv_buff = static_cast<uint8_t *>(malloc(camrea_yuv_buffLen));
    /* 先给为编码和显示的封装的对象开辟一下内存，这样需要镜像的时候就可以少做以此memcpy */
    drawYuvData.setYUVData(camrea_yuv_buffLen, camrea_yuv_buff);
    /* 用来做镜像的内存 */
    uint8_t *camrea_yuv_buff_mirror;
    camrea_yuv_buff_mirror = static_cast<uint8_t *>(malloc(camrea_yuv_buffLen));
    /* 记录一下开始获取的时间，免得时间过大，出问题 */
    lastGetYUVDataTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
    /* 计算下每次获取相机图像的时间间隔 单位ms */
    int getCmaeraYUVInterval = 1000 / G3_Configuration::getInstance().getCameraFrameRate();
    while (!needStop) {
        /* 判断一下距离上一次取图像的时间是否超过了40ms（定帧25） */
        int curInterval = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - lastGetYUVDataTime;
        if(curInterval >= getCmaeraYUVInterval){
            /* 时间到了，保存下获取时间 */
            lastGetYUVDataTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
            /* 从相机拿出一帧图像 */
            ret = camera.getCameraYUV(camrea_yuv_buff, camrea_yuv_buffLen);
            /* 比对一下返回的长度，正确了才能进行下一步 */
            if (ret == camrea_yuv_buffLen) {
                /* 先封装成一个对象传给算法用 */
                curCameraYuvData.setYUVData(ret, camrea_yuv_buff);
                curMainUnitInterface->onDetectionYUVGet(curCameraYuvData);
                /* 如果需要镜像，那么就镜像一下 */
                if (needMirror) {
                    /* 镜像的话就先进行镜像翻转 */
                    xuRgaUtils.imageTransformation(imagWidth, imagHeight, XuRGAUtils::IMG_TYPE_NV21,
                                                   camrea_yuv_buff, imagWidth, imagHeight,
                                                   XuRGAUtils::IMG_TYPE_NV21, camrea_yuv_buff_mirror,
                                                   false, 0, 0, XuRGAUtils::FLIP_TYPE_H);

                    /* 然后画时间戳那些 */
                    drawYUV(camrea_yuv_buff_mirror, ret);
                    /* 一切搞定了就封装，避免屏幕显示时间戳会闪一下的问题 */
                    drawYuvData.setYUVData(ret, camrea_yuv_buff_mirror);
                }else{
                    /* 不用镜像的情况下，就先画时间戳那些 */
                    drawYUV(camrea_yuv_buff, ret);
                    /* 一切搞定了就封装，避免屏幕显示时间戳会闪一下的问题 */
                    drawYuvData.setYUVData(ret, camrea_yuv_buff);
                }
                /* 先给编码器用 */
                curMainUnitInterface->onCameraYUVGet(drawYuvData);  //20%
                /* 最后给屏幕显示用 */
                curMainUnitInterface->onShowYUVGet(drawYuvData);    //10%



            }
        }else{
            /* 不够时间，就什么都不做 */
        }
        /* 等待1ms（这里是为了在处理时间较长是也能快一点应用过来，不过会占用更多的CPU） */
        usleep(1*1000);
    }
    /* 释放一下内存 */
    free(camrea_yuv_buff);
    free(camrea_yuv_buff_mirror);
}

void CameraImagRunnable::getImageFromMp4File(const char *mp4FilePath) {
    /* 实例化从MP4读图片的工具类 */
    MP4ToYUV mp4ToYuv;
    /* 声明用到的变量 */
    cv::Mat srcframe;
    uint8_t *camrea_yuv_buff = static_cast<uint8_t *>(malloc(imagWidth * imagHeight * 3 / 2));
    /* 尝试打开视频文件 */
    bool ret = mp4ToYuv.openMp4File(mp4FilePath);
    /* 打开成功才能进行操作 */
    if(ret){
        /* 循环从文件里面读图片 */
        cameraopened = true;
        while (mp4ToYuv.readOneFrame(srcframe)){
            /* 转成NV21 */
            xuRgaUtils.imageTransformation(1280,720,xuRgaUtils.IMG_TYPE_BGR888,srcframe.data,1280,720,xuRgaUtils.IMG_TYPE_NV21,camrea_yuv_buff);
            /* 先封装成一个对象传给算法用 */
            curCameraYuvData.setYUVData(1280*720*3/2, camrea_yuv_buff);
            curMainUnitInterface->onDetectionYUVGet(curCameraYuvData);

            drawYUV(camrea_yuv_buff,1280*720*3/2);
            drawYuvData.setYUVData(1280*720*3/2, camrea_yuv_buff);

            /* 先给编码器用 */
            curMainUnitInterface->onCameraYUVGet(drawYuvData);  //20%
            /* 最后给屏幕显示用 */
            curMainUnitInterface->onShowYUVGet(drawYuvData);    //10%

            /* 保存下获取时间 */
            lastGetYUVDataTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();

            /* 等40ms，就是固定25帧 */
            usleep(40*1000);

        }
    }

}

void CameraImagRunnable::setPhotoresistorValue(float photoresistorValue) {
    CameraImagRunnable::photoresistorValue = photoresistorValue;
}

void CameraImagRunnable::drawTestInfoByV6(uint8_t *yuvdata, int len) {

    testInfostr.append("Photoresistor:  ");
    testInfostr.append(std::to_string(photoresistorValue));
    yuvDataOpt.drawToNV21(50, 200, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("CPUTemperature: ");
    testInfostr.append(temperatureCPUGood ? "YES" : "NO");
    yuvDataOpt.drawToNV21(50, 250, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("TCP:            ");
    testInfostr.append((tcpClient == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(50, 300, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("UDP Broadcast:  ");
    testInfostr.append((udpBroadcast == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(50, 350, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();



    testInfostr.append("CPU:           ");
    testInfostr.append(cpuSerialStr);
    yuvDataOpt.drawToNV21(50, 400, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("SoftwareVer:    ");
    testInfostr.append(g3SoftwardVersion);
    yuvDataOpt.drawToNV21(50, 450, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("FirmwareVer:    ");
    testInfostr.append(getenv("G3_FIRMWARE_VERSION"));
    yuvDataOpt.drawToNV21(50, 500, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("IR Lamp:        ");
    testInfostr.append((cameraIRLampSwichEnable) ? "ON" : "OFF");
    yuvDataOpt.drawToNV21(50, 550, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

//    testInfostr.append("OUTPUT1:  ");
//
//    yuvDataOpt.drawToNV21(50, 600, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
//                          imagHeight, yuvdata,
//                          XuYUVDataOpt::COLOR_YELLOW,
//                          XuYUVDataOpt::TYPE_ONLY_TEXT);
//    testInfostr.clear();


}

void CameraImagRunnable::setIRLampSwichStatus(const bool lampSwich) {
    cameraIRLampSwichEnable = lampSwich;
}

void CameraImagRunnable::drawTestInfoByG4MINI(uint8_t *yuvdata, int len) {


    testInfostr.append("UART:    ");
    testInfostr.append(((status_uart == 1) ? "YES" : ((status_uart == 0) ? "NO" : "UNKNOW")));

    yuvDataOpt.drawToNV21(50, 200, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (status_uart == 1) ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("RS485:   ");
    testInfostr.append(((status_rs485 == 1) ? "YES" : ((status_rs485 == 0) ? "NO" : "UNKNOW")));

    yuvDataOpt.drawToNV21(50, 250, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (status_rs485 == 1) ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();




    testInfostr.append("Voltage: ");
    testInfostr.append(voltageGood ? "YES" : "NO");
    yuvDataOpt.drawToNV21(50, 300, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (voltageGood) ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();



    testInfostr.append("IO_110:  ");
    testInfostr.append((ioValue_110 == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(50, 350, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (ioValue_110 == 1)  ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("IO_111:  ");
    testInfostr.append(((ioValue_111 == 1) ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(50, 400, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (ioValue_111 == 1)  ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();



    testInfostr.append("IO_115:  ");
    testInfostr.append((ioValue_115 == 1) ? "YES" : "NO");
    yuvDataOpt.drawToNV21(50, 450, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (ioValue_115 == 1)  ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("CANWorkStatus:  ");
    testInfostr.append(canWorkStatus ? "YES" : "NO");
    yuvDataOpt.drawToNV21(50, 500, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          canWorkStatus ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("MCUVersion:     ");
    testInfostr.append(mcuversion);
    yuvDataOpt.drawToNV21(50, 550, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("CPUTemperature: ");
    testInfostr.append(temperatureCPUGood ? "YES" : "NO");
    yuvDataOpt.drawToNV21(500, 200, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (temperatureCPUGood) ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("TFCard:         ");
    testInfostr.append((status_tf == 1 ? "YES" : (status_tf == 2 ? "NO" : "Error")));
    yuvDataOpt.drawToNV21(500, 250, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (status_tf == 1) ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    testInfostr.append("G-sensor:       ");
    testInfostr.append((status_sensor == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(500, 300, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (status_sensor == 1) ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("TCP:            ");
    testInfostr.append((tcpClient == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(500, 350, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (tcpClient == 1) ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("UDP Broadcast:  ");
    testInfostr.append((udpBroadcast == 1 ? "YES" : "NO"));
    yuvDataOpt.drawToNV21(500, 400, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          (udpBroadcast == 1) ? XuYUVDataOpt::COLOR_YELLOW : XuYUVDataOpt::COLOR_RED,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("CPU:           ");
    testInfostr.append(cpuSerialStr);
    yuvDataOpt.drawToNV21(500, 450, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();


    testInfostr.append("SoftwareVer:    ");
    testInfostr.append(g3SoftwardVersion);
    yuvDataOpt.drawToNV21(500, 500, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();

    /* 画固件版本号 */
    testInfostr.append("FirmwareVer:    ");
    testInfostr.append(getenv("G3_FIRMWARE_VERSION"));
    yuvDataOpt.drawToNV21(500, 550, -1, -1, -1, -1, -1, -1, testInfostr.c_str(), imagWidth,
                          imagHeight, yuvdata,
                          XuYUVDataOpt::COLOR_YELLOW,
                          XuYUVDataOpt::TYPE_ONLY_TEXT);
    testInfostr.clear();
}

void CameraImagRunnable::getImageFromDEKOMIPC() {
    int ret = -1;
    std::string cameraIp;
    int decodecChannel = XuDecoder::H264_ENDECODEC_CHANNEL_1;
    uint8_t cameraMAC[6] = {0x00,0x00,0x00,0x00,0x00,0x00};
    /* 先获取当前镜头对应的MAC地址 */
    switch (curCameraId) {
        case CAMERA_ID_1:{
            cameraMAC[0] = 0x02;
            cameraMAC[1] = 0x00;
            cameraMAC[2] = 0x00;
            cameraMAC[3] = 0x00;
            cameraMAC[4] = 0x00;
            cameraMAC[5] = 0x03;

            decodecChannel = XuDecoder::H264_ENDECODEC_CHANNEL_3;
        }
            break;
        case CAMERA_ID_2:{
            cameraMAC[0] = 0x00;
            cameraMAC[1] = 0x05;
            cameraMAC[2] = 0x04;
            cameraMAC[3] = 0x03;
            cameraMAC[4] = 0x02;
            cameraMAC[5] = 0x02;
            decodecChannel = XuDecoder::H264_ENDECODEC_CHANNEL_4;
        }
            break;


    }
    /* 扫描是否存在目标相机 */
    DEKOMAVTPCameraOpt cameraOpt;
    do {
        /* 先启动一下扫描 */
        printf("+++++++++++++++++++camera%d scanCamera  \n",curCameraId);
        std::vector<DEKOMAVTPCameraOpt::ScanResultItemInfo> sacnRet = cameraOpt.scanCamera(5000);
        /* 看看扫描是否有结果 */
        if(!sacnRet.empty()){
            /* 扫描有结果了，那么就看看是不是有对应的MAC地址存在 */
            for(std::size_t i = 0; i < sacnRet.size(); i ++){
                printf("camera%d  scanret[%d] MAC=%02x:%02x:%02x:%02x:%02x:%02x \n",curCameraId,i,cameraMAC[0],cameraMAC[1],cameraMAC[2],cameraMAC[3],cameraMAC[4],cameraMAC[5]);
                /* 比对一下MAC地址是否相同 */
                if(CodeUtils::getInstance().bytesCMP(cameraMAC,sacnRet[i].mac, sizeof(cameraMAC))){
                    /* MAC地址相同，说明找到了,保存下IP地址 */
                    cameraIp = sacnRet[i].ipadress;
                    printf("find the camera%d! MAC=%02x:%02x:%02x:%02x:%02x:%02x \n",curCameraId,cameraMAC[0],cameraMAC[1],cameraMAC[2],cameraMAC[3],cameraMAC[4],cameraMAC[5]);
                    break;
                }
            }
        }
    } while (cameraIp.empty());
    /* 看看是否扫描到设备了 */
    if(!cameraIp.empty()){
        /* 扫描到了，那么就循环尝试打开AVTP推流 */
        do {
            cameraOpt.release();
            /* 先初始化Socket */
            if(cameraOpt.init(cameraIp,17215)){
                /* 初始化成功了，那么就尝试打开 */
                cameraopened = cameraOpt.openCamera();
                sleep(1);
            }
        } while (!cameraopened);
        cameraOpt.release();
        /* 打开AVTP推流成功，那么就初始化解码器 */
        if(decoder.initDecoder(imagWidth,imagHeight,decodecChannel) == 0){
            /* 初始化解码器成功，开启网卡数据捕获 */
            DEKOMAVTPCameraImageGet imageGeter;
            imageGeter.init(curCameraId,cameraMAC,*this);
            imageGeter.start();
            /* 不停得获取解码后的数据 */
            VIS::XuMemGeter<uint8_t> yuvDataBuf(1536000);
            VIS::XuMemGeter<uint8_t> camrea_yuv_buff(imagWidth * imagHeight * 3 / 2);
            VIS::XuMemGeter<uint8_t> camrea_yuv_buff_mirror(imagWidth * imagHeight * 3 / 2);
            VIS::XuMemGeter<uint8_t> camrea_yuv_buff_black(imagWidth * imagHeight * 3 / 2);
            /* 读黑色的图片，用来CVBS的时候做黑边 */
            XuFile::getInstance().readFile(BLACK_YUV_FILE_PATHA,camrea_yuv_buff_black.getPtr(),camrea_yuv_buff_black.getSize());
            int getYUVFailedSum = 0;

            while(!needStop){
                int yuvRet = decoder.getYUVData(yuvDataBuf.getPtr());
                if(yuvRet == 1536000){
                    getYUVFailedSum = 0;
                    // 先转成1280*720的分辨率
                    xuRgaUtils.imageTransformation(1280, 800, XuRGAUtils::IMG_TYPE_NV21,
                                                   yuvDataBuf.getPtr(), 1280, 720,
                                                   XuRGAUtils::IMG_TYPE_NV21, camrea_yuv_buff.getPtr());
                }else{
                    getYUVFailedSum ++;
                    if(getYUVFailedSum > 5){
                        xuRgaUtils.imageTransformation(1280, 720, XuRGAUtils::IMG_TYPE_NV21,
                                                       camrea_yuv_buff_black.getPtr(), 1280, 720,
                                                       XuRGAUtils::IMG_TYPE_NV21, camrea_yuv_buff.getPtr());
                        getYUVFailedSum = 10;
                    }

                }
                /* 封装成一个对象传给算法用 */
                curCameraYuvData.setYUVData(1382400, camrea_yuv_buff.getPtr());
                curMainUnitInterface->onDetectionYUVGet(curCameraYuvData);

                /* 如果需要镜像，那么就镜像一下 */
                if (needMirror) {
                    /* 镜像的话就先进行镜像翻转 */
                    xuRgaUtils.imageTransformation(imagWidth, imagHeight, XuRGAUtils::IMG_TYPE_NV21,
                                                   camrea_yuv_buff.getPtr(), imagWidth, imagHeight,
                                                   XuRGAUtils::IMG_TYPE_NV21, camrea_yuv_buff_mirror.getPtr(),
                                                   false, 0, 0, XuRGAUtils::FLIP_TYPE_H);

                    /* 然后画时间戳那些 */
                    drawYUV(camrea_yuv_buff_mirror.getPtr(), 1382400);
                    /* 一切搞定了就封装，避免屏幕显示时间戳会闪一下的问题 */
                    drawYuvData.setYUVData(1382400, camrea_yuv_buff_mirror.getPtr());
                }else{
                    /* 不用镜像的情况下，就先画时间戳那些 */
                    drawYUV(camrea_yuv_buff.getPtr(), 1382400);
                    /* 一切搞定了就封装，避免屏幕显示时间戳会闪一下的问题 */
                    drawYuvData.setYUVData(1382400, camrea_yuv_buff.getPtr());
                }
                /* 先给编码器用 */
                curMainUnitInterface->onCameraYUVGet(drawYuvData);  //20%
//                    printf("camera%d  curMainUnitInterface->onCameraYUVGet=%d  \n",curCameraId,drawYuvData.getDataLen());
                /* 最后给屏幕显示用 */
                curMainUnitInterface->onShowYUVGet(drawYuvData);    //10%

            }
        }else{
            printf("camera%d init decoder failed!  \n",curCameraId);
        }

    }else{
        /* 没有扫描到设备，那么就打印一下 */
        printf("can not find the camera%d! MAC=%02x:%02x:%02x:%02x:%02x:%02x \n",curCameraId,cameraMAC[0],cameraMAC[1],cameraMAC[2],cameraMAC[3],cameraMAC[4],cameraMAC[5]);
    }




    cameraopened = true;
}

int CameraImagRunnable::onDEKOMIPCH264Get(const CameraH264Data &cameraH264Data) {
    int ret = decoder.toDecodeH264(&cameraH264Data);
    uint64_t capTime = XuTimeUtil::getInstance().currentTimeMillis();
    printf("camera%d  capTime = %llu  \n",curCameraId,capTime);
//    if(h264Test == nullptr){
//        std::string fileName = "/userdata/media/camera";
//        fileName.append(std::to_string(curCameraId));
//        fileName.append("h264");
//        h264Test = fopen(fileName.c_str(),"wb");
//    }
//    fwrite(cameraH264Data.getCurH264Data(),1,cameraH264Data.getDataLen(),h264Test);
//    fflush(h264Test);
    return ret;
}






