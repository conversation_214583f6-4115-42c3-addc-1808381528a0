//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/22.
//


#include "XuCameraUtils.h"
#include "XuLog.h"
#include "XuFile.h"

const char *get_format_name(unsigned int format) {
    if (format == V4L2_PIX_FMT_YUV422P)
        return "YUV422P";
    else if (format == V4L2_PIX_FMT_YUV420)
        return "YUV420";
    else if (format == V4L2_PIX_FMT_YVU420)
        return "YVU420";
    else if (format == V4L2_PIX_FMT_NV16)
        return "NV16";
    else if (format == V4L2_PIX_FMT_NV12)
        return "NV12";
    else if (format == V4L2_PIX_FMT_NV61)
        return "NV61";
    else if (format == V4L2_PIX_FMT_NV21)
        return "NV21";
    else if (format == V4L2_PIX_FMT_HM12)
        return "MB YUV420";
    else if (format == V4L2_PIX_FMT_YUYV)
        return "YUYV";
    else if (format == V4L2_PIX_FMT_YVYU)
        return "YVYU";
    else if (format == V4L2_PIX_FMT_UYVY)
        return "UYVY";
    else if (format == V4L2_PIX_FMT_VYUY)
        return "VYUY";
    else if (format == V4L2_PIX_FMT_MJPEG)
        return "MJPEG";
    else if (format == V4L2_PIX_FMT_H264)
        return "H264";
    else
        return nullptr;
}


int XuCameraUtils::openCamera(int cameraId, int width, int height) {
    int ret = -1;
    curCameraId = cameraId;
    const char *videoNode = "/dev/video0";
    switch (cameraId) {
        case CAMERA_ID_1: {
            videoNode = "/dev/video0";
        }
            break;
        case CAMERA_ID_2: {
            /* 根据核心板的类型确定打开哪个节点 */
            switch (G3_Configuration::getInstance().getCoreBoardType()) {
                case COREBOARDTYPE_MRV220:
                case COREBOARDTYPE_MRV220K:{
                    videoNode = "/dev/video6";
                }
                    break;
                case COREBOARDTYPE_MRV240:{
                    videoNode = "/dev/video1";
                }
                    break;
            }
        }
            break;
        case CAMERA_ID_3: {
            videoNode = "/dev/video2";
        }
            break;
        case CAMERA_ID_4: {
            videoNode = "/dev/video3";
        }
            break;
    }

    if (cap.setDevice(videoNode) < 0) {
        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG, "CAMERATest") << "setDevice fail" << XU_LOG_END;
        ret = -1;
    } else {
        /* 先获取一下相机当前的分辨率，以此确定输入的信号类型 */
        struct v4l2_format curFmt;
        if (cap.getFmt(curFmt) == 0) {
            printf("========================== camera=%d   w=%d  h=%d   \n", curCameraId, curFmt.fmt.pix_mp.width,
                   curFmt.fmt.pix_mp.height);
            /* 先看下是不是CVBS的P制的 */
            if (curFmt.fmt.pix_mp.width == 720 && curFmt.fmt.pix_mp.height == 604) {
                curCameraInputSignalType = CAMERA_INPUT_TYPE_CVBS_D1_PAL;
                imgWidth = 720;
                imgHeight = 576;
            } else if (curFmt.fmt.pix_mp.width == 720 && curFmt.fmt.pix_mp.height == 510) {         /* 看下是不是CVBS的N制的 */
                curCameraInputSignalType = CAMERA_INPUT_TYPE_CVBS_D1_NTSC;
                imgWidth = 720;
                imgHeight = 480;
            } else if (curFmt.fmt.pix_mp.width == 1280 && curFmt.fmt.pix_mp.height == 720) {     /* 看下是不是AHD的720P的 */
                curCameraInputSignalType = CAMERA_INPUT_TYPE_AHD_720P;
                imgWidth = 1280;
                imgHeight = 720;
            } else if (curFmt.fmt.pix_mp.width == 1920 && curFmt.fmt.pix_mp.height == 1080) {     /* 看下是不是AHD的1080P的 */
                curCameraInputSignalType = CAMERA_INPUT_TYPE_AHD_1080P;
                imgWidth = 1920;
                imgHeight = 1080;
            } else {
                /* 其他分辨率都算没插镜头 */
                curCameraInputSignalType = CAMERA_INPUT_TYPE_NONE;
                imgWidth = -1;
                imgHeight = -1;
            }

        } else {
            /* 获取不到分辨，那么就认为是没插镜头 */
            curCameraInputSignalType = CAMERA_INPUT_TYPE_NONE;
            imgWidth = -1;
            imgHeight = -1;
        }
        /* 设置一下接入相机的类型 */
        G3_Configuration::getInstance().setCameraInputSignalType(curCameraId, curCameraInputSignalType);
        /* 判断一下这个镜头的类型是否被允许接入到MRV220 */
        Camera_Input_Type_Filter cameraInputTypeFilter = G3_Configuration::getInstance().getCameraInputTypeFilter();
        if ((curCameraInputSignalType == CAMERA_INPUT_TYPE_CVBS_D1_PAL && cameraInputTypeFilter.cvbs_p) ||
            (curCameraInputSignalType == CAMERA_INPUT_TYPE_CVBS_D1_NTSC && cameraInputTypeFilter.cvbs_n) ||
            (curCameraInputSignalType == CAMERA_INPUT_TYPE_AHD_720P && cameraInputTypeFilter.ahd_720p) ||
            (curCameraInputSignalType == CAMERA_INPUT_TYPE_AHD_1080P && cameraInputTypeFilter.ahd_1080p)) {
            /* 判断下分辨率是否正确，如果分辨率不对（没插镜头），那么就直接关闭，不往下走 */
            if (imgWidth > 0 && imgHeight > 0) {
                /* 设置分辨率 */
                int setFmt = cap.setFmt(curFmt.fmt.pix_mp.width, curFmt.fmt.pix_mp.height, V4L2_PIX_FMT_UYVY,
                                        curCameraInputSignalType);
                if (setFmt < 0) {
                    vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG, "CAMERATest") << "setFmt fail"
                                                                                             << XU_LOG_END;
                    ret = -1;
                } else {
                    if (cap.streamOn() < 0) {
                        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_DEBUG, "CAMERATest") << "streamOn fail"
                                                                                                 << XU_LOG_END;
                        ret = -1;
                    } else {
                        needToStopCamera = false;
                        /* 如果没创建过内存，才可以再创建 */
                        if (yuvBufLen < 0) {
                            yuvBufLen = width * height * 3 / 2;
                            /* 先开辟内存，开大一点 */
                            yudData_ahd = (uint8_t *) malloc(yuvBufLen);
                            yudData_cvbs = (uint8_t *) malloc(yuvBufLen);
                            yudData_black = (uint8_t *) malloc(yuvBufLen);
                            /* 读黑色的图片，用来CVBS的时候做黑边 */
                            int readBlackLen = XuFile::getInstance().readFile(BLACK_YUV_FILE_PATHA,yudData_black,yuvBufLen);
                            if(readBlackLen > 0){
                                /* 把黑色的图像复制到cvbs图像的指针里，用来后续加黑边 */
                                memcpy(yudData_cvbs, yudData_black, yuvBufLen);
                            }

                        }
                        ret = 0;
                    }
                }
            } else {
                ret = -2;
            }
        } else {
            ret = -3;
            printf("This camera input type is can not be use! \n");
        }

    }

    /* 不同的返回值不同处理 */
    switch (ret) {
        case 0: {
            /* 一切正常 */

            /* 记录一下镜头已连接（pr2000上的话也可能未连接）且成功打开 */
            cameraIsConnected = true;

        }
            break;

        case -1: {
            /* 打开失败（未知错误），可以多次尝试重新打开，这里先关闭一下 */
            cameraIsConnected = false;
            vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, "XuCameraUtils.openCamera") << "ret = -1"
                                                                                                   << XU_LOG_END;
            closeCamera();
        }
            break;

        case -2: {
            /* 没插相机，不用重复尝试了 */

            /* 读取黑色的图片放到内存里 */
            if (yuvBufLen < 0) {
                yuvBufLen = width * height * 3 / 2;
                /* 先开辟内存，开大一点 */
                yudData_cvbs = (uint8_t *) malloc(yuvBufLen);
                /* 读黑色的图片，用来CVBS的时候做黑边 */
                XuFile::getInstance().readFile(BLACK_YUV_FILE_PATHA,yudData_cvbs,yuvBufLen);


            }
            /* 把返回的结果设置为成功 */
            cameraIsConnected = false;
            ret = 0;
        }
            break;

        case -3: {
            /* 这种类型的相机不允许被接入使用（软件限制的） */
            curCameraInputSignalType = CAMERA_INPUT_TYPE_NONE;
            imgWidth = -1;
            imgHeight = -1;
            /* 读取黑色的图片放到内存里 */
            if (yuvBufLen < 0) {
                yuvBufLen = width * height * 3 / 2;
                /* 先开辟内存，开大一点 */
                yudData_ahd = (uint8_t *) malloc(yuvBufLen);
                yudData_cvbs = (uint8_t *) malloc(yuvBufLen);
                /* 读黑色的图片，用来CVBS的时候做黑边 */
                XuFile::getInstance().readFile(BLACK_YUV_FILE_PATHA,yudData_cvbs,yuvBufLen);
            }
            /* 把返回的结果设置为成功 */
            cameraIsConnected = false;
            ret = 0;
        }
            break;

        default:{
            ; // 未知的返回值先不处理，因为不可能有未知的返回值
        }
            break;
    }


    return ret;
}

void XuCameraUtils::closeCamera() {
    needToStopCamera = true;
    cap.streamOff();
    cap.closeDevices();

}


int XuCameraUtils::getCameraImag(uint8_t *yuvBuf, const int len) {
    int ret = cap.getCamData(yuvBuf, len);
    return ret;
}



void XuCameraUtils::run() {
    pthread_setname_np(pthread_self(), "XuCamTtils");

    int getImgRet = -1;

    uint8_t *bufTemp = nullptr;

    while (!needToStopCamera) {
        getImgRet = -1;
        /* 根据相机的制式进行不同的操作 */
        switch (curCameraInputSignalType) {
            case CAMERA_INPUT_TYPE_AHD_720P: {
                /* AHD相机不用管，直接拿 */
                cpyYUVLock.lock();
                getImgRet = getCameraImag(yudData_ahd, yuvBufLen);
                cpyYUVLock.unlock();
            }
                break;
            case CAMERA_INPUT_TYPE_CVBS_D1_PAL: {
                if (bufTemp == nullptr) {
                    bufTemp = (uint8_t *) malloc(720 * 604 * 2);
                }
                /* 获取一帧混合的 */
                getImgRet = getCameraImag(bufTemp, 720 * 604 * 2);

                /* 看看是不是有获取到数据 */
                if (getImgRet >= 720 * 604 * 2) {
                    /* 把奇偶场合并一下 */
                    cvbsDataMix(bufTemp, curCameraInputSignalType, yudData_ahd, imgWidth, imgHeight);
                    cpyYUVLock.lock();
                    /* 给加到黑边过去 */
                    rgaUtils.synthesisImg_center(imgWidth, imgHeight, XuRGAUtils::IMG_TYPE_UYVY_422,
                                                 yudData_ahd, 1280, 720, XuRGAUtils::IMG_TYPE_NV21, yudData_cvbs);
                    cpyYUVLock.unlock();
                } else {
                    /* 没有读到数据，直接给黑色的图像 */

                    cpyYUVLock.lock();
                    rgaUtils.synthesisImg_center(1280, 720, XuRGAUtils::IMG_TYPE_NV21,
                                                 yudData_black, 1280, 720, XuRGAUtils::IMG_TYPE_NV21, yudData_cvbs);
                    cpyYUVLock.unlock();

                }
            }
                break;
            case CAMERA_INPUT_TYPE_CVBS_D1_NTSC: {
                if (bufTemp == nullptr) {
                    bufTemp = (uint8_t *) malloc(720 * 510 * 2);
                }
                /* 获取一帧混合的 */
                getImgRet = getCameraImag(bufTemp, 720 * 510 * 2);

                /* 看看是不是有获取到数据 */
                if (getImgRet >= 720 * 510 * 2) {

                    /* 把奇偶场合并一下 */
                    cvbsDataMix(bufTemp, curCameraInputSignalType, yudData_ahd, imgWidth, imgHeight);

                    cpyYUVLock.lock();
                    /* 给加到黑边过去 */
                    rgaUtils.synthesisImg_center(imgWidth, imgHeight, XuRGAUtils::IMG_TYPE_UYVY_422,
                                                 yudData_ahd, 1280, 720, XuRGAUtils::IMG_TYPE_NV21, yudData_cvbs);
                    cpyYUVLock.unlock();
                } else {
                    /* 没有读到数据，直接给黑色的图像 */
                    cpyYUVLock.lock();
                    rgaUtils.synthesisImg_center(1280, 720, XuRGAUtils::IMG_TYPE_NV21,
                                                 yudData_black, 1280, 720, XuRGAUtils::IMG_TYPE_NV21, yudData_cvbs);
                    cpyYUVLock.unlock();

                }
            }
                break;

            case CAMERA_INPUT_TYPE_AHD_1080P: {
                /* AHD相机不用管，直接拿 */
                cpyYUVLock.lock();
                getImgRet = getCameraImag(yudData_ahd, yuvBufLen);
                cpyYUVLock.unlock();
            }
                break;

        }

//           }




        usleep(40 * 1000);

    }
    /* 释放一下 */
    free(bufTemp);

    pthread_setname_np(pthread_self(), "Finish");


}


int
XuCameraUtils::cvbsDataMix(uint8_t *cvbs, uint8_t curCameraInputSignalType, uint8_t *decBuf, int width, int height) {
    int ret = -1;
    int dataLen = 0;
    /* 获取奇偶场的Y跟UV的起始指针 */
    uint8_t *oddPtr = nullptr;
    uint8_t *evenPtr = nullptr;


    switch (curCameraInputSignalType) {
        case CAMERA_INPUT_TYPE_CVBS_D1_PAL: {
            oddPtr = cvbs;
            evenPtr = cvbs + (720 * 313 * 2);

        }
            break;

        case CAMERA_INPUT_TYPE_CVBS_D1_NTSC: {
            oddPtr = cvbs;
            evenPtr = cvbs + (720 * 263 * 2);
        }
            break;

    }


    if (oddPtr != nullptr && evenPtr != nullptr) {
        int oddIndex = 0;
        int evenIndex = 0;
        /* 交错插入 */
        for (int i = 0; i < height; i += 2) {
            /* 把奇数行复制过去 */
            memcpy(decBuf + ((width * 2) * i), oddPtr + oddIndex, (width * 2));
            oddIndex += (width * 2);
            /* 把偶数行复制过去 */
            memcpy(decBuf + ((width * 2) * (i + 1)), evenPtr + evenIndex, (width * 2));
            evenIndex += (width * 2);

            /* 累计下数据 */
            dataLen += (width * 4);
        }


//        /* 再复制奇偶行的UV */
//        for (int i = 0; i < height; i += 2) {
//            /* 把奇数行复制过去 */
//            memcpy(decBuf + (uvstart + ((width / 2) * i)), oddUV + oddIndex, (width / 2));
//            oddIndex += (width / 2);
//            /* 把偶数行复制过去 */
//            memcpy(decBuf + (uvstart + ((width / 2) * (i+1))), evenUV + evenIndex, (width / 2));
//            evenIndex += (width / 2);
//            /* 累计下数据 */
//            dataLen += width ;
//        }

        ret = dataLen;
    }
    return ret;
}

int XuCameraUtils::getCameraYUV(uint8_t *yuvBuf, const int len) {


    int ret = -1;
    /* 根据相机的制式进行不同的操作 */
    switch (curCameraInputSignalType) {
        case CAMERA_INPUT_TYPE_AHD_720P: {
            /* AHD相机不用管，直接拿 */
            cpyYUVLock.lock();
            memcpy(yuvBuf, yudData_ahd, len);
            ret = len;
            cpyYUVLock.unlock();
        }
            break;
        case CAMERA_INPUT_TYPE_CVBS_D1_PAL:
        case CAMERA_INPUT_TYPE_CVBS_D1_NTSC: {
            /* CVBS相机需要加黑边，所以拿加黑边的 */
            cpyYUVLock.lock();
            memcpy(yuvBuf, yudData_cvbs, len);
            ret = len;
            cpyYUVLock.unlock();
        }
            break;

        case CAMERA_INPUT_TYPE_AHD_1080P: {
            /* AHD相机不用管，直接拿 */
            cpyYUVLock.lock();
            memcpy(yuvBuf, yudData_ahd, len);
            ret = len;
            cpyYUVLock.unlock();
        }
            break;
        default: {
            /* 默认拿黑色的图像 */
            cpyYUVLock.lock();
            memcpy(yuvBuf, yudData_cvbs, len);
            ret = len;
            cpyYUVLock.unlock();
        }
            break;

    }



    return ret;
}

void XuCameraUtils::start() {
    /* 只有一切正常才能开启线程，不然就步开启线程 */
    if (cameraIsConnected) {
        /* 一切正常，启动线程 */
        if (!camCapThread.isRunning()) {
            camCapThread.start(cap);
        }

        if (!cameraUtilsThread.isRunning()) {
            cameraUtilsThread.start(*this);
        }
    } else {
        /* 镜头未连接，不能启动线程 */
        vis::XuLog::getInstance().log(vis::XuLog::LOG_LEVEL_ERROR, "XuCameraUtils") << "camera" << curCameraId
                                                                                    << " is camera not connect!"
                                                                                    << XU_LOG_END;
    }


}
