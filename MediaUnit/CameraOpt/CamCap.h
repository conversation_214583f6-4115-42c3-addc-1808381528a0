//
// Created by xwf on 2021/12/10.
//

#ifndef VISPECT__CAMCAP_H
#define VISPECT__CAMCAP_H

#include <linux/videodev2.h>

#include <Poco/Runnable.h>
#include <Poco/ThreadPool.h>

#include <string>
#include <vector>
#include <mutex>
#include "XuRGAUtils.h"

struct camMem_s {
    void *start;
    uint32_t length;
};

struct camMemInfo_s {
    std::vector<struct v4l2_plane> plane;
    std::vector<struct camMem_s> memPoint;
};

typedef struct {
    int videoFd;
    struct v4l2_capability cap;

    std::vector<struct camMemInfo_s> meminfo;
} cameraInfo_t;


struct CamCapBufIndex {
    void *yuvData = nullptr;
    int yuvDataLen = -1;
    struct v4l2_buffer buffer = {0};
    bool isrelse = true;
};

class CamCap : public Poco::Runnable {
public:
    CamCap() = default;

    ~CamCap();

    explicit CamCap(const std::string &deviceNode);

    int setDevice(const std::string &deviceNode);

    bool isCamera();

    bool isMplaneCamera();

    int getFmt(struct v4l2_format &fmt);

    int getFmt(uint32_t &fource);

    int getFmt(uint32_t &width, uint32_t &height);

    int getFmt(uint32_t &width, uint32_t &height, uint32_t &fource);

    int setFmt(uint32_t width, uint32_t height, uint32_t fourcc = 0, uint8_t cameraInputSignalType = 0);

    int setFmt(uint32_t fourcc);

    int streamOn();

    int streamOff();

    int getCamData(uint8_t *buf,int bufLen);


    int reset(uint8_t cameraInputSignalType = 0);

    int closeDevices();

    int reStart();



    void run() override;

    int releaseBufToCam(CamCapBufIndex &bufInfo);


private:

    cameraInfo_t _cameraInfo = {-1};
    std::string _deviceNode;
    uint32_t _width;
    uint32_t _height;
    uint32_t _fourcc;



    /* 当前的相机输入信号类型 */
    uint8_t  curCameraInputSignalType = 0;

    CamCapBufIndex bufInfo_ahd;
    CamCapBufIndex bufInfo_cvbs;



    bool needStopCap = true;

    std::mutex cpyYUVLock;

    XuRGAUtils rgaUtils;


private:
    int setFmt(struct v4l2_format fmt);     // 慎用
    int memMap();

    int memUnmap();

    int closeCam();

    int getVideoData(CamCapBufIndex &bufInfo, uint msec);

    int getFmtData(CamCapBufIndex &bufInfo);



};

#endif // VISPECT__CAMCAP_H
