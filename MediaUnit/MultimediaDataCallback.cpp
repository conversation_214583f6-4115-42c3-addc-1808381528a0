//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/24.
//

#include <cstdio>
#include "MultimediaDataCallback.h"


int MultimediaDataCallback::onCameraYUVGet(CameraYUVData &cameraData) {
    return 0;
}

MultimediaDataCallback::MultimediaDataCallback() {

}

int MultimediaDataCallback::onCameraH264Get(const CameraH264Data &cameraH264Data) {
    return 0;
}

int MultimediaDataCallback::onMp4FileGet(const Mp4FileInfo &mp4FileInfo) {
    return 0;
}

int MultimediaDataCallback::onShowYUVGet(CameraYUVData &cameraData) {
    return 0;
}

int MultimediaDataCallback::onDetectionYUVGet(CameraYUVData &cameraData) {
    return 0;
}

int MultimediaDataCallback::onJpgFileGet(const JPGFileInfo &jpgFileInfo) {
    return 0;
}
