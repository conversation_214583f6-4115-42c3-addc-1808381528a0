//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/23.
//

#ifndef VIS_G3_SOFTWARE_XUENCODER_H
#define VIS_G3_SOFTWARE_XUENCODER_H


#include <cstdint>
#include "rkmedia_api.h"
#include "rkmedia_venc.h"
#include "CameraYUVData.h"


class XuEncoder {
public:
    static const int H264_ENDECODEC_CHANNEL_1 = 0;
    static const int H264_ENDECODEC_CHANNEL_2 = 1;
    static const int H264_ENDECODEC_CHANNEL_3 = 2;
    static const int H264_ENDECODEC_CHANNEL_4 = 3;
    static const int H264_ENDECODEC_CHANNEL_5 = 4;
    static const int H264_ENDECODEC_CHANNEL_6 = 5;


    /**
     * 初始化编码器
     *
     * @param width ： 宽
     * @param height ： 高
     * @param vencchn ： 编码通道
     * @param frameRate : 帧率
     * @param bitRate ： 比特率
     *
     *@return 结果 ：    0：成功  其他：失败
     *
     * */
    int initEncoder(int width, int height, int vencchn, int frameRate, int bitRate);

    /**
     * 喂数据给编码器
     *
     * @param len ： YUV数据的长度
     * @param buf ： 数据的指针
     *
     * @return 结果 ：    0：成功  其他：失败
     *
     * */
    int toEncodeH264(CameraYUVData *cameraYuvData);

    /**
     * 获取编码好的数据
     *
     * @param h264buf ： 装编码号的H264数据的内存
     *
     * @return 获取到的H264数据的长度
     *
     * */
    int getH264Frame(uint8_t *h264buf);

    /**
     * 关闭并释放编码器（打开后一定要关闭）
     *
     * */
    int releaseEncoder();


private:
    /* 当前使用的编码通道 */
    int curVencChn;
    uint64_t curFrameId = 0;
    /* 喂给编码器的数据的信息 */
    MB_IMAGE_INFO_S curStImageInfo;

    MEDIA_BUFFER encoderMediaBuffer;

    MEDIA_BUFFER h264DataMediaBuffer = nullptr;


};


#endif //VIS_G3_SOFTWARE_XUENCODER_H
