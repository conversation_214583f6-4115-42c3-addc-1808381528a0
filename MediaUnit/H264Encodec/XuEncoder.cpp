//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/23.
//

#include <cstring>
#include <cstdio>
#include "rkmedia_aenc.h"
#include "rkmedia_api.h"
#include "XuEncoder.h"
#include "XuTimeUtil.h"

int XuEncoder::initEncoder(int width, int height, int vencchn, int frameRate, int bitRate) {
    int ret = -1;
    RK_MPI_SYS_Init();

    VENC_CHN_ATTR_S venc_chn_attr;
    memset(&venc_chn_attr, 0, sizeof(venc_chn_attr));
    venc_chn_attr.stVencAttr.enType = RK_CODEC_TYPE_H264;
    venc_chn_attr.stVencAttr.imageType = IMAGE_TYPE_NV12;
    venc_chn_attr.stVencAttr.u32PicWidth = width;
    venc_chn_attr.stVencAttr.u32PicHeight = height;
    venc_chn_attr.stVencAttr.u32VirWidth = width;
    venc_chn_attr.stVencAttr.u32VirHeight = height;
    venc_chn_attr.stVencAttr.u32Profile = 100;

    venc_chn_attr.stRcAttr.enRcMode = VENC_RC_MODE_H264CBR;
    venc_chn_attr.stRcAttr.stH264Cbr.u32Gop = frameRate;
    venc_chn_attr.stRcAttr.stH264Cbr.u32BitRate = bitRate;
    // frame rate: in 30/1, out 30/1.
    venc_chn_attr.stRcAttr.stH264Cbr.fr32DstFrameRateDen = 1;
    venc_chn_attr.stRcAttr.stH264Cbr.fr32DstFrameRateNum = frameRate;
    venc_chn_attr.stRcAttr.stH264Cbr.u32SrcFrameRateDen = 1;
    venc_chn_attr.stRcAttr.stH264Cbr.u32SrcFrameRateNum = frameRate;


    curVencChn = vencchn;
    ret = RK_MPI_VENC_CreateChn(curVencChn, &venc_chn_attr);

    curStImageInfo = {venc_chn_attr.stVencAttr.u32PicWidth, venc_chn_attr.stVencAttr.u32PicHeight,
                      venc_chn_attr.stVencAttr.u32VirWidth, venc_chn_attr.stVencAttr.u32VirHeight,
                      venc_chn_attr.stVencAttr.imageType};

    encoderMediaBuffer = RK_MPI_MB_CreateImageBuffer(&curStImageInfo, RK_TRUE, MB_FLAG_NOCACHED);
    if (ret) {
        printf("ERROR: create VENC[%d] error! ret=%d\n", curVencChn, ret);
    }


    return ret;
}


int XuEncoder::toEncodeH264(CameraYUVData *cameraYuvData) {
    int ret = -1;
    cameraYuvData->getYUVData(static_cast<uint8_t *>(RK_MPI_MB_GetPtr(encoderMediaBuffer)), cameraYuvData->getDataLen());
    RK_MPI_MB_SetSize(encoderMediaBuffer, cameraYuvData->getDataLen());
    RK_MPI_MB_SetTimestamp(encoderMediaBuffer, XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis());
    ret = RK_MPI_SYS_SendMediaBuffer(RK_ID_VENC, curVencChn, encoderMediaBuffer);
//    printf("RK_MPI_SYS_SendMediaBuffer ret=%d   len=%d   %02x%02x%02x%02x%02x \n", ret,len,buf[0],buf[1],buf[2],buf[3],buf[4]);
//    RK_MPI_MB_ReleaseBuffer(mb);
    return ret;
}

int XuEncoder::releaseEncoder() {
    int ret = -1;
    ret = RK_MPI_VENC_DestroyChn(curVencChn);
    if (ret) {
        printf("ERROR: Destroy VENC[%d] error! ret=%d\n", curVencChn, ret);

    }
    return ret;
}

int XuEncoder::getH264Frame(uint8_t *h264buf) {
    int ret = -1;
    h264DataMediaBuffer = RK_MPI_SYS_GetMediaBuffer(RK_ID_VENC, curVencChn, -1);
    if (!h264DataMediaBuffer) {
        printf("RK_MPI_SYS_GetMediaBuffer get nullptr buffer!\n");
        ret = -1;
    } else {
//            printf("Get packet:ptr:%p, fd:%d, size:%zu, mode:%d, channel:%d, ""timestamp:%lld\n",RK_MPI_MB_GetPtr(mb), RK_MPI_MB_GetFD(mb), RK_MPI_MB_GetSize(mb),RK_MPI_MB_GetModeID(mb), RK_MPI_MB_GetChannelID(mb),RK_MPI_MB_GetTimestamp(mb));
        if (RK_MPI_MB_GetSize(h264DataMediaBuffer) > 0) {
            memcpy(h264buf, RK_MPI_MB_GetPtr(h264DataMediaBuffer), RK_MPI_MB_GetSize(h264DataMediaBuffer));
            ret = RK_MPI_MB_GetSize(h264DataMediaBuffer);
        }

        /* 只要拿到的不为空  就都要释放 */
        RK_MPI_MB_ReleaseBuffer(h264DataMediaBuffer);
    }
    return ret;
}

