//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/25.
//

#include <cstdlib>
#include <cstring>
#include <cstdio>
#include "CameraH264Data.h"

int CameraH264Data::getCameraId() const {
    return cameraId;
}

void CameraH264Data::setCameraId(int cameraId) {
    CameraH264Data::cameraId = cameraId;
}

uint8_t *CameraH264Data::getCurH264Data() const {
    return const_cast<uint8_t *>(curH264Data.data());
}


int CameraH264Data::getDataLen() const {
    return dataLen;
}


uint64_t CameraH264Data::getTimestamp() const {
    return timestamp;
}

void CameraH264Data::setTimestamp(uint64_t timestamp) {
    CameraH264Data::timestamp = timestamp;
}

int CameraH264Data::getImagWidth() const {
    return imagWidth;
}

void CameraH264Data::setImagWidth(int imagWidth) {
    CameraH264Data::imagWidth = imagWidth;
}

int CameraH264Data::getImagHeight() const {
    return imagHeight;
}

void CameraH264Data::setImagHeight(int imagHeight) {
    CameraH264Data::imagHeight = imagHeight;
}

int CameraH264Data::getBitRate() const {
    return bitRate;
}

void CameraH264Data::setBitRate(int bitRate) {
    CameraH264Data::bitRate = bitRate;
}

int CameraH264Data::getFrameRate() const {
    return frameRate;
}

void CameraH264Data::setFrameRate(int frameRate) {
    CameraH264Data::frameRate = frameRate;
}

int CameraH264Data::getVenChn() const {
    return venChn;
}

void CameraH264Data::setVenChn(int venChn) {
    CameraH264Data::venChn = venChn;
}

void CameraH264Data::setCurH264Data(int len, uint8_t *h264Data) {
    if (len > 0) {
        if(curH264Data.size() < static_cast<std::size_t>(len)){
            curH264Data.clear();
            curH264Data.resize(len);
        }
        memcpy(curH264Data.data(),h264Data,len);
        dataLen = len;
    }else{
        dataLen = 0;
    }

}

int CameraH264Data::getFramePurpose() const {
    return framePurpose;
}

void CameraH264Data::setFramePurpose(int framePurpose) {
    CameraH264Data::framePurpose = framePurpose;
}

void CameraH264Data::relese() {
    if (dataLen > 0) {
        curH264Data.clear();
        dataLen = 0;
    }
}




void CameraH264Data::setDataLen(int dataLen) {
    CameraH264Data::dataLen = dataLen;
}

CameraH264Data::~CameraH264Data() {
    if (dataLen > 0) {
        curH264Data.clear();
        dataLen = 0;
    }
}

CameraH264Data::CameraH264Data() {
//    /*  */
//    curH264Data = static_cast<uint8_t *>(malloc(1));

}

std::vector<uint8_t> CameraH264Data::getVector() {
    return curH264Data;
}
