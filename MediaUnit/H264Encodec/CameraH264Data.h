//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/25.
//

#ifndef VIS_G3_SOFTWARE_CAMERAH264DATA_H
#define VIS_G3_SOFTWARE_CAMERAH264DATA_H


#include <cstdint>
#include <vector>

class CameraH264Data {

public:
    /* 创建文件 */
    static const int MUXER_STATE_CREATE = 0x00;
    /* 普通的H264数据 */
    static const int MUXER_STATE_WRITE = 0x01;
    /* 停止MP4的录制 */
    static const int MUXER_STATE_STOP = 0x02;

    CameraH264Data();

    ~CameraH264Data();


    int getCameraId() const;

    void setCameraId(int cameraId);

    uint8_t *getCurH264Data() const;

    void setCurH264Data(int len, uint8_t *h264Data);

    int getDataLen() const;

    uint64_t getTimestamp() const;

    void setTimestamp(uint64_t timestamp);

    int getImagWidth() const;

    void setImagWidth(int imagWidth);

    int getImagHeight() const;

    void setImagHeight(int imagHeight);

    int getBitRate() const;

    void setBitRate(int bitRate);

    int getFrameRate() const;

    void setFrameRate(int frameRate);

    int getVenChn() const;

    void setVenChn(int venChn);

    int getFramePurpose() const;

    void setFramePurpose(int framePurpose);

    void setDataLen(int dataLen);

    void relese();

    std::vector<uint8_t> getVector();

private:
    /* 数据来源的相机的ID */
    int cameraId;
    /* 编码器通道号 */
    int venChn;
    /* 图像的宽 */
    int imagWidth;
    /* 图像的高 */
    int imagHeight;
    /* 比特率 */
    int bitRate;
    /* 帧率 */
    int frameRate;
    /* H264数据的内存 */
    std::vector<uint8_t> curH264Data;
    /* H264数据的长度 */
    int dataLen = 0;
    /* 时间戳    单位：ms */
    uint64_t timestamp = 0;
    /* 帧的用途 */
    int framePurpose;


};


#endif //VIS_G3_SOFTWARE_CAMERAH264DATA_H
