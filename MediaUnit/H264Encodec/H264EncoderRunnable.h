//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/25.
//

#ifndef VIS_G3_SOFTWARE_H264ENCODERRUNNABLE_H
#define VIS_G3_SOFTWARE_H264ENCODERRUNNABLE_H

#include "MultimediaDataCallback.h"
#include "XuEncoder.h"
#include <Poco/Runnable.h>
#include <mutex>


class H264EncoderRunnable : public Poco::Runnable {

public:


    void run() override;

    void init(int cameraId, int width, int height, int frameRate, int bitRate, int venChn,
              MultimediaDataCallback &mainUnitInterface);

    void setMainUnitInterface(MultimediaDataCallback &mainUnitInterface);

    void setCurCameraYuvData(CameraYUVData &cameraYuvData);


    int getCurCameraId() const;

    void setCurCameraId(int curCameraId);

    bool isEncoderOpened() const;

    uint64_t getLastGetH264DataTime() const;

private:

    /* 对应的相机ID */
    int curCameraId;

    /* 是否需要停止线程 */
    bool needStop = true;

    /* 上一帧H264的时间戳 */
    uint64_t lastH264FrameTimestamp;

    /* 根据帧率算出来的喂编码器的时间间隔 */
    uint64_t encodeH264Interval = 40;

    /* 回传给Main模块的接口 */
    MultimediaDataCallback *curMainUnitInterface;

    /* 拿到的YUV图像 */
    CameraYUVData *curCameraYuvData = nullptr;

    /* 编码好的H264数据 */
    CameraH264Data curCamerH264Data;


    /* 操作H264数据的互斥锁 */
    std::mutex h264CacheLock;

    /* 编码通道是否已打开 */
    bool encoderOpened = false;

    /* 上一帧H264数据的获取时间 */
    uint64_t lastGetH264DataTime = 0xFFFFFFFFFFFFFFFF;


};


#endif //VIS_G3_SOFTWARE_H264ENCODERRUNNABLE_H
