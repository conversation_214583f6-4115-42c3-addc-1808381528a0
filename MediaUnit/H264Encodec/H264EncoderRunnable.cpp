//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/25.
//

#include <unistd.h>
#include "H264EncoderRunnable.h"
#include "utils/XuTimeUtil.h"
#include "XuLog.h"


void H264EncoderRunnable::run() {
    std::string pthreadName = "H264Encoder_";
    pthreadName.append(std::to_string(curCameraId));
    pthread_setname_np(pthread_self(), pthreadName.c_str());
    int ret = -1;
    /* 初始化编码器 */
    XuEncoder xuEncoder;
    ret = xuEncoder.initEncoder(curCamerH264Data.getImagWidth(), curCamerH264Data.getImagHeight(),
                                curCamerH264Data.getVenChn(), curCamerH264Data.getFrameRate(),
                                curCamerH264Data.getBitRate());
    /* 初始化成功才进行下一步操作 */
    if (ret == 0) {
        encoderOpened = true;
        uint8_t *h264Buf = static_cast<uint8_t *>(malloc(curCamerH264Data.getBitRate()));
        int h264DataLen = -1;
        /* 上一次喂编码的时间 */
        uint64_t lastEncodeH264Time = 0;
        while (!needStop) {
            /* 先获取下距离上次喂编码器的时间 */
            uint64_t intervalEncodeH264Time = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - lastEncodeH264Time;
            /* 判断下如果时间阈值到了，且有数据，那么就喂数据给编码器 */
            if (intervalEncodeH264Time >= encodeH264Interval && curCameraYuvData != nullptr && curCameraYuvData->getDataLen() > 0) {
                /* 记录一下喂编码器的时间 */
                lastEncodeH264Time = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                xuEncoder.toEncodeH264(curCameraYuvData);
                /* 从编码器里面拿下数据  如果有就拿出来 */
                h264DataLen = xuEncoder.getH264Frame(h264Buf);
                if (h264DataLen > 0) {
                    curCamerH264Data.setCurH264Data(h264DataLen, h264Buf);
                    curCamerH264Data.setTimestamp(XuTimeUtil::getInstance().currentTimeMillis());
                    lastH264FrameTimestamp = curCamerH264Data.getTimestamp();
                    curMainUnitInterface->onCameraH264Get(curCamerH264Data);
                    lastGetH264DataTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();

                }

            } else {
                usleep(500);
            }

        }

        free(h264Buf);
    } else {
        printf("xuEncoder.initEncoder failed! \n");
    }
    pthread_setname_np(pthread_self(), "Finish");
}

void H264EncoderRunnable::setCurCameraYuvData(CameraYUVData &cameraYuvData) {
    curCameraYuvData = &cameraYuvData;
}


void H264EncoderRunnable::init(int cameraId, int width, int height, int frameRate, int bitRate, int venChn,
                               MultimediaDataCallback &mainUnitInterface) {
    curCamerH264Data.setCameraId(cameraId);
    curCamerH264Data.setImagWidth(width);
    curCamerH264Data.setImagHeight(height);
    curCamerH264Data.setFrameRate(frameRate);
    curCamerH264Data.setBitRate(bitRate);
    curCamerH264Data.setVenChn(venChn);
    curCamerH264Data.setFramePurpose(CameraH264Data::MUXER_STATE_WRITE);
    curMainUnitInterface = &mainUnitInterface;
    setCurCameraId(cameraId);
    /* 计算下目标帧率下喂一帧数据给编码应该间隔多长时间 */
    encodeH264Interval = 1000 / frameRate;
    needStop = false;
}

int H264EncoderRunnable::getCurCameraId() const {
    return curCameraId;
}

void H264EncoderRunnable::setCurCameraId(int curCameraId) {
    H264EncoderRunnable::curCameraId = curCameraId;
}

bool H264EncoderRunnable::isEncoderOpened() const {
    return encoderOpened;
}

uint64_t H264EncoderRunnable::getLastGetH264DataTime() const {
    return lastGetH264DataTime;
}




