//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/21.
//

#ifndef G3SOFTWARE_MEDIAUNITMANAGR_H
#define G3SOFTWARE_MEDIAUNITMANAGR_H


#include "CameraOpt/XuCameraUtils.h"
#include "H264Encodec/XuEncoder.h"
#include "Mp4Record/XuH264ToMp4.h"
#include <Poco/Thread.h>
#include <Poco/ThreadPool.h>
#include <Poco/Runnable.h>
#include "MultimediaDataCallback.h"
#include "CameraOpt/CameraImagRunnable.h"
#include "H264Encodec/H264EncoderRunnable.h"
#include "Mp4Record/MP4RecordRunnable.h"
#include "utils/CodeUtils.h"
#include "utils/XuYUVDataOpt.h"
#include "tdShareDefine.h"
#include "G3DetectionDefind.h"
#include "MultimediaFilesEncrypt.h"
#include "JPEGEncoder.h"


class MediaUnitManagr : public MultimediaDataCallback {
public:


    /* DVR文件的录制时长 （ms） */
    const static uint64_t DVR_FILE_RECORD_TIME = 60000;


    MediaUnitManagr();

    int onCameraYUVGet(CameraYUVData &cameraData) override;

    int onCameraH264Get(const CameraH264Data &cameraH264Data) override;

    int onMp4FileGet(const Mp4FileInfo &mp4FileInfo) override;

    int onShowYUVGet(CameraYUVData &cameraData) override;

    int onDetectionYUVGet(CameraYUVData &cameraData) override;

    int onJpgFileGet(const JPGFileInfo &jpgFileInfo) override;


    void start();

    void setInterface(MultimediaDataCallback &mainUnitInterface);

    /**
     * 获取当前的运行状态
     *
     * @return 0：一切正常   其他：有故障
     *
     * */
    int getWorkStaus();


    /**
     * 录制一个MP4文件
     *
     * @param timeStamp ： 报警发生的时间戳（单位ms）
     * @param before ： 需要录制报警发生前的多少毫秒
     * @param last :  需要录制报警发生后的多少毫秒
     * @param type : MP4文件的类型（用以区分放在哪个目录）
     **/
    void muxerWarMP4(uint64_t timeStamp, uint64_t before, uint64_t last, int type);

    /**
     * 复制一串H264数据帧的数组
     *
     * @param  src : 需要被复制的源H264数据帧的数组
     * @param  des ：目标H264数据帧的数组
     * @param  copyLen ： 复制多长
     *
     * @return 结果  成功：0  其他:失败
     **/
    int copyH264FrameList(CameraH264Data src[], CameraH264Data des[], int copyLen);

    /**
     * 设置从识别模块拿到的识别信息(BSD)
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectionInfo_BSD(td::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);


    /**
     * 设置从识别模块拿到的报警事件
     *
     * @param cameraId : 相机ID
     * @param detectType : 识别算法类型
     * @param eventCode ： 事件码
     *
     * */
    void setDetectionEvent(CameraType &curCameraType, int eventCode, AlarmEventInfo &alarmEventInfo);


    /**
     * 设置从识别模块拿到的识别信息(DSM)
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDSMInfo(G3DetectionResult &detectionResult);

    /**
     * 设置从识别模块拿到的识别信息(手势)
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectionInfo_GES(gt::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

    /**
     * 设置从识别模块拿到的识别信息(Adas)
     *
     * @param objectInfo ： 识别信息的结构体
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectionInfo_Adas(das::objectInfo_ &objectInfo, G3DetectionResult &detectionResult);

    /**
    * 设置车速
    *
    * @param speed : 速度  （单位：KM\H）
    * @param baseOf ： 来源    （0：CAN  1：UART  2：GPS）
    *
    * @return 无
    * */
    void setSpeed(const float speed, const int baseOf);

    /**
     * 设置从识别模块拿到的识别信息(镜头状态)
     *
     * @param detectionResult : 整理过后的识别结果
     *
     * */
    void setDetectInfo_CameraStatus(G3DetectionResult &detectionResult);


    void setCPUSerial(const char *cpuSerialStr, const int len);

    void setVoltageStr(float voltage);

    void setStatusUart(int statusUart);

    void setStatusRs485(int statusRs485);

    void setStatusTf(int statusTf);

    void setTemperature_CPU(float temperature);

    void setStatusSensor(int statusSensor);

    void setTcpClient(int tcpClient);

    void setUdpBroadcast(int udpBroadcast);

    void setMcuversion(char *mcuversion, int len);

    void setIoValue110(int ioValue110);

    void setIoValue111(int ioValue111);

    void setIoValue113(int ioValue113);

    void setIoValue115(int ioValue115);

    void setG3SoftwardVersion(const char *version, const int len);

    void setIsptm(bool isptm);

    void setCANWorkStatus(bool isWork);

    void setPhotoresistorValue(float photoresistorValue);
    /**
     * 设置红外灯的开关状态
     *
     * @param lampSwich ： 红外灯的开关  ture：打开  false：关闭
     */
    void setIRLampSwichStatus(const bool lampSwich);
private:
    /* Media 模块所有线程使用的线程池 */
    Poco::ThreadPool threadPool;
    /* 返回给Main的回调 */
    MultimediaDataCallback *curMainUnitInterface;


    /* 镜头的功能集合 */
    struct CameraFuncSet{
        /* 镜头ID */
        CAMERAIDLIST cameraId = CAMERA_ID_UNKNOW;
        /* 获取镜头图像的线程 */
        CameraImagRunnable *getCameraImagRunnable = nullptr;
        /* H.264数据的编码线程 */
        H264EncoderRunnable *h264EncoderRunnable = nullptr;
        /* 录制镜头MP4视频的线程 */
        MP4RecordRunnable *mp4RecordRunnable = nullptr;
        /* 录制报警图片的线程 */
        JPEGEncoder *jpegEncoder = nullptr;
        /* 当前DVR开始录制的时间  单位us */
        uint64_t curDVRStartTime = 0;
    };




//    /* 获取镜头1的图像的线程 */
//    CameraImagRunnable getCameraImagRunnable_1;
//    /* 获取镜头2的图像的线程 */
//    CameraImagRunnable getCameraImagRunnable_2;
//    /* 获取镜头1的H264数据的线程 */
//    H264EncoderRunnable h264EncoderRunnable_1;
//
//    /* 获取镜头2的H264数据的线程 */
//    H264EncoderRunnable h264EncoderRunnable_2;
//    /* 录制镜头1的视频的线程 */
//    MP4RecordRunnable mp4RecordRunnable_1;
//    /* 当前DVR1开始录制的时间  单位ms */
//    uint64_t curDVRStartTime_1 = 0;
//    /* 录制镜头2的视频的线程 */
//    MP4RecordRunnable mp4RecordRunnable_2;
//    /* 当前DVR2开始录制的时间  单位us */
//    uint64_t curDVRStartTime_2 = 0;

    /* 需要执行的镜头功能列表 */
    std::vector<CameraFuncSet> cameraRunnableList;




    /* 分割SPS+PPS+I帧的组合帧时使用的缓存 */
    CameraH264Data cameraH264DataTemp;

    /* 2022年4月21号11点58分的时间戳 */
    const uint64_t MIN_TIME_STAMP = 1650513515;

    /* 报警录像要录报警前多少毫秒 */
    const uint32_t ALARM_VIDEO_BEFORETIME = 5000;
    /* 报警录像要录报警后多少毫秒 */
    const uint32_t ALARM_VIDEO_AFTERTIME = 5000;


    /****************************图像叠加相关的类****************************/
    /* 镜头1的图像识别出来的识别信息 */
    td::objectInfo_t curObjectInfo_camera1;
    /* 镜头2的图像识别出来的识别信息 */
    td::objectInfo_t curObjectInfo_camera2;

    /* 多媒体文件加密的线程 */
    MultimediaFilesEncrypt multimediaFilesEncrypt;



    /**
     * 启动镜头的工作线程
     *
     * @param cameraId
     * @return
     */
    bool startUpCamera(CAMERAIDLIST cameraId);


    /**
     * 启动镜头的工作线程
     *
     * @param listSize ： 需要启动几个镜头（从0开始一个个开始启动）
     *
     * @return 是否启动成功
     */
    bool startUpCamera(const int listSize);

    CAMERAIDLIST getOtherCameraFromC3Rule(CAMERAIDLIST curCameraId);



};


#endif //G3SOFTWARE_MEDIAUNITMANAGR_H
